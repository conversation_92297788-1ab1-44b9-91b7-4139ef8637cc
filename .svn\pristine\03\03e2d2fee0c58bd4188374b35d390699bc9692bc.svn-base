<?php

/**
 * Mstr_ShohinController
 *
 * 商品マスタ コントローラクラス
 *
 * @category   Mstr
 * @package    controller
 * <AUTHOR> M<PERSON>
 * @since      2014/02/25
 * @version 2015/06/24 互助会値引きマスタを追加
 * @version 2023/03/xx MSI mihara #8115 商品登録コピー機能の追加. ShohinController.ceremore.php からコピー
 * @filesource
 */
require_once dirname(__FILE__) . '/AbstractController.php';

/**
 * 商品マスタ コントローラクラス
 *
 * @category   Mstr
 * @package    controller
 * <AUTHOR> M.Hayashi
 * @since      2014/02/25
 */
class Mstr_ShohinController extends Mstr_AbstractController {
    private $def_hojo_kamoku_cd = 'XXXX'; // 補助科目の未設定時コード

    /**
     * 他テーブル利用中チェック
     * @return boolean;
     */
    protected function existsUsage($db, $model, &$msg = null) {
        // 利用フィールドチェック
        // App_Mstr_Utils_Db::existsUsageField($db, $tablename, $wheres);
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();  // 2017/05/30 ADD Kayo
        // 受注伝票明細に商品コードが存在する場合 
        $select = $db->easySelect(<<< END_OF_SQL
SELECT  count(*) AS shohin_cnt
FROM juchu_denpyo_msi m
WHERE m.delete_flg	=	0
   AND m.shohin_cd  =   :shohin_cd
   AND m.kaisya_cd  =   :kaisya_cd             
   AND m.shohin_bumon_cd  =   :bumon_cd             
END_OF_SQL
                , array('shohin_cd' => $model['__shohin_cd'], 'kaisya_cd' => $kaisya_cd, 'bumon_cd' => $model['bumon_cd']));
        if (count($select) > 0) {
            if ($select[0]['shohin_cnt'] > 0) {
                $msg = '該当の商品コードは使用中のため、削除できません。';
                return true;
            }
        }
        // 売上伝票明細に商品コードが存在する場合 
        $select = $db->easySelect(<<< END_OF_SQL
SELECT  count(*) AS shohin_cnt
FROM uriage_denpyo_msi m
WHERE m.delete_flg	=	0
   AND m.shohin_cd  =   :shohin_cd
   AND m.kaisya_cd  =   :kaisya_cd        
   AND m.shohin_bumon_cd  =   :bumon_cd             
END_OF_SQL
                , array('shohin_cd' => $model['__shohin_cd'], 'kaisya_cd' => $kaisya_cd, 'bumon_cd' => $model['bumon_cd']));
        if (count($select) > 0) {
            if ($select[0]['shohin_cnt'] > 0) {
                $msg = '該当の商品コードは使用中のため、削除できません。';
                return true;
            }
        }
        return false;
    }

    /**
     * オルタネートフィールド補完
     */
    protected function appendAlt(&$model) {
        
    }

    /**
     * HTML生成
     * @abstract
     */
    public function mainAction() {
        $db = Msi_Sys_DbManager::getMyDb();
        $kihonMapper = new DataMapper_MstrShohinMst($db);
        $tankaMapper = new DataMapper_MstrShohinTankaMst($db);
        $bunruiMapper = new DataMapper_MstrShohinBunruiMst($db);
        $gojokaiMapper = new DataMapper_MstrGojokaiNebikiMst($db);   //2015/06/24 ADD Kayo
        $bumonsiireMapper = new DataMapper_MstrShohinBumonSiireMst($db); //2017/05/15 ADD Kayo
        $incentiveMapper = new DataMapper_MstrIncentiveMst($db);         //2017/05/30 ADD Kayo

        if (!App_Utils::isFukusuKaisyaKanri()) {
            $kaisya_rec = DataMapper_KaisyaInfo::findOne($db);
            $bumonRecord = DataMapper_Bumon::find($db, array('bumon_shohin_sel_kbn' => '1'));
            if (count($bumonRecord) > 0) {
                $this->view->select_bumon = true;
            } else {
                $this->view->select_bumon = false;
            }
        } else {
            $kaisya_cd = App_Utils::getCtxtKaisyaEasy();  // 2017/05/30 ADD Kayo
            $kaisya_rec = DataMapper_KaisyaInfo::findOne($db, array('kaisya_cd' => $kaisya_cd));
            $bumonRecord = DataMapper_Bumon::find($db, array('kaisya_cd' => $kaisya_cd, 'bumon_shohin_sel_kbn' => '1'));
            if (count($bumonRecord) > 0) {
                $this->view->select_bumon = true;
            } else {
                $this->view->select_bumon = false;
            }
        }
        // インセンティブ情報を表示するかの管理区分
        if (count($kaisya_rec) > 0) {
            $this->view->incentive_kbn = $kaisya_rec['incentive_kbn'];
        } else {
            $this->view->incentive_kbn = 0;
        }
        // ひらがな対応 2018/05/25 ADD Kayo
        $getCstmKey = Msi_Sys_Utils::getCstmKeyArr();
        foreach ($getCstmKey as $rec) {
            if ($rec == 'hiragana') {
                $this->view->l1_kana = 'ひらがな名';
                break;
            }
        }

        // $this->addResponse('submenu', App_Mstr_Utils::getSubMenu(dirname(__FILE__).'/../shohin.submenu')); // サブメニュー構造
        $this->addCssFiles('app/mstr.base.layout.css');
        $this->addJsFiles('app/mstr.shohin.js');
        $this->addCssFiles('app/lib.dlg_confirm_opt.css');  // mihara added #8115
        $this->addJsFiles('app/lib.dlg_confirm_opt.js');    // mihara added #8115
        // 単位名マスタを取得
        $this->addResponse('TaniMst', DataMapper_MsterGetLib::GetTaniMst($db));
        // 0260	在庫管理区分
        $this->addResponse('ZaikoKanriKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0260'));
        // 0270	発注書区分
        $this->addResponse('HachuSyoKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0270'));
        // 0280	有無区分
        $this->addResponse('UmuKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0280'));
        // 0290	課税区分
        $this->addResponse('ZeiKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0290'));
        // 0295	軽減税率区分
        $this->addResponse('ReducedTaxRate', DataMapper_MsterGetLib::GetCodeNameMst($db, '0295'));
        // 0300	原価評価区分
        $this->addResponse('GenkaHyoukaKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0300'));
        // 0310	名称入力区分
        $this->addResponse('NameInputKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0310'));
        // 0320	非表示区分
        $this->addResponse('NonDispKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0320'));
        // 1950	WEB表示区分 2015/04/09 ADD Kayo
        $this->addResponse('WebDispKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '1950'));
        // 0330	返品可否区分
        $this->addResponse('HenpinKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0330'));
        // 0270	発注書区分
        $this->addResponse('HachuKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '0270'));
        // 2020	値引区分    2015/06/25 ADD Kayo
        $this->addResponse('NebikiKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '2020'));
        // 2090	奉仕料・湯灌区分    2015/07/12 ADD Kayo
        $this->addResponse('HoshiryoKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '2090'));
        // 2340	印刷グループ    2016/03/19 ADD Kayo
        $this->addResponse('PrintGroupCd', DataMapper_MsterGetLib::GetCodeNameMst($db, '2340'));
        // 2350	小計グループ    2016/03/19 ADD Kayo
        $this->addResponse('SyoukeiGroupCd', DataMapper_MsterGetLib::GetCodeNameMst($db, '2350'));
        // 6610	その他分類区分    2018/05/25 ADD Tosaka
        $this->addResponse('OtherBunruiKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '6610'));
        // 8583	価格集計区分    
        $this->addResponse('SyukeiGroupCd', DataMapper_MsterGetLib::GetCodeNameMst($db, '8583'));
        // 売上科目コードを取得	2016/07/30 ADD Kayo
        $this->addResponse('UriKamokuMst', DataMapper_MsterGetLib::GetUriKamokuMst($db));
        // 売上補助コードを取得	2016/07/30 ADD Kayo
        //$this->addResponse('UriHojoMst', DataMapper_MsterGetLib::GetUriHojoMst($db));
        $this->addResponse('UriHojoMst', array());
        // 仕入科目コードを取得	2015/11/29 ADD Kayo
        $this->addResponse('SiireKamokuMst', DataMapper_MsterGetLib::GetSiireKamokuMst($db));
        // 仕入補助コードを取得	2016/07/30 ADD Kayo
        //$this->addResponse('SiireHojoMst', DataMapper_MsterGetLib::GetSiireHojoMst($db));
        $this->addResponse('SiireHojoMst', array());

        // 使用目的区分
        // ajax ./restgetmokutekikbn?shohin_kbn=*
        // 大分類
        $this->addResponse('DaiBunrui', DataMapper_MsterGetLib::GetDaiBunruiMst($db));
        // 中分類
        $this->addResponse('ChuBunrui', DataMapper_MsterGetLib::GetChuBunruiMst($db));
        // 商品区分
        $this->addResponse('ShohinKbn', DataMapper_MsterGetLib::GetShohinKbnMst($db));
        // 7880	互助会区分(値引)
        if (!App_Utils::isFukusuKaisyaKanri()) {
            $this->addResponse('GojokaiKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '7880'));
        } else {
            $kaisya_cd = App_Utils::getCtxtKaisyaEasy();  // 2017/05/30 ADD Kayo
            $this->addResponse('GojokaiKbn', DataMapper_MsterGetLib::GetCodeNameMstKaisya($db, '7880', $kaisya_cd));
        }
        // 会場
        $this->addResponse('Kaijyo', DataMapper_MsterGetLib::GetKaijyoMst($db));
        // 予算入力区分
        $this->addResponse('Yosaninpkbn', array(
            array('code_kbn' => null // コード区分
                , 'code_kbn_nm' => null // コード区分名
                , 'kbn_value_cd' => '0'  // 区分値コード
                , 'kbn_value_cd_num' => '0'  // 区分値コード数値
                , 'kbn_value_lnm' => 'あり' // 区分値正式名
                , 'kbn_value_snm' => 'あり' // 区分値簡略名
            ),
            array('code_kbn' => null // コード区分
                , 'code_kbn_nm' => null // コード区分名
                , 'kbn_value_cd' => '1'  // 区分値コード
                , 'kbn_value_cd_num' => '1'  // 区分値コード数値
                , 'kbn_value_lnm' => 'なし' // 区分値正式名
                , 'kbn_value_snm' => 'なし' // 区分値簡略名
        )));
        // サービス表示区分
        $this->addResponse('ServiceDispKbn', array(
            array('code_kbn' => null            // コード区分
                , 'code_kbn_nm' => null            // コード区分名
                , 'kbn_value_cd' => '0'             // 区分値コード
                , 'kbn_value_cd_num' => '0'             // 区分値コード数値
                , 'kbn_value_lnm' => '表示なし'      // 区分値正式名
                , 'kbn_value_snm' => '表示なし'      // 区分値簡略名
            ),
            array('code_kbn' => null            // コード区分
                , 'code_kbn_nm' => null            // コード区分名
                , 'kbn_value_cd' => '1'             // 区分値コード
                , 'kbn_value_cd_num' => '1'             // 区分値コード数値
                , 'kbn_value_lnm' => '表示あり'      // 区分値正式名
                , 'kbn_value_snm' => '表示あり'      // 区分値簡略名
            )
        ));
        // 御寝棺対象外商品
        $this->addResponse('GenteiShohinKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '9654'));
        // 仕入計算区分
        $this->addResponse('SiireKeisanKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '2510'));
        // 役務区分    2017/05/15 UPD Kayo
        $this->addResponse('EkimuKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '3840'));
        // 部門マスタ
        $this->addResponse('Bumon', DataMapper_MsterGetLib::GetBumonMst($db));
//        if ( App_Utils::isFukusuKaisyaKanri() ) {   // 複数会社対応
        // 部門別商品選択有の部門マスタ
        $this->addResponse('ShohinBumonSelect', DataMapper_MsterGetLib::GetShohinBumonSelect($db));
//        }
        // 仕入先マスタ
        $this->addResponse('Siire', DataMapper_Siire::find($db, array('siire_flg' => 1)));
        // インセンティブ計算区分    2017/05/30 UPD Kayo
        $this->addResponse('IncentiveKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '3920'));
        // 仲介部門    2020/09/08 ADD Mogi　tyukai_bumon_cd
        $this->addResponse('TyukaiBumonCd', DataMapper_MsterGetLib::GetCodeNameMst($db, '8120'));
        // 自動検品区分    2020/09/09 ADD Mogi　jidou_kenpin_flg
        $this->addResponse('JidouKenpinKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '8130'));
        // 貸出品区分    2020/12/03 ADD Mogi　kashidashi_kbn
        $this->addResponse('KashidashiKbn', DataMapper_MsterGetLib::GetCodeNameMst($db, '8510'));
        // 品目コード
        $this->addResponse('HinmokuCd', DataMapper_MsterGetLib::GetCodeNameMst($db, '8588'));

        // modelConfigure
        $this->addResponse('kihonConfig', $kihonMapper->getModelConfigs(1)); // 基本モデルパラメータセット
        $this->addResponse('tankaConfig', $tankaMapper->getModelConfigs(1)); // 単価モデルパラメータセット
        $this->addResponse('bunruiConfig', $bunruiMapper->getModelConfigs(1)); // 分類モデルパラメータセット
        $this->addResponse('gojokaiConfig', $gojokaiMapper->getModelConfigs(1)); // 互助系値引モデルパラメータセット //2015/06/24 ADD Kayo
        $this->addResponse('bumonsiireConfig', $bumonsiireMapper->getModelConfigs(1)); // 部門別商品仕入先モデルパラメータセット //2017/05/15 ADD Kayo
        $this->addResponse('incentiveConfig', $incentiveMapper->getModelConfigs(1)); // インセンィブマスタモデルパラメータセット //2017/05/30 ADD Kayo
        $this->outHtml();
    }

    // ==========================================================
    // * rest common functions
    // ==========================================================
    /**
     * 登録系共通処理
     */
    protected function _inputModel($db, $mapper, $method) {
        // パラメータ取得
        $model = $this->getRequest()->getPost();
        $model = array_merge($model, $_FILES);
        $orgModel = $model; // mihara added #8115
        if ( $method === 'post' ) { // コピー由来の新規登録で論理削除データが存在する場合の調整 mihara added #8115
            $this->_adjDeletedDataOnPost($orgModel, $model);
            // 商品CD存在チェック
            $msg = null;
            if ($this->checkShohinCd($db, $model, $msg)) {
                $this->addErrorMsg($msg);
                return false;
            }
        }
        if (App_Utils::isFukusuKaisyaKanri()) {   // 複数会社対応
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $model['kaisya_cd'], 'bumon_cd' => $model['bumon_cd']));
        } else {
            $bumonData = DataMapper_Bumon::find($db, array('bumon_cd' => $model['bumon_cd']));
        }
        $tankaList = Msi_Sys_Utils::json_decode($model['tankaJson']);   // 単価
        for ($i = 0; $i < count($tankaList); $i++) {
            $tankaList[$i]['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/10/24 ADD Kayo
            if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                $tankaList[$i]['bumon_cd'] = $model['bumon_cd'];
            } else {
                $tankaList[$i]['bumon_cd'] = '00000';  // 2016/10/24 ADD Kayo
            }
        }
        $bunruiList = Msi_Sys_Utils::json_decode($model['bunruiJson']);  // 分類
        for ($i = 0; $i < count($bunruiList); $i++) {
            $bunruiList[$i]['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/10/24 ADD Kayo
            if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                $bunruiList[$i]['bumon_cd'] = $model['bumon_cd'];
            } else {
                $bunruiList[$i]['bumon_cd'] = '00000';                        // 2016/10/24 ADD Kayo
            }
        }
        $gojokaiList = Msi_Sys_Utils::json_decode($model['gojokaiJson']);  // 互助会値引　2015/06/24 ADD Kayo
        for ($i = 0; $i < count($gojokaiList); $i++) {
            $gojokaiList[$i]['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/10/24 ADD Kayo
            if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                $gojokaiList[$i]['bumon_cd'] = $model['bumon_cd'];
            } else {
                $gojokaiList[$i]['bumon_cd'] = '00000';                        // 2016/10/24 ADD Kayo
            }
        }
        $bumonsiireList = Msi_Sys_Utils::json_decode($model['bumonsiireJson']);  // 部門別商品仕入先　2017/05/15 ADD Kayo
        for ($i = 0; $i < count($bumonsiireList); $i++) {
            $bumonsiireList[$i]['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2017/05/15 ADD Kayo
        }
        $incentivList = Msi_Sys_Utils::json_decode($model['incentiveJson']);  // インセンィブマスタ　2017/05/30 ADD Kayo
        for ($i = 0; $i < count($incentivList); $i++) {
            $incentivList[$i]['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2017/05/30 ADD Kayo
        }
        // * 基本登録
        $model['delete_flg'] = "0"; // 2016/10/11 ADD Kayo
        // （未指定）をnullにする
        if (isset($model['uri_kamoku_cd']) && $model['uri_kamoku_cd'] == '9999') {
            $model['uri_kamoku_cd'] = null;
        }
        if (isset($model['uri_hojo_cd']) && $model['uri_hojo_cd'] == $this->def_hojo_kamoku_cd) {
            $model['uri_hojo_cd'] = null;
        }
        if (isset($model['uri_kamoku_cd1']) && $model['uri_kamoku_cd1'] == '9999') {
            $model['uri_kamoku_cd1'] = null;
        }
        if (isset($model['uri_hojo_cd1']) && $model['uri_hojo_cd1'] == $this->def_hojo_kamoku_cd) {
            $model['uri_hojo_cd1'] = null;
        }
        if (isset($model['uri_kamoku_cd2']) && $model['uri_kamoku_cd2'] == '9999') {
            $model['uri_kamoku_cd2'] = null;
        }
        if (isset($model['uri_hojo_cd2']) && $model['uri_hojo_cd2'] == $this->def_hojo_kamoku_cd) {
            $model['uri_hojo_cd2'] = null;
        }
        if (isset($model['uri_kamoku_cd3']) && $model['uri_kamoku_cd3'] == '9999') {
            $model['uri_kamoku_cd3'] = null;
        }
        if (isset($model['uri_hojo_cd3']) && $model['uri_hojo_cd3'] == $this->def_hojo_kamoku_cd) {
            $model['uri_hojo_cd3'] = null;
        }
        if (isset($model['uri_kamoku_cd4']) && $model['uri_kamoku_cd4'] == '9999') {
            $model['uri_kamoku_cd4'] = null;
        }
        if (isset($model['uri_hojo_cd4']) && $model['uri_hojo_cd4'] == $this->def_hojo_kamoku_cd) {
            $model['uri_hojo_cd4'] = null;
        }
        if (isset($model['siire_kamoku_cd']) && $model['siire_kamoku_cd'] == '9999') {
            $model['siire_kamoku_cd'] = null;
        }
        if (isset($model['siire_hojo_cd']) && $model['siire_hojo_cd'] == $this->def_hojo_kamoku_cd) {
            $model['siire_hojo_cd'] = null;
        }
        if (isset($model['siire_kamoku_cd1']) && $model['siire_kamoku_cd1'] == '9999') {
            $model['siire_kamoku_cd1'] = null;
        }
        if (isset($model['siire_hojo_cd1']) && $model['siire_hojo_cd1'] == $this->def_hojo_kamoku_cd) {
            $model['siire_hojo_cd1'] = null;
        }
        if (isset($model['siire_kamoku_cd2']) && $model['siire_kamoku_cd2'] == '9999') {
            $model['siire_kamoku_cd2'] = null;
        }
        if (isset($model['siire_hojo_cd2']) && $model['siire_hojo_cd2'] == $this->def_hojo_kamoku_cd) {
            $model['siire_hojo_cd2'] = null;
        }
        if (isset($model['siire_kamoku_cd3']) && $model['siire_kamoku_cd3'] == '9999') {
            $model['siire_kamoku_cd3'] = null;
        }
        if (isset($model['siire_hojo_cd3']) && $model['siire_hojo_cd3'] == $this->def_hojo_kamoku_cd) {
            $model['siire_hojo_cd3'] = null;
        }
        if (isset($model['siire_kamoku_cd4']) && $model['siire_kamoku_cd4'] == '9999') {
            $model['siire_kamoku_cd4'] = null;
        }
        if (isset($model['siire_hojo_cd4']) && $model['siire_hojo_cd4'] == $this->def_hojo_kamoku_cd) {
            $model['siire_hojo_cd4'] = null;
        }
        //Okuyama	2017/08/08	"商品摘要コンボ" がNULLの場合「未指定」が表示されない不具合対応
        if ($model['shohin_combo_cd'] == '9999') {
            $model['shohin_combo_cd'] = null;
        }
        if ($model['uri_reduced_tax_rate'] == '9') {         // 売上軽減税率 2019/05/29 ADD Kayo
            $model['uri_reduced_tax_rate'] = null;
        }
        if ($model['shiire_reduced_tax_rate'] == '9') {      // 仕入軽減税率区分）2019/05/29 ADD Kayo
            $model['shiire_reduced_tax_rate'] = null;
        }
        if (isset($model['tyukai_bumon_cd']) && $model['tyukai_bumon_cd'] == '00000') {    //未指定の表示（仲介部門）
            $model['tyukai_bumon_cd'] = null;
        }


        $model['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/10/24 ADD Kayo
        if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            $model['bumon_cd'] = $bumonData[0]['bumon_cd'];
        } else {
            $model['bumon_cd'] = '00000';                        // 2016/10/24 ADD Kayo
        }
        $mapper->$method($model);
        if ($mapper->hasError()) {
            $this->addMapperLog($mapper->getError());
            return false;
        }
        $_params = array('shohin_cd' => $model['shohin_cd'],
            'kaisya_cd' => App_Utils::getCtxtKaisyaEasy(), // 2016/10/24 ADD Kayo
            'bumon_cd' => $model['bumon_cd']                         // 2016/10/24 ADD Kayo
        );
        // * 単価登録(一括更新)
        $tankaMapper = new DataMapper_MstrShohinTankaMst($db);
        $tankaMapper->setCollection($tankaList);
        $tankaMapper->fdnMerge($_params);
        if ($tankaMapper->hasError()) {
            $this->addMapperLog($tankaMapper->getErrorLast());
            return false;
        }
        // * 分類登録(一括更新)
        $bunruiMapper = new DataMapper_MstrShohinBunruiMst($db);
        $bunruiMapper->setCollection($bunruiList);
        $bunruiMapper->fdnMerge($_params);
        if ($bunruiMapper->hasError()) {
            $this->addMapperLog($bunruiMapper->getErrorLast());
            return false;
        }
        // * 互助会値引き(一括更新)　2015/06/24 ADD Kayo
        $gojokaiMapper = new DataMapper_MstrGojokaiNebikiMst($db);
        //Msi_Sys_Utils::debug( 'gojokaiList=>' . Msi_Sys_Utils::dump($gojokaiList) );
        $gojokaiMapper->setCollection($gojokaiList);
        $gojokaiMapper->fdnMerge($_params);
        if ($gojokaiMapper->hasError()) {
            $this->addMapperLog($gojokaiMapper->getErrorLast());
            return false;
        }
        // * 部門別商品仕入先(一括更新)　2017/05/15 ADD Kayo
        $bumonsiirMapper = new DataMapper_MstrShohinBumonSiireMst($db);
        //Msi_Sys_Utils::debug( 'gojokaiList=>' . Msi_Sys_Utils::dump($gojokaiList) );
        $bumonsiirMapper->setCollection($bumonsiireList);
        // 期間の重複チェック
        if (isset($bumonsiireList) && count($bumonsiireList) > 0) {
            $duplicate = self::checkDuplicate($bumonsiireList);
            if ($duplicate === false) {
                $this->addErrorMsg('同一仕入先で適用期間が重複しています。');
                return false;
            }
        }
        $bumonsiirMapper->fdnMerge($_params);
        if ($bumonsiirMapper->hasError()) {
            $this->addMapperLog($bumonsiirMapper->getErrorLast());
            return false;
        }
        // * インセンィブマスタ(一括更新)　2017/05/15 ADD Kayo
        $incentivMapper = new DataMapper_MstrIncentiveMst($db);
        //Msi_Sys_Utils::debug( 'gojokaiList=>' . Msi_Sys_Utils::dump($gojokaiList) );
        $incentivMapper->setCollection($incentivList);
        $incentivMapper->fdnMerge($_params);
        if ($incentivMapper->hasError()) {
            $this->addMapperLog($incentivMapper->getErrorLast());
            return false;
        }

        // #8115 2023/03/xx mihara コピー由来の新規登録の場合、必要に応じ BLOB 登録
        if ( $method === 'post' ) {
            $this->_adjBlobOnPost($orgModel, $model);
        }

        // * 正常系終端
        $db->commit();
        // $this->appendAlt($model);
        // $this->addResponse($mapper->exportFormat($model));
        return true;
    }

    /**
     * 削除系共通処理
     */
    protected function _deleteModel($db, $mapper, $method) {
        // パラメータ取得
        $model = $this->getRequest()->getPost();
        if (!isset($model['__shohin_cd'])) {
            $this->addErrorMsg('パラメータが不正です。');
            return false;
        }
        // * 利用有無確認
        $msg = null;
        if ($this->existsUsage($db, $model, $msg)) {
            $this->addErrorMsg($msg);
            return false;
        }
        // 基本削除
        $mapper->setAllowFields(array('delete_flg'));
        $model['gazo_file_nm'] = null;      // 2016/10/11 ADD Kayo
        $model['gazo_img'] = null;          // 2016/10/11 ADD Kayo
        $model['delete_flg'] = "1";

        $mapper->$method($model);
        if ($mapper->hasError()) {
            $this->addMapperLog($mapper->getErrorLast());
            return false;
        }
        // 単価削除(一括)
        $model['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/10/24 ADD Kayo
        if (App_Utils::isFukusuKaisyaKanri()) {   // 複数会社対応
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $model['kaisya_cd'], 'bumon_cd' => $model['bumon_cd']));
        } else {
            $bumonData = DataMapper_Bumon::find($db, array('bumon_cd' => $model['bumon_cd']));
        }
        if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            $model['bumon_cd'] = $bumonData[0]['bumon_cd'];
        } else {
            $model['bumon_cd'] = '00000';                        // 2016/10/24 ADD Kayo
        }
        $_params = array('kaisya_cd' => $model['kaisya_cd'], 'bumon_cd' => $model['bumon_cd'], 'shohin_cd' => $model['__shohin_cd']);
        $tankaMapper = new DataMapper_MstrShohinTankaMst($db);
        $tankaMapper->fdnDelete($_params);
        if ($tankaMapper->hasError()) {
            $this->addMapperLog($tankaMapper->getErrorLast());
            return false;
        }
        // 分類削除(一括)
        $bunruiMapper = new DataMapper_MstrShohinBunruiMst($db);
        $bunruiMapper->fdnDelete($_params);
        if ($bunruiMapper->hasError()) {
            $this->addMapperLog($bunruiMapper->getErrorLast());
            return false;
        }
        // 互助会値引き削除(一括)2015/06/24 ADD Kayo
        $gojokaiMapper = new DataMapper_MstrGojokaiNebikiMst($db);
        $gojokaiMapper->fdnDelete($_params);
        if ($gojokaiMapper->hasError()) {
            $this->addMapperLog($gojokaiMapper->getErrorLast());
            return false;
        }
        // 部門別商品仕入先削除(一括)2017/05/15 ADD Kayo
        $bumonsiirMapper = new DataMapper_MstrShohinBumonSiireMst($db);
        $bumonsiirMapper->fdnDelete($_params);
        if ($bumonsiirMapper->hasError()) {
            $this->addMapperLog($bumonsiirMapper->getErrorLast());
            return false;
        }
        // インセンィブマスタ削除(一括)2017/05/30 ADD Kayo
        $incentiveMapper = new DataMapper_MstrIncentiveMst($db);
        $incentiveMapper->fdnDelete($_params);
        if ($incentiveMapper->hasError()) {
            $this->addMapperLog($incentiveMapper->getErrorLast());
            return false;
        }
        // * 正常系終端
        $db->commit();
        // $this->addResponse($mapper->exportFormat($model));
        return true;
    }

    // ==========================================================
    // * rest actions
    // ==========================================================
    /**
     * 商品情報取得
     * @url mstr/[:controller]/restget
     *
     * @request GET
     * @response json
     * @return void
     */
    public function restgetAction() {
        // パラメータ取得
        $db = Msi_Sys_DbManager::getMyDb();
        $args = $this->getRequest()->getQuery();
        $args['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy();    // 2016/10/16 ADD Kayo
        // $args2は部門別商品がある会社の商品コード重複検索用 // 2017/12/11 ADD Tosaka
        // 会社情報を取得
        $kaisyaInfo = DataMapper_KaisyaInfo::find($db);
        $args2 = $args;
        // 部門選択商品のある部門
        $selectBumonCd = array();
        // 複数会社の分の部門配列
        $anotherBumonDataArray = array();
        // 複数会社で部門選択商品のある部門
        $selectAnotherBumonCd = array();

        // 基本情報
        $kihonMapper = new DataMapper_MstrShohinMst($db);
        if (empty($args['bumon_cd'])) {
            $args['bumon_cd'] = '00000';
            $bumonData = null;
        }
        if (App_Utils::isFukusuKaisyaKanri()) {   // 複数会社対応
            foreach ($kaisyaInfo as $value) {
                if ($value['kaisya_cd'] === $args['kaisya_cd']) {
                    $select = DataMapper_Bumon::find($db, array('kaisya_cd' => $args['kaisya_cd'], 'bumon_cd' => $args['bumon_cd']));
                    $allBumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $args['kaisya_cd']));
                    if (count($select) !== 0) {
                        $bumonData = $select[0];
                        if (empty($bumonData) || $bumonData['bumon_shohin_sel_kbn'] !== '1') {
                            $args['bumon_cd'] = '00000';  // 2016/10/16 ADD Kayo
                        } else {
                            $selectBumonCd[] = '00000';    // 部門別が選択されているときは全社共通のコードを代入
                        }
                    } else {
                        $args['bumon_cd'] = '00000';  // 2017/08/10 ADD Okuyama
                    }
                } else {
                    $allBumonAnotherData = DataMapper_Bumon::find($db, array('kaisya_cd' => $value['kaisya_cd']));
                    // 部門コードのデフォルト値を複数会社ごとに挿入
                    $anotherBumonDataArray[] = array_merge($anotherBumonDataArray, array('kaisya_cd' => $value['kaisya_cd'], 'bumon_cd' => '00000', 'default_flg' => true));
                    $anotherBumonDataArray = array_merge($anotherBumonDataArray, $allBumonAnotherData);
                }
            }
        } else {
            $select = DataMapper_Bumon::find($db, array('kaisya_cd' => $args['kaisya_cd'], 'bumon_cd' => $args['bumon_cd']));
            $allBumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $args['kaisya_cd']));
            if (count($select) !== 0) {
                $bumonData = $select[0];
                if (empty($bumonData) || $bumonData['bumon_shohin_sel_kbn'] !== '1') {
                    $args['bumon_cd'] = '00000';  // 2016/10/16 ADD Kayo
                } else {
                    $selectBumonCd[] = '00000';    // 部門別が選択されているときは全社共通のコードを代入
                }
            } else {
                $args['bumon_cd'] = '00000';  // 2017/08/10 ADD Okuyama
            }
        }
        // 現会社の全部門から選択区分が1のものを配列にいれる
        if (count($allBumonData) !== 0) {
            foreach ($allBumonData as $oneData) {
                if ($args['bumon_cd'] !== $oneData['bumon_cd'] && $oneData['bumon_shohin_sel_kbn'] === '1') {
                    $selectBumonCd[] = $oneData['bumon_cd'];
                }
            }
        }
        // 複数会社分の全部門からデフォルト値と選択区分が1のものを配列にいれる
        if (count($anotherBumonDataArray) !== 0) {
            foreach ($anotherBumonDataArray as $oneData) {
                if (isset($oneData['default_flg']) && $oneData['default_flg']) {
                    $selectAnotherBumonCd[] = $oneData;
                } else if ($args['bumon_cd'] !== $oneData['bumon_cd'] && $oneData['bumon_shohin_sel_kbn'] === '1') {
                    $selectAnotherBumonCd[] = $oneData;
                }
            }
        }
        // 現会社の部門別選択商品の有無
//        if (isset($args['shohin_cd']) && isset($args['bumon_cd'])) {
//            foreach ($selectBumonCd as $oneBumonCd) {
//                $args2['bumon_cd'] = $oneBumonCd;
//                $model2 = $kihonMapper->get($args2);
//                if ($model2 && count($model2) !== 0) {
//                    $args2['_isnew'] = 2;
//                    $args2['msg'] = 'この商品コードは既に使用されています。';
//                    $this->addResponse($args2);
//                    $this->outJson();
//                    return;
//                }
//            }
//        }
        // 複数会社分の部門別選択商品の有無
//        if (count($selectAnotherBumonCd) > 0) {
//            if (isset($args['shohin_cd']) && isset($args['bumon_cd'])) {
//                foreach ($selectAnotherBumonCd as $oneBumonData) {
//                    $args2['bumon_cd'] = $oneBumonData['bumon_cd'];
//                    $args2['kaisya_cd'] = $oneBumonData['kaisya_cd'];
//                    $model2 = $kihonMapper->get($args2);
//                    if ($model2 && count($model2) !== 0) {
//                        $args2['_isnew'] = 2;
//                        $args2['msg'] = 'この商品コードは既に使用されています。';
//                        $this->addResponse($args2);
//                        $this->outJson();
//                        return;
//                    }
//                }
//            }
//        }
        $model = $kihonMapper->get($args);
        if (!$model || $model['delete_flg'] !== "0") {
            $kihonMapper->setModel($args);
            $args['delete_flg'] = $model['delete_flg'];

            //Okuyama 2017/08/08 NULLあり項目の画面表示用初期値設定
            $args['uri_kamoku_cd'] = '9999';
            $args['uri_hojo_cd'] = $this->def_hojo_kamoku_cd;
            $args['uri_kamoku_cd1'] = '9999';
            $args['uri_hojo_cd1'] = $this->def_hojo_kamoku_cd;
            $args['uri_kamoku_cd2'] = '9999';
            $args['uri_hojo_cd2'] = $this->def_hojo_kamoku_cd;
            $args['uri_kamoku_cd3'] = '9999';
            $args['uri_hojo_cd3'] = $this->def_hojo_kamoku_cd;
            $args['uri_kamoku_cd4'] = '9999';
            $args['uri_hojo_cd4'] = $this->def_hojo_kamoku_cd;
            $args['siire_kamoku_cd'] = '9999';
            $args['siire_hojo_cd'] = $this->def_hojo_kamoku_cd;
            $args['siire_kamoku_cd1'] = '9999';
            $args['siire_hojo_cd1'] = $this->def_hojo_kamoku_cd;
            $args['siire_kamoku_cd2'] = '9999';
            $args['siire_hojo_cd2'] = $this->def_hojo_kamoku_cd;
            $args['siire_kamoku_cd3'] = '9999';
            $args['siire_hojo_cd3'] = $this->def_hojo_kamoku_cd;
            $args['siire_kamoku_cd4'] = '9999';
            $args['siire_hojo_cd4'] = $this->def_hojo_kamoku_cd;
            $args['shohin_combo_cd'] = '9999';
            $args['tyukai_bumon_cd'] = '00000';
            //Okuyama 2017/08/08 NULLあり項目の画面表示用初期値設定 ここまで

            if (!$model)
                $args['_isnew'] = 1;
            $this->addResponse($args);
            $this->addSuccessMsg('指定の商品コードで新規に登録します。');
            $this->outJson();
            return;
        }
        $model = $kihonMapper->exportFormat($model);
        // nullの場合は（未指定）が表示されないため9999を設定
        if (!isset($model['uri_kamoku_cd'])) {
            $model['uri_kamoku_cd'] = '9999';
        }
        if (!isset($model['uri_hojo_cd'])) {
            $model['uri_hojo_cd'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['uri_kamoku_cd1'])) {
            $model['uri_kamoku_cd1'] = '9999';
        }
        if (!isset($model['uri_hojo_cd1'])) {
            $model['uri_hojo_cd1'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['uri_kamoku_cd2'])) {
            $model['uri_kamoku_cd2'] = '9999';
        }
        if (!isset($model['uri_hojo_cd2'])) {
            $model['uri_hojo_cd2'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['uri_kamoku_cd3'])) {
            $model['uri_kamoku_cd3'] = '9999';
        }
        if (!isset($model['uri_hojo_cd3'])) {
            $model['uri_hojo_cd3'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['uri_kamoku_cd4'])) {
            $model['uri_kamoku_cd4'] = '9999';
        }
        if (!isset($model['uri_hojo_cd4'])) {
            $model['uri_hojo_cd4'] = $this->def_hojo_kamoku_cd;
        }

        if (!isset($model['siire_kamoku_cd'])) {
            $model['siire_kamoku_cd'] = '9999';
        }
        if (!isset($model['siire_hojo_cd'])) {
            $model['siire_hojo_cd'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['siire_kamoku_cd1'])) {
            $model['siire_kamoku_cd1'] = '9999';
        }
        if (!isset($model['siire_hojo_cd1'])) {
            $model['siire_hojo_cd1'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['siire_kamoku_cd2'])) {
            $model['siire_kamoku_cd2'] = '9999';
        }
        if (!isset($model['siire_hojo_cd2'])) {
            $model['siire_hojo_cd2'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['siire_kamoku_cd3'])) {
            $model['siire_kamoku_cd3'] = '9999';
        }
        if (!isset($model['siire_hojo_cd3'])) {
            $model['siire_hojo_cd3'] = $this->def_hojo_kamoku_cd;
        }
        if (!isset($model['siire_kamoku_cd4'])) {
            $model['siire_kamoku_cd4'] = '9999';
        }
        if (!isset($model['siire_hojo_cd4'])) {
            $model['siire_hojo_cd4'] = $this->def_hojo_kamoku_cd;
        }

        //Okuyama	2017/08/08	"商品摘要コンボ" がNULLの場合「未指定」が表示されない不具合対応
        if (!isset($model['shohin_combo_cd'])) {
            $model['shohin_combo_cd'] = '9999';
        }
        if (!isset($model['uri_reduced_tax_rate'])) {         //未指定の表示（売上軽減税率） 2019/05/29 ADD Kayo
            $model['uri_reduced_tax_rate'] = '9';
        }
        if (!isset($model['shiire_reduced_tax_rate'])) {      //未指定の表示（仕入軽減税率区分）  2019/05/29 ADD Kayo
            $model['shiire_reduced_tax_rate'] = '9';
        }
        if (!isset($model['tyukai_bumon_cd'])) {
            $model['tyukai_bumon_cd'] = '00000';
        }

        // 単価情報
        $tankaMapper = new DataMapper_MstrShohinTankaMst($db);
        $_config = array();
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'delete_flg = ?', 0);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'shohin_cd  = ?', $model['shohin_cd']);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'kaisya_cd  = ?', App_Utils::getCtxtKaisyaEasy());   // 2016/10/24 ADD Kayo
        if (isset($bumonData) && $bumonData['bumon_shohin_sel_kbn'] === '1') {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', $bumonData['bumon_cd']);
        } else {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', '00000');                           // 2016/10/24 ADD Kayo
        }
        $tankaMapper->all($_config);
        $model['tankaCollection'] = $tankaMapper->exportFormat($tankaMapper->getCollection());
        // 分類情報
        $bunruiMapper = new DataMapper_MstrShohinBunruiMst($db);
        $_config = array();
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'delete_flg = ?', 0);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'shohin_cd  = ?', $model['shohin_cd']);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'kaisya_cd  = ?', App_Utils::getCtxtKaisyaEasy());   // 2016/10/24 ADD Kayo
        if (isset($bumonData) && $bumonData['bumon_shohin_sel_kbn'] === '1') {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', $bumonData['bumon_cd']);
        } else {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', '00000');                           // 2016/10/24 ADD Kayo
        }
        $bunruiMapper->all($_config);
        $model['bunruiCollection'] = $bunruiMapper->exportFormat($bunruiMapper->getCollection());
        // 互助会値引き　2015/06/24 ADD Kayo
        $gojokaiMapper = new DataMapper_MstrGojokaiNebikiMst($db);
        $_config = array();
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'delete_flg = ?', 0);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'shohin_cd  = ?', $model['shohin_cd']);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'J1.kbn_value_lnm  IS NOT NULL', NULL);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'kaisya_cd  = ?', App_Utils::getCtxtKaisyaEasy());   // 2016/10/24 ADD Kayo
        if (isset($bumonData) && $bumonData['bumon_shohin_sel_kbn'] === '1') {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', $bumonData['bumon_cd']);
        } else {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', '00000');                           // 2016/10/24 ADD Kayo
        }
        $gojokaiMapper->all($_config);
        $model['gojokaiCollection'] = $gojokaiMapper->exportFormat($gojokaiMapper->getCollection());
        // 部門別商品仕入先　2017/05/15 ADD Kayo
        $bumonsiirMapper = new DataMapper_MstrShohinBumonSiireMst($db);
        $_config = array();
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'delete_flg = ?', 0);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'shohin_cd  = ?', $model['shohin_cd']);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'kaisya_cd  = ?', App_Utils::getCtxtKaisyaEasy());   // 2016/10/24 ADD Kayo
        if (isset($bumonData) && $bumonData['bumon_shohin_sel_kbn'] === '1') {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', $bumonData['bumon_cd']);
        } else {
            $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'bumon_cd  = ?', '00000');                           // 2016/10/24 ADD Kayo
        }
        $bumonsiirMapper->all($_config);
        $model['bumonsiireCollection'] = $bumonsiirMapper->exportFormat($bumonsiirMapper->getCollection());
        // インセンィブマスタ　2017/05/30 ADD Kayo
        $incentiveMapper = new DataMapper_MstrIncentiveMst($db);
        $_config = array();
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'delete_flg = ?', 0);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'shohin_cd  = ?', $model['shohin_cd']);
        $_config['where'][] = App_Mstr_Utils_Db::quoteInto($db, 'kaisya_cd  = ?', App_Utils::getCtxtKaisyaEasy());   // 2016/10/24 ADD Kayo
        $incentiveMapper->all($_config);
        $model['incentiveCollection'] = $incentiveMapper->exportFormat($incentiveMapper->getCollection());
        // 出力
        $this->addResponse($model);
        $this->outJson();
    }

    /**
     * 商品情報更新
     * @url mstr/[:controller]/restput
     *
     * @request POST
     * @response json
     * @return void
     */
    public function restputAction() {
        // 初期化
        $db = Msi_Sys_DbManager::getMyDb();
        $mapper = new DataMapper_MstrShohinMst($db);
        // 出力
        if ($this->_inputModel($db, $mapper, 'put'))
            $this->addSuccessMsg('更新しました。');
        $this->outJson();
    }

    /**
     * 商品情報新規登録
     * @url mstr/[:controller]/restpost
     *
     * @request POST
     * @response json
     * @return void
     */
    public function restpostAction() {
        // 初期化
        $db = Msi_Sys_DbManager::getMyDb();
        $mapper = new DataMapper_MstrShohinMst($db);
        // 出力
        if ($this->_inputModel($db, $mapper, 'post'))
            $this->addSuccessMsg('新規登録しました。');
        $this->outJson();
    }

    /**
     * 商品情報削除
     * @url mstr/[:controller]/resthide
     *
     * @request POST
     * @response json
     * @return void
     */
    public function resthideAction() {
        // 初期化
        $db = Msi_Sys_DbManager::getMyDb();
        $mapper = new DataMapper_MstrShohinMst($db);
        // 出力
        if ($this->_deleteModel($db, $mapper, 'put'))
            $this->addSuccessMsg('削除しました。');
        $this->outJson();
    }

    /**
     * 使用目的区分
     * [商品区分マスタ:shohin_kbn_mst]より逆引き
     * @url mstr/[:controller]/restgetmokutekikbn
     * 
     * @request GET
     * @response json
     * @return void
     *
     * response [{
     *    "id":"word1",
     *    "text":"word2"
     * },...]
     */
    public function restgetmokutekikbnAction() {
        // パラメータ取得
        $args = $this->getRequest()->getQuery();
        // 初期化
        $db = Msi_Sys_DbManager::getMyDb();
        // 参照
        $_resp = array();
        if (@$args['shohin_kbn']) {
            $_params = array();
            $_params[] = $args['shohin_kbn'];
            $_sql = <<<SQL
SELECT 
       S01.kbn_value_cd_num  AS id
      ,S01.kbn_value_lnm     AS text
  FROM shohin_kbn_mst AS M01
    INNER JOIN (
      SELECT 
           CASE code_kbn 
             WHEN '1010' THEN 2 -- 返礼品
             WHEN '1020' THEN 3 -- 料理
             WHEN '4670' THEN 4 -- 車両
             ELSE 0
           END               AS mokuteki_inp_kbn
          ,kbn_value_cd_num
          ,kbn_value_lnm
        FROM code_nm_mst
        WHERE delete_flg = 0 
          AND code_kbn IN ('1010', '1020', '4670')
    ) AS S01
      ON S01.mokuteki_inp_kbn = M01.mokuteki_inp_kbn
  WHERE M01.shohin_kbn = ?
SQL;
            $_resp = $db->easySelect($_sql, $_params);
        }
        if (!$_resp) {
            $_resp[] = array(
                'id' => 0,
                'text' => 'なし',
            );
        }
        $this->addResponse($_resp);
        $this->outJson();
    }

    /**
     * 科目コードから補助科目を取得
     * 
     * <AUTHOR> Kayo
     * @since      2015/05/18
     */
    public function getkamokuhojoAction() {
        $db = Msi_Sys_DbManager::getMyDb();

        $req = $this->getRequest();

        $kamoku_cd = $req->getPost('kamoku_cd');
        $dataApp = array('kamoku_cd' => $kamoku_cd);
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();            // 2017/01/24 ADD Kayo
        $hojodata = DataMapper_MstrKamokuHojoMst::find($db, array(
                    'delete_flg' => 0,
                    'kamoku_cd' => $kamoku_cd,
                    'kaisya_cd' => $kaisya_cd, // 2017/01/24 ADD Kayo
                    '__etc_orderby' => array('disp_no, hojo_cd') // 2017/09/17 ADD Kayo
        ));
        $dataApp = array();
        $i = 0;
        $dataApp[$i]['id'] = $this->def_hojo_kamoku_cd;
        $dataApp[$i]['text'] = '（未指定）';
        ++$i;
        foreach ($hojodata as $rec) {
            $dataApp[$i]['id'] = $rec['hojo_cd'];
            $dataApp[$i]['text'] = $rec['hojo_nm'];
            ++$i;
        }
        // 初期設定データ
        $data = array(
            'status' => 'OK',
            'dataApp' => $dataApp,
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 部門別仕入先マスタ 登録前に期間重複チェック処理
     * <AUTHOR> Mogi
     * @since      2020/09/18
     * @param array $dataCol	
     * @return true or false
     */
    private function checkDuplicate($dataCol) {

        foreach ($dataCol as $key => $value) {
            $id1[$key] = $value['siire_cd'];
            $id2[$key] = $value['tekiyo_st_date'];
            $id3[$key] = $value['tekiyo_ed_date'];
        }
        array_multisort($id1, SORT_ASC, $id2, SORT_ASC, $id3, SORT_ASC, $dataCol);

        $oldSiireCd = null;
        $oldTekiyoEdDate = null;

        foreach ($dataCol as $row) {

            if ($oldSiireCd === $row['siire_cd']) {
                // 仕入コードが同じで適用開始日が前じゃないとき
                if (!($oldTekiyoEdDate < $row['tekiyo_st_date'])) {
                    return false;
                }
            }
            $oldSiireCd = $row['siire_cd'];
            $oldTekiyoEdDate = $row['tekiyo_ed_date'];
        }
        return;
    }

    /**
     * 既存存在チェック アクション
     *
     * <AUTHOR> Mihara
     * @since  2023/03/xx
     */
    public function checkexistingAction()
    {
        $db = Msi_Sys_DbManager::getMyDb();

        try {
            // 入力を JSON で受け取る
            $dataReq = Msi_Sys_Utils::restReqData();
            // Msi_Sys_Utils::debug( 'dataReq==>' . Msi_Sys_Utils::dump($dataReq) );

            $tgt_shohin_cd = $dataReq['new_shohin_cd'];
            $tgt_bumon_cd  = $dataReq['new_bumon_cd'];
            $tgt_kaisya_cd = $dataReq['new_kaisya_cd'];

            $args = array( 'shohin_cd' => $tgt_shohin_cd,
                           'bumon_cd'  => $tgt_bumon_cd,
                           'kaisya_cd' => $tgt_kaisya_cd );

            // 基本情報
            $kihonMapper = new DataMapper_MstrShohinMst($db);
            $model = $kihonMapper->get($args);

            // Msi_Sys_Utils::debug( 'model==>' . Msi_Sys_Utils::dump($model) );
            $isExisting = ($model && $model['delete_flg'] == 0) ? 1 : 0; // 論理削除したものもコピー対象

            // コピー元のデータ
            $cond = array( 'shohin_cd' => $dataReq['old_shohin_cd'],
                           'bumon_cd'  => $dataReq['old_bumon_cd'],
                           'kaisya_cd' => $dataReq['old_kaisya_cd'] );

            $orgShohinRec = $db->easySelOne( <<< END_OF_SQL
SELECT * FROM shohin_mst 
 WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=0
END_OF_SQL
                                          , $cond );

            $outData = array( 'status'        => 'OK',
                              'isExisting'    => $isExisting,
                              'orgShohinRec'  => $orgShohinRec );
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $outData = array(
                             'status' => 'NG',
                             'msg' => $userMsg,
                             );
        }

        Msi_Sys_Utils::outJson( $outData );
    }

    /**
     * コピー由来の新規登録の場合、必要に応じ BLOB 登録
     *
     * <AUTHOR> Mihara
     * @since  2023/03/xx
     */
    protected function _adjBlobOnPost($param, $model)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        // Msi_Sys_Utils::debug( '@@@ _adjBlobOnPost param ==>' . Msi_Sys_Utils::dump($param) );

        if ( !array_key_exists('_copied_data_flg', $param) || !$param['_copied_data_flg'] ) {
            return;
        }

        $kaisya_cd = $model['kaisya_cd'];
        $bumon_cd  = $model['bumon_cd'];
        $shohin_cd = $model['shohin_cd'];
        $org_kaisya_cd = $param['_copied_kaisya_cd'];
        $org_bumon_cd  = $param['_copied_bumon_cd'];
        $org_shohin_cd = $param['_copied_shohin_cd'];

        if ( (array_key_exists('_copied_gazo_img', $param) && $param['_copied_gazo_img']) && // コピー元あり
             !(array_key_exists('gazo_img__del', $param) && $param['gazo_img__del'] ==1) && // 削除された場合は除く
             !(array_key_exists('gazo_img', $param) && is_array($param['gazo_img'])) // 改めて登録された場合も除く
        ) {
            $org_gazo_img = $param['_copied_gazo_img'];
            $orgShohinRec = $db->easySelOne( <<< END_OF_SQL
SELECT * FROM shohin_mst 
 WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND gazo_img=:gazo_img
   AND delete_flg=0
END_OF_SQL
                                             , array( 'kaisya_cd' => $org_kaisya_cd,
                                                      'bumon_cd'  => $org_bumon_cd,
                                                      'shohin_cd' => $org_shohin_cd,
                                                      'gazo_img'  => $org_gazo_img ) );

            if ( $orgShohinRec ) { // コピー元があるなら
                $gazo_file_nm = $orgShohinRec['gazo_file_nm']; // $param['_copied_gazo_file_nm'];
                $gazo_tn_data = $orgShohinRec['gazo_tn_data'];
                $gazo_img = $db->copyBlob( $org_gazo_img );

                $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE shohin_mst 
   SET gazo_img=:gazo_img, gazo_file_nm=:gazo_file_nm, gazo_tn_data=:gazo_tn_data
 WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd
   AND gazo_img IS NULL
   AND delete_flg=0
END_OF_SQL
                                         , array( 'kaisya_cd' => $kaisya_cd,
                                                  'bumon_cd'  => $bumon_cd,
                                                  'shohin_cd' => $shohin_cd,
                                                  'gazo_img'  => $gazo_img,
                                                  'gazo_file_nm' => $gazo_file_nm,
                                                  'gazo_tn_data' => $gazo_tn_data ) );
            }
        }
    }

    /**
     * コピー由来の新規登録で論理削除データが存在する場合に既存データを物理削除
     *
     * <AUTHOR> Mihara
     * @since  2023/03/xx
     */
    protected function _adjDeletedDataOnPost($param, $model)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        if ( !array_key_exists('_copied_data_flg', $param) || !$param['_copied_data_flg'] ) {
            return;
        }

        $kaisya_cd = $model['kaisya_cd'];
        $bumon_cd  = $model['bumon_cd'];
        $shohin_cd = $model['shohin_cd'];

        $cnt1 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM shohin_tanka_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );

        $cnt2 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM shohin_bunrui_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );

        $cnt3 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM gojokai_nebiki_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );

        $cnt4 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM shohin_bumon_siire_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );

        $cnt5 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM incentive_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );

        $cnt0 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM shohin_mst WHERE kaisya_cd=:kaisya_cd AND bumon_cd=:bumon_cd AND shohin_cd=:shohin_cd AND delete_flg=1
END_OF_SQL
                                  , array('kaisya_cd' => $kaisya_cd, 'bumon_cd' => $bumon_cd, 'shohin_cd' => $shohin_cd) );
    }
    /**
     * 他テーブル利用中チェック
     * @return boolean;
     */
    protected function checkShohinCd($db, $model, &$msg = null) {
        // 商品コードが存在する場合 
        $select = $db->easySelect(<<< END_OF_SQL
        select 
            bumon_lv.bumon_cd
            , bumon_lv.lv
            , sm.shohin_cd
            , sm.shohin_nm
        from 
            (select 
                lv1.ko_bumon_cd as bumon_cd
                , lv1bm.bumon_lnm as bumon_nm
                , 1 as lv
            from 
                (select 
                    ko_bumon_cd 
                from bumon_kaso_mst 
                where oya_bumon_cd = '00000' 
                and delete_flg = 0
                ) as lv1  -- 1階層('00000'はハードコード)
            inner join bumon_mst lv1bm 
            on lv1bm.bumon_cd = lv1.ko_bumon_cd 
            and lv1bm.bumon_shohin_sel_kbn = 1    
            union all    
            select 
                lv2.ko_bumon_cd as lv_bumon_cd
                , lv2bm.bumon_lnm as lv_bumon_nm
                , 2 as lv
            from 
                (select 
                    ko_bumon_cd 
                from bumon_kaso_mst 
                where oya_bumon_cd = '00000' 
                and delete_flg = 0
                ) as lv1  -- 1階層('00000'はハードコード)
            left join bumon_kaso_mst lv2 
            on lv2.oya_bumon_cd = lv1.ko_bumon_cd 
            and lv2.delete_flg = 0
            inner join bumon_mst lv2bm 
            on lv2bm.bumon_cd = lv2.ko_bumon_cd 
            and lv2bm.bumon_shohin_sel_kbn = 1
            union all
            select 
                lv3.ko_bumon_cd as lv_bumon_cd
                , lv3bm.bumon_lnm as lv_bumon_nm
                , 3 as lv
            from (select ko_bumon_cd 
                from bumon_kaso_mst 
                where oya_bumon_cd = '00000' 
                and delete_flg = 0
                ) as lv1  -- 1階層('00000'はハードコード)
            left join bumon_kaso_mst lv2 
            on lv2.oya_bumon_cd = lv1.ko_bumon_cd 
            and lv2.delete_flg = 0
            left join bumon_kaso_mst lv3 
            on lv3.oya_bumon_cd = lv2.ko_bumon_cd 
            and lv3.delete_flg = 0
            inner join bumon_mst lv1bm 
            on lv1bm.bumon_cd = lv1.ko_bumon_cd 
            and lv1bm.bumon_shohin_sel_kbn = 1
            inner join bumon_mst lv2bm 
            on lv2bm.bumon_cd = lv2.ko_bumon_cd 
            and lv2bm.bumon_shohin_sel_kbn = 1
            inner join bumon_mst lv3bm 
            on lv3bm.bumon_cd = lv3.ko_bumon_cd 
            and lv3bm.bumon_shohin_sel_kbn = 1 
            and lv3bm.bumon_cd = lv3bm.kaikei_bumon_cd
            union all        
            select 
                lv4bm.bumon_cd as lv_bumon_cd
                , lv4bm.bumon_lnm as lv_bumon_nm
                , 4 as lv
            from (select 
                ko_bumon_cd 
                from bumon_kaso_mst 
                where oya_bumon_cd = '00000' 
                and delete_flg = 0
                ) as lv1  -- 1階層('00000'はハードコード)
            left join bumon_kaso_mst lv2 
            on lv2.oya_bumon_cd = lv1.ko_bumon_cd 
            and lv2.delete_flg = 0
            left join bumon_kaso_mst lv3 
            on lv3.oya_bumon_cd = lv2.ko_bumon_cd 
            and lv3.delete_flg = 0
            inner join bumon_mst lv1bm 
            on lv1bm.bumon_cd = lv1.ko_bumon_cd 
            and lv1bm.bumon_shohin_sel_kbn = 1
            inner join bumon_mst lv2bm 
            on lv2bm.bumon_cd = lv2.ko_bumon_cd 
            and lv2bm.bumon_shohin_sel_kbn = 1
            inner join bumon_mst lv3bm 
            on lv3bm.bumon_cd = lv3.ko_bumon_cd 
            and lv3bm.bumon_shohin_sel_kbn = 1
            and lv3bm.bumon_cd = lv3bm.kaikei_bumon_cd
            inner join bumon_mst lv4bm 
            on lv3bm.bumon_cd = lv3.ko_bumon_cd 
            and lv3bm.bumon_shohin_sel_kbn = 1
            and lv4bm.bumon_cd <> lv4bm.kaikei_bumon_cd 
            and lv3bm.bumon_cd = lv4bm.kaikei_bumon_cd
        ) bumon_lv
        left join shohin_mst sm 
        on sm.bumon_cd = bumon_lv.bumon_cd 
        and sm.delete_flg = 0
        and sm.shohin_cd = :shohin_cd
        order by bumon_lv.bumon_cd       
END_OF_SQL
                , array('shohin_cd' => $model['shohin_cd']));
        if (count($select) > 0) {
            $keyIndex = array_search($model['bumon_cd'], array_column($select, 'bumon_cd'));
            $curBumon = $select[$keyIndex];
            foreach ($select as $val) {
                if (($curBumon['lv'] != $val['lv']) && isset($val['shohin_cd'])) {
                    $msg = '該当の商品コードは使用中のため、登録できません。';
                    return true;
                }
            }
        }
        return false;
    }

}
