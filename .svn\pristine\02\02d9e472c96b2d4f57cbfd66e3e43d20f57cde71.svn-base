/* 
 * 請求書合算処理
 */
var appskgs = appskgs || {};
$(function () {
    "use strict";
    var utils = window.msiBbUtils;
    // モデル属性と要素のオブジェクト
    appskgs.pro = {
        sekyu_nm: '#g_seikyu_nm'
        , shohin_nm: '#g_seikyu_hinmei'
        , uri_den_no: '.seikyu_no'
    };
    var SHORI_MODE_NEW = '1'; // 処理モード　1:新規
    var SHORI_MODE_CHG = '2';// 処理モード　2:修正
    var SEIKYU_APPROVAL = '1'; // 承認済
    var BUN_GAS_SAKI = '20'; // 合算先
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                shori_mode: SHORI_MODE_NEW, // 処理モード 1:新規　2:修正
                bunkatsu_kbn: '0', // 分割元区分　0:葬儀　1：別注品
                s_kaisya: null, // 検索会社
                s_seko_no: null, // 検索施行番号
                s_seikyu_no: null, // 検索請求番号
                s_gseikyu_no: null, // 合算先検索請求番号
                kokyaku_kbn: null, // 合算元顧客区分
                s_kokyaku_no: null, // 合算元顧客番号
                s_kokyaku_nm: null, // 合算元顧客名
                s_seikyusyo_kbn: null, // 合算元請求書区分
                s_shime_ymd: null, // 合算元締め日
                s_nonyu_st_ymd: null, // 検索納品日FROM
                s_nonyu_ed_ymd: null, // 検索納品日TO
                seikyu_den_no: null, // 請求番号
                nonyu_dt: null, // 納品日
                bank_cd: null, // 振込銀行コード
                bank_nm: null, // 振込銀行名
                pay_kbn: '1', // 支払方法
                harai_ymd: null, // 支払予定日
                seikyu_zei_prc: null, // 請求金額(合算元)
                sekyu_nm1: null, // 請求先名1
                sekyu_nm2: null, // 請求先名2
                sekyu_knm1: null, // 請求先カナ名1
                sekyu_knm2: null, // 請求先カナ名2
                tokuisaki_nm: null, // 得意先名
                nafuda_nms: null, // 名札名義
                sekyu_soufu_nm: null, // 請求書送付先名
                sekyu_soufu_nm2: null, // 請求書送付先名2
                soufu_tel: null, // 請求書送付先tel
                soufu_yubin_no: null, // 請求書送付先郵便番号
                soufu_addr1: null, // 請求書送付先住所1
                soufu_addr2: null, // 請求書送付先住所2
                syorui_tenpu_kbn: null, // 請求書フォーマット
                status_kbn: null, // ステータス区分
                bun_gas_kbn_num: null, // 分割合算区分
                _mod_cnt: null, // 更新回数

                rs_print_kbn: null, // 領収書発行
                ryosyusyo_meigi: null, // 領収書名義
                ryosyusyo_soufu_nm: null, // 領収書送付先名
                ryosyusyo_soufu_yubin_no: null, // 領収書送付先郵便番号
                ryosyusyo_soufu_addr1: null, // 領収書送付先住所1
                ryosyusyo_soufu_addr2: null, // 領収書送付先住所2

                gassan_prc_sum: 0, // 合算請求金額合計

                seikyu_prc_sum: 0, // 明細請求金額合計
                nyukin_prc_sum: 0, // 明細入金金額合計
                mimnyu_prc_sum: 0, // 明細未入金額合計

                sekyu_dlg_flag: false
            };
        },
        validation: {
            harai_ymd: {
                required: false
            },
            sekyu_nm1: {
                required: false
            },
            sekyu_knm1: {
                required: false
            },
            sekyu_soufu_nm: {
                required: true
            },
            soufu_yubin_no: {
                required: true
            },
            soufu_addr1: {
                required: true
            },
            ryosyusyo_meigi: {
                required: function () {
                    // 領収書発行不要な場合は必須チェック無し
                    if (appskgs.appModel.get("rs_print_kbn") == '2') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            ryosyusyo_soufu_nm: {
                required: function () {
                    // 領収書発行不要または領収書用紙不要な場合は必須チェック無し
                    if (appskgs.appModel.get("rs_print_kbn") == '2' || appskgs.appModel.get("rs_soufu_kbn") == '2') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            ryosyusyo_soufu_yubin_no: {
                required: function () {
                    // 領収書発行不要または領収書用紙不要な場合は必須チェック無し
                    if (appskgs.appModel.get("rs_print_kbn") == '2' || appskgs.appModel.get("rs_soufu_kbn") == '2') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            ryosyusyo_soufu_addr1: {
                required: function () {
                    // 領収書発行不要または領収書用紙不要な場合は必須チェック無し
                    if (appskgs.appModel.get("rs_print_kbn") == '2' || appskgs.appModel.get("rs_soufu_kbn") == '2') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
        },
        labels: {
            harai_ymd: '支払予定日',
            sekyu_nm1: '請求先名1',
            sekyu_knm1: '請求先カナ名1',
            sekyu_soufu_nm: '請求書送付先名',
            soufu_yubin_no: '請求書送付先郵便番号',
            soufu_addr1: '請求書送付先住所1',
            ryosyusyo_meigi: '領収書名義',
            ryosyusyo_soufu_nm: '領収書送付先名',
            ryosyusyo_soufu_yubin_no: '領収書送付先郵便番号',
            ryosyusyo_soufu_addr1: '領収書送付先住所1',
        }
    });
    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#order"),
        events: {
            "click .dlg_s_seikyu_no": "seikyuDenpyoHelper",
            "click .dlg_s_gseikyu_cd": 'gseikyuDenpyoHelper',
            "click .dlg_date": "setDatePicker",
            "click .dlg_bank": "bankHelper",
            "click #btn_search": 'doSearch',
            "click #btn_save": "doSave",
            "click #btn_del": "doDelete",
            "click #btn_clear": "doCancel",
            "click #btn_kokyaku_reset": "doSearchKokyaku",
//            "click .zip_no-ref": "zipHelper",
            "click .zip_no-ref2": "zipHelper2",
            "click .s_copy": "seikyuCopy",
            "click .r_copy": "ryoshuCopy",
        },
        bindings: {
            '#bunkatsu_kyoka': $.msiJqlib.getRadioBinding('bunkatsu_kbn'),
            '#s_kaisya': {
                observe: 's_kaisya',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    $el.select2("val", vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
            '#s_seikyu_no': 's_seikyu_no',
            '#s_gseikyu_no': 's_gseikyu_no',
            '#s_kokyaku_no': 's_kokyaku_no',
            '#s_kokyaku_nm': 's_kokyaku_nm',
            '#s_seikyusyo_kbn': {
                observe: 's_seikyusyo_kbn',
                afterUpdate: $.msiJqlib.setSelect2Val
            },
            '#s_shime_ymd': 's_shime_ymd',
            '#s_nonyu_st_ymd': 's_nonyu_st_ymd',
            '#s_nonyu_ed_ymd': 's_nonyu_ed_ymd',
            '#g_seikyu_no': 'seikyu_den_no',
            '#g_harai_ymd': 'harai_ymd',
            '#g_nonyu_dt': 'nonyu_dt',
            '#g_bank_cd': 'bank_cd',
            '#g_bank_nm': 'bank_nm',
            '#g_syorui_tenpu_kbn': {
                observe: 'syorui_tenpu_kbn',
                afterUpdate: $.msiJqlib.setSelect2Val
            },
            '#g_seikyu_nm1': 'sekyu_nm1',
            '#g_seikyu_nm2': 'sekyu_nm2',
            '#g_seikyu_knm1': 'sekyu_knm1',
            '#g_seikyu_knm2': 'sekyu_knm2',
            '#g_tokuisaki_nm': 'tokuisaki_nm',
            '#g_nafuda_nm': 'nafuda_nms',
            '#g_seikyu_soufu_saki_mei': 'sekyu_soufu_nm',
            '#g_seikyu_soufu_saki_mei2': 'sekyu_soufu_nm2',
            '#g_seikyu_soufu_saki_tel': 'soufu_tel',
            '#g_sekyu_yubin_no': 'soufu_yubin_no',
            '#g_seikyu_addr1': 'soufu_addr1',
            '#g_seikyu_addr2': 'soufu_addr2',
            '#g_ryoshu_meigi': 'ryosyusyo_meigi',
            '#g_ryosyusyo_soufu_nm': 'ryosyusyo_soufu_nm',
            '#g_ryoshu_yubin_no': 'ryosyusyo_soufu_yubin_no',
            '#g_ryoshu_addr1': 'ryosyusyo_soufu_addr1',
            '#g_ryoshu_addr2': 'ryosyusyo_soufu_addr2',
            '#g_ryoshu_hakko': {
                observe: 'rs_print_kbn',
                afterUpdate: $.msiJqlib.setSelect2Val
            },
            '#g_seikyu_prc': {
                observe: 'seikyu_zei_prc',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#g_uri_prc_sum': {
                observe: 'gassan_prc_sum',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },

            '#m_seikyu_prc': {
                observe: 'seikyu_prc_sum',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#m_nyukin_prc': {
                observe: 'nyukin_prc_sum',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#m_mimnyu_prc': {
                observe: 'mimnyu_prc_sum',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },

        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appskgs.pro));
            this.listenTo(this.collection, 'reset', this.addAllCol);
            this.listenTo(this.collection, 'add', this.addOneNext);
            this.listenTo(this.collection, 'reset remove add', this.recalc);
            this.listenTo(this.model, 'change:pay_kbn', this.setFurikomiYousi);
            this.listenTo(this.model, 'change:houjin_kbn', this.setHoujinSbt);
            this.listenTo(this.model, 'change:rs_print_kbn', this.seSoufuKbn);
            this.listenTo(this.model, 'change:rs_soufu_kbn', this.setRyosyusyoAddr);
            this.render();
        },
        render: function () {
            $('.my-type-date', this.$el).datepicker($.extend({}, $.msiJqlib.datePickerDefault)); // 日付picker
            this.stickit();
            return this;
        },
        addOne: function (m) {
            var v = new MeisaiView({model: m});
            this.$("#dtl-table-id").append(v.render().el);
        },
        addOneNext: function (m) {
            var index = m.get('line_no') - 2;
            // 一行も存在しない場合、空白行一行を追加する
            if (index < 0) {
                this.addOne(m);
            } else {
                var v = new MeisaiView({model: m});
                this.$("#dtl-table-id").find('tbody').eq(index).after(v.render().el);
            }
            this.restRowno();
        },
        addAllCol: function (collection) {
            var $table = this.$("#dtl-table-id");
            $table.find('tbody').remove();
            collection.each(this.addOne, this);
            this.restRowno();
        },
        recalc: function () {
            var totalPrice = 0;
            _.each(appskgs.meisaiList.models, function (m) {
                var seikyu_prc = m.get('seikyu_zei_prc');
                if (m.get('selected') === 1) {
                    if (!$.msiJqlib.isNullEx2(seikyu_prc) && !isNaN(seikyu_prc)) {
                        totalPrice += seikyu_prc * 1;
                    }
                }
            });
            var moto_seikyu_prc = appskgs.appModel.get('seikyu_zei_prc');
            totalPrice += moto_seikyu_prc * 1;
            if (totalPrice !== 0) {
                appskgs.appModel.set('gassan_prc_sum', String(totalPrice));
            } else {
                appskgs.appModel.set('gassan_prc_sum', null);
            }
            appskgs.appView.recalcMsi(); // 明細請求金額合計計算
        },
        recalcMsi: function () {
            var seikyu_prc_sum = 0;
            var nyukin_prc_sum = 0;
            var mimnyu_prc_sum = 0;
            _.each(appskgs.meisaiList.models, function (m) {
                if (m.get('selected') === 1) {
                    var seikyu_prc = m.get('seikyu_zei_prc');
                    var nyukin_prc = m.get('nyukin_prc');
                    var mimnyu_prc = m.get('seikyu_zan');
                    if (!$.msiJqlib.isNullEx2(seikyu_prc) && !isNaN(seikyu_prc)) {
                        seikyu_prc_sum += seikyu_prc * 1;
                    }
                    if (!$.msiJqlib.isNullEx2(nyukin_prc) && !isNaN(nyukin_prc)) {
                        nyukin_prc_sum += nyukin_prc * 1;
                    }
                    if (!$.msiJqlib.isNullEx2(mimnyu_prc) && !isNaN(mimnyu_prc)) {
                        mimnyu_prc_sum += mimnyu_prc * 1;
                    }
                }
            });
            appskgs.appModel.set('seikyu_prc_sum', String(seikyu_prc_sum));
            appskgs.appModel.set('nyukin_prc_sum', String(nyukin_prc_sum));
            appskgs.appModel.set('mimnyu_prc_sum', String(mimnyu_prc_sum));
        },
        setFurikomiYousi: function () {
            // 支払方法が「振込」の場合は、振込用紙「郵送」＆非活性、「現金」「精算済」の場合は、振込用紙「不要」＆非活性
            if (appskgs.appModel.get("pay_kbn") == '1') {
                appskgs.appModel.set("soufu_kbn", '1');
                $("#g_furikomi_yousi").attr("disabled", "disabled");
            } else if (appskgs.appModel.get("pay_kbn") == '2' || appskgs.appModel.get("pay_kbn") == '6') {
                appskgs.appModel.set("soufu_kbn", '2');
                $("#g_furikomi_yousi").attr("disabled", "disabled");
            } else {
                $("#g_furikomi_yousi").removeAttr("disabled");
            }
        },
        setHoujinSbt: function () {
            // 法人区分が「個人」「法人(無)」の場合は、法人種別は未設定＆非活性
            if (appskgs.appModel.get("houjin_kbn") == '1' || appskgs.appModel.get("houjin_kbn") == '4') {
                appskgs.appModel.set("houjin_sbt", null);
                $("#g_houjin_sbt").attr("disabled", "disabled");
            } else {
                $("#g_houjin_sbt").removeAttr("disabled");
            }
        },
        seSoufuKbn: function () {
            // 領収証発行不要の場合は、領収証用紙を自動で不要と領収証用紙、領収証名義、領収証送付先名、領収証送付先住所は非活性
            if (appskgs.appModel.get("rs_print_kbn") == '2') {
                appskgs.appModel.set("rs_soufu_kbn", '2');
                appskgs.appModel.set("ryosyusyo_meigi", null);
                appskgs.appModel.set("ryosyusyo_soufu_nm", null);
                appskgs.appModel.set("ryosyusyo_soufu_yubin_no", null);
                appskgs.appModel.set("ryosyusyo_soufu_addr1", null);
                appskgs.appModel.set("ryosyusyo_soufu_addr2", null);
                $("#g_ryoshu_yousi").attr("disabled", "disabled");
                $("#g_ryoshu_meigi").attr("disabled", "disabled");
                $("#g_ryosyusyo_soufu_nm").attr("disabled", "disabled");
                $("#g_ryoshu_yubin_no").attr("disabled", "disabled");
                $("#g_ryoshu_addr1").attr("disabled", "disabled");
                $("#g_ryoshu_addr2").attr("disabled", "disabled");
            } else {
                appskgs.appModel.set("rs_soufu_kbn", '1');
                $("#g_ryoshu_yousi").removeAttr("disabled");
                $("#g_ryoshu_meigi").removeAttr("disabled");
                $("#g_ryosyusyo_soufu_nm").removeAttr("disabled");
                $("#g_ryoshu_yubin_no").removeAttr("disabled");
                $("#g_ryoshu_addr1").removeAttr("disabled");
                $("#g_ryoshu_addr2").removeAttr("disabled");
                this.setRyosyusyoAddr();
            }
        },
        setRyosyusyoAddr: function () {
            // 領収証用紙不要の場合は、領収証送付先名、領収証送付先住所は非活性
            if (appskgs.appModel.get("rs_soufu_kbn") == '2') {
                appskgs.appModel.set("ryosyusyo_soufu_nm", null);
                appskgs.appModel.set("ryosyusyo_soufu_yubin_no", null);
                appskgs.appModel.set("ryosyusyo_soufu_addr1", null);
                appskgs.appModel.set("ryosyusyo_soufu_addr2", null);
                $("#g_ryosyusyo_soufu_nm").attr("disabled", "disabled");
                $("#g_ryoshu_yubin_no").attr("disabled", "disabled");
                $("#g_ryoshu_addr1").attr("disabled", "disabled");
                $("#g_ryoshu_addr2").attr("disabled", "disabled");
            } else {
                $("#g_ryosyusyo_soufu_nm").removeAttr("disabled");
                $("#g_ryoshu_yubin_no").removeAttr("disabled");
                $("#g_ryoshu_addr1").removeAttr("disabled");
                $("#g_ryoshu_addr2").removeAttr("disabled");
            }
        },
        // 明細No再設定処理
        restRowno: function () {
            var rowNo = 1;
            _.each(appskgs.meisaiList.models, function (m) {
                m.set('line_no', rowNo);
                rowNo++;
            });
        },
        renderBtn: function () {
            if ((appskgs.appModel.get('shori_mode') === SHORI_MODE_NEW) ||
                    (appskgs.appModel.get('bun_gas_kbn_num') === BUN_GAS_SAKI && appskgs.appModel.get('seikyu_approval_status') === SEIKYU_APPROVAL)) { // 新規or合算＆承認
                $('#btn_del').attr("disabled", "disabled");
            } else {
                $('#btn_del').removeAttr("disabled");
            }
        },
        // 振込銀行ピッカー
        bankHelper: function() {
            var bbv = this;

            this.$el.msiPickHelper({
                action: '/mref/brkozadlg',
                width: '94%',
                height: '94%',
                onSelect: function (data) {
//                    console.log( '@@@ brkozaHelper =>', data );
                    if ( !data ) { return; }
                    var dumpStr = JSON.stringify(data, undefined, 2);
                    bbv.model.set( { bank_cd:   data.transfer_bank_cd,
                                     bank_nm: data.koza_info} );
                },
                onClear: function () {
                    bbv.model.set( {bank_cd: null,
                                    bank_nm: null} );
                },
                hookSetData: function () {
                    return {
                        init_search: 1,
                        s_transfer_bank_cd: null, // '05',
                        s_bumon_cd: null, // '00002',
                        s_seko_kbn: null, // (施行区分(振込先口座)(9645): 1:葬儀,3:アフター(法事含む),4:供花供物
                    }
                },
            });

        },
        seikyuDenpyoHelper: function () { // 請求番号(通常伝票対象)
            var bbm = this.model;
            var t = this;
            this.$el.msiPickHelper({
                action: '/saiken/seikyudenpyodlg',
                _myId: '#msi-dialog2',
                data: {
                    'bun_gas_kbns': "'0','2'",
                    's_seikyu_kbn': '4',
                    's_nyukin_kbn': '1',    // 未入金
                    'seikyu_approval_status': '0',    // 未入金
                    'disp_kbn': '3',
                },
                onSelect: function (data) {
//                    console.log(data);
                    bbm.set({
                        s_seikyu_no: data.code, //請求書№
                        s_gseikyu_no: null
                    });
                    t.doSearch();
                },
                onClear: function () {
                    bbm.set({
                        s_seikyu_no: null, //請求書№
                        s_gseikyu_no: null
                    });
                },
                hookSetData: function () {
                    return {
                        init_search: 1
                    }
                },
            });
        },
        gseikyuDenpyoHelper: function () { // 合算先請求番号(合算先伝票対象)
            var bbm = this.model;
            var t = this;
            this.$el.msiPickHelper({
                action: '/saiken/seikyudenpyodlg',
                _myId: '#msi-dialog2',
                data: {
                    'bun_gas_kbns': '20',
                    's_seikyu_kbn': '4',
                    'disp_kbn': '3'
                },
                onSelect: function (data) {
//                    console.log(data);
                    bbm.set({
                        s_gseikyu_no: data.code, //合算先請求書№
                        s_seikyu_no: null
                    });
                    t.doSearch();
                },
                onClear: function () {
                    bbm.set({
                        s_gseikyu_no: null, //合算先請求書№
                        s_seikyu_no: null 
                    });
                },
                hookSetData: function () {
                    return {
                        init_search: 1
                    }
                },
            });
        },
        // 郵便番号 picker
        zipHelper: function () {
            var bbv = this;
            if (utils.isReadOnlyCtxt())
                return; // 参照専用の場合は何もしない
            msiLib2.setPickHelper('zipno', '#g_sekyu_yubin_no'
                    , function (data) {
                        bbv.model.set('soufu_yubin_no', data.code);
                        bbv.model.set('soufu_addr1', data.name);
                    }
            , function () {
                bbv.model.set('soufu_yubin_no', '');
                bbv.model.set('soufu_addr1', '');
            }
            , function () {
                var zipno = $('#g_sekyu_yubin_no').val();
                var init_search = (!!zipno && zipno.length > 2) ? 1 : 0;
                return {
                    s_zipno: zipno,
                    init_search: init_search,
                };
            }
            );
        },
        // 郵便番号 picker
        zipHelper2: function () {
            var bbv = this;
            if (utils.isReadOnlyCtxt())
                return; // 参照専用の場合は何もしない
            msiLib2.setPickHelper('zipno', '#g_ryoshu_yubin_no'
                    , function (data) {
                        bbv.model.set('ryosyusyo_soufu_yubin_no', data.code);
                        bbv.model.set('ryosyusyo_soufu_addr1', data.name);
                    }
            , function () {
                bbv.model.set('ryosyusyo_soufu_yubin_no', '');
                bbv.model.set('ryosyusyo_soufu_addr1', '');
            }
            , function () {
                var zipno = $('#g_ryoshu_yubin_no').val();
                var init_search = (!!zipno && zipno.length > 2) ? 1 : 0;
                return {
                    s_zipno: zipno,
                    init_search: init_search,
                };
            }
            );
        },
        seikyuCopy: function () {
            var sekyu_nm1 = this.model.get('sekyu_nm1');
            var sekyu_nm2 = this.model.get('sekyu_nm2');
            if ($.msiJqlib.isNullEx2(sekyu_nm1)) {
                sekyu_nm1 = '';
            }
            if ($.msiJqlib.isNullEx2(sekyu_nm2)) {
                sekyu_nm2 = '';
            }
            var sekyu_nm = sekyu_nm1 + sekyu_nm2;
            this.model.set('sekyu_soufu_nm', sekyu_nm);
        },
        ryoshuCopy: function () {
            var ryosyusyo_meigi = this.model.get('ryosyusyo_meigi');
            this.model.set('ryosyusyo_soufu_nm', ryosyusyo_meigi);
        },
        // 検索
        doSearch: function () {
            $.msiJqlib.clearAlert();
            var t = this;
            var s_seikyu_den_no = this.model.get("s_seikyu_no");
            var s_gseikyu_den_no = this.model.get("s_gseikyu_no");
            var err_text = '';
            if ($.msiJqlib.isNullEx2(s_seikyu_den_no) && $.msiJqlib.isNullEx2(s_gseikyu_den_no)) {
                $.msiJqlib.showErr('検索条件を入力してください。');
                return;
            }
            if (!$.msiJqlib.isNullEx2(s_seikyu_den_no) && !$.msiJqlib.isNullEx2(s_gseikyu_den_no)) {
                err_text = err_text + '「請求伝票」と「合算先請求伝票」は同時検索できません。一方の条件のみ設定ください。';
            }
            if (!$.msiJqlib.isNullEx2(s_seikyu_den_no) && (s_seikyu_den_no.length !== 10)) {
                err_text = err_text + '請求番号は10ケタです';
            }
            if (!$.msiJqlib.isNullEx2(s_gseikyu_den_no) && (s_gseikyu_den_no.length !== 10)) {
                err_text = err_text + '請求番号は10ケタです';
            }
            if (err_text.length > 0) {
                $.msiJqlib.showErr(err_text);
                return;
            }
            var dataAppJson = JSON.stringify(appskgs.appModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/seikyugassan/msi',
                data: {
                    dataAppJson: dataAppJson
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // 葬儀分割明細設定処理
                        _resetMsiData(mydata);
                        appskgs.appModel.set(mydata.appdata);
                        appskgs.appView.recalc(); // 合算請求金額合計計算
                        appskgs.appView.renderBtn();
                    } else if (mydata.status === 'NG') {
                        _resetMsiData(mydata);
                        t.model.set({
                            shori_mode: SHORI_MODE_NEW, // 処理モード 1:新規　2:修正
                            bunkatsu_kbn: '0', // 分割元区分　0:葬儀　1：別注品
                            s_kaisya: null, // 検索会社
                            s_seko_no: null, // 検索施行番号
                            s_gseikyu_no: null, // 合算先検索請求番号
                            kokyaku_kbn: null, // 合算元顧客区分
                            s_kokyaku_no: null, // 合算元顧客番号
                            s_kokyaku_nm: null, // 合算元顧客名
                            s_seikyusyo_kbn: null, // 合算元請求書区分
                            s_shime_ymd: null, // 合算元締め日
                            s_nonyu_st_ymd: null, // 検索納品日FROM
                            s_nonyu_ed_ymd: null, // 検索納品日TO
                            seikyu_den_no: null, // 請求番号
                            nonyu_dt: null, // 納品日
                            bank_cd: null, // 振込銀行コード
                            bank_nm: null, // 振込銀行名
                            pay_kbn: '1', // 支払方法
                            harai_ymd: null, // 支払予定日
                            seikyu_zei_prc: null, // 請求金額(合算元)
                            sekyu_nm1: null, // 請求先名1
                            sekyu_nm2: null, // 請求先名2
                            sekyu_knm1: null, // 請求先カナ名1
                            sekyu_knm2: null, // 請求先カナ名2
                            tokuisaki_nm: null, // 得意先名
                            nafuda_nms: null, // 名札名義
                            sekyu_soufu_nm: null, // 請求書送付先名
                            sekyu_soufu_nm2: null, // 請求書送付先名2
                            soufu_tel: null, // 請求書送付先tel
                            soufu_yubin_no: null, // 請求書送付先郵便番号
                            soufu_addr1: null, // 請求書送付先住所1
                            soufu_addr2: null, // 請求書送付先住所2
                            syorui_tenpu_kbn: null, // 請求書フォーマット
                            rs_print_kbn: null, // 領収書発行
                            ryosyusyo_meigi: null, // 領収書名義
                            ryosyusyo_soufu_nm: null, // 領収書送付先名
                            ryosyusyo_soufu_yubin_no: null, // 領収書送付先郵便番号
                            ryosyusyo_soufu_addr1: null, // 領収書送付先住所1
                            ryosyusyo_soufu_addr2: null, // 領収書送付先住所2
                            gassan_prc_sum: 0, // 合算請求金額合計
                            seikyu_prc_sum: 0, // 明細請求金額合計
                            nyukin_prc_sum: 0, // 明細入金金額合計
                            mimnyu_prc_sum: 0, // 明細未入金額合計
                            sekyu_dlg_flag: false
                        });
                        appskgs.appView.recalc(); // 合算請求金額合計計算
                        appskgs.appView.renderBtn();
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        // 請求先処理
        seikyucdHelper: function () {
            if ($(".dlg_g_seikyu_cd").hasClass("disable_btn")) {
                return;
            }
            var m = this.model;
            var updateSekyuInfo = function (sd) {
                m.set('sekyu_cd', sd.sekyu_cd);
                m.set('sekyu_nm1', sd.sekyu_nm);
                m.set('sekyu_knm1', sd.sekyu_knm);
                m.set('sekyu_yubin_no', sd.sekyu_yubin_no);
                m.set('sekyu_addr1', sd.sekyu_addr1);
                m.set('sekyu_addr2', sd.sekyu_addr2);
                m.set('sekyu_tel', sd.sekyu_tel);
                m.set('sekyu_fax', sd.sekyu_fax);
                m.set('sekyu_soufu_nm', sd.sekyu_soufu_nm);
                m.set('sekyu_biko1', sd.sekyu_biko1);
                m.set('sekyu_biko2', sd.sekyu_biko2);

                m.set('nafuda_nm1', sd.v_free2);
                m.set('nafuda_nm2', sd.v_free3);
                m.set('nafuda_nm3', sd.v_free4);
                m.set('ryoshusho_atena', sd.sekyu_nm);
                m.set('busho_nm', sd.v_free1);
                m.set('g_sekyu_soufu_nm', sd.sekyu_soufu_nm);
                m.set('g_sekyu_soufu_knm', sd.sekyu_soufu_knm);
            };
            m.set('sekyu_dlg_flag', true);
            var event_select = "update.sekyu";
            Backbone.off(event_select);
            Backbone.on(event_select, updateSekyuInfo);
            this.$el.msiPickHelper({
                action: 'juchu.denpyo.sekyu',
                onSelect: function (data) {
                    Backbone.trigger('pick.sekyu.select', data);
                },
                onClear: function () {
                    appskgs.appModel.set('sekyu_dlg_flag', false);
                },
                hookSetData: function () {
                    var p = {init_search: 0, no_cond: 0};
                    _(msiGlobalObj.bbExports.SekyuModel.myKeys).each(function (v) {
                        var val = m.get(v);
                        if ($.msiJqlib.isNullEx2(val)) {
                            val = null;
                        }
                        p[v] = val;
                    });
                    return p;
                }
            });
        },
        // 横アイコンクリック時ピッカー表示
        setDatePicker: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
        isInputOk: function () {
            //合明細の請求内の請求方法と合算請求先内の請求方法が同じかチェックする　　ADDED by MSI Lakshita on 2019/05/21
            var kaishu_flg = false;
            appskgs.meisaiList.each(function (m, i) {
                var oldKaishu = null;
                var kaishu_kbn = appskgs.appModel.get('kaishu_kbn');
                if ((!$.msiJqlib.isNullEx2(oldKaishu) && m.get('kaishu_kbn') !== oldKaishu) || (kaishu_kbn !== m.get('kaishu_kbn'))) {
                    kaishu_flg = true;
                }
                oldKaishu = m.get('kaishu_kbn');
            });
            if (kaishu_flg) {
                $.msiJqlib.showErr('合算請求先内の請求方法と明細の請求内の請求方法を同じにしてください。');
                return false;
            }
            var aMsg = [];
            var result = appskgs.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // コレクションチェック
            appskgs.meisaiList.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });
            // NG
            if (aMsg.length > 0) {
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            return true;
        },
        doSave: function () {
            $.msiJqlib.clearAlert();
            if ($.msiJqlib.isNullEx2(this.model.get("seikyu_den_no"))) {
                return;
            }
            if (!this.isInputOk()) {
                return;
            }
            if (this.isDataEqual()) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            if (appskgs.appModel.get('shori_mode') === SHORI_MODE_NEW) { // 新規
                if (this.getSelectRows().length === 0) {
                    $.msiJqlib.showErr('合算する行を選択してください');
                    return;
                }
            }
            var dataAppJson = JSON.stringify(appskgs.appModel.toJSON());
            var dataGassanColJson = JSON.stringify(appskgs.meisaiList.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/seikyugassan/save',
                data: {
                    dataAppJson: dataAppJson,
                    dataGassanColJson: dataGassanColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (mydata.result === 'search') {
                            appskgs.appModel.set('s_seikyu_no', null);
                            appskgs.appModel.set('s_gseikyu_no', mydata.saki_seikyu_no);
                            appskgs.appView.doSearch();
                        } else if (mydata.result === 'reset') {
                            appskgs.appModel.reset();
                            appskgs.meisaiList.reset();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doDelete: function () {
            $.msiJqlib.clearAlert();
            var m = this.model;
            if ($.msiJqlib.isNullEx2(this.model.get("seikyu_den_no"))) {
                return;
            }
            if (!confirm('請求合算明細を削除します。よろしいですか')) {
                return;
            }
            var dataAppJson = JSON.stringify(appskgs.appModel.toJSON());
            var dataGassanColJson = JSON.stringify(appskgs.meisaiList.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/seikyugassan/delete',
                data: {
                    dataAppJson: dataAppJson,
                    dataGassanColJson: dataGassanColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appskgs.appModel.set(new AppModel().toJSON()); // 初期化
                        appskgs.meisaiList.reset();
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            location.href = $.msiJqlib.baseUrl() + '/saiken/seikyugassan/index';
//            window.location.reload();
        },
        doSearchKokyaku: function () {
            if ($.msiJqlib.isNullEx2(this.model.get("seikyu_den_no"))) {
                return;
            }
            var dataAppJson = JSON.stringify(appskgs.appModel.toJSON());
            var tm = this.model;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/seikyugassan/searchkokyaku',
                data: {
                    dataAppJson: dataAppJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (mydata.data.kokyaku_kbn == '1') {
                            tm.set('sekyu_nm',mydata.data.tokui_nm);
                            tm.set('tokuisaki_nm',mydata.data.tokui_nm);
                            tm.set('sekyu_nm1',null);
                            tm.set('sekyu_nm2',null);
                            tm.set('sekyu_knm1',null);
                            tm.set('sekyu_knm2',null);
                        } else {
                            tm.set('sekyu_nm',null);
                            tm.set('tokuisaki_nm',null);
                            tm.set('sekyu_nm1',mydata.data.kokyaku_nm1);
                            tm.set('sekyu_nm2',mydata.data.kokyaku_nm2);
                            tm.set('sekyu_knm1',mydata.data.kokyaku_kana1);
                            tm.set('sekyu_knm2',mydata.data.kokyaku_kana2);
                        }
                        tm.set('sekyu_soufu_nm',mydata.data.sekyu_atena1);
                        tm.set('sekyu_soufu_nm2',mydata.data.sekyu_atena2);
                        tm.set('soufu_tel',mydata.data.tel1);
                        tm.set('soufu_yubin_no',mydata.data.sekyu_yubin_no);
                        tm.set('soufu_addr1',mydata.data.sekyu_addr1);
                        tm.set('soufu_addr2',mydata.data.sekyu_addr2);
                        tm.set('s_kokyaku_nm',mydata.data.kokyaku_hyoji_nm);
                        tm.set('s_seikyusyo_kbn',mydata.data.seikyusho_kbn);
                        tm.set('s_shime_ymd',mydata.data.simebi_ymd);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        isDataEqual: function () {
            // イコール
            var appEq = $.msiJqlib.isEqual(appskgs.appModel.toJSON(), orgDataApp);
            var msiEq = $.msiJqlib.isEqual(appskgs.meisaiList.toJSON(), orgDataMeisaiCol);
            return appEq && msiEq;
        },
        getSelectRows: function () {
            var selectRows = [];
            _.each(appskgs.meisaiList.models, function (m) {
                if (m.get('selected') === 1) {
                    selectRows.push(m);
                }
            });
            return selectRows;
        },
    });
    var MeisaiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                kokyaku_no: null, // 顧客番号
                uri_den_no: null, // 請求番号
                msi_no: null, // 明細番号
                moushi_kbn: null, // 申込区分
                juchusaki_kbn_nm: null, // 受注区分
                nonyu_dt: null, // 納品日
                sekyu_nm: null, // 請求先名
                nafuda_nms: null, // 名札名義
                seikyu_zei_prc: null, // 請求金額
                nyukin_prc: null, // 入金金額
                seikyu_zan: null, // 未入金額
                bumon_nm: null, // 部門名
                status_kbn: null, // ステータス区分
                bun_gas_kbn_num: null, // 分割合算区分
                line_no: 1, // 行番号
                selected: 0             // 行選択
            };
        },
        validation: {
        },
        labels: {
        }
    });
    var MeisaiCollection = Backbone.Collection.extend({
        model: MeisaiModel
    });

    var MeisaiView = Backbone.View.extend({
        tagName: "tbody",
        template: _.template($('#item-template').html()),
        events: {
            "click a.destroy": "doDestroy",
            "click a.add": "doAdd",
            "click tr": "clickSelected"
        },
        bindings: {
            '.line_no': 'line_no',
            '.kokyaku_no': 'kokyaku_no',
            '.seikyu_no': 'seikyu_den_no',
            '.moushi_kbn': 'moushi_kbn',
            '.nonyu_dt': 'nonyu_dt',
            '.seikyu_nm': 'sekyu_nm',
            '.nafuda_nm': 'nafuda_nms',
            '.bumon_nm': 'bumon_nm',
            '.seikyu_prc': {
                observe: 'seikyu_zei_prc',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '.nyukin_prc': {
                observe: 'nyukin_prc',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '.minyu_prc': {
                observe: 'seikyu_zan',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            }
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appskgs.pro));
            this.listenTo(this.model, 'change:seikyu_prc', appskgs.appView.recalc);
            this.listenTo(this.model, 'destroy', this.remove);
        },
        render: function () {
            this.$el.html(this.template(this.model.toJSON()));
            this.stickit();
            this.setSelected();
            return this;
        },
        doAdd: function (e) {
            var no = this.model.get('line_no');
            var m = new MeisaiModel({line_no: no + 1});
            appskgs.meisaiList.add(m, {at: no});
        },
        doDestroy: function () {
            $.msiJqlib.clearAlert();
            if (!$.msiJqlib.isNullEx2(this.model.get('msi_no'))) {
                // トランデータの場合は削除コレクションに追加する
                trnDelCol.add(this.model.clone());
            }
            this.model.destroy();
            appskgs.appView.restRowno();
            _addMsiBlankOne();
        },
        setSelected: function (e) {
            var selected = this.model.get('selected');
            if (selected === 1) {
                this.$('tr').addClass('row-selected');
            } else {
                this.$('tr').removeClass('row-selected');
            }
            appskgs.appView.recalc(); // 合算請求金額合計計算
        },
        clickSelected: function (e) {
            var shori_mode = appskgs.appModel.get('shori_mode');
            var selected = this.model.get('selected');
            if (shori_mode === SHORI_MODE_NEW) { // 新規
                if (selected === 1) {
                    this.$('tr').removeClass('row-selected');
                    this.model.set('selected', 0)
                } else {
                    this.$('tr').addClass('row-selected');
                    this.model.set('selected', 1);
                }
            } else {
                return;
            }
            appskgs.appView.recalc(); // 合算請求金額合計計算
        },
    });

    // データ取得
    try {
        var data = JSON.parse(_.unescape($('#my-data-init-id').text()));
    } catch (e) {
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    appskgs.data = data;
    // APP初期化処理
    appskgs.meisaiList = new MeisaiCollection();
    appskgs.appModel = new AppModel();
    appskgs.appView = new AppView({model: appskgs.appModel, collection: appskgs.meisaiList});
    var orgDataApp, orgDataMeisaiCol, trnDelCol;
    var _resetAppData = function (mydata) {
        if (!$.msiJqlib.isNullEx2(mydata.appdata)) {
            appskgs.appModel.set(mydata.appdata);
        }
        if (!$.msiJqlib.isNullEx2(mydata.dataGassan)) {
            appskgs.appModel.set(mydata.dataGassan);
            GASSAN_UPDATE_FLG = mydata.dataGassan.update_flg; //ADDED by MSI Lakshita on 2019/05/21
        }
        orgDataApp = appskgs.appModel.toJSON();
    };
    var _resetMsiData = function (mydata) {
        $(document).msiErrClearAll();
        if (!$.msiJqlib.isNullEx2(mydata)) {
            appskgs.meisaiList.reset(mydata.dataCol);
        } else {
            appskgs.meisaiList.reset();
        }
        _addMsiBlankOne();
        orgDataApp = appskgs.appModel.toJSON();
        orgDataMeisaiCol = appskgs.meisaiList.toJSON();
        trnDelCol = new MeisaiCollection();
    };
    var _addMsiBlankOne = function () {
        if (appskgs.meisaiList.length === 0) {
            //空データの行を一行追加
            var m = new MeisaiModel({line_no: 1});
            appskgs.meisaiList.add(m);
        }
    };

    $.msiJqlib.setSelect2Com1($("#s_kaisya"), ($.extend({data: appskgs.data.kaisya}, {})));
    $.msiJqlib.setSelect2Com1($("#g_harai_method"), ({data: $.msiJqlib.objToArray3(appskgs.data.harai_method)}));
    $.msiJqlib.setSelect2Com1($("#g_houjin_kbn"), ({data: $.msiJqlib.objToArray3(appskgs.data.houjin_kbn)}));
    $.msiJqlib.setSelect2Com1($("#g_houjin_sbt"), ({data: $.msiJqlib.objToArray3(appskgs.data.houjin_sbt)}));
    $.msiJqlib.setSelect2Com1($("#g_furikomi_yousi"), ({data: $.msiJqlib.objToArray3(appskgs.data.furikomi_yousi)}));
    $.msiJqlib.setSelect2Com1($("#g_ryoshu_hakko"), ({data: $.msiJqlib.objToArray3(appskgs.data.ryoshu_hakko)}));
    $.msiJqlib.setSelect2Com1($("#g_ryoshu_yousi"), ({data: $.msiJqlib.objToArray3(appskgs.data.ryoshu_yousi)}));
    $.msiJqlib.setSelect2Com1($("#s_seikyusyo_kbn"), ({data: $.msiJqlib.objToArray3(appskgs.data.seikyusho_kbn)}));
    $.msiJqlib.setSelect2Com1($("#g_syorui_tenpu_kbn"), ({data: $.msiJqlib.objToArray3(appskgs.data.syorui_tenpu_kbn)}));

    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel({
        '#g_seikyu_nm1': ['#g_seikyu_knm1', 'sekyu_knm1', appskgs.appModel],
        '#g_seikyu_nm2': ['#g_seikyu_knm2', 'sekyu_knm2', appskgs.appModel]
    });

    // 郵便番号による住所1自動入力設定
    $.msiJqlib.setAutoZipToAddrModel({
        '#g_sekyu_yubin_no': ['soufu_yubin_no', 'soufu_addr1', appskgs.appModel],
        '#g_ryoshu_yubin_no': ['ryosyusyo_soufu_yubin_no', 'ryosyusyo_soufu_addr1', appskgs.appModel]
    });

    // ログインユーザーの所属会社を初期表示
    if (!$.msiJqlib.isNullEx2(data.s_kaisya)) {
        appskgs.appModel.set('s_kaisya', data.s_kaisya);
    }
    // 遷移先の場合で合算先請求伝票番号の指定があれば検索結果を表示させる
    if (!$.msiJqlib.isNullEx2(data.shitei_saki_seikyu_den_no)) {
        appskgs.appModel.set('s_gseikyu_no', data.shitei_saki_seikyu_den_no);
        $('#btn_search').click();
    }
    // 遷移先の場合で合算元請求伝票番号の指定があれば検索結果を表示させる
    if (!$.msiJqlib.isNullEx2(data.shitei_moto_seikyu_den_no)) {
        appskgs.appModel.set('s_seikyu_no', data.shitei_moto_seikyu_den_no);
        $('#btn_search').click();
    }

    $("#main").show();
});


