<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\MyBusinessLodging;

class GetGoogleUpdatedLodgingResponse extends \Google\Model
{
  /**
   * @var string
   */
  public $diffMask;
  protected $lodgingType = Lodging::class;
  protected $lodgingDataType = '';

  /**
   * @param string
   */
  public function setDiffMask($diffMask)
  {
    $this->diffMask = $diffMask;
  }
  /**
   * @return string
   */
  public function getDiffMask()
  {
    return $this->diffMask;
  }
  /**
   * @param Lodging
   */
  public function setLodging(Lodging $lodging)
  {
    $this->lodging = $lodging;
  }
  /**
   * @return Lodging
   */
  public function getLodging()
  {
    return $this->lodging;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GetGoogleUpdatedLodgingResponse::class, 'Google_Service_MyBusinessLodging_GetGoogleUpdatedLodgingResponse');
