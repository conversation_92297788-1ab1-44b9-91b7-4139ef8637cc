<?php

/**
 * Logic_SisetsuYoyaku
 *
 * 施設予約
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Sai
 * @since      2020/04/xx
 * @filesource
 */

/**
 * 施設予約
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Sai
 * @since      2020/04/xx
 */
class Logic_SisetsuYoyaku {

    public static $riyo_kbn_reian = '1'; // 霊安室
    public static $riyo_kbn_horei = '2'; // 保冷庫
    private static $bunrui_hansou = '0'; // 予約分類 搬送
    private static $bunrui_sougi = '1'; // 予約分類 葬儀
    private static $bunrui_houji = '2'; // 予約分類 法事
    private static $tsuya_r_kbn = array('3', '5'); // 利用区分（通夜関係）
    private static $sougi_r_kbn = array('6'); // 利用区分（告別式）
    private static $houji_r_kbn = array('9'); // 利用区分（法事施行場所）
    private static $nitei_kbn_sougi = '11'; // 日程区分(葬儀)
    private static $nitei_kbn_hoyo = '1'; // 法事日程区分(法要)
    /**
     * 
     * key：利用区分　1:霊安室 2:保冷庫 3:通夜施行場所 4:通夜・葬儀控室 5:通夜会食場所 6:告別式施行場所 7:告別式会食場所 
     *                  8:法要(戻り)施行場所 9:法事施行場所 10:法事控室 11:法事会食場所 12:イベント 13:使用不可(補修等、施設利用不可)  
     * value：日程区分(葬儀)  1:死亡日時 3:納棺 4:通夜 5:出棺 6:火葬 8:法要(戻り) 9:葬儀後会食 10:納骨 11:葬儀・告別式 12:創想の儀 13:散会 14:控室 15:湯灌 16:通夜後会食
     *      　日程区分(法事)  0:施行日 1:ご法要 2:ご墓参 3:ご法宴 4:控室 5:納骨
     */
    private static $r_n_pair = array(
        '3' => array('4'),
        '4' => array('14'),
        '5' => array('16'),
        '6' => array('11', '5'),
        '7' => array('9'),
        '8' => array('8'),
        '9' => array('1'),
        '10' => array('4'),
        '11' => array('3'),
    );

    /**
     * 施設予約保存処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $dataYoyaku 予約データ
     * @return array $data 更新結果
     */
    public static function saveYoyaku($db, $dataYoyaku) {
        try {
            static::checkSekoData($db, $dataYoyaku);
            // 通夜なしチェック処理
            $riyo_kbn = Msi_Sys_Utils::emptyToNull($dataYoyaku['riyo_kbn']);
            if (in_array($riyo_kbn, static::$tsuya_r_kbn)) {
                $sekoTsuyaNashiData = DataMapper_SekoNiteiEx::find($db, array('seko_no' => $dataYoyaku['yoyaku_seko_no'], 'nitei_kbn' => '4', 'v_free1' => '1')); // 日程区分が通夜、通夜なし区分があり
                if (Msi_Sys_Utils::myCount($sekoTsuyaNashiData) > 0) {
                    throw new Msi_Sys_Exception_InputException("通夜なしのため、予約できません。");
                }
            }
            // 当社式場未使用チェック
            $mishiyoata = static::getShikijoMishiyoData($db, $dataYoyaku);
            if (Msi_Sys_Utils::myCount($mishiyoata) > 0) {
                throw new Msi_Sys_Exception_InputException("該当する利用区分は当社式場未使用になっているため、予約できません。");
            }
            // 重複予約を避けるため明示的に施設予約テーブルロック
            $db->easyExecute('LOCK TABLE shisetsu_yoyaku IN SHARE UPDATE EXCLUSIVE MODE ');
            // 他の予約と重複チェック
            $doubleData = static::getDoubleYoyaku($db, $dataYoyaku);
            if (Msi_Sys_Utils::myCount($doubleData) > 0) {
                throw new Msi_Sys_Exception_InputException("該施設の他の予約と重複しています。");
            }
            // 利用区分と施行番号で該当予約存在チェック
            $sekoYoyakuData = static::getSekoYoyaku($db, $dataYoyaku);
            if (Msi_Sys_Utils::myCount($sekoYoyakuData) > 0) {
                throw new Msi_Sys_Exception_InputException("該当する利用区分の施行は既に予約されています。");
            }
            $cnt = static::saveData($db, $dataYoyaku);
            $db->commit();
        } catch (Msi_Sys_Exception_DbException $e) {
            $userMsg = 'DB整合性エラー';
            $dbErrCode = $e->getNativeDbErrorCode();
            if ($dbErrCode == '55P03') { // 55P03: lock_not_available
                $userMsg = '予約テーブルロック取得ができませんでした。';
            }
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            return $errData;
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            return $errData;
        }

        $data = array(
            'msg' => ('更新しました'),
            'status' => 'OK',
        );
        return $data;
    }

    /**
     * 重複予約データ取得（他の予約と重複チェック用）
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return array $select
     */
    private static function getDoubleYoyaku($db, $data) {

        // 予約開始日
        $yoyaku_s_ymd = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($data['yoyaku_s_ymd']), 'DATE2');
        if ($yoyaku_s_ymd === false) {
            throw new Msi_Sys_Exception_InputException("予約開始日が不正です。");
        }
        // 予約終了日
        $yoyaku_e_ymd = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($data['yoyaku_e_ymd']), 'DATE2');
        if ($yoyaku_e_ymd === false) {
            throw new Msi_Sys_Exception_InputException("予約終了日が不正です。");
        }

        // 予約開始時間
        $yoyaku_s_time = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($data['yoyaku_s_time']), '/^(\d{2}):(\d{2})$/');
        if ($yoyaku_s_time === false) {
            throw new Msi_Sys_Exception_InputException("予約開始時間が不正です。");
        }

        // 予約終了時間
        $yoyaku_e_time = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($data['yoyaku_e_time']), '/^(\d{2}):(\d{2})$/');
        if ($yoyaku_e_time === false) {
            throw new Msi_Sys_Exception_InputException("予約終了時間が不正です。");
        }
        // 部門コード
//        $bumon_cd = $data['bumon_cd'];
//        if (!$db->isRefDataExists('bumon_mst', array('bumon_cd' => $bumon_cd))) {
//            throw new Msi_Sys_Exception_InputException("部門 ($bumon_cd) のデータが存在しません");
//        }
        // 会場コード
        $kaijyo_cd = $data['kaijyo_cd'];
        if (!$db->isRefDataExists('kaijyo_mst', array('kaijyo_cd' => $kaijyo_cd))) {
            throw new Msi_Sys_Exception_InputException("会場 ($kaijyo_cd) のデータが存在しません。");
        }
        $yoyaku_s_ts = Msi_Sys_Utils::normYYYYMMDD($yoyaku_s_ymd) . $yoyaku_s_time;
        $yoyaku_e_ts = Msi_Sys_Utils::normYYYYMMDD($yoyaku_e_ymd) . $yoyaku_e_time;
        if ($yoyaku_e_ts <= $yoyaku_s_ts) {
            throw new Msi_Sys_Exception_InputException("予約開始と終了時間が不正です。");
        }
//        $cond['kaijyo_cd'] = $kaijyo_cd; //
        $cond['__x0'] = array('x', '((yoyaku_s_ymdhm < :x0_1 AND :x0_1 < yoyaku_e_ymdhm) 
                                        OR (yoyaku_s_ymdhm < :x0_2 AND :x0_2 < yoyaku_e_ymdhm)
                                         OR (yoyaku_s_ymdhm >= :x0_1 AND :x0_2 >= yoyaku_e_ymdhm))'
            , array('x0_1' => $yoyaku_s_ts, 'x0_2' => $yoyaku_e_ts));
        if (isset($data['yoyaku_id'])) {
            $cond['__x1'] = array('x', "(yoyaku_id <> :x1_1)", array('x1_1' => $data['yoyaku_id']));
        }
        $kaijyo_cds = static::getKaijyos($db, $data['kaijyo_cd']);
        $cond['kaijyo_cd'] = DataMapper_Utils::condOneOf('kaijyo_cd', $kaijyo_cds, 'kaijyo_cd');
        $select = DataMapper_SisetsuYoyaku::find2($db, $cond);
        return $select;
    }

    /**
     * 重複予約データ取得（利用区分と施行番号で該当予約存在チェック）
     *
     * <AUTHOR> Sai
     * @since  2020/05/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return array $select
     */
    private static function getSekoYoyaku($db, $data) {
        $sekoYoyaku = array();
        // 利用区分
        $riyo_kbn = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($data['riyo_kbn']), 'DIGIT');
        if ($riyo_kbn === false) {
            throw new Msi_Sys_Exception_InputException("利用区分が不正です。");
        }
        $yoyaku_seko_no = Msi_Sys_Utils::easyGetVar($data, 'yoyaku_seko_no');
        $yoyaku_seko_no_sub = Msi_Sys_Utils::easyGetVar($data, 'yoyaku_seko_no_sub');
        if (strlen($yoyaku_seko_no) > 0) {
            $cond['yoyaku_seko_no'] = $yoyaku_seko_no;
            $cond['riyo_kbn'] = $riyo_kbn;
            if (isset($data['yoyaku_id'])) {
                $cond['__x1'] = array('x', "(yoyaku_id <> :x1_1)", array('x1_1' => $data['yoyaku_id']));
            }
            if (strlen($yoyaku_seko_no_sub) > 0) {
                $cond['yoyaku_seko_no_sub'] = $yoyaku_seko_no_sub;
            }
            $sekoYoyaku = DataMapper_SisetsuYoyaku::find2($db, $cond);
        }
        return $sekoYoyaku;
    }

    /**
     * 施行データチェック（ステータスチェック）
     *
     * <AUTHOR> Sai
     * @since  2020/09/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return array $select
     */
    private static function checkSekoData($db, $data) {
        // ステータス区分によっては処理不可
        if (isset($data['yoyaku_seko_no'])) {
//            $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
//            if ($riyo_kbn == static::$riyo_kbn_reian || $riyo_kbn == static::$riyo_kbn_horei) { // 霊安室と保冷庫はチェックしない
//                return;
//            }
            $sekoKihon = DataMapper_SekoKihonInfo::findOne($db, array('seko_no' => $data['yoyaku_seko_no']));
            if (Msi_Sys_Utils::myCount($sekoKihon) > 0 && $sekoKihon['status_kbn'] > 2) { // 1:見積未確定 2:見積確定済 3:施行金額確定 4:請求確定 5:入金済
                throw new Msi_Sys_Exception_InputException("請求金額確定済のため、処理できません。");
            }
        }
    }

    /**
     * 当社式場未使用データ取得
     *
     * <AUTHOR> Sai
     * @since  2020/09/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return array $select
     */
    private static function getShikijoMishiyoData($db, $data) {
        $mishiyoData = array();
        if (isset($data['yoyaku_seko_no'])) {
            $nitei_kbns = array();
            $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($data['yoyaku_bunrui']);
            $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
            if ($yoyaku_bunrui == static::$bunrui_sougi) {
                $nitei_kbns = static::getNiteiKbnSougi($riyo_kbn);
                if (Msi_Sys_Utils::myCount($nitei_kbns) > 0) {
                    $cond['seko_no'] = $data['yoyaku_seko_no'];
                    $cond['nitei_kbn'] = DataMapper_Utils::condOneOf('nitei_kbn', implode($nitei_kbns, ","), 'nt2_');
                    $cond['v_free2'] = '1';
                    $mishiyoData = DataMapper_SekoNiteiEx::find($db, $cond);
                }
            } else if ($yoyaku_bunrui == static::$bunrui_houji) {
                $nitei_kbns = static::getNiteiKbnHouji($riyo_kbn);
                if (Msi_Sys_Utils::myCount($nitei_kbns) > 0) {
                    $cond['seko_no'] = $data['yoyaku_seko_no'];
                    $cond['nitei_kbn'] = DataMapper_Utils::condOneOf('nitei_kbn', implode($nitei_kbns, ","), 'nt2_');
                    $cond['v_free2'] = '1';
                    $mishiyoData = DataMapper_SekoNiteiEx::findHouji($db, $cond);
                }
            }
        }
        return $mishiyoData;
    }

    /**
     * 保存処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function saveData($db, $data) {
        $cnt = 0;

        $cnt += static::saveSisetsu($db, $data);
        $cnt += static::saveOther($db, $data);
        $cnt += static::saveSekoKihon($db, $data);
        $cnt += static::makeJuchudata($db, $data['yoyaku_seko_no']);
        return $cnt;
    }

    /**
     * 施設保存処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return string $cnt
     */
    private static function saveSisetsu($db, &$data) {
        // 施設データ
        $select = array();

        $kijyunYmd = Msi_Sys_Utils::getDate();
        $kijyunYmd2 = Msi_Sys_Utils::getDatetimeStd();

        // 更新対象項目設定
        $dataSisetsu = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_id kaijyo_cd kaijyo_kbn riyo_kbn yoyaku_kbn yoyaku_bunrui
                                                             yoyaku_seko_no yoyaku_seko_no_sub cre_tanto_cd cre_tanto_nm memo');
        // emptyToNull
        $dataSisetsu['kaijyo_kbn'] = Msi_Sys_Utils::emptyToNull($dataSisetsu['kaijyo_kbn']);
        $dataSisetsu['yoyaku_bunrui'] = Msi_Sys_Utils::emptyToNull($dataSisetsu['yoyaku_bunrui']);
        $dataSisetsu['yoyaku_seko_no_sub'] = Msi_Sys_Utils::emptyToNull($dataSisetsu['yoyaku_seko_no_sub']);
        $dataSisetsu['yoyaku_start_ts'] = $data['yoyaku_s_ymd'] . ' ' . $data['yoyaku_s_time'];
        $dataSisetsu['yoyaku_end_ts'] = $data['yoyaku_e_ymd'] . ' ' . $data['yoyaku_e_time'];
        $dataSisetsu['mod_tanto_ts'] = $kijyunYmd2;
        $dataSisetsu['mod_tanto_cd'] = App_Utils::getTantoCd();
        $dataSisetsu['mod_tanto_nm'] = App_Utils::getTantoNm();

        if (isset($dataSisetsu['yoyaku_id'])) {
            $select = DataMapper_SisetsuYoyaku::find2($db, array('yoyaku_id' => $dataSisetsu['yoyaku_id']));
        }
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $dataSisetsu['yoyaku_id'] = App_ClsGetCodeNo::GetCodeNo($db, 'shisetsu_yoyaku', 'yoyaku_id', $kijyunYmd);
            $dataSisetsu['cre_tanto_ts'] = $kijyunYmd2;
            $dataSisetsu['cre_tanto_cd'] = App_Utils::getTantoCd();
            $dataSisetsu['cre_tanto_nm'] = App_Utils::getTantoNm();
            $data['yoyaku_id'] = $dataSisetsu['yoyaku_id'];
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('shisetsu_yoyaku', $dataSisetsu);
        } else {
            // 条件部
            $where['yoyaku_id'] = $dataSisetsu['yoyaku_id'];  // 予約ID
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('shisetsu_yoyaku', $dataSisetsu, $where, array('yoyaku_id'));
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * その他保存処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function saveOther($db, $data) {
        $cnt = null;
        $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($data['yoyaku_bunrui']);
        $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
        if ($yoyaku_bunrui == static::$bunrui_hansou) {
//            cnt = 0;
//            $param = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_seko_no yoyaku_seko_no_sub kaijyo_cd kaijyo_lnm');
//            $sql = "
//                UPDATE
//                    syutsudo_irai_msi
//                SET 
//                    iso_saki_cd = '1'
//                    ,iso_saki_kbn = 1
//                    ,iso_saki_basho_cd = :kaijyo_cd
//                    ,iso_saki_nm = :kaijyo_lnm
//                WHERE
//                    uketsuke_no = :yoyaku_seko_no
//                AND uketuke_no_sub = :yoyaku_seko_no_sub
//                AND delete_flg = 0
//                ";
//            $cnt = $db->easyExecute($sql, $param);
        } else {
            if ($yoyaku_bunrui == static::$bunrui_sougi) {
                $cnt = 0;
                $param = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_id yoyaku_seko_no kaijyo_cd kaijyo_lnm yoyaku_s_ymd');
                $param['yoyaku_e_ymd'] = null;
                $nitei_kbns = static::getNiteiKbnSougi($riyo_kbn);
                foreach ($nitei_kbns as $nitei_kbn) {
                    $param['nitei_kbn'] = $nitei_kbn;
                    // 存在する場合は時間を取得し設定する
                    $sqlSelset = "
                    SELECT
                        TO_CHAR(nitei_ymd,'HH24:MI') AS nitei_time
                        ,TO_CHAR(nitei_ed_ymd,'HH24:MI') AS nitei_ed_time
                    FROM
                        seko_nitei
                    WHERE
                        seko_no = :seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0
                    ";
                    $select = $db->easySelOne($sqlSelset, array('seko_no' => $param['yoyaku_seko_no'], 'nitei_kbn' => $param['nitei_kbn']));
                    if (Msi_Sys_Utils::myCount($select) > 0) {
                        if (isset($select['nitei_time'])) {
                            $param['yoyaku_s_ymd'] = $data['yoyaku_s_ymd'] . ' ' . $select['nitei_time'];
                        }
                        if (isset($select['nitei_ed_time'])) {
                            $param['yoyaku_e_ymd'] = $data['yoyaku_s_ymd'] . ' ' . $select['nitei_ed_time'];
                        }
                    }
                    $sql = "
                    UPDATE
                        seko_nitei
                    SET 
                        nitei_ymd = :yoyaku_s_ymd
                        ,nitei_ed_ymd = :yoyaku_e_ymd
                        ,spot_code_kbn = '0670'
                        ,spot_cd = '02'
                        ,basho_kbn = 2
                        ,basho_cd = :kaijyo_cd
                        ,basho_nm = :kaijyo_lnm
                        ,v_free3 = :yoyaku_id
                    WHERE
                        seko_no = :yoyaku_seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0";
                    $cnt += $db->easyExecute($sql, $param);
                }
                if ($cnt == 0) {
                    throw new Msi_Sys_Exception_InputException("該葬儀施行の日程が存在しないため、予約できません。");
                }
            } else if ($yoyaku_bunrui == static::$bunrui_houji) {
                $cnt = 0;
                $param = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_id yoyaku_seko_no kaijyo_cd kaijyo_lnm yoyaku_s_ymd');
                $nitei_kbns = static::getNiteiKbnHouji($riyo_kbn);
                foreach ($nitei_kbns as $nitei_kbn) {
                    $param['nitei_kbn'] = $nitei_kbn;
                    // 存在する場合は時間を取得し設定する
                    $sqlSelset = "
                    SELECT
                        TO_CHAR(nitei_ymd,'HH24:MI') AS nitei_time
                    FROM
                        seko_nitei_houji
                    WHERE
                        seko_no = :seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0
                    ";
                    $select = $db->easySelOne($sqlSelset, array('seko_no' => $param['yoyaku_seko_no'], 'nitei_kbn' => $param['nitei_kbn']));
                    if (Msi_Sys_Utils::myCount($select) > 0) {
                        if (isset($select['nitei_time'])) {
                            $param['yoyaku_s_ymd'] = $data['yoyaku_s_ymd'] . ' ' . $select['nitei_time'];
                        }
                    }
                    $sql = "
                    UPDATE
                        seko_nitei_houji
                    SET 
                        nitei_ymd = :yoyaku_s_ymd
                        ,spot_code_kbn = '0960'
                        ,spot_cd = '02'
                        ,basho_kbn = 2
                        ,basho_cd = :kaijyo_cd 
                        ,basho_nm = :kaijyo_lnm
                        ,v_free3 = :yoyaku_id
                    WHERE
                        seko_no = :yoyaku_seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0";
                    $cnt += $db->easyExecute($sql, $param);
                }
                if ($cnt == 0) {
                    throw new Msi_Sys_Exception_InputException("該法事施行の日程が存在しないため、予約できません。");
                }
            }
        }
        return $cnt;
    }

    /**
     * 施行基本情報保存処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/07/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function saveSekoKihon($db, $data) {
        $cnt = null;
        $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
        $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($data['yoyaku_bunrui']);
        if ($yoyaku_bunrui == static::$bunrui_sougi) {
            $nitei_kbns = static::getNiteiKbnSougi($riyo_kbn);
            $target_nitei = static::$nitei_kbn_sougi;
        } else if ($yoyaku_bunrui == static::$bunrui_houji) {
            $nitei_kbns = static::getNiteiKbnHouji($riyo_kbn);
            $target_nitei = static::$nitei_kbn_hoyo;
        } else {
            $nitei_kbns = array(); // 施行と紐づかない（その他施設予約の場合）
        }
        $sekoData = DataMapper_SekoKihon::find($db, array('seko_no' => $data['yoyaku_seko_no']));
        $param = array();
        $param['seko_no'] = $data['yoyaku_seko_no'];
        foreach ($nitei_kbns as $nitei_kbn) {
            if ($nitei_kbn == $target_nitei) {
                $kaijyoData = DataMapper_Kaijyo::find($db, array('kaijyo_cd' => $data['kaijyo_cd']));
                if (count($kaijyoData) == 0) {
                    throw new Msi_Sys_Exception_InputException("該当の会場が存在しません。");
                }
                // 式場の部門を見積式場に設定する
                if (isset($kaijyoData[0]['bumon_cd'])) {
                    $param['est_shikijo_cd'] = $kaijyoData[0]['bumon_cd'];
                    // 式場の親部門と請負部門の親部門が同一ならば見積式場と施行式場は同一
                    $est_oya = DataMapper_BumonEx::find($db, array('bumon_cd' => $kaijyoData[0]['bumon_cd']));
                    $seko_oya = DataMapper_BumonEx::find($db, array('bumon_cd' => $sekoData[0]['bumon_cd']));
                    if ($est_oya[0]['oya_bumon_cd'] == $seko_oya[0]['oya_bumon_cd']) {
                        $param['seko_shikijo_cd'] = $kaijyoData[0]['bumon_cd'];
                    } else {
                        // 請負部門の親部門配下の共通部門を施行式場に設定する
                        $comBumonCd = App_Utils2::getCommonBumonCd($sekoData[0]['bumon_cd']);
                        $param['seko_shikijo_cd'] = $comBumonCd;
                    }
                    $param['sougi_ymd'] = $data['yoyaku_s_ymd'];
                    $sql = "
                    UPDATE
                        seko_kihon_info
                    SET 
                        est_shikijo_cd = :est_shikijo_cd
                        ,seko_shikijo_cd = :seko_shikijo_cd
                        ,sougi_ymd = :sougi_ymd
                    WHERE 
                        seko_no = :seko_no
                        AND delete_flg = 0";
                    $cnt += $db->easyExecute($sql, $param);
                }
            }
        }
        return $cnt;
    }

    /**
     * 施設予約保存処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $dataYoyaku 予約データ
     * @return array $data 更新結果
     */
    public static function deleteYoyaku($db, $dataYoyaku) {
        try {
            static::checkSekoData($db, $dataYoyaku);
            // 利用プラン選択時は告別式の予約キャンセルは不可チェック処理
            $riyo_kbn = Msi_Sys_Utils::emptyToNull($dataYoyaku['riyo_kbn']);
            $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($dataYoyaku['yoyaku_bunrui']);
            if (($yoyaku_bunrui == static::$bunrui_sougi && in_array($riyo_kbn, static::$sougi_r_kbn)) || ($yoyaku_bunrui == static::$bunrui_houji && in_array($riyo_kbn, static::$houji_r_kbn))) {
                $sekoKihonData = DataMapper_SekoKihon::find2($dataYoyaku['yoyaku_seko_no']);
                if (Msi_Sys_Utils::myCount($sekoKihonData) > 0 && strlen($sekoKihonData['seko_plan_cd']) > 0) {
                    throw new Msi_Sys_Exception_InputException("該施行は利用プランが設定されているため、キャンセルできません。");
                }
            }

            $cnt = static::deleteSisetsu($db, $dataYoyaku);
            $cnt += static::deleteOther($db, $dataYoyaku);
            $cnt += static::deleteSekokihon($db, $dataYoyaku);
            $cnt += static::makeJuchudata($db, $dataYoyaku['yoyaku_seko_no']);
            $db->commit();
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            return $errData;
        }

        $data = array(
            'msg' => ('削除しました'),
            'status' => 'OK',
        );
        return $data;
    }

    /**
     * 施設削除処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function deleteSisetsu($db, $data) {

        // 更新対象項目設定
        $dataSisetsu = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_id');

        if (isset($dataSisetsu['yoyaku_id'])) {
            $dataSisetsu['delete_flg'] = 1;  // 削除フラグ
            // 条件部
            $where['yoyaku_id'] = $dataSisetsu['yoyaku_id'];  // 予約ID
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('shisetsu_yoyaku', $dataSisetsu, $where, array('yoyaku_id'));
            $cnt = $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * その他削除処理
     *
     * <AUTHOR> Sai
     * @since  2020/04/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function deleteOther($db, $data) {

        $cnt = 0;
        $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($data['yoyaku_bunrui']);
        $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
        if ($yoyaku_bunrui == static::$bunrui_hansou) {
            
        } else {
            if ($yoyaku_bunrui == static::$bunrui_sougi) {
                $param = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_seko_no');
                $nitei_kbns = static::getNiteiKbnSougi($riyo_kbn);
                foreach ($nitei_kbns as $nitei_kbn) {
                    $param['nitei_kbn'] = $nitei_kbn;
                    $sql = "
                    UPDATE
                        seko_nitei
                    SET 
                        nitei_ymd = null
                        ,nitei_ed_ymd = null
                        ,spot_code_kbn = null
                        ,spot_cd = null
                        ,basho_kbn = null
                        ,basho_cd = null 
                        ,basho_nm = null
                        ,v_free3 = null
                    WHERE
                        seko_no = :yoyaku_seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0";
                    $cnt += $db->easyExecute($sql, $param);
                }
            } else if ($yoyaku_bunrui == static::$bunrui_houji) {
                $param = Msi_Sys_Utils::remapArrayFlat($data, 'yoyaku_seko_no');
                $nitei_kbns = static::getNiteiKbnHouji($riyo_kbn);
                foreach ($nitei_kbns as $nitei_kbn) {
                    $param['nitei_kbn'] = $nitei_kbn;
                    $sql = "
                    UPDATE
                        seko_nitei_houji
                    SET 
                        nitei_ymd = null
                        ,spot_code_kbn = null
                        ,spot_cd = null
                        ,basho_kbn = null
                        ,basho_cd = null 
                        ,basho_nm = null
                        ,v_free3 = null
                    WHERE
                        seko_no = :yoyaku_seko_no
                    AND nitei_kbn = :nitei_kbn
                    AND delete_flg = 0";
                    $cnt += $db->easyExecute($sql, $param);
                }
            }
        }
        return $cnt;
    }

    /**
     * 施行基本情報論理削除処理
     * キャンセル時は見積式場をNULLにする
     *
     * <AUTHOR> Tosaka
     * @since  2020/07/xx
     * @param  Msi_Sys_Db $db
     * @param  string $data 予約データ
     * @return int $cnt
     */
    private static function deleteSekoKihon($db, $data) {
        $cnt = null;
        $riyo_kbn = Msi_Sys_Utils::emptyToNull($data['riyo_kbn']);
        $yoyaku_bunrui = Msi_Sys_Utils::emptyToNull($data['yoyaku_bunrui']);
        $sekoData = DataMapper_SekoKihon::find($db, array('seko_no' => $data['yoyaku_seko_no']));
        $param = array();
        $nitei_kbns = array();
        $param['seko_no'] = $data['yoyaku_seko_no'];
        if ($yoyaku_bunrui == static::$bunrui_sougi) {
            $nitei_kbns = static::getNiteiKbnSougi($riyo_kbn);
            $target_nitei = static::$nitei_kbn_sougi;
        } else if ($yoyaku_bunrui == static::$bunrui_houji) {
            $nitei_kbns = static::getNiteiKbnHouji($riyo_kbn);
            $target_nitei = static::$nitei_kbn_hoyo;
        }
        foreach ($nitei_kbns as $nitei_kbn) {
            if ($nitei_kbn == $target_nitei) {
                // 見積式場をNULLに設定する
                $sql = "
                    UPDATE
                        seko_kihon_info
                    SET 
                        est_shikijo_cd = NULL
                    WHERE 
                        seko_no = :seko_no
                        AND delete_flg = 0";
                $cnt += $db->easyExecute($sql, $param);
            }
        }
        return $cnt;
    }

    /**
     * 霊安室と保冷庫の終了時間更新処理
     *
     * <AUTHOR> Sai
     * @since  2020/05/xx
     * @param  Msi_Sys_Db $db
     * @param  string $seko_no 施行番号
     * @return int $cnt
     */
    public static function updateAnchiYoyaku($db, $seko_no) {
        $cnt = 0;
        $sekoYoyaku = array();
        $cond['yoyaku_seko_no'] = $seko_no;
        $cond['riyo_kbn'] = static::$riyo_kbn_reian;
        $sekoYoyaku1 = DataMapper_SisetsuYoyaku::find2($db, $cond);
        if (Msi_Sys_Utils::myCount($sekoYoyaku1) > 0) {
            $sekoYoyaku = array_merge($sekoYoyaku, $sekoYoyaku1);
        }
        $cond2['yoyaku_seko_no'] = $seko_no;
        $cond2['riyo_kbn'] = static::$riyo_kbn_horei;
        $sekoYoyaku2 = DataMapper_SisetsuYoyaku::find2($db, $cond2);
        if (Msi_Sys_Utils::myCount($sekoYoyaku2) > 0) {
            $sekoYoyaku = array_merge($sekoYoyaku, $sekoYoyaku2);
        }
        if (Msi_Sys_Utils::myCount($sekoYoyaku) > 0) {
            $condNitei['seko_no'] = $seko_no;
            $condNitei['nitei_kbn'] = DataMapper_Utils::condOneOf('nitei_kbn', '4,11', 'nitei_kbn_');
            $condNitei['__etc_orderby'] = 'nitei_ymd';
            $sekoNitei = DataMapper_SekoNiteiEx::find($db, $condNitei);
            if (Msi_Sys_Utils::myCount($sekoNitei) > 0) {
                $nitei_ymd = $sekoNitei[0]['nitei_ymd'];
                foreach ($sekoYoyaku as $value) {
                    $end_ymdhm = $value['end_ymdhm'];
                    if (strlen($nitei_ymd) > 0 && $end_ymdhm > $nitei_ymd) {
                        $dataSisetsu['yoyaku_end_ts'] = $nitei_ymd;  // 予約終了時間
                        // 条件部
                        $where['yoyaku_id'] = $value['yoyaku_id'];  // 予約ID
                        // 更新SQL
                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('shisetsu_yoyaku', $dataSisetsu, $where, array('yoyaku_id'));
                        $cnt = $db->easyExecute($sql, $param);
                    }
                }
            }
        }
        return $cnt;
    }

    /**
     * 利用区分に該当する日程区分を取得する(葬儀)
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     * @param  string $riyo_kbn 利用区分
     * @return int $nitei_kbn
     */
    private static function getNiteiKbnSougi($riyo_kbn) {
        $nite_kbns = array();
        if (key_exists($riyo_kbn, static::$r_n_pair)) {
            $nite_kbns = static::$r_n_pair[$riyo_kbn];
        }
        return $nite_kbns;
    }

    /**
     * 利用区分に該当する日程区分を取得する(法事)
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     * @param  string $riyo_kbn 利用区分
     * @return int $nitei_kbn
     */
    private static function getNiteiKbnHouji($riyo_kbn) {
        return static::getNiteiKbnSougi($riyo_kbn);
    }

    /**
     * 会場コード親、子会場コードを返す（自身を含める）
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     * @param  Msi_Sys_Db $db 
     * @return string $kaijyo_cd
     */
    private static function getKaijyos($db, $kaijyo_cd) {
        $kaijyos[] = $kaijyo_cd;
        $kaijyo_tree = DataMapper_SisetsuYoyaku::find($db);
        $kaijyo_des = static::getKaijyoDes($kaijyo_tree, $kaijyo_cd); // 子会場取得
        if (Msi_Sys_Utils::myCount($kaijyo_des) > 0) {
            $kaijyos = array_merge($kaijyos, $kaijyo_des);
        }
        $kaijyo_par = static::getKaijyoPar($kaijyo_tree, $kaijyo_cd); // 親会場取得
        if (Msi_Sys_Utils::myCount($kaijyo_par) > 0) {
            $kaijyos = array_merge($kaijyos, $kaijyo_par);
        }
        return $kaijyos;
    }

    /**
     * 子部門コード取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @param array $kaijyo_tree 会場データ
     * @param string $kaijyo_cd 会場コード
     * @return array
     */
    public static function getKaijyoDes($kaijyo_tree, $kaijyo_cd) {
        $ret = array();
        foreach ($kaijyo_tree as $kaijyo) {
            if ($kaijyo['kaijyo_cd'] <> $kaijyo_cd && preg_match('/' . $kaijyo_cd . '/', $kaijyo['path'])) {
                $ret[] = $kaijyo['kaijyo_cd'];
            }
        }

//        Msi_Sys_Utils::debug($kaijyo_cd.'* KaijyoDes=>' . Msi_Sys_Utils::dump($ret));
        return $ret;
    }

    /**
     * 親部門コード取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @param array $kaijyo_tree 会場データ
     * @param string $kaijyo_cd 会場コード
     * @return array
     */
    public static function getKaijyoPar($kaijyo_tree, $kaijyo_cd) {
        $path_str = '';
        $ret = array();
        if (Msi_Sys_Utils::myCount($kaijyo_tree) > 0) {
            foreach ($kaijyo_tree as $kaijyo) {
                if ($kaijyo['kaijyo_cd'] == $kaijyo_cd) {
                    $path_str = $kaijyo['path_str'];
                    break;
                }
            }
            $path = explode(":", $path_str);
            foreach ($path as $value) {
                if ($value <> $kaijyo_cd) {
                    $ret[] = $value;
                }
            }
        }
//        Msi_Sys_Utils::debug($kaijyo_cd.'* KaijyoPar=>' . Msi_Sys_Utils::dump($ret));
        return $ret;
    }

    /**
     * 式場利用費と霊柩搬送作成処理
     *
     * <AUTHOR> Sai
     * @since  2020/09/xx
     * @param  Msi_Sys_Db $db
     * @param  string $seko_no 施行番号
     * @return int $cnt
     */
    private static function makeJuchudata($db, $seko_no) {
        $cnt = 0;
        if (strlen($seko_no) > 0) {
            $juchuShikijyo = new Juchu_JuchuShikijyo();
            $cnt = $juchuShikijyo->saveShikijyoData($db, $seko_no);
            $juchuReikyusya = new Juchu_JuchuReikyusya();
            $cnt += $juchuReikyusya->saveReikyuData($db, $seko_no);
        }
        return $cnt;
    }

}
