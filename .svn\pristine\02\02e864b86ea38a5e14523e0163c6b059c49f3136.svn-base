<?php

/**
 * Seikyu_SeikyuShonin
 *
 * 請求承認クラス
 * @category   App
 * @package    controllers\Seikyu\SeikyuShonin
 * <AUTHOR> Sai
 * @since      2014/04/15
 * @version 2019/04/30 mihara 軽減税率対応
 * @filesource 
 */

/**
 * 請求承認クラス
 *
 * @category   App
 * @package    controllers\Seikyu\SeikyuShonin
 * <AUTHOR>
 * @since      2014/04/15
 */
class Saiken_SeikyuShonin extends Juchu_JuchuEstimate {

    /**
     *
     * 葬送儀礼～その他の明細データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/16
     * @return array 明細データ
     */
    protected function getDetailData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            dm.msi_no -- 明細番号
            ,dm.dai_bunrui_cd -- 大分類コード
            ,dm.chu_bunrui_cd -- 中分類コード
            ,cbm.chu_bunrui_nm -- 中分類名
            ,dm.shohin_kbn -- 商品区分
            ,dm.shohin_kbn_nm-- 商品区分名
            ,dm.shohin_cd -- 商品コード
            ,dm.shohin_nm -- 商品名
            ,dm.shohin_tkiyo_nm -- 商品摘要名
            ,dm.mokuteki_kbn -- 目的区分
            ,dm.juchu_suryo -- 数量
            ,dm.uri_tnk AS juchu_tnk-- 単価
            ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
            ,dm.gojokai_nebiki_prc -- 付帯値引き
            ,dm.nebiki_prc -- 値引き
            ,dm.juchu_suryo * uri_tnk + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
            ,dm.hoshi_umu_kbn -- 奉仕料有無区分
            ,dm.hoshi_prc -- 奉仕料
            ,sm.tnk_chg_kbn-- 売上単価変更区分
            ,sm.nm_input_kbn-- 名称入力区分
            ,sbm.hachu_kbn -- 発注書区分
            ,sm.tani_cd-- 単位コード
            ,sm.uri_kamoku_cd-- 科目コード
            ,dm.zei_kbn -- 消費税区分
            ,dm.gen_tnk-- 原価単価
            ,CASE  -- タブ区分 1:葬送儀礼, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ 
                WHEN dm.dai_bunrui_cd = '0010' THEN '1'
                WHEN dm.dai_bunrui_cd = '0020' THEN '2'
                WHEN dm.dai_bunrui_cd = '0030' THEN '3'
                WHEN dm.dai_bunrui_cd = '0040' THEN '3'
                WHEN dm.dai_bunrui_cd = '0060' THEN '4'
                WHEN dm.dai_bunrui_cd = '0050' THEN '5'
                WHEN dm.dai_bunrui_cd = '0070' THEN '5'
            END tab_kbn
            ,'0'	AS	nebiki_kbn	-- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
            ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
            ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                WHEN '0020' THEN '0'
                ELSE '1'
            END add_down_kbn
            ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                WHEN '0020' THEN '0'
                ELSE '1'
            END del_kbn
            ,dm.add_kbn -- 追加区分 0:トランデータ
            ,sbm.mitumori_print_seq
            ,dm.out_zei_prc
            ,dm.in_zei_prc
            ,dm.zei_cd  -- 2019/04/30 mihara keigen
            ,dm.reduced_tax_rate --  2019/04/30 mihara keigen
            ,dm.shohin_bumon_cd
            ,dm.upgrade_kbn
            ,dm.tani_nm
        FROM
            (
                SELECT
                    m1.msi_no
                    ,m1.denpyo_kbn
                    ,m1.disp_no
                    ,m1.add_kbn
                    ,m1.dai_bunrui_cd
                    ,m1.chu_bunrui_cd
                    ,m1.shohin_kbn
                    ,COALESCE(skm.shohin_kbn_nm,'')	AS	shohin_kbn_nm	--2018/06/20	Okuyama
                    ,m1.shohin_cd
                    ,m1.shohin_nm
                    ,m1.shohin_tkiyo_nm
                    ,m1.juchu_suryo
                    ,m1.uri_tnk
                    ,m1.hoshi_umu_kbn
                    ,m1.hoshi_prc
                    ,m1.gojokai_nebiki_prc
                    ,m1.nebiki_prc
                    ,m1.zei_kbn        
                    ,m1.gen_tnk
                    ,m1.mokuteki_kbn
                    ,m1.out_zei_prc
                    ,m1.in_zei_prc
                    ,m1.zei_cd  -- 2019/04/30 mihara keigen
                    ,m1.reduced_tax_rate -- 2019/04/30 mihara keigen
                    ,m1.shohin_bumon_cd
                    ,m1.upgrade_kbn
                    ,tani.tani_nm
                FROM
                    uriage_denpyo_msi m1
                LEFT JOIN shohin_kbn_mst skm
                    ON  (m1.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0)
                LEFT JOIN tani_mst tani
                    ON m1.tani_cd = tani.tani_cd
                    AND 0 = tani.delete_flg
                WHERE
                    m1.delete_flg = 0
                AND m1.seko_no = :seko_no
                AND m1.seko_no_sub = :seko_no_sub
            ) dm
            LEFT JOIN shohin_chu_bunrui_mst cbm
                ON dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                AND cbm.delete_flg = 0
            LEFT JOIN shohin_mst sm
                ON dm.shohin_cd = sm.shohin_cd
                AND sm.hihyoji_kbn = 0
                AND sm.bumon_cd = dm.shohin_bumon_cd
                AND sm.delete_flg = 0
            LEFT JOIN shohin_bunrui_mst sbm
                ON dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                AND dm.shohin_kbn  = sbm.shohin_kbn
                AND dm.shohin_cd  = sbm.shohin_cd
                AND dm.shohin_bumon_cd  = sbm.bumon_cd
                AND sbm.delete_flg = 0
            ORDER BY
                dm.dai_bunrui_cd
                ,CASE WHEN dm.upgrade_kbn IN (1,2) THEN 1 ELSE 2 END
                ,sbm.mitumori_print_seq
                ,dm.disp_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
        ));
        return $select;
    }
    
    /**
     *
     * 別注品（喪家分で未入金かつ自社請求のもの）のデータを取得
     *
     * <AUTHOR> Tosaka
     * @since 2018/4/20
     * @return array 明細データ
     */
    protected function getBechuData($uri_den_no) {
        
        $db = Msi_Sys_DbManager::getMyDb();
        // 伝票の状態を調べる
        $cond['uri_den_no'] = $uri_den_no;
        $select = DataMapper_UriageDenpyo::findDenpyo2($db, $cond);
        if (count($select) === 0) {
            return array();
        }
        $uri_data = $select[0];
        // 施行金額確定前の場合は供花供物のデータを取得し
        // 確定後の場合は請求伝票の明細から直接取得する
        if ($uri_data['seko_prc_kakute_kbn'] === '0') {
            $sql = "
            SELECT
                dm.msi_no -- 明細番号
                ,dm.dai_bunrui_cd -- 大分類コード
                ,dm.chu_bunrui_cd -- 中分類コード
                ,cbm.chu_bunrui_nm -- 中分類名
                ,dm.shohin_kbn -- 商品区分
                ,dm.shohin_kbn_nm-- 商品区分名
                ,dm.shohin_cd -- 商品コード
                ,dm.shohin_nm -- 商品名
                ,dm.shohin_tkiyo_nm -- 商品摘要名
                ,dm.mokuteki_kbn -- 目的区分
                ,dm.juchu_suryo -- 数量
                ,dm.uri_tnk AS juchu_tnk-- 単価
                ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
                ,dm.gojokai_nebiki_prc -- 付帯値引き
                ,dm.nebiki_prc -- 値引き
                ,dm.juchu_suryo * uri_tnk + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
                ,dm.hoshi_umu_kbn -- 奉仕料有無区分
                ,dm.hoshi_prc -- 奉仕料
                ,sm.tnk_chg_kbn-- 売上単価変更区分
                ,sm.nm_input_kbn-- 名称入力区分
                ,sbm.hachu_kbn -- 発注書区分
                ,sm.tani_cd-- 単位コード
                ,sm.uri_kamoku_cd-- 科目コード
                ,dm.zei_kbn -- 消費税区分
                ,dm.gen_tnk-- 原価単価
                ,'7' AS tab_kbn
                ,'0'	AS	nebiki_kbn	-- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
                ,'0' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
                ,'0' AS add_down_kbn -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                ,'0' AS del_kbn -- 行削除可能区分 0:削除不可, 1:削除可
                ,dm.add_kbn -- 追加区分 0:トランデータ
                ,sbm.mitumori_print_seq
                ,dm.out_zei_prc
                ,dm.in_zei_prc
                ,dm.zei_cd  -- 2019/04/30 mihara keigen
                ,dm.reduced_tax_rate --  2019/04/30 mihara keigen
                ,dm.shohin_bumon_cd
                ,dm.upgrade_kbn
            FROM
                (
                    SELECT
                        m1.msi_no
                        ,m1.denpyo_kbn
                        ,m1.disp_no
                        ,m1.add_kbn
                        ,m1.dai_bunrui_cd
                        ,m1.chu_bunrui_cd
                        ,m1.shohin_kbn
                        ,COALESCE(skm.shohin_kbn_nm,'')	AS	shohin_kbn_nm	--2018/06/20	Okuyama
                        ,m1.shohin_cd
                        ,m1.shohin_nm
                        ,m1.shohin_tkiyo_nm
                        ,m1.juchu_suryo
                        ,m1.uri_tnk
                        ,m1.hoshi_umu_kbn
                        ,m1.hoshi_prc
                        ,m1.gojokai_nebiki_prc
                        ,m1.nebiki_prc
                        ,m1.zei_kbn        
                        ,m1.gen_tnk
                        ,m1.mokuteki_kbn
                        ,m1.out_zei_prc
                        ,m1.in_zei_prc
                        ,m1.zei_cd  -- 2019/04/30 mihara keigen
                        ,m1.reduced_tax_rate -- 2019/04/30 mihara keigen
                        ,m1.shohin_bumon_cd
                        ,m1.upgrade_kbn
                    FROM uriage_denpyo ud
                    INNER JOIN uriage_denpyo_msi m1
                        ON m1.uri_den_no = ud.uri_den_no
                        AND m1.delete_flg = 0
                    LEFT JOIN shohin_kbn_mst skm
                        ON  (m1.shohin_kbn = skm.shohin_kbn
                        AND skm.delete_flg = 0)
                    WHERE ud.delete_flg = 0
                        AND ud.data_kbn = 4 
                        AND ud.juchusaki_kbn = 1
                        AND ud.seko_no = :seko_no
                    ORDER BY ud.uri_den_no
                ) dm
                LEFT JOIN shohin_chu_bunrui_mst cbm
                    ON dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                    AND cbm.delete_flg = 0
                LEFT JOIN shohin_mst sm
                    ON dm.shohin_cd = sm.shohin_cd
                    AND sm.hihyoji_kbn = 0
                    AND sm.bumon_cd = dm.shohin_bumon_cd
                    AND sm.delete_flg = 0
                LEFT JOIN shohin_bunrui_mst sbm
                    ON dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                    AND dm.shohin_kbn  = sbm.shohin_kbn
                    AND dm.shohin_cd  = sbm.shohin_cd
                    AND dm.shohin_bumon_cd  = sbm.bumon_cd
                    AND sbm.delete_flg = 0
                    ";
            $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        } else if ($uri_data['seko_prc_kakute_kbn'] === '1') { 
            $sql = "
            SELECT
                dm.msi_no -- 明細番号
                ,dm.dai_bunrui_cd -- 大分類コード
                ,dm.chu_bunrui_cd -- 中分類コード
                ,cbm.chu_bunrui_nm -- 中分類名
                ,dm.shohin_kbn -- 商品区分
                ,dm.shohin_kbn_nm-- 商品区分名
                ,dm.shohin_cd -- 商品コード
                ,dm.shohin_nm -- 商品名
                ,dm.shohin_tkiyo_nm -- 商品摘要名
                ,dm.mokuteki_kbn -- 目的区分
                ,dm.juchu_suryo -- 数量
                ,dm.uri_tnk AS juchu_tnk-- 単価
                ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
                ,dm.gojokai_nebiki_prc -- 付帯値引き
                ,dm.nebiki_prc -- 値引き
                ,dm.juchu_suryo * uri_tnk + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
                ,dm.hoshi_umu_kbn -- 奉仕料有無区分
                ,dm.hoshi_prc -- 奉仕料
                ,sm.tnk_chg_kbn-- 売上単価変更区分
                ,sm.nm_input_kbn-- 名称入力区分
                ,sbm.hachu_kbn -- 発注書区分
                ,sm.tani_cd-- 単位コード
                ,sm.uri_kamoku_cd-- 科目コード
                ,dm.zei_kbn -- 消費税区分
                ,dm.gen_tnk-- 原価単価
                ,'7' AS tab_kbn
                ,'0'	AS	nebiki_kbn	-- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
                ,'0' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
                ,'0' AS add_down_kbn -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                ,'0' AS del_kbn -- 行削除可能区分 0:削除不可, 1:削除可
                ,dm.add_kbn -- 追加区分 0:トランデータ
                ,sbm.mitumori_print_seq
                ,dm.out_zei_prc
                ,dm.in_zei_prc
                ,dm.zei_cd  -- 2019/04/30 mihara keigen
                ,dm.reduced_tax_rate --  2019/04/30 mihara keigen
                ,dm.shohin_bumon_cd
                ,dm.upgrade_kbn
            FROM
                (
                    SELECT
                        m1.msi_no
                        ,m1.denpyo_kbn
                        ,m1.disp_no
                        ,m1.add_kbn
                        ,m1.dai_bunrui_cd
                        ,m1.chu_bunrui_cd
                        ,m1.shohin_kbn
                        ,COALESCE(skm.shohin_kbn_nm,'')	AS	shohin_kbn_nm	--2018/06/20	Okuyama
                        ,m1.shohin_cd
                        ,m1.shohin_nm
                        ,m1.shohin_tkiyo_nm
                        ,m1.juchu_suryo
                        ,m1.uri_tnk
                        ,m1.hoshi_umu_kbn
                        ,m1.hoshi_prc
                        ,m1.gojokai_nebiki_prc
                        ,m1.nebiki_prc
                        ,m1.zei_kbn        
                        ,m1.gen_tnk
                        ,m1.mokuteki_kbn
                        ,m1.out_zei_prc
                        ,m1.in_zei_prc
                        ,m1.zei_cd  -- 2019/04/30 mihara keigen
                        ,m1.reduced_tax_rate -- 2019/04/30 mihara keigen
                        ,m1.shohin_bumon_cd
                        ,m1.upgrade_kbn
                    FROM seikyu_denpyo_msi m1
                    LEFT JOIN shohin_kbn_mst skm
                        ON  (m1.shohin_kbn = skm.shohin_kbn
                        AND skm.delete_flg = 0)
                    WHERE m1.delete_flg = 0
                        AND m1.seikyu_den_no = :seikyu_den_no
                        AND m1.dai_bunrui_cd = '0080'
                    ORDER BY m1.msi_no
                ) dm
                LEFT JOIN shohin_chu_bunrui_mst cbm
                    ON dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                    AND cbm.delete_flg = 0
                LEFT JOIN shohin_mst sm
                    ON dm.shohin_cd = sm.shohin_cd
                    AND sm.hihyoji_kbn = 0
                    AND sm.bumon_cd = dm.shohin_bumon_cd
                    AND sm.delete_flg = 0
                LEFT JOIN shohin_bunrui_mst sbm
                    ON dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                    AND dm.shohin_kbn  = sbm.shohin_kbn
                    AND dm.shohin_cd  = sbm.shohin_cd
                    AND dm.shohin_bumon_cd  = sbm.bumon_cd
                    AND sbm.delete_flg = 0
                    ";
            $select = $db->easySelect($sql, array('seikyu_den_no' => $this->getSeikyudenpyoNo()));
        }
        return $select;
    }

    /**
     * 承認実行処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/16
     */
    public function shoninexe() {
        $db = Msi_Sys_DbManager::getMyDb();
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        $inkan_img = null;
        $shonin_dt = null;

        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();

        // 事務→所属長の順番に承認する
        $status_kbn = 0;    // 2015/03/02 ADD Kayo
        if (!isset($dateUriShonin['inkan_img1'])) {
            $inkan_img = 'inkan_img1';
            $shonin_dt = 'shonin_dt1';
        } else if (!isset($dateUriShonin['inkan_img2'])) {
            $inkan_img = 'inkan_img2';
            $shonin_dt = 'shonin_dt2';
            $status_kbn = 1;    // 2015/03/02 ADD Kayo
        } else {
            // 承認済み
            $data['status'] = 'NG';
            $data['msg'] = '承認済みです。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $uri_den_no = $this->getUriagedenpyoNo();
        $sql = "
        UPDATE
            uriage_denpyo
        SET 
            {$inkan_img} = :inkan_img
            ,{$shonin_dt} = :shonin_dt
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array(
            'uri_den_no' => $uri_den_no
            , 'inkan_img' => $db->copyBlob(App_Utils::getTantoInkanOid())
            , 'shonin_dt' => Msi_Sys_Utils::getDatetimeStd()
        ));
        // 施行基本情報のステータスを変更 2015/03/02 ADD Kayo
        if ($status_kbn == 1) {
            $db->easyExecute(<<< END_OF_SQL
UPDATE	seko_kihon_info
SET		status_kbn	= 3     -- 3：請求済み
WHERE   delete_flg  = 0
AND		seko_no		= :seko_no
AND    not status_kbn IN (4,9)   -- 2016/03/02 ADD Kayo                   
END_OF_SQL
                    , array('seko_no' => $dataApp['seko_no']));
        }

        $db->commit();
        $data = $this->getData();

        // 画面データを設定する
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '承認しました';
        Msi_Sys_Utils::outJson($data);
        return $cnt;
    }

    /**
     *
     * 請求情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/18
     * @return array 請求情報
     */
    protected function getSeikyuData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $data = DataMapper_SekyuSakiInfo::findJuchusekyu($db, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
        $dataSeikyu = array();
        if (count($data) > 0) {
            $dataSeikyu['sekyu_cd'] = $data[0]['sekyu_cd'];
            $sekyu_nm = null;
            if (isset($data[0]['sekyu_nm1']) && isset($data[0]['sekyu_nm2'])) {
                $sekyu_nm = $data[0]['sekyu_nm1'].' '.$data[0]['sekyu_nm2'];
            } else if (isset($data[0]['sekyu_nm1'])) {
                $sekyu_nm = $data[0]['sekyu_nm1'];
            } else if (isset($data[0]['sekyu_nm2'])) {
                $sekyu_nm = $data[0]['sekyu_nm2'];
            }
            $dataSeikyu['sekyu_nm'] = $sekyu_nm;
            $sekyu_knm = null;
            if (isset($data[0]['sekyu_knm1']) && isset($data[0]['sekyu_knm2'])) {
                $sekyu_knm = $data[0]['sekyu_knm1'].' '.$data[0]['sekyu_knm2'];
            } else if (isset($data[0]['sekyu_knm1'])) {
                $sekyu_knm = $data[0]['sekyu_knm1'];
            } else if (isset($data[0]['sekyu_knm2'])) {
                $sekyu_knm = $data[0]['sekyu_knm2'];
            }
            $dataSeikyu['sekyu_knm'] = $sekyu_knm;
            $dataSeikyu['yubin_no'] = $data[0]['sekyu_yubin_no'];
            $dataSeikyu['addr1'] = $data[0]['sekyu_addr1'];
            $dataSeikyu['addr2'] = $data[0]['sekyu_addr2'];
            $dataSeikyu['tel'] = $data[0]['sekyu_tel'];
            $dataSeikyu['mobile_tel'] = $data[0]['sekyu_mobile_tel'];
            $dataSeikyu['sekyu_soufu_nm'] = $data[0]['sekyu_soufu_nm'];
            $dataSeikyu['soufu_yubin_no'] = $data[0]['soufu_yubin_no'];
            $dataSeikyu['soufu_addr1'] = $data[0]['soufu_addr1'];
            $dataSeikyu['soufu_addr2'] = $data[0]['soufu_addr2'];
            $dataSeikyu['soufu_tel'] = $data[0]['soufu_tel'];
        }
        return $dataSeikyu;
    }

    /**
     * 承認取消処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/16
     */
    public function shonintorikesi() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 施行番号を設定する
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();

        // 所属長→事務の順番に承認取消する
        if (isset($dateUriShonin['inkan_img2'])) {
            $inkan_img = 'inkan_img2';
            $shonin_dt = 'shonin_dt2';
        } else if (isset($dateUriShonin['inkan_img1'])) {
            $inkan_img = 'inkan_img1';
            $shonin_dt = 'shonin_dt1';
        } else {
            // 承認済み
            $data['status'] = 'NG';
            $data['msg'] = '承認取消済みです。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $uri_den_no = $this->getUriagedenpyoNo();
        $sql = "
        UPDATE
            uriage_denpyo
        SET 
            {$inkan_img} = NULL
            ,{$shonin_dt} = NULL
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array(
            'uri_den_no' => $uri_den_no
        ));
        // 施行基本情報のステータスを変更 2015/03/02 ADD Kayo
        $db->easyExecute(<<< END_OF_SQL
UPDATE	seko_kihon_info
SET		status_kbn	= 2     -- 2：施工中
WHERE   delete_flg  = 0
AND		seko_no		= :seko_no
AND    not status_kbn IN (4,9)   -- 2016/03/02 ADD Kayo                   
END_OF_SQL
                , array('seko_no' => $dataApp['seko_no']));

        $db->commit();
        $data = $this->getData();

        // 画面データを設定する
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '承認を取り消しました';
        Msi_Sys_Utils::outJson($data);
        return $cnt;
    }

}
