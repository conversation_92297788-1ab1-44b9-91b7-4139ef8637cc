{include file="fdn_head_std.tpl"}
{include file="fdn_header_32.tpl"}

{include file="header_info.tpl"}

<form id="my-form-id" method="post" class="{$ctxt_readonly}">
  <div id="main">
	<div id="order" style="display:none">

      <div class="my-clearfix"><div class="page-title"><span>当社葬家受注入力</span></div></div>

      <!-- ヘッダ (伝票データ1) -->
	  <!-- fieldset class="type_1">
		<label for="collect_type" class="lbl_collect_type my-require">支払方法</label>
        <input type="hidden" id="kaishu_kbn" name="kaishu_kbn" value="{$kaishu_kbn}"
                       class="msi-picker kaishu_kbn-cls" data-picker-kind="cdNm"
                       data-picker-param="kind2:'0430',placeholder:''" />
	  </fieldset -->

      <fieldset class="type_1">
       <label for="juchusaki_kbn" class="lbl_juchusaki_kbn my-require">受注先区分</label>
        <input type="hidden" id="juchusaki_kbn" name="juchusaki_kbn" value="{$juchusaki_kbn}"
                       class="msi-picker juchusaki_kbn-cls" data-picker-kind="cdNm"
                       data-picker-param="kind2:'1140',placeholder:''" />
        <label for="pay_method_cd" class="lbl_pay_method_cd my-require">支払方法</label>
        <input type="hidden" id="pay_method_cd" name="pay_method_cd" value="{$pay_method_cd}"
                       class="msi-picker pay_method_cd-cls" data-picker-kind="cdNm"
                       data-picker-param="kind2:'1130',placeholder:''" />
        <label for="kaishu_kbn" class="lbl_kaishu_kbn my-require">回収区分</label>
        <input type="hidden" id="kaishu_kbn" name="kaishu_kbn" value="{$kaishu_kbn}"
                       class="msi-picker kaishu_kbn-cls" data-picker-kind="cdNm"
                       data-picker-param="kind2:'0430',placeholder:''" />
        <label for="keijo_ymd" class="lbl_keijo_ymd my-require">売上計上日</label>
        <input name="keijo_ymd" id="keijo_ymd" type="text" class="txt my-type-date" value="" style="ime-mode: disabled"/>
        <div class="label dlg_date_keijo my-type-date-trg dlg_date" data-ref-rel="#keijo_ymd"></div>
        <label for="kaishu_ymd" class="lbl_kaishu_ymd my-require">回収予定日</label>
        <input name="kaishu_ymd" id="kaishu_ymd" type="text" class="txt my-type-date" value="" style="ime-mode: disabled"/>
        <div class="label dlg_date_kaishu my-type-date-trg dlg_date" data-ref-rel="#kaishu_ymd"></div>
        <label for="kaiin_kbn_nm" class="lbl_kaiin_kbn_nm my-require">会員区分</label>
 		<input name="kaiin_kbn_nm" id="kaiin_kbn_nm" type="text" class="txt" value="" readonly/>
 		<input name="kaiin_sbt_nm" id="kaiin_sbt_nm" type="text" class="txt" value="" readonly/>
      </fieldset>
	  <fieldset class="base_1">
		<!-- label for="denpyo_no" class="lbl_denpyo_no">伝票No.</label>
		<input name="denpyo_no" id="denpyo_no" type="text" class="txt" value="" placeholder="(新規)" readonly />
		<div class="label dlg_denpyo_no denpyo_no-ref cursor-pointer" ></div -->
		<label for="sekyu_nm" class="lbl_sekyu_nm require">請求先</label>
		<input name="sekyu_nm" id="sekyu_nm" type="text" class="txt" value="" placeholder="(新規)" style="ime-mode:active"/>
		<div class="label dlg_bill sekyu_cd-ref" data-ref-rel="#sekyu_nm" ></div>
		<label for="sekyu_soufu_nm" class="lbl_sekyu_soufu_nm" title="請求書送付先名">送付先名</label>
		<input name="sekyu_soufu_nm" id="sekyu_soufu_nm" type="text" class="txt" value="" placeholder="(新規)"  style="ime-mode:active"/>
		<label for="sekyu_tel" class="lbl_sekyu_tel my-require">電話</label>
		<input name="sekyu_tel" id="sekyu_tel" type="text" class="txt" value="" style="ime-mode: disabled"/>
		<!-- div class="label dlg_date my-type-date-trg" data-ref-rel="#sekyu_tel"></div -->
		<label for="tax_kbn" class="lbl_tax my-require">税</label>
        <input type="hidden" id="tax_kbn" name="tax_kbn" value="{$tax_kbn}"
                       class="msi-picker tax_kbn-cls" data-picker-kind="cdNm"
                       data-picker-param="kind2:'0150',placeholder:''" />
        <input type="hidden" id="zei_cd" name="zei_cd" value="{$zei_cd}"
                       class="msi-picker zei_cd-cls" data-picker-kind="zei"
                       data-picker-param="placeholder:''"/>
	  </fieldset>
	  <fieldset class="base_2">
		<!-- label for="juchu_ymd" class="lbl_juchu_ymd my-require">受注日</label>
		<input name="juchu_ymd" id="juchu_ymd" type="text" class="txt my-type-date" value="" />
		<div class="label dlg_date my-type-date-trg" data-ref-rel="#juchu_ymd"></div -->
		<label for="sekyu_addr" class="lbl_sekyu_addr">送付先住所</label>
		<input name="sekyu_yubin_no" id="sekyu_yubin_no" type="text" class="txt" value="" placeholder="(〒)" style="ime-mode: disabled"/>
        <label for="sekyu_yubin_no" class="lbl_zip cursor-pointer zip_no-ref">〒</label>
		<input name="sekyu_addr1" id="sekyu_addr1" type="text" class="txt" value="" placeholder="(住所１)" style="ime-mode:active"/>
		<input name="sekyu_addr2" id="sekyu_addr2" type="text" class="txt" value="" placeholder="(住所２)" style="ime-mode:active"/>
		<!-- div class="label dlg_deliv sekyu_nm2-ref"></div -->
		<!-- label for="kaishu_ymd" class="lbl_kaishu_ymd my-require">回収予定日</label>
		<input name="kaishu_ymd" id="kaishu_ymd" type="text" class="txt my-type-date" value="" />
		<div class="label dlg_date my-type-date-trg" data-my-type-date-id="kaishu_ymd" data-ref-rel="#kaishu_ymd"></div -->
		<label for="tanto_cd" class="lbl_staff require">担当者</label>
		<input name="tanto_cd" id="tanto_cd" type="hidden" class="txt staff" value="" />
		<input name="tanto_nm" id="tanto_nm" type="text" class="txt staff" value="" readonly />
		<div class="label dlg_staff tanto-ref" data-ref-rel="#tanto_cd"></div>

	  </fieldset>

      <!-- 明細行 (内訳データ) -->
	  <div class="items" style="overflow:hidden">
		<div class="header" style="overflow-y:scroll;overflow-x: hidden;">
		  <table>
			<tr>
			  <td class="row" rowspan="2">#</td>
			  <td class="control" rowspan="2"><i class="glyphicon glyphicon-cog"></i></td>
			  <!-- td class="type" rowspan="2">伝区</td -->
			  <td class="juchu_bi" rowspan="2">受注日</td>
			  <td class="item_id" rowspan="2">商品コード</td>
			  <td class="item">商品名称</td>
			  <td class="price" rowspan="2">単価</td>
			  <td class="quantity" rowspan="2">数量</td>
			  <td class="nebiki" rowspan="1">値引額</td>
			  <td class="juchu_gaku" rowspan="1">金額</td>
			  <td class="memo2" rowspan="2">名札</td>
			</tr>
			<tr>
			  <td class="tekiyo">商品摘要</td>
			  <td class="soko" colspan="2">倉庫</td>
			  <!-- td class="unit">単位</td -->
			  <!-- td class="memo1">明細備考</td -->
			</tr>
		  </table>
		</div><!-- /.header -->
		<div class="list"  style="overflow-y:scroll;height:80%;padding-bottom:1px;">
		  <table id="dtl-table-id"></table>
        </div><!-- /.list -->
	  </div><!-- /.items -->

      <!-- フッタ (伝票データ2) -->
	  <fieldset class="note_1">
		<label for="denpyo_biko1" class="lbl_note my-require">備考</label>
		<input name="denpyo_biko1" id="denpyo_biko1" type="text" class="txt" value="" maxlength="30" style="ime-mode:active"/>
		<div class="label total_title">受注合計</div>
		<div class="label total_price"><span class="my-bold total_kingaku" id="total_kingaku"></span></div>
		<!-- div class="label total_price"><span class="my-bold" id="total_genka"></span></div -->
        <!--
        <a href="#" class="btn btn-default my-readonly-hidden" id="add-meisai"
           style="float:right"><i class="glyphicon glyphicon-plus"></i>行追加</a>
        -->
	  </fieldset>
	  <fieldset class="note_2">
		<label for="denpyo_biko2" class="lbl_note my-require">入金備考</label>
		<input name="denpyo_biko2" id="denpyo_biko2" type="text" class="txt" value="" maxlength="30" style="ime-mode:active"/>
		<div class="label total_title">消費税等</div>
		<div class="label total_price"><span class="my-bold total_zei" id="total_zei"></span></div>
	  </fieldset>
	  <fieldset class="note_x">
		<label for="note_id_x" class="lbl_note my-require"></label>
		<input name="note_txt_x" id="note_txt_x" type="text" class="txt" value=""/>
		<div class="label total_title">税込合計</div>
		<div class="label total_price"><span class="my-bold total_zeikomi" id="total_zeikomi"></span></div>
	  </fieldset>
	  <fieldset class="upd_info">
		<div class="label cre_user"><span class="my-bold cre_user" id="cre_user"></span></div>
		<div class="label mod_user"><span class="my-bold mod_user" id="mod_user"></span></div>
		<div class="label mod_user"><span class="my-bold seikyu_user" id="seikyu_user"></span></div>
	  </fieldset>

      <!-- 処理ボタン -->
	  <div class="buttons">
		<input type="button" name="btn_go_list" id="btn_go_list" class="my-no-readonly" value="一覧へ" />
		<input type="button" name="btn_save" id="btn_save" value="保存" />
		<!-- input type="button" name="btn_copy" id="btn_copy" value="コピー" / -->
		<!-- input type="button" name="btn_print" id="btn_print" value="印刷" / -->
		<input type="button" name="btn_delete" id="btn_delete" value="削除" />
		<input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
		<!-- input type="button" name="btn_new" id="btn_new" value="新規" / -->
		<!-- input type="button" name="btn_check" id="btn_check" value="入力チェック" />
		<input type="button" name="btn_clear_err" id="btn_clear_err" value="エラークリア" / -->
	  </div><!-- /.buttons -->

	</div><!-- /.order -->
  </div><!-- /#main -->
</form><!-- /#my-form-id -->

<!-- 明細行 テンプレート -->
<script type="text/template" id="item-template">
  <!-- tbody -->
  <tr>
	<td class="row" rowspan="2"><span class="line_no"></span></td>
	<td class="control no-border-bottom"><a href="javascript:void(0)" 
        class="destroy my-readonly-hidden"><i title="この行を削除します" class="glyphicon glyphicon-trash"></i></a></td>
	<!-- td class="type" rowspan="2">
      <input type="hidden" style="width:90%" value="{$denku}"
             class="msi-picker denku small" data-picker-kind="cdNm"
             data-picker-param="kind2:'0420',placeholder:''" />
    </td -->
	<td class="juchu_bi" rowspan="2">
      <input name="juchu_bi_v" type="text" class="juchu_bi_v my-txt60 my-type-date" style="text-align:center;ime-mode:disabled" value="" />
      <div class="label dlg_juchu_bi_v my-type-date-trg dlg_date" data-ref-rel="#juchu_bi_v"></div>
    </td>
	<td class="item_id shohin-ref" rowspan="2"><input type="text" class="my-txt60 shohin_cd" value="" 
                                           /><div class="label dlg_shohin shohin-ref my-readonly-hidden cursor-pointer"></div></td>
	<td class="item_nm"><input type="text" class="my-txt95 shohin_nm" value="" readonly  style="ime-mode:active"/></td>
	<td class="price" rowspan="2"><input type="text" class="my-txt90 juchu_tanka my-select-all commafy" value="" readonly style="ime-mode: disabled"/></td>
	<td class="quantity" rowspan="2"><input type="text" 
                                            class="my-txt90 suryo text-right my-select-all" value="" style="ime-mode: disabled"/></td>
	<td class="nebiki my-border-bottom" rowspan="1"><input type="text" class="my-txt90 nebiki_gaku my-select-all commafy" 
                                                           value="" readonly style="ime-mode: disabled"/></td>
	<td class="juchu_gaku_cal" rowspan="1"><input type="text" class="my-txt90 juchu_gaku_cal my-select-all commafy" value="" readonly style="ime-mode: disabled"/></td>
	<td class="memo2" rowspan="2"><input type="text" class="my-txt95 nafuda_nm" value="" maxlength="60" style="ime-mode:active"/></td>
  </tr>
  <tr>
	<td class="control"><a href="javascript:void(0)"
        class="add my-readonly-hidden"><i title="直下に行追加します" class="glyphicon glyphicon-plus-sign"></i></a></td>
	<td class="tekiyo"><input type="text" class="my-txt95 shohin_tekiyo" value="" readonly style="ime-mode:active"/></td>
	<td class="soko-cd"><input type="text" class="my-txt60 soko_cd" value="" readonlystyle="ime-mode: disabled"/><div class="label dlg_soko my-readonly-hidden cursor-pointer"></div></td>
	<td class="soko-nm"><div class="soko_nm"></div></td>
        
	<!-- td class="unit">
      <input type="hidden" style="width:90%" value="{$tani_cd}"
             class="msi-picker tani_cd small" data-picker-kind="tani" 
             data-picker-param="placeholder:'',minWidth:100" readonly />
    </td -->
	<!-- td class="cost my-border-bottom"><input type="text" class="my-txt80 genka_gaku" value="" readonly style="ime-mode: disabled"
                                              /></td -->
	<!-- <td class="memo1"><input type="text" class="my-txt95 msi_biko1" value="" maxlength="30"  style="ime-mode:active"/></td> -->
    </tr>
  <!-- /tbody -->
</script><!-- /#item-template -->

<script id="my-data-init-id" type="application/json">
{$mydata_json|smarty:nodefaults}
</script>

{include file="fdn_footer_std.tpl"}
