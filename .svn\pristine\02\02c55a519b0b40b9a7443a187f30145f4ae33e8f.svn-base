<?php

/**
 * Juchu_JuchuEstimate
 *
 * 見積書クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuEstimate
 * <AUTHOR> Sai
 * @since      2014/03/26
 * @version 2015/08/19 佐野カスタマイズ
 * @version 2019/04/30 mihara 軽減税率対応
 * @version    2020/03/16 nasu nise02からコピー　補助コード追加
 * @version 2020/12/02 arai MOUSHI_KBN_SAMPLEのコードブロックを削除
 * @version    2020/12/04 kino 売上伝票と施行発注管理情報のJOIN条件を変更 ex)old→shi.jc_denpyo_no = udm.uri_den_no, new→hi.uri_denpyo_no = udm.uri_den_no
 * @version 2020/12/09 arai 権限区分取得処理追加
 * @version    2021/06/XX kino 商品詳細情報保存処理追加
 * @version    2021/07/12 nasu 売上伝票作成処理に施行プランコードと内訳明細コードを追加
 * @version    2021/07/13 nasu 売上伝票明細保存内容にupgrade_kbn追加
 * @version    2021/07/27 nasu アップグレード対応
 * @version    2021/09/30 nasu Mocのステータス更新処理追加（nise03,昭和興業）
 * @filesource 
 */

/**
 * 見積書クラス
 *
 * @category   App
 * @package    controllers\Juchu\Juchu_JuchuEstimate
 * <AUTHOR>
 * @since      2014/03/26
 */
class Juchu_JuchuEstimate extends Juchu_JuchuAbstract {

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/26
     * @return array jsonData
     */
    public function getInitData() {
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();

        $data = $this->getData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 見積書データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     * @return array 見積書データ
     */
    protected function getData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // APPデータを取得する
        $dataApp = $this->getAppData();
        // 承認データを取得する
        $dataApp['shonin'] = $this->getShoninData();
        $dataApp['role_kbn'] = App_Utils::ifRolesEqualTo('sysman') || App_Utils::ifRolesEqualTo('jimu') || App_Utils::ifRolesEqualTo('manager');
        // 請求データを取得する
        $dataApp['seikyu'] = $this->getSeikyuData();
        // 商品分類データを取得する
        $dataBunrui = $this->getBunruiData();
        // 葬送儀礼～その他の明細データを取得する
        $dataCol = $this->getDetailData();
        $this->adjColData($dataCol);
        DataMapper_Pdf0113::adjMeisaiData($dataCol, $this->_gojokaiKbn, array('service_kbn' => 'nebiki_kbn'));
        //ログイン者の権限データを取得する
        $role = Msi_Sys_Utils::getAuthInfo('roles_arr');
        $dataApp['login_role'] = $role[0];
        // 名札（画像ファイル情報マスタ）取得  2023/03 add kino
        $dataKbns['nafuda_gazo'] = DataMapper_GazoFileInfoMst::findForNafuda($db, array('gazo_kbn' => '7'));
        // 画面データを設定する
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataCol,
            'dataBunrui' => $dataBunrui,
            'dataKbns' => $dataKbns,
        );
        return $data;
    }

    /**
     *
     * APPデータを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @version 2020/12/09 arai 権限区分取得処理追加
     * @return array APPデータ
     */
    private function getAppData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 見積基本情報を取得する
        $estimate = $this->selectSekoKihon();
        // 奉仕率情報を取得する
        $hoshi = $this->getHosiritu();
        // 消費税情報を取得する
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        // 互助会加入情報を取得する
        $gojokai = $this->getGojokaiMember();
        // 消費税等情報を取得する
        if (App_Utils::isMitsuInJuchu() || App_Utils::isHoujiInJuchu()) {
            $shohizei = App_MitsuLib::getKingaku($db, $this->_sekoNo, $this->_sekoNoSub, $this->getDataKbn());
        } else {
            $shohizei = App_SeikyuLib::getKingaku($db, $this->_sekoNo, $this->_sekoNoSub, $this->getDataKbn());
        }

        $estimate['hoshi_ritu_cd'] = $hoshi['hoshi_ritu_cd'];
        $estimate['hoshi_ritu_org'] = $hoshi['zei_rtu_org'];
        $estimate['hoshi_ritu'] = $hoshi['zei_rtu'];
        $estimate['hasu_kbn'] = $hoshi['hasu_kbn'];
        $estimate['shohi_zei_rtu'] = (int) $taxInfo['zei_rtu'];
        $estimate['shohi_zei_cd'] = (int) $taxInfo['zei_cd'];
        $estimate['sosogorei_plan_a_nm'] = $this->getChubunruiName('0020'); // 0020:【Ａ】葬送儀礼費用基本プラン
        $estimate['sosogorei_plan_b_nm'] = $this->getChubunruiName('0030'); // 0030:【Ｂ】葬送儀礼費用プラン外選択商品
        $estimate['kaiin_no'] = $this->_kaiinNo;
        $estimate['seko_plan_cd'] = $this->_sekoPlanCd;
        $estimate['no_seko_plan_msg'] = '基本プランが選択されていないため、見積書を作成することができません。';
        $estimate['gojokai'] = $gojokai;
        $estimate['gojokai_zeirtu'] = DataMapper_SekoGojokaiMember::getZeirtu($db, array('seko_no' => $this->_sekoNo));
        $estimate['shohizei'] = $shohizei;
        // コード名称マスタデータを取得する
        $estimate['printKbn'] = DataMapper_MsterGetLib::GetCodeNameMst($db, '0910');
        // 会社情報会員名取得する
        $estimate['kain_nm'] = App_Utils::getKainNm();
        // 消費税名または掛金消費税差額かのフラグを取得する
        $estimate['zei_flg'] = App_Utils::getZeiNmFlg($this->_sekoNo);
        // コンビニ入金票発行区分
        $estimate['is_konbini_hako_kbn'] = App_Utils::KonbiniHakoKbn();
        $estimate['uri_den_no'] = $this->getUriagedenpyoNo();
        //権限区分合計を取得する
        $estimate['n_free10'] = $this->getTotalAuthority($estimate['uri_den_no']);
        return $estimate;
    }

    /**
     *
     * 葬送儀礼中分類名を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @param string $chubunruiCd 中分類コード
     * @return string 中分類名
     */
    private function getChubunruiName($chubunruiCd) {
        $chubunruiNm = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                cbm.chu_bunrui_nm -- 中分類名
            FROM
                shohin_chu_bunrui_mst cbm 
            WHERE
                cbm.delete_flg = 0
            AND cbm.chu_bunrui_cd = :chu_bunrui_cd
                ";
        $select = $db->easySelOne($sql, array('chu_bunrui_cd' => $chubunruiCd));
        if (count($select) > 0) {
            $chubunruiNm = $select['chu_bunrui_nm'];
        }
        return $chubunruiNm;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/3
     * @return array 施行互助会加入者情報
     */
    private function getGojokaiMember() {

        $db = Msi_Sys_DbManager::getMyDb();
		$kaisya_cd = App_Utils::getCtxtKaisyaEasy();// 会社コード取得 ADD Otake 2017/05/31
        $sql = "
            SELECT
                gm.seko_no          -- 施行番号
                ,gm.kain_no         -- 会員番号
                ,gm.gojokai_cose_cd -- 互助会コースコード
                ,gm.kanyu_nm        -- 加入者名
                ,gm.keiyaku_gaku    -- 契約金額
                ,gm.yoto_kbn        -- 用途
                ,gm.harai_gaku      -- 払込金額
                ,gm.harai_no        -- 払込回数
                ,gm.wari_gaku       -- 前納割引額
                ,TO_CHAR(gm.kanyu_dt ,'YYYY/MM/DD') AS kanyu_dt-- 加入年月日
                ,TO_CHAR(gm.zei_kijyn_ymd ,'YYYY/MM/DD') AS zei_kijyn_ymd-- 消費税基準日
                ,gm.zei_cd          -- 消費税コード
                ,gm.cose_chg_gaku   -- コース変更差額金
                ,gm.early_use_cost  -- 早期利用費
                ,COALESCE(zm.zei_rtu, 0) AS zei_rtu -- 消費税消費税率
            FROM
                seko_gojokai_member gm
                LEFT OUTER JOIN
                    zei_mst zm
                ON  (
                        gm.zei_cd = zm.zei_cd
                    AND zm.delete_flg = 0
					AND zm.kaisya_cd = :kaisya_cd -- ADD Otake 2017/05/31
                    )
            WHERE
                    gm.seko_no = :seko_no
                AND gm.yoto_kbn IN (1, 2, 3) -- 用途区分　1:葬送儀礼 2:返礼品 3:壇払い
                AND gm.delete_flg = 0
            ORDER BY
                gm.yoto_kbn
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'kaisya_cd' => $kaisya_cd)); //kaisya_cd ADD Otake 2017/05/31
        return $select;
    }

    /**
     *
     * 承認情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/16
     * @return array 承認情報
     */
    private function getShoninData() {
        // 施行担当印鑑
        $dataShonin["inkan_oid"] = App_Utils::getTantoInkanOid($this->_sekoTantoCd);
        // 見積確定
        $dataShonin["juchukakutei_dt"] = Msi_Sys_Utils::getDatetime(App_DateCalc::strToEpoch($this->_juchuKakuteiYMD), 'm/d H:i');
        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();
        $dataShonin["hasShonin"] = $this->hasUriageShonin($dateUriShonin);
        $dataShonin["hasShoninAll"] = $this->hasUriageShonin($dateUriShonin, 'ALL');
        return array_merge($dataShonin, $dateUriShonin);
    }

    /**
     *
     * 請求情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/18
     * @return array 請求情報
     */
    protected function getSeikyuData() {
        return array();
    }

    /**
     *
     * 葬送儀礼～その他の明細データを取得する
     *  ※SQL修正する場合、Saiken_SeikyuShoninのSQLも修正が必要
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @version 2015/05/03 施行発注管理情報のJOINにデータ区分を追加 Kayo
     * @version 2021/07/XX kino 名札を追加
     * @return array 明細データ
     */
    protected function getDetailData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            dm.msi_no -- 明細番号
            ,dm.dai_bunrui_cd -- 大分類コード
            ,dm.chu_bunrui_cd -- 中分類コード
            ,cbm.chu_bunrui_nm -- 中分類名
            ,dm.shohin_kbn -- 商品区分
            ,dm.shohin_kbn_nm-- 商品区分名
            ,dm.shohin_cd -- 商品コード
            ,dm.shohin_nm -- 商品名
            ,dm.shohin_tkiyo_nm -- 商品摘要名
            ,dm.mokuteki_kbn -- 目的区分
            ,dm.juchu_suryo -- 数量
            ,dm.juchu_tnk -- 単価
            ,dm.juchu_suryo * juchu_tnk AS juchu_prc -- 受注金額
            ,dm.gojokai_nebiki_prc -- 付帯値引き
            ,dm.nebiki_prc -- 値引き
            ,dm.juchu_suryo * juchu_tnk + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
            ,dm.hoshi_umu_kbn -- 奉仕料有無区分
            ,dm.hoshi_prc -- 奉仕料
            ,sm.tnk_chg_kbn-- 売上単価変更区分
            ,sm.nm_input_kbn-- 名称入力区分
            ,sbm.hachu_kbn -- 発注書区分
            ,sm.tani_cd-- 単位コード
            ,sm.uri_kamoku_cd-- 科目コード
            ,dm.zei_kbn -- 消費税区分
            ,dm.gen_tnk-- 原価単価
            ,CASE  -- タブ区分 1:葬送儀礼, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ
                WHEN dm.dai_bunrui_cd = '0010' THEN '1'
                WHEN dm.dai_bunrui_cd = '0020' THEN '2'
                WHEN dm.dai_bunrui_cd = '0030' THEN '3'
                WHEN dm.dai_bunrui_cd = '0040' THEN '3'
                WHEN dm.dai_bunrui_cd = '0060' THEN '4'
                WHEN dm.dai_bunrui_cd = '0050' THEN '5'
                WHEN dm.dai_bunrui_cd = '0070' THEN '5'
            END tab_kbn
            ,CASE 
                spsm.service_kbn 
                WHEN 1 THEN '1' 
                WHEN 2 THEN '2' 
                ELSE '0' 
            END nebiki_kbn -- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
            ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
            ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                WHEN '0020' THEN '0'
                ELSE '1'
            END add_down_kbn
            ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                WHEN '0020' THEN '0'
                ELSE '1'
            END del_kbn
            ,dm.add_kbn -- 追加区分 0:トランデータ, 1:見積画面新規入力データ
            ,COALESCE(shi.siire_cd, dm.siire_cd) AS siire_cd       -- 仕入コード
            ,COALESCE(siirem.siire_lnm, dm.siire_lnm) AS siire_lnm -- 仕入名     
            ,shi.order_flg      -- 発注済み 0:未発注 1:発注済み
            ,sbm.print_group_cd
            ,dm.out_zei_prc
            ,dm.in_zei_prc
            ,dm.zei_cd  -- 2019/04/30 mihara keigen
            ,dm.reduced_tax_rate --  2019/04/30 mihara keigen
            ,dm.nafuda_umu_kbn -- 202303 add kino
            ,dm.nafuda_gazo_cd -- 202303 add kino
            ,dm.nafuda_nm -- 202107 add kino
            ,dm.nafuda_nm2 -- 202107 add kino
            ,dm.nafuda_nm3 -- 202107 add kino
            ,dm.nafuda_nm4 -- 202107 add kino
            ,dm.nafuda_nm5 -- 202107 add kino
            ,dm.nafuda_nm6 -- 202107 add kino
            ,dm.nafuda_nm7 -- 202107 add kino
            ,dm.nafuda_nm8 -- 202107 add kino
            ,dm.nafuda_nm9 -- 202107 add kino
            ,dm.nafuda_nm10 -- 202107 add kino
            ,dm.seko_plan_cd -- 2021/07/27 add nasu
            ,dm.seko_plan_uchiwk_no -- 2021/07/27 add nasu
            ,sm.sagaku_keisan_grp -- 2021/07/27 add nasu
            ,dm.upgrade_kbn -- 2021/07/27 add nasu
        FROM
            (
                SELECT
                    m1.msi_no
                    ,m1.denpyo_kbn
                    ,m1.seko_no
                    ,h1.juchu_ymd
                    ,m1.disp_no
                    ,m1.add_kbn
                    ,m1.dai_bunrui_cd
                    ,m1.chu_bunrui_cd
                    ,m1.shohin_kbn
                    ,skm.shohin_kbn_nm
                    ,m1.shohin_cd
                    ,m1.shohin_nm
                    ,m1.shohin_tkiyo_nm
                    ,m1.juchu_suryo
                    ,m1.juchu_tnk
                    --,m1.juchu_suryo * m1.juchu_tnk AS m1.juchu_prc
                    ,m1.hoshi_umu_kbn
                    ,m1.hoshi_prc
                    ,m1.gojokai_nebiki_prc
                    ,m1.nebiki_prc
                    ,m1.zei_kbn        
                    ,m1.gen_tnk
                    ,m1.mokuteki_kbn
                    ,m1.siire_cd
                    ,m1.siire_lnm
                    ,m1.out_zei_prc
                    ,m1.in_zei_prc
                    ,m1.zei_cd  -- 2019/04/30 mihara keigen
                    ,m1.reduced_tax_rate -- 2019/04/30 mihara keigen
                    ,m1.nafuda_umu_kbn -- 202303 add kino
                    ,m1.nafuda_gazo_cd -- 202303 add kino
                    ,m1.nafuda_nm -- 202107 add kino
                    ,m1.nafuda_nm2 -- 202107 add kino
                    ,m1.nafuda_nm3 -- 202107 add kino
                    ,m1.nafuda_nm4 -- 202107 add kino
                    ,m1.nafuda_nm5 -- 202107 add kino
                    ,m1.nafuda_nm6 -- 202107 add kino
                    ,m1.nafuda_nm7 -- 202107 add kino
                    ,m1.nafuda_nm8 -- 202107 add kino
                    ,m1.nafuda_nm9 -- 202107 add kino
                    ,m1.nafuda_nm10 -- 202107 add kino
                    ,m1.seko_plan_cd -- 2021/07/27 add nasu
                    ,m1.seko_plan_uchiwk_no -- 2021/07/27 add nasu
                    ,m1.upgrade_kbn -- 2021/07/27 add nasu
					,h1.data_kbn							-- 2015/05/03 ADD Kayo
                FROM juchu_denpyo	h1						-- 2015/05/03 ADD Kayo
                    LEFT OUTER JOIN	juchu_denpyo_msi m1		-- 2015/05/03 UPD Kayo
					ON	h1.denpyo_no = m1.denpyo_no			-- 2015/05/03 UPD Kayo
					AND 0			 = m1.delete_flg		-- 2015/05/03 UPD Kayo
                    INNER JOIN
                        shohin_kbn_mst skm
                    ON  (
                            m1.shohin_kbn = skm.shohin_kbn
                        AND skm.delete_flg = 0
                        )	
                WHERE
                    h1.delete_flg = 0						-- 2015/05/03 UPD Kayo
                AND h1.seko_no		= :seko_no				-- 2015/05/03 UPD Kayo
                AND h1.seko_no_sub	= :seko_no_sub			-- 2015/05/03 UPD Kayo
				AND h1.data_kbn		IN (1,2)				-- 2015/05/03 ADD Kayo
                AND m1.data_sbt IN (1, 2, 3, 4, 5, 6, 7) --データ種別 1：葬送儀礼 2：返礼品　3：料理 4：壇払い 5：別途費用 6：立替金 7：値引き
            ) dm
            INNER JOIN
                shohin_chu_bunrui_mst cbm
            ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                AND cbm.delete_flg = 0)
            INNER JOIN
                shohin_mst sm
            ON  (
                    dm.shohin_cd = sm.shohin_cd
                AND  sm.hihyoji_kbn = 0
                AND sm.delete_flg = 0
                )
            LEFT OUTER JOIN         -- 2014/07/11 UPDATE Kayo
                shohin_bunrui_mst sbm
            ON  (
                    dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                AND dm.shohin_kbn  = sbm.shohin_kbn
                AND dm.shohin_cd  = sbm.shohin_cd
                AND sbm.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_plan_smsi_mst spsm
            ON  (
                    dm.dai_bunrui_cd = spsm.dai_bunrui_cd
                AND dm.chu_bunrui_cd  = spsm.chu_bunrui_cd
                AND dm.shohin_kbn  = spsm.shohin_kbn
                AND dm.shohin_cd  = spsm.shohin_cd
                AND TO_CHAR(spsm.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(dm.juchu_ymd,'YYYY/MM/DD')
                AND TO_CHAR(spsm.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(dm.juchu_ymd,'YYYY/MM/DD')
                AND spsm.seko_plan_cd = :seko_plan_cd
                AND spsm.gojokai_kbn = :gojokai_kbn
                AND spsm.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_hachu_info shi
                ON  (
                    dm.seko_no = shi.seko_no
                    AND dm.msi_no = shi.jc_msi_no
                    AND dm.shohin_cd = shi.shohin_cd
					AND dm.data_kbn	 = shi.data_kbn		-- 2015/05/03 ADD Kayo
                    AND shi.hachu_no_moto IS NULL
                    AND shi.delete_flg = 0
                    )
                        -- 仕入先マスタ
            LEFT OUTER JOIN siire_mst siirem			
                ON  shi.siire_cd = siirem.siire_cd		
                AND 0 = siirem.delete_flg
        ORDER BY
            sbm.mitumori_print_seq
            ,dm.dai_bunrui_cd
            ,dm.disp_no
            ,dm.msi_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
            'seko_plan_cd' => $this->_sekoPlanCd,
            'gojokai_kbn' => $this->_gojokaiKbn,
        ));
        return $select;
    }

    /**
     * 保存処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     */
    public function save() {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        if ($this->isKakuteiOutNg()) {
            return;
        }
        
        $dataHachuEdiCol = Msi_Sys_Utils::json_decode($req->getPost('appediColJSON'));
        $dataHachuEdiDelCol = Msi_Sys_Utils::json_decode($req->getPost('appediDelColJSON'));

//        $dataApp['juchu_kbn'] = parent::MITSU;
        // 明細削除データ
        $dataDelCol = Msi_Sys_Utils::json_decode($req->getPost('dataDelColJson'));
        // データ加工処理
        $dataCol = $this->getDataCol($req);

        $db = Msi_Sys_DbManager::getMyDb();
        
        // 発注済かをチェックし、発注済ならキャンセル画面を表示させる
        if ( $this->_chkHachuAndCancel($dataApp, $dataCol, $dataDelCol) ) {
            return; // JSONデータ送信はすでに行っている
        }
        
        // 商品詳細情報保存処理 202106XX add kino
        if($dataHachuEdiCol || $dataHachuEdiDelCol){
            Logic_Hachu_UnivAdcHachu::upsert($dataHachuEdiCol, $dataHachuEdiDelCol, $this->_sekoNo);
        }
        
//        // 発注済みのチェック 2018/12/21 ADD Kayo
//        $msg = App_HachuLib::chkHachuiOrder($db, $this->_sekoNo, $dataCol, $dataDelCol);
//        if (strlen($msg) > 0)   {
//            $data = array(
//                'status' => 'NG',
//                'msg'    => $msg
//            );
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }

//        Msi_Sys_Utils::profilerMark('saveJuchu-start');
        // 受注伝票を保存する
        $cnt = $this->saveJuchu($db, $dataApp, $dataCol, $dataDelCol);
//        Msi_Sys_Utils::profilerMark('saveJuchu-end');
        // 施行発注管理情報を保存する
        $cnt +=$this->saveHachuInfo($db, $dataApp, $dataCol, $dataDelCol);

        $sbt = 1;
        $item = 12;
        if ($this->_moushiKbn === self::MOUSHI_KBN_HOUJI) {
            $sbt = 4;
            $item = 15;
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $sbt, $item);
        $db->commit();

        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '更新しました';
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 見積データ加工処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     * @param request $req リクエスト
     */
    private function getDataCol($req) {
        $dataCol = array();
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $dataCol1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson1')); // 葬送儀礼
        $dataCol2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson2')); // 返礼品
        $dataCol3_1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson3_1')); // 飲食費(料理) 中分類コード:0070
        $dataCol3_2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson3_2')); // 飲食費(壇払い) 中分類コード:0080
        $dataCol4 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson4')); // 立替金
        $dataCol5_1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson5_1')); // その他(別途費用) 中分類コード:0090
        $dataCol5_2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson5_2')); // その他(値引き) 中分類コード:0120
        $dataCol6 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson6')); // パックプラン
        // 葬送儀礼処理
        if ($changeFlg['meisaiChangeFlg1']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol1);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol1);
        }
        // 返礼品処理
        if ($changeFlg['meisaiChangeFlg2']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol2);
        }
        // 飲食費処理
        if ($changeFlg['meisaiChangeFlg3']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol3_1);
            $this->setMitsuInfo($dataCol3_2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol3_1, $dataCol3_2);
        }
        // 立替金処理
        if ($changeFlg['meisaiChangeFlg4']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol4);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol4);
        }
        // その他処理
        if ($changeFlg['meisaiChangeFlg5']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol5_1);
            $this->setMitsuInfo($dataCol5_2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol5_1, $dataCol5_2);
        }
        // パックプラン
        if (isset($changeFlg['meisaiChangeFlg6']) && $changeFlg['meisaiChangeFlg6']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol6);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol6);
        }
        return $dataCol;
    }

    /**
     * 見積の受注伝票明細情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     * @param array &$dataCol 受注伝票明細
     */
    private function setMitsuInfo(&$dataCol) {
        for ($i = 0; $i < count($dataCol); $i++) {
            // 表示順(連番)
            $dataCol[$i]['disp_no'] = $i + 1;
            // 伝票区分 1：受注
            if (!isset($dataCol[$i]['denpyo_kbn'])) {
                $dataCol[$i]['denpyo_kbn'] = 1;
            } else if ($dataCol[$i]['denpyo_kbn'] === '2') {
                // 返品の場合
            }
            // 目的区分
//            if (!isset($dataCol[$i]['mokuteki_kbn'])) {
//                $dataCol[$i]['mokuteki_kbn'] = $this->getMokutekiKbn($dataCol[$i]);
//            }
            // 部門コード
            if (!isset($dataCol[$i]['bumon_cd'])) {
                $dataCol[$i]['bumon_cd'] = $this->_bumonCd;
            }
            // 値引き額
            if (!isset($dataCol[$i]['nebiki_prc'])) {
                $dataCol[$i]['nebiki_prc'] = 0;
            }
            // 納入先情報デフォルト
            $dataCol[$i]['nonyu_cd'] = null;
            $dataCol[$i]['nonyu_nm'] = null;
            $dataCol[$i]['nonyu_knm'] = null;
            $dataCol[$i]['nonyu_yubin_no'] = null;
            $dataCol[$i]['nonyu_addr1'] = null;
            $dataCol[$i]['nonyu_addr2'] = null;
            $dataCol[$i]['nonyu_tel'] = null;
            $dataCol[$i]['nonyu_fax'] = null;
            $dataCol[$i]['nonyu_dt'] = null;
            // 仕入先情報デフォルト
            if (!isset($dataCol[$i]['siire_cd'])) {
                $dataCol[$i]['siire_cd'] = null;
            }
            if (!isset($dataCol[$i]['siire_lnm'])) {
                $dataCol[$i]['siire_lnm'] = null;
            }
        }
    }

    /**
     *
     * データ種別を取得する
     * 1 => '葬送儀礼', 2 => '返礼品', 3 => '料理', 4 => '壇払い', 5 => '別途費用', 6 => '立替金', 7 => '値引き'
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @param array $record 受注伝票明細
     * @return string 3:料理
     */
    protected function getDataSbt($record) {
        $datasbt = '';
        $dabunrui = $record['dai_bunrui_cd'];
        if ($dabunrui === '0010') {
            $datasbt = '1';
        } else if ($dabunrui === '0020') {
            $datasbt = '2';
        } else if ($dabunrui === '0030') {
            $datasbt = '3';
        } else if ($dabunrui === '0040') {
            $datasbt = '4';
        } else if ($dabunrui === '0050') {
            $datasbt = '5';
        } else if ($dabunrui === '0060') {
            $datasbt = '6';
        } else if ($dabunrui === '0070') {
            $datasbt = '7';
        }
        return $datasbt;
    }

//    /**
//     *
//     * 目的区分を取得する
//     * 1 => '会葬', 2 => 'あつらい', 3 => '忌中', 4 => '遺物', 10 => '通夜用', 20 => '当日昼食', 30 => '壇払い'
//     * <AUTHOR> Sai
//     * @since 2014/3/31
//     * @return string 目的区分
//     */
//    private function getMokutekiKbn($record) {
//        $mokutekiKbn = '';
//        $dabunrui = $record['dai_bunrui_cd'];
//        if ($dabunrui === '0010') {
//            $mokutekiKbn = '1';
//        } else if ($dabunrui === '0020') {
//            $mokutekiKbn = '2';
//        } else if ($dabunrui === '0030') {
//            $mokutekiKbn = '20';
//        } else if ($dabunrui === '0040') {
//            $mokutekiKbn = '30';
//        } else if ($dabunrui === '0050') {
//            $mokutekiKbn = '1';
//        } else if ($dabunrui === '0060') {
//            $mokutekiKbn = '1';
//        } else if ($dabunrui === '0070') {
//            $mokutekiKbn = '1';
//        }
//        return $mokutekiKbn;
//    }

    /**
     *
     * 受注伝票明細テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 受注伝票明細情報
     */
    public function setJuchudenpyoMsiComInfo($dataApp, $record) {
        $juchuDenpyoMsi = array();
        if ($record['upsert'] === 1) { // 登録の場合
            $juchuDenpyoMsi = parent::setJuchudenpyoMsiComInfo($dataApp, $record);
            $juchuDenpyoMsi['add_kbn'] = 1; // 追加区分 
            $juchuDenpyoMsi['data_sbt'] = $this->getDataSbt($juchuDenpyoMsi); // データ種別 
        } else { // 更新の場合
            // 追加区分 0:トランデータ, 1:見積画面新規入力データ
            if ($record['add_kbn'] === '0') {
                // 見積書画面以外データは表示順のみ更新する
                $juchuDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
            } else {
                $juchuDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
                $juchuDenpyoMsi['bumon_cd'] = $record['bumon_cd'];  // 売上部門コード
                $juchuDenpyoMsi['mokuteki_kbn'] = $record['mokuteki_kbn'];  // 使用目的区分
                $juchuDenpyoMsi['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
                $juchuDenpyoMsi['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
                $juchuDenpyoMsi['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
                $juchuDenpyoMsi['shohin_cd'] = $record['shohin_cd'];  // 商品コード
                $juchuDenpyoMsi['shohin_nm'] = $record['shohin_nm'];  // 商品名
                $juchuDenpyoMsi['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
                $juchuDenpyoMsi['juchu_suryo'] = $record['juchu_suryo'];  // 商品数量
                $juchuDenpyoMsi['juchu_tnk'] = $record['juchu_tnk'];  // 単価
                $juchuDenpyoMsi['juchu_prc'] = $this->getPrc($record); // 受注金額
                $juchuDenpyoMsi['nebiki_prc'] = $record['nebiki_prc'];  // 値引額
                $juchuDenpyoMsi['gojokai_nebiki_prc'] = $record['gojokai_nebiki_prc'];  // 互助会値引額
                $juchuDenpyoMsi['gen_tnk'] = $record['gen_tnk'];  // 原価単価
                $juchuDenpyoMsi['gen_gaku'] = $this->getGenka($record);  // 原価金額
                $juchuDenpyoMsi['arari_gaku'] = $this->getAari($record); // 粗利益額
                $juchuDenpyoMsi['hoshi_prc'] = $record['hoshi_prc'];  // 奉仕料金額
                $juchuDenpyoMsi['hoshi_umu_kbn'] = $record['hoshi_umu_kbn'];  // 奉仕有無区分
                $juchuDenpyoMsi['data_sbt'] = $this->getDataSbt($juchuDenpyoMsi); // データ種別 
                $juchuDenpyoMsi['siire_cd'] = $record['siire_cd'];  // 仕入先コード
                $juchuDenpyoMsi['siire_lnm'] = $record['siire_lnm'];  // 仕入先名
                // 施行プランコード 2021/07/27 add nasu
                if (isset($record['seko_plan_cd'])) {
                    $record['seko_plan_cd'] = (isset($record['seko_plan_cd']) && empty($record['seko_plan_cd']))? null : $record['seko_plan_cd'];
                    $record['seko_plan_uchiwk_no'] = (isset($record['seko_plan_uchiwk_no']) && ($record['seko_plan_uchiwk_no'] !== '0' && empty($record['seko_plan_uchiwk_no'])))? null : $record['seko_plan_uchiwk_no'];
                    $juchuDenpyoMsi['seko_plan_cd'] = $record['seko_plan_cd'];  // 施行プランコード
                    $juchuDenpyoMsi['seko_plan_uchiwk_no'] = (isset($record['seko_plan_uchiwk_no']) || is_null($record['seko_plan_uchiwk_no']))? $record['seko_plan_uchiwk_no'] : $record['msi_no'];  // 施行プラン内訳明細no
                }
                $juchuDenpyoMsi['upgrade_kbn'] = (array_key_exists('upgrade_kbn', $record))? $record['upgrade_kbn'] : 0;  // アップグレード区分
//                if ($record['add_kbn'] === '2') {
//                    $juchuDenpyoMsi['add_kbn'] = 1; // 追加区分 
//                }
                // 消費税項目を設定する
                $this->setZeiInfo($dataApp, $juchuDenpyoMsi, $record, $juchuDenpyoMsi['juchu_prc'] + $juchuDenpyoMsi['hoshi_prc'] + $juchuDenpyoMsi['gojokai_nebiki_prc'] + $juchuDenpyoMsi['nebiki_prc']);
            }
        }
        // 名札 2021/07/XX add kino
        $nafudaData = Logic_Cmn_CmnNafuda::setNafudaData($record);
        foreach($nafudaData as $nfdKey => $nfdRec){
            $juchuDenpyoMsi[$nfdKey] = $nfdRec;
        }
        
        return $juchuDenpyoMsi;
    }

    /**
     *
     * 施行発注管理共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/9/24
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @param array $reportKanri レポート管理マスタ
     * @return array 施行発注管理情報
     */
    public function setHachuComInfo($dataApp, $record, $reportKanri) {

        $hachuInfo = array();
        if ($record['upsert'] === 1) { // 登録の場合
            $hachuInfo = parent::setHachuComInfo($dataApp, $record, $reportKanri);
        } else {
            $hachuInfo['report_cd'] = $reportKanri['report_cd'];  // 帳票コード
            $hachuInfo['ha_rp_cd'] = $reportKanri['ha_rp_cd'];  // 発注書区分コード
            $hachuInfo['ha_rp_kbn'] = $record['hachu_kbn'];  // 発注書区分
            $hachuInfo['ha_entry_kbn'] = $reportKanri['ha_entry_kbn'];  // 発注書入力区分
            $hachuInfo['ha_syori_kbn'] = $reportKanri['ha_syori_kbn'];  // 発注書処理区分
            $hachuInfo['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
            $hachuInfo['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
            $hachuInfo['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
            $hachuInfo['shohin_cd'] = $record['shohin_cd'];  // 商品コード
            $hachuInfo['shohin_nm'] = $record['shohin_nm'];  // 商品名
            $hachuInfo['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
            $hachuInfo['hanbai_tnk'] = $record['juchu_tnk'];  // 販売単価
            //if ($hachuInfo['ha_rp_kbn'] != self::HA_RP_KBN_SEIKA) {
                $hachuInfo['hachu_tnk'] = $record['gen_tnk'];  // 発注単価
                $hachuInfo['hachu_suryo'] = $record['juchu_suryo'];  // 商品数量
                $hachuInfo['hachu_prc'] = $record['gen_tnk'] * $record['juchu_suryo'];  // 発注金額
            //}
            $hachuInfo['tani_cd'] = $record['tani_cd'];  // 単位コード
            $hachuInfo['siire_cd'] = $record['siire_cd'];  // 仕入先コード
        }
        return $hachuInfo;
    }

    /**
     * 見積確定処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     * @version 2020/12/02 arai MOUSHI_KBN_SAMPLEのコードブロックを削除
     */
    public function mitsukakutei() {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        $cnt = 0;
        // 施行基本情報を設定する
        $this->setInitParam();
        // 生前依頼は見積確定ができない
        if ($this->_moushiKbn === self::MOUSHI_KBN_SEIZEN) {
            $this->outWarnJson('生前依頼のため、見積を確定することができません。');
            return;
        }
        // ミニプランの場合
        if ($this->_sekoPlanCd === self::PLAN_MINI) {
            $kasoYmd = $this->getNiteiYmdData(6);
            // 火葬日日が未入力の場合は見積確定ができない
            if (!isset($kasoYmd)) {
                $this->outWarnJson('火葬日が入力されていないため、見積を確定することができません。');
                return;
            }
        } else {
            // 葬儀日が未入力の場合は見積確定ができない
            if (!isset($this->_selectSekoKihon['sougi_ymd'])) {
                $this->outWarnJson('施行日が入力されていないため、見積を確定することができません。');
                return;
            }
        }
        
        // 葬儀の場合お客様情報必須チェック
//        if ($this->_moushiKbn === self::MOUSHI_KBN_SOUGI) {
//            $msg = $this->sekoKihonCheck();
//            if (!empty($msg)) {
//                $this->outWarnJson($msg);
//                return;
//            }
//        }
        // 未確定の場合
        if (!$this->isMitsuKakutei()) {
            $db = Msi_Sys_DbManager::getMyDb();

            // 売上伝票を作成する
            $cnt += $this->makeUriage($db);
            $denpyoNo = $this->getJuchudenpyoNo();
            $data_kbn = $this->getDataKbn();
            $cnt +=Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, $data_kbn); // 各種集計テーブル作成、更新処理
            // 施行発注管理情報を更新する 20201203 kino
            $cnt += $this->updSekoHachuIns($db);
            // 施行基本情報を設定する
            $kihon = array(
                'jichu_kakute_ymd' => Msi_Sys_Utils::getDatetimeStd(),
                'jichu_kakute_cd' => App_Utils::getTantoCd(),
                'status_kbn' => 2,
            );
            $where['seko_no'] = $this->_sekoNo;
            $where['delete_flg'] = 0;
            // 施行基本情報を更新する
            //$sql = $this->makeUpdateSQL("seko_kihon_info", $kihon, $where);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
            $cnt += $db->easyExecute($sql, $param);
            // 入庫出庫明細売上伝票明細番号設定処理
            $cnt +=$this->setNyukoShukoUriInfo($db);
            // 外部会員システム連携  2021/09/27 nasu nowldemoからコピー
            if ( App_Utils::isEnableKaiinRendoFunc('upd.seko') ) {
                list($kaiinErrMsg, $kaiinRtnInfo) = Logic_Exkaiin_Moc2KaiinUpdSeko::doExec( $db, $this->_sekoNo );
                if ( $kaiinErrMsg && App_Utils::isKaiinRendoUpdStrict() ) {
                    $this->outWarnJson('会員システム:' . $kaiinErrMsg);
                    return;
                }
            }
            $db->commit();
        }

        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '見積を確定しました';
        
        // 外部会員システム連携  2019/02/05
        if ( App_Utils::isEnableKaiinRendoFunc('upd.seko') ) {
            if ( isset($kaiinErrMsg) && strlen($kaiinErrMsg) > 0 ) {
                $data['status'] = 'NG';
                $data['msg'] = sprintf("%s. ただし会員システム連携処理エラーが発生しました: %s", $data['msg'], $kaiinErrMsg);
            }
        }
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 売上伝票作成処理
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @since 2014/04/04
     * @version 2020/12/09 arai 権限区分取得処理追加
     */
    private function makeUriage($db) {
        $cnt = 0;

        // 売上伝票番号採番処理
        $uriageDenpyoNo = $this->getAutoUriageDenpyoNo($db);
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        
        //権限区分を取得
        $authority=$this->getAuthority($db);
        $uri_n_free10_str = ", $authority AS n_free10 -- 数字フリー項目10 2020/12/09 add arai";
        
        // 申込区分により売上科目（補助科目）が変わる
        $data_kbn = $this->getDataKbn();
        $uri_kamoku_str = "";
        if ($data_kbn == 1) {
            $uri_kamoku_str = "                
                ,s.uri_kamoku_cd -- 科目コード 2020/03/16 add nasu
                ,s.uri_hojo_cd   -- 補助科目コード 2020/03/16 add nasu
                ";
        } else {
            $uri_kamoku_str = "                
                ,s.uri_kamoku_cd1 AS uri_kamoku_cd -- 科目コード 2020/03/16 add nasu
                ,s.uri_hojo_cd1 AS uri_hojo_cd   -- 補助科目コード 2020/03/16 add nasu
                ";
        }
        

        // 売上伝票登録SQL
        $sql1 = "
            SELECT
                denpyo_no
                ,juchu_ymd
                ,data_kbn
                ,seko_no
                ,seko_no_sub
                ,bumon_cd
                ,tanto_cd
                ,gojokai_cose_cd
                ,seko_plan_cd
                ,juchu_ymd AS seikyu_ymd   -- 請求日
                ,null AS zen_seikyu_ymd  -- 前回請求日
                ,0 AS sekkyu_kaisu     -- 請求回数
                ,kaishu_kbn
                ,kaishu_ymd
                ,0 AS nyukin_prc -- 入金金額
                ,juchu_prc_sum + juchu_hepn_sum + juchu_nebk_sum + hoshi_prc_sum + out_zei_prc + in_zei_prc AS seikyu_zan -- 請求残高
                ,sekyu_cd
                ,sekyu_nm
                ,sekyu_knm
                ,sekyu_soufu_nm
                ,sekyu_yubin_no
                ,sekyu_addr1
                ,sekyu_addr2
                ,sekyu_tel
                ,sekyu_fax
                -- ,delivery_kbn 売上伝票にない
                ,nonyu_cd
                ,nonyu_nm
                ,nonyu_knm
                ,nonyu_yubin_no
                ,nonyu_addr1
                ,nonyu_addr2
                ,nonyu_tel
                ,nonyu_fax
                ,nonyu_dt
                ,juchu_prc_sum AS uri_prc_sum
                ,genka_prc_sum
                ,juchu_hepn_sum AS uri_hepn_sum
                ,juchu_nebk_sum AS uri_nebk_sum
                ,hoshi_prc_sum
                ,szei_katax_taisho_prc
                ,uzei_katax_taisho_prc
                ,hitax_katax_taisho_prc
                ,tax_code_kbn
                ,tax_cd
                ,tax_kbn
                ,zei_cd
                ,out_zei_prc
                ,in_zei_prc
                ,arari_prc
                ,denpyo_biko1
                ,denpyo_biko2
                ,null AS inkan_img1 -- 承認１印鑑イメージ
                ,null AS inkan_img2 -- 承認２印鑑イメージ
                ,null AS shonin_dt1 -- 承認１日時
                ,null AS shonin_dt2 -- 承認２日時
                ,2    AS status_kbn -- ステータス
                $uri_n_free10_str
                ,delete_flg
            FROM
                juchu_denpyo
            WHERE
                denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";

        // 売上伝票明細登録SQL
        $sql2 = "
            SELECT
                msi_no
                ,disp_no
                ,add_kbn
                ,data_sbt
                ,m.bumon_cd
                ,seko_no
                ,seko_no_sub
                ,juchu_ymd
                ,denpyo_kbn
                ,mokuteki_kbn
                ,dai_bunrui_cd
                ,chu_bunrui_cd
                ,shohin_kbn
                ,m.shohin_cd
                ,m.shohin_nm
                ,m.shohin_tkiyo_nm
                ,juchu_suryo
                ,m.tani_cd
                ,juchu_tnk AS uri_tnk
                ,juchu_prc AS uri_prc
                ,nebiki_prc
                ,gojokai_nebiki_prc
                ,gen_tnk
                ,gen_gaku
                ,arari_gaku
                ,zei_Kbn
                ,zei_cd
                ,reduced_tax_rate -- mihara keigen
                ,out_zei_prc
                ,in_zei_prc
                ,nafuda_umu_kbn -- 202303 add kino
                ,nafuda_gazo_cd -- 202303 add kino
                ,nafuda_nm
                ,nafuda_nm2 -- 202107 add kino
                ,nafuda_nm3 -- 202107 add kino
                ,nafuda_nm4 -- 202107 add kino
                ,nafuda_nm5 -- 202107 add kino
                ,nafuda_nm6 -- 202107 add kino
                ,nafuda_nm7 -- 202107 add kino
                ,nafuda_nm8 -- 202107 add kino
                ,nafuda_nm9 -- 202107 add kino
                ,nafuda_nm10 -- 202107 add kino
                ,m.hoshi_umu_kbn
                ,hoshi_ritu_cd
                ,hoshi_prc
                --,delivery_kbn -- 納品場所区分 売上伝票にない
                ,nonyu_cd
                ,nonyu_nm
                ,nonyu_knm
                ,nonyu_yubin_no
                ,nonyu_addr1
                ,nonyu_addr2
                ,nonyu_tel
                ,nonyu_fax
                ,nonyu_dt
                ,msi_biko1
                ,msi_biko2
                ,m.siire_cd
                ,siire_lnm
                $uri_kamoku_str
                ,refer_uchiwk_no
                ,select_shohin_cd
                ,shuko_status_kbn
                ,m.delete_flg
                ,m.seko_plan_cd
                ,m.seko_plan_uchiwk_no
                ,m.upgrade_kbn
            FROM
                juchu_denpyo_msi m
            LEFT JOIN shohin_mst s -- 科目・補助科目を商品マスタから取得 2020/03/16 add nasu
                   ON s.shohin_cd = m.shohin_cd
                  AND s.delete_flg = 0
            WHERE
                denpyo_no = :denpyo_no
            AND m.delete_flg = 0
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $juchuDenpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenpyoNo));
        foreach ($select1 as $data) {
            $data['uri_den_no'] = $uriageDenpyoNo;
            $data['juchu_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            $data['keijo_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            // 売上伝票登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_denpyo', $data);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($select2 as $data) {
            $data['uri_den_no'] = $uriageDenpyoNo;
            $data['juchu_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            // 売上伝票明細登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_denpyo_msi', $data);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }
    
    /**
     * 施行発注情報を更新する(カラムに値を格納)
     *
     * <AUTHOR> kino
     * @param Msi_Sys_Db $db db
     * @since 2020/12/03
     */
    private function updSekoHachuIns($db) {
        
        $cnt = 0;
        
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        // 売上伝票番号取得する
        $uriageDenpyoNo = $this->getUriagedenpyoNo();
        
        // 売上伝票明細取得
        $sqlUri = "SELECT * FROM uriage_denpyo_msi WHERE uri_den_no = :uri_den_no AND delete_flg = 0";
        $selUri = $db->easySelect($sqlUri, array('uri_den_no' => $uriageDenpyoNo));
        foreach($selUri as $recUri){
            $updClm = array(
                'uri_denpyo_no' => $recUri['uri_den_no'],
                'uri_msi_no'    => $recUri['msi_no'],
            );
            $where['jc_denpyo_no'] = $juchuDenpyoNo;
            $where['jc_msi_no'] = $recUri['msi_no'];
            $where['delete_flg'] = 0;
            // 施行発注情報を更新する
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hachu_info", $updClm, $where);
            $cnt += $db->easyExecute($sql, $param);
        }
        
        return $cnt;
    }
    
    /**
     * 施行発注情報を更新する(カラムにnullを格納)
     *
     * <AUTHOR> kino
     * @param Msi_Sys_Db $db db
     * @since 2020/12/03
     */
    private function updSekoHachuDel($db) {
        
        $cnt = 0;
        
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        
        $clm = array(
            'uri_denpyo_no' => null,
            'uri_msi_no'    => null,
        );
        $where['jc_denpyo_no'] = $juchuDenpyoNo;
        $where['delete_flg'] = 0;
        // 施行発注情報を更新する
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hachu_info", $clm, $where);
        $cnt += $db->easyExecute($sql, $param);
        
        return $cnt;
    }

    /**
     * 見積取消処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     * @version 2015/03/03 施行発注管理情報の更新、削除 処理を追加 Kayo
     * @version 2015/06/08 再見積フラグ追加
     */
    public function mitsutorikesi($flg_saimitsu = false) {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = 0;
        // 施行基本情報を設定する
        $this->setInitParam();

        // 承認されたら見積確定不可
        if ($this->hasUriageShonin()) {
            $this->outWarnJson('承認済みのため、見積を取り消すことができません。');
            return false;
        }
        // 発注済商品があったら見積確定不可
        if (App_Utils2::hasHachuShohin($this->_sekoNo)) {
            $this->outWarnJson('発注済みの商品があるため、見積を取り消すことができません。');
            return false;
        }
        // 受注変更画面で変更(操作)をした場合、見積確定取消不可 20201203 kino
        if ($this->getHenkoFlg($db)) {
            $this->outWarnJson('受注変更画面で操作をしたため、見積を取り消すことができません。');
            return false;
        }else{
            // 施行発注管理情報を更新する
            $this->updSekoHachuDel($db);
        }
        $msg = '見積確定を取り消しました';
        // 確定済みの場合
        if ($this->isMitsuKakutei()) {

            // 売上伝票を論理削除する
            $cnt = $this->deleteUriage($db);
            // 受注確定→未確定に戻した場合、入金伝票が残ってしまう場合があるので、念のため	2014/06/29 ADD Kayo
            $cnt +=Logic_GojokaiNyukinDenpyo::NyukinDenDelete($db, $this->_sekoNo, '00'); // 入金伝票（互助会）削除 処理 受注確定→未確定にされた場合、入金伝票を削除する
            // 施行発注管理情報の更新、削除 処理 2015/03/03 ADD Kayo
            $cnt +=Logic_SekoHachuInfoUpdate::HachuInfoUpdate($db, $this->_sekoNo, '00');
            // 施行基本情報を設定する
            $kihon = array(
                'jichu_kakute_ymd' => null,
                'jichu_kakute_cd' => null,
                'status_kbn' => 1,
            );
            $where['seko_no'] = $this->_sekoNo;
            $where['delete_flg'] = 0;
            // 施行基本情報を更新する
            //$sql = $this->makeUpdateSQL("seko_kihon_info", $kihon, $where);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
            $cnt += $db->easyExecute($sql, $param);
            // 入庫出庫明細売上伝票明細番号クリア処理
            $cnt +=$this->clearNyukoShukoUriInfo($db);
            if ($flg_saimitsu) {
                $cnt +=$this->makeDenpyoRireki($db);
                $msg = '見積履歴を作成しました';
            }
            
            // 外部会員システム連携  2021/09/30 nasu nowldemoからコピー
            if ( App_Utils::isEnableKaiinRendoFunc('upd.seko') ) {
                list($kaiinErrMsg, $kaiinRtnInfo) = Logic_Exkaiin_Moc2KaiinUpdSeko::doExec( $db, $this->_sekoNo );
                if ( $kaiinErrMsg && App_Utils::isKaiinRendoUpdStrict() ) {
                    $this->outWarnJson('会員システム:' . $kaiinErrMsg);
                    return;
                }
            }

            // 外部会員システム連携  2021/09/30 nasu nowldemoからコピー
            if ( App_Utils::isEnableKaiinRendoFunc('upd.seko') ) {
                if ( isset($kaiinErrMsg) && strlen($kaiinErrMsg) > 0 ) {
                    $data['status'] = 'NG';
                    $data['msg'] = sprintf("%s. ただし会員システム連携処理エラーが発生しました: %s", $data['msg'], $kaiinErrMsg);
                }
            }
            $db->commit();
        }

        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = $msg;
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 再見積処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/08
     */
    public function saimitsu() {
        $this->mitsutorikesi(true);
    }

    /**
     * 見積履歴作成処理
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @since 2014/06/08
     */
    public function makeDenpyoRireki($db) {
        $cnt = 0;
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();

        // 売上伝票登録SQL
        $sql1 = "
            SELECT
                *
                ,now() AS history_dt
            FROM
                juchu_denpyo
            WHERE
                denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";

        // 売上伝票明細登録SQL
        $sql2 = "
            SELECT
                *
            FROM
                juchu_denpyo_msi
            WHERE
                denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $juchuDenpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenpyoNo));
        $except = array("_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn");
        $history_no = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $this->_sekoNo);
        $history_no +=1;
        foreach ($select1 as $data) {
            // 売上伝票登録SQL
            $data['history_no'] = $history_no;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($select2 as $data) {
            // 売上伝票明細登録SQL
            $data['history_no'] = $history_no;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_msi_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 売上伝票論理削除処理
     *
     * <AUTHOR> Sai
     * @version 2014/09/07 売上伝票、明細履歴テーブルの削除処理を追加 Kayo
     * @param Msi_Sys_Db $db db
     * @since 2014/04/04
     */
    private function deleteUriage($db) {
        $cnt = 0;
        // 売上伝票番号取得する
        $uriageDenpyoNo = $this->getUriagedenpyoNo();

        $sql1 = "
        UPDATE
            uriage_denpyo
        SET 
            delete_flg = 1
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";

        $sql2 = "
        UPDATE
            uriage_denpyo_msi
        SET 
            delete_flg = 1
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";

        $cnt += $db->easyExecute($sql1, array('uri_den_no' => $uriageDenpyoNo));
        $cnt += $db->easyExecute($sql2, array('uri_den_no' => $uriageDenpyoNo));
        // 売上伝票、売上伝票明細履歴の削除処理
        $ret = Logic_DenpyoHistoryMake::UriageHistoryDelete($db, $uriageDenpyoNo);

        return $cnt;
    }

    /**
     * サイドメニューデータ取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/17
     * @return array サイドメニューデータ
     */
    protected function getSideMenuData() {
        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub, $this->_moushiKbn);
        // 画面データを設定する
        $dataSideMenu = Juchu_Utils::getSideMenuData('estimate');
        return $dataSideMenu;
    }

    /**
     * 入庫出庫明細売上伝票明細番号設定処理(受注伝票明細番号を売上伝票明細番号にコピーする)
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return int 処理件数
     */
    private function setNyukoShukoUriInfo($db) {
        $cnt = 0;
        $msiZai = $this->selectNyukoShukoMsi($db);
        // 入庫出庫明細売上伝票明細番号設定処理
        foreach ($msiZai as $rec) {
            $denpyoMsi['uriage_den_msi_no'] = $rec['juchu_den_msi_no'];
            $where['denpyo_no'] = $rec['denpyo_no'];
            $where['msi_no'] = $rec['msi_no'];
            $where['bunkatu_no'] = $rec['bunkatu_no'];
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_msi", $denpyoMsi, $where);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
        // 入庫出庫伝票売上伝票番号設定処理
        if (count($msiZai) > 0) {
            $denpyo_no = $msiZai[0]['denpyo_no'];
            $uri_den_no = $this->getUriagedenpyoNo();
            $denpyo['uriage_den_no'] = $uri_den_no;
            $where2['denpyo_no'] = $denpyo_no;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_denpyo", $denpyo, $where2);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
    }

    /**
     * 入庫出庫明細売上伝票明細番号クリア処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return int 処理件数
     */
    private function clearNyukoShukoUriInfo($db) {
        $cnt = 0;
        $msiZai = $this->selectNyukoShukoMsi($db);
        // 入庫出庫明細売上伝票明細番号クリア処理
        foreach ($msiZai as $rec) {
            $denpyoMsi['uriage_den_msi_no'] = null;
            $where['denpyo_no'] = $rec['denpyo_no'];
            $where['msi_no'] = $rec['msi_no'];
            $where['bunkatu_no'] = $rec['bunkatu_no'];
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_msi", $denpyoMsi, $where);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
        // 入庫出庫伝票売上伝票番号クリア処理
        if (count($msiZai) > 0) {
            $denpyo_no = $msiZai[0]['denpyo_no'];
            $denpyo['uriage_den_no'] = null;
            $where2['denpyo_no'] = $denpyo_no;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_denpyo", $denpyo, $where2);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
    }

    /**
     * 入庫出庫明細取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return array 入庫出庫明細
     */
    private function selectNyukoShukoMsi($db) {
        $sql = '
                SELECT 
                    nsm.denpyo_no
                    ,nsm.msi_no
                    ,nsm.bunkatu_no
                    ,nsm.juchu_den_msi_no
                FROM 
                    nyuko_shuko_msi nsm
                    INNER JOIN nyuko_shuko_denpyo ns
                    ON 
                        (
                        nsm.denpyo_no = ns.denpyo_no
                        )
                WHERE 
                    nsm.delete_flg=0
                    AND ns.delete_flg=0
                    AND ns.seko_no=:seko_no';
        $msiZai = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        return $msiZai;
    }

    /**
     * 互助会区分が6の36万円コースの場合自動作成のサービス区分を変更する
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param array $dataCol
     * @return array $dataCol
     */
    private function adjColData(&$dataCol) {
        if ($this->_gojokaiKbn === '6') {
            foreach ($dataCol as &$value) {
                if ($value['nebiki_kbn'] === '1' && $value['add_kbn'] === self::ADD_KBN_AUTO) {
                    $value['nebiki_kbn'] = '0';
                }
            }
        }
    }

    private function getBunruiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = '
            SELECT 
                dai_bunrui_cd,chu_bunrui_cd
            FROM 
                shohin_bunrui_mst
            WHERE delete_flg  = 0 -- 2016/10/06 ADD Kayo
            GROUP BY 
                dai_bunrui_cd,chu_bunrui_cd
            ORDER BY 
                    dai_bunrui_cd,chu_bunrui_cd';
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     * 受注伝票履歴データ取得処理
     *
     * <AUTHOR> Sai
     * @since 2015/06/08
     * @return array jsonData
     */
    public function getHistoryData() {
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();

        $data = $this->getDenpyoRirekiData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     * 受注伝票履歴データ
     *
     * <AUTHOR> Sai
     * @since 2015/06/08
     * @return array 受注伝票履歴データ
     */
    private function getDenpyoRirekiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        $sql = "
                SELECT 
                    dh.seko_no
                    ,dh.denpyo_no
                    ,dh.history_no
                    ,TO_CHAR(dh.history_dt ,'YYYY/MM/DD HH24:MI') AS history_dt
                    ,dh.data_kbn
                    ,row_number() OVER () AS row
                FROM 
                    juchu_denpyo_history dh
                WHERE 
                    dh.denpyo_no = :denpyo_no
                    AND dh.delete_flg=0
                ORDER BY
                    history_no
                    ";
        $select = $db->easySelect($sql, array('denpyo_no' => $juchuDenpyoNo));
        return $select;
    }

    /**
     * お客様情報必須チェック
     *
     * <AUTHOR> Sai
     * @since 2017/05/19
     * @return string 
     */
    private function sekoKihonCheck() {
        $msg = null;
        $errItem = array();
        $checkItem = array('k_nm' => '故人お名前', 'k_knm' => '故人お名前フリガナ', 'kg_addr1' => '故人現住所1', 'k_seinengappi_ymd' => '故人生年月日',
            'souke_nm' => '葬家', 'souke_knm' => '葬家フリガナ', 'syushi_cd' => '宗旨',
            'm_nm' => '喪主お名前', 'm_knm' => '喪主お名前フリガナ', 'm_zoku_cd' => '続柄', 'm_zoku_cd2' => '喪主様からみた続柄',);
        $select = $this->getSekoKihon();
        if (count($select) > 0) {
            foreach ($checkItem as $key => $value) {
                if (strlen($select[$key]) == 0) {
                    $errItem[] = $value;
                }
            }
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $nitei_kaso = DataMapper_Pdf0101Nitei::find($db, array("seko_no" => $this->_sekoNo, "nitei_kbn" => 6));
//        if (count($nitei_kaso) > 0) {
//            $nitei = $nitei_kaso[0];
//            if (strlen($nitei['v_free5'] == 0)) {
//                if (empty($nitei['nitei_ymd'] )) {
//                    $errItem[] = '火葬日時';
//                }
//                if (empty($nitei['basho_nm'] )) {
//                    $errItem[] = '火葬場所';
//                }
//            }
//        }
        if (count($errItem) > 0) {
            $msg = 'お客様情報画面の必須項目入力に不備があるため、確定することができません。';
            $errs = implode(",", $errItem);
            $msg .= $errs;
        }
        return $msg;
    }
    
    /**
     * 発注済ならキャンセル画面を表示させる処理
     * BPNからコピー
     *
     * <AUTHOR> kino BPNからコピー
     * @since  2018/08/27
     * @param  array $dataApp
     * @param  array $dataCol
     * @param  array $dataDelCol
     * @return true(発注済ありでキャンセル画面を開く)|false(発注済なし)
     */
    protected function _chkHachuAndCancel($dataApp, $dataCol, $dataDelCol)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $req = Msi_Sys_Utils::getRequestObject();

        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson1')); // 元データ。変更されたかの判断に使う
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson2')); // 元データ。変更されたかの判断に使う
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson3')); // 元データ。変更されたかの判断に使う
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson4')); // 元データ。変更されたかの判断に使う
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson5')); // 元データ。変更されたかの判断に使う
        $arrOrgDataCol[] = Msi_Sys_Utils::json_decode($req->getPost('orgDataColJson35')); // 元データ。変更されたかの判断に使う
        
        $msi_nos = array();
        foreach ($arrOrgDataCol as $orgDataCol) {
            $hOrgCol = array_reduce( $orgDataCol,
                                     function($m, $v) { 
                                         if ( array_key_exists('msi_no', $v) ) {
                                             $m[ $v['msi_no'] ] = $v;
                                         }
                                         return $m; 
                                     }, [] );
            
            foreach ( $dataCol as $rec ) {
                if ( !isset($rec['msi_no']) ) { // msi_no がなければ追加行なので無視
                    continue;
                }
                $msi_no = $rec['msi_no'];
                if ( !array_key_exists($msi_no, $hOrgCol) ) { // 対応する前回行がない場合も無視
                    continue;
                }
                $orgRec = $hOrgCol[$msi_no];
                $flgChanged = false;
                foreach ( $orgRec as $key => $orgVal ) {
                    if (is_array($orgVal)) {
                        continue;
                    }
                    @ $curVal = $rec[$key];
                    if ( (string)$curVal === (string)$orgVal ) {
                        continue;
                    }
                    $flgChanged = true;
                    break;
                }
                if ( $flgChanged ) {
                    $msi_nos[] = $msi_no;
                }
            }
            foreach ( $dataDelCol as $rec ) { // 削除行はとりあえずデータを渡す
                if ( !isset($rec['msi_no']) ) { // msi_no がなければ追加行なので無視
                    continue;
                }
                $msi_no = $rec['msi_no'];
                $msi_nos[] = $msi_no;              
            }
        }

        $rtn = Logic_JuchuEtc::chkHachuAndCancel($db, $juchuDenpyoNo, $msi_nos, $this->_sekoNo);
        return $rtn;
    }
    
    /**
     * 受注変更画面で操作したのか判定
     *
     * <AUTHOR> kino
     * @since 2020/12/03
     * @param Msi_Sys_Db $db db
     */
    public function getHenkoFlg($db) {
        // 施行基本情報取得
        $sql = "SELECT n_free1 FROM seko_kihon_info WHERE seko_no = :seko_no AND delete_flg = 0";
        $sel = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $sel['n_free1']; // 1:変更あり、null:変更なし
    }
    
    /**
     * 権限区分取得
     *
     * <AUTHOR> Arai
     * @since 2020/12/09
     * @param Msi_Sys_Db $db db
     */
    public function getAuthority($db) {
        $sql = "SELECT outhority_flg FROM tanto_mst WHERE tanto_cd = :tanto_cd AND delete_flg = 0";
        $sel = $db->easySelOne($sql, array('tanto_cd' => $this->_sekoTantoCd));
        return $sel['outhority_flg']; 
    }
    
    /**
     *
     *権限区分合計取得
     *
     * <AUTHOR> Arai
     * @since 2020/12/09
     * @return string 権限区分合計
     */
    protected function getTotalAuthority($uri_den_no) {
        $totalAuthority = '';
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            n_free10
        FROM
            uriage_denpyo
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('uri_den_no' => $uri_den_no));
        if (isset($select) && count($select) > 0) {
            $totalAuthority = $select['n_free10'];
        }
        return $totalAuthority;
    }

}
