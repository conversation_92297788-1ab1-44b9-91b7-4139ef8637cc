/** 施行カレンダー */
var appcal = appcal || {};
$(function () {
    "use strict";

    // リサイズ処理 明細がスクロールがある場合、ヘッダーもスクロールを追加する
    $(window).resize(function () {
        var msi = $("#calendar #msi").get(0);
        var flg = msi.clientWidth === msi.offsetWidth;
        if (flg) {
            $("#calendar ul.date").css("overflow-y", "auto");
        } else {
            $("#calendar ul.date").css("overflow-y", "scroll");
        }
    });
    // 明細ダイアロール閉じる処理
    $('#calendar_dialog .close, #shade').click(function () {
        $('#calendar_dialog, #shade').fadeOut(200);
    });
    // ESC押下時に明細ダイアロール閉じる処理
    $(document).bind("keydown", function (e) {
        if (e.keyCode === 27) {
            $('#calendar_dialog .close').click();
        }
    });
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                s_current_bumon_cd: null, // サイドメニューの選択された部門コード
                date_term: null, // 表示期間
                s_display_term: null, // 表示日数
                date_today: null, // 今日の日付 y/m/d
                s_tgt_day: null, // 表示期間開始 y/m/d
                s_bumon: null // 検索部門
            };
        },
        validation: {
        }
    }); // AppModel

    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#wrapper"),
        events: {
            "click .week li a": "dateChange",
            "click #btn_reload": "refresh",
            "change #division": "refresh",
            "click #btn_today": "showToday"
        },
        bindings: {
            '#header .week .term': 'date_term',
            '#division': {
                observe: 's_bumon',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    $el.select2("val", vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            }
        },
        initialize: function () {
            this.listenTo(appcal.sideCol, 'reset', this.addAllSideCol);
            this.listenTo(appcal.sekoHdCol, 'reset', this.addAllSekoHdCol);
            this.listenTo(appcal.sekoMsiHdCol, 'reset', this.addAllSekoMsiHdCol);
            this.listenTo(appcal.sekoMsiNone, 'reset', this.addAllSekoMsiNone);
            this.listenTo(appcal.sekoNitei, 'reset', this.addAllSekoNiteiCol);
            this.listenTo(appcal.sekoNiteiMsi, 'reset', this.addAllSekoNiteiMsiCol);
            this.render();
        },
        render: function () {
            this.stickit();
            $.msiJqlib.setSelect2Com1($("#division"), ($.extend({data: appcal.initData.bumon_select2}, $.msiJqlib.setSelect2Default2
                    , {multiple: true, placeholder: '対象部門', maximumSelectionSize: 5
                        , formatSelection: function (item) {
                            return $.trim(item.text);
                        }})));
            return this;
        },
        addSideOne: function (m) {
            var v = new SideMenuView({model: m});
            this.$("#side ul").append(v.render().el);
        },
        addAllSideCol: function (collection) {
            this.$("#side ul li").remove();
            collection.each(this.addSideOne, this);
            var m = this.model;
            if (collection.length > 0) {
                var current_bm = collection.where({bumon_cd: m.get('s_current_bumon_cd')}); // 選択されている部門 
                var cm;
                if (current_bm.length === 1) {
                    cm = current_bm[0];
                } else {
                    cm = collection.models[0];
                }
                cm.set("current", 1);
            }
        },
        addSekoHdOne: function (m) {
            var v = new SekoHdView({model: m});
            this.$("#calendar ul.date").append(v.render().el);
        },
        addAllSekoHdCol: function (collection) {
            this.$("#calendar ul.date li.week").remove();
            collection.each(this.addSekoHdOne, this);
        },
        addSekoMsiHdOne: function (m) {
            var v = new SekoMsiView1({model: m});
            this.$("#calendar #msi").append(v.render().el);
        },
        addAllSekoMsiHdCol: function (collection) {
            collection.each(this.addSekoMsiHdOne, this);
        },
        addAllSekoMsiNone: function (collection) {
            this.$("#calendar ul.schedule").remove();
            var v = new SekoMsiView3();
            this.$("#calendar #msi").append(v.render().el);
        },
        addAllSekoNiteiCol: function (collection) {
            var v = new SekoMsiView2();
            this.$("#calendar .schedule").last().append(v.render().el);
        },
        addSekoNiteiMsiOne: function (m) {
            var nitei_kbn = m.get("nitei_kbn");
            var c = "c_def c" + nitei_kbn;
            var v = new SekoNiteiMsiView({model: m, className: c});
            this.$(".ceremonies").last().append(v.render().el);
        },
        addAllSekoNiteiMsiCol: function (collection) {
            collection.each(this.addSekoNiteiMsiOne, this);
        },
        dateChange: function (e) {
            var day = $(e.currentTarget).closest("li").data("day");
            var s_tgt_day = appcal.appModel.get("s_tgt_day");
            s_tgt_day = $.msiJqlib.addDays(s_tgt_day, day);
            appcal.appModel.set("s_tgt_day", s_tgt_day);
            this.refresh();
        },
        showToday: function () {
            appcal.appModel.set("s_tgt_day", null);
            this.refresh();
        },
        refresh: function () {
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/cale/sekocalendar/list',
                data: {
                    s_tgt_day: appcal.appModel.get("s_tgt_day"),
                    s_bumon: appcal.appModel.get("s_bumon"),
                    s_display_term: appcal.appModel.get("s_display_term"),
                    s_current_bumon_cd: appcal.appModel.get("s_current_bumon_cd")
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // データ再設定
                        appcal.resetData(mydata);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        }
    });

    // サイドメニュー明細モデル
    var SideMenuModel = Backbone.Model.extend({
        defaults: function () {
            return {
                bumon_cd: null, // 部門コード
                bumon_nm: null, // 部門名
                count: null, // カウント
                current: 0 // 0:非アクティブ 1:アクティブ 
            };
        }
    });
    // SideMenu明細コレクション
    var SideMenuCollection = Backbone.Collection.extend({
        model: SideMenuModel
    });
    // SideMenu明細ビュー
    var SideMenuView = Backbone.View.extend({
        tagName: 'li',
        tmpl1: _.template($('#tmpl-side-menu').html()),
        events: {
            "click span": "clickSide"
        },
        bindings: {
            '.subtitle': 'bumon_nm',
            '.badge': 'count'
        },
        initialize: function () {
            this.listenTo(this.model, 'change:current', this.changeSide);
        },
        render: function () {
            this.$el.html(this.tmpl1(this.model.toJSON()));
            this.stickit();
            return this;
        },
        clickSide: function (e) {
            if (this.model.get("bumon_cd") === appcal.appModel.get("s_current_bumon_cd")) {
                return;
            }
            this.setCurrent($(e.currentTarget).closest("li"));
            this.setSession();
        },
        changeSide: function () {
            this.setCurrent(this.$el);
        },
        setCurrent: function ($tar) {
            $('#side ul li').removeClass('current');
            $tar.addClass('current');
            appcal.appModel.set("s_current_bumon_cd", this.model.get("bumon_cd"));
            _setSekoMsi();
        },
        setSession: function () {
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/cale/sekocalendar/setsession',
                data: {
                    s_tgt_day: appcal.appModel.get("s_tgt_day"),
                    s_bumon: appcal.appModel.get("s_bumon"),
                    s_display_term: appcal.appModel.get("s_display_term"),
                    s_current_bumon_cd: appcal.appModel.get("s_current_bumon_cd")
                },
                type: 'POST',
                success: function (mydata) {
                }
            });
        }
    });
    // 施行ヘッダーモデル
    var SekoHdModel = Backbone.Model.extend({
        defaults: function () {
            return {
                ymd: null, // 日付
                mm: null, // 月
                dd: null, // 日
                w: null, // 曜日
                rokuyou: null // 六曜 
            };
        }
    });

    // 施行ヘッダー明細コレクション
    var SekoHdCollection = Backbone.Collection.extend({
        model: SekoHdModel
    });
    // 施行ヘッダー明細ビュー
    var SekoHdView = Backbone.View.extend({
        tagName: 'li',
        className: 'week',
        tmpl: _.template($('#tmpl-seko-hd').html()),
        bindings: {
            '.mm': 'mm',
            '.dd': 'dd',
            '.w': 'w',
            '.rokuyou': 'rokuyou'
        },
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl(this.model.toJSON()));
            var ymd = this.model.get("ymd");
            // 六曜設定
            var rokuyou = msiLibKyureki.getRokuyo(ymd);
            this.model.set("rokuyou", rokuyou);
            // 六曜色設定
            if (rokuyou === '友引') {
                this.$el.find(".rokuyou").addClass("tomobiki");
            }
            // 今日の色を設定
            if (appcal.appModel.get("date_today") === ymd) {
                this.$el.addClass("today");
            }
            this.stickit();
            return this;
        }
    });
    // 施行明細ーモデル
    var SekoMsiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                header_kbn: 2, // ヘッダー区分 1:ヘッダー 2:明細 3: データなし
                seko_no: null, // 施行番号
                k_nm: null, // 故人名
                m_nm: null, // 喪主名
                careful_memo: null, // 注意事項
                m_zoku_kbn_nm: null, // 続柄
				uketuke_tanto_nm: null,    // 受付担当者名
                seko_tanto_nm: null, // 施行担当担当者名
                mg_tel: null, // 喪主電話番号
                mg_addr: null, // 喪主住所
                mg_addr1: null, // 喪主住所1
                mg_addr2: null, // 喪主住所2
                biko: null, // 備考
                nitei_kbn: null, // 日程区分
                nitei_kbn_nm: null, // 日程区分名
                nitei_ymd: null, // 日程日付 2015/08/29 15:00
                nitei_ymd_hm: null, // 日程日付（時刻のみ）15:00
                nitei_ymd_ymd: null // 日程日付（日付のみ）2015/08/29
            };
        }
    });

    // 施行明細コレクション
    var SekoMsiCollection = Backbone.Collection.extend({
        model: SekoMsiModel
    });
    // 施行明細ヘッダービュー
    var SekoMsiView1 = Backbone.View.extend({
        tagName: 'ul',
        className: 'schedule on',
        tmpl1: _.template($('#tmpl-seko-msi-1').html()), // 施行明細のヘッダー
        events: {
            "click span.memo": "showDetail",
            "click a.more": "showNew"
        },
        bindings: {
            '.family .seko_no': 'seko_no',
            '.family .souke_nm': 'souke_nm',
            '.family .seko_hoyo_nm': 'seko_hoyo_nm',
            '.family .careful_memo': 'careful_memo',
            '.family .tanto_nm': 'seko_tanto_nm',
			'.family .uketuke_tanto_nm': 'uketuke_tanto_nm',    // 受付担当者名
            '.family .basho_nm': {
                observe: 'basho_nm',
                updateMethod: 'html',
                onGet: function (val) {
                    var value;
                    var nitei = _.where(appcal.dataAll.dataSougi, {seko_no: this.model.get("seko_no")});
                    if (nitei.length === 1) {
                        var basho_kbn = nitei[0].basho_kbn;
                        var basho_nm = nitei[0].basho_nm;
                        if (basho_kbn === '0') {
                            value = '自宅';
                        } else {
                            value = basho_nm;
                        }
                    }
                    if ($.msiJqlib.isNullEx2(value)) {
                        value = "&nbsp";
                    }
                    return value;
                }
            },
            '.family .basho_nm2': {
                observe: 'basho_nm',
                updateMethod: 'html',
                onGet: function (val) {
                    var value;
                    var nitei = _.where(appcal.dataAll.dataHouen, {seko_no: this.model.get("seko_no")});
                    if (nitei.length === 1) {
                        var basho_kbn = nitei[0].basho_kbn;
                        var basho_nm = nitei[0].basho_nm;
                        if (basho_kbn === '0') {
                            value = '自宅';
                        } else {
                            value = basho_nm;
                        }
                    }
                    if ($.msiJqlib.isNullEx2(value)) {
                        value = "&nbsp";
                    }
                    return value;
                }
            },
            '.family .k_nm': 'k_nm',
            '.family .m_nm': 'm_nm',
            '.family .relation': 'm_zoku_kbn_nm',
            '.family .tel': 'mg_tel',
            '.family .address': 'mg_addr'
        },
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl1(this.model.toJSON()));
            this.$(".k_nm").attr("title", this.model.get("seko_no"));
            if (this.model.get("moushi_kbn") === '2') { // 法事
                this.$(".basho_nm2").css('display', 'block'); // 法宴場所
                this.$(".careful_memo").hide();
                this.$(".seko_hoyo_nm").css('display', 'block');
            } else {
                this.$(".basho_nm2").hide();
                this.$(".careful_memo").css('display', 'block');
                this.$(".seko_hoyo_nm").hide();
            }
            this.stickit();
            return this;
        },
        showDetail: function () {
            var cont = this.$(".family").html();
            $('#calendar_dialog .content').html('').html(cont).find("a.more").attr("data-seko_no", this.model.get("seko_no")).attr("data-moushi_kbn", this.model.get("moushi_kbn"));

            // 施行参照処理
            $('#calendar_dialog .content a.more').click(function () {
                var seko_no = $(this).data("seko_no");
                var moushi_kbn = $(this).data("moushi_kbn");
                if ($.msiJqlib.isNullEx2(seko_no)) {
                    return;
                }
                var url = $.msiJqlib.baseUrl() + '/juchu/mitsu/input/sn/' + seko_no;
                if (moushi_kbn == '2') {
                     url = $.msiJqlib.baseUrl() + '/juchu/houji/input/sn/' + seko_no;
                }
                var refreshFunc = function () {
                    console.log('refresh searching...');
                };
                msiLib2.openWinSub(refreshFunc, url);
            });
            $('#calendar_dialog, #shade').fadeIn(200);
            var niteiDtl = new SekoMsiCollection();// 施行日程の明細
            this.listenTo(niteiDtl, 'reset', this.addSekoNiteiMsiCol);
            // ワン施行情報取得処理
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/cale/sekocalendar/oneseko',
                data: {
                    seko_no: this.model.get("seko_no")
                },
                type: 'POST',
                success: function (data) {
                    niteiDtl.reset(data);
                }
            });
        },
//        nl2br: function (str) {
//            return str.replace(/[\n\r]/g, "<br />");
//        },
        addNiteiMsiOne: function (m) {
            var v = new SekoNiteiMsiView2({model: m});
            $('#calendar_dialog .content .detail').append(v.render().el);
        },
        addSekoNiteiMsiCol: function (collection) {
            collection.each(this.addNiteiMsiOne, this);
        }
    });
    // 施行明細ビュー
    var SekoMsiView2 = Backbone.View.extend({
        tagName: 'li',
        tmpl2: _.template($('#tmpl-seko-msi-2').html()), // 施行明細の明細 w1-w7
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl2());
            return this;
        }
    });
    // 施行明細ヘッダービュー
    var SekoMsiView3 = Backbone.View.extend({
        tagName: 'ul',
        className: 'schedule on',
        tmpl3: _.template($('#tmpl-seko-msi-3').html()), // 施行明細データなし
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl3());
            return this;
        }
    });
    // 施行日程明細ビュー
    var SekoNiteiMsiView = Backbone.View.extend({
        tagName: 'li',
        tmpl: _.template($('#tmpl-seko-nitei-msi').html()), // 施行明細の施行日程明細
        events: {
        },
        bindings: {
            '.time': 'nitei_ymd_hm',
            '.ceremony': 'nitei_kbn_nm'
        },
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl(this.model.toJSON()));
            var basho_nm = this.model.get("basho_nm");
            if (this.model.get("basho_kbn") === '0') {
                basho_nm = '自宅';
            }
            this.$el.attr("title", basho_nm);
            this.stickit();
            return this;
        }
    });
    // ワン施行日程明細ビュー
    var SekoNiteiMsiView2 = Backbone.View.extend({
        tagName: 'li',
        tmpl: _.template($('#tmpl-seko-nitei-msi2').html()),
        bindings: {
            '.time': 'nitei_ymd_mdhm',
            '.ceremony': 'nitei_kbn_nm',
            '.place': {
                observe: 'basho_nm',
                onGet: function (val) {
                    if (this.model.get("basho_kbn") === '0') {
                        return '自宅';
                    } else {
                        return val;
                    }
                }
            }
        },
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl(this.model.toJSON()));
            this.stickit();
            return this;
        }
    });
    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-json').text()));
        appcal.initData = data;
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    // 初期化処理
    appcal.sideCol = new SideMenuCollection(); // サイドメニュー
    appcal.sekoHdCol = new SekoHdCollection(); // 施行ヘッダー w1-w7
    appcal.sekoMsiHdCol = new SekoMsiCollection();// 施行明細のヘッダー（一番左の施行情報）
    appcal.sekoMsiNone = new SekoMsiCollection();// 施行明細のデータなし
    appcal.sekoNitei = new SekoMsiCollection();// 施行日程
    appcal.sekoNiteiMsi = new SekoMsiCollection();// 施行日程の明細(亡日～初七日)
    appcal.appModel = new AppModel();
    appcal.appModel.set(data.dataApp);
    appcal.appView = new AppView({model: appcal.appModel});
    // データ設定処理
    appcal.resetData = function (data) {
        // データ退避
        appcal.dataAll = data;
        // APPデータを設定する
        appcal.appModel.set(data.dataApp);
        // 施行ヘッダー設定処理
        _setSekoHeader(data);
        // SideMenu設定処理
        _setSidemenu(data);
        // 最終更新日時
        $("#btn_reload").attr("title", "最終更新：" + data.dataApp.date_refresh);
        $("#wrapper").show();
    };
    // SideMenu設定処理
    var _setSidemenu = function (data) {
        var dataSide = [];
        var bms = _.indexBy(data.dataBumon, 'bumon_cd'); // 部門毎
        _.each(bms, function (v, k) {
            if(k == '00000'){
                var bm = data.dataAll;
            }else{
                var bm = _.where(data.dataAll, {bumon_cd: k});
            }            
            var sekos = _.indexBy(bm, 'seko_no'); // 施行毎
            var side = {bumon_cd: k, bumon_nm: v.bumon_lnm, count: _.keys(sekos).length};
            dataSide.push(side);
        });
        appcal.sideCol.reset(dataSide);
    };
    // 施行ヘッダー設定処理
    var _setSekoHeader = function (data) {
        appcal.sekoHdCol.reset(data.dataSekoHd);
    };
    // 施行明細設定処理
    var _setSekoMsi = function (data) {
        var cur_bumon = appcal.appModel.get("s_current_bumon_cd");
        if ($.msiJqlib.isNullEx2(data)) {
            data = appcal.dataAll;  // 既存データ
        }
        if(cur_bumon == '00000'){
            var bm = data.dataAll;
        }else{
            var bm = _.where(data.dataAll, {bumon_cd: cur_bumon});
        }
        var sekos = _.indexBy(bm, 'seko_no'); // 施行毎
        if (_.keys(sekos).length === 0) {
            appcal.sekoMsiNone.reset(); // 明細無し（スケジュール無し）
        } else {
            $("#calendar ul.schedule").remove();
            // 施行毎にループ
            _.each(sekos, function (seko, k) {
                var dataMsiHd = []; // 明細ヘッダー
                var nitei = _.where(bm, {seko_no: k});
                var h = {}; // 明細ヘッダー
                if (nitei.length > 0) {
                    h = nitei[0];
                }
                dataMsiHd.push(h);
                appcal.sekoMsiHdCol.reset(dataMsiHd);
                var nitei_from = appcal.appModel.get("s_tgt_day");
                // 日程明細設定　一週間分ループ処理
                _(appcal.appModel.get("s_display_term")).times(function (n) {
                    var niteiMsi = _.where(nitei, {nitei_ymd2: $.msiJqlib.addDays(nitei_from, n)}); // 日程明細
                    appcal.sekoNitei.reset();
                    appcal.sekoNiteiMsi.reset(niteiMsi);
                });
            });
        }
        var disply_one_day = appcal.appModel.get("s_display_term") === '1';
        if (disply_one_day) {
            $("#calendar ul li").toggleClass("oneday", true);
        } else {
            $("#calendar ul li").toggleClass("oneday", false);
        }
        // リサイズ発火
        setTimeout('$(window).trigger("resize")', 10);
    };
    appcal.appView.refresh();
    // 5分間隔で自動更新
    var interval = 1000 * 60 * 5;
    var setInterval = function () {
        setTimeout(setInterval, interval);
        appcal.appView.refresh();
    };
//    setInterval();
});