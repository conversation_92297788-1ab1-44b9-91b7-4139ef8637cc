<?php

/**
 * DataMapper_Pdf1202
 *
 * PDF出力 仕入伝票入力チェックリスト データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Kayo
 * @since      2014/09/25
 * @filesource 
 */

/**
 * PDF出力 仕入伝票入力チェックリスト データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Kayo
 * @since      2014/05/26
 */
class DataMapper_Pdf1202 extends DataMapper_Abstract {

     // 帳票区分
	const REPKBN1_MSI   = 0; // 明細
	const REPKBN1_BUMON = 1; // 部門別
	const REPKBN1_BUMON_SIIRE = 2; // 部門別仕入先別
	const REPKBN1_SIIRE = 2; // 仕入先別
   // 帳票区分
	const REPKBN_KAIKAKE = 0; // 買掛
	const REPKBN_GENKIN  = 1; // 現仕
	const REPKBN_ALL     = 9; // 全て
    // 出力順ID
	const ORDERKBN_DENPYONO = 0;    // 伝票番号
	const ORDERKBN_SIIRE_CD = 1;    // 仕入先コード
    // 更新区分
	const UPDATE_KBN_ADD =  0;    // 新規
	const UPDATE_KBN_UPD =  1;    // 更新
	const UPDATE_KBN_ALL =  9;    // 全て
    // 削除データ
	const DELETE_KBN_NO = 0;    // 印刷なし
	const DELETE_KBN_YES = 1;   // 印刷あり
	const DELETE_KBN_ONRY = 2;   // 削除データのみ
    // 計上区分
	const KEIJO_KBN_KAKE  = 0;     // 売掛部門
	const KEIJO_KBN_URI   = 1;     // 売上部門
        
    // 仕入先対象区分
	const SIIRE_TARGET_KBN_IN       = 0;    // 仕入先を対象
	const SIIRE_TARGET_KBN_NOT_IN   = 1;    // 仕入先を除外

    /**
     * PDF出力 仕入伝票入力チェックリスト 取得(明細)
     * <AUTHOR> kayo
     * @since      2014/09/25
     * @param Msi_Sys_Db $db
	 * @param string $ymd_st	対象年年月日（自）
	 * @param string $ymd_ed	対象年年月日（至）
	 * @param string $syori_st_ymd 処理日（自）
     * @param string $syori_ed_ymd 処理日（至）
     * @param array $bum_ary 部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
	 * @param string $rep_kbn1	帳票区分
	 * @param string $rep_kbn2		支払区分
	 * @param string $order_kbn2	出力順
     * @param string $delete_kbn 削除データ
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param string $kaisya_cd 会社コード
     * @param type $csv true:CSV出力用
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find1($db, $ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $bum_ary, $siire_target_kbn
            , $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $rep_kbn1, $rep_kbn2, $order_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd, $csv = false) {

        $where = self::getWhere1($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary, $siire_target_kbn
                , $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd);
        $order = self::getOrder1($order_kbn, $keijo_kbn);
        // 計上区分による処理
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
            // 売掛部門
            $sel_item = null;
            $sel_join = '
            '; 
        }  else {
            // 売上部門
            $sel_item = '
                ,CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END                                    AS	u_bumon_cd			-- 売上部門コード
                ,bum_u.bumon_lnm                        AS  u_bumon_nm          -- 部門名
                ';
            $sel_join = '
                LEFT JOIN juchu_denpyo_msi jm
                   ON   shi.jc_denpyo_no   = jm.denpyo_no
                   AND  shi.seko_no        = jm.seko_no
                   AND  shi.jc_msi_no      = jm.msi_no  
                   AND 0                   = jm.delete_flg
                LEFT JOIN uriage_denpyo_msi um
                   ON   shi.jc_denpyo_no    = um.uri_den_no 
                   AND  shi.seko_no         = um.seko_no 
                   AND  shi.jc_msi_no       = um.msi_no  
                   AND 0                    = um.delete_flg
                LEFT JOIN bumon_mst bum_u 
                ON  CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END                        = bum_u.bumon_cd
                AND 0                       = bum_u.delete_flg
            '; 
        }   
        $where_del = null;
        if ($delete_kbn == self::DELETE_KBN_NO) {
            $where_del = ' AND 0 = m.delete_flg';
        }            
        $select = $db->easySelect(<<< END_OF_SQL
SELECT
	h.siire_den_no			AS	h_siire_den_no					-- 仕入伝票№
   ,COALESCE(h.denpyo_no,m.hachu_denpyo_no)	AS	h_denpyo_no		-- 発注伝票NO
   ,shi.seko_no				AS	seko_no							-- 施行番号				
   ,TO_CHAR(h.siire_ymd,'YYYY/MM/DD')	AS	h_siire_ymd			-- 仕入日
   ,h.data_kbn				AS	h_data_kbn						-- データ区分
   ,CASE	WHEN h.data_kbn = 1	THEN
			'(葬)'
		WHEN h.data_kbn = 2	THEN
			'(法)'
		WHEN h.data_kbn = 3	THEN
		 	'(単)'
		WHEN h.data_kbn = 4	THEN
			'(別)'
		WHEN h.data_kbn = 5	THEN
            '(生)'
        ELSE null   END     AS	data_kbn_nm
        ,h.bumon_cd			AS	h_bumon_cd						-- 部門コード
        ,bum.bumon_lnm      AS  h_bumon_nm                      -- 部門名
$sel_item            
        ,CASE WHEN h.data_kbn = 3 THEN
            h.tanto_cd
        ELSE
            COALESCE(sk.seko_tanto_cd,h.tanto_cd)    
        END h_tanto_cd -- 担当者コード
        ,CASE WHEN h.data_kbn = 3 THEN
            tan.tanto_nm
        ELSE
            COALESCE(tan2.tanto_nm,tan.tanto_nm)    
        END h_tanto_nm -- 担当者名
	,CASE WHEN sk.souke_nm is null THEN
		NULL
	ELSE			
		sk.souke_nm	|| ' 家'		
	END                                             AS	souke_nm	-- 葬家
    ,CASE WHEN h.data_kbn = 4 THEN
        ud2.sekyu_nm
     ELSE
        COALESCE(ud.sekyu_nm,jd.sekyu_nm)
     END                    AS	h_sekyu_nm                          -- 請求先名
	,h.siire_cd				AS	h_siire_cd							-- 仕入先コード
	,h.siire_nm				AS	h_siire_nm							-- 仕入先名
	,TO_CHAR(h.sime_ymd,'YYYY/MM/DD')         AS	h_sime_ymd		-- 支払締日
	,h.harai_kbn			AS	h_harai_kbn                         -- 支払区分
	,cm1.kbn_value_lnm      AS	h_harai_kbn_nm                      -- 支払区分名      
	,TO_CHAR(h.harai_yotei_ymd,'YYYY/MM/DD')	AS	h_harai_yotei_ymd	-- 支払予定日
	,h.soko_cd				AS	h_soko_cd                           -- 倉庫コード
	,h.nonyu_cd				AS	h_nonyu_cd                          -- 納入先コード
	,h.nonyu_nm				AS	h_nonyu_nm                          -- 納入先名
	,h.nonyu_knm			AS	h_nonyu_knm                         -- 納入先名カナ
	,h.nonyu_yubin_no		AS	h_nonyu_yubin_no                    -- 納入先郵便番号
	,h.nonyu_addr1			AS	h_nonyu_addr1                       -- 納入先住所1
	,h.nonyu_addr2			AS	h_nonyu_addr2                       -- 納入先住所2
	,h.nonyu_tel				AS	h_nonyu_tel                     -- 納入先電話番号
	,h.nonyu_fax				AS	h_nonyu_fax                     -- 納入先FAX
	,TO_CHAR(h.nonyu_dt,'YYYY/MM/DD')  AS	h_nonyu_dt              -- 納入日
	,CASE WHEN h.delete_flg = 0 THEN 
        h.siire_prc_sum + h.siire_nebk_sum
     ELSE 0 END                 AS	h_siire_prc_sum                 -- 仕入金額合計
	,CASE WHEN h.delete_flg = 0 THEN 
        h.siire_nebk_sum
     ELSE 0 END                 AS	h_siire_nebk_sum                -- 仕入値引合計
	,CASE WHEN h.delete_flg = 0 THEN
        h.szei_katax_taisho_prc
     ELSE 0 END                 AS	h_szei_katax_taisho_prc                     -- 外税課税対象額
	,CASE WHEN h.delete_flg = 0 THEN
        h.uzei_katax_taisho_prc
     ELSE 0 END                 AS	h_uzei_katax_taisho_prc                     -- 内税課税対象額
	,CASE WHEN h.delete_flg = 0 THEN    
        h.hitax_katax_taisho_prc
     ELSE 0 END       AS	h_hitax_katax_taisho_prc                            -- 非税課税対象額
	,h.tax_kbn				AS	h_tax_kbn                                       -- 税区分
	,CASE WHEN h.delete_flg = 0 THEN    
        NULL
     ELSE '(削)' END        AS	delete_kbn                                      -- 削除区分
	,cm2.kbn_value_lnm || ':  ' || zm.zei_rtu || '%'  AS	h_tax_kbn_nm        -- 税区分名      
	,h.zei_cd				AS	h_zei_cd                                        -- 消費税コード
	,h.out_zei_prc			AS	h_out_zei_prc                                   -- 外税消費税額
	,h.in_zei_prc			AS	h_in_zei_prc                                    -- 内税消費税額
	,h.denpyo_biko1			AS	h_denpyo_biko1                                  -- 伝票備考１
	,h.denpyo_biko2			AS	h_denpyo_biko2                                  -- 伝票備考２
	,h.delivery_kbn			AS	h_delivery_kbn                                  -- 納品場所区分
	,m.msi_no 				AS	m_msi_no                                        -- 仕入明細№
	,m.disp_no 				AS	m_disp_no                                       -- 表示順
	,m.denpyo_kbn			AS	m_denpyo_kbn                                    -- 伝票区分
	,CASE WHEN m.delete_flg = 0 THEN 
        cm3.kbn_value_lnm
     ELSE '(削)'    END     AS	m_denpyo_kbn_nm                                 -- 伝票区分名      
	,m.dai_bunrui_cd 		AS	m_dai_bunrui_cd                                 -- 大分類コード
	,sbm.dai_bunrui_nm      AS  m_dai_bunrui_nm                                 -- 大分類名              
	,m.chu_bunrui_cd		AS	m_chu_bunrui_cd                                 -- 中分類コード
	,cbm.chu_bunrui_nm      AS  m_chu_bunrui_nm                                 -- 中分類名              
	,m.shohin_kbn			AS	m_shohin_kbn                                    -- 商品区分
	,skm.shohin_kbn_nm      AS  m_shohin_kbn_nm                                 -- 商品区分名        
	,m.shohin_cd			AS	m_shohin_cd                                     -- 商品コード
	,m.shohin_nm			AS	m_shohin_nm                                     -- 商品名
	,m.shohin_tkiyo_nm		AS	m_shohin_tkiyo_nm                               -- 商品摘要名
	,m.siire_suryo          AS	m_siire_suryo                                   -- 数量
	,m.tani_cd				AS	m_tani_cd                                       -- 単位コード
	,tani.tani_nm           AS  m_tani_nm                                       -- 単位名              
	,CASE WHEN m.delete_flg = 0 THEN 
        m.siire_tnk
     ELSE 0 END             AS	m_siire_tnk                                     -- 仕入単価
	,CASE WHEN m.delete_flg = 0 THEN
        m.siire_prc
     ELSE 0 END             AS	m_siire_prc                                     -- 仕入金額
	,CASE WHEN m.delete_flg = 0 THEN
        m.siire_prc + m.nebiki_prc
     ELSE 0 END             AS	m_siire_nebiki_prc                              -- 仕入金額（値引き込）
	,CASE WHEN m.delete_flg = 0 THEN
        m.nebiki_prc
     ELSE 0 END                 AS	m_nebiki_prc                                -- 値引額
	,m.nonyu_ymd				AS	m_nonyu_ymd                                 -- 納品（納入）日時
	,m.nyuka_ymd				AS	m_nyuka_ymd                                 -- 入荷日
	,m.msi_biko1				AS	m_msi_biko1                                 -- 明細備考１
	,m.msi_biko2				AS	m_msi_biko2                                 -- 明細備考２
	,m.zei_kbn                  AS	m_zei_kbn                                   -- 消費税区分
	,m.zei_cd					AS	m_zei_cd                                    -- 消費税コード
	,CASE WHEN m.delete_flg = 0 THEN    
        m.out_zei_prc
     ELSE 0 END                 AS	m_out_zei_prc                               -- 行外税消費税額
	,CASE WHEN m.delete_flg = 0 THEN
        m.out_zei_prc
     ELSE 0 END                 AS	m_out_zei_prc2                              -- 行外税消費税額
	,CASE WHEN m.delete_flg = 0 THEN
        m.in_zei_prc
     ELSE 0 END                 AS	m_in_zei_prc                                -- 行内税消費税額
	,CASE WHEN m.delete_flg = 0 THEN
        m.siire_prc + m.nebiki_prc + m.out_zei_prc + m.out_zei_hasu_prc
     ELSE 0 END                 AS m_siire_zei_prc  -- 仕入金額(税込)
	,CASE WHEN m.delete_flg = 0 THEN
        m.out_zei_hasu_prc
     ELSE 0 END                 AS  m_out_zei_hasu_prc                          -- 消費税端数
	,m.soko_cd                  AS	m_soko_cd                                   -- 倉庫コード
	,sokm.soko_lnm              AS  m_soko_nm                                   -- 倉庫名            
	,m.kaku_tanto_cd			AS	m_kaku_tanto_cd                             -- 入荷確認者コード
	,m.kaku_dt                  AS	m_kaku_dt                                   -- 入荷確認日
	,CASE WHEN m.delete_flg = 0 THEN
        m.jyodai_tnk
     ELSE 0 END                 AS	m_jyodai_tnk                                -- 上代単価
	,CASE WHEN m.delete_flg = 0 THEN
        m.jyodai_prc
     ELSE 0 END                 AS	m_jyodai_prc                                -- 上代金額
	,m.siire_kamoku_cd          AS  m_kamoku_cd                                 -- 仕入科目コード     
	,km.kamoku_nm               AS  m_kamoku_nm                                 -- 仕入科目名
	,sirm.com_siire_cd          AS  com_siire_cd                                -- 共通仕入先コード
    ,CASE WHEN rd.siire_den_no IS NULL THEN
        NULL
     ELSE
        '(済)'
     END                        AS  rendo_mark      -- 連動済
	,rh.dendo_denno				AS	dendo_denno		-- 連動伝票番号			 
FROM    siire_denpyo h
LEFT JOIN siire_denpyo_msi m
    ON h.siire_den_no       = m.siire_den_no
    $where_del
LEFT JOIN siire_rendo_data rh
    ON  m.siire_den_no      = rh.siire_den_no
    AND 0                   = rh.delete_flg
LEFT JOIN siire_rendo_data_msi rd
    ON  m.siire_den_no      = rd.siire_den_no
    AND m.msi_no            = rd.msi_no
    AND 0                   = rd.delete_flg
LEFT JOIN siire_mst sirm
    ON  h.siire_cd = sirm.siire_cd
    AND h.kaisya_cd= sirm.kaisya_cd     -- 2016/11/18 ADD Kayo            
    AND 0          = sirm.delete_flg             
LEFT JOIN seko_hachu_info shi
    ON  m.hachu_denpyo_no   = shi.hc_denpyo_no
    AND m.hachu_msi_no      = shi.hachu_no
    AND 0                   = shi.delete_flg
LEFT JOIN seko_kihon_info sk 
	ON shi.seko_no	=	sk.seko_no 
	AND 0			=	sk.delete_flg
LEFT JOIN juchu_denpyo jd
   ON   shi.jc_denpyo_no   = jd.denpyo_no
   AND  shi.seko_no        = jd.seko_no 
   AND  shi.data_kbn       = jd.data_kbn 
   AND 0                   = jd.delete_flg
LEFT JOIN uriage_denpyo ud
   ON   shi.jc_denpyo_no    = ud.uri_den_no 
   AND  shi.seko_no         = ud.seko_no 
   AND  shi.data_kbn        = ud.data_kbn 
   AND 0                    = ud.delete_flg
LEFT JOIN URIAGE_DENPYO ud2
   ON  shi.seko_no         = ud2.seko_no
   AND shi.data_kbn        = ud2.data_kbn
   AND shi.jc_denpyo_no    = ud2.denpyo_no
   AND 0                   = ud2.delete_flg
LEFT JOIN code_nm_mst cm1
   ON   '1510'              = cm1.code_kbn
   AND  h.harai_kbn         = cm1.kbn_value_cd_num 
   AND  0                   = cm1.delete_flg
LEFT JOIN code_nm_mst cm2
   ON   '0150'              = cm2.code_kbn
   AND   h.tax_kbn          = cm2.kbn_value_cd_num 
   AND 0                    = cm2.delete_flg
LEFT JOIN code_nm_mst cm3
   ON   '0630'              = cm3.code_kbn
   AND   m.denpyo_kbn       = cm3.kbn_value_cd_num 
   AND 0                    = cm3.delete_flg
LEFT JOIN shohin_dai_bunrui_mst sbm
    ON  m.dai_bunrui_cd = sbm.dai_bunrui_cd
    AND 0               = sbm.delete_flg            
LEFT JOIN shohin_chu_bunrui_mst cbm
    ON  m.chu_bunrui_cd = cbm.chu_bunrui_cd
    AND 0               = cbm.delete_flg            
LEFT JOIN shohin_kbn_mst skm
    ON  m.shohin_kbn    = skm.shohin_kbn
    AND 0               = skm.delete_flg
LEFT JOIN zei_mst   zm
    ON h.zei_cd     = zm.zei_cd
    AND 0           = zm.delete_flg              
	AND zm.kaisya_cd	= '$kaisya_cd' -- 2017/06/01 ADD Otake
LEFT JOIN tani_mst tani 
    ON m.tani_cd   = tani.tani_cd
    AND 0           = tani.delete_flg
LEFT JOIN kamoku_mst    km                
    ON 2                    = km.kamoku_kbn
    AND m.siire_kamoku_cd   = km.kamoku_cd         
    AND 0                   = km.delete_flg
	AND km.kaisya_cd	= '$kaisya_cd' -- 2019/03/06_ADD_Mogi
LEFT JOIN soko_mst    sokm                
    ON  m.soko_cd           = sokm.soko_cd
    AND 0                   = sokm.delete_flg
LEFT JOIN bumon_mst bum 
ON h.bumon_cd   = bum.bumon_cd
AND h.kaisya_cd = sirm.kaisya_cd     -- 2016/11/18 ADD Kayo            
AND 0           = bum.delete_flg
$sel_join
LEFT JOIN tanto_mst tan 
    ON h.tanto_cd   = tan.tanto_cd 
    AND 0           = tan.delete_flg
LEFT JOIN tanto_mst tan2 
    ON sk.seko_tanto_cd   = tan2.tanto_cd 
    AND 0           = tan2.delete_flg
$where
$order
END_OF_SQL
        );
        return $select;
    }

    /**
     * WHERE句を取得
     * <AUTHOR> Kayo
     * @since      2014/09/25
     * @version 2015/06/22 施行発注管理情報に紐づかないデータは対象外に修正
     * @param string $ymd_st    対象期間（自）
     * @param string $ymd_ed    対象期間（至）
	 * @param string $syori_st_ymd 処理日（自）
     * @param string $syori_ed_ymd 処理日（至）
	 * @param string $rep_kbn1	帳票区分
	 * @param string $rep_kbn2 支払区分
     * @param string $bum_ary  部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
     * @param string $delete_kbn 削除データ
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param string $kaisya_cd 会社コード
     * @return string WHERE句
     */
    private static function getWhere1($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary, $siire_target_kbn
            , $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd) {
        $where = null;
        // 期間
        if (isset($ymd_st) && isset($ymd_ed)) {
            $where_ymd = "to_char(h.siire_ymd,'YYYY/MM/DD') BETWEEN '".$ymd_st."' AND '".$ymd_ed."'";
        } else if (isset($ymd_st)) {
            $where_ymd = "to_char(h.siire_ymd,'YYYY/MM/DD') >= '".$ymd_st."'";
        } else if (isset($ymd_ed)) {
            $where_ymd = "to_char(h.siire_ymd,'YYYY/MM/DD') <= '".$ymd_ed."'";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }
        // 処理日
        if (isset($syori_st_ymd) && isset($syori_ed_ymd)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') BETWEEN '".$syori_st_ymd."' AND '".$syori_ed_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._mod_ts,'YYYY/MM/DD') BETWEEN '".$syori_st_ymd."' AND '".$syori_ed_ymd."')";
        } else if (isset($ymd_st)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') >= '".$syori_st_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._cre_ts,'YYYY/MM/DD') >= '".$syori_st_ymd."')";
        } else if (isset($ymd_ed)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') <= '".$syori_ed_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._cre_ts,'YYYY/MM/DD') <= '".$syori_ed_ymd."')";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }
        // 部門
        if (isset($bum_ary) && count($bum_ary) > 0) {
            $ary = array();
            foreach ($bum_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "h.bumon_cd IN (" . implode(',', $ary) . ")";
        }
        
        if ($keijo_kbn !=  self::KEIJO_KBN_KAKE)    {
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= 'h.bumon_cd <> CASE WHEN h.data_kbn = 3 THEN
               h.bumon_cd
            ELSE   
               COALESCE(um.bumon_cd,jm.bumon_cd)
            END';
        }
        
        // 商品コード
        if (isset($shohin_ary) && count($shohin_ary) > 0) {
            $ary = array();
            foreach ($shohin_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "m.shohin_cd IN (" . implode(',', $ary) . ")";
        }
        // 仕入先コード
        if (isset($siire_ary) && count($siire_ary) > 0) {
            $ary = array();
            foreach ($siire_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "h.siire_cd ". self::getOpeBySiireTargetKbn($siire_target_kbn) ." (" . implode(',', $ary) . ")";
        }
		// 葬家名
		if (strlen($soke_nm) > 0) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= "sk.souke_nm like '%" . $soke_nm . "%'";
		}
		// 請求先名
		if (strlen($seikyu_nm) > 0) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= " (CASE WHEN h.data_kbn = 4 THEN
                            ud2.sekyu_nm
                        ELSE
                            coalesce(ud.sekyu_nm,jd.sekyu_nm)
                        END) like '%" . $seikyu_nm . "%'";
		}
        // 支払区分
		if ($rep_kbn2 != self::REPKBN_ALL) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= 'h.harai_kbn = ' . $rep_kbn2;
		}
        // 更新区分
		if ($update_kbn != self::UPDATE_KBN_ALL) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= 'CASE WHEN rd.siire_den_no IS NULL THEN 0 --未連動
                       ELSE 1 END = ' . $update_kbn;
		}
        if ($rep_kbn1 == self::REPKBN1_MSI) {
            if ($delete_kbn == self::DELETE_KBN_NO) {
                if (isset($where)) {
                    $where .= ' AND ';
                }
                // 2015/06/22 UPD Kayo
                $where .= 'h.delete_flg = 0
                    AND ((h.data_kbn <> 3 AND shi.seko_no is not null)
                    OR   h.data_kbn = 3) 
                    ';
            }
        } else {
            if (isset($where)) {
                $where .= ' AND ';
            }
            // 2015/06/22 UPD Kayo
            $where .= 'h.delete_flg = 0
                    AND ((h.data_kbn <> 3 AND shi.seko_no is not null)
                    OR   h.data_kbn = 3) 
                    ';
        }    
        $where .= " AND h.kaisya_cd = '" . $kaisya_cd . "'"; // 2016/11/18 ADD Kayo

        return 'WHERE ' . $where;
    }

    /**
     * ORDER句を取得
     * <AUTHOR> Kayo
     * @since      2014/09/25
	 * @param string $order_kbn		出力順
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @return string WORDER句
     */
    private static function getOrder1($order_kbn, $keijo_kbn) {
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
             // 売掛部門
             if ($order_kbn == self::ORDERKBN_DENPYONO) {
                 $order = 'ORDER BY h.bumon_cd, h.data_kbn, h.siire_den_no, m.msi_no';
             } else {
                 $order = 'ORDER BY h.bumon_cd,h.siire_cd, h.siire_ymd, h.data_kbn, h.siire_den_no, m.msi_no';
             }    
        }  else {
             // 売上部門
             if ($order_kbn == self::ORDERKBN_DENPYONO) {
                 $order = 'ORDER BY h.bumon_cd,
                 CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END, h.data_kbn, h.siire_den_no, m.msi_no';
             } else {
                 $order = 'ORDER BY h.bumon_cd,
                 CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END,h.siire_cd, h.siire_ymd, h.data_kbn, h.siire_den_no, m.msi_no';
             }    
        }
        return $order;
    }
    
    /**
     * PDF出力 仕入伝票入力チェックリスト 取得(部門別)
     * <AUTHOR> kayo
     * @since      2014/09/25
     * @param Msi_Sys_Db $db
	 * @param string $ymd_st	対象年年月日（自）
	 * @param string $ymd_ed	対象年年月日（至）
	 * @param string $syori_st_ymd 処理日（自）
     * @param string $syori_ed_ymd 処理日（至）
     * @param type $bum_ary 部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
	 * @param string $rep_kbn1	帳票区分
	 * @param string $rep_kbn2	支払区分
	 * @param string $order_kbn 出力順
     * @param string $delete_kbn 削除データ
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param type $csv true:CSV出力用
     * @param string $kaisya_cd 会社コード
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find2($db, $ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $bum_ary
			, $siire_target_kbn, $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $rep_kbn1, $rep_kbn2, $order_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd, $csv = false) {

        $where = self::getWhere2($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary
                , $siire_target_kbn, $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd);
        $order = self::getOrder2($order_kbn, $keijo_kbn);
        // 計上区分による処理
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
            // 売掛部門
            $sel_item = null;
            $sel_join = null; 
            $sel_group_by = ' GROUP BY h.bumon_cd, bum.bumon_lnm';                 
        }  else {
            // 売上部門
            $sel_item = '
                ,CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END                                    AS	u_bumon_cd			-- 売上部門コード
                ,bumu.bumon_lnm                         AS  u_bumon_nm          -- 部門名
            ';
            $sel_join = '
                LEFT JOIN juchu_denpyo_msi jm
                   ON   shi.jc_denpyo_no   = jm.denpyo_no
                   AND  shi.seko_no        = jm.seko_no
                   AND  shi.jc_msi_no      = jm.msi_no  
                   AND 0                   = jm.delete_flg
                LEFT JOIN uriage_denpyo_msi um
                   ON   shi.jc_denpyo_no    = um.uri_den_no 
                   AND  shi.seko_no         = um.seko_no 
                   AND  shi.jc_msi_no       = um.msi_no  
                   AND 0                    = um.delete_flg
                LEFT JOIN bumon_mst bumu 
                ON  CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END                        = bumu.bumon_cd
                AND h.kaisya_cd             = bumu.kaisya_cd     -- 2016/11/18 ADD Kayo            
                AND 0                       = bumu.delete_flg
            '; 
            $sel_group_by = ' 
                GROUP BY h.bumon_cd, bum.bumon_lnm,
                CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                END  , bumu.bumon_lnm';                 
        }   

        $select = $db->easySelect(<<< END_OF_SQL
SELECT
     h.bumon_cd				AS	h_bumon_cd						-- 部門コード
    ,bum.bumon_lnm          AS  h_bumon_nm                      -- 部門名
$sel_item           
	,sum(m.siire_prc)               AS	m_siire_prc                 -- 仕入金額
	,sum(m.siire_prc + m.nebiki_prc)	AS	m_siire_nebiki_prc      -- 仕入金額（値引き込）
	,sum(m.nebiki_prc)				AS	m_nebiki_prc                -- 値引額
	,sum(m.out_zei_prc)             AS	m_out_zei_prc               -- 行外税消費税額
	,sum(m.out_zei_prc)             AS	m_out_zei_prc2              -- 行外税消費税額
	,sum(m.in_zei_prc)				AS	m_in_zei_prc                -- 行内税消費税額
	,sum(m.siire_prc + m.nebiki_prc + m.out_zei_prc + m.out_zei_hasu_prc)  AS m_siire_zei_prc  -- 仕入金額(税込)
	,sum(m.out_zei_hasu_prc)        AS  m_out_zei_hasu_prc          -- 消費税端数
FROM    siire_denpyo h
LEFT JOIN siire_denpyo_msi m
    ON h.siire_den_no       = m.siire_den_no
    AND 0                   = m.delete_flg
LEFT JOIN siire_rendo_data_msi rd
    ON  m.siire_den_no      = rd.siire_den_no
    AND m.msi_no            = rd.msi_no
    AND 0                   = rd.delete_flg
LEFT JOIN seko_hachu_info shi
    ON  m.hachu_denpyo_no   = shi.hc_denpyo_no
    AND m.hachu_msi_no      = shi.hachu_no
    AND 0                   = shi.delete_flg
LEFT JOIN seko_kihon_info sk 
	ON shi.seko_no	=	sk.seko_no 
	AND 0			=	sk.delete_flg
LEFT JOIN juchu_denpyo jd
   ON   shi.jc_denpyo_no   = jd.denpyo_no
   AND  shi.seko_no        = jd.seko_no 
   AND  shi.data_kbn       = jd.data_kbn 
   AND 0                   = jd.delete_flg
LEFT JOIN uriage_denpyo ud
   ON   shi.jc_denpyo_no    = ud.uri_den_no 
   AND  shi.seko_no         = ud.seko_no 
   AND  shi.data_kbn        = ud.data_kbn 
   AND 0                    = ud.delete_flg
LEFT JOIN URIAGE_DENPYO ud2
   ON  shi.seko_no         = ud2.seko_no
   AND shi.data_kbn        = ud2.data_kbn
   AND shi.jc_denpyo_no    = ud2.denpyo_no
   AND 0                   = ud2.delete_flg
LEFT JOIN bumon_mst bum 
    ON h.bumon_cd   = bum.bumon_cd
    AND h.kaisya_cd = bum.kaisya_cd     -- 2016/11/18 ADD Kayo            
    AND 0           = bum.delete_flg
$sel_join
$where
$sel_group_by                 
$order
END_OF_SQL
        );
        return $select;
    }

    /**
     * WHERE句を取得
     * <AUTHOR> Kayo
     * @since      2014/09/25
     * @version 2015/06/22 施行発注管理情報に紐づかないデータは対象外に修正
     * @param string $ymd_st
     * @param string $ymd_ed
	 * @param string $rep_kbn1	帳票区分
	 * @param string $rep_kbn2 支払区分
     * @param string $bum_ary 部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param string $kaisya_cd 会社コード
     * @return string WHERE句
     */
    private static function getWhere2($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary
            , $siire_target_kbn, $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd) {
        $where = null;
        
        // 期間
        if (isset($ymd_st) && isset($ymd_ed)) {
            $where_ymd  = "to_char(h.siire_ymd,'YYYY/MM/DD') BETWEEN '".$ymd_st."' AND '".$ymd_ed."'";
        } else if (isset($ymd_st)) {
            $where_ymd = "to_char(h.siire_ymd,'YYYY/MM/DD') >= '".$ymd_st."'";
        } else if (isset($ymd_ed)) {
            $where_ymd = "to_char(h.siire_ymd,'YYYY/MM/DD') <= '".$ymd_ed."'";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }
        // 処理日
        if (isset($syori_st_ymd) && isset($syori_ed_ymd)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') BETWEEN '".$syori_st_ymd."' AND '".$syori_ed_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._mod_ts,'YYYY/MM/DD') BETWEEN '".$syori_st_ymd."' AND '".$syori_ed_ymd."')";
        } else if (isset($ymd_st)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') >= '".$syori_st_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._mod_ts,'YYYY/MM/DD') >= '".$syori_st_ymd."')";
        } else if (isset($ymd_ed)) {
            $where_ymd  = "(to_char(m._cre_ts,'YYYY/MM/DD') <= '".$syori_ed_ymd."'";
            $where_ymd .= " OR ";
            $where_ymd .= "to_char(m._mod_ts,'YYYY/MM/DD') <= '".$syori_ed_ymd."'";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }
        // 部門
        if (isset($bum_ary) && count($bum_ary) > 0) {
            $ary = array();
            foreach ($bum_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "h.bumon_cd IN (" . implode(',', $ary) . ")";
        }
        if ($keijo_kbn !=  self::KEIJO_KBN_KAKE)    {
            if (isset($where)) {
                $where .= ' AND ';
            }
            // 売上部門
            $where .=  "
            CASE WHEN h.data_kbn = 3 THEN
                h.bumon_cd
             ELSE   
                COALESCE(um.bumon_cd,jm.bumon_cd)
             END <> h.bumon_cd";
        }
        // 商品コード
        if (isset($shohin_ary) && count($shohin_ary) > 0) {
            $ary = array();
            foreach ($shohin_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "m.shohin_cd IN (" . implode(',', $ary) . ")";
        }
        // 仕入先コード
        if (isset($siire_ary) && count($siire_ary) > 0) {
            $ary = array();
            foreach ($siire_ary as $value) {
                $ary[] = "'" . $value . "'";
            }
            if (isset($where)) {
                $where .= ' AND ';
            }
            $where .= "h.siire_cd ". self::getOpeBySiireTargetKbn($siire_target_kbn) ." (" . implode(',', $ary) . ")";
        }
        // 葬家名
		if (strlen($soke_nm) > 0) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= "sk.souke_nm like '%" . $soke_nm . "%'";
		}
		// 請求先名
		if (strlen($seikyu_nm) > 0) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= " (CASE WHEN h.data_kbn = 4 THEN
                            ud2.sekyu_nm
                        ELSE
                            coalesce(ud.sekyu_nm,jd.sekyu_nm)
                        END) like '%" . $seikyu_nm . "%'";
		}
        // 支払区分
		if ($rep_kbn2 != self::REPKBN_ALL) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= 'h.harai_kbn = ' . $rep_kbn2;
		}
        // 更新区分
		if ($update_kbn != self::UPDATE_KBN_ALL) {
			if (isset($where)) {
				$where .= ' AND ';
			}
			$where .= 'CASE WHEN rd.siire_den_no IS NULL THEN 0 --新規
                       ELSE 1 END =' . $update_kbn;
		}

		if (isset($where)) {
            $where .= ' AND ';
        }
        // 2015/06/22 UPD Kayo
        $where .= 'h.delete_flg = 0
                AND ((h.data_kbn <> 3 AND shi.seko_no is not null)
                OR   h.data_kbn = 3)
            ';    

        $where .= " AND h.kaisya_cd = '" . $kaisya_cd . "'"; // 2016/11/18 ADD Kayo

        return 'WHERE ' . $where;
    }

    /**
     * ORDER句を取得
     * <AUTHOR> Kayo
     * @since      2014/09/25
	 * @param string $order_kbn		出力順
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @return string ORDER句
     */
    private static function getOrder2($order_kbn, $keijo_kbn) {
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
                $order = 'ORDER BY h.bumon_cd';
        }  else {
            // 売上部門
            $order = '
            ORDER BY h.bumon_cd, CASE WHEN h.data_kbn = 3 THEN
               h.bumon_cd
            ELSE   
               COALESCE(um.bumon_cd,jm.bumon_cd)
            END';
        }
        return $order;
    }
    
    /**
     * PDF出力 仕入伝票入力チェックリスト 取得(部門別仕入先別)
     * <AUTHOR> kayo
     * @since      2014/11/25
     * @param Msi_Sys_Db $db
	 * @param string $ymd_st	対象年年月日（自）
	 * @param string $ymd_ed	対象年年月日（至）
	 * @param string $syori_st_ymd 処理日（自）
     * @param string $syori_ed_ymd 処理日（至）
     * @param type $bum_ary 部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
	 * @param string $rep_kbn2	支払区分
	 * @param string $order_kbn 出力順
     * @param string $delete_kbn 削除データ
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param string $kaisya_cd 会社コード
     * @param type $csv true:CSV出力用
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find3($db, $ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $bum_ary, $siire_target_kbn, $siire_ary
			, $soke_nm, $seikyu_nm, $update_kbn, $rep_kbn1, $rep_kbn2, $order_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd, $csv = false) {

        $where = self::getWhere1($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary
                , $siire_target_kbn, $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd);
        $order = self::getOrder3($order_kbn, $keijo_kbn);
        // 計上区分による処理
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
            // 売掛部門
            $sel_item = null;
            $sel_join = null;
            $sel_group_by = ' GROUP BY 
                            h.bumon_cd				-- 部門コード
                           ,bum.bumon_lnm          -- 部門名
                           ,h.siire_cd             -- 仕入先コード
                           ,sirm.siire_lnm			-- 仕入先名
                           ,sirm.com_siire_cd      -- 共通仕入先コード
                        ';                 
        }  else {
            // 売上部門
            $sel_item = '
                ,CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END                                    AS	u_bumon_cd			-- 売上部門コード
                ,bumu.bumon_lnm                         AS  u_bumon_nm          -- 部門名
                ';
            $sel_join = '
                LEFT JOIN juchu_denpyo_msi jm
                   ON   shi.jc_denpyo_no   = jm.denpyo_no
                   AND  shi.seko_no        = jm.seko_no
                   AND  shi.jc_msi_no      = jm.msi_no  
                   AND 0                   = jm.delete_flg
                LEFT JOIN uriage_denpyo_msi um
                   ON   shi.jc_denpyo_no    = um.uri_den_no 
                   AND  shi.seko_no         = um.seko_no 
                   AND  shi.jc_msi_no       = um.msi_no  
                   AND 0                    = um.delete_flg
                LEFT JOIN bumon_mst bumu
                ON  CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                END                        = bumu.bumon_cd
                AND h.kaisya_cd            = bumu.kaisya_cd     -- 2016/11/18 ADD Kayo            
                AND 0                      = bumu.delete_flg
            '; 
            $sel_group_by = ' 
                GROUP BY 
                 h.bumon_cd				-- 部門コード
                ,bum.bumon_lnm          -- 部門名
                ,CASE WHEN h.data_kbn = 3 THEN
                    h.bumon_cd
                 ELSE   
                    COALESCE(um.bumon_cd,jm.bumon_cd)
                 END
                ,bumu.bumon_lnm          -- 部門名
                ,h.siire_cd             -- 仕入先コード
                ,sirm.siire_lnm			-- 仕入先名
                ,sirm.com_siire_cd      -- 共通仕入先コード
             ';                 
        }   

        $select = $db->easySelect(<<< END_OF_SQL
SELECT
	 NULL                   AS	h_siire_den_no				-- 仕入伝票№
    ,NULL                   AS	h_denpyo_no                 -- 発注伝票NO
    ,NULL                   AS	seko_no						-- 施行番号				
    ,NULL                   AS	h_siire_ymd                 -- 仕入日
    ,NULL                   AS	h_data_kbn					-- データ区分
    ,NULL                   AS	data_kbn_nm
    ,h.bumon_cd				AS	h_bumon_cd						-- 部門コード
    ,bum.bumon_lnm          AS  h_bumon_nm                      -- 部門名
$sel_item                
	,NULL                   AS	h_tanto_cd						-- 担当者コード
	,NULL                   AS	h_tanto_nm                      -- 担当者名
	,NULL                   AS	souke_nm                        -- 葬家
	,NULL                   AS	h_sekyu_nm                      -- 請求先名
	,h.siire_cd				AS	h_siire_cd						-- 仕入先コード
	,sirm.siire_lnm			AS	h_siire_nm						-- 仕入先名
	,NULL                   AS	h_sime_ymd                      -- 支払締日
	,NULL                   AS	h_harai_kbn                     -- 支払区分
	,NULL                   AS	h_harai_kbn_nm                  -- 支払区分名      
	,NULL                   AS	h_harai_yotei_ymd               -- 支払予定日
	,NULL                   AS	h_soko_cd                       -- 倉庫コード
	,NULL                   AS	h_nonyu_cd                      -- 納入先コード
	,NULL                   AS	h_nonyu_nm                      -- 納入先名
	,NULL                   AS	h_nonyu_knm                     -- 納入先名カナ
	,NULL                   AS	h_nonyu_yubin_no                -- 納入先郵便番号
	,NULL                   AS	h_nonyu_addr1                   -- 納入先住所1
	,NULL                   AS	h_nonyu_addr2                   -- 納入先住所2
	,NULL                   AS	h_nonyu_tel                     -- 納入先電話番号
	,NULL                   AS	h_nonyu_fax                     -- 納入先FAX
	,NULL                   AS	h_nonyu_dt                      -- 納入日
	,SUM(h.siire_prc_sum + h.siire_nebk_sum)   AS	h_siire_prc_sum   -- 仕入金額合計
	,SUM(h.siire_nebk_sum)			AS	h_siire_nebk_sum              -- 仕入値引合計
	,SUM(h.szei_katax_taisho_prc)	AS	h_szei_katax_taisho_prc       -- 外税課税対象額
	,SUM(h.uzei_katax_taisho_prc)	AS	h_uzei_katax_taisho_prc       -- 内税課税対象額
	,SUM(h.hitax_katax_taisho_prc)	AS	h_hitax_katax_taisho_prc      -- 非税課税対象額
	,NULL                   AS	h_tax_kbn                       -- 税区分
	,NULL                   AS	h_tax_kbn_nm                    -- 税区分名      
	,NULL                   AS	h_zei_cd                        -- 消費税コード
	,SUM(h.out_zei_prc)		AS	h_out_zei_prc                   -- 外税消費税額
	,SUM(h.in_zei_prc)		AS	h_in_zei_prc                    -- 内税消費税額
	,NULL                   AS	h_denpyo_biko1                  -- 伝票備考１
	,NULL                   AS	h_denpyo_biko2                  -- 伝票備考２
	,NULL                   AS	h_delivery_kbn                  -- 納品場所区分
	,NULL                   AS	m_msi_no                        -- 仕入明細№
	,NULL    				AS	m_disp_no                       -- 表示順
	,NULL                   AS	m_denpyo_kbn                    -- 伝票区分
	,NULL                   AS	m_denpyo_kbn_nm                 -- 伝票区分名      
	,NULL                   AS	m_dai_bunrui_cd                 -- 大分類コード
	,NULL                   AS  m_dai_bunrui_nm                 -- 大分類名              
	,NULL                   AS	m_chu_bunrui_cd                 -- 中分類コード
	,NULL                   AS  m_chu_bunrui_nm                 -- 中分類名              
	,NULL                   AS	m_shohin_kbn                    -- 商品区分
	,NULL                   AS  m_shohin_kbn_nm                 -- 商品区分名        
	,NULL                   AS	m_shohin_cd                     -- 商品コード
	,NULL                   AS	m_shohin_nm                     -- 商品名
	,NULL                   AS	m_shohin_tkiyo_nm               -- 商品摘要名
	,SUM(m.siire_suryo)		AS	m_siire_suryo                   -- 数量
	,NULL                   AS	m_tani_cd                       -- 単位コード
	,NULL                   AS  m_tani_nm                       -- 単位名              
	,NULL                   AS	m_siire_tnk                     -- 仕入単価
	,SUM(m.siire_prc)       AS	m_siire_prc                     -- 仕入金額
	,SUM(m.siire_prc + m.nebiki_prc)	AS	m_siire_nebiki_prc  -- 仕入金額（値引き込）
	,SUM(m.nebiki_prc)		AS	m_nebiki_prc                    -- 値引額
	,NULL                   AS	m_nonyu_ymd                     -- 納品（納入）日時
	,NULL                   AS	m_nyuka_ymd                     -- 入荷日
	,NULL   				AS	m_msi_biko1                     -- 明細備考１
	,NULL                   AS	m_msi_biko2                     -- 明細備考２
	,NULL                   AS	m_zei_kbn                       -- 消費税区分
	,NULL					AS	m_zei_cd                        -- 消費税コード
	,SUM(m.out_zei_prc)             AS	m_out_zei_prc           -- 行外税消費税額
	,SUM(m.out_zei_prc)             AS	m_out_zei_prc2          -- 行外税消費税額
	,SUM(m.in_zei_prc)				AS	m_in_zei_prc            -- 行内税消費税額
	,SUM(m.siire_prc + m.nebiki_prc + m.out_zei_prc + m.out_zei_hasu_prc)  AS m_siire_zei_prc  -- 仕入金額(税込)
	,SUM(m.out_zei_hasu_prc)        AS  m_out_zei_hasu_prc      -- 消費税端数
	,NULL                   AS	m_soko_cd                       -- 倉庫コード
	,NULL                   AS  m_soko_nm                       -- 倉庫名            
	,NULL                   AS	m_kaku_tanto_cd                 -- 入荷確認者コード
	,NULL                   AS	m_kaku_dt                       -- 入荷確認日
	,NULL                   AS	m_jyodai_tnk                    -- 上代単価
	,NULL                   AS	m_jyodai_prc                    -- 上代金額
	,NULL                   AS  m_kamoku_cd                     -- 仕入科目コード     
	,NULL                   AS  m_kamoku_nm                     -- 仕入科目名
	,sirm.com_siire_cd      AS  com_siire_cd                    -- 共通仕入先コード
    ,NULL                   AS  rendo_mark                      -- 連動済
	,NULL                   AS	dendo_denno                     -- 連動伝票番号			 
FROM    siire_denpyo h
LEFT JOIN siire_denpyo_msi m
    ON h.siire_den_no       = m.siire_den_no
    AND 0                   = m.delete_flg
LEFT JOIN siire_rendo_data rh
    ON  m.siire_den_no      = rh.siire_den_no
    AND 0                   = rh.delete_flg
LEFT JOIN siire_rendo_data_msi rd
    ON  m.siire_den_no      = rd.siire_den_no
    AND m.msi_no            = rd.msi_no
    AND 0                   = rd.delete_flg
LEFT JOIN siire_mst sirm
    ON  h.siire_cd = sirm.siire_cd
    AND 0          = sirm.delete_flg             
LEFT JOIN seko_hachu_info shi
    ON  m.hachu_denpyo_no   = shi.hc_denpyo_no
    AND m.hachu_msi_no      = shi.hachu_no
    AND 0                   = shi.delete_flg
LEFT JOIN seko_kihon_info sk 
	ON shi.seko_no	=	sk.seko_no 
	AND 0			=	sk.delete_flg
LEFT JOIN juchu_denpyo jd
   ON   shi.jc_denpyo_no   = jd.denpyo_no
   AND  shi.seko_no        = jd.seko_no 
   AND  shi.data_kbn       = jd.data_kbn 
   AND 0                   = jd.delete_flg
LEFT JOIN uriage_denpyo ud
   ON   shi.jc_denpyo_no    = ud.uri_den_no 
   AND  shi.seko_no         = ud.seko_no 
   AND  shi.data_kbn        = ud.data_kbn 
   AND 0                    = ud.delete_flg
LEFT JOIN URIAGE_DENPYO ud2
   ON  shi.seko_no         = ud2.seko_no
   AND shi.data_kbn        = ud2.data_kbn
   AND shi.jc_denpyo_no    = ud2.denpyo_no
   AND 0                   = ud2.delete_flg
LEFT JOIN bumon_mst bum 
    ON h.bumon_cd   = bum.bumon_cd
    AND h.kaisya_cd = bum.kaisya_cd     -- 2016/11/18 ADD Kayo            
    AND 0           = bum.delete_flg
$sel_join
$where
$sel_group_by
$order
END_OF_SQL
        );
        return $select;
    }

    /**
     * ORDER句を取得
     * <AUTHOR> Kayo
     * @since      2014/11/26
	 * @param string $order_kbn		出力順
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @return string ORDER句
     */
    private static function getOrder3($order_kbn, $keijo_kbn) {
        if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
                $order = 'ORDER BY h.bumon_cd,h.siire_cd';
        }  else {
            // 売上部門
            $order = '
            ORDER BY
             h.bumon_cd
            ,CASE WHEN h.data_kbn = 3 THEN
               h.bumon_cd
            ELSE   
               COALESCE(um.bumon_cd,jm.bumon_cd)
            END, h.siire_cd';
        }
        return $order;
    }
    
    /**
     * PDF出力 仕入伝票入力チェックリスト 取得(仕入先別)
     * <AUTHOR> kayo
     * @since      2014/11/25
     * @param Msi_Sys_Db $db
	 * @param string $ymd_st	対象年年月日（自）
	 * @param string $ymd_ed	対象年年月日（至）
	 * @param string $syori_st_ymd 処理日（自）
     * @param string $syori_ed_ymd 処理日（至）
     * @param type $bum_ary 部門配列
     * @param array $siire_target_kbn	仕入先対象区分
     * @param array $siire_ary	仕入先コード
     * @param string $soke_nm   葬家名
     * @param string $seikyu_nm 請求先名
     * @param string $update_kbn 更新区分
	 * @param string $rep_kbn2		支払区分
	 * @param string $order_kbn2	出力順
     * @param string $delete_kbn 削除データ
	 * @param array $shohin_ary 商品コード
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @param string $kaisya_cd 会社コード
     * @param type $csv true:CSV出力用
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find4($db, $ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $bum_ary, $siire_target_kbn, $siire_ary
            , $soke_nm, $seikyu_nm, $update_kbn, $rep_kbn1, $rep_kbn2, $order_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd, $csv = false) {

        $where = self::getWhere1($ymd_st, $ymd_ed, $syori_st_ymd, $syori_ed_ymd, $rep_kbn1, $rep_kbn2, $bum_ary
                , $siire_target_kbn, $siire_ary, $soke_nm, $seikyu_nm, $update_kbn, $delete_kbn, $shohin_ary, $keijo_kbn, $kaisya_cd);
        $order = self::getOrder4($order_kbn, $keijo_kbn);
       if ($keijo_kbn ==  self::KEIJO_KBN_KAKE)    {
            // 売掛部門
            $sel_join = null; 
        }  else {
            $sel_join = '
                LEFT JOIN juchu_denpyo_msi jm
                   ON   shi.jc_denpyo_no   = jm.denpyo_no
                   AND  shi.seko_no        = jm.seko_no
                   AND  shi.jc_msi_no      = jm.msi_no  
                   AND 0                   = jm.delete_flg
                LEFT JOIN uriage_denpyo_msi um
                   ON   shi.jc_denpyo_no    = um.uri_den_no 
                   AND  shi.seko_no         = um.seko_no 
                   AND  shi.jc_msi_no       = um.msi_no  
                   AND 0                    = um.delete_flg
            '; 
        }   

        $select = $db->easySelect(<<< END_OF_SQL
SELECT
	 NULL                   AS	h_siire_den_no				-- 仕入伝票№
    ,NULL                   AS	h_denpyo_no                 -- 発注伝票NO
    ,NULL                   AS	seko_no						-- 施行番号				
    ,NULL                   AS	h_siire_ymd                 -- 仕入日
    ,NULL                   AS	h_data_kbn					-- データ区分
    ,NULL                   AS	data_kbn_nm
	,null                   AS	h_bumon_cd						-- 部門コード
	,NULL                   AS  h_bumon_nm                      -- 部門名
	,NULL                   AS	h_tanto_cd						-- 担当者コード
	,NULL                   AS	h_tanto_nm                      -- 担当者名
	,NULL                   AS	souke_nm                        -- 葬家
	,NULL                   AS	h_sekyu_nm                      -- 請求先名
	,h.siire_cd				AS	h_siire_cd						-- 仕入先コード
	,sirm.siire_lnm			AS	h_siire_nm						-- 仕入先名
	,NULL                   AS	h_sime_ymd                      -- 支払締日
	,NULL                   AS	h_harai_kbn                     -- 支払区分
	,NULL                   AS	h_harai_kbn_nm                  -- 支払区分名      
	,NULL                   AS	h_harai_yotei_ymd               -- 支払予定日
	,NULL                   AS	h_soko_cd                       -- 倉庫コード
	,NULL                   AS	h_nonyu_cd                      -- 納入先コード
	,NULL                   AS	h_nonyu_nm                      -- 納入先名
	,NULL                   AS	h_nonyu_knm                     -- 納入先名カナ
	,NULL                   AS	h_nonyu_yubin_no                -- 納入先郵便番号
	,NULL                   AS	h_nonyu_addr1                   -- 納入先住所1
	,NULL                   AS	h_nonyu_addr2                   -- 納入先住所2
	,NULL                   AS	h_nonyu_tel                     -- 納入先電話番号
	,NULL                   AS	h_nonyu_fax                     -- 納入先FAX
	,NULL                   AS	h_nonyu_dt                      -- 納入日
	,SUM(h.siire_prc_sum + h.siire_nebk_sum)   AS	h_siire_prc_sum   -- 仕入金額合計
	,SUM(h.siire_nebk_sum)			AS	h_siire_nebk_sum              -- 仕入値引合計
	,SUM(h.szei_katax_taisho_prc)	AS	h_szei_katax_taisho_prc       -- 外税課税対象額
	,SUM(h.uzei_katax_taisho_prc)	AS	h_uzei_katax_taisho_prc       -- 内税課税対象額
	,SUM(h.hitax_katax_taisho_prc)	AS	h_hitax_katax_taisho_prc      -- 非税課税対象額
	,NULL                   AS	h_tax_kbn                       -- 税区分
	,NULL                   AS	h_tax_kbn_nm                    -- 税区分名      
	,NULL                   AS	h_zei_cd                        -- 消費税コード
	,SUM(h.out_zei_prc)		AS	h_out_zei_prc                   -- 外税消費税額
	,SUM(h.in_zei_prc)		AS	h_in_zei_prc                    -- 内税消費税額
	,NULL                   AS	h_denpyo_biko1                  -- 伝票備考１
	,NULL                   AS	h_denpyo_biko2                  -- 伝票備考２
	,NULL                   AS	h_delivery_kbn                  -- 納品場所区分
	,NULL                   AS	m_msi_no                        -- 仕入明細№
	,NULL    				AS	m_disp_no                       -- 表示順
	,NULL                   AS	m_denpyo_kbn                    -- 伝票区分
	,NULL                   AS	m_denpyo_kbn_nm                 -- 伝票区分名      
	,NULL                   AS	m_dai_bunrui_cd                 -- 大分類コード
	,NULL                   AS  m_dai_bunrui_nm                 -- 大分類名              
	,NULL                   AS	m_chu_bunrui_cd                 -- 中分類コード
	,NULL                   AS  m_chu_bunrui_nm                 -- 中分類名              
	,NULL                   AS	m_shohin_kbn                    -- 商品区分
	,NULL                   AS  m_shohin_kbn_nm                 -- 商品区分名        
	,NULL                   AS	m_shohin_cd                     -- 商品コード
	,NULL                   AS	m_shohin_nm                     -- 商品名
	,NULL                   AS	m_shohin_tkiyo_nm               -- 商品摘要名
	,SUM(m.siire_suryo)		AS	m_siire_suryo                   -- 数量
	,NULL                   AS	m_tani_cd                       -- 単位コード
	,NULL                   AS  m_tani_nm                       -- 単位名              
	,NULL                   AS	m_siire_tnk                     -- 仕入単価
	,SUM(m.siire_prc)       AS	m_siire_prc                     -- 仕入金額
	,SUM(m.siire_prc + m.nebiki_prc)	AS	m_siire_nebiki_prc  -- 仕入金額（値引き込）
	,SUM(m.nebiki_prc)		AS	m_nebiki_prc                    -- 値引額
	,NULL                   AS	m_nonyu_ymd                     -- 納品（納入）日時
	,NULL                   AS	m_nyuka_ymd                     -- 入荷日
	,NULL   				AS	m_msi_biko1                     -- 明細備考１
	,NULL                   AS	m_msi_biko2                     -- 明細備考２
	,NULL                   AS	m_zei_kbn                       -- 消費税区分
	,NULL					AS	m_zei_cd                        -- 消費税コード
	,SUM(m.out_zei_prc)             AS	m_out_zei_prc           -- 行外税消費税額
	,SUM(m.out_zei_prc)             AS	m_out_zei_prc2          -- 行外税消費税額
	,SUM(m.in_zei_prc)				AS	m_in_zei_prc            -- 行内税消費税額
	,SUM(m.siire_prc + m.nebiki_prc + m.out_zei_prc + m.out_zei_hasu_prc)  AS m_siire_zei_prc  -- 仕入金額(税込)
	,SUM(m.out_zei_hasu_prc)        AS  m_out_zei_hasu_prc      -- 消費税端数
	,NULL                   AS	m_soko_cd                       -- 倉庫コード
	,NULL                   AS  m_soko_nm                       -- 倉庫名            
	,NULL                   AS	m_kaku_tanto_cd                 -- 入荷確認者コード
	,NULL                   AS	m_kaku_dt                       -- 入荷確認日
	,NULL                   AS	m_jyodai_tnk                    -- 上代単価
	,NULL                   AS	m_jyodai_prc                    -- 上代金額
	,NULL                   AS  m_kamoku_cd                     -- 仕入科目コード     
	,NULL                   AS  m_kamoku_nm                     -- 仕入科目名
	,sirm.com_siire_cd      AS  com_siire_cd                    -- 共通仕入先コード
    ,NULL                   AS  rendo_mark                      -- 連動済
	,NULL                   AS	dendo_denno                     -- 連動伝票番号			 
FROM    siire_denpyo h
LEFT JOIN siire_denpyo_msi m
    ON h.siire_den_no       = m.siire_den_no
    AND 0                   = m.delete_flg
LEFT JOIN siire_rendo_data rh
    ON  m.siire_den_no      = rh.siire_den_no
    AND 0                   = rh.delete_flg
LEFT JOIN siire_rendo_data_msi rd
    ON  m.siire_den_no      = rd.siire_den_no
    AND m.msi_no            = rd.msi_no
    AND 0                   = rd.delete_flg
LEFT JOIN siire_mst sirm
    ON  h.siire_cd = sirm.siire_cd
    AND h.kaisya_cd= sirm.kaisya_cd     -- 2016/11/18 ADD Kayo            
    AND 0          = sirm.delete_flg             
LEFT JOIN seko_hachu_info shi
    ON  m.hachu_denpyo_no   = shi.hc_denpyo_no
    AND m.hachu_msi_no      = shi.hachu_no
    AND 0                   = shi.delete_flg
LEFT JOIN seko_kihon_info sk 
	ON shi.seko_no	=	sk.seko_no 
	AND 0			=	sk.delete_flg
LEFT JOIN juchu_denpyo jd
   ON   shi.jc_denpyo_no   = jd.denpyo_no
   AND  shi.seko_no        = jd.seko_no 
   AND  shi.data_kbn       = jd.data_kbn 
   AND 0                   = jd.delete_flg
LEFT JOIN uriage_denpyo ud
   ON   shi.jc_denpyo_no    = ud.uri_den_no 
   AND  shi.seko_no         = ud.seko_no 
   AND  shi.data_kbn        = ud.data_kbn 
   AND 0                    = ud.delete_flg
LEFT JOIN URIAGE_DENPYO ud2
   ON  shi.seko_no         = ud2.seko_no
   AND shi.data_kbn        = ud2.data_kbn
   AND shi.jc_denpyo_no    = ud2.denpyo_no
   AND 0                   = ud2.delete_flg
$sel_join                
$where
GROUP BY 
	 h.siire_cd             -- 仕入先コード
	,sirm.siire_lnm			-- 仕入先名
	,sirm.com_siire_cd      -- 共通仕入先コード
$order
END_OF_SQL
        );
        return $select;
    }
    /**
     * PDF出力 仕入伝票入力チェックリスト 取得（アークベル用）
     * 
     * <AUTHOR> Sugiyama
     * @since      2018/05/15
     * @param type $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
     public static function find5($db,$keyHash=array()){
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            //$orderBy = 'ORDER BY T._mod_ts_ord, T.bumon_cd, T.nonyu_cd, T.siire_den_no, T.msi_no';
            $orderBy = 'ORDER BY T.bumon_cd, T._cre_ts_sort, T.msi_no';
        }
        $select = $db->easySelect( <<< END_OF_SQL
        SELECT *
        FROM (
            SELECT
                concat(
                    MST1.bumon_lnm,
                    CASE 
                        WHEN shi.nonyu_nm IS NULL THEN ''
                        WHEN shi.nonyu_nm = ''    THEN ''
                        ELSE '/'
                    END,
                    shi.nonyu_nm
                ) AS nonyu_saki,
                MAIN.siire_den_no,
                SUB1.msi_no,
                CASE 
                    WHEN SUB1._cre_ts = SUB1._mod_ts THEN '新規'
                    ELSE '変更'
                END AS syori_state,
                CASE
                    WHEN SUB1.delete_flg = 1 THEN 'D'
                    ELSE   ''
                END AS delete_state,
                to_char(MAIN.siire_ymd, 'MMDD') AS _siire_ymd,
                MAIN.siire_nm,
                SUB1.shohin_nm,
                SUB1.acos_hachu_no,
                SUB1.siire_prc,
                MAIN.siire_ymd,
                MAIN.tanto_cd,
                MAIN.nonyu_cd,
                MAIN.bumon_cd,
                MST2.tsujo_hachu_flg,
                SUB1._mod_ts AS _mod_ts_ord,
                to_char(SUB1._cre_ts, 'YYYY/MM/DD') AS _cre_ts,
                SUB1._cre_ts AS _cre_ts_sort,
                SUB1._cre_user,
                SUB1.siire_suryo
                ,CASE  -- keigen 軽減税率対応  
                        WHEN SUB1.zei_kbn = 0
                          THEN cm4.kbn_value_snm
                        WHEN SUB1.reduced_tax_rate = 2
                          THEN cm4.kbn_value_snm || '軽' ||zm2.zei_rtu || '%'  
                          ELSE cm4.kbn_value_snm ||        zm2.zei_rtu || '%'
                     END                                  AS      zei_kbn_nm                    -- 税区分名 軽減税率対応
                ,SUB1.zei_kbn                   AS  m_zei_kbn              -- 税区分  
	            ,SUB1.zei_cd					AS  m_zei_cd               -- 消費税コード
	            ,SUB1.out_zei_prc               AS  m_out_zei_prc          -- 行外税消費税額                
                ,SUB1.in_zei_prc                AS	m_in_zei_prc           -- 行内税消費税額
                ,CASE WHEN SUB1.zei_kbn = 2 THEN COALESCE(SUB1.out_zei_prc, 0) 
                      WHEN SUB1.zei_kbn = 1 THEN COALESCE(SUB1.in_zei_prc, 0)
                                            ELSE 0 END  AS  zei_prc           -- 税額
                ,CASE WHEN SUB1.zei_kbn = 1 THEN siire_prc - COALESCE(SUB1.in_zei_prc, 0)
                                            ELSE siire_prc END AS siire_prc_nuki  -- 税を含まない仕入金額 keigen
            FROM siire_denpyo MAIN
                JOIN siire_denpyo_msi SUB1
                  ON SUB1.siire_den_no = MAIN.siire_den_no
                JOIN bumon_mst MST1
                  ON MST1.bumon_cd = MAIN.bumon_cd
                LEFT JOIN shohin_mst MST2
                  ON MST2.shohin_cd = SUB1.shohin_cd
                 AND MST2.bumon_cd  = SUB1.shohin_bumon_cd
                LEFT JOIN code_nm_mst cm4 -- 軽減税率対応
                   ON   '0150'          = cm4.code_kbn
                   AND   SUB1.zei_kbn      = cm4.kbn_value_cd_num 
                   AND   0              = cm4.delete_flg
                LEFT JOIN zei_mst   zm2 -- 軽減税率対応
                    ON SUB1.zei_cd      = zm2.zei_cd
                    AND 0               = zm2.delete_flg
                LEFT JOIN seko_hachu_info shi
                    ON shi.hc_denpyo_no = SUB1.hachu_denpyo_no
                    AND shi.hachu_no    = SUB1.hachu_msi_no
                    AND shi.delete_flg  = 0
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
        , $param );

        return $select;                
     }

    /**
     * ORDER句を取得
     * <AUTHOR> Kayo
     * @since      2014/11/26
	 * @param string $order_kbn		出力順
     * @param string $keijo_kbn 計上区分 0:売掛部門 1:売上部門
     * @return string ORDER句
     */
    private static function getOrder4($order_kbn, $keijo_kbn) {
        $order = 'ORDER BY h.siire_cd';
        return $order;
    }
    
    /**
     * 仕入先対象区分から演算子を取得
     * <AUTHOR> Matsuyama
     * @param string $siire_target_kbn  仕入先対象区分
     * @return string 演算子
     */
    private static function getOpeBySiireTargetKbn($siire_target_kbn) {
        if ($siire_target_kbn ==  self::SIIRE_TARGET_KBN_NOT_IN)    {
            return 'NOT IN'; // 仕入先を除外
        } else {
            return 'IN'; // 仕入先を対象
        }
    }
}
