<?php

require_once dirname(__FILE__) . '/../../bootstrap.php';

class App_OtUserUtilsTest extends PHPUnit\Framework\TestCase
{
    public function setup():void
    {
        Zend_Session::$_unitTestEnabled = true;
    }

    public function test01()
    {
        print "\n";
        print " " . App_OtUserUtils::genNonce() ."\n";
        print " " . App_OtUserUtils::genNonce() ."\n";
        print " " . App_OtUserUtils::genNonce() ."\n";

        $nonce1 = App_OtUserUtils::genOtUser('30030300.user99996');
        print "nonce=>" . $nonce1 ."\n";

        $nonce2 = App_OtUserUtils::genOtUser('30030300.user99996', array('proc_max_cnt'=>200, 'expire_sec'=>3000));
        print "nonce=>" . $nonce2 ."\n";

        $isDel1 = App_OtUserUtils::delOtUser($nonce1);
        print "isDel=>" . $isDel1 ."\n";

        // $isDel2 = App_OtUserUtils::updOtUserEx($nonce2);
        // print "isDel=>" . $isDel2 ."\n";
    }

}
