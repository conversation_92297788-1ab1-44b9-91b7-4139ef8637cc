msiGlobalObj.func.selpart01=function(w){return function(s){void 0===s&&(s=12);var g=window.msiBbUtils,p=window.msiBbMixin,x=[],l=$("#"+w),z=l.closest(".comp-parent").length?l.closest(".comp-parent"):l.parent(),f=function(a){return l.find(a)},t=f(".component-root"),m={kenpin_status:'\u691c\u54c1\u30b9\u30c6\u30fc\u30bf\u30b9  ""  null         _na                _na',my_kenpin_ts:'\u7d0d\u54c1\u65e5    ""        null         .my_kenpin_ts      rtCheck',my_kenpin_dt:'\u691c\u54c1\u65e5    ""        ymd          .my_kenpin_dt      rtCheck',
my_kenpin_biko:'\u691c\u54c1\u5099\u8003  ""        max(60)      .my_kenpin_biko    rtCheck'},q=g.genModelBase(m,{defaults:function(){return{}},idAttribute:"myid",initialize:function(){this.on("change",this.chgKenpinEtc);this.on("change:kenpin_status",this.chgKenpinStatus);this.on("change:my_kenpin_dt",this.chgMyKenpinDt);this.on("change:my_kenpin_biko",this.chgMyKenpinBiko);this.chgKenpinEtc()},chgKenpinEtc:function(){this.isKenpinChanged();d.model.syncSelectedIds();d.trigger("my_refresh")},isKenpinChanged:function(){this.setNoDiff(!0);
var a=this.isChangedFromOrg("_");this.setNoDiff(!1);return a},canChangeKenpin:function(){return!!this.get("can_change_kenpin")},chgKenpinStatus:function(){if(this.get("can_change_kenpin"))if(0==+this.get("kenpin_status"))this.set({my_kenpin_ts:null,my_kenpin_dt:null});else{var a=new Date,b=a.getFullYear(),c=("0"+(a.getMonth()+1)).slice(-2),e=("0"+a.getDate()).slice(-2),d=("0"+a.getHours()).slice(-2),f=("0"+a.getMinutes()).slice(-2);("0"+a.getSeconds()).slice(-2);a=b+"/"+c+"/"+e;d=d+":"+f;f=this.get("nonyu_ymd_ymd");
this.set({my_kenpin_ts:a+" "+d,my_kenpin_dt:f})}else msiLib2.showErr("chgKenpinStatus error")},chgMyKenpinDt:function(){var a=this.get("my_kenpin_dt");$.msiJqlib.isNullEx2(a)&&this.set("my_kenpin_dt",null)},chgMyKenpinBiko:function(){var a=this.get("my_kenpin_biko");$.msiJqlib.isNullEx2(a)&&this.set("my_kenpin_biko",null)},validation:{_all:function(a,b,c){var e=this.isKenpinChanged(),f=+this.get("kenpin_status_org"),y=+this.get("kenpin_status"),h=this.get("my_kenpin_dt");a=d.model.get("_siire_fixed_ymd");
var k=[];b=[];var g,l=this;c=d.model.get("_flg_kenpin_kaijyo");if(e&&(0!=y?($.msiJqlib.isNullEx2(h)&&(b.push("\u691c\u54c1\u65e5\u304c\u5165\u529b\u3055\u308c\u3066\u3044\u307e\u305b\u3093"),k.push(".my_kenpin_dt")),!$.msiJqlib.isNullEx2(a)&&h<=a&&(b.push("\u691c\u54c1\u65e5("+h+")\u304c\u4ed5\u5165\u78ba\u5b9a\u65e5("+a+")\u4ee5\u524d\u3067\u3059"),k.push(".my_kenpin_dt")),0!=f&&f!=y&&(b.push("\u691c\u54c1OK/\u691c\u54c1NG \u306f\u76f4\u63a5\u5909\u66f4\u3067\u304d\u307e\u305b\u3093. \u691c\u54c1OK/\u691c\u54c1NG \u3092\u89e3\u9664\u3057\u3066\u304b\u3089\u8a2d\u5b9a\u3057\u3066\u304f\u3060\u3055\u3044"),
k.push(".kenpin_status_ck_cls"))):($.msiJqlib.isNullEx2(h)||(b.push("\u691c\u54c1\u30c1\u30a7\u30c3\u30af\u304c\u306a\u3044\u306e\u3067\u691c\u54c1\u65e5\u306f\u7121\u52b9\u3067\u3059"),k.push(".my_kenpin_dt")),e=this.get("my_kenpin_dt_org"),!$.msiJqlib.isNullEx2(a)&&!$.msiJqlib.isNullEx2(e)&&!c&&e<=a&&(b.push("\u691c\u54c1("+e+")\u306f\u4ed5\u5165\u78ba\u5b9a("+a+")\u3055\u308c\u3066\u3044\u307e\u3059"),k.push(".my_kenpin_dt"))),b.length))return g=b.join(", "),_.defer(function(){l.trigger("decoErrInfo",
k.join(","),g)}),g}}}),q=Backbone.Model.extend(q);_.defaults(q.prototype,p.ModelDataChangedMixin);var r=Backbone.Collection.extend({model:q,nextSeqNo:function(){++r.seq_cnt;return r.seq_cnt},myReset:function(a,b,c){null!==a&&this.setOffset(a);this.reset(b,c);this.markAsOrg()},_my_offset:0,updateRows:function(a){var b=this;_.each(a,function(a){var e=b.findWithId(a.myid);e&&(e.set(a),e.markAsOrg())});this.markAsOrg()},setOffset:function(a){this._my_offset=+a},getOffset:function(a){return+this._my_offset},
findWithId:function(a){return this.findWhere({myid:a})},myUpdate:function(a,b){void 0===b&&(b=[]);var c=this;_.each(a,function(a){c.add(a,{silent:!0,merge:!0})});_.each(b,function(a){c.remove(a,{silent:!0})});this.reset(this.models)},resetLineNo:function(){var a,b,c,e=this.getOffset();a=0;for(b=this.length;a<b;a++)c=this.at(a),c.set("line_no",e+a+1,{silent:!0}),c.set("line_no_p",a+1,{silent:!0})},getAllIds:function(){return this.pluck("myid")},isAny:function(){return 0<this.length},validateEx:function(){var a=
[];this.each(function(b,c){var e,d,f;if(f=b.validate())d=c+1,_.each(f,function(b,c){e=d+"\u884c\u76ee:"+b;a.push(e)})});return a},validateSelected:function(){var a=[];if(d){var b=d.model.getSelectedIds();this.each(function(c,e){var d,f,g;g=c.get("myid");_.contains(b,g)&&(g=c.validate())&&(f=e+1,_.each(g,function(b,c){d=f+"\u884c\u76ee:"+b;a.push(d)}))});return a}},getSelected:function(){if(!d)return null;var a=d.model.getSelectedIds();return this.filter(function(b){b=b.get("myid");return _.contains(a,
b)})},toJSONSelected:function(){if(!d)return[];var a=this.getSelected();return _.map(a,function(a){return a.toJSON()})},isAnyKenpinChanged:function(){return!!this.find(function(a){return a.isKenpinChanged()})},allKenpinCheck:function(a){this.map(function(b){b.canChangeKenpin()&&b.set("kenpin_status",a)})},allKenpinCheckGuard:function(a,b){this.map(function(c){if(c.canChangeKenpin()){var e=c.get("kenpin_status");e!=b&&0!=e||c.set("kenpin_status",a)}})}},{seq_cnt:0});_.defaults(r.prototype,p.ModelDataChangedMixin);
var p=new r,g=g.genViewBase(m,{tagName:"tbody",template:_.template(f(".item-template").html()),events:{"click .dlg_my_kenpin_dt":"setDatePicker","click .kenpin_status_ck_cls":"hClickKenpinStatus","click .shohin_img_link":"dispShohinImg"},initialize:function(){Backbone.Validation.bind(this,Backbone.Validation.msi_v_iv_callback2({},void 0,"."));this.listenTo(this.model,"change",this.render);this.listenTo(this.model,"decoErrInfo",this.decoErrInfo);this.listenTo(this.model,"clearErrInfo",this.clearErrInfo);
this.$el.html(this.template(this.model.toJSON()));this.$(".my_kenpin_dt").datetimepicker($.extend({},$.msiJqlib.datePickerDefault,{showTimepicker:!1}));this.stickit()},dispShohinImg:function(a){var b=this.model.get("shohin_gazo_img");a=this.model.get("shohin_nm");$.msiJqlib.isNullEx2(b)||(b=$.msiJqlib.baseUrl()+"/mref/gazodlg/shohinimg/imgid/"+b,$.colorbox({href:b,photo:!0,opacity:.4,maxWidth:"600",title:a}))},setDatePicker:function(a){$(a.currentTarget).is("[disabled]")||$(a.currentTarget).prev("input").datepicker("show")},
render:function(){this.model.get("kenpin_status_org");var a=this.model.get("kenpin_status");this.model.get("myid");var b=this.model.canChangeKenpin();b?(this.$(".kenpin_status_ck_cls").addClass("can-click"),this.$(".kenpin_status_ck_cls").attr("title","")):(this.$(".kenpin_status_ck_cls").removeClass("can-click"),this.$(".kenpin_status_ck_cls").attr("title","\u5909\u66f4\u3067\u304d\u307e\u305b\u3093"));this.$(".kenpin_status_ck_cls").removeClass("selected");-1==a?this.$(".kenpin_status_ng").addClass("selected"):
1==a&&this.$(".kenpin_status_ok").addClass("selected");b?(this.$(".my_kenpin_dt").removeAttr("readonly"),this.$(".dlg_my_kenpin_dt").removeAttr("disabled"),this.$(".dlg_my_kenpin_dt").addClass("cursor-pointer"),this.$(".my_kenpin_dt").attr("title","")):(this.$(".my_kenpin_dt").attr("readonly","readonly"),this.$(".dlg_my_kenpin_dt").attr("disabled","disabled"),this.$(".dlg_my_kenpin_dt").removeClass("cursor-pointer"),this.$(".my_kenpin_dt").attr("title","\u5909\u66f4\u3067\u304d\u307e\u305b\u3093"));
this.$(".my_kenpin_biko").removeAttr("readonly");a=this.$(".shohin_img_link");this.model.get("shohin_gazo_img")?a.addClass("shohin-img-clickable"):a.removeClass("shohin-img-clickable");return this},decoErrInfo:function(a,b){this.$(a).msiErrDeco(b)},clearErrInfo:function(a){this.$(a).msiErrClear()},canChangeSt:function(){return this.model.canChangeKenpin()?!0:!1},hClickKenpinStatus:function(a){this.model.trigger("clearErrInfo",".kenpin_status_ok,.kenpin_status_ng,.juchu_ng_note");var b=$(a.currentTarget);
this.canChangeSt()&&(a=this.model.get("kenpin_status"),b=b.hasClass("kenpin_status_ok")?1:b.hasClass("kenpin_status_ng")?-1:0,0!=a&&a==b?(a=0,d.unselectRow(this.model.get("myid"))):(a=b,d.selectRow(this.model.get("myid"))),this.model.set("kenpin_status",a))}}),A=Backbone.View.extend(g),g={defaults:function(){return{}},validation:{},labels:{},initAdj:function(a){var b=this;_.each(a,function(a,e){b.has(e)||b.set(e,a)});u=this.toJSON()},condKeys:function(){return _.reduce(this.toJSON(),function(a,b,
c){/^s_/.test(c)&&a.push(c);return a},[])},condKeyValues:function(){return _.reduce(this.toJSON(),function(a,b,c){/^s_/.test(c)&&(a[c]=b);return a},{})},condKeyValuesWithOffset:function(a){var b=this.condKeyValues();b.s_offset=a;return b},getSelectedIds:function(){return this.has("item_ids_selected")?_.keys(this.get("item_ids_selected")):[]},syncSelectedIds:function(){var a=[];d.collection.each(function(b){b.isKenpinChanged()&&(b=b.get("myid"),a[b]=b)});this.set("item_ids_selected",a)}};msiLib2.injObjExtract(g,
"selpart01","SrcCondModel");g=Backbone.Model.extend(g);m={el:t,myUrlModCont:function(){return $.msiJqlib.baseUrl()+"/"+$.msiJqlib.urlPathModCont()},myUrlSearch:function(){return this.myUrlModCont()+"/search"},myUrlCondinit:function(){return this.myUrlModCont()+"/condinit"},myShowDtlFunc:function(){return this._showNew},events:{"click .btn_search":"doSearch","click .btn_search_prev":"doSearchPrev","click .btn_search_next":"doSearchNext","click .btn_search_redo":"doSearchRedo","click .btn_clear":"doClear",
"click .chkAllToggle":"toggleChkAll","click .chkAllOk":"toggleChkOk","click .chkAllNg":"toggleChkNg"},initialize:function(){this.listenTo(this.model,"change",this.render);this.listenTo(Backbone,"msi.ajaxStop",this.renderBtn);this.on("my_refresh",this.render);this.listenTo(this.model,"change:item_ids_selected",this.render);this.listenTo(this.model,"my_rebind",this.rebindings);this.listenTo(this.collection,"reset",this.resetCol);this.initialize_sub();this.stickit();this.render()},initialize_sub:function(){},
rebindings:function(){this.stickit();Backbone.Validation.bind(this,Backbone.Validation.msi_v_iv_callback2({},void 0,"."));this.model.trigger("change")},setMstrUI:function(){var a=this;_.defer(function(){l.find(".msi-local-picker").each(function(){msiLib2.msiLocalPickerBinder.apply(this,[x])});var b=msiLib2.getObjViaMsiDelayedBinder(t),c=b.modelBase,e=b.viewBase,b=b.initValues;_.has(c,"validation")&&(a.model.validation=_.defaults(a.model.validation,c.validation));_.has(c,"labels")&&(a.model.labels=
_.defaults(a.model.labels,c.labels));_.has(e,"bindings")&&(a.bindings=_.defaults(a.bindings,e.bindings));_.has(e,"events")&&(a.events=_.defaults(a.events,e.events),a.delegateEvents());a.model.initAdj(b);a.model.trigger("my_rebind")})},render:function(){var a=this;_.defer(function(){a.scrollAdj()});this.renderBtn();this.render_sub();return this},render_sub:function(){},resetCol:function(a,b){var c=this,e=f(".component-root .result-list .list");e.empty();var d="";if(a.length){a.resetLineNo();var d=
f(".item-template-colgroup").html(),g=$("<table>"+d+"</table>").appendTo(e);_.each(a.models,function(a){var b=new A({model:a});g.append(b.render().el);a.markAsOrg()})}if(0>=a.length&&_.has(b,"msg")&&_.isString(b.msg)&&0<b.msg.length){var d=b.msg,h=f(".list-msg-template").html(),d=_.template(h,{msg:d});e.append(d)}setTimeout(function(){c.render()},0)},renderBtn:function(){this.collection.isAnyKenpinChanged()?(this.btnEnabled('.sekolist-cmd-buttons input[type="button"]'),this.btnEnabled(".btn_ctrl_selected")):
(this.btnDisabled('.sekolist-cmd-buttons input[type="button"]'),this.btnDisabled(".btn_ctrl_selected"));this.renderCheck()},renderCheck:function(){var a=this.$(".result-list .list table").find("tr"),b=this.model.getSelectedIds(),c=this;a.removeClass("row-selected");_.each(b,function(a){c.$(".result-list .list table").find('tr[data-item_id="'+a+'"]').addClass("row-selected")})},scrollAdj:function(){var a=z.height(),b=this.$(".result-list .list"),c=this.$(".result-list .header"),e;e=c.outerHeight(!0);
var d=this.$(".header-part").outerHeight(!0),g=this.$(".search-cond-part").outerHeight(!0),h=this.$(".footer-part-1").outerHeight(!0),k=this.$(".footer-part-2").outerHeight(!0),a=a-(d+g+h+k+s);e=a-e;f(".component-root .result-part").height(a);b.height(e);b[0].scrollHeight===b[0].clientHeight?(e="auto",b.css("overflow-y",e),c.css("overflow-y",e)):(e="scroll",a=c.height(),b.css("overflow-y",e),c.css("overflow-y",e),c.height(a))},clearErr:function(){this.$el.msiErrClearAll();msiLib2.clearAlert()},btnDisabled:function(a){f(a).attr("disabled",
"disabled")},btnEnabled:function(a){f(a).removeAttr("disabled")},isInputOk:function(){this.clearErr();var a=[];if(!this.model.validate)return!0;var b=this.model.validate();b&&_.each(b,function(b,e){a.push(b)});if(0<a.length)return msiLib2.showErr(a.join(", ")),!1;msiLib2.clearAlert();return!0},isChanged:function(){return this.collection&&this.collection.isChangedFromOrg("_")?!0:!1},dispSearchResult:function(a,b){var c={};b.msg&&(c={msg:b.msg});this.collection.myReset(a,b.list_data,c);var c=b.next_offset,
e=b.prev_offset;0<c&&f(".sp-act-button-search-next").attr("data-offset",c).css("visibility","visible");0<=e&&f(".sp-act-button-search-prev").attr("data-offset",e).css("visibility","visible");f(".sp-act-btn-search-redo").css("visibility","visible");_.has(b,"search_cnt_disp")&&f(".search-cnt-disp-txt").text(b.search_cnt_disp)},_isOkPageRefresh:function(){return d.isChanged()&&!confirm("\u4fdd\u5b58\u3055\u308c\u3066\u3044\u306a\u3044\u30c7\u30fc\u30bf\u304c\u3042\u308a\u307e\u3059.\n\u3088\u308d\u3057\u3044\u3067\u3059\u304b\uff1f")?
!1:!0},searchFunc_lastOffset:0,searchFunc:function(a,b){if(this.isInputOk()&&this._isOkPageRefresh()){void 0===b&&(b=0);this.searchFunc_lastOffset=b;var c=this.model.condKeyValuesWithOffset(b);f(".sp-act-button-search-next").removeAttr("data-offset").css("visibility","hidden");f(".sp-act-button-search-prev").removeAttr("data-offset").css("visibility","hidden");f(".sp-act-btn-search-redo").css("visibility","hidden");f(".search-cnt-disp-txt").text("");this.selectFunc(null);this.model.set("item_ids_selected",
{});this.model.set("_chkAll",!1);this.$(".chkAllToggle").removeClass("selected");this.model.set("_chkAllOk",!1);this.$(".chkAllOk").removeClass("selected");this.model.set("_chkAllNg",!1);this.$(".chkAllNg").removeClass("selected");var c=JSON.stringify(c),e=this;$.ajax({url:this.myUrlSearch(),type:"POST",data:c,contentType:"application/json",dataType:"json",success:function(a){"OK"==a.status?e.dispSearchResult(b,a):msiLib2.showErr2(a.msg)}})}},doSearch:function(a){this.searchFunc(a)},doSearchRedo:function(a){this.searchFunc(a,
this.searchFunc_lastOffset)},doSearchPrev:function(a){var b=f(".btn_search_prev").attr("data-offset");this.searchFunc(a,b)},doSearchNext:function(a){var b=f(".btn_search_next").attr("data-offset");this.searchFunc(a,b)},doClear:function(){var a=this;this.clearErr();if(this._isOkPageRefresh()){var b=JSON.stringify({});$.ajax({url:this.myUrlCondinit(),type:"POST",data:b,contentType:"application/json",dataType:"json",success:function(b){"OK"==b.status?setTimeout(function(){n(b.data.dataApp);a.doReset()},
0):msiLib2.showErr2(b.msg)}})}},doReset:function(){this.clearErr();n(u);this.model.set("item_ids_selected",{});this.model.set("_chkAll",!1);this.$(".chkAllToggle").removeClass("selected");this.model.set("_chkAllOk",!1);this.$(".chkAllOk").removeClass("selected");this.model.set("_chkAllNg",!1);this.$(".chkAllNg").removeClass("selected");f(".component-root .result-list .list").empty();f(".sp-act-button-search-next").removeAttr("data-offset").css("visibility","hidden");f(".sp-act-button-search-prev").removeAttr("data-offset").css("visibility",
"hidden");f(".sp-act-btn-search-redo").css("visibility","hidden");this.collection.setOffset(0);f(".search-cnt-disp-txt").text("");this.model.trigger("change")},unselectRow:function(a){var b=this.$(".result-list .list table").find('tr[data-item_id="'+a+'"]'),c=this.model.get("item_ids_selected");!b||1>b.length||!b.hasClass("row-selected")||(delete c[a],b.removeClass("row-selected"),this.model.set("item_ids_selected",c),this.model.trigger("change:item_ids_selected"))},selectRow:function(a){var b=this.$(".result-list .list table").find('tr[data-item_id="'+
a+'"]'),c=this.model.get("item_ids_selected");!b||1>b.length||b.hasClass("row-selected")||(c[a]=a,b.addClass("row-selected"),this.model.set("item_ids_selected",c),this.model.trigger("change:item_ids_selected"))},selectFunc:function(a){var b=this.$(".result-list .list table").find('tr[data-item_id="'+a+'"]'),c=this.model.get("item_ids_selected");!b||1>b.length||(_.has(c,a)?(delete c[a],b.removeClass("row-selected")):(c[a]=a,b.addClass("row-selected")),this.model.set("item_ids_selected",c),this.model.trigger("change:item_ids_selected"))},
_showNew:function(a){a=$.msiJqlib.baseUrl()+a;var b=this;msiLib2.openWinSub(function(){b.doSearch()},a)},_showNewAbs:function(a){a=$.msiJqlib.baseUrl()+a;msiLib2.openNewWinAbs(a)},checkAll:function(){this._checkAll(!0)},uncheckAll:function(){this._checkAll(!1)},_checkAll:function(a){var b=this,c={};a&&f("tr[data-item_id]").each(function(){var a=f(this).attr("data-item_id");if(canCheckOnlyMi){var d=b.collection.findWithId(a);if(!d||0!=d.get("kenpin_status_org"))return}c[a]=a});this.model.set("item_ids_selected",
c);this.model.trigger("change:item_ids_selected");a?this.$(".chkAllToggle").addClass("selected"):this.$(".chkAllToggle").removeClass("selected");this.model.set("_chkAll",a)},toggleChkAll:function(){this.model.get("_chkAll")?this.uncheckAll():this.checkAll()},_checkKenpinAll:function(a){this.collection.allKenpinCheck(a);this.model.syncSelectedIds()},_checkKenpinAllGuard:function(a,b){this.collection.allKenpinCheckGuard(a,b);this.model.syncSelectedIds()},toggleChkOk:function(){var a=!this.model.get("_chkAllOk");
a?(this.$(".chkAllNg").removeClass("selected"),this.model.set("_chkAllNg",!1),this.$(".chkAllOk").addClass("selected"),this._checkKenpinAll(1)):(this.$(".chkAllOk").removeClass("selected"),this._checkKenpinAllGuard(0,1));this.model.set("_chkAllOk",a)},toggleChkNg:function(){var a=!this.model.get("_chkAllNg");a?(this.$(".chkAllOk").removeClass("selected"),this.model.set("_chkAllOk",!1),this.$(".chkAllNg").addClass("selected"),this._checkKenpinAll(-1)):(this.$(".chkAllNg").removeClass("selected"),this._checkKenpinAllGuard(0,
-1));this.model.set("_chkAllNg",a)},bindings:{}};msiLib2.injObjExtract(m,"selpart01","AppViewBase");var d,u={},n,v;d=new (Backbone.View.extend(m))({model:new g,collection:p});n=function(a){d.model.set(a);u=d.model.toJSON();f(document).msiErrClearAll();d.collection.myReset(0,[],{});d.model.trigger("change");d.trigger("resetData",a)};d._resetData=n;v=function(a){void 0===a&&(a=msiLib2.getJsonFromHtml(f(".my-data-init-cls")));a&&a.mstrData&&(x=a.mstrData,d._mstrData=a.mstrData,d.setMstrUI());a&&a.exData&&
(d._exData=a.exData);a&&a.dataApp&&(setTimeout(function(){n(a.dataApp)},0),a.dataApp.init_search&&msiLib2.isValTrue(a.dataApp.init_search)?setTimeout(function(){d.doSearch()},0):a.dataApp.inline_result&&setTimeout(function(){d.dispSearchResult(0,a.dataApp.inline_result)},0));d.trigger("setInitData",a)};d._setInitData=v;msiLib2.msiPrepareParts(t);$(window).on("beforeunload",function(){if(d.isChanged())return"\u4fdd\u5b58\u3055\u308c\u3066\u3044\u306a\u3044\u30c7\u30fc\u30bf\u304c\u3042\u308a\u307e\u3059."});
$(window).on("resize",function(){d.render()});$.msiJqlib.initDone(function(){v();_.defer(function(){$(".component-root").fadeIn("fast");d.model.trigger("change")})});msiGlobalObj.appView[w]=d}};
