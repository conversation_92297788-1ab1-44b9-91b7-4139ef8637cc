<?php
  /**
   * DataMapper_SekoYukanInfo
   *
   * 施行湯灌情報 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/03/13
   * @filesource 
   */

  /**
   * 施行湯灌情報 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/03/13
   */
class DataMapper_SekoYukanInfo extends DataMapper_Abstract
{
    /**
     * 施行湯灌情報 取得
     *
     * <AUTHOR> Sato
     * @since      2014/03/13
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
	 s.seko_no
    ,s.yukan_ymd
    ,yukan_yubin_no
    ,yukan_addr1
    ,yukan_addr2
    ,s.mouth_kbn
    ,s.nose_kbn
    ,s.ear_kbn
    ,s.etc_body_nm
    ,s.death_spot
    ,s.death_cause
    ,s.br_butsui_kbn
    ,s.br_butsui_nm
    ,s.br_jyoui_kbn
    ,s.br_waraji_kbn
    ,s.br_amigasa_kbn
    ,s.br_etc_kbn
    ,s.br_etc_nm
    ,s.biko1
    ,s.biko2
    ,kihon.syuha_nm syuha_nm2
    ,tanto.tanto_nm
    ,yukan.kbn_value_lnm AS yukan_nm
    ,yu_te.kbn_value_lnm AS yu_te_nm
    ,syushi.kbn_value_lnm AS syushi_nm
    ,syuha.kbn_value_lnm AS syuha_nm
  FROM seko_yukan_info s
  LEFT JOIN seko_kihon_info kihon 
      ON s.seko_no = kihon.seko_no 
      AND kihon.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.yukan_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN code_nm_mst yukan 
      ON yukan.code_kbn = '0340' 
      AND s.yukan_cd = yukan.kbn_value_cd 
      AND yukan.delete_flg = 0
  LEFT JOIN code_nm_mst yu_te 
      ON yu_te.code_kbn = '0350' 
      AND s.yu_te_cd = yu_te.kbn_value_cd 
      AND yu_te.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
      ON syushi.code_kbn = '0240' 
      AND kihon.syushi_cd = syushi.kbn_value_cd 
      AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst syuha 
      ON syuha.code_kbn = '0200' 
      AND kihon.syuha_cd = syuha.kbn_value_cd
      AND syuha.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }

    /**
     * 施行湯灌情報 取得
     *
     * <AUTHOR> Yanagiso
     * @since      2015/03/24
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
	 s.seko_no
    ,s.yukan_ymd
    ,yukan_yubin_no
    ,yukan_addr1
    ,yukan_addr2
    ,s.mouth_kbn
    ,s.nose_kbn
    ,s.ear_kbn
    ,s.etc_body_nm
    ,s.death_spot
    ,s.death_cause
    ,s.br_butsui_kbn
    ,s.br_butsui_nm
    ,s.br_jyoui_kbn
    ,s.br_waraji_kbn
    ,s.br_amigasa_kbn
    ,s.br_etc_kbn
    ,s.br_etc_nm
    ,s.biko1
    ,s.biko2
    ,kihon.syuha_nm syuha_nm2
    ,kihon.syuha_knm -- 宗派カナ名 2017/11/28 ADD Otake
    ,tanto.tanto_nm
    ,yukan.kbn_value_lnm AS yukan_nm
    ,yu_te.kbn_value_lnm AS yu_te_nm
    ,syushi.kbn_value_lnm AS syushi_nm
    ,syuha.kbn_value_lnm AS syuha_nm
    ,s.yu_basyo_cd
    ,s.yu_spot_nm
    ,jdm.shohin_nm as hitsugi_nm
    ,nitei.spot_cd
    ,nitei.basho_nm
    ,s.yukan_tel
    ,s.item1_use_kbn
    ,s.item2_use_kbn
    ,s.item3_use_kbn
    ,s.item4_use_kbn
    ,s.item5_use_kbn
    ,s.item6_use_kbn
    ,s.item7_use_kbn
    ,s.item8_use_kbn             
    ,s.k_free1
  FROM seko_yukan_info s
  LEFT JOIN seko_kihon_info kihon 
      ON s.seko_no = kihon.seko_no 
      AND kihon.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.yukan_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN code_nm_mst yukan 
      ON yukan.code_kbn = '0340' 
      AND s.yukan_cd = yukan.kbn_value_cd 
      AND yukan.delete_flg = 0
  LEFT JOIN code_nm_mst yu_te 
      ON yu_te.code_kbn = '0350' 
      AND s.yu_te_cd = yu_te.kbn_value_cd 
      AND yu_te.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
      ON syushi.code_kbn = '0240' 
      AND kihon.syushi_cd = syushi.kbn_value_cd 
      AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst syuha 
      ON syuha.code_kbn = '0200' 
      AND kihon.syuha_cd = syuha.kbn_value_cd
      AND syuha.delete_flg = 0
  LEFT JOIN juchu_denpyo_msi jdm
      ON s.seko_no = jdm.seko_no
      AND jdm.shohin_kbn = '0570'
      AND jdm.juchu_suryo > 0
      AND jdm.delete_flg = 0
  LEFT JOIN seko_nitei nitei
      ON s.seko_no = nitei.seko_no
      AND nitei.nitei_kbn = 3
      AND nitei.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
 /**
     * 施行湯灌情報 取得
     *
     * <AUTHOR> Sai
     * @since      2015/07/22
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find3($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
	 s.seko_no
        ,kihon.k_knm
        ,kihon.k_nm
        ,kihon.k_sex_kbn
        ,kihon.k_nenrei_man
        ,kihon.syuha_nm
        ,cnm2.kbn_value_lnm AS syushi_nm
        ,s.yukan_ymd -- 業務日時
        ,s.k_free1 -- 業務内容区分：身支度
        ,s.k_free2 -- 業務内容区分：納棺
        ,s.k_free3 -- 業務内容区分：湯灌
        ,s.k_free4 -- 業務内容区分：リハビリテーション
        ,s.k_free5 -- 業務内容区分：パーティングキープ
        ,s.k_free6 -- 着付け区分
        ,s.k_free7 -- 業務場所区分
        ,s.k_free8 -- 死因区分
        ,s.k_free9 -- 病名区分：癌
        ,s.k_free10 -- 病名区分：心疾患
        ,s.k_free11 -- 病名区分：脳血管疾患
        ,s.k_free12 -- 病名区分：肝疾患
        ,s.k_free13 -- 病名区分：肺炎
        ,s.k_free14 -- 病名区分：老衰
        ,s.k_free15 -- 病名区分：多臓器不全
        ,s.k_free16 -- 病名区分：その他
        ,s.k_free17 -- 状態区分：感染症
        ,s.k_free18 -- 状態区分：腹水
        ,s.k_free19 -- 状態区分：出血
        ,s.k_free20 -- 状態区分：顔変色
        ,s.k_free21 -- 状態区分：腹部膨張
        ,s.k_free22 -- 状態区分：ガス
        ,s.k_free23 -- 状態区分：腐敗
        ,s.k_free24 -- 状態区分：他
        ,s.k_free25 -- 体型区分
        ,s.k_free30 -- 仏衣区分
        ,s.k_free31 -- 納棺花区分
        ,s.k_free32 -- パーティングコース
        ,cnm1.kbn_value_lnm AS parting_nm -- パーティングコース
        ,s.n_free1 -- 業務場所：分前
        ,s.v_free1 -- 業務内容のコース名
        ,s.v_free2 -- 着付け
        ,s.v_free3 -- 死因：病死
        ,s.v_free4 -- 死因：事故
        ,s.v_free5 -- 死因：自殺
        ,s.v_free6 -- 死因：他
        ,s.v_free7 -- 病名：癌
        ,s.v_free8 -- 状態：他
        ,s.v_free9 -- 仏衣
        ,s.v_free10 -- 納棺花
        ,s.biko1 -- 備考          
  FROM seko_yukan_info s
  INNER JOIN seko_kihon_info kihon 
      ON s.seko_no = kihon.seko_no 
      AND kihon.delete_flg = 0
  LEFT JOIN code_nm_mst cnm1 
      ON cnm1.code_kbn = '2130' 
      AND s.k_free32 = cnm1.kbn_value_cd_num 
      AND cnm1.delete_flg = 0
  LEFT JOIN code_nm_mst cnm2 
      ON cnm2.code_kbn = '0240' 
      AND kihon.syushi_cd = cnm2.kbn_value_cd
      AND cnm2.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }

    /**
     * 施行湯灌情報 取得（アイパル用）
     *
     * <AUTHOR> Sugiyama
     * @since      2017/09/29
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find4($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
    s.seko_no
    ,s.yukan_ymd
    ,yukan_yubin_no
    ,yukan_addr1
    ,yukan_addr2
    ,s.mouth_kbn
    ,s.nose_kbn
    ,s.ear_kbn
    ,s.etc_body_nm
    ,s.death_spot
    ,s.death_cause
    ,s.br_butsui_kbn
    ,s.br_butsui_nm
    ,s.br_jyoui_kbn
    ,s.br_waraji_kbn
    ,s.br_amigasa_kbn
    ,s.br_etc_kbn
    ,s.br_etc_nm
    ,s.biko1
    ,s.biko2
    ,kihon.syuha_nm syuha_nm2
    ,tanto.tanto_nm
    ,yukan.kbn_value_lnm AS yukan_nm
    ,yu_te.kbn_value_lnm AS yu_te_nm
    ,syushi.kbn_value_lnm AS syushi_nm
    ,syuha.kbn_value_lnm AS syuha_nm
    ,s.yu_basyo_cd
    ,s.yu_spot_nm
    ,jdm.shohin_nm as hitsugi_nm
    ,nitei.spot_cd
    ,nitei.basho_nm
    ,s.yukan_tel
    ,s.yu_basyo_cd
    ,s.yu_spot_nm
    ,s.v_free1
    ,s.v_free2
    ,s.v_free3
    ,s.v_free4
    ,s.v_free5
    ,s.n_free1
    ,s.k_free1
    ,s.k_free2
    ,s.item1_use_kbn -- 準備道具1
    ,s.item2_use_kbn -- 準備道具2
    ,s.item3_use_kbn -- 準備道具3
    ,s.item4_use_kbn -- 準備道具4
    ,s.item5_use_kbn -- 準備道具5
    ,s.item6_use_kbn -- 準備道具6
    ,s.item7_use_kbn -- 準備道具7
    ,s.item8_use_kbn -- 準備道具8
  FROM seko_yukan_info s
  LEFT JOIN seko_kihon_info kihon 
      ON s.seko_no = kihon.seko_no 
      AND kihon.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.yukan_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN code_nm_mst yukan 
      ON yukan.code_kbn = '0340' 
      AND s.yukan_cd = yukan.kbn_value_cd 
      AND yukan.delete_flg = 0
  LEFT JOIN code_nm_mst yu_te 
      ON yu_te.code_kbn = '0350' 
      AND s.yu_te_cd = yu_te.kbn_value_cd 
      AND yu_te.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
      ON syushi.code_kbn = '0240' 
      AND kihon.syushi_cd = syushi.kbn_value_cd 
      AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst syuha 
      ON syuha.code_kbn = '0200' 
      AND kihon.syuha_cd = syuha.kbn_value_cd
      AND syuha.delete_flg = 0
  LEFT JOIN juchu_denpyo_msi jdm
      ON s.seko_no = jdm.seko_no
      AND jdm.shohin_kbn = '0570'
      AND jdm.juchu_suryo > 0
      AND jdm.delete_flg = 0
  LEFT JOIN seko_nitei nitei
      ON s.seko_no = nitei.seko_no
      AND nitei.nitei_kbn = 3
      AND nitei.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
}