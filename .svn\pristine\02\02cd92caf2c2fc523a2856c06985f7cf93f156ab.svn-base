<?php

/**
 * DataMapper_Pdf0186
 *
 * PDF出力 引取・返金_引取確認書 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2022/08/xx
 * @filesource 
 */

/**
 * PDF出力 引取・返金_引取確認書 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2022/08/xx
 */
class DataMapper_Pdf0186 extends DataMapper_Abstract {

    /**
     * PDF 引取・返金_引取確認書 データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2022/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT
    *
FROM (
    SELECT 
         ski.seko_no
        ,nyuden1.kbn_value_lnm                                                  AS nyuden_nm1           -- 入電理由1
        ,nyuden2.kbn_value_lnm                                                  AS nyuden_nm2           -- 入電理由2
        ,nyuden3.kbn_value_lnm                                                  AS nyuden_nm3           -- 入電理由3
        ,skaf.v_free1                                                           AS nyudensaki_nm1       -- 入電先名1
        ,skaf.v_free2                                                           AS nyudensaki_nm2       -- 入電先名2
        ,skaf.v_free3                                                           AS nyudensha_nm         -- 入電者
        ,skaf.v_free8                                                           AS nyudensaki_tel1      -- 入電者TEL1
        ,skaf.v_free9                                                           AS nyudensaki_tel2      -- 入電者TEL2
        ,skaf.v_free10                                                          AS homonsaki_nm         -- 訪問先名
        ,skaf.tel_no1                                                           AS homonsaki_tel1       -- 訪問先TEL1
        ,skaf.mobile_tel1                                                       AS homonsaki_tel2       -- 訪問先TEL2
        ,skaf.addr1_1                                                           AS homonsaki_addr1      -- 訪問先住所1
        ,skaf.addr1_2                                                           AS homonsaki_addr2      -- 訪問先住所2
        ,ski.hs_anchi_nm                                                                                -- 安置先名
        ,ski.k_cif_no                                                                                   -- 故人顧客No.
        ,CASE 
            WHEN LENGTH(COALESCE(ski.k_last_knm,'')) > 0 AND LENGTH(COALESCE(ski.k_first_knm,'')) > 0 THEN CONCAT(ski.k_last_knm,' ',ski.k_first_knm)
            WHEN LENGTH(COALESCE(ski.k_last_knm,'')) > 0  THEN ski.k_last_knm
            WHEN LENGTH(COALESCE(ski.k_first_knm,'')) > 0 THEN ski.k_first_knm
            ELSE NULL
         END                                                                    AS k_knm                -- 故人名カナ
        ,CASE 
            WHEN LENGTH(COALESCE(ski.k_last_nm,'')) > 0 AND LENGTH(COALESCE(ski.k_first_nm,'')) > 0 THEN CONCAT(ski.k_last_nm,' ',ski.k_first_nm)
            WHEN LENGTH(COALESCE(ski.k_last_nm,'')) > 0  THEN ski.k_last_nm
            WHEN LENGTH(COALESCE(ski.k_first_nm,'')) > 0 THEN ski.k_first_nm
            ELSE NULL
         END                                                                    AS k_nm                 -- 故人名
        ,k_sex.kbn_value_lnm                                                    AS k_sex                -- 故人性別
        ,ski.kg_tel                                                                                     -- 故人TEL
        ,ski.free2_cd                                                           AS jizen_seko_no        -- 事前相談施行番号
        ,TO_CHAR(ski.k_seinengappi_ymd_y, 'yyyy年mm月dd日')                     AS k_seinengappi_ymd_y  -- 故人生年月日
        ,CASE 
            WHEN ski.k_nenrei_man IS NOT NULL THEN CONCAT(ski.k_nenrei_man,'才')
            ELSE NULL 
         END                                                                    AS k_nenrei_man         -- 故人年齢
        ,ski.kg_addr1                                                                                   -- 故人住所1
        ,ski.kg_addr2                                                                                   -- 故人住所2
        ,CASE 
            WHEN LENGTH(COALESCE(ski.v_free26,'')) > 0 AND LENGTH(COALESCE(ski.k_first_knm,'')) > 0 THEN CONCAT(ski.v_free26,' ',ski.v_free27)
            WHEN LENGTH(COALESCE(ski.v_free26,'')) > 0 THEN ski.v_free26
            WHEN LENGTH(COALESCE(ski.v_free27,'')) > 0 THEN ski.v_free27
            ELSE NULL
         END                                                                    AS izoku_knm            -- 遺族名カナ
        ,CASE 
            WHEN LENGTH(COALESCE(ski.v_free24,'')) > 0 AND LENGTH(COALESCE(ski.v_free25,'')) > 0 THEN CONCAT(ski.v_free24,' ',ski.v_free25)
            WHEN LENGTH(COALESCE(ski.v_free24,'')) > 0 THEN ski.v_free24
            WHEN LENGTH(COALESCE(ski.v_free25,'')) > 0 THEN ski.v_free25
            ELSE NULL
         END                                                                    AS izoku_nm             -- 遺族名
        ,ski.free4_cd                                                           AS izoku_cif_no         -- 遺族顧客No.
        ,kokyaku.addr1                                                          AS izoku_addr1
        ,kokyaku.addr2                                                          AS izoku_addr2
        ,ski.v_free28                                                           AS izoku_tel1           -- 遺族TEL1
        ,ski.v_free29                                                           AS izoku_tel2           -- 遺族TEL2
        ,skaf.v_free4                                                           AS renraku_nm           -- 連絡者のお名前
        ,r_zoku.kbn_value_lnm                                                   AS r_zoku_nm            -- 連絡者続柄
        ,skaf.v_free5                                                           AS renraku_tel          -- 連絡者携帯番号
        ,skaf.v_free5                                                           AS renraku_tel          -- 連絡者携帯番号
        ,ski.biko1                                                              AS seko_biko1           -- 連絡事項
        ,TO_CHAR(skaf.ts_free1 ,'HH24時MI分')                                   AS uketsuke_time        -- 受付日時刻
        ,uketsuke.tanto_nm                                                      AS uketsuke_tanto_nm    -- 見積担当者名
        ,s_tanto11.tanto_nm                                                     AS shutsudo_nm11        -- 出動者1-1
        ,s_tanto12.tanto_nm                                                     AS shutsudo_nm12        -- 出動者1-2
        ,s_tanto13.tanto_nm                                                     AS shutsudo_nm13        -- 出動者1-3
    FROM seko_kihon_info ski
    LEFT JOIN seko_kihon_all_free skaf  -- 施行基本フリー
    ON  skaf.seko_no    = ski.seko_no
    AND skaf.delete_flg = 0
    LEFT JOIN kokyaku_mst kokyaku
    ON  kokyaku.kokyaku_no = TRIM(ski.free4_cd)
    AND kokyaku.delete_flg = 0
    LEFT JOIN code_nm_mst k_sex         -- コード名称：性別
    ON  k_sex.code_kbn     = '0080'
    AND k_sex.kbn_value_cd = ski.k_sex_cd
    AND k_sex.delete_flg   = 0
    LEFT JOIN code_nm_mst r_zoku        -- コード名称：連絡者続柄
    ON  r_zoku.code_kbn         = '0190'
    AND r_zoku.kbn_value_cd_num = skaf.free2_kbn
    AND r_zoku.delete_flg       = 0
    LEFT JOIN code_nm_mst nyuden1       -- 入電理由1
    ON  nyuden1.code_kbn         = '9603'
    AND nyuden1.kbn_value_cd_num = skaf.free5_kbn
    AND nyuden1.delete_flg       = 0
    LEFT JOIN code_nm_mst nyuden2       -- 入電理由2
    ON  nyuden2.code_kbn         = '9603'
    AND nyuden2.kbn_value_cd_num = skaf.free6_kbn
    AND nyuden2.delete_flg       = 0
    LEFT JOIN code_nm_mst nyuden3       -- 入電理由3
    ON  nyuden3.code_kbn         = '9603'
    AND nyuden3.kbn_value_cd_num = skaf.free7_kbn
    AND nyuden3.delete_flg       = 0
    LEFT JOIN tanto_mst uketsuke        -- 見積担当者
    ON  uketsuke.tanto_cd   = ski.uketuke_tanto_cd
    AND uketsuke.delete_flg = 0
    LEFT JOIN tanto_mst s_tanto11       -- 出動者1-1
    ON  s_tanto11.tanto_cd   = ski.iso_tanto_cd1
    AND s_tanto11.delete_flg = 0
    LEFT JOIN tanto_mst s_tanto12       -- 出動者1-2
    ON  s_tanto12.tanto_cd   = ski.iso_tanto_cd2
    AND s_tanto12.delete_flg = 0
    LEFT JOIN tanto_mst s_tanto13       -- 出動者1-3
    ON  s_tanto13.tanto_cd   = ski.iso_tanto_cd3
    AND s_tanto13.delete_flg = 0
) T
WHERE $whereStr
$orderBy
$tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    /**
     * PDF出力 見積書(鑑) 取得
     *
     * <AUTHOR> Sai
     * @since      2015/01/28
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findHead($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT
    *
FROM
    (
        SELECT
            sk.seko_no
            ,sk.k_nm                            -- 故人名
            ,t4.tanto_nm    AS mitsu_tanto      -- 見積担当者
            ,sn4.nitei_ymd  AS kokubetsu_ymd
            ,bm.bumon_lnm
            ,bm.addr1_nm    AS bumon_addr1_nm
            ,bm.addr2_nm    AS bumon_addr2_nm
            ,bm.tel         AS bumon_tel
            ,kf.v_free12    AS keiyaku_nm       -- 契約団体名1
            ,hd.k_free3     AS kouza_shitei -- 返金指定口座 0:ゆうちょ銀行以外 1:ゆうちょ銀行 2:現金返金
            -- ゆうちょ銀行以外
            ,hd.text_free1  AS bank_nm    -- 金融機関名
            ,hd.v_free1     AS shiten_mei -- 支店名
            ,hd.k_free1     AS kouza_sbt0 -- 口座種別 0:普通 1:当座
            ,hd.v_free2     AS kouza_no0  -- 口座番号
            -- ゆうちょ銀行
            ,hd.k_free2     AS kouza_sbt1 -- 口座種別 0:総合 1:振替
            ,hd.v_free3     AS kigou      -- 記号
            ,hd.v_free4     AS kouza_no1  -- 番号
            ,hd.v_free5     AS kouza_nm   -- 口座名義
            ,htm.tanto_nm   AS henkin_tanto_nm -- 返金登録担当者 
        FROM
            seko_kihon_info sk
        LEFT JOIN hikitori_denpyo hd
        ON  hd.seko_no    = sk.seko_no
        AND hd.delete_flg = 0
        LEFT OUTER JOIN seko_nitei sn4
        ON  (
                sk.seko_no     = sn4.seko_no
            AND sn4.nitei_kbn  = 11 -- 告別式
            AND sn4.delete_flg = 0
        )
        LEFT OUTER JOIN seko_kihon_all_free kf
        ON  (
                sk.seko_no    = kf.seko_no
            AND kf.delete_flg = 0
        )
        LEFT OUTER JOIN tanto_mst t4
        ON  (
                kf.tanto_cd1  = t4.tanto_cd
            AND t4.delete_flg = 0
        )
        LEFT OUTER JOIN bumon_mst bm
        ON  (
                sk.bumon_cd   = bm.bumon_cd
            AND bm.delete_flg = 0
        )
        LEFT JOIN tanto_mst htm
        ON  htm.tanto_cd   = hd.tanto_cd
        AND htm.delete_flg = 0
    ) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }
    /**
     * PDF出力 引取返金確認書 明細書 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2022/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findMsi($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T2');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T3');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
                $orderBy = 'ORDER BY T3.dai_bunrui_cd, CASE WHEN T3.upgrade_kbn IN (1,2) THEN 1 ELSE 2 END, T3.mitumori_print_seq, T3.disp_no ';
        }
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
FROM (
SELECT
     T2.record_kbn
    ,T2.data_kbn
    ,T2.denpyo_no
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.disp_no -- 一行にまとめない
    ,MAX(T2.add_kbn)        AS  add_kbn
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.hoshi_disp
    ,T2.zei_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.denpyo_kbn
    ,ABS(SUM(T2.juchu_suryo))   AS  juchu_suryo
    ,T2.juchu_tnk
    ,ABS(SUM(T2.juchu_prc))     AS  juchu_prc
    ,ABS(SUM(T2.henkin_prc))    AS  henkin_prc
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.om_hendo_shohin_flg
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加
    ,T2.nebiki_ritu            -- 割引率
    ,ABS(SUM(T2.out_zei_prc))	AS out_zei_prc
    ,ABS(SUM(T2.in_zei_prc)) 	AS in_zei_prc
    ,T2.reduced_tax_rate AS reduced_tax_rate   -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
FROM (
SELECT
     CASE WHEN plan_smsi.service_kbn IN (4,5) THEN 2 ELSE 0 END AS  record_kbn
    ,h.data_kbn
    ,h.denpyo_no
    ,h.seko_no
    ,h.seko_no_sub
    ,d.disp_no
    ,d.add_kbn
    ,d.denpyo_kbn
    ,d.chu_bunrui_cd
    ,d.shohin_kbn
    ,d.shohin_cd
    ,CASE WHEN shohin.nm_input_kbn in (2,4) THEN d.shohin_nm
        ELSE shohin.shohin_nm END AS shohin_nm --商品名（見積書は簡易商品名を表示）
    ,COALESCE(d.shohin_tkiyo_nm, '') AS  shohin_tkiyo_nm
    ,CASE WHEN d.hoshi_prc <> 0 THEN 'サービス料対象' ELSE '' END AS  hoshi_disp
    ,CASE WHEN d.zei_Kbn = 0 THEN '非' WHEN d.zei_Kbn = 1 THEN '内' ELSE '' END AS  zei_disp
    ,d.zei_kbn
    ,d.zei_cd
    ,d.juchu_suryo
    ,d.uri_tnk AS juchu_tnk
    ,d.uri_prc AS juchu_prc
    ,d.uri_prc - d.gojokai_nebiki_prc - d.nebiki_prc AS henkin_prc
    ,chu_bunrui.chu_bunrui_nm
    ,tani.tani_nm
    ,sho_kbn.shohin_kbn_nm AS koumoku
    ,sho_kbn.om_hendo_shohin_flg
    ,CASE WHEN kihon.gojokai_kbn = 6 AND plan_smsi.service_kbn = 1 AND d.add_kbn = 9 THEN 0 ELSE plan_smsi.service_kbn END service_kbn
    ,d.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,stm.siire_tnk
    ,sbm.hachu_kbn
    ,sbm.print_group_cd         --20151207追加
    ,sbm.mitumori_print_seq     --20151207追加
    ,d.gojokai_nebiki_prc       --20151207追加
    ,d.nebiki_prc               --20151207追加
    ,d.n_free2 AS nebiki_ritu   -- 割引率
    ,d.out_zei_prc
    ,d.in_zei_prc  
    ,d.reduced_tax_rate   -- 20190430 mihara keigen
    ,shohin.gazo_img
    ,shohin.kashidashi_kbn
    ,d.upgrade_kbn
    ,d.plan_shohin_cd           --アップデート元商品コード
    ,p_shohin.kani_shohin_nm AS plan_shohin_nm --アップデート元商品名（見積書は簡易商品名を表示）
  FROM hikitori_denpyo h
  INNER JOIN hikitori_denpyo_msi d 
        ON  h.uri_den_no = d.uri_den_no
        AND d.delete_flg = 0
  LEFT JOIN seko_kihon_info kihon
        ON h.seko_no            = kihon.seko_no
        AND kihon.delete_flg    = 0
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
        ON d.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd
        AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn
        ON  d.shohin_kbn = sho_kbn.shohin_kbn
        AND sho_kbn.delete_flg = 0
  INNER JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn  = 0
        AND shohin.delete_flg   = 0
  LEFT JOIN shohin_mst p_shohin 
        ON d.plan_shohin_cd = p_shohin.shohin_cd
        AND d.plan_shohin_bumon_cd = p_shohin.bumon_cd
        AND p_shohin.hihyoji_kbn  = 0
        AND p_shohin.delete_flg   = 0
  LEFT JOIN tani_mst tani 
        ON d.tani_cd = tani.tani_cd
        AND tani.delete_flg = 0
  LEFT JOIN seko_plan_smsi_mst plan_smsi 
        ON h.seko_plan_cd = plan_smsi.seko_plan_cd
        AND d.msi_no = plan_smsi.seko_plan_uchiwk_no
        AND d.shohin_bumon_cd = plan_smsi.bumon_cd
        AND d.dai_bunrui_cd = plan_smsi.dai_bunrui_cd
        AND d.chu_bunrui_cd = plan_smsi.chu_bunrui_cd
        AND d.shohin_kbn    = plan_smsi.shohin_kbn
        AND d.shohin_cd     = plan_smsi.shohin_cd
        AND TO_CHAR(plan_smsi.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND TO_CHAR(plan_smsi.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND kihon.gojokai_kbn = plan_smsi.gojokai_kbn
        AND plan_smsi.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON d.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
  LEFT JOIN 
        shohin_bunrui_mst sbm
        ON  (
            d.dai_bunrui_cd = sbm.dai_bunrui_cd
        AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
        AND d.shohin_bumon_cd = sbm.bumon_cd
        AND d.shohin_kbn  = sbm.shohin_kbn
        AND d.shohin_cd  = sbm.shohin_cd
        AND sbm.delete_flg = 0
        )
  LEFT JOIN 
        shohin_tanka_mst stm
        ON  d.shohin_bumon_cd = stm.bumon_cd
        AND d.shohin_cd  = stm.shohin_cd
        AND stm.delete_flg = 0
        AND TO_CHAR(d.juchu_ymd,'YYYY/MM/DD') BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')
 WHERE d.juchu_suryo <> 0 AND h.delete_flg = 0
UNION ALL
-- 奉仕料
SELECT
     1                  AS  record_kbn
    ,T1.data_kbn
    ,T1.denpyo_no
    ,T1.seko_no
    ,T1.seko_no_sub
    ,NULL               AS  disp_no
    ,NULL               AS  add_kbn
    ,NULL               AS  denpyo_kbn
    ,T1.chu_bunrui_cd
    ,NULL AS shohin_kbn
    ,NULL AS shohin_cd
    ,(select service_nm from kaisya_info limit 1) || '（' || hos.zei_rtu || '％）' AS shohin_nm
    ,NULL AS shohin_tkiyo_nm
    ,NULL               AS  hoshi_disp　
    ,NULL               AS  zei_disp
    ,NULL               AS  zei_kbn
    ,NULL               AS  zei_cd
    ,NULL               AS  juchu_suryo
    ,NULL               AS  juchu_tnk
    ,T1.hoshi_prc       AS  juchu_prc
    ,NULL               AS  henkin_prc
    ,chu_bunrui.chu_bunrui_nm
    ,NULL               AS  tani_nm
    ,NULL               AS  koumoku
    ,NULL               AS  om_hendo_shohin_flg
    ,NULL               AS  service_kbn
    ,T1.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,T1.siire_tnk
    ,T1.hachu_kbn
    ,T1.print_group_cd         --20151207追加
    ,T1.mitumori_print_seq     --20151207追加
    ,T1.gojokai_nebiki_prc     --20151207追加
    ,T1.nebiki_prc             --20151207追加
    ,NULL               AS nebiki_ritu
    ,NULL               AS out_zei_prc 
    ,NULL               AS in_zei_prc
    ,NULL AS reduced_tax_rate   -- 20190430 mihara keigen
    ,NULL               AS  gazo_img
    ,0                  AS  kashidashi_kbn
    ,NULL AS upgrade_kbn
    ,NULL AS plan_shohin_cd           --アップデート元商品コード
    ,NULL AS plan_shohin_nm           --アップデート元商品名
  FROM ( -- 奉仕料
    SELECT
      h.data_kbn
     ,h.denpyo_no
     ,h.seko_no
     ,h.seko_no_sub
     ,d.chu_bunrui_cd
     ,d.dai_bunrui_cd
     ,d.hoshi_ritu_cd
     ,SUM(d.hoshi_prc) AS   hoshi_prc
     ,SUM( CASE WHEN d.hoshi_umu_kbn = 1 THEN d.uri_prc ELSE 0 END) AS juchu_prc
     ,0 AS siire_tnk
     ,0 AS hachu_kbn
     ,0 AS print_group_cd        --20151207追加
     ,MAX(mitumori_print_seq) AS mitumori_print_seq     --20151207追加
     ,0 AS gojokai_nebiki_prc    --20151207追加
     ,0 AS nebiki_prc            --20151207追加
    FROM hikitori_denpyo h
    INNER JOIN hikitori_denpyo_msi d 
        ON h.uri_den_no = d.uri_den_no 
        AND d.delete_flg = 0
    INNER JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd 
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn = 0
        AND shohin.delete_flg = 0
    INNER JOIN 
          shohin_bunrui_mst sbm
          ON  (
              d.dai_bunrui_cd = sbm.dai_bunrui_cd
          AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
          AND d.shohin_bumon_cd = sbm.bumon_cd
          AND d.shohin_kbn  = sbm.shohin_kbn
          AND d.shohin_cd  = sbm.shohin_cd
          AND sbm.delete_flg = 0
          )
    WHERE   h.delete_flg = 0
    GROUP BY h.data_kbn
        , h.denpyo_no
        , h.seko_no
        , h.seko_no_sub
        , d.chu_bunrui_cd
        , d.dai_bunrui_cd
        , d.hoshi_ritu_cd
    HAVING SUM(d.hoshi_prc) <> 0
  ) T1
  INNER JOIN hoshi_ritu_mst hos 
      ON    T1.hoshi_ritu_cd = hos.hoshi_ritu_cd
      AND   0                = hos.delete_flg       
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
      ON T1.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd 
      AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON T1.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
) T2
 WHERE $whereStr
 GROUP BY
     T2.record_kbn
    ,T2.data_kbn
    ,T2.denpyo_no
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.juchu_tnk
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.om_hendo_shohin_flg
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.hoshi_disp
    ,T2.zei_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加    
    ,T2.nebiki_ritu
    ,T2.disp_no
    ,T2.denpyo_kbn
    ,T2.reduced_tax_rate        -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
) T3  
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }
}
