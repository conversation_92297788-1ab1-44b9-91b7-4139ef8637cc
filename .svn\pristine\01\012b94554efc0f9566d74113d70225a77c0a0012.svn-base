<?php
  /**
   * Msi_Sys_SKaisyaConf
   *
   * 会社設定値取得
   *
   * @category   library
   * @package    library\Msi_Sys
   * <AUTHOR> Mihara
   * @since      2016/06/xx
   * @filesource 
   */

  /**
   * 会社設定値取得
   *
   * s_kaisya テーブル(等)から会社毎の設定値を取得する
   *
   * @category   library
   * @package    library\Msi_Sys
   * <AUTHOR> Mihara
   * @since      2016/06/xx
   */
class Msi_Sys_SKaisyaConf
{
    /**
     * @ignore
     */
    private $_sKaisyaDb = null;

    /**
     * コンストラクタ
     * 
     * <AUTHOR> Mihara
     * @since      2016/06/xx
     */
    protected function __construct()
    {
        $this->_sKaisyaDb = array();
    }

    /**
     * @ignore
     */
    private static $_obj = null;

    /**
     * オブジェクト取得
     *
     * <AUTHOR> Mihara
     * @since      2016/06/xx
     * @return Msi_Sys_SKaisyaConf
     */
    protected static function _getInstance()
    {
        if ( static::$_obj === null ) {
            static::$_obj = new self();
        }

        return static::$_obj;
    }

    /**
     * s_kaisya テーブルから１レコード取得
     * 
     * <AUTHOR> Mihara
     * @since      2016/06/xx
     * @param      string $kaisyaCd
     * @return     array|null(レコードなし)
     */
    protected function getSkaisyaRec( $kaisyaCd )
    {
        if ( ! array_key_exists($kaisyaCd, $this->_sKaisyaDb) ) {
            $sysdb = Msi_Sys_DbManager::getMyDb('sysdb');
            $sel = DataMapper_SKaisya::find2( $sysdb, array('kaisya_cd' => $kaisyaCd) );
            if ( count($sel) === 0 ) {
                return null;
            }
            $this->_sKaisyaDb[ $kaisyaCd ] = $sel[0];
        }
        return $this->_sKaisyaDb[ $kaisyaCd ];
    }

    /**
     * 設定値を取得する
     * 
     * <AUTHOR> Mihara
     * @since      2016/06/xx
     * @param      string $key
     * @param      string $kaisyaCd  省略値は現在ログインしている会社
     * @return     mixed(null:設定なし)
     */
    public static function getConf( $key, $kaisyaCd=null )
    {
        if ( $kaisyaCd === null ) {
            $kaisyaCd = Msi_Sys_Utils::getKaisyaCd();
            if ( $kaisyaCd === '-' || strlen($kaisyaCd) <= 0 ) { // ログインしていない
                return null;
            }
        }

        $obj = self::_getInstance();

        $rec = $obj->getSkaisyaRec( $kaisyaCd );

        if ( $rec === null ) {
            return null;
        }

        if ( array_key_exists($key, $rec) ) {
            return $rec[ $key ];
        }

        return null;
    }


}
