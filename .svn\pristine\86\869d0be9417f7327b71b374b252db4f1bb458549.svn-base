<?php

/**
 * App_Utils2
 *
 * App 用ユーティリティ関数
 * 会社毎にロジックが異なる場合、関数を作成する
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> Sai
 * @since      2015/05/07
 * @version    2015/05/28 Mihara adjGojokaiNebiki(互助会値引マスタによる値引額調整)追加
 * @version    2015/06/08 Mihara adjGojokaiNebiki3 追加
 * @version    2015/06/10 Mihara adjGojokaiNebiki3 値引対象とする数量のロジック変更
 * @version    2015/06/11 Mihara adjGojokaiNebiki3 互助会値引マスタ割引で seko_gojokai_info.nebiki_gojokai_kbn を優先させる
 * @version    2017/06/22 Kobayashi sagakuKensho 施行プラン値引マスタで差額の設定がある場合は差額を適用
 * @filesource
 */

/**
 * App 用ユーティリティ関数
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> Sai
 * @since      2015/05/07
 */
final class App_Utils2 extends App_Utils2Abstract {

    /**
     * 消費税等情報を反映した見積・請求金額を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/16
     * @param   number $prc_sum
     * @param   array $shohizei
     * @return  number 金額
     */
    public static function getJuchuSum($prc_sum, $shohizei, $sekoNo, $kbn, $history_no) {
        $prc = $prc_sum + $shohizei[App_MitsuLib::ID_ZEIPRC];	// 消費税
        $prc += $shohizei[App_MitsuLib::ID_AZUKARI_DAN];		// 預り金（壇払い）
        $prc += $shohizei[App_MitsuLib::ID_AZUKARI_HEN];		// 預り金（返礼品）
        $prc += $shohizei[App_MitsuLib::ID_EARLYUSE];			// 早期利用費
        $prc += $shohizei[App_MitsuLib::ID_EARLYUSEZEI];		// 早期利用費の消費税
        //2017/04/23 DEL Kayo $prc += $shohizei[App_MitsuLib::ID_KAKEZAN];			// （佐野）掛金残
        //2017/04/23 DEL Kayo $prc += $shohizei[App_MitsuLib::ID_KAKEZANZEI];		// （佐野）掛金残消費税
		$prc += $shohizei[App_MitsuLib::ID_GOJOHARAI];          // 互助会払込金額(充当金)
        $prc += $shohizei[App_MitsuLib::ID_ZENNOWARI];          // 前納割引(完納割引)
        $prc += $shohizei[App_MitsuLib::ID_CHOUI_COST];         // 弔慰金
        //Msi_Sys_Utils::debug( 'shohizei=>' . Msi_Sys_Utils::dump($shohizei) );


        return $prc;
    }

    /**
     * 互助会値引マスタによる値引額調整(3)
     * 見積の割引を一行にまとめる対応
     * ※商品選択画面から受注した際に伝票明細に保存する会員金額の計算
     *
     * <AUTHOR> Kayo
     * @since 2017/06/04
     * @param  string $seko_no
     * @param  array  $data
     * @return void
     */
    public static function adjGojokaiNebiki3($seko_no, &$data) {
        $db = Msi_Sys_DbManager::getMyDb();
        // 会員コースコードの取得
        $sekoRec = $db->easySelOne(<<< END_OF_SQL
SELECT gojokai_kbn
	, gojokai_cose_cd
	, kaisya_cd			-- 2017/06/04 ADD Kayo
FROM seko_kihon_info
WHERE seko_no=:seko_no
AND delete_flg=0
END_OF_SQL
        , array('seko_no' => $seko_no));

        if ($sekoRec === null) {
            return;
        }
        $gojokai_kbn = $sekoRec['gojokai_kbn'];
        if ($gojokai_kbn === null) {
            return;
        }

        $nebiki_gojokai_kbn = null;
        $sekoGojokaiInfoRec = $db->easySelOne(<<< END_OF_SQL
SELECT seko_no, nebiki_group_no, nebiki_join_cnt, nebiki_gojokai_kbn
  FROM seko_gojokai_info
 WHERE seko_no=:seko_no
  AND delete_flg=0
END_OF_SQL
                , array('seko_no' => $seko_no));
        if ($sekoGojokaiInfoRec) {
            $nebiki_gojokai_kbn = $sekoGojokaiInfoRec['nebiki_gojokai_kbn'];
        }
//        $use_cnt = static::getUseCount($db, $seko_no);
        $sp_basho_rec = $db->easySelOne(<<< END_OF_SQL
SELECT basho_cd, basho_nm FROM seko_nitei
 WHERE seko_no=:seko_no AND delete_flg=0 AND nitei_kbn=7 -- 葬儀
END_OF_SQL
                , array('seko_no' => $seko_no));
        $sp_basho_cd = null;
        if ($sp_basho_rec) {
            $sp_basho_cd = $sp_basho_rec['basho_cd'];
        }
        //Msi_Sys_Utils::debug( 'adjGojokaiNebiki3==>' . Msi_Sys_Utils::dump($data) );
        foreach ($data as &$rec) {
			if (isset($rec['item']))	{
				$shohin_cd = $rec['item'];
			} else {
				$shohin_cd = $rec['shohin_cd'];
			}	
			if (isset($rec['price']))	{
				if ($rec['price'] == 0) {
					$rec['gojokai_nebiki_prc'] = 0;
					continue;
				}
			} else {
				if ($rec['juchu_prc'] == 0) {
					$rec['gojokai_nebiki_prc'] = 0;
					continue;
				}
			}	
            // 互助会値引マスタ由来の値引行
            // 2015/06/11 seko_gojokai_info.nebiki_gojokai_kbn で探す処理の追加
            $nebikiRec = null;
            if (!$nebikiRec) { // 次に seko_kihon_info.gojokai_kbn で探す
                $nebikiRec = DataMapper_GojokaiNebiki::findOne($db
						, array('shohin_cd'		=> $shohin_cd,
								'gojokai_kbn'	=> $gojokai_kbn,
								'kaijyo_cd'		=> $sp_basho_cd,
								'kaisya_cd'		=> $sekoRec['kaisya_cd']
							));

                if (!$nebikiRec) {
                    $nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
							array('shohin_cd' => $shohin_cd,
								  'gojokai_kbn' => $gojokai_kbn,
								'kaisya_cd'		=> $sekoRec['kaisya_cd']
						));
                }
				if (!isset($nebikiRec) || count($nebikiRec)<=0) {
					// 会場別の場合 2018/07/16 ADD Kayo
					if (isset($rec['shohin_cd_kaijyo']) && strlen($rec['shohin_cd_kaijyo']) > 0)	{
						$nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
									array('shohin_cd'   => $rec['shohin_cd_kaijyo'],
										  'gojokai_kbn' => $gojokai_kbn,
										  'kaisya_cd'	=> $sekoRec['kaisya_cd']
									));
					}	
				}
            }

            if ($nebikiRec) {
                $nebiki_prc = 0;
                if ($nebikiRec['nebiki_kbn'] == 1) { // 金額値引
                    $nebiki_prc = $nebikiRec['nebiki_prc'];
                } else if ($nebikiRec['nebiki_kbn'] == 0) { // 率による値引
                    $nebiki00 = $rec['price'] * $rec['quantity'] * $nebikiRec['nebiki_ritu'] / 100;
                    $nebiki_prc = floor($nebiki00); // 小数点切り捨て
                    if ($nebiki_prc > 0) {
                        $nebiki_prc = -1 * $nebiki_prc;
                    }
                } else if ($nebikiRec['nebiki_kbn'] == 3) { // 単価×数量での値引き
                    $quantity = 0;
                    if ($nebikiRec['nebiki_suryo'] <= $rec['quantity']) {
                        $quantity = $nebikiRec['nebiki_suryo'];
                    } else {
                        $quantity = $rec['quantity'];
                    }
                    $nebiki_prc = $nebikiRec['nebiki_prc'] * $quantity;
                } else if ($nebikiRec['nebiki_kbn'] == 4) { // 販売単価×数量での値引き
                    if (isset($rec['quantity'])) {
                        $quantity = 1;
                        if ($nebikiRec['nebiki_suryo'] <= $rec['quantity']) {
                            $quantity = $nebikiRec['nebiki_suryo'];
                        } else {
                            $quantity = $rec['quantity'];
                        }
                        if ($rec['price'] > 0) {
                            $nebiki_prc = $rec['price'] * $quantity;
                            $nebiki_prc = -1 * $nebiki_prc;
                        }
                    }
                }
                $rec['gojokai_nebiki_prc'] = $nebiki_prc;
                $rec['nebiki_prc_update_flg'] = 'false'; // 商品選択画面でこの項目は更新する必要がないので、このフラグで判定する

        //Msi_Sys_Utils::debug( 'adjGojokaiNebiki1==>' . Msi_Sys_Utils::dump($rec) );
                //差額が設定されているか検証    2017/06/22 ADD Kobayashi
				if (isset($rec['item']))	{
					static::sagakuKensho2($db, $rec, $seko_no, $rec['item'], 'price');
				} else {
					static::sagakuKensho2($db, $rec, $seko_no, $rec['shohin_cd'], 'price');
				}	
        //Msi_Sys_Utils::debug( 'adjGojokaiNebiki2==>' . Msi_Sys_Utils::dump($rec) );
            }
        }
    }

    /**
     * 互助会値引マスタによる値引額調整(3)
     * 見積の割引を一行にまとめる対応
     * ※商品選択画面に表示される会員金額の計算（商品一つ分）
     *
     * <AUTHOR> sai
     * @since 2015/11/26
     * @param  string $seko_no
     * @param  array  $data
     * @return void
     */
    public static function adjGojokaiNebiki4($seko_no, &$data) {
        $db = Msi_Sys_DbManager::getMyDb();
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        if ( App_Utils::isFukusuKaisyaKanri() ) {
            $kaisyaWhere = " AND m.kaisya_cd='" . $curKaisyaCd . "' AND w1.kaisya_cd='" . $curKaisyaCd . "' ";
            $shohinTankaMstWhere = " AND tk.kaisya_cd='$curKaisyaCd'";
            $shohinBunruiMstWhere = " AND br.kaisya_cd='$curKaisyaCd'";
			$zeiMstWhere = " AND z.kaisya_cd='$curKaisyaCd'"; // 2017/06/01 ADD Otake
            $kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2017/01/28 ADD Kayo
            $kaisya = DataMapper_KaisyaInfo::findOne($db, array('kaisya_cd' => $kaisyacd));
        } else {
			$kaisya = DataMapper_KaisyaInfo::findOne($db);
		}
		//
		$shohin_tanka_disp_char = null;		// 商品単価文字		2017/01/28 ADD Kayo
		if (count($kaisya) > 0)	{
			$shohin_tanka_disp_char= $kaisya['shohin_tanka_disp_char'];		// 商品単価文字		2017/01/28 ADD Kayo
		}
        // 会員コースコードの取得
        $sekoRec = $db->easySelOne(<<< END_OF_SQL
SELECT gojokai_kbn
	, gojokai_cose_cd
	, TO_CHAR(sougi_ymd,'YYYY/MM/DD') AS sougi_ymd
	, kaisya_cd		-- 2017/06/04 ADD Kayo
FROM seko_kihon_info
WHERE seko_no=:seko_no
AND delete_flg=0
END_OF_SQL
        , array('seko_no' => $seko_no));

        if ($sekoRec === null) {
            return;
        }
        $gojokai_kbn = $sekoRec['gojokai_kbn'];
        if ($gojokai_kbn === null) {
            return;
        }

        $nebiki_gojokai_kbn = null;
        $sekoGojokaiInfoRec = $db->easySelOne(<<< END_OF_SQL
SELECT seko_no, nebiki_group_no, nebiki_join_cnt, nebiki_gojokai_kbn
  FROM seko_gojokai_info
 WHERE seko_no=:seko_no
  AND delete_flg=0
END_OF_SQL
        , array('seko_no' => $seko_no));
        if ($sekoGojokaiInfoRec) {
            $nebiki_gojokai_kbn = $sekoGojokaiInfoRec['nebiki_gojokai_kbn'];
        }
//        $use_cnt = static::getUseCount($db, $seko_no);
        $sp_basho_rec = $db->easySelOne(<<< END_OF_SQL
SELECT basho_cd, basho_nm
FROM seko_nitei
WHERE seko_no=:seko_no
AND delete_flg=0
AND nitei_kbn=7 -- 葬儀
END_OF_SQL
                , array('seko_no' => $seko_no));
        $sp_basho_cd = null;
        if ($sp_basho_rec) {
            $sp_basho_cd = $sp_basho_rec['basho_cd'];
        }
		$sougi_ymd = $sekoRec['sougi_ymd'];
		if (strlen($sougi_ymd) <= 0)	{
			$sougi_ymd = Msi_Sys_Utils::getDate();
		}
        
        $plan = $db->easySelOne(<<< END_OF_SQL
SELECT  seko_plan_cd
FROM    seko_kihon_info
WHERE   seko_no             =   :seko_no
AND     delete_flg          =   0
END_OF_SQL
        , array('seko_no' => $seko_no));
        // 施行プラン商品明細マスタの商品に含まれている、サービス区分＝２は、単価分を互助会値引きする。
		$seko_plan = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM seko_plan_smsi_mst
WHERE kaisya_cd     =:kaisya_cd
AND seko_plan_cd    =:seko_plan_cd
AND delete_flg      = 0
AND service_kbn     = 2
ORDER BY 	seko_plan_uchiwk_no
END_OF_SQL
        , array('kaisya_cd'   => $curKaisyaCd
			   ,'seko_plan_cd'=> $plan['seko_plan_cd'] ));
		$i = -1;
        foreach ($data as $rec) {
			$i++;
            if (isset($rec['kaiin_prc_get_kbn']) && $rec['kaiin_prc_get_kbn'] == 0)    {   // 0:互助会値引きマスタを反映する 1:互助会値引きマスタを反映しない
				$shohin_cd = $rec['shohin_cd'];
				$nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
						array('shohin_cd'	=> $shohin_cd
							 ,'gojokai_kbn' => $gojokai_kbn
							 ,'kaijyo_cd'	=> $sp_basho_cd
							 ,'kaisya_cd'	=> $sekoRec['kaisya_cd']));

				if (!isset($nebikiRec) || count($nebikiRec)<=0) {	// PHP Warning 2021/01/31 UPD Kayo
					$nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
								array('shohin_cd'   => $shohin_cd,
									  'gojokai_kbn' => $gojokai_kbn,
									  'kaisya_cd'	=> $sekoRec['kaisya_cd']
								));
					if (!isset($nebikiRec) ||count($nebikiRec)<=0) {	// PHP Warning 2021/01/31 UPD Kayo
						// 会場別の場合 2018/07/16 ADD Kayo
						if (isset($rec['shohin_cd_kaijyo']) && strlen($rec['shohin_cd_kaijyo']) > 0)	{
						$nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
									array('shohin_cd'   => $rec['shohin_cd_kaijyo'],
										  'gojokai_kbn' => $gojokai_kbn,
										  'kaisya_cd'	=> $sekoRec['kaisya_cd']
									));
						}
					}
				}

				$nebiki_prc = 0;
				if (isset($nebikiRec) && count($nebikiRec)>0) {
					 if ($nebikiRec['nebiki_kbn'] == 1) { // 金額値引
						$nebiki_prc = $nebikiRec['nebiki_prc'];
					} else if ($nebikiRec['nebiki_kbn'] == 0) { // 率による値引
						$nebiki00 = $rec['hanbai_tnk'] * $nebikiRec['nebiki_ritu'] / 100;
						$nebiki_prc = floor($nebiki00); // 小数点切り捨て
						if ($nebiki_prc > 0) {
							$nebiki_prc = -1 * $nebiki_prc;
						}
					} else if ($nebikiRec['nebiki_kbn'] == 3) { // 互助会値引額×数量での値引き（互助会値引きの対象数量以上になると値引きは無し）
						if ($rec['quantity'] <= $nebikiRec['nebiki_suryo']) {
							$nebiki_prc = $nebikiRec['nebiki_prc'];
						} else {
							$nebiki_prc = 0;
						}
					} else if ($nebikiRec['nebiki_kbn'] == 4) { // 販売単価×数量での値引き（互助会値引きの対象数量以上になると値引きは無し）
						if ($rec['quantity'] <= $nebikiRec['nebiki_suryo']) {
							$nebiki_prc = -1 * $rec['hanbai_tnk'];
						} else {
							$nebiki_prc = 0;
						}
					}
					//Msi_Sys_Utils::debug( 'hanbai_tnk==>' .  $rec['hanbai_tnk']);
					//Msi_Sys_Utils::debug( 'nebiki_prc==>' .  $nebiki_prc);
					$rec['kaiin_prc']	= intval($rec['hanbai_tnk']) + intval($nebiki_prc);
					if ($rec['kaiin_prc'] < 0)	{
						$rec['kaiin_prc'] = 0;
                        if  ($rec['sagaku_keisan_grp'] != '00000') {    // 2017/11/09 ADD Kayo
                            $rec['course_str']	= $shohin_tanka_disp_char;	// Web表示コース
                            $rec['is_course']	= 3;
                        }
					}
					//Msi_Sys_Utils::debug( 'kaiin_prc==>' .  $rec['kaiin_prc']);
					// 消費税を計算
					$tax = App_ClsTaxLib::GetCalcTax($db, $sougi_ymd, $rec['kaiin_prc'], $rec['uri_zei_kbn']);
					$rec['kaiin_prc_zei']= intval($rec['kaiin_prc']) + intval($tax['ZeiPrc']);	// 販売単価（税込）
					$rec['gojokai_nebiki_prc']	  = $nebiki_prc;
					$rec['nebiki_prc_update_flg'] = 'false'; // 商品選択画面でこの項目は更新する必要がないので、このフラグで判定する
					$rec['nebiki_kbn'] = $nebikiRec['nebiki_kbn'];
					$rec['nebiki_ritu']= $nebikiRec['nebiki_ritu'];
					if($nebiki_prc !== 0){
						$rec['kaiin_prc_disp_kbn'] = 1;
					}
				}
            }
			//差額が設定されているか検証    2017/06/22 ADD Kobayashi
			static::sagakuKensho1($db, $rec, $seko_no, $rec['shohin_cd'], $shohin_tanka_disp_char);
            
            foreach ($seko_plan as $plan_rec) { // 施行プランサービス区分互助会値引き
				if ($rec['shohin_cd'] == $plan_rec['shohin_cd']) {
                    if($rec['quantity'] <= $plan_rec['suryo']){
                        $rec['hanbai_tnk'] = $plan_rec['hanbai_tnk'];
                        $rec['gojokai_nebiki_prc'] = -1 * $plan_rec['hanbai_tnk'];
                        $rec['kaiin_prc'] = 0;
                        $rec['kaiin_prc_zei'] = 0;
                        $rec['kaiin_prc_disp_kbn'] = 1;
                        break;
                    }
				}
			}
			//$rec['kaiin_prc_disp_kbn'] = 1;
			$data[$i] = $rec;
			//Msi_Sys_Utils::debug( 'rec==>' . Msi_Sys_Utils::dump($rec) );
        }
        Msi_Sys_Utils::debug( 'data2018/08/29==>' . Msi_Sys_Utils::dump($data) );
    }

    /**
     * プランマスタ「サービス区分」による互助会値引き設定
     *
     * <AUTHOR> Kobayashi
     * @since 2018/02/13
     * @param  array  $data
     * @param  string $sekoplan
     * @return void
     */
    public static function adjGojokaiNebiki5(&$data) {
        foreach ($data as &$rec) {
            $service_kbn = isset($rec['_sekoplan_rec']['service_kbn']) ? $rec['_sekoplan_rec']['service_kbn'] : '' ;
            if ($service_kbn === '2') { // 「互助会値引き」の場合は請求額分を互助会値引きとして設定
                    $rec['gojokai_nebiki_prc'] = -1 * $rec['price'] * $rec['quantity'];
            }
        }
    }

    /**
     *
     * 基本プランのパターンを求める。
     *
     * <AUTHOR> Kayo
     * @since 2016/12/09
     * @version 2015/07/17 Juchu_JuchuAbstractより引越し
     * @param string $sogi  葬儀区分 1:個人 2:社葬 3:寺院葬
     * @param string $keishiki 形式 1:個人葬 2:団体葬（合同葬）3:家族葬 4:火葬式 5:お別れ会
     * @param string $anchi 施行打合せ「会員」
     * @param string $sikijyo 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
     * @param string $plan プラン選択 1:一般 2:一般(まごころ) 3:一般(互助会) 4：:一般(ハート共済)
     * @return array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     */
    public static function calcKihonPtn($sogi, $keishiki, $anchi, $sikijyo, $plan) {
        $main_pt_cd  = null;
        $main_pt_kbn = null;
        
        //  ------ 株式会社 セレモ -------
        // 斎場用も１～13型を表示するように修正 2018/08/06 Kayo
        if ($anchi === '0') {
            // 0:一般
            if ($plan == '2') {
                // 2:一般(まごころ)
                if ($sikijyo === '03') {
                    // 斎場用
                    $main_pt_cd     = '15';
                    $main_pt_kbn    = '15';
                } else {
                    // ホール用
                    $main_pt_cd     = '15';
                    $main_pt_kbn    = '15';
                } 
            } else if ($plan == '3') {
                // 3:一般(互助会)
                if ($sikijyo === '03') {
                    // 斎場用
                    $main_pt_cd     = '40';
                    $main_pt_kbn    = '40';
                } else {
                    // ホール用
                    $main_pt_cd     = '40';
                    $main_pt_kbn    = '40';
                }
            } else if ($plan == '4') {
                // 4:一般(ハート共済)
                if ($sikijyo === '03') {
                    // 斎場用
                    $main_pt_cd     = '42';
                    $main_pt_kbn    = '42';
                } else {
                    // ホール用
                    $main_pt_cd     = '42';
                    $main_pt_kbn    = '42';
                }
            } else if ($plan == '5') {
                $main_pt_cd = '50';
                $main_pt_kbn = '50';
            } else {
                // 1 or 未設定:一般
                $main_pt_cd     = '0';
                $main_pt_kbn    = '0';
            }
        } else if ($anchi === '1') {
            // 1:互助会
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '1';
                $main_pt_kbn    = '1';
           } else {
                // ホール用
                $main_pt_cd     = '1';
                $main_pt_kbn    = '1';
           }
        } else if ($anchi === '2') {
            // 2:ハート共済
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '3';
                $main_pt_kbn    = '3';
           } else {
                // ホール用
                $main_pt_cd     = '3';
                $main_pt_kbn    = '3';
           }
        } else if ($anchi === '3') {
            // 3:まごころ
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '5';
                $main_pt_kbn    = '5';
           } else {
                // ホール用
                $main_pt_cd     = '5';
                $main_pt_kbn    = '5';
           }
        } else if ($anchi === '4') {
            // 4:ゴールド
            $main_pt_cd     = '7';
            $main_pt_kbn    = '7';
        } else if ($anchi === '5') {
            // 5:チラシプラン
            $main_pt_cd     = '8';
            $main_pt_kbn    = '8';
        } else if ($anchi === '6') {
            // 6:福祉
            $main_pt_cd     = '12';
            $main_pt_kbn    = '12';
            // 7:その他
            // 8:小さなお葬式
        } else if ($anchi === '10') {
            // 10:いずみ
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '9';
                $main_pt_kbn    = '9';
           } else {
                // ホール用
                $main_pt_cd     = '9';
                $main_pt_kbn    = '9';
           }
        } else if ($anchi === '11') {
            // 11:しんらい
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '11';
                $main_pt_kbn    = '11';
           } else {
                // ホール用
                $main_pt_cd     = '11';
                $main_pt_kbn    = '11';
           }
        } else if ($anchi === '44') {
            // 44:やすらぎ
           if ($sikijyo === '03') {
                // 斎場用
                $main_pt_cd     = '44';
                $main_pt_kbn    = '44';
           } else {
                // ホール用
                $main_pt_cd     = '44';
                $main_pt_kbn    = '44';
           }
        } else if ($anchi === '45') {
            // 45:小さなお葬式
                $main_pt_cd     = '45';
                $main_pt_kbn    = '45';
        } else if ($anchi === '12') {   // 2017/09/11 ADD Kayo
            // 12:ネットプラン
            $main_pt_cd     = '14';
            $main_pt_kbn    = '14';
        } else if ($anchi === '17') {   // 2018/04/26 ADD Kobayashi
            // 17:ハート共済安心葬儀
            $main_pt_cd     = '17';
            $main_pt_kbn    = '17';
        } else if ($anchi === '18') {   // 2018/05/31 ADD Kayo
            // 18:ハートフル
            $main_pt_cd     = '18';
            $main_pt_kbn    = '18';
        } else if ($anchi === '19') {   // 2019/01/09 ADD Tosaka
            // 19:サニーライフ
            $main_pt_cd     = '19';
            $main_pt_kbn    = '19';
        } else if ($anchi === '47') {   // 2022/06/17 ADD Kayo
            // 47:互助会（NWコース）
            $main_pt_cd     = '47';
            $main_pt_kbn    = '47';
        } else if ($anchi === '48') {   // 2022/06/17 ADD Kayo
            // 48:まごころ2
            $main_pt_cd     = '48';
            $main_pt_kbn    = '48';
        //  ------ 株式会社 ハートフル ------- 2018/04/04 UPD Kobayashi 
        } else if ($anchi === '20') { 
            // 20:小さな家族葬（火葬式）
            $main_pt_cd     = '20';
            $main_pt_kbn    = '20';
        } else if ($anchi === '21') {
            // 21:小さな家族葬（1日葬）
            $main_pt_cd     = '21';
            $main_pt_kbn    = '21';
        } else if ($anchi === '22') {
            // 22:小さな家族葬（2日葬）少人数
            $main_pt_cd     = '22';
            $main_pt_kbn    = '22';
        } else if ($anchi === '23') {
            // 23:小さな家族葬（2日葬）50名
            $main_pt_cd     = '23';
            $main_pt_kbn    = '23';
        } else if ($anchi === '46') {
            // 46:ふれあい 2022/04/15 ADD Kayo
            $main_pt_cd     = '46';
            $main_pt_kbn    = '46';
        } else if ($anchi === '49') {
            // 49:きづな 2023/01/16 ADD Tosaka
            $main_pt_cd     = '49';
            $main_pt_kbn    = '49';
        } else if ($anchi === '50') {
            // 50:朝鮮総連 2025/01/19 ADD Kayo
            $main_pt_cd     = '50';
            $main_pt_kbn    = '50';
        } else if ($anchi === '51') {
            // 51:自由に家族葬 2025/01/19 ADD Kayo
            $main_pt_cd     = '51';
            $main_pt_kbn    = '51';
        } else if ($anchi === '52') {
            // 52:一般（会員） 2025/03/11 ADD Kayo
            $main_pt_cd     = '52';
            $main_pt_kbn    = '52';
        } else if ($anchi === '53') {
            // 53:1日葬・家族葬のこれから 2025/05/25 ADD Kayo
            $main_pt_cd     = '53';
            $main_pt_kbn    = '53';
        }
        return array($main_pt_cd, $main_pt_kbn);
    }

    /**
     * デフォルトは互助会の掛金残に対する税金を設定している
     * 佐野の場合は契約金に対する税金に置き換える
     *
     * <AUTHOR> Sai
     * @since 2015/10/14
     * @param  array  $zeiInfo
     * @return void
     */
    public static function setZeiPrc(&$zeiInfo) {
        if (isset($zeiInfo['keiyaku_gaku_zei'])) {
            $zeiInfo['zan_gaku_zei'] = $zeiInfo['keiyaku_gaku_zei'];
        }
    }

    /**
     * 付帯値引き設定処理
     * ※商品検索ダイアログに表示される、または選択した時に設定される会員金額と
     * 　プラン選択時に伝票明細に保存する会員金額の計算
     *
     * <AUTHOR> Sai
     * @since 2015/11/25
     * @param  array  $msi
     * @param  string  $seko_no
     * @param  array  $keyMapper
     * @return void
     */
    public static function setGojokaiNebiki(&$msi, $seko_no, $keyMapper = null) {
        if ($keyMapper === null) {
            $keyMapper = array('hanbai_tnk' => 'juchu_tnk');
        }
        $db = Msi_Sys_DbManager::getMyDb();
        // 会員情報取得
        $sekoRec = $db->easySelOne(<<< END_OF_SQL
SELECT gojokai_kbn
	, gojokai_cose_cd
	, seko_plan_cd	-- 2017/06/04 ADD Kayo
	, kaisya_cd		-- 2016/12/03 ADD Kayo
FROM seko_kihon_info
WHERE seko_no=:seko_no
AND delete_flg=0
END_OF_SQL
        , array('seko_no' => $seko_no));

        if ($sekoRec === null) {
            return;
        }
        $gojokai_kbn = $sekoRec['gojokai_kbn'];
        if ($gojokai_kbn === null) {
            return;
        }
        $nebiki_gojokai_kbn = null;
        $sekoGojokaiInfoRec = $db->easySelOne(<<< END_OF_SQL
SELECT seko_no, nebiki_group_no, nebiki_join_cnt, nebiki_gojokai_kbn
  FROM seko_gojokai_info
 WHERE seko_no=:seko_no
  AND delete_flg=0
END_OF_SQL
        , array('seko_no' => $seko_no));
        if ($sekoGojokaiInfoRec) {
            $nebiki_gojokai_kbn = $sekoGojokaiInfoRec['nebiki_gojokai_kbn'];
        }
        $sp_basho_cd = null;
        $sp_basho_rec = $db->easySelOne(<<< END_OF_SQL
SELECT basho_cd, basho_nm
FROM seko_nitei
WHERE seko_no=:seko_no
AND delete_flg=0
AND nitei_kbn=7 -- 葬儀
END_OF_SQL
        , array('seko_no' => $seko_no));
        if ($sp_basho_rec) {
            $sp_basho_cd = $sp_basho_rec['basho_cd'];
        }
//        $use_cnt = static::getUseCount($db, $seko_no);
		// 施行プラン商品明細マスタの商品に含まれている、サービス区分＝０は、互助会値引きをしない。
		$seko_plan = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM seko_plan_smsi_mst
WHERE kaisya_cd     =:kaisya_cd
AND seko_plan_cd    =:seko_plan_cd
AND delete_flg      = 0
AND service_kbn     = 0
ORDER BY 	seko_plan_uchiwk_no
END_OF_SQL
        , array('kaisya_cd'   => $sekoRec['kaisya_cd']
			   ,'seko_plan_cd'=> $sekoRec['seko_plan_cd'] ));

        foreach ($msi as &$rec) {
            $nebikiRec = null;
			// 施行プラン商品明細マスタの商品に含まれている、サービス区分＝０は、互助会値引きをしない。
 			$find_flg = 0;
			foreach ($seko_plan as $plan_rec) {
				if ($rec['shohin_cd'] == $plan_rec['shohin_cd']) {
					$find_flg = 1;
					break;
				}
			}
			if ($find_flg == 1)	{
                if(isset($rec['code'])){
                    static::sagakuKensho2($db, $rec, $seko_no, $rec['code'], 'tanka', 1);
                    $rec['g_nebiki_prc'] = $rec['gojokai_nebiki_prc'];  // 2018/10/17 ADD Kayo
                }else if(isset($rec['shohin_cd'])){
                    static::sagakuKensho2($db, $rec, $seko_no, $rec['shohin_cd'], 'tanka', 0);
                    $rec['g_nebiki_prc'] = $rec['gojokai_nebiki_prc'];  // 2018/10/17 ADD Kayo
                }
                continue;
			}
            if (!$nebikiRec) { // 次に seko_kihon_info.gojokai_kbn で探す
                $nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
					  array( 'shohin_cd'  => $rec['shohin_cd']
                            ,'gojokai_kbn'=> $gojokai_kbn
							,'kaijyo_cd'  => $sp_basho_cd
							,'kaisya_cd'  => $sekoRec['kaisya_cd']));

                if (!$nebikiRec) {
                    $nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
							array('shohin_cd'   => $rec['shohin_cd']
                                , 'gojokai_kbn' => $gojokai_kbn
								, 'kaisya_cd'   => $sekoRec['kaisya_cd']));
                }
				if (empty($nebikiRec)) {
					// 会場別の場合 2018/07/16 ADD Kayo
					if (isset($rec['shohin_cd_kaijyo']) && strlen($rec['shohin_cd_kaijyo']) > 0)	{
					$nebikiRec = DataMapper_GojokaiNebiki::findOne($db,
								array('shohin_cd'   => $rec['shohin_cd_kaijyo'],
									  'gojokai_kbn' => $gojokai_kbn,
									  'kaisya_cd'	=> $sekoRec['kaisya_cd']
								));
					}
				}
            }
            $nebiki_prc = '0';
            $g_nebiki_kbn =  9;   // 互助会値引マスタ 値引区分 2017/12/20 ADD Kayo
            $g_nebiki_ritu=  0;   // 互助会値引マスタ 値引率   2017/12/20 ADD Kayo
            $g_nebiki_prc =  0;   // 互助会値引マスタ 値引金額 2017/12/20 ADD Kayo
            $g_nebiki_suryo= 0;   // 互助会値引マスタ 値引数量 2017/12/20 ADD Kayo
            if ($nebikiRec) {
                $g_nebiki_kbn =  $nebikiRec['nebiki_kbn'];   // 互助会値引マスタ 値引区分 2017/12/20 ADD Kayo
                $g_nebiki_ritu=  $nebikiRec['nebiki_ritu'];  // 互助会値引マスタ 値引率   2017/12/20 ADD Kayo
                $g_nebiki_prc =  $nebikiRec['nebiki_prc'];   // 互助会値引マスタ 値引金額 2017/12/20 ADD Kayo
                $g_nebiki_suryo= $nebikiRec['nebiki_suryo']; // 互助会値引マスタ 値引数量 2017/12/20 ADD Kayo
                $tnk = $keyMapper['hanbai_tnk'];
                if ($nebikiRec['nebiki_kbn'] == 1) { // 金額値引
                    $nebiki_prc = $nebikiRec['nebiki_prc'];
                } else if ($nebikiRec['nebiki_kbn'] == 0) { // 率による値引
                    $rec[$tnk] = str_replace(',','',$rec[$tnk]);    // カンマを削除
                    $nebiki00 = $rec[$tnk] * $nebikiRec['nebiki_ritu'] / 100;
                    $nebiki_prc = floor($nebiki00); // 小数点切り捨て
                    if ($nebiki_prc > 0) {
                        $nebiki_prc = -1 * $nebiki_prc;
                    }
                    $nebiki_prc = strval($nebiki_prc);  // 文字列に変換
                } else if ($nebikiRec['nebiki_kbn'] == 3) { // 単価×数量での値引き
                    if (isset($rec['juchu_suryo'])) {
                        $quantity = 1;
                        if ($nebikiRec['nebiki_suryo'] <= $rec['juchu_suryo']) {
                            $quantity = $nebikiRec['nebiki_suryo'];
                        } else {
                            $quantity = $rec['juchu_suryo'];
                        }
                        $nebiki_prc = $nebikiRec['nebiki_prc'] * $quantity;
                    } else {
                        $nebiki_prc = $nebikiRec['nebiki_prc'];
                    }
                } else if ($nebikiRec['nebiki_kbn'] == 4) { // 販売単価×数量での値引き
                    if (isset($rec['juchu_suryo'])) {
                        $quantity = 1;
                        if ($nebikiRec['nebiki_suryo'] <= $rec['juchu_suryo']) {
                            $quantity = $nebikiRec['nebiki_suryo'];
                        } else {
                            $quantity = $rec['juchu_suryo'];
                        }
                        if ($rec[$tnk] > 0) {
                            $nebiki_prc = $rec[$tnk] * $quantity;
                            $nebiki_prc = -1 * $nebiki_prc;
                        }
                    }
                }
            }
            $rec['gojokai_nebiki_prc'] = $nebiki_prc;

            $rec['g_nebiki_kbn']  = $g_nebiki_kbn;   // 互助会値引マスタ 値引区分 2017/12/20 ADD Kayo
            $rec['g_nebiki_ritu'] = $g_nebiki_ritu;  // 互助会値引マスタ 値引率   2017/12/20 ADD Kayo
            $rec['g_nebiki_prc']  = $g_nebiki_prc;   // 互助会値引マスタ 値引金額 2017/12/20 ADD Kayo
            $rec['g_nebiki_suryo']= $g_nebiki_suryo; // 互助会値引マスタ 値引数量 2017/12/20 ADD Kayo
            //プラン選択・検索ダイアログセット
            if(isset($rec['code'])){
                static::sagakuKensho2($db, $rec, $seko_no, $rec['code'], 'tanka', 1);
                $rec['g_nebiki_prc'] = $rec['gojokai_nebiki_prc'];  // 2018/10/17 ADD Kayo
            }else if(isset($rec['shohin_cd'])){
                static::sagakuKensho2($db, $rec, $seko_no, $rec['shohin_cd'], 'hanbai_tnk', 1);
                $rec['g_nebiki_prc'] = $rec['gojokai_nebiki_prc'];  // 2018/10/17 ADD Kayo
            }
        }
        Msi_Sys_Utils::debug( '*** Kayo =>' . Msi_Sys_Utils::dump($msi) );
    }

   /**
     * 特別値引き処理（）
     * 　生花90・白木80以上を選択した場合、棺の割引金額の設定を行う
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param  string  $juchuKakuteiYMD 受注確定日
     * @param  string  $gojokaiKbn 互助会区分
     * @param  string  $sekoPlanCd 施行プランコード
     * @param  array  $dataCol 明細データ
     * @param array &$dataApp 画面Appデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @param array $delete_cds 削除データ（商品選択画面）
     * @return void
     */
    public static function adjJuchu($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, $gojokaiKbn, $sekoPlanCd, &$dataCol, $dataApp, $dataTrnDelCol=array(), $delete_cds=array()) {
        static::setMoushiOkuri($db, $sekoNo, $juchuKakuteiYMD, $dataCol, $dataTrnDelCol, $delete_cds);
        static::setJuchuTokaSai($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, $dataCol, $dataApp, $dataTrnDelCol, $delete_cds);
   }
   
    /**
     *申し送り票特別処理
     * <AUTHOR> Sai
     * @since 2018/05/xx
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @return boolean
     */
    private function setMoushiOkuri($db, $sekoNo, $juchuKakuteiYMD, $dataCol, $dataTrnDelCol, $delete_cds) {
        
        // 見積確定前しか変更しない
        if (!isset($juchuKakuteiYMD)) {
            $hikiData = array();
            $delData = array();
            // 商品選択画面と見積画面と受注内容変更の削除データ
            if (count($delete_cds) > 0) {
                $delData = $delete_cds;
            } else if (count($dataTrnDelCol) > 0) {
                $delData = $dataTrnDelCol;
            }
            if (count($delData) > 0) {
                foreach ($delData as $value) {
                    $shohinOneData = DataMapper_Shohin::find($db, array('shohin_cd' => $value['shohin_cd']));
                    // 申し送り書に登録するもの
                    if (isset($shohinOneData[0]['other_bunrui_kbn'])) {
                        switch ($shohinOneData[0]['other_bunrui_kbn']) {
                            case '1' :  // サイネージ
                                $hikiData['k_free14'] = '2';
                                break;
                            case '2' :  // 案内看板
                                $hikiData['k_free15'] = '2';
                                break;
                            case '3' :  // 出棺用マイクロバス
                                $hikiData['n_free8'] = '2';
                                break;
                            case '4' :  // 料理
                                $hikiData['eisei2_kbn'] = null;
                                break;
                            case '5' :  // 本位牌
                                $hikiData['k_free26'] = null;
                                break;
                            case '6' :  // 通夜送迎
                                $hikiData['k_free43'] = '2';
                                break;
                            case '7' :  // 普通
                                $hikiData['k_free7'] = null;
                                break;
                            case '8' :  // ブラック
                                $hikiData['k_free7'] = null;
                                break;
                            case '9' :  // シルバー
                                $hikiData['k_free7'] = null;
                                break;
                            case '10' :  // ラベンダー
                                $hikiData['k_free7'] = null;
                                break;
                            case '11' :  // ホワイト
                                $hikiData['k_free7'] = null;
                                break;
                            case '12' :  // ピンク
                                $hikiData['k_free7'] = null;
                                break;
                        }
                    }
                }
                if (isset($shohinOneData[0]['shohin_kbn'])) {
                    switch ($shohinOneData[0]['shohin_kbn']) {
                        case '1108' :  // 一膳飯・団子
                            $hikiData['dango_kbn'] = null;
                            break;
                    }
                }
            }
            // 商品選択画面と見積画面と受注内容変更画面の登録データ
            if (count($dataCol) > 0) {
                foreach ($dataCol as $value) {
                    $shohinOneData = DataMapper_Shohin::find($db, array('shohin_cd' => $value['shohin_cd']));
                    // 申し送り書に登録するもの
                    if (isset($shohinOneData[0]['other_bunrui_kbn'])) {
                        switch ($shohinOneData[0]['other_bunrui_kbn']) {
                            case '1' :  // サイネージ
                                $hikiData['k_free14'] = '1';
                                break;
                            case '2' :  // 案内看板
                                $hikiData['k_free15'] = '1';
                                break;
                            case '3' :  // 出棺用マイクロバス
                                $hikiData['n_free8'] = '1';
                                break;
                            case '4' :  // 料理
                                $hikiData['eisei2_kbn'] = '1';
                                break;
                            case '5' :  // 本位牌
                                $hikiData['k_free26'] = '1';
                                break;
                            case '6' :  // 通夜送迎
                                $hikiData['k_free43'] = '1';
                                break;
                            case '7' :  // 普通
                                $hikiData['k_free7'] = '1';
                                break;
                            case '8' :  // ブラック
                                $hikiData['k_free7'] = '2';
                                break;
                            case '9' :  // シルバー
                                $hikiData['k_free7'] = '5';
                                break;
                            case '10' :  // ラベンダー
                                $hikiData['k_free7'] = '4';
                                break;
                            case '11' :  // ホワイト
                                $hikiData['k_free7'] = '3';
                                break;
                            case '12' :  // ピンク
                                $hikiData['k_free7'] = '6';
                                break;
                        }
                    }
                    if (isset($shohinOneData[0]['shohin_kbn'])) {
                        switch ($shohinOneData[0]['shohin_kbn']) {
                            case '1108' :  // 一膳飯・団子
                                $hikiData['dango_kbn'] = '2';
                                break;
                        }
                    }
                }
            }
            // 該当するものがあれば登録・更新する
            if (count($hikiData) > 0) {
                // 存在チェック
                $select = $db->easySelect(<<< END_OF_SQL
                    SELECT 1 FROM seko_hiki_info 
                    WHERE EXISTS 
                    (SELECT * FROM seko_hiki_info    
                    WHERE seko_no = :seko_no
                    AND delete_flg = 0)
END_OF_SQL
                    , array('seko_no' => $sekoNo));
                if (count($select) === 0) {
                    $hikiData['seko_no'] = $sekoNo;
                    // 登録SQL
                    list($sql, $param) = DataMapper_Utils::makeInsertSQL('seko_hiki_info', $hikiData);
                } else {
                    // 条件部
                    $where['seko_no'] = $sekoNo;  // 施行番号 
                    $where['delete_flg'] = 0;  // 削除フラグ
                    // 更新SQL
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_hiki_info', $hikiData, $where);
                }
                $db->easyExecute($sql, $param);
            }
        }
    }

    /**
     * 初七日(十日祭)の特殊処理
     * <AUTHOR> Sai
     * @since 2018/05/xx
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @return boolean
     */
    private function setJuchuTokaSai($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, &$dataCol, $dataApp, $dataTrnDelCol=array(), $delete_cds=array()) {
        // 初七日(十日祭)の金額があるか？
    	$select = $db->easySelOne( <<< END_OF_SQL
		SELECT n_free2	-- 式中・戻り
		FROM seko_kihon_all_free    
		WHERE	delete_flg	= 0
        AND     seq_no      = 0    
		AND		seko_no		= :seko_no		
END_OF_SQL
		,array('seko_no' => $sekoNo));
        $shohin_kbn = '5001';
        // 受注伝票に存在するかチェックする
        $check_flg = static::checkSyonanokaFromJuchu($db, $sekoNo, $shohin_kbn, $juchuKakuteiYMD);
        if (Msi_Sys_Utils::myCount($select) <=   0)  {
            // 受注伝票明細を削除
            static::delJuchu($db, $sekoNo, $shohin_kbn, $juchuKakuteiYMD); // 初七日(十日祭)の商品区分
        }
        if (strlen($select['n_free2']) > 0) {
            if ($select['n_free2'] <> 0) {
                //  初七日(十日祭)データ設定処理
                static::addJuchu($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, $shohin_kbn, $select['n_free2'], $dataCol, $dataApp, $check_flg);
            } else {
                // 受注伝票明細を削除
                static::delJuchu($db, $sekoNo, $shohin_kbn, $juchuKakuteiYMD); // 初七日(十日祭)の商品区分
            }   
        }
    }
    
     /**
     *
     * 初七日商品の存在チェック（受注伝票のみ）
     *
     * <AUTHOR> Tosaka
     * @since 2018/10/31
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $shohin_kbn 商品区分
     * @param string $juchuKakuteiYMD 受注確定日
     * @return array 
     */
    private static function checkSyonanokaFromJuchu($db, $sekoNo, $shohin_kbn, $juchuKakuteiYMD) {
        
        $flg = false;
        if (isset($juchuKakuteiYMD)) {
            $tbl_h = 'uriage_denpyo';
            $tbl_m = 'uriage_denpyo_msi';
            $join = 'm.uri_den_no = d.uri_den_no';
        } else {
            $tbl_h = 'juchu_denpyo';
            $tbl_m = 'juchu_denpyo_msi';
            $join = 'm.denpyo_no = d.denpyo_no';
        }
        $select = $db->easySelect( <<< END_OF_SQL
        SELECT *
        FROM $tbl_h d
        INNER JOIN $tbl_m m
                ON $join
                AND m.shohin_kbn = :shohin_kbn
                AND m.delete_flg = 0
        WHERE
            d.seko_no = :seko_no
        AND d.data_kbn = 1
        AND d.delete_flg = 0
END_OF_SQL
        , array('seko_no' => $sekoNo, 'shohin_kbn' => $shohin_kbn));
        if (count($select) > 0) {
            $flg = true;
        }
        return $flg;
    }
    
     /**
     *
     * 受注・売上伝票の初七日(十日祭)データ削除処理
     *
     * <AUTHOR> Kayo
     * @since 2018/06/06
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $shohin_kbn 商品区分
     * @param string $juchuKakuteiYMD 受注確定日
     * @return array 
     */
    private static function delJuchu($db, $sekoNo, $shohin_kbn, $juchuKakuteiYMD) {
        $tbl = 'juchu_denpyo_msi';
        // 確定したら売上伝票を参照
        if ($juchuKakuteiYMD) {
            $tbl = 'uriage_denpyo_msi';
        }
        // 削除処理は論理削除(deleteflgを更新)する
        $cnt = $db->easyExecute( <<< END_OF_SQL
        UPDATE {$tbl}
        SET 
            delete_flg = 1
        WHERE
            seko_no = :seko_no
        AND shohin_kbn = :shohin_kbn
        AND delete_flg = 0
END_OF_SQL
        , array('seko_no' => $sekoNo, 'shohin_kbn' => $shohin_kbn));
        return $cnt;
    }
     /**
     *
     * 受注・売上伝票の初七日(十日祭)登録データ登録処理
     *
     * <AUTHOR> Kayo
     * @since 2015/09/18
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $juchuDenpyoNo 伝票番号
     * @param string $juchuKakuteiYMD 受注確定日
     * @param string $shohin_kbn 商品区分
     * @param  string  $n_free2 初七日(十日祭)
     * @param  array  $dataApp appデータ
     * @param  boolean  $check_flg true:受注伝票明細に有 false:受注伝票明細に無
     * @return array 
     */
    private static function addJuchu($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, $shohin_kbn , $n_free2, &$dataCol, $dataApp, $check_flg) {

        $tblm = 'juchu_denpyo_msi';
        $tblh = 'juchu_denpyo';
        $join = 'm.denpyo_no = h.denpyo_no';
        $item = 'm.juchu_prc';
        // 確定したら売上伝票を参照
        if ($juchuKakuteiYMD) {
            $tblm = 'uriage_denpyo_msi';
			$tblh = 'uriage_denpyo';
			$join = 'm.uri_den_no = h.uri_den_no';
            $item = 'm.uri_prc';
        }
        
        // 初七日(十日祭)の金額があるか？
    	$select = $db->easySelOne( <<< END_OF_SQL
        SELECT {$item}	-- 式中・戻り
		FROM {$tblm} m
		INNER JOIN 	{$tblh} h
			ON	{$join}
			AND	0			=	h.delete_flg
		WHERE	m.delete_flg	= 0
		AND		m.shohin_kbn	= :shohin_kbn
		AND		m.seko_no		= :seko_no
		AND		h.data_kbn		= 1
END_OF_SQL
		,array('shohin_kbn' => $shohin_kbn, 'seko_no' => $sekoNo));
        // 存在したら更新しない
        if (Msi_Sys_Utils::myCount($select) > 0) {
            return;
        }
        // 明細に追加する
        $msi1 = static::getColData($db, $sekoNo, $shohin_kbn, $n_free2, $juchuKakuteiYMD, $check_flg);
        // 明細を登録する
        static::insertColData($db, $msi1, $juchuKakuteiYMD, $sekoNo, $juchuDenpyoNo, $dataApp);
    }
    
    /**
     * 明細に初七日(十日祭)を追加する
     *
     * <AUTHOR> Kayo
     * @since 2016/06/07
     * @param Msi_Sys_Db $db
     * @param string $sekoNo 施行番号
     * @param string $shohin_kbn 商品区分
     * @param  string  $n_free2 初七日(十日祭)
     * @param string $juchuKakuteiYMD 受注確定日
     * @param  boolean  $check_flg true:受注伝票明細に有 false:受注伝票明細に無
     * @return void
     */
    private static function getColData($db, $sekoNo, $shohin_kbn, $n_free2, $juchuKakuteiYMD, $check_flg) {
        $data = array();
        // 会社コード
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();
        $kihon = DataMapper_SekoKihonInfo::find($db, array('seko_no' => $sekoNo));
        if (count($kihon) > 0 && strlen($kihon[0]['sougi_ymd']) > 0) {
            $kijunYmd = $kihon[0]['sougi_ymd'];
        } else {
            $kijunYmd = Msi_Sys_Utils::getDate();
        }
        $zeiMst = DataMapper_ZeiMst::find($db, array('__raw1' => "T.tekiyo_st_date <= '".$kijunYmd."' AND T.tekiyo_ed_date >= '".$kijunYmd."'", 'reduced_tax_rate' => 1), false);

        $select = $db->easySelOne( <<< END_OF_SQL
            SELECT
                msi_no              -- 受注明細№
                ,0                  AS disp_no       -- 表示順
                ,0                  AS add_kbn       -- 追加区分
                ,5                  AS data_sbt      -- データ種別
                ,(SELECT bumon_cd FROM seko_kihon_info WHERE seko_no = '{$sekoNo}' LIMIT 1)  -- 部門コード
                ,1                  AS denpyo_kbn    -- 伝票区分
                ,shohin_cd          -- 商品コード
				,kaisya_cd
				,shohin_bumon_cd
                ,shohin_kbn         -- 商品区分
                ,shohin_kbn_nm      -- 商品区分名
                ,shohin_nm          -- 商品名称
                ,shohin_tkiyo_nm    -- 商品摘要
                ,0                      AS mokuteki_kbn  -- 目的区分
                ,dai_bunrui_cd      -- 大分類コード
                ,chu_bunrui_cd      -- 中分類コード
                ,juchu_tnk          -- 単価
                ,juchu_suryo        -- 数量
                ,juchu_tnk  * juchu_suryo   AS juchu_gokei-- 受注金額合計
                ,juchu_tnk                  AS juchu_prc-- 受注金額
                ,nebiki_prc		-- 値引額
                ,gen_tnk		-- 原価単価
                ,shohin_type	-- 商品タイプ
                ,nonyu_nm		-- 納入場所
                ,nonyu_dt		-- 納入予定日
                ,nm_input_kbn	-- 名称入力区分
                ,tani_cd		-- 単位コード
                ,zei_kbn            -- 売上課税区分
                ,uri_kamoku_cd      -- 科目コード
                ,siire_cd           -- 仕入コード
                ,siire_lnm          -- 仕入名
                ,tnk_chg_kbn        -- 売上単価変更区分
                ,hachu_kbn          -- 発注書区分
                ,hoshi_umu_kbn	-- 奉仕料有無区分
                ,hoshi_prc_tnk * juchu_suryo hoshi_prc  -- 奉仕料
                ,NULL	AS nonyu_cd   -- 納入先コード 
                ,NULL	AS nonyu_knm  -- 納入先名カナ
                ,NULL	AS nonyu_yubin_no -- 納入先郵便番号
                ,NULL	AS nonyu_addr1    -- 納入先住所1
                ,NULL	AS nonyu_addr2    -- 納入先住所2
                ,NULL	AS nonyu_tel  -- 納入先電話番号
                ,NULL	AS nonyu_fax  -- 納入先FAX
                ,0		AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
                ,0		AS order_flg     -- 発注済み 0:未発注 1:発注済み
                ,sougi_ymd
                ,NULL AS add_henpin_ymd
                ,{$zeiMst[0]['zei_cd']} AS zei_cd      -- 消費税コード
                ,1 AS reduced_tax_rate      -- 軽減税率区分
            FROM (SELECT
                NULL AS msi_no
               ,row_number () OVER () AS disp_no 
               ,sm.shohin_cd
			   ,sm.kaisya_cd
			   ,sm.bumon_cd		AS	shohin_bumon_cd
               ,skm.shohin_kbn
               ,skm.shohin_kbn_nm
               ,sm.shohin_nm
               ,sm.shohin_tkiyo_nm
               ,sbm.dai_bunrui_cd
               ,sbm.chu_bunrui_cd
               ,{$n_free2} AS juchu_tnk
               ,1			AS juchu_suryo
               ,0			AS nebiki_prc
               ,0			AS gen_tnk
               ,NULL		AS shohin_type
               ,NULL		AS nonyu_nm
               ,NULL		AS nonyu_dt
               ,sm.nm_input_kbn
               ,sm.tani_cd
               ,sm.uri_zei_kbn AS zei_kbn
               ,sm.siire_cd 
               ,NULL AS siire_lnm
               ,sm.hoshi_umu_kbn
               ,sm.tnk_chg_kbn    
               ,sbm.hachu_kbn
               ,sm.uri_kamoku_cd
               ,0									AS hoshi_prc_tnk -- 奉仕料 非対応
               ,TO_CHAR(sk.sougi_ymd,'YYYY/MM/DD')  AS  sougi_ymd
                FROM shohin_mst sm
                INNER JOIN seko_kihon_info    sk
                    ON sk.seko_no = :seko_no
                    AND 0         = sk.delete_flg
                INNER JOIN  shohin_bunrui_mst sbm
                    ON  sm.shohin_cd = sbm.shohin_cd
                    AND sm.kaisya_cd = sbm.kaisya_cd
                    AND sbm.shohin_kbn= :shohin_kbn   
                    AND 0             = sbm.delete_flg
                INNER JOIN shohin_kbn_mst skm
                    ON  sbm.shohin_kbn = skm.shohin_kbn
                    AND 0              = skm.delete_flg
                WHERE sm.delete_flg = 0
                AND sm.kaisya_cd    = :kaisya_cd  
                AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
            ) M
        ORDER BY
            disp_no
END_OF_SQL
        , array('shohin_kbn' => $shohin_kbn, 'kaisya_cd' => $kaisya_cd, 'seko_no' => $sekoNo));
        if (count($select) > 0) {
            $data = $select;
            // データ登録が売上伝票明細でかつ受注伝票に存在していなかった場合　伝票区分:追加 変更日:現在日
            if (isset($juchuKakuteiYMD) && !$check_flg) {
                $data['denpyo_kbn'] = 11;
                $data['add_henpin_ymd'] = date('Y-m-d');
            }
        }
        return $data;
    }

    /**
     * 明細に初七日(十日祭)を追加する
     *
     * <AUTHOR> Kayo
     * @since 2018/06/07
     * @param Msi_Sys_Db $db
     * @param array $data 明細
     * @param string $juchuKakuteiYMD 受注確定日
     * @param string $sekoNo 施行番号
     * @param string $juchuDenpyoNo 伝票番号
     * @param  array  $dataApp appデータ
     * @return void
     */
    private static function insertColData($db, $data, $juchuKakuteiYMD, $sekoNo, $juchuDenpyoNo, $dataApp) {
        $cnt = 0;
        if (count($data) === 0) {
            return;
        }
        $tbl = 'juchu_denpyo_msi';
        $juchu_prc = $data['juchu_tnk'] * $data['juchu_suryo'];
        if ($juchuKakuteiYMD) {
            $maxMsiNo = static::getMaxUriagedenpyoMsiNo($juchuDenpyoNo);
            $denpyoMsi['juchu_suryo'] = $data['juchu_suryo'];  // 商品数量
            $denpyoMsi['uri_tnk'] = $data['juchu_tnk'];  // 単価
            $denpyoMsi['uri_prc'] = $juchu_prc; // 受注金額
            $denpyoMsi['uri_den_no'] = $juchuDenpyoNo;  // 売上伝票
            $tbl = 'uriage_denpyo_msi';
        } else {
            $maxMsiNo = static::getMaxdenpyoMsiNo($juchuDenpyoNo);
            $denpyoMsi['juchu_suryo'] = $data['juchu_suryo'];  // 商品数量
            $denpyoMsi['juchu_tnk'] = $data['juchu_tnk'];  // 単価
            $denpyoMsi['juchu_prc'] = $juchu_prc; // 受注金額
            $denpyoMsi['denpyo_no'] = $juchuDenpyoNo;  // 受注伝票
        }
        $denpyoMsi['disp_no']			= $maxMsiNo;  // 表示順
        $denpyoMsi['kaisya_cd']			= $data['kaisya_cd'];			// 会社コード
        $denpyoMsi['shohin_bumon_cd']	= $data['shohin_bumon_cd'];	// 商品部門コード
        $denpyoMsi['add_kbn']			= $data['add_kbn'];  // 伝票区分
        $denpyoMsi['denpyo_kbn']		= $data['denpyo_kbn'];  // 伝票区分
        $denpyoMsi['juchu_ymd']			= $data['sougi_ymd'];   // 葬儀日
        $denpyoMsi['bumon_cd']			= $data['bumon_cd'];    // 売上部門コード
        $denpyoMsi['mokuteki_kbn']		= $data['mokuteki_kbn'];  // 使用目的区分
        $denpyoMsi['dai_bunrui_cd']		= $data['dai_bunrui_cd'];  // 大分類コード
        $denpyoMsi['chu_bunrui_cd']		= $data['chu_bunrui_cd'];  // 中分類コード
        $denpyoMsi['shohin_kbn']		= $data['shohin_kbn'];  // 商品区分
        $denpyoMsi['shohin_cd']			= $data['shohin_cd'];  // 商品コード
        $denpyoMsi['shohin_nm']			= $data['shohin_nm'];  // 商品名
        $denpyoMsi['shohin_tkiyo_nm']	= $data['shohin_tkiyo_nm'];  // 商品摘要名
        $denpyoMsi['tani_cd']			= $data['tani_cd'];  // 単位コード
        $denpyoMsi['zei_kbn']			= $data['zei_kbn'];  // 消費税区分
        $denpyoMsi['add_henpin_ymd']		= $data['add_henpin_ymd'];  // 変更日
        // 消費税項目を設定する
        static::setZeiInfo($db, $dataApp, $denpyoMsi, $juchu_prc);
        $denpyoMsi['msi_no']        = ++$maxMsiNo;  // 受注明細№
        $denpyoMsi['data_sbt']      = $data['data_sbt'];  // データ種別
        $denpyoMsi['seko_no']       = $sekoNo;  // 施行番号
        $denpyoMsi['seko_no_sub']   = '00';  // 施行番号枝番

        list($sql, $param) = DataMapper_Utils::makeInsertSQL($tbl, $denpyoMsi);
        $cnt += $db->easyExecute($sql, $param);
        return $cnt;
    }
    
    /**
     *
     * 最大売上明細№を取得する
     *
     * <AUTHOR> Kayo
     * @since 2018/06/06
     * @param string $denpyoNo 伝票番号
     * @return int 最大受注明細№
     */
    private function getMaxUriagedenpyoMsiNo($denpyoNo) {
        // 売上伝票明細の最大売上明細№
        $maxMsiNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT COALESCE(MAX(msi_no),0) AS msi_no
        FROM uriage_denpyo_msi
        WHERE uri_den_no = :denpyo_no
                ";
        $select = $db->easySelOne($sql, array('denpyo_no' => $denpyoNo));
        if (count($select) > 0) {
            $maxMsiNo = $select['msi_no'];
        }
        return $maxMsiNo;
    }

    /**
     *
     * 最大受注明細№を取得する
     *
     * <AUTHOR> Kayo
     * @since 2018/06/06
     * @param string $denpyoNo 伝票番号
     * @return int 最大受注明細№
     */
    private function getMaxdenpyoMsiNo($denpyoNo) {
        // 受注伝票明細の最大受注明細№
        $maxMsiNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql1 = "
        SELECT
            COALESCE(MAX(msi_no),0) AS msi_no
        FROM
            juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
                ";
        $select1 = $db->easySelOne($sql1, array('denpyo_no' => $denpyoNo));
        if (count($select1) > 0) {
            $maxMsiNo = $select1['msi_no'];
        }
        return $maxMsiNo;
    }
    
    /**
     *
     * 受注・売上伝票明細の消費税項目を設定する
     *
     * <AUTHOR> Kayo
     * @since 2018/06/07
     * @param array $dataApp appデータ
     * @param array &$denpyoMsi 受注・売上伝票明細更新対象
     * @param float $prc 受注・売上金額
     */
    private function setZeiInfo($db, $dataApp, &$denpyoMsi, $prc) {

        $zeiKbn = (int) $denpyoMsi['zei_kbn'];
        $denpyoMsi['zei_kbn'] = $zeiKbn;  // 消費税区分
        // 消費税処理
        $zei = App_ClsTaxLib::GetCalcTax2($db, $dataApp['zei_kijun_ymd'], $prc, $zeiKbn);
        if ($zeiKbn === 1) {
            $denpyoMsi["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
            $denpyoMsi['out_zei_prc'] = 0;    // 行外税消費税額
        } else if ($zeiKbn === 2) {
            $denpyoMsi['in_zei_prc'] = 0;     // 行内税消費税額
            $denpyoMsi["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
        }
        $denpyoMsi["zei_cd"] = $zei["zei_cd"];  // 消費税コード
        $denpyoMsi["reduced_tax_rate"] = 1;  // 軽減税率対象区分
    }
   
    /**
     * 互助会コースにかどうかのチェック処理
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $gojokaiKbn 互助会区分
     * @return boolean
     */
    private static function checkGojokai($db, $gojokaiKbn) {
        $flg = false;

        $sql = "
        SELECT
            gojokai_kbn
        FROM
            gojokai_price_mst
        WHERE
            gojokai_kbn = :gojokai_kbn
        AND delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('gojokai_kbn' => $gojokaiKbn));
        if (count($select) > 0) {
            $flg = true;
        }
        return $flg;
    }

    /**
     * 互助会の用途が葬送儀礼と祭壇値引きのカウントを取得する
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @return $cnt
     */
    private static function getUseCount($db, $sekoNo) {
        // MYTODO 互助会利用換算口数プラン値引きは？
        $cnt = 0;
        $sql = "
        SELECT
            SUM(gcm.join_convert_cnt) AS cnt
        FROM
            seko_gojokai_member sgm
			LEFT JOIN seko_kihon_info sk		-- 2016/12/03 ADD Kayo
				ON sgm.seko_no = sk.seko_no
				AND 0		   = sk.delete_flg	-- 2016/12/03 ADD Kayo
            INNER JOIN
                gojokai_couse_mst gcm
                ON (sgm.course_snm_cd = gcm.gojokai_cose_iw)
				AND sk.kaisya_cd	  = gcm.kaisya_cd
        WHERE
            sgm.seko_no = :seko_no
        AND sgm.yoto_kbn IN (1, 10) -- 葬送儀礼と祭壇値引き
        AND sgm.delete_flg = 0
        AND gcm.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo));
        if (count($select) > 0) {
            $cnt = $select['cnt'];
        }
        return $cnt;
    }

    /**
     * 法事用のサイドメニュー取得処理
     *
     * <AUTHOR> Kayo
     * @since 2016/07/28
     * @param  string $catKbn  カテゴリ区分
     * @param  string $sekoNo  施行番号
     * @return array サイドメニュー
     */
    public static function getSideMenuDatahoj($catKbn, $sekoNo) {
        switch( $catKbn ) {
        case 101:       // 法要・墓参り
            $sideMenuData = Juchu_Utils::getSideMenuData( 'jigo', $sekoNo, null, '2' );
            break;
        case 102:       // 法要返礼品
            $sideMenuData = Juchu_Utils::getSideMenuData( 'gift', $sekoNo, null, '2' );
            break;
        case 111:       // 法要会食
            $sideMenuData = Juchu_Utils::getSideMenuData( 'food', $sekoNo, null, '2' );
            break;
        default:
            throw new Exception( "Juchu_JuchuItemhj00::save(): getSideMenuData( $catKbn ?" );
        }
        return  $sideMenuData;
    }

    /**
     * 法事用のサイドメニュー取得処理
     *
     * <AUTHOR> Kayo
     * @since 2016/07/28
     * @param  string $catKbn  カテゴリ区分
     * @return string 項目番号
     */
    public static function getItemnohoj($catKbn) {
        switch( $catKbn ) {
        case 101:       // 法要・墓参り
            $itemno = 4;
            break;
        case 102:       // 返礼品
            $itemno = 11;
            break;
        case 111:       // 会食
            $itemno = 12;
            break;
        case 142:    // 非課税
            $itemno = 14;
            break;
        default:
            $itemno = 3;
        }
        return  $itemno;
    }

    /**
     * 領収書発行権限があれば真を返す
     * (管理者ロールか事務担当ロール)
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @return     boolean
     * @version    2017/11/21 manager追加
     */
    public static function hasRyoshuHakkoRole()
    {
        $isAllowed = false;
        foreach ( Msi_Sys_Utils::strArrayify_qw('sysman jimu manager shikitentanto') as $role ) {
            if ( Msi_Sys_Utils::hasRole($role) ) {
                $isAllowed = true;
                break;
            }
        }
        return $isAllowed;
    }

    /**
     * 画像置換用場所データ取得(1)
     *  org. JuchuItemexInfoAdapterAbst.php
     *         // mihara added -- begin 2015/01/27
        // web_disp_shohin_mst.disp_kbn が 2(会場別商品区分単位) or 3(会場別商品単位)の場合、gazo_img を変更する
        // 2015/08/04    $sp_basho_cd = $this->_selectSekoKihon['hs_anchi_cd'];
        // Msi_Sys_Utils::debug( '*** sp_basho_cd **** =>' . $sp_basho_cd . ' ==>' . $dtl['shohin_cd']);
        // mihara mod 2015/08/04 hs_anchi_cd ではなく、施行日程の告別式の場所コードとする
        // 2016/05/06 Mihara  AND basho_kbn=2 条件追加
     *
     * <AUTHOR> Mihara
     * @since 2016/05/06
     * @param  string $seko_no
     * @param  array  $data
     * @return string|null(該当なし) $sp_basho_cd(条件に合致する場所CD)
     */
    public static function getSpBashoCdBySougi($seko_no, $db=null)
    {
        if ( $db === null ) {
            $db = Msi_Sys_DbManager::getMyDb();
        }

        $sp_basho_cd = null;
        $sp_basho_rec = $db->easySelOne( <<< END_OF_SQL
SELECT basho_cd, basho_nm
  FROM seko_nitei
 WHERE seko_no=:seko_no
   AND delete_flg=0
   AND nitei_kbn=11 -- 葬儀
   AND basho_kbn=2
END_OF_SQL
        , array('seko_no'=>$seko_no) );
        if (isset($sp_basho_rec) && count($sp_basho_rec) > 0) {	// 2019/11/24 UPD Kayo PHP Warning
            $sp_basho_cd = $sp_basho_rec['basho_cd'];
		} else {
	        $sp_basho_rec = $db->easySelOne( <<< END_OF_SQL
SELECT basho_cd, basho_nm
  FROM seko_nitei
 WHERE seko_no=:seko_no
   AND delete_flg=0
   AND nitei_kbn=5 -- 出棺
   AND basho_kbn=2
END_OF_SQL
			, array('seko_no'=>$seko_no) );
			if (isset($sp_basho_rec) && count($sp_basho_rec) > 0) {	// 2019/11/24 UPD Kayo PHP Warning
				$sp_basho_cd = $sp_basho_rec['basho_cd'];
			}	
		}

        return $sp_basho_cd;
    }
    
    /**
     * 画像置換用場所データ取得(1)
     *  org. JuchuItemexInfoAdapterAbst.php
     *         // mihara added -- begin 2015/01/27
        // web_disp_shohin_mst.disp_kbn が 2(会場別商品区分単位) or 3(会場別商品単位)の場合、gazo_img を変更する
        // 2015/08/04    $sp_basho_cd = $this->_selectSekoKihon['hs_anchi_cd'];
        // Msi_Sys_Utils::debug( '*** sp_basho_cd **** =>' . $sp_basho_cd . ' ==>' . $dtl['shohin_cd']);
        // mihara mod 2015/08/04 hs_anchi_cd ではなく、施行日程の告別式の場所コードとする
        // 2016/05/06 Mihara  AND basho_kbn=2 条件追加
     *
     * <AUTHOR> Kayo
     * @since 2018/11/28
     * @param  string $seko_no
     * @param  array  $data
     * @return string|null(該当なし) $sp_basho_cd(条件に合致する場所CD)
     */
    public static function getSpBashoCdBySougiPlan($seko_no, $db=null)
    {
        if ( $db === null ) {
            $db = Msi_Sys_DbManager::getMyDb();
        }

        $sp_basho_cd = null;
        $sp_basho_rec = $db->easySelOne( <<< END_OF_SQL
SELECT basho_kbn,basho_cd, basho_nm
  FROM seko_nitei
 WHERE seko_no=:seko_no
   AND delete_flg=0
   AND nitei_kbn=11 -- 葬儀
END_OF_SQL
        , array('seko_no'=>$seko_no) );
        if ( Msi_Sys_Utils::myCount($sp_basho_rec) > 0) {
            $sp_basho_cd = $sp_basho_rec['basho_kbn'] . $sp_basho_rec['basho_cd'];
		} else {
	        $sp_basho_rec = $db->easySelOne( <<< END_OF_SQL
SELECT basho_kbn, basho_cd, basho_nm
  FROM seko_nitei
 WHERE seko_no=:seko_no
   AND delete_flg=0
   AND nitei_kbn=5 -- 出棺
END_OF_SQL
			, array('seko_no'=>$seko_no) );
			if ( Msi_Sys_Utils::myCount($sp_basho_rec) > 0) {
				$sp_basho_cd = $sp_basho_rec['basho_cd']. $sp_basho_rec['basho_cd'];
			}	
		}

        return $sp_basho_cd;
    }

    /**
     * 画像置換用場所データ取得(2)
     * 売上部門の会館に属する画像を表示するため
     *
     * <AUTHOR> Mihara
     * @since 2016/05/06
     * @param  string $bumon_cd
     * @param  array  $data
     * @return string|null(該当なし) $sp_basho_cd(条件に合致する場所CD)
     */
    public static function getSpBashoCdByKaikan($bumon_cd, $db=null)
    {
        if ( $db === null ) {
            $db = Msi_Sys_DbManager::getMyDb();
        }

        $sp_basho_cd = null;
        $sp_basho_rec = $db->easySelOne( <<< END_OF_SQL
SELECT kaijyo_cd, kaijyo_lnm
  FROM kaijyo_mst
 WHERE bumon_cd=:bumon_cd
   AND delete_flg=0
   AND kaijyo_kbn=1
   AND type_kbn=99
END_OF_SQL
        , array('bumon_cd'=>$bumon_cd) );
        if ( $sp_basho_rec ) {
            $sp_basho_cd = $sp_basho_rec['kaijyo_cd'];
        }

        return $sp_basho_cd;
    }

    /**
     * 発注管理特殊処理
     * 斎場運営費の場合は警備とシンシアーの発注管理作成・削除処理を行う
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo sekoNo
     * @param  string  $juchuKakuteiYMD
     * @param  array  $dataCol
     * @return void
     */
    public static function adjHachuinfo($db, $sekoNo, $juchuKakuteiYMD, &$dataCol, $dataTrnDelCol = array(), $delete_cds = array()) {

                foreach ($dataCol as $one) {
                    $hachu_kbn = '35'; // 寺院依頼書
                    if (isset($one['hachu_kbn']) && $one['hachu_kbn'] === $hachu_kbn) {
                        // 施行発注管理削除処理
                        static::delHachuinfo($db, $sekoNo, $hachu_kbn,  $juchuKakuteiYMD);
                        // 施行発注管理登録情報設定処理
                        static::addHachuinfo($db, $sekoNo, $hachu_kbn,  $dataCol);
                    }
                }

//		$hachu_kbn = '92'; // 貸布団:仮通夜控室
//		// 施行発注管理削除処理
//		static::delHachuinfo($db, $sekoNo, $hachu_kbn,  $juchuKakuteiYMD);
//		// 施行発注管理登録情報設定処理
//		static::addHachuinfo($db, $sekoNo, $hachu_kbn,  $dataCol);
//
//		$hachu_kbn = '93'; // 貸布団:通夜控室
//		// 施行発注管理削除処理
//		static::delHachuinfo($db, $sekoNo, $hachu_kbn,  $juchuKakuteiYMD);
//		// 施行発注管理登録情報設定処理
//		static::addHachuinfo($db, $sekoNo, $hachu_kbn,  $dataCol);
//
//		$hachu_kbn = '94'; // 貸布団:離れの仮通夜控室
//		// 施行発注管理削除処理
//		static::delHachuinfo($db, $sekoNo, $hachu_kbn,  $juchuKakuteiYMD);
//		// 施行発注管理登録情報設定処理
//		static::addHachuinfo($db, $sekoNo, $hachu_kbn,  $dataCol);
//
//        $hachu_kbn = '95'; // 基本セットＡ 2016/04/19 ADD Kayo
//		// 施行発注管理削除処理
//		static::delHachuinfo($db, $sekoNo, $hachu_kbn,  $juchuKakuteiYMD);
//		// 施行発注管理登録情報設定処理
//		static::addHachuinfo($db, $sekoNo, $hachu_kbn,  $dataCol);
    }
    /**
     *
     * 施行発注管理削除処理
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $hachu_kbn 発注区分
     * @param string $juchuKakuteiYMD 発注書区分
     * @return array
     */
    private static function delHachuinfo($db, $sekoNo, $hachu_kbn, $juchuKakuteiYMD) {
        $cnt = 0;
        // 伝票に斎場運営費が存在しない場合は、削除を行う
        $den = static::findDenpyo($db, $sekoNo, $hachu_kbn, $juchuKakuteiYMD);
        if (Msi_Sys_Utils::myCount($den) === 0) {
			$kaisyacd = App_Utils::getCtxtKaisyaEasy();	// 2017/06/03 ADD Kayo
            $shohinFree = static::findShohin($db, $hachu_kbn, $kaisyacd);
            foreach ($shohinFree as $free) {
                $cnt +=static::deleteHachuInfoByHarpKbn($db, $sekoNo, $free['hachu_kbn'], $free['shohin_cd'], $hachu_kbn);
            }
		}
        return $cnt;
    }

    /**
     *
     * 施行発注管理登録情報設定処理
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $hachu_kbn 発注番号
     * @param  array  $dataCol
     * @return array
     */
    private static function addHachuinfo($db, $sekoNo, $hachu_kbn, &$dataCol) {
        // 発注情報追加処理
        //Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($dataCol) );
        $kaisyacd = App_Utils::getCtxtKaisyaEasy();	// 2017/09/22 ADD Kobayashi
        // 施行基本情報
        $select_seko = $db->easySelOne( <<< END_OF_SQL
SELECT   jyusho_cd  -- 寺院コード
FROM seko_kihon_info
WHERE delete_flg	= 0
AND	seko_no		   = :seko_no
END_OF_SQL
        , array('seko_no' => $sekoNo));
        foreach ($dataCol as &$DenpyoAddRow) {
            if (isset($DenpyoAddRow['hachu_kbn']) && ($DenpyoAddRow['hachu_kbn'] === $hachu_kbn)) {
                $shohinFree = static::findShohin($db, $hachu_kbn, $kaisyacd);
                foreach ($shohinFree as $free) {
                    // 施行発注管理存在チェック
                    $select = static::selectHachuInfo($db, $sekoNo, $free['hachu_kbn'], $free['shohin_cd'], $hachu_kbn);
                    if (Msi_Sys_Utils::myCount($select) > 0) {
                        continue;
                    }
                    // 施行発注管理がなければ、発注情報を設定
                    $hachuOne = static::getHachuInfoOne($db, $free['shohin_cd'], $kaisyacd);
                    if (count($hachuOne) <= 0) {
                        $hachuOne['upsert'] = 1;
                        $hachuOne['k_free2']        = $hachu_kbn;   // 発注区分を設定2016/04/20 ADD Kayo
                        $hachuOne['auto_make_flg']  = 1;            // 自動作成フラグ 2016/06/29 ADD Kayo
                        if(  $hachu_kbn == "35"){       // 寺院依頼書
                            $hachuOne['send_mst_kbn'] = "1";        // 住所名称マスタの寺院のマスタを参照する
                            if (count($select_seko) > 0) {
                                $hachuOne['siire_cd']     = $select_seko['jyusho_cd'];
                            } else {
                                $hachuOne['siire_cd']     = null;
                            }
                        }
                        $dataCol[] = $hachuOne;
                    }  else {
                        $DenpyoAddRow['auto_make_flg']  = 1;            // 自動作成フラグ 
                        $DenpyoAddRow['send_mst_kbn'] = "1";        // 住所名称マスタの寺院のマスタを参照する
                        if (count($select_seko) > 0) {
                            $DenpyoAddRow['siire_cd']     = $select_seko['jyusho_cd'];
                        } else {
                            $DenpyoAddRow['siire_cd']     = null;
                        }
                    }
                }
            }
        }
        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($dataCol) );
    }
    /**
     *
     * 施行発注管理情報取得
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $hachu_kbn 発注番号
     * @param string $juchuKakuteiYMD 発注書区分
     * @return array
     */
    private static function findDenpyo($db, $sekoNo, $hachu_kbn, $juchuKakuteiYMD) {
        $tbl = 'juchu_denpyo_msi';
        // 確定したら売上伝票を参照
        if ($juchuKakuteiYMD) {
            $tbl = 'uriage_denpyo_msi';
        }
        // 伝票明細取得SQL
        $sql = "
        SELECT
            dm.seko_no
        FROM
            {$tbl} dm
            INNER JOIN  shohin_bunrui_mst sbm
            ON  (   dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                AND dm.chu_bunrui_cd = sbm.chu_bunrui_cd
                AND dm.shohin_kbn	 = sbm.shohin_kbn
                AND dm.shohin_cd	 = sbm.shohin_cd
				AND dm.kaisya_cd	 = sbm.kaisya_cd -- 2017/06/03 ADD Kayo
                AND sbm.delete_flg = 0
                )
        WHERE
            dm.seko_no		= :seko_no
        AND sbm.hachu_kbn	= :hachu_kbn
        AND dm.delete_flg	= 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo, 'hachu_kbn' => $hachu_kbn));
        return $select;
    }
    /**
     * 商品フリーマスタ情報取得
     *
     * <AUTHOR> Sai
     * @since      2015/07/09
     * @param      Msi_Sys_Db $db
     * @param      string      $hachu_kbn  発注書区分
     * @return     array
     */
    private static function findShohin($db, $hachu_kbn, $kaisya_cd) {
        $sql = "
            SELECT
                 sbm.shohin_cd
                ,sbm.hachu_kbn
                ,sfm.k_free1
            FROM
                shohin_bunrui_mst sbm
                INNER JOIN
                    shohin_free_mst sfm
                ON  (
                        sbm.shohin_cd     = sfm.shohin_cd
                    AND sbm.dai_bunrui_cd = sfm.dai_bunrui_cd   -- 大分類コード 2016/04/20 ADD Kayo
                    AND sbm.chu_bunrui_cd = sfm.chu_bunrui_cd   -- 中分類コード 2016/04/20 ADD Kayo
                    AND sbm.shohin_kbn    = sfm.shohin_kbn      -- 商品区分     2016/04/20 ADD Kayo
					AND sbm.kaisya_cd	  = sfm.kaisya_cd		-- 2017/06/03 ADD Kayo
                    AND sfm.delete_flg = 0
                    )
            WHERE
                sbm.delete_flg = 0
            AND sfm.k_free1 = :k_free1
            AND sbm.kaisya_cd =:kaisya_cd
                ";
        $select = $db->easySelect($sql, array('k_free1' => $hachu_kbn,'kaisya_cd' => $kaisya_cd));
        return $select;
    }
        /**
     *
     * 施行発注管理情報取得
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $ha_rp_kbn 発注書区分
     * @param string $hachu_kbn 発注区分 91:自動（斎場運営費） 92:自動（貸布団：仮通夜） :93 自動（貸布団：通夜） 94:自動（貸布団：離れ） 95:自動（基本セット）
     * @return array
     */
    private static function selectHachuInfo($db, $sekoNo, $ha_rp_kbn, $shohin_cd, $hachu_kbn) {
        // 施行発注管理情報取得SQL
        $sql = "
        SELECT
             seko_no
            ,hachu_no
        FROM
            seko_hachu_info
        WHERE
            seko_no		= :seko_no
        AND ha_rp_kbn	= :ha_rp_kbn
        AND delete_flg	= 0
		AND shohin_cd	= :shohin_cd
        AND auto_make_flg = 1           -- 2016/06/29 ADD Kayo
        AND (k_free2     = :k_free2      -- 2016/04/20 ADD Kayo
        OR   k_free2     IS NULL)        -- 過去データの対応
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo, 'ha_rp_kbn' => $ha_rp_kbn, 'shohin_cd' => $shohin_cd, 'k_free2' => $hachu_kbn));
        return $select;
    }

    /**
     *
     * 発注管理情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param      Msi_Sys_Db $db
     * @param string $shohinCd 商品コード
     * @param string $kaisyacd 会社コード
     * @return array 発注管理情報
     */
    private static function getHachuInfoOne($db, $shohinCd, $kaisyacd) {

        $sql = "
            SELECT
                 NULL AS msi_no
                ,sm.shohin_cd
                ,skm.shohin_kbn
                ,skm.shohin_kbn_nm
                ,sm.shohin_nm
                ,sm.shohin_tkiyo_nm
                ,sbm.dai_bunrui_cd
                ,sbm.chu_bunrui_cd
                ,0  AS juchu_tnk
                ,1  AS juchu_suryo
                ,0  AS juchu_prc
                ,0  AS nebiki_prc
                ,0  AS gen_tnk
                ,NULL AS nonyu_cd
                ,NULL AS nonyu_nm
                ,NULL AS nonyu_dt
                ,sm.tani_cd
                ,sm.siire_cd
                ,sbm.hachu_kbn
            FROM
                shohin_mst sm
                INNER JOIN
                    shohin_bunrui_mst sbm
                ON  (
                        sm.shohin_cd = sbm.shohin_cd
					AND sm.kaisya_cd = sbm.kaisya_cd
					AND sm.bumon_cd  = sbm.bumon_cd
                    AND sbm.delete_flg = 0
                    )
                INNER JOIN
                    shohin_kbn_mst skm
                ON  (sbm.shohin_kbn = skm.shohin_kbn)
            WHERE
                sm.delete_flg = 0
            AND skm.delete_flg = 0
            AND sm.shohin_cd = :shohin_cd
			AND sm.kaisya_cd = :kaisya_cd
            AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                ";
        $select = $db->easySelOne($sql, array('shohin_cd' => $shohinCd, 'kaisya_cd' => $kaisyacd));
        return $select;
    }
    /**
     *
     * 発注書区分に絞って発注管理情報を削除する
     *
     * <AUTHOR> Sai
     * @since 2015/07/09
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param string $ha_rp_kbn 発注書区分
     * @param string $hachu_kbn 発注区分 91:自動（斎場運営費） 92:自動（貸布団：仮通夜） :93 自動（貸布団：通夜） 94:自動（貸布団：離れ） 95:自動（基本セット）
     * @return $cnt 処理件数
     */
    private static function deleteHachuInfoByHarpKbn($db, $sekoNo, $ha_rp_kbn, $shohin_cd, $hachu_kbn) {
        // 削除処理は論理削除(deleteflgを更新)する
        $sql = "
        UPDATE
            seko_hachu_info
        SET
            delete_flg = 1
        WHERE
            seko_no     = :seko_no
        AND ha_rp_kbn   = :ha_rp_kbn
        AND delete_flg  = 0
		AND shohin_cd   = :shohin_cd
        AND auto_make_flg= 1             -- 2016/06/29 ADD Kayo
        AND (k_free2     = :k_free2      -- 2016/04/20 ADD Kayo
        OR   k_free2     IS NULL)        -- 過去データの対応
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $sekoNo, 'ha_rp_kbn' => $ha_rp_kbn, 'shohin_cd' => $shohin_cd, 'k_free2' =>$hachu_kbn));
        return $cnt;
    }

    /**
     *
     * ≪商品選択画面用≫施行プラン値引マスタで差額が設定されているか検証する
     * （設定されている場合は設定の金額を適用）
     *
     * <AUTHOR> Kobayashi
     * @since 2017/06/22
     * @param Msi_Sys_Db $db db
     * @param string $rec
     * @param string $seko_no 施行番号
     * @param string $shohin_cd 商品コード
     */
    private static function sagakuKensho1($db, &$rec, $seko_no, $shohin_cd, $shohin_tanka_disp_char) {
        //会社コード取得
        $kaisya = App_Utils::getCtxtKaisyaEasy();
        //施行プランコードを取得
        $plan = $db->easySelOne(<<< END_OF_SQL
SELECT  seko_plan_cd
FROM    seko_kihon_info
WHERE   seko_no             =   :seko_no
        AND delete_flg      =   0
END_OF_SQL
        , array('seko_no' => $seko_no));
        //施行プラン値引きを取得
        $price = $db->easySelOne(<<< END_OF_SQL
SELECT   nebiki_prc
        ,sagaku_disp_kbn
FROM    plan_nebiki_mst
WHERE   delete_flg          = 0
        AND seko_plan_cd    = :seko_plan_cd
        AND kaisya_cd       = :kaisya_cd
        AND shohin_cd       = :shohin_cd
        AND CURRENT_DATE BETWEEN tekiyo_st_date AND tekiyo_ed_date
END_OF_SQL
        ,array('seko_plan_cd' => $plan['seko_plan_cd'], 'kaisya_cd' => $kaisya, 'shohin_cd' => $shohin_cd));
        if(!isset($price) || count($price) <= 0){	// PHP Warning 2021/01/31 UPD Kayo
            return; // 施行プラン値引マスタに登録がなければ対応なし
        }else{
            $rec['hanbai_tnk']         = $price['nebiki_prc'];
            $rec['gojokai_nebiki_prc'] = 0; // 施行プラン値引マスタに登録があれば、会員差額のみ表示
            $rec['kaiin_prc_disp_kbn'] = 0;	// 2017/09/10 ADD Kayo
            // 税込差額
            if($rec['uri_zei_kbn'] === '2'){
                $rec['hanbai_tnk_zei'] = $price['nebiki_prc'] + ($price['nebiki_prc'] * $rec['zei_rtu'] / 100);
                switch ($rec['zei_hasu_kbn']) { // 外税
                    case 0: // 切り捨て
                        floor($rec['hanbai_tnk_zei']);
                        break;
                    case 1: // 四捨五入
                        round($rec['hanbai_tnk_zei']);
                        break;
                    default: // 切り上げ
                        ceil($rec['hanbai_tnk_zei']);
                        break;
                }
            }else{ // 内税
                $rec['hanbai_tnk_zei'] = 0;
            }
            // Web表示コース
            if ($rec['hanbai_tnk'] == 0) {
                $rec['course_str']      = $shohin_tanka_disp_char;
                $rec['is_course']       = 3;
            } else {
                if ($price['sagaku_disp_kbn'] == 0) {
                    $rec['course_str']  = '差額';
                    $rec['is_course']   = 4;
				}
            }
        }
    }
    /**
     *
     * ≪見積明細画面・検索ダイアログ・受注内容変更商品Select≫施行プラン値引マスタで差額が設定されているか検証する
     * （設定されている場合は設定の金額を適用）
     *
     * <AUTHOR> Kobayashi
     * @since 2017/06/29
     * @param Msi_Sys_Db $db db
     * @param string $rec
     * @param string $seko_no 施行番号
     * @param string $shohin_cd 商品コード
     * @param string $key 一般差額が入る項目
     * @param string $priceKbn 金額の型 0:数字 1:文字
     */
    public static function sagakuKensho2($db, &$rec, $seko_no, $shohin_cd, $key, $priceKbn = 0) {
        //会社コード取得
        $kaisya = App_Utils::getCtxtKaisyaEasy();
        //施行プランコードを取得
        $plan = $db->easySelOne(<<< END_OF_SQL
SELECT  seko_plan_cd
FROM    seko_kihon_info
WHERE   seko_no             =   :seko_no
AND     delete_flg          =   0
END_OF_SQL
        , array('seko_no' => $seko_no));
        //施行プラン値引きを取得（会員価格）
        $price = $db->easySelOne(<<< END_OF_SQL
SELECT  nebiki_prc
FROM    plan_nebiki_mst
WHERE   delete_flg          = 0
        AND seko_plan_cd    = :seko_plan_cd
        AND kaisya_cd       = :kaisya_cd
        AND shohin_cd       = :shohin_cd
        AND CURRENT_DATE BETWEEN tekiyo_st_date AND tekiyo_ed_date
END_OF_SQL
        ,array('seko_plan_cd' => $plan['seko_plan_cd'], 'kaisya_cd' => $kaisya, 'shohin_cd' => $shohin_cd));
        if(!$price){
            return; // 施行プラン値引マスタに登録がなければ対応なし
        }

        //2017/09/10 DEL Kayo $kaiinCdKey = substr($plan['seko_plan_cd'], 1, 1);
        //2017/09/10 DEL Kayo if($kaiinCdKey === '1'){    // 一般
            if($priceKbn == 0){
                $rec[$key] = $price['nebiki_prc'];
                $rec['gojokai_nebiki_prc'] = 0;
                $rec['kaiin_prc_disp_kbn'] = 0;	// 2017/09/10 ADD Kayo -->2018/04/06 Kobayashi JuchuhenkoSnAbstractで差額対象商品かの判断にも使用
            }else{
                $rec[$key] = number_format($price['nebiki_prc']);
                $rec['gojokai_nebiki_prc'] = 0;
                $rec['kaiin_prc_disp_kbn'] = 0;	// 2017/09/10 ADD Kayo -->2018/04/06 Kobayashi JuchuhenkoSnAbstractで差額対象商品かの判断にも使用
            }
		/*	2017/09/10 DEL Kayo
        }else{	// 会員は一般のコードに変換
            $ippan_plan_cd = substr_replace($plan['seko_plan_cd'], 1, 1, 1);
			//施行プラン値引きを取得（一般価格）
			$ippan_price = $db->easySelOne(<<< END_OF_SQL
SELECT  nebiki_prc
FROM    plan_nebiki_mst
WHERE   delete_flg          = 0
        AND seko_plan_cd    = :seko_plan_cd
        AND kaisya_cd       = :kaisya_cd
        AND shohin_cd       = :shohin_cd
END_OF_SQL
			,array('seko_plan_cd' => $ippan_plan_cd, 'kaisya_cd' => $kaisya, 'shohin_cd' => $shohin_cd));
            if($priceKbn == 0){
                $rec[$key] = $ippan_price['nebiki_prc'];
                $rec['gojokai_nebiki_prc'] = $price['nebiki_prc'] - $ippan_price['nebiki_prc'];
				$rec['kaiin_prc_disp_kbn']= 0;	// 2017/09/10 ADD Kayo
            }else{
                $rec[$key] = number_format($ippan_price['nebiki_prc']);
                $rec['gojokai_nebiki_prc'] = number_format($price['nebiki_prc'] - $ippan_price['nebiki_prc']);
				$rec['kaiin_prc_disp_kbn']= 0;	// 2017/09/10 ADD Kayo
            }
        }
		*/
    }

    /**
     * 受注伝票より見積金額を取得する
     *
     * <AUTHOR> Kayo
     * @since 2017/11/24
     * @param   string $sekoNo
     * @param   string $sekoNoSub
     * @param   int $dataKbn
     * @return  number 金額
     */
    public static function getMitsuKingakuSQL($sekoNo, $sekoNoSub, $dataKbn = 1) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = '
                SELECT
                    -- 受注金額合計 - 受注返品合計 + 奉仕料金額合計 + 値引き金額合計
                    juchu_prc_sum + juchu_hepn_sum + hoshi_prc_sum + juchu_nebk_sum AS prc_sum
                FROM
                    juchu_denpyo
                WHERE
                    seko_no=?
                AND seko_no_sub=?
                AND data_kbn=?
                AND delete_flg=0';
        $selOne = $db->easySelOne($sql, array($sekoNo, $sekoNoSub, $dataKbn));
        return  $selOne;
    }

    /**
     * 共通商品→会場別商品の設定
     * <AUTHOR> Kobayashi
     * @since 2018/02/28
     * @param $db
     * @param $kaisya_cd 会社コード
     * @param $dataDtl 伝票明細に登録する情報
     */
    public function setShohinMstByKaijyo($db, $kaisya_cd, $seko_no, &$dataDtl) {
        // 葬儀場所取得
        $sp_basho_cd = App_Utils2::getSpBashoCdBySougi( $seko_no, $db );
        if($sp_basho_cd){
            foreach ($dataDtl as &$rec) {
                $rec['kaijyo_tanka_kbn']    = 0;        // 0:通常単価 1:会場別単価  2018/08/31 ADD Kayo
                //WEB商品表示の表示区分が「会場別表示商品」の商品のみ対象
                $sql = "
                    SELECT
                        wsm.shohin_cd
                    FROM
                        web_disp_shohin_mst wsm
                    WHERE
                        wsm.disp_kbn   IN ('2','3') --会場別表示商品
                    AND wsm.shohin_cd  = :shohin_cd
                    AND wsm.kaisya_cd  = :kaisya_cd
                    AND ( wsm.tekiyo_st_date <= CURRENT_DATE AND wsm.tekiyo_ed_date >= CURRENT_DATE )
                    AND wsm.delete_flg = 0
                    ";
                $select = $db->easySelOne($sql, array('shohin_cd' => $rec['shohin_cd'], 'kaisya_cd' => $kaisya_cd));
                if(isset($select) && count($select) > 0){
                    // 会場別商品の存在確認
                    $kaijyo_shohin_cd = DataMapper_ShohinFreeMstUtil::shohinTankaMstByKaijyo($db, $sp_basho_cd, $select['shohin_cd'], date_create()->format('YYYY/MM/DD'));
                    if (isset($kaijyo_shohin_cd)) {
                        $kaijo_shohin_info = DataMapper_Shohin::find($db, array('shohin_cd' => $kaijyo_shohin_cd['shohin_cd']));
                        if ( count($kaijo_shohin_info) > 0 ) {
                            // 会場別商品の情報を上書きする
                            $kaijyo_rec = $kaijo_shohin_info[0];
                            $rec['shohin_cd']			= $kaijyo_shohin_cd['com_shohin_cd']; // 共通商品コード
                            $rec['shohin_cd_kaijyo']	= $kaijyo_rec['shohin_cd'];		// 商品コード
                            $rec['juchu_tnk']			= $kaijyo_rec['hanbai_tnk'];	// 受注単価
                            $rec['juchu_prc']			= $kaijyo_rec['hanbai_tnk'];	// 受注金額
                            $rec['hanbai_tnk']			= $kaijyo_rec['hanbai_tnk'];	// 販売単価
                            $rec['kaijyo_tanka_kbn']    = 1;                            // 0:通常単価 1:会場別単価  2018/08/31 ADD Kayo
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 担当者の権限名を取得する
     * <AUTHOR> Tosaka
     * @since 2018/10/22
     * @param $tanto_cd 担当コード
     */
    public function getRoleNm($tanto_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = '
                SELECT roles
                FROM login_mst
                WHERE tanto_cd = :tanto_cd
                AND delete_flg=0';
        $selOne = $db->easySelOne($sql, array('tanto_cd' => $tanto_cd));
        return  $selOne['roles'];
    }
    
    /**
     * 受付番号を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2018/09/14
     * @param   string $sekoNo 
     * @return  number 金額
     */
    public static function getUketsukeNo($sekoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $uketsuke_no = null;
        // 受付番号を取得する
        $sql = "SELECT ski.daicho_no_eria
                FROM seko_kihon_info ski
                WHERE ski.seko_no = :seko_no
		AND delete_flg=0
			";
        $select = $db->easySelect($sql, array('seko_no' => $sekoNo));
        if (count($select) > 0) {
            $uketsuke_no = $select[0]['daicho_no_eria'];
        }
        return $uketsuke_no;
    }
    
    /**
     * 受注登録後の付加処理
     * CTI連携 ファイル出力
     *
     * <AUTHOR> Tosaka
     * @since  2019/11/12
     * @param  Msi_Sys_Db  $db
     * @param  string      $seko_no
     * @param  array       $opt         処理オプション
     * @return void
     */
    public static function doProcAfterJuchuUpd($db, $seko_no, $opt = array()) {
        try {
            // Msi_Sys_Utils::profilerMark('pre-doProcAfterJuchuUpd'); // XXX DELME
            // CTI連携 ファイル出力
//            $is_force_send = false;
//            if (isset($opt['is_force_send'])) {
//                $is_force_send = !!$opt['is_force_send'];
//            }
            $isCti = Logic_Exdata_Send06644460::output_cti_data($db, $seko_no, false);

            // output_eboard_data()/output_shindai_data() 両方で sftp_put07121270.php をコールした際
            // ほぼ同時に実行されるためのロックエラーを避けるため(エラーが出ても実害はないが)
            $isSendInline = false;
            if ($isCti) {
                $isCti = Msi_Sys_Utils::getExConfigByKeyDefault('msi.app.rendo.cti.sendInline', false);
            }
            if ($isSendInline) {
                // backgound 処理でファイル送信する
                Logic_Exdata_Send06644460::call_bg_sftp_put06644460($seko_no);
            }

            // Msi_Sys_Utils::profilerMark('POST-doProcAfterJuchuUpd'); // XXX DELME
        } catch (Exception $e) {
            // すでに commit() している場合があるのでエラーにはしない
            $err = $e->getMessage();
            Msi_Sys_Utils::warn($err);
            return;
        }
    }

    /**
     * 姓名分割処理
     *
     * <AUTHOR>  Oka
     * @since      2017/01/06
     * @param type $name    故人名
     * @return  array 姓,名
     */
    public function split_name($name) {
        //全角スペースを半角スペースに変換
        $name = str_replace('　', ' ', $name);
        //前後のスペース削除（trimの対象半角スペースのみなので半角スペースに変換後行う）
        $name = trim($name);
        //連続する半角スペースを半角スペースひとつに変換
        $name = preg_replace('/\s+/', ' ', $name);
        //姓と名で分割
        $name = explode(' ', $name);

        $last_name = $first_name = null;

        if (!empty($name[0])) {
            $last_name = $name[0];
        }
        if (!empty($name[1])) {
            $first_name = $name[1];
        }
        return [$last_name, $first_name];
    }
}
