<?php

/**
 * PDF フリー生花受注一覧
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Tosaka
 * @since      2020/xx/xx
 * @filesource 
 */

/**
 * PDF フリー生花受注一覧
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Tosaka
 * @since      2020/xx/xx
 */
class Kanri_Pdf0604Controller extends Zend_Controller_Action {

    private static $title = 'フリー生花受注一覧';
    private static $sourceFileName = 'pdf_tmpl/0604.pdf';
    private static $row_height = 67.8;
    private static $row_count = 8;
    private static $CHU_BUNRUI_BIKO = 100;

    /**
     * アクション
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     */
    public function indexAction($paramsData = array()) {

        if (count($paramsData) > 0) {
            $params = $paramsData;
            $dataAppAry = $paramsData;
        } else {
            $params = Msi_Sys_Utils::webInputs();
            $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        }

        $where = array();
        $prn_ymd = date('Y/m/d');
        $taisho_st_ymd = null;
        $taisho_ed_ymd = null;
        $bumon_cd = null;

        // 印刷年月日
        if (array_key_exists('print_ymd', $dataAppAry) && $dataAppAry['print_ymd'] != '') {
            $prn_ymd = htmlspecialchars($dataAppAry['print_ymd']);
        }
        // 対象開始日
        if (array_key_exists('taisho_st_ymd', $dataAppAry) && $dataAppAry['taisho_st_ymd'] != '') {
            $taisho_st_ymd = htmlspecialchars($dataAppAry['taisho_st_ymd']);
            $where['taisho_st_ymd'] = $taisho_st_ymd;
        }
        // 対象終了日
        if (array_key_exists('taisho_ed_ymd', $dataAppAry) && $dataAppAry['taisho_ed_ymd'] != '') {
            $taisho_ed_ymd = htmlspecialchars($dataAppAry['taisho_ed_ymd']);
            $where['taisho_ed_ymd'] = $taisho_ed_ymd;
        }
        // 部門コード
        if (array_key_exists('s_bumon', $dataAppAry) && $dataAppAry['s_bumon'] != '') {
            $bumon_cd = htmlspecialchars($dataAppAry['s_bumon']);
            $where['bumon_cd'] = $bumon_cd;
        }

        $db = Msi_Sys_DbManager::getMyDb();

        $pdfObj = new App_Pdf(self::$title);
        $ret = self::outData($pdfObj, $db, $prn_ymd, $where);
        if ($ret != App_PdfKanriLib::STATUS_OK) {
            App_PdfKanriLib::err($ret);
            return;
        }
        $pdfObj->download();
    }

    private function outData($pdfObj, $db, $prn_ymd, $where) {
        $pdfObj->set_default_font_size(10);
        $pdfObj->set_default_minus_font_color('red');

        $headRec = DataMapper_Pdf0604::findHead($db, $where);
        if (count($headRec) == 0) {
            return App_PdfKanriLib::STATUS_NODATA;
        }
        $todoke = null;
        if (isset($where['taisho_st_ymd']) && isset($where['taisho_ed_ymd'])) {
            $todoke = $where['taisho_st_ymd'].'～'.$where['taisho_ed_ymd'];
        } else if (isset($where['taisho_st_ymd'])) {
            $todoke = $where['taisho_st_ymd'].'～';
        } else if (isset($where['taisho_st_ymd'])) {
            $todoke = '～'.$where['taisho_ed_ymd'];
        }

        $row_ary = array();
        $total_sekyu_prc = 0;
        $brk_new_bumon_cd = null;
        $brk_old_bumon_cd = null;
        $ini_flg = 0;
        $msi_no = 1;
        foreach ($headRec as $head) {
            $uri_den_no = $head['uri_den_no'];
            $rec = DataMapper_Pdf0604::find($db, array('uri_den_no' => $uri_den_no, 'chu_bunrui_biko' => self::$CHU_BUNRUI_BIKO));
            if (count($rec) == 0) {
                continue;
            }
            $page = $pdfObj->getNumPages() + 1;
            $i = 0;
            for ($index = 0, $len = count($rec); $index < $len; $index++) {
                $row = $rec[$index];
                $brk_new_bumon_cd = $row['h_bumon_nm'];   // ブレークキーを設定
                if ($ini_flg == 0) {
                    $brk_old_bumon_cd = $brk_new_bumon_cd;
                    $ini_flg = 1;
                }
                // 部門コードがブレークしたか
                if ($brk_new_bumon_cd != $brk_old_bumon_cd) {
                    // 部門ブレーク
                    $head_ary = array(array('x' => 75, 'y' => 95, 'width' => 80, 'height' => 15, 'value' => $brk_old_bumon_cd));
                    $pdfObj->write_table(self::getSetAry(), self::$row_height, $row_ary, self::$row_count, __DIR__ . '/' . self::$sourceFileName, 1, $head_ary);
                    $row_ary = array();
                    $msi_no = 1;
                }

                $yubin = null;
                if (strlen($row['soufu_yubin_no']) > 0) {
                    $yubin = '〒' . $row['soufu_yubin_no'] . ' ';
                }

                $ary = array();
                $ary[] = $msi_no;
                // 法人区分が設定されていれば値によって請求先名の表示を変更する
                $sekyu_nm = null;
                $soufu_nm = null;
                if ($row['kokyaku_kbn'] == '1') {
                    if (isset($row['sekyu_nm'])) {
                        $sekyu_nm = $row['sekyu_nm'];
                    }
                } else {
                    if (isset($row['sekyu_nm1']) && isset($row['sekyu_nm2'])) {
                        $sekyu_nm = $row['sekyu_nm1'].' '.$row['sekyu_nm2'];
                    } else if (isset($row['sekyu_nm1'])) {
                        $sekyu_nm = $row['sekyu_nm1'];
                    } else if (isset($row['sekyu_nm2'])) {
                        $sekyu_nm = $row['sekyu_nm2'];
                    }
                }
                if ((isset($row['sekyu_soufu_nm'])) && ($row['sekyu_soufu_nm'] != "")) {
                    if (isset($row['sekyu_soufu_nm']) && isset($row['sekyu_soufu_nm2'])) {
                        $soufu_nm = $row['sekyu_soufu_nm'].' '.$row['sekyu_soufu_nm2'];
                    } else if (isset($row['sekyu_soufu_nm'])) {
                        $soufu_nm = $row['sekyu_soufu_nm'];
                    } else if (isset($row['sekyu_soufu_nm2'])) {
                        $soufu_nm = $row['sekyu_soufu_nm2'];
                    }
                }
                $ary[] = $sekyu_nm;
                $ary[] = $soufu_nm;
                $nebiki_ritsu = null;
                if (isset($row['n_free2'])) {
                    $nebiki_ritsu = $row['n_free2'] . '%';
                }
                $ary[] = $row['soufu_addr1'];
                $ary[] = $row['soufu_addr2'];
//                $ary[] = $row['soufu_tel'];
                $ary[] = $row['sekyu_bank_nm'];
                $ary[] = $row['nyukin_ymd'];
                $ary[] = $row['seikyu_den_no'];
                $ary[] = '領収証：'.$row['rs_print_nm'];
                $ary[] = $row['denpyo_no'];
                $ary[] = $row['shohin_tkiyo_nm'];
                $ary[] = $row['juchu_ymd'];
                $ary[] = $row['format_nm'];
                $ary[] = $row['status_nm'];
                $ary[] = $row['shohin_nm'];
                $ary[] = $row['nonyu_dt'];
                $ary[] = $row['nonyu_nm'];
                $ary[] = number_format($row['uri_tnk']);
                $ary[] = $row['juchu_suryo'];
                $ary[] = $nebiki_ritsu;
                $ary[] = number_format($row['sekyu_prc']);
                $ary[] = $row['keiyaku_nm'];
                $ary[] = $row['area_nm'];
                $ary[] = $row['nafuda_nm1'];
                $ary[] = $row['nafuda_nm2'];
                $ary[] = $row['nafuda_nm3'];
                $ary[] = $row['nafuda_nm4'];
                $row_ary[] = $ary;
                $total_sekyu_prc += $row['sekyu_prc'];
                $i++;
                $msi_no++;
                $brk_old_bumon_cd = $row['h_bumon_nm'];   // ブレークキーを設定
            }


            // 確認用のグリッドを出力
            //$pdfObj->test_line_out(600, 850);
        }
        // 出力
        $head_ary = array(array('x' => 75, 'y' => 95, 'width' => 80, 'height' => 15, 'value' => $row['h_bumon_nm']));
        $pdfObj->write_table(self::getSetAry(), self::$row_height, $row_ary, self::$row_count, __DIR__ . '/' . self::$sourceFileName, 1, $head_ary);

        // 明細がない場合、終了（明細未入力は入力時にガードするので発生しないはず）
        if ($pdfObj->getNumPages() == 0) {
            return App_PdfKanriLib::STATUS_NODATA;
        }

        // 全ページ共通項目を出力
        $pdfObj->header_out(array(
            array('x' => 460, 'y' => 81, 'width' => 120, 'height' => 15, 'value' => $prn_ymd, 'format' => 'Y/m/d', 'type' => 'ymd'), // 作成日
            array('x' => 75, 'y' => 108, 'width' => 150, 'height' => 15, 'value' => $todoke), // 届け日
            array('x' => 18, 'y' => 780, 'width' => 560, 'height' => 15, 'type' => 'page', 'align' => 'C') // ページ
        ));
    }

    /**
     * @version    2017/08/31 PHYO
     */
    private function getSetAry() {
        static $row_top = 235;
        static $y1 = 0;
        static $y2 = 13;
        static $y3 = 26;
        static $y4 = 39;
        static $y5 = 55;

        $set_ary[] = array('x' => 18, 'y' => $row_top + $y1, 'width' => 25, 'height' => 15, 'type' => 'num', 'align' => 'C'); // No.
        $set_ary[] = array('x' => 43, 'y' => $row_top + $y1, 'width' => 158, 'height' => 15); // 請求先名
        $set_ary[] = array('x' => 43, 'y' => $row_top + $y2, 'width' => 158, 'height' => 15); // 請求書送付先名
        $set_ary[] = array('x' => 43, 'y' => $row_top + $y3, 'width' => 158, 'height' => 15); // 請求先住所1
        $set_ary[] = array('x' => 43, 'y' => $row_top + $y4, 'width' => 158, 'height' => 15); // 請求先住所2
//        $set_ary[] = array('x' => 43, 'y' => $row_top + $y4, 'width' => 158, 'height' => 15); // 請求先TEL
        $set_ary[] = array('x' => 43, 'y' => $row_top + $y5, 'width' => 158, 'height' => 15); // 振込先
        $set_ary[] = array('x' => 201, 'y' => $row_top + $y1, 'width' => 52, 'height' => 15, 'align' => 'C'); // 入金日
        $set_ary[] = array('x' => 201, 'y' => $row_top + $y2, 'width' => 52, 'height' => 15, 'align' => 'C'); // 売上伝票NO.
        $set_ary[] = array('x' => 201, 'y' => $row_top + $y3, 'width' => 52, 'height' => 15, 'align' => 'C'); // 領収証
        $set_ary[] = array('x' => 201, 'y' => $row_top + $y4, 'width' => 52, 'height' => 15, 'align' => 'C'); // 受注伝票No
        $set_ary[] = array('x' => 201, 'y' => $row_top + $y5, 'width' => 100, 'height' => 15, 'align' => 'C'); // 商品摘要
        $set_ary[] = array('x' => 253, 'y' => $row_top + $y1, 'width' => 47, 'height' => 15, 'type' => 'ymd', 'format' => 'm/d', 'align' => 'C'); // 受付
        $set_ary[] = array('x' => 253, 'y' => $row_top + $y2, 'width' => 47, 'height' => 15, 'align' => 'C'); // 請求フォーマット
        $set_ary[] = array('x' => 253, 'y' => $row_top + $y3, 'width' => 47, 'height' => 15, 'align' => 'C'); // ステータス
        $set_ary[] = array('x' => 300, 'y' => $row_top + $y1, 'width' => 110, 'height' => 15); // 商品名
        $set_ary[] = array('x' => 410, 'y' => $row_top + $y1, 'width' => 60, 'height' => 15, 'type' => 'time', 'format' => 'm/d', 'align' => 'C'); // お届け日
        $set_ary[] = array('x' => 470, 'y' => $row_top + $y1, 'width' => 103, 'height' => 15); // お届け先
        $set_ary[] = array('x' => 300, 'y' => $row_top + $y2, 'width' => 55, 'height' => 15, 'align' => 'C'); // 単価
        $set_ary[] = array('x' => 355, 'y' => $row_top + $y2, 'width' => 40, 'height' => 15, 'type' => 'num'); // 数量
        $set_ary[] = array('x' => 405, 'y' => $row_top + $y2, 'width' => 40, 'height' => 15); // 割引率
        $set_ary[] = array('x' => 460, 'y' => $row_top + $y2, 'width' => 80, 'height' => 15, 'align' => 'R'); // 請求額
        $set_ary[] = array('x' => 300, 'y' => $row_top + $y3, 'width' => 230, 'height' => 15); // 契約団体
        $set_ary[] = array('x' => 530, 'y' => $row_top + $y3, 'width' => 40, 'height' => 15); // エリア
        $set_ary[] = array('x' => 300, 'y' => $row_top + $y4, 'width' => 130, 'height' => 15); // 名札1
        $set_ary[] = array('x' => 435, 'y' => $row_top + $y4, 'width' => 130, 'height' => 15); // 名札2
        $set_ary[] = array('x' => 300, 'y' => $row_top + $y5, 'width' => 130, 'height' => 15); // 名札3
        $set_ary[] = array('x' => 435, 'y' => $row_top + $y5, 'width' => 130, 'height' => 15); // 名札4

        return $set_ary;
    }

}
