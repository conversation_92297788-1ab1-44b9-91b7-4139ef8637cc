<?php
  /**
   * Kanri_Hachulist22Controller
   *
   * 発注管理 コントローラクラス
   *
   * @category   App
   * @package    controllers\Hachu
   * <AUTHOR> Mihara
   * @since      2014/06/xx
   * @filesource 
   */

  /**
   * 発注管理 コントローラクラス
   *
   * @category   App
   * @package    controllers\Hachu
   * <AUTHOR> Mihara
   * @since      2014/06/xx
   */
class Kanri_Hachulist22Controller extends Msi_Zend_Controller_Action
{
    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2014/06/xx
     */
	public function indexAction()
    {
        $this->_forward( 'main' );
	}

    /**
     * main アクション
     *
     * <AUTHOR> Mihara
     * @since  2014/06/xx
     */
    public function mainAction()
    {
        $req = $this->getRequest();

        $params  = Msi_Sys_Utils::webInputs();
        Msi_Sys_Utils::debug( 'hachulist22 params==>' . Msi_Sys_Utils::dump($params) );
        $readonly = Msi_Sys_Utils::isReadOnlyCtxt();
        // 参照専用
        if ( $readonly ) {
            $this->view->ctxt_readonly = 'my-ctxt-readonly';
        }

        App_Smarty::pushCssFile( ['app/kanri.hachulist22.css'] );

        App_Smarty::pushJsFile( ['app/kanri.hachulist22.js'] );

        // 初期値
        $data = array( 
                'dataApp' => array(
                                   's_sougi_from' => null,
                                   's_sougi_to'   => null,
                                   's_apply' => null, // 1,
                                   's_status_kbn' => null, // 1,
                                   's_order_flg' => 0, // 0:未発注
                                   's_chk_order_flg' => 0, // 0:未確認
                                   'init_search' => false,
                                   'sys_ymd' => Msi_Sys_Utils::getDate(), 
                                   ),
                       );
        // 上書き設定
        if ( Msi_Sys_Utils::globalGet('_dataApp_init') ) { // cf. Kanri_DispController
            $data['dataApp'] = Msi_Sys_Utils::globalGet('_dataApp_init');
        }

        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        $this->view->page_title = '発注管理';
    }

    /**
     * @ignore
     * 検索条件パラメタ
     */
    protected $_params = null;

    /**
     * 検索結果表示 アクション
     *
     * <AUTHOR> Mihara
     * @since 2014/06/xx
     * @version 2014/12/18 orderbyを変更 Kayo
     */
	public function searchAction()
    {
        try {
            if ( isset($this->_params) ) {
                $params  = $this->_params;
            } else {
                $this->_params = $params = Msi_Sys_Utils::webInputs();
            }
            // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );

            $db = Msi_Sys_DbManager::getMyDb();

            $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 300);

            $cond = array(
                          '__etc_orderby' => array('seko_no DESC', 'jc_msi_no ASC'),
                          '__etc_limit'   => $limit +1,
                          );

            $seko_no          = Msi_Sys_Utils::easyGetVar($params, 's_number');
            $daicho_no_eria   = Msi_Sys_Utils::easyGetVar($params, 's_code_1');
            $daicho_no_mm     = Msi_Sys_Utils::easyGetVar($params, 's_code_2');
            $daicho_no_seq    = Msi_Sys_Utils::easyGetVar($params, 's_code_3');
            $mg_tel           = Msi_Sys_Utils::easyGetVar($params, 's_tel');
            $uketuke_tanto_cd = Msi_Sys_Utils::easyGetVar($params, 's_staff_1');
            $data_kbn         = Msi_Sys_Utils::easyGetVar($params, 's_apply');
            $status_kbn_2     = Msi_Sys_Utils::easyGetVar($params, 's_ontime');
            $souke_nm         = Msi_Sys_Utils::easyGetVar($params, 's_name_1');
            $k_nm             = Msi_Sys_Utils::easyGetVar($params, 's_name_2');
            $m_nm             = Msi_Sys_Utils::easyGetVar($params, 's_name_3');
            $seko_tanto_cd    = Msi_Sys_Utils::easyGetVar($params, 's_staff_2');
            $sougi_ymd_y      = Msi_Sys_Utils::easyGetVar($params, 's_year');
            $sougi_ymd_m      = Msi_Sys_Utils::easyGetVar($params, 's_month');
            $status_kbn       = Msi_Sys_Utils::easyGetVar($params, 's_status_kbn');
            $bumon            = Msi_Sys_Utils::easyGetVar($params, 's_bumon');
            $basho_cd         = Msi_Sys_Utils::easyGetVar($params, 's_kaijyosiki');
            $sougi_from       = Msi_Sys_Utils::easyGetVar($params, 's_sougi_from', 'DATE', null, 'emptyToNull');
            $sougi_to         = Msi_Sys_Utils::easyGetVar($params, 's_sougi_to', 'DATE', null, 'emptyToNull');
            $order_flg        = Msi_Sys_Utils::easyGetVar($params, 's_order_flg');
            $chk_order_flg    = Msi_Sys_Utils::easyGetVar($params, 's_chk_order_flg');
            $ha_rp_cd         = Msi_Sys_Utils::easyGetVar($params, 's_ha_rp');
            $siire_cd         = Msi_Sys_Utils::easyGetVar($params, 's_siire');
            $moke_no = Msi_Sys_Utils::easyGetVar($params, 's_moke_no'); //  MSI HITESH ADD 2018/10/23

            
            App_Utils::setSessionData('data_kbn', $data_kbn);            
            
            // 発注書その他区分 が 0 のものだけ表示する
            $cond['ha_etc_kbn'] = 0;

            // 部分一致
            foreach ( Msi_Sys_Utils::strArrayify_qw('seko_no mg_tel souke_nm k_nm m_nm') as $k ) {
                if ( strlen($$k) > 0 ) {
                    $qm = quotemeta($$k);
                    $cond[$k] = array( '~', $qm );
                    // $cond[$k] = array( '~', $$k );
                }
            }

            // 完全一致
            foreach ( Msi_Sys_Utils::strArrayify_qw( 'daicho_no_eria daicho_no_mm daicho_no_seq ' . 
                                                     ' uketuke_tanto_cd data_kbn seko_tanto_cd ' .
                                                     'status_kbn  basho_cd  order_flg chk_order_flg ' .
                                                     'ha_rp_cd siire_cd'
                                                     ) as $k ) {
                if ( strlen($$k) > 0 ) {
                    $cond[$k] = $$k;
                }
            }

            // 施行中
            if ( strlen($status_kbn_2) > 0 && !!$status_kbn_2 ) {
                $cond['status_kbn'] = 2;
            }

            // 葬儀日
            if ( strlen($sougi_ymd_y) > 0 ) {
                $cond['__x1'] = array( 'x', "(TO_CHAR(sougi_ymd::date, 'YYYY') = :x1_1)", array('x1_1'=>$sougi_ymd_y) );
            }
            if ( strlen($sougi_ymd_m) > 0 ) {
                $sougi_ymd_m = substr('0'.$sougi_ymd_m, -2);
                $cond['__x2'] = array( 'x', "(TO_CHAR(sougi_ymd::date, 'MM') = :x2_1)", array('x2_1'=>$sougi_ymd_m) );
            }
            if ( strlen($sougi_from) > 0 ) {
                $cond['__x3'] = array( 'x', "sougi_ymd >= :x3_1", array('x3_1'=>$sougi_from) );
            }
            if ( strlen($sougi_to) > 0 ) {
                $cond['__x4'] = array( 'x', "sougi_ymd <= :x4_1", array('x4_1'=>$sougi_to) );
            }
            if (strlen($moke_no) > 0) { //  MSI HITESH ADD 2018/10/23
                $cond['__raw_moke_no'] = "(moke_no::varchar(100) LIKE '%$moke_no%')";
            }

            // 部門.  配下(ホール)へ展開すること
            if ( strlen($bumon) > 0 ) {
                $cond['__x5'] = DataMapper_BumonEx::findDesCond( $db, $bumon );
            }
            // 複数会社対応
            if (App_Utils::isFukusuKaisyaKanri()) {
                $KaisyaCd = App_Utils::getCtxtKaisyaEasy();
                $cond['kaisya_cd'] = $KaisyaCd;
            }

            $select = DataMapper_SekoHachuInfoEx::find( $db, $cond );

            $data = array();
            $count = 0;
            foreach ( $select as $rec ) {
                $count++;
                if ( $count > $limit ) {
                    break;
                }
                $rec['_juchu_prc_sum'] = Msi_Sys_Utils::filterComma( $rec['juchu_prc_sum'] );
                $rec['_uri_prc_sum'] = Msi_Sys_Utils::filterComma( $rec['uri_prc_sum'] );
                $rec['oddEven'] = $count % 2 ? 'odd' : 'even';
                $rec['my_id'] = $rec['seko_no'] . '-' . $rec['hachu_no'];
                $rec['siire_cd'] = $rec['siire_cd'];
                $data[] = $rec;
            }

            /*
              $data = array_merge( $data, $data, $data, $data, $data, 
              $data, $data, $data, $data, $data, 
              $data, $data, $data, $data, $data,
              $data, $data, $data, $data, $data );
            */

            // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );
        }
        catch ( Msi_Sys_Exception_InputException $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::debug( '検索条件エラー=>' . $err );
            $msg = '検索条件エラーです';
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $msg = '内部エラーです';
        }

        if ( isset($data) ) {
            $this->view->list_count = $count;
            $this->view->list_data = $data;
            if ( $count <= 0 ) {
                $msg = '該当するデータはありません';
            } else if ( $count > $limit ) {
                $msg = '該当するデータが他に存在します';
            }
        }

        if ( isset($msg) ) {
            $this->view->my_msg = $msg;
        }

        $this->_helper->viewRenderer->setScriptAction( 'result-list' );
    }

    /**
     * 発注確認 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/06/xx
     */
	public function setchkAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );

        $reqData = $params['hachu_nos'];
        $chk_order_flg = $params['chk_order_flg'];

        $data = array();
        foreach ( $reqData as $one ) { 
            list( $seko_no, $hachu_no ) = preg_split('/-/', $one);
            $data[] = array( 'seko_no'  => $seko_no,
                             'hachu_no' => $hachu_no );
        }

        $tanto_cd = App_Utils::getTantoCd();

        try {
            $db = Msi_Sys_DbManager::getMyDb();
            if ( $chk_order_flg ) { // 発注確認
                $sql = <<< END_OF_SQL
UPDATE seko_hachu_info
   SET chk_order_flg=1,
       chk_order_tanto_cd='{$tanto_cd}',
       chk_order_ymd=CURRENT_TIMESTAMP
 WHERE seko_no=:seko_no
   AND hachu_no=:hachu_no
   AND delete_flg=0
END_OF_SQL;
            
    $sime_ymd = App_DateCalc::getSiireShimeYmd($db, Msi_Sys_Utils::getDate(), null);   
    $harai_yotei_ymd = App_DateCalc::getSiireHraiyoteiYmd($db, $sime_ymd, null);
       $sql2 = <<< END_OF_SQL
  UPDATE hachu_denpyo h
   SET 
       sime_ymd='{$sime_ymd}',
       harai_yotei_ymd='{$harai_yotei_ymd}'
   FROM seko_hachu_info s
 WHERE s.seko_no=:seko_no
   AND s.hachu_no=:hachu_no
   AND s.delete_flg=0
   AND h.delete_flg=0
   AND h.denpyo_no=s.hc_denpyo_no
END_OF_SQL;
                $msg = '発注確認を設定しました';
            }
            else { // 発注確認取消
                $sql = <<< END_OF_SQL
UPDATE seko_hachu_info
   SET chk_order_flg=0,
       chk_order_tanto_cd=NULL,
       chk_order_ymd=NULL
 WHERE seko_no=:seko_no
   AND hachu_no=:hachu_no
   AND delete_flg=0
END_OF_SQL;
                $msg = '発注確認を取消しました';
       $sql2 = <<< END_OF_SQL
  UPDATE hachu_denpyo h
   SET 
       sime_ymd=null,
       harai_yotei_ymd=null
   FROM seko_hachu_info s
 WHERE s.seko_no=:seko_no
   AND s.hachu_no=:hachu_no
   AND s.delete_flg=0
   AND h.delete_flg=0
   AND h.denpyo_no=s.hc_denpyo_no
END_OF_SQL;
            }

            $db->easyExecute( $sql, $data );

            $cnt = $db->easyExecute( $sql2, $data );
            $db->commit();

            $outData = array(
                             'status' => 'OK',
                             'msg' => $msg,
                             'reqData' => $reqData,
                             'chk_order_flg' => $chk_order_flg,
                             );
        } 
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $outData = array(
                             'status' => 'NG',
                             'msg' => $userMsg,
                             );
        }

        Msi_Sys_Utils::outJson( $outData );
    }


}
