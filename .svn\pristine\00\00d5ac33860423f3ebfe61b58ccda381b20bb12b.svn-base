<?php
/**
 * Ka<PERSON><PERSON>_HansoiraiprnController
 *   cf. Ka<PERSON><PERSON>_UrimsiprnController
 *
 * EDI受注状況一覧出力　コントローラクラス
 *
 * @category   App
 * @package    controller\Kanri
 * <AUTHOR> Kobayashi
 * @since      2021/10/xx
 * @filesource 
 */
class Kanri_EdijuchuprnController extends Msi_Zend_Controller_Action {

    private static $title = 'EDI受注状況';

    /**
     * EDI受注状況一覧出力 コントローラクラス
     *
     * @category   App
     * @package    controller\saiken
     * <AUTHOR> Kobayashi
     * @since      2021/10/xx
     */
    public function indexAction() {
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile([
            'app/kanri.edijuchuprn.js'
        ]);

        // 利用 CSS 設定
        App_Smarty::pushCssFile([
            'app/kanri.print.siji.css',
            'app/kanri.kaikeiprint.siji.css',
            'app/kanri.edijuchuprn.css',
        ]);

        //画面初期表示時のデータ取得
        $data = $this->_getInitData();
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
    }

    /**
     * 初期表示データ 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2021/10/xx
     */
    private function _getInitData() {

        $dataApp = array(
            'st_ymd' => date('Y/m/d', strtotime(date('Y-m-1').' -1 month')),  // 前月1日
            'ed_ymd' => date('Y/m/t', strtotime(date('Y-m-1').' -1 month')),  // 前月末日
        );

        $data = array(
            'dataApp' => $dataApp,
        );

        return $data;
    }

    /**
     * CSV出力 mainアクション
     *
     * <AUTHOR> Kobayashi
     * @since      2021/10/xx
     */
    public function csvAction() {
        $db = Msi_Sys_DbManager::getMyDb();

        // 出力項目   DB 応答値は ASCII 大文字が小文字になるので注意
        $keyReturn = Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
EDI受注伝票番号
発注伝票番号
発注日時
売上伝票番号
受注伝票番号
データ区分
施行番号
依頼部門コード
依頼元部門
発注元部門コード
発注元部門
仕入先コード
仕入先名
EDIステータス
商品部門コード
商品部門名
商品コード
商品名
発注数量
上代単価
上代金額
発注単価
発注金額
在庫管理区分
EDI出荷
共通仕入先コード
仕入伝票番号
仲介仕入伝票番号
検品日
財務連動区分
共通仕入先コード
グループ会社区分
検品操作日時
検品担当者コード
検品担当者名
納品場所
納品場所住所1
納品場所住所2
発注担当者コード
発注担当者
自動検品フラグ_発注伝票商品
納品日
月次確定操作日時
自動検品対象操作日時
検品解除日時
検品解除担当者コード
検品解除担当者名
END_OF_TXT
        );

        try {
            $params = Msi_Sys_Utils::webInputs();
            $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

            // Msi_Sys_Utils::debug( 'dataAppAry==>' . Msi_Sys_Utils::dump($dataAppAry) );

            // 受託日付
            $st_ymd = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($dataAppAry['st_ymd']), 'DATE2');
            if (strlen($st_ymd) <= 0) {
                throw new Msi_Sys_Exception_InputException("発注日付(自)エラー");
            }
            $ed_ymd = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($dataAppAry['ed_ymd']), 'DATE2');
            if (strlen($ed_ymd) <= 0) {
                throw new Msi_Sys_Exception_InputException("発注日付(至)エラー");
            }

            // 条件
            $cond = array();
            $cond['__x2'] = array('x', "(発注日時::date BETWEEN :x2_1 AND :x2_2)",
                                  array( 'x2_1' => $st_ymd, 'x2_2' => $ed_ymd ) );
            
            // EDIステータス    
            if(isset($dataAppAry['edi_status'])){
                $edi_status = Msi_Sys_Utils::emptyToNull($dataAppAry['edi_status']);
                if ( strlen($edi_status) > 0 ) {
                    $cond['edi_status'] = $edi_status;
                }
            }

            // 出力用データの取得
            $select00 = DataMapper_Kanri_Edijuchuprn::find($db, $cond);

            if ( Msi_Sys_Utils::myCount($select00) <= 0 ) {
                $err = '該当するデータはありません';
                throw new Exception($err);
            }

            // 出力項目調整
            $select = array();
            foreach ( $select00 as $rec11 ) {
                $one = array();
                foreach ( $keyReturn as $key11 ) {
                    if ( Msi_Sys_Utils::beginsWith($key11, '#') ) { // skip
                        continue;
                    }
                    $key11 = strtolower($key11); // not mb_strtolower()
                    $one[$key11] = $rec11[$key11];
                }
                $select[] = $one;
            }

            $tempFile = Msi_Sys_Utils::tempnam();
            $csvObj = new Msi_Sys_CsvOut( $tempFile );

            $titleLine = array_keys($select[0]);
            $csvObj->output( $titleLine );

            foreach ( $select as $rec ) {
                $dataLine = array_values( $rec );
                $csvObj->output( $dataLine );
            }

            $csvObj = null;

            $buf = array( 'file' => $tempFile );

            $mytitle = sprintf("%s_%s_%s.csv", self::$title, Msi_Sys_Utils::normYYYYMMDD2($st_ymd), Msi_Sys_Utils::normYYYYMMDD2($ed_ymd));

            Msi_Sys_Utils::out2wayPush( $buf, $mytitle, 'text/csv' );

            Msi_Sys_Utils::out2wayFlush();
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            Msi_Sys_Utils::err( 'CSV出力エラー. ' . Msi_Sys_MsgIf::errMsgForLog($err, $e) );
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $outData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson( $outData );
            return;
        }
    }

}
