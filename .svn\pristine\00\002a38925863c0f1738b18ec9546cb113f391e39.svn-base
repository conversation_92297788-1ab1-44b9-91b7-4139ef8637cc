/* 
    消費税コード登録
    Created on : 2015/05/17
    Author     : MSI Kayo
*/
#main #t_head, 
#main #t_dtl
{
    table-layout: fixed;	/*テーブル列幅を固定レイアウトにする*/
}
#main #t_head 
{
    width:100%;
}
/*表全体*/
.items{
    margin-top: 20px;
    width:70%;
    height:80%;
}
/*明細部分*/
.list{
    overflow-y:auto;/*スクロールバー*/
    width:100%;
    height:90%;
    padding-bottom:1px;
}
/*テーブル列の幅調整*/
#main table .w2 {
    width: 2%!important;	
}
#main table .w3 {
    width: 3%!important;	
}
#main table .w4 {
    width: 4%!important;	
}
#main table .w5 {
    width: 5%!important;	
}
#main table .w6 {
    width: 6%!important;	
}
#main table .w6 {
    width: 6%!important;	
}
#main table .w8 {
    width: 8%!important;	
}
#main table .w10 {
    width: 10%!important;
}
#main table .w15 {
    width: 15%!important;
}
#main table .w20 {
    width: 20%!important;	
}
#main table .w100 {
    width: 20%!important;	
}
/*テーブル見出し部分*/
#main table .tbhd{
    color: #244b20!important;
    background-color: transparent!important;
    //border-right: 1px solid #88B1D1;
    //border-bottom: none;
    border:none!important;
}
/*テーブル見出し 右端部分*/
#main table .tbhdr{
    color: #244b20!important;
    background-color: transparent!important;
    border: none;
}
/*テーブル明細部分*/
#main table .tbdt {
    padding: 0 2px;
    border-bottom: 1px solid #88B1D1;
    border-right: 1px solid #88B1D1;
}
/*テーブル明細 右端部分*/
#main table .tbdtr {
    padding: 0 8px;
    border-bottom: 1px solid #88B1D1;
    border-right: none;
}
/*行追加、行削除アイコン*/
#main table .iconcolor {
text-decoration: none;
color: inherit;
}
#main table td .disp_no,
#main table td .kamoku_cd,
#main table td .kamoku_nm
{
    width: 100%;
	height: 28px;
	border: none;
}
#main table td .zei_cd,
#main table td .shoihi_zei_cd,
#main table td .default_kbn
{
    width: 100%;
    height: 90%;
    border: none;
}
#main .list .no-border-bottom {
  border-bottom: none !important;
}
#main .list .control {
  width: 3%;
  text-align: center;
}
#wideBasic table td {
	height: 30px;
	background-color: #fff;
	text-align: center;
	color: #244b20;
	font-weight: bold;
	border-right: 1px solid #88B1D1;
	overflow: hidden;
	text-overflow: ellipsis;
	border-bottom: 1px solid #88B1D1;
}

