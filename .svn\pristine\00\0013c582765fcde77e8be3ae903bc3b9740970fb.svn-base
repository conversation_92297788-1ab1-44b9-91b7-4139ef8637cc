(function ($) {
    "use strict";

    var utils = window.msiBbUtils;

    msiGlobalObj.msi_dialog2_cnt = 0; // do(Reg)Update が複数回呼ばれることのパッチ
    
    // var _nonyu_delivery_kbn_init = 9; // その他

    var _defTableForView = {
        // item            selector
        'sekyu_cd':       ['#sub_sekyu_cd'],
        'sekyu_nm':       ['#sub_sekyu_nm'],
        'sekyu_knm':      ['#sub_sekyu_knm'],
        'sekyu_yubin_no': ['#sub_sekyu_yubin_no'],
        'sekyu_addr1':    ['#sub_sekyu_addr1'],
        'sekyu_addr2':    ['#sub_sekyu_addr2'],
        'sekyu_tel':      ['#sub_sekyu_tel'],
        'sekyu_fax':      ['#sub_sekyu_fax'],
        'sekyu_biko1':    ['#sub_sekyu_biko1'],
        'sekyu_biko2':    ['#sub_sekyu_biko2'],
        'moshu_kankei':   ['#sub_moshu_kankei'],
        'seko_no':        ['#sub_seko_no'],
    };

    var _nm2sel     = msiLib2.hashPluck( _defTableForView, 0 ); // ex. { 'sekyu_cd': '#sub_sekyu_cd', ...
    var _bindings   = _.invert( _nm2sel );                      // ex. { '#sub_sekyu_cd': 'sekyu_cd', ...
    _bindings['#sub_sekyu_yubin_no'] = {
        observe: 'sekyu_yubin_no',
        getVal: utils.getValSel2,
        update: utils.updateSel2,
        onSet:  utils.onSetYubinNo,
    };
    // _bindings['#sub_sekyu_delivery_kbn'] = {
    //     observe: 'sekyu_delivery_kbn',
    //     getVal: utils.getValSel2,
    //     update: utils.updateSel2,
    // };

    // console.log( '==>' + JSON.stringify(_bindings) );

    var MyModel = msiGlobalObj.bbExports.SekoSekyuModel;

    var MyView = Backbone.View.extend({
        el: '#msi-dialog2', // '#msi-dialog2', // '#msi-dialog2-form-id',

        events: {
            "click #btn_set_sekyu":  "doUpdate",
            "click #btn_reg_sekyu":  "doRegUpdate",
            "click #btn_back_sekyu": "doBack", 
            "click #btn_clear_sekyu": "clearSekyu",
            "click #btn_copy_sekyu": "copySekyu",
            // "click .sub_sekyu_yubin_no-ref": "zipHelper",
        },

        initialize: function() {
            // console.log( '***** seko.sekyusaki.js init' + JSON.stringify(this.model.toJSON()) );

            Backbone.Validation.bind( this, Backbone.Validation.msi_v_iv_callback(_nm2sel) );
            
            this.listenTo(this.model, 'change', this.render);

            this.listenTo(Backbone, 'pick.seko.sekyusaki.select', this.updateSekyu);

            // this.listenTo(Backbone, 'pick.seko.sekyusaki.clear', this.clearSekyu); // 'pick.sekyu.clear'

            $('.msi-picker', this.$el).each( msiLib2.msiPickerBinder );

            this.stickit();

            this.render();
        },

        render: function() {
            var m = this.model;
            // var m = this.model,
            //     sekyu_delivery_kbn = m.get('sekyu_delivery_kbn');
            // $('#sub_sekyu_delivery_kbn').select2( 'val', sekyu_delivery_kbn );
            $('#sub_sekyu_yubin_no').select2( 'val', m.get('sekyu_yubin_no') );
            // if ( sekyu_delivery_kbn != _sekyu_delivery_kbn_init ) {
            //     $('#msi-dialog2-form-id').msiInputReadonly();
            // } else {
            //     $('#msi-dialog2-form-id').msiInputReadonlyUnset();
            //     $('#sub_sekyu_delivery_kbn').select2( 'readonly', true ); // 納品場所区分だけは変更不可
            // }
        },

        zipHelper: function() {
            Backbone.trigger( 'update.seko.sekyusaki', this.model.toJSON() );

            var $zip  = $('#sub_sekyu_yubin_no');
            var $addr = $('#sub_sekyu_addr1');

            $zip.msiPickHelper( {
                action: 'zipno',
                _isAutoClose: false,
                onSelect: function(data) {
                    Backbone.trigger( 'update.seko.sekyusaki.zip',
                                      { yubin_no: data.code, addr1: data.name } );
                },
                onClear: function() {
                    Backbone.trigger( 'update.seko.sekyusaki.zip', 
                                      { yubin_no: null, addr1: null } );
                },
                hookSetData: function() {
                    var zipno = $zip.val();
                    return {
                        s_zipno: zipno,
                        init_search: (!!zipno && zipno.length>2) ? 1 : 0,
                    };
                },
                onClose: function() {
                    Backbone.trigger( 'update.seko.sekyusaki.zip', null );
                }
            } );
        },

        updateSekyu: function(sekyuData) {
            this.errClear();

            // console.log( '* seko.sekyusaki.js * * * * * updateSekyu called' + JSON.stringify(sekyuData) );
            var m = this.model;
            _.map( 'sekyu_cd  sekyu_nm  sekyu_knm  seko_no  moshu_kankei'.split(/\s+/),
                   function(k) { m.set( k, sekyuData[ k ] ); } );
            _.map( 'yubin_no  addr1 addr2  tel  fax  biko1  biko2'.split(/\s+/),
                   function(k) { m.set( 'sekyu_' + k, sekyuData[ k ] ); } );
        },

        clearSekyu: function() {
            this.errClear();

            // console.log( 'updateSekyu called' + JSON.stringify(sekyuData) );
            var m = this.model;
            _.map( 'sekyu_cd  sekyu_nm  sekyu_knm  moshu_kankei'.split(/\s+/),
                   function(k) { m.set( k, null ); } );
            _.map( 'yubin_no  addr1 addr2  tel  fax  biko1  biko2'.split(/\s+/),
                   function(k) { m.set( 'sekyu_' + k, null ); } );
            // m.set( 'sekyu_delivery_kbn', _sekyu_delivery_kbn_init );
        },

        copySekyu: function() {
            this.errClear();

            var m = this.model;
            m.set( 'sekyu_cd', null );
            // m.set( 'sekyu_delivery_kbn', _sekyu_delivery_kbn_init );
        },

        errClear: function() {
            $( _(_bindings).keys() ).msiErrClear();
        },

        isInputOk: function() {
            var aMsg = [], line;
            var result = this.model.validate();
            var bbm = this.model;
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                    // console.log( '*** err ' + k + ': ' + v );
                    // console.log( '*** sekyu_yubin_no =>' + bbm.get('sekyu_yubin_no') );
                } );
            }

            // NG
            if ( aMsg.length > 0 ) {
                // msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            // msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },

        doBack: function() {
            Backbone.trigger( 'update.seko.sekyusaki', this.model.toJSON() );

            $.msiJqlib.msiPickupNewEndDialog();
        },

        doClear: function() {
            this.doClear();

            Backbone.trigger( 'update.seko.sekyusaki', this.model.toJSON() );

            $.msiJqlib.msiPickupNewEndDialog();
        },

        doUpdate: function() {
            // console.log( '%%%%%%%%%%%%%%% doUpdate  => ' + msiGlobalObj.msi_dialog2_cnt );
            if ( msiGlobalObj.msi_dialog2_cnt ) return;

            if ( ! this.isInputOk() ) {
                return;
            }

            msiGlobalObj.msi_dialog2_cnt++;

            Backbone.trigger( 'update.seko.sekyusaki', this.model.toJSON() );

            $.msiJqlib.msiPickupNewEndDialog();
        },

        doRegUpdate: function() {
            // console.log( '%%%%%%%%%%%%%%% doRegUpdate  => ' + msiGlobalObj.msi_dialog2_cnt );
            if ( msiGlobalObj.msi_dialog2_cnt ) return;

            if ( ! this.isInputOk() ) {
                return;
            }

            msiGlobalObj.msi_dialog2_cnt++;

            var m = this.model,
                data = this.model.toJSON(),
                formData = data;

            // console.log( '***** doRegUpdate==>' + JSON.stringify(data) );
            
            $.ajax( {
                url: $.msiJqlib.baseUrl() + '/juchu/etc/regsekyu',
                type: 'POST',
                data: { 'form_data': formData },
                dataType: 'json',
                success: function(data) {
                    console.log( '/juchu/etc/regsekyu rtn=> ' + JSON.stringify(data) );
                    if ( data.status == 'OK' ) {
                        m.set( 'sekyu_cd', data.data['sekyu_cd'] );
                        msiLib2.showInfo( data.msg );
                    } else {
                        msiLib2.showErr( data.msg );
                    }
                }
            } );

            Backbone.trigger( 'update.seko.sekyusaki', this.model.toJSON() );

            $.msiJqlib.msiPickupNewEndDialog();
        },

        bindings: _bindings,

    }); // MyView


    var app, orgData, _resetData, _setInitData;

    // 初期設定
    $( function() {

        app = new MyView( { model: new MyModel } );

        _resetData = function( myForm ) {
            app.model.set( myForm );
            orgData = app.model.toJSON();
        };

        _setInitData = function() {
            var mydata = msiLib2.getJsonFromHtml( $('#my-data-form-id') );
            // console.log( '* * * seko.sekyusaki.js myForm=>' + JSON.stringify(mydata.dataForm) );
            _resetData( mydata.dataForm );
        };

        _setInitData();

    });

})(jQuery);
