(function ($) {
    "use strict";

    var utils = window.msiBbUtils;

    var _defTableForView = {
        // item            selector
        'sekyu_cd': ['#sub_sekyu_cd'],
        'sekyu_last_nm': ['#sub_sekyu_last_nm'],
        'sekyu_first_nm': ['#sub_sekyu_first_nm'],
        'sekyu_last_knm': ['#sub_sekyu_last_knm'],
        'sekyu_first_knm': ['#sub_sekyu_first_knm'],
        'sekyu_yubin_no': ['#sub_sekyu_yubin_no'],
        'sekyu_addr1': ['#sub_sekyu_addr1'],
        'sekyu_addr2': ['#sub_sekyu_addr2'],
        'sekyu_tel': ['#sub_sekyu_tel'],
        'sekyu_fax': ['#sub_sekyu_fax'],
        'sekyu_biko1': ['#sub_sekyu_biko1'],
        'sekyu_biko2': ['#sub_sekyu_biko2'],
        'sekyu_soufu_nm': ['#sub_sekyu_soufu_nm'], 
        'sekyu_soufu_last_nm': ['#sub_sekyu_soufu_last_nm'], 
        'sekyu_soufu_first_nm': ['#sub_sekyu_soufu_first_nm'], 
    };

    var _nm2sel = msiLib2.hashPluck(_defTableForView, 0); // ex. { 'sekyu_cd': '#sub_sekyu_cd', ...
    var _bindings = _.invert(_nm2sel);                      // ex. { '#sub_sekyu_cd': 'sekyu_cd', ...
    _bindings['#sub_sekyu_yubin_no'] = {
        observe: 'sekyu_yubin_no',
        // getVal: utils.getValSel2,
        // update: utils.updateSel2,
        afterUpdate: utils.afterUpdateSel2,
        onSet: utils.onSetYubinNo,
    };

//console.log( 'nm2sel ==>' + JSON.stringify(_nm2sel) );
//console.log( 'bindings==>' + JSON.stringify(_bindings) );

    var MyModel = msiGlobalObj.bbExports.SekyuModel;

    var MyView = Backbone.View.extend({
        el: '#msi-dialog2', // '#msi-dialog2', // '#msi-dialog2-form-id',

        events: {
            "click #btn_set_sekyu": "doUpdate",
            "click #btn_back_sekyu": "doBack",
            "click #btn_clear_sekyu": "clearSekyu",
            "click #btn_copy_sekyu": "copySekyu",
            // "click .sub_sekyu_yubin_no-ref": "zipHelper",
        },

        initialize: function () {

            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(_nm2sel));

            this.listenTo(Backbone, 'pick.sekyu.select', this.updateSekyu);

            // this.listenTo(Backbone, 'pick.sekyu.clear', this.clearSekyu);

            $('.msi-picker', this.$el).each(msiLib2.msiPickerBinder);

            $('#sub_sekyu_yubin_no').msiPicker2Options({
                minimumInputLength: 2,
                formatInputTooShort: function (input, min) {
                    var n = min - input.length;
                    return "郵便番号ヘルプ"; // 郵便番号を" + min + "桁以上(ハイフン不要)入れてください";
                },
            });

            this.stickit();
        },

        zipHelper: function () {
            Backbone.trigger('update.sekyu', this.model.toJSON());

            var $zip = $('#sub_sekyu_yubin_no');
            var $addr = $('#sub_sekyu_addr1');

            $zip.msiPickHelper({
                action: 'zipno',
                _isAutoClose: false,
                onSelect: function (data) {
                    Backbone.trigger('update.sekyu.zip',
                            {yubin_no: data.code, addr1: data.name});
                },
                onClear: function () {
                    Backbone.trigger('update.sekyu.zip',
                            {yubin_no: null, addr1: null});
                },
                hookSetData: function () {
                    var zipno = $zip.val();
                    return {
                        s_zipno: zipno,
                        init_search: (!!zipno && zipno.length > 2) ? 1 : 0,
                    };
                },
                onClose: function () {
                    Backbone.trigger('update.sekyu.zip', null);
                }
            });
        },

        updateSekyu: function (sekyuData) {
            this.errClear();

console.log( 'updateSekyu called' + JSON.stringify(sekyuData) );
            var m = this.model;
            _.map('sekyu_cd sekyu_last_nm sekyu_first_nm sekyu_last_knm sekyu_first_knm v_free4 '.split(/\s+/),
                    function (k) {
                        m.set(k, sekyuData[ k ]);
                    });
            _.map('yubin_no addr1 addr2 tel fax biko1 biko2 soufu_nm soufu_last_nm soufu_first_nm '.split(/\s+/),
                    function (k) {
                        m.set('sekyu_' + k, sekyuData[ k ]);
                    });
            var sekyu_nm = null;
            var sekyu_knm = null;
            if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_last_nm']) && !$.msiJqlib.isNullEx2(sekyuData['sekyu_first_nm'])) {
                sekyu_nm = sekyuData['sekyu_last_nm'] + '　' + sekyuData['sekyu_first_nm'];
            } else if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_last_nm'])) {
                sekyu_nm = sekyuData['sekyu_last_nm'];
            } else if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_first_nm'])) {
                sekyu_nm = sekyuData['sekyu_first_nm'];
            }
            if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_last_knm']) && !$.msiJqlib.isNullEx2(sekyuData['sekyu_first_knm'])) {
                sekyu_knm = sekyuData['sekyu_last_knm'] + '　' + sekyuData['sekyu_first_knm'];
            } else if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_last_knm'])) {
                sekyu_knm = sekyuData['sekyu_last_knm'];
            } else if (!$.msiJqlib.isNullEx2(sekyuData['sekyu_first_knm'])) {
                sekyu_knm = sekyuData['sekyu_first_knm'];
            }
            m.set('sekyu_nm', sekyu_nm);
            m.set('sekyu_knm', sekyu_knm);

            var sub_sekyu_cd = $('#sub_sekyu_cd');
            sub_sekyu_cd = sub_sekyu_cd[0].value;
//console.log( 'sub_sekyu_cd=>' + sub_sekyu_cd );

            if ($.msiJqlib.isNullEx2(sub_sekyu_cd)) {
                this.$('#sub_sekyu_last_nm').removeAttr('readonly');
                this.$('#sub_sekyu_first_nm').removeAttr('readonly');
                this.$('#sub_sekyu_last_knm').removeAttr('readonly');
                this.$('#sub_sekyu_first_knm').removeAttr('readonly');
                this.$('#sub_sekyu_tel').removeAttr('readonly');
                this.$('#sub_sekyu_fax').removeAttr('readonly');
                this.$('#sub_sekyu_soufu_nm').removeAttr('readonly');
                this.$('#sub_sekyu_soufu_last_nm').removeAttr('readonly');
                this.$('#sub_sekyu_soufu_first_nm').removeAttr('readonly');
                this.$('#sub_sekyu_yubin_no').removeAttr('readonly');
                this.$('#sub_sekyu_addr1').removeAttr('readonly');
                this.$('#sub_sekyu_addr2').removeAttr('readonly');
                this.$('#sub_sekyu_biko1').removeAttr('readonly');
                this.$('#sub_sekyu_biko2').removeAttr('readonly');
            } else {
                this.$('#sub_sekyu_last_nm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_first_nm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_last_knm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_first_knm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_tel').attr('readonly', 'readonly');
                this.$('#sub_sekyu_fax').attr('readonly', 'readonly');
                this.$('#sub_sekyu_soufu_nm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_soufu_last_nm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_soufu_first_nm').attr('readonly', 'readonly');
                this.$('#sub_sekyu_yubin_no').attr('readonly', 'readonly');
                this.$('#sub_sekyu_addr1').attr('readonly', 'readonly');
                this.$('#sub_sekyu_addr2').attr('readonly', 'readonly');
                this.$('#sub_sekyu_biko1').attr('readonly', 'readonly');
                this.$('#sub_sekyu_biko2').attr('readonly', 'readonly');
            }
        },

        clearSekyu: function () {
            this.errClear();

            // console.log( 'updateSekyu called' + JSON.stringify(sekyuData) );
            var m = this.model;
            _.map('sekyu_cd sekyu_nm sekyu_last_nm sekyu_first_nm sekyu_knm sekyu_last_knm sekyu_first_knm v_free4 '.split(/\s+/),
                    function (k) {
                        m.set(k, null);
                    });
            _.map('yubin_no addr1 addr2 tel fax biko1 biko2 soufu_nm soufu_last_nm soufu_first_nm '.split(/\s+/),
                    function (k) {
                        m.set('sekyu_' + k, null);
                    });
            this.$('#sub_sekyu_last_nm').removeAttr('readonly');
            this.$('#sub_sekyu_first_nm').removeAttr('readonly');
            this.$('#sub_sekyu_last_knm').removeAttr('readonly');
            this.$('#sub_sekyu_first_knm').removeAttr('readonly');
            this.$('#sub_sekyu_tel').removeAttr('readonly');
            this.$('#sub_sekyu_fax').removeAttr('readonly');
            this.$('#sub_sekyu_soufu_nm').removeAttr('readonly');
            this.$('#sub_sekyu_soufu_last_nm').removeAttr('readonly');
            this.$('#sub_sekyu_soufu_first_nm').removeAttr('readonly');
            this.$('#sub_sekyu_yubin_no').removeAttr('readonly');
            this.$('#sub_sekyu_addr1').removeAttr('readonly');
            this.$('#sub_sekyu_addr2').removeAttr('readonly');
            this.$('#sub_sekyu_biko1').removeAttr('readonly');
            this.$('#sub_sekyu_biko2').removeAttr('readonly');
        },

        copySekyu: function () {
            this.errClear();

            var m = this.model;
            m.set('sekyu_cd', null);
        },

        errClear: function () {
            $(_(_bindings).keys()).msiErrClear();
        },

        isInputOk: function () {
            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                    // console.log( '*** err ' + k + ': ' + v );
                });
            }

            // NG
            if (aMsg.length > 0) {
                // msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            // msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },

        doBack: function () {
//            Backbone.trigger('update.sekyu', this.model.toJSON());

            $.msiJqlib.msiPickupNewEndDialog();
        },

        doClear: function () {
            this.doClear();

            Backbone.trigger('update.sekyu', this.model.toJSON());

            $.msiJqlib.msiPickupNewEndDialog();
        },

        doUpdate: function () {
//            if (!this.isInputOk()) {
//                return;
//            }

            Backbone.trigger('update.sekyu', this.model.toJSON());

            $.msiJqlib.msiPickupNewEndDialog();
        },

        bindings: _bindings,

    }); // MyView


    var app, orgData, _resetData, _setInitData;

    // 初期設定
    $(function () {

        app = new MyView({model: new MyModel});

        _resetData = function (myForm) {
            app.model.set(myForm);
            orgData = app.model.toJSON();
//console.log( 'myForm=>' + JSON.stringify(myForm) );
//console.log( 'orgData=>' + JSON.stringify(orgData) );
        };

        _setInitData = function () {
            var mydata = msiLib2.getJsonFromHtml($('#my-data-form-id'));
//console.log( 'mydata=>' + JSON.stringify(mydata) );
            // console.log( '* * * juchu.denpyo.order.sekyu.js myForm=>' + JSON.stringify(mydata.dataForm) );
            _resetData(mydata.dataForm);
//console.log( 'sub_kaiin_kbn_nm=>' + $('#sub_kaiin_kbn_nm').val()  );
//console.log( 'sub_kaiin_biko1=>' + $('#sub_kaiin_biko1').val()  );

            var sub_sekyu_cd = $('#sub_sekyu_cd');
            sub_sekyu_cd = sub_sekyu_cd[0].value;

//console.log( 'sub_sekyu_cd=>' + sub_sekyu_cd );

//2018/04/21	ごんきや	全面的に上書き不可にする
            if ($.msiJqlib.isNullEx2(sub_sekyu_cd)) {
                $('#sub_sekyu_last_nm').removeAttr('readonly');
                $('#sub_sekyu_first_nm').removeAttr('readonly');
                $('#sub_sekyu_last_knm').removeAttr('readonly');
                $('#sub_sekyu_first_knm').removeAttr('readonly');
                $('#sub_sekyu_tel').removeAttr('readonly');
                $('#sub_sekyu_fax').removeAttr('readonly');
                $('#sub_sekyu_soufu_nm').removeAttr('readonly');
                $('#sub_sekyu_soufu_last_nm').removeAttr('readonly');
                $('#sub_sekyu_soufu_first_nm').removeAttr('readonly');
                $('#sub_sekyu_yubin_no').removeAttr('readonly');
                $('#sub_sekyu_addr1').removeAttr('readonly');
                $('#sub_sekyu_addr2').removeAttr('readonly');
                $('#sub_sekyu_biko1').removeAttr('readonly');
                $('#sub_sekyu_biko2').removeAttr('readonly');
            } else {
                $('#sub_sekyu_last_nm').attr('readonly', 'readonly');
                $('#sub_sekyu_first_nm').attr('readonly', 'readonly');
                $('#sub_sekyu_last_knm').attr('readonly', 'readonly');
                $('#sub_sekyu_first_knm').attr('readonly', 'readonly');
                $('#sub_sekyu_tel').attr('readonly', 'readonly');
                $('#sub_sekyu_fax').attr('readonly', 'readonly');
                $('#sub_sekyu_soufu_nm').attr('readonly', 'readonly');
                $('#sub_sekyu_soufu_last_nm').attr('readonly', 'readonly');
                $('#sub_sekyu_soufu_first_nm').attr('readonly', 'readonly');
                $('#sub_sekyu_yubin_no').attr('readonly', 'readonly');
                $('#sub_sekyu_addr1').attr('readonly', 'readonly');
                $('#sub_sekyu_addr2').attr('readonly', 'readonly');
                $('#sub_sekyu_biko1').attr('readonly', 'readonly');
                $('#sub_sekyu_biko2').attr('readonly', 'readonly');
            }
        };

        // カナ自動入力設定
        $.msiJqlib.setAutoKanaModel({
            '#sub_sekyu_last_nm': ['#sub_sekyu_last_knm', 'sekyu_last_knm', app.model],
            '#sub_sekyu_first_nm': ['#sub_sekyu_first_knm', 'sekyu_first_knm', app.model],
        });

        _setInitData();

    });

})(jQuery);
