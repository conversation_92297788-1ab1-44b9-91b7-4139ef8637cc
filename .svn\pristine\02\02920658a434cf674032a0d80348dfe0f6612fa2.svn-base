<?php
  /**
   * DataMapper_UriageFromJuchu
   *
   * 受注伝票由来 売上伝票登録
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   * @version    2019/06/24 tosaka 軽減税率対応 keigen    _filterJuchuMsi() に reduced_tax_rate 追加
   * @filesource 
   */

  /**
   * 受注伝票由来 売上伝票登録
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   */
class DataMapper_UriageFromJuchu extends DataMapper_Abstract
{
    /**
     * データ 登録・更新
     * 単品、別注品について、受注伝票から売上伝票を登録する。
     * 単品、別注品については、受注伝票（受注伝票明細含む）は売上伝票（売上伝票明細）と１対１対応している。
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $denpyo_no  受注番号
     * @param      array      $opt_data   付加的情報    売上計上日
     * @return     string     $uri_den_no 売上伝票番号
     */
    public static function upsert($db, $denpyo_no, $opt_data=null)
    {
        // $data, $kijyunYmd=null)

        $juchuDenpyo = $db->easySelOne( <<< END_OF_SQL
SELECT *
  FROM juchu_denpyo den
 WHERE den.denpyo_no = :denpyo_no
   AND den.delete_flg=0
END_OF_SQL
                                        , array( 'denpyo_no' => $denpyo_no ) );
        if ( $juchuDenpyo === null ) {
            throw new Msi_Sys_Exception_InputException( "受注伝票($denpyo_no)のデータが存在しません" );
        }

        $uriageDenpyo = $db->easySelOne( <<< END_OF_SQL
SELECT *
  FROM uriage_denpyo den
 WHERE den.denpyo_no = :denpyo_no
   AND den.delete_flg=0
END_OF_SQL
                                        , array( 'denpyo_no' => $denpyo_no ) );

        if ( $uriageDenpyo === null ) { // 新規登録
            $rtn = static::_insert($db, $juchuDenpyo);
        } else { // 更新
            $rtn = static::_update($db, $juchuDenpyo, $uriageDenpyo);
        }

        // 売上計上日の設定
        if ( $opt_data && isset($opt_data['keijo_ymd']) ) {
            $uri_den_no = $rtn;
            $db->easyExecute( 'UPDATE uriage_denpyo u SET keijo_ymd=:keijo_ymd WHERE u.uri_den_no=:uri_den_no AND u.delete_flg=0',
                              array( 'keijo_ymd'=>$opt_data['keijo_ymd'], 'uri_den_no'=>$uri_den_no) );
        }

        // 集計処理
        if ( Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyo_no, $juchuDenpyo['data_kbn']) !== true ) {	// 2014/07/06 UPD Kayo
            throw new Msi_Sys_Exception_LogicException( "集計処理でエラーが発生しました" );
        }
        // 受注伝票（単品）入金伝票
        if ( Logic_TanpinNyuknMake::Main($db, $uri_den_no) !== 0 ) {	// 2017/05/31 UPD Kayo
            throw new Msi_Sys_Exception_LogicException( "受注伝票（単品）入金伝票処理でエラーが発生しました" );
        }

        return $rtn;
    }

    /**
     * データ 登録
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $juchuDenpyo
     * @return     string     $uri_den_no 売上伝票番号
     */
    protected static function _insert($db, $juchuDenpyo)
    {
        // 売上伝票番号 取得
        $denpyo_no = $juchuDenpyo['denpyo_no'];
        $kijyunYmd = Msi_Sys_Utils::normYYYYMMDD($juchuDenpyo['juchu_ymd']);
        $uri_den_no = App_ClsGetCodeNo::GetCodeNo($db, 'uriage_denpyo', 'uri_den_no', $kijyunYmd);

        $uriageDenpyo = static::_filterJuchuDenpyo( $juchuDenpyo );

        $uriageDenpyo['uri_den_no'] = $uri_den_no;
        $uriageDenpyo['status_kbn'] = 2; // 2：施工中 3：請求済み（完了） 4：入金済み
        @ $uriageDenpyo['seikyu_zan'] = $uriageDenpyo['uri_prc_sum'] + $uriageDenpyo['uri_hepn_sum'] +
                                        $uriageDenpyo['uri_nebk_sum'] + $uriageDenpyo['hoshi_prc_sum'] +
                                        $uriageDenpyo['out_zei_prc']
                                        - $uriageDenpyo['nyukin_prc'];
        $uriageDenpyo['sekkyu_kaisu'] = 0; // 請求回数
        //追加　by MSI Lakshita on 2019/04/11
        $uriageDenpyo['ts_free2'] = $juchuDenpyo['ts_free2']; // 保証書発行日

        list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'uriage_denpyo', $uriageDenpyo );

        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        $cnt = $db->easyExecute($sql, $param);

        // 受注明細データ取得
        $juchuMsi = static::_findMsi($db, $denpyo_no);

        foreach ( $juchuMsi as $dtl ) {
            $uriageMsi = static::_filterJuchuMsi($dtl);

            $uriageMsi['uri_den_no'] = $uri_den_no;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'uriage_denpyo_msi', $uriageMsi );

            // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
            $cnt = $db->easyExecute($sql, $param);
        }

        return $uri_den_no;
    }

    /**
     * データ 更新
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $juchuDenpyo
     * @param      array      $uriageDenpyo
     * @return     string     $uri_den_no 売上伝票番号
     */
    protected static function _update($db, $juchuDenpyo, $p_uriageDenpyo)
    {
        $denpyo_no = $juchuDenpyo['denpyo_no'];
        $uri_den_no = $p_uriageDenpyo['uri_den_no'];

        $uriageDenpyo = static::_filterJuchuDenpyo( $juchuDenpyo );

        if ( true   || $p_uriageDenpyo['sekkyu_kaisu'] <= 0 ) {
            @ $uriageDenpyo['seikyu_zan'] = $uriageDenpyo['uri_prc_sum'] + $uriageDenpyo['uri_hepn_sum'] +
                                            $uriageDenpyo['uri_nebk_sum'] + $uriageDenpyo['hoshi_prc_sum'] +
                                            $uriageDenpyo['out_zei_prc']
                                            - $uriageDenpyo['nyukin_prc'];
        }

        $uriageDenpyo['uri_den_no'] = $uri_den_no;
        //追加　by MSI Lakshita on 2019/04/11
        $uriageDenpyo['ts_free2'] = $juchuDenpyo['ts_free2']; // 保証書発行日
        // $uriageDenpyo['status_kbn'] = 2; // 2：施工中 3：請求済み（完了） 4：入金済み

        list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'uriage_denpyo', $uriageDenpyo,
                                                               array('uri_den_no') );

        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        $cnt = $db->easyExecute($sql, $param);

        // 受注明細データ取得
        $juchuMsi = static::_findMsi($db, $denpyo_no);

        foreach ( $juchuMsi as $dtl ) {
            $uriageMsi = static::_filterJuchuMsi($dtl);

            $uriageMsi['uri_den_no'] = $uri_den_no;
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'uriage_denpyo_msi', $uriageMsi,
                                                                   array('uri_den_no', 'msi_no') );
            // 削除も update で済む(delete_flg=1)

            // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
            $cnt = $db->easyExecute($sql, $param);

            if ( $cnt === 0 ) { // データが存在しないので登録
                list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'uriage_denpyo_msi', $uriageMsi );

                // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
                $cnt = $db->easyExecute($sql, $param);
            }
        }

        return $uri_den_no;
    }

    /**
     * 受注明細データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      string   $denpyo_no  受注番号
     * @return     array
     */
    protected static function _findMsi($db, $denpyo_no)
    {
        $juchuMsi = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM juchu_denpyo_msi dtl
 WHERE dtl.denpyo_no = :denpyo_no
--    AND dtl.delete_flg=0    delete_flg=1 のデータも取得する
 ORDER BY dtl.disp_no, dtl.msi_no
END_OF_SQL
                                     , array( 'denpyo_no' => $denpyo_no ) );
        return $juchuMsi;
    }

    /**
     * 売上伝票 項目フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      array      $juchuDenpyo
     * @return     array
     */
    protected static function _filterJuchuDenpyo($juchuDenpyo)
    {
        /* 売上未設定: uri_den_no seikyu_ymd zen_seikyu_ymd sekkyu_kaisu nyukin_prc seikyu_zan status_kbn
           受注未設定: delivery_kbn
        */

        $uriageDenpyo = Msi_Sys_Utils::remapArrayFlat( $juchuDenpyo, <<< END_OF_TXT
denpyo_no  juchu_ymd  data_kbn  seko_no  seko_no_sub kaisya_cd
bumon_cd   tanto_cd input_tanto_cd gojokai_cose_cd  seko_plan_cd  
kaishu_kbn kaishu_ymd
pay_method_cd  juchusaki_kbn
sekyu_cd   sekyu_nm   sekyu_knm   sekyu_soufu_nm sekyu_soufu_knm   sekyu_yubin_no
sekyu_addr1  sekyu_addr2  sekyu_tel  sekyu_fax
nonyu_cd  nonyu_nm   nonyu_knm  nonyu_yubin_no  nonyu_addr1  nonyu_addr2
nonyu_tel  nonyu_fax  nonyu_dt
juchu_prc_sum   genka_prc_sum   juchu_hepn_sum   juchu_nebk_sum  hoshi_prc_sum
szei_katax_taisho_prc   uzei_katax_taisho_prc  hitax_katax_taisho_prc
tax_code_kbn  tax_cd   tax_kbn  zei_cd  out_zei_prc   in_zei_prc
arari_prc  denpyo_biko1  denpyo_biko2 denpyo_biko3 
k_free1 k_free2 k_free3 k_free4 k_free5
v_free1 v_free2 v_free3 v_free4 v_free5 ts_free1
n_free1 n_free2 n_free3 n_free4 n_free5 n_free6 n_free7 n_free9  
text_free1 text_free2 text_free3 text_free4 text_free5 
ts_free3    
ryoshusho_atena ryoshusho_atena2 ryoshusho_atena3
d_free3 ryoshusho_genkin ryoshu_sho_kogite ryoshusho_furikomi ryoshusho_tadashikaki_cd
ryoshusho_tadashikaki ryoshusho_biko ryoshusho_syukin_tanto_cd ryoshusho_syukin_tanto_nm
h_nafuda_nm h_nafuda_nm2 h_nafuda_nm3 h_nafuda_nm4 h_nafuda_nm5 nohinsho_pirint_kbn
delete_flg
END_OF_TXT
                                                       , array(
                                                               'juchu_prc_sum' => 'uri_prc_sum',
                                                               'juchu_hepn_sum' => 'uri_hepn_sum',
                                                               'juchu_nebk_sum' => 'uri_nebk_sum'
                                                               ) );
        return $uriageDenpyo;
    }

    /**
     * 売上伝票明細 項目フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      array      $juchuDenpyo
     * @return     array
     */
    protected static function _filterJuchuMsi($dtl)
    {
        $uriageMsi = Msi_Sys_Utils::remapArrayFlat( $dtl, <<< END_OF_TXT
msi_no   disp_no   add_kbn   data_sbt  bumon_cd
seko_no  seko_no_sub   juchu_ymd  denpyo_kbn   mokuteki_kbn
dai_bunrui_cd  chu_bunrui_cd  shohin_kbn kaisya_cd  shohin_bumon_cd
shohin_cd  shohin_nm  shohin_tkiyo_nm
juchu_suryo  tani_cd  juchu_tnk  juchu_prc  nebiki_prc
gen_tnk  gen_gaku  arari_gaku  zei_kbn  zei_cd     reduced_tax_rate
out_zei_prc  in_zei_prc
nafuda_nm nafuda_nm2 nafuda_nm3 nafuda_nm4 nafuda_nm5
hoshi_umu_kbn  hoshi_ritu_cd  hoshi_prc
nonyu_cd  nonyu_nm  nonyu_knm  nonyu_yubin_no  nonyu_addr1  nonyu_addr2
nonyu_tel  nonyu_fax  nonyu_dt
msi_biko1  msi_biko2
siire_cd  siire_lnm  uri_kamoku_cd
refer_uchiwk_no  select_shohin_cd
delete_flg soko_cd  nonyu_time  nonyu_status_kbn  gs_nouki_dt  gs_nouki_time  
gs_nohinsaki_kbn  gs_nohinsaki_nm  noshi_kbn 
n_free1 n_free2 n_free3 n_free5
k_free1 k_free3 k_free4 
v_free2
END_OF_TXT
                                                    , array( 'juchu_tnk' => 'uri_tnk',
                                                             'juchu_prc' => 'uri_prc',
                                                             ) );
        return $uriageMsi;
    }
    /**
     * データ 登録・更新(セレモ専用)
     * 単品、別注品について、受注伝票から売上伝票を登録する。
     * 単品、別注品については、受注伝票（受注伝票明細含む）は売上伝票（売上伝票明細）と１対１対応している。
     * 別注品受注画面で新規保存時は入金伝票を作成しない為、こちらを使用する。
     * 
     * <AUTHOR> Oka
     * @since      2016/06/22
     * @param      Msi_Sys_Db $db
     * @param      string     $denpyo_no  受注番号
     * @param      array      $opt_data   付加的情報    売上計上日
     * @return     string     $uri_den_no 売上伝票番号
     */
    public static function upsert_ceremo($db, $denpyo_no, $opt_data=null)
    {
        // $data, $kijyunYmd=null)

        $juchuDenpyo = $db->easySelOne( <<< END_OF_SQL
SELECT *
  FROM juchu_denpyo den
 WHERE den.denpyo_no = :denpyo_no
   AND den.delete_flg=0
END_OF_SQL
                                        , array( 'denpyo_no' => $denpyo_no ) );
        if ( $juchuDenpyo === null ) {
            throw new Msi_Sys_Exception_InputException( "受注伝票($denpyo_no)のデータが存在しません" );
        }

        $uriageDenpyo = $db->easySelOne( <<< END_OF_SQL
SELECT *
  FROM uriage_denpyo den
 WHERE den.denpyo_no = :denpyo_no
   AND den.delete_flg=0
END_OF_SQL
                                        , array( 'denpyo_no' => $denpyo_no ) );

        if ( $uriageDenpyo === null ) { // 新規登録
            $rtn = static::_insert($db, $juchuDenpyo);
        } else { // 更新
            $rtn = static::_update($db, $juchuDenpyo, $uriageDenpyo);
        }

        // 売上計上日の設定
        if ( $opt_data && isset($opt_data['keijo_ymd']) ) {
            $uri_den_no = $rtn;
            $db->easyExecute( 'UPDATE uriage_denpyo u SET keijo_ymd=:keijo_ymd WHERE u.uri_den_no=:uri_den_no AND u.delete_flg=0',
                              array( 'keijo_ymd'=>$opt_data['keijo_ymd'], 'uri_den_no'=>$uri_den_no) );
        }

        // 集計処理
        if ( Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyo_no, $juchuDenpyo['data_kbn']) !== true ) {	// 2014/07/06 UPD Kayo
            throw new Msi_Sys_Exception_LogicException( "集計処理でエラーが発生しました" );
        }


        return $rtn;
    }
}
