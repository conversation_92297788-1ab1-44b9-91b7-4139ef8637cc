@charset "UTF-8";
.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-justify {
    text-align: justify;
}

.small {
    font-size: 85%;
}

.small2 {
    font-size: 80%;
}

.small3 {
    font-size: 75%;
}

.smallest {
    font-size: 70%;
}

.my-error {
    border: 1px solid red !important;
}

#order {
    position: fixed;
    width: 100%;
    height: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 25px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    top: 40px;
    left: 0;
    background-color: #D4E5F4;
    box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
    line-height: 1;
    z-index: 2;
    display: none;
    padding-bottom: 80px;
}
#order a {
    text-decoration: none;
    color: inherit;
}
#order .page-title {
    position: static;
    margin-top: 10px;
    margin-bottom: 15px;
    float: left;
}
#order .page-title span {
    display: inline;
    padding: .3em .6em .3em;
    font-weight: bold;
    line-height: 1;
    color: white;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    background-color: #ff9900;
    font-size: 1.2em;
}
#order .dlg_date,
#order .dlg_denpyo_no,
#order .dlg_staff,
#order .dlg_nyuko_soko,
#order .dlg_tanto,
#order .dlg_seko_no,
#order .dlg_bumon {
    width: 3%;
    background-image: url(../../../img/ico_dialog_2.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 12px 10px;
    border-top: none;
    border-right: 1px solid #CCC;
    border-left: none;
}
#order .lbl_denpyo_no 
,#order .lbl_data_sbt 
,#order .lbl_soko
,#order .lbl_bumon 
,#order .lbl_shuko_bumon 
,#order .lbl_nyuko_dt 
,#order .lbl_nyuko_soko 
{
    width: 7%;
}
#order .lbl_denpyo_no 
,#order .lbl_shuko_denpyo_no 
,#order .lbl_nyuko_tanto
,#order .lbl_tanto
,#order .lbl_seko_no 
{
    width: 8%;
}
#order #denpyo_no 
,#order #shuko_denpyo_no 
,#order #seko_no 
{
    width: 9%;
}
#order #nyuko_dt 
{
    width: 10%;
}
#order .cls_data_sbt {
    float: left;
    width: 10%;
}
#order #bumon_nm
,#order #nyuko_soko_nm 
{
    width: 12%;
}
#order #tanto_nm {
    width: 11%;
}
#order #soko_nm
,#order #shuko_bumon_nm {
    width: 15%;
}
#order #nyuko_tanto_nm {
    width: 8%;
}
#order fieldset.note_1 {
    margin-top: 10px;
}
#order .lbl_note{
    width: 8%;
    height: 64px;
    border-top: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;
    border-left: 1px solid #88B1D1;
}
#order #denpyo_biko1{
    height: 64px;
    width: 40%;
    padding: 6px;
    border-top: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;
    border-right: 1px solid #88B1D1;
}
#order .items {
    height: 60%;
    overflow: auto;
    border: 1px solid #88B1D1;
    background-color: #EBF3FA;
    overflow:hidden;
    -webkit-overflow-scrolling: touch;
}
#order .header {
    overflow-y:scroll;    
    overflow-x: hidden;
}
#order .header table {
    width: 100%;
    border-bottom: 1px solid #88B1D1;
    border-collapse: collapse;
    background: none;
    background-color: #E8F3FB;
    font-size: 13px;
    table-layout: fixed;
}
#order .header table td {
    width: 5%;
    height: 30px;
    background-color: transparent;
    text-align: center;
    color: #286EA6;
    border-right: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#order .list {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 80%;
    padding-bottom: 1px;
}
#order .list table {
    width: 100%;
    border-bottom: 1px solid #88B1D1;
    border-collapse: collapse;
    background-color: #FFF;
    table-layout: fixed;
}
#order .list table tbody:nth-child(even) {
    background-color: #F5F9FD;
}
#order .list table td {
    width: 5%;
    height: 30px;
    background-color: transparent;
    text-align: center;
    color: #286EA6;
    border-right: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#order .header .row {
    width: 4%;
}
#order .header .control {
    width: 4%;
}
#order .header .bumon {
    width: 10%;
}
#order .header .item_id {
    width: 9%;
    border-right: 1px solid #88B1D1;
}
#order .header .item {
    width: 22%;
    border-bottom: 1px dotted #88B1D1;
}
#order .header .quantity {
    width: 7%;
    /*border-left: 1px solid #88B1D1;*/
}
#order .header .unit {
    width: 5%;
}
#order .header .memo1 {
    width: 22%;
    /*border-bottom: 1px dotted #88B1D1;*/
    border-right: none;
}
/*#order .header .memo2 {
    width: 20%;
    border-right: none;
}*/
#order .list .row {
    width: 4%;
    text-align: center;
}
#order .list .control {
    width: 4%;
    text-align: center;
}
#order .list .control i {
    cursor: pointer;
}
#order .list .bumon {
    width: 10%;
    text-align: left;
    padding: 8px;
}
#order .list .bumon_nm {
    color: black;
}
#order .list .item_id {
    width: 9%;
    border-right: 1px solid #88B1D1;
}
#order .list .dlg_item {
    width: 3%;
    left: 22%;
}
#order .list .item {
    width: 22%;
    border-bottom: 1px solid #88B1D1;
}
#order .list .item input,
#order .list .item div {
    position: absolute;
    top: 0;
    left: 0;
    height: 30px;
    border-right: none;
}
#order .list .quantity {
    width: 7%;
    border-left: 1px solid #88B1D1;
}
#order .list .unit {
    width: 5%;
    color: #000;
}
#order .list .memo1 {
    width: 22%;
    border-right: none;
}
/*#order .list .memo2 {
    width: 20%;
    border-right: none;
}*/
#order .list .juchu_gaku, #order .list .genka_gaku {
    cursor: auto !important;
}
#order .list .dlg_shohin {
    float: right;
    width: 25%;
    border: none;
    background-image: url(../../../img/ico_dialog_2.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 12px 10px;
    background-color: transparent;
}
#order .list .item_nm {
    width: 22%;
    border-bottom: 1px dotted #88B1D1;
}
#order .list .no-border-bottom {
    border-bottom: none !important;
}
#order .list .my-txt50
, #order .list .my-txt60
, #order .list .my-txt75
, #order .list .my-txt80
, #order .list .my-txt85
, #order .list .my-txt90
, #order .list .my-txt95 {
    border: none;
    vertical-align: middle;
    border-radius: 4px;
    padding: 0.5px 4px;
    background-color: inherit;
    height: 28px;
}
#order .list .my-txt50 {
    width: 50% !important;
}
#order .list .my-txt60 {
    width: 60% !important;
}
#order .list .my-txt75 {
    width: 75% !important;
}
#order .list .my-txt80 {
    width: 80% !important;
}
#order .list .my-txt85 {
    width: 85% !important;
}
#order .list .my-txt90 {
    width: 90% !important;
}
#order .list .my-txt95 {
    width: 95% !important;
}
#order .select2-choice, #order .select-container {
    height: 32px !important;
    border-radius: 0px !important;
    padding-top: 2px !important;
    border-left: none;
    border-top: none;
    border-bottom: 1px solid #ccc;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#order .select2-arrow {
    border-radius: 0px !important;
}
#order .list .select2-choice, #order .list .select-container {
    margin: 0;
    padding: 0;
    border: 1px solid #DDD;
    background-color: transparent;
    background-image: none;
}
#order fieldset.base_1 {
    margin-bottom: 10px;
}
#order .my-akaji {
    color: red !important;
}
#order .my-bold {
    font-weight: bold;
}
#order .my-error {
    border: 1px solid red !important;
}
#order .my-disabled {
    background-color: #f4f4f4 !important;
    cursor: auto;
}
#order input[readonly], #order input:disabled {
    background-color: #f4f4f4 !important;
    cursor: auto;
}
#order input[type="button"]:disabled {
    opacity: .5;
}
#order input[type="button"]:disabled:active {
    background: none;
    border: 1px solid #94B9D6;
    box-shadow: 1px 1px 0 #FFF;
    color: #296FA7;
}
#order input:not([type="button"]):focus:not([readonly]) {
    background-color: #FAFAD2 !important;
}
