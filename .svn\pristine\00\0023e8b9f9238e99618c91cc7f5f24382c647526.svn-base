<?php
  /**
   * Sample_KaisyainfoController
   *
   * 会社情報設定 コントローラクラス
   *
   * @category   Sample
   * @package    controller
   * <AUTHOR> XXXXXX
   * @since      2014/xx/xx
   * @filesource 
   */

  /**
   * 会社情報設定 コントローラクラス
   *
   * @category   Sample
   * @package    controller
   * <AUTHOR> XXXXXX
   * @since      2014/xx/xx
   */
class Sample_KaisyainfoController extends Zend_Controller_Action
{
    /**
     * index アクション (省略時(Action未指定時)のアクセス画面)
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     */
    public function indexAction()
    {
        // main アクションへ委譲
        $this->_forward( 'main' );
    }

    /**
     * main アクション (表示画面)
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     */
    public function mainAction()
    {
        $dataApp = $this->_getKaisyaInfo();

        $data = array( 'dataApp' => $dataApp );

        // JSON 化して HTML に設定する
        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        App_Smarty::pushCssFile( ['app/sample.kaisyainfo.css'] );
        
        App_Smarty::pushJsFile( ['app/sample.kaisyainfo.js'] );

    }

    /**
     * 会社データ取得 ラッパ
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     *
     * @return array()     対象とする会社情報項目
     */
    protected function _getKaisyaInfo()
    {
        $db = Msi_Sys_DbManager::getMyDb();

        // 会社情報を取得
        $info = DataMapper_KaisyaInfo::findOne( $db );

        if ( $info === null ) {
            throw new Exception( '会社情報が存在しません' );
        }

        // 返却データを設定する
        $dataTgt = Msi_Sys_Utils::remapArrayFlat( $info, <<< END_OF_TXT
kaisya_cd      kaisya_lnm     kaisya_lknm    kaisya_snm     kaisya_sknm   
zip_no         addr1_nm       addr2_nm       addr1_knm      addr2_knm     
tel            fax            daihyo_nm      daihyo_knm     kesan_st_date_
kesan_ed_date_ logo_file_nm   logo_img       ryoshusho_kbn  db_version    
syaban_file_nm syaban_img
END_OF_TXT
                                                  , array( 'kesan_st_date_' => 'kesan_st_date',
                                                           'kesan_ed_date_' => 'kesan_ed_date' ) );

        return $dataTgt;
    }


    /**
     * データ更新 アクション
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     */
    public function updateAction()
    {
        $req = $this->getRequest();

        $params  = Msi_Sys_Utils::webInputs();
        Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        $dataApp = Msi_Sys_Utils::json_decode( $req->getPost('dataAppJson') );

        Msi_Sys_Utils::debug( 'dataApp==>' . Msi_Sys_Utils::dump($dataApp) );

        $dataIn = Msi_Sys_Utils::remapArrayFlat( $dataApp, <<< END_OF_TXT
kaisya_cd      kaisya_lnm     kaisya_lknm    kaisya_snm     kaisya_sknm   
zip_no         addr1_nm       addr2_nm
tel            fax            daihyo_nm      daihyo_knm     kesan_st_date 
kesan_ed_date  logo_file_nm   logo_img       ryoshusho_kbn
syaban_file_nm syaban_img
END_OF_TXT
                                                 );
        // , array( 'kaisya_lnm' => 'kaisya_lnm' ) );

        $dataIn = Msi_Sys_Utils::emptyToNullArr( $dataIn );

        Msi_Sys_Utils::debug( 'dataIn==>' . Msi_Sys_Utils::dump($dataIn) );

        try {
            $db = Msi_Sys_DbManager::getMyDb();

            DataMapper_KaisyaInfo::upsert( $db, $dataIn );

            $db->commit();

            $dataApp = $this->_getKaisyaInfo();

            $data = array(
                          'dataApp' => $dataApp,
                          'status' => 'OK',
                          'msg' => '更新しました',
                          );
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $data = array(
                          'status' => 'NG',
                          'msg' => $userMsg,
                          );
        }

        Msi_Sys_Utils::outJson( $data );
    }

    /**
     * blob 読込 アクション
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     */
    public function readblobAction()
    {
        // abst モジュール, util コントローラ, readblob アクションへ委譲
        $this->_forward( 'readblob', 'util', 'abst' );
    }

    /**
     * 画像読込 アクション
     *
     * <AUTHOR> XXXXXX
     * @since 2014/xx/xx
     */
    public function writeblobAction()
    {
        // abst モジュール, util コントローラ, writeblob アクションへ委譲
        $this->_forward( 'writeblob', 'util', 'abst' );
    }


}
