<?php

/**
 * DataMapper_NyukinDenpyo
 *
 * 入金伝票 データマッパークラス
 * 	■ごんきや向けカスタマイズ
 * 		(1)	単品／別注品の赤伝起票での入金伝票の赤伝起票に対応し、
 * 			元伝票 および 赤伝票 の入金は、修正／削除不可にするため、
 * 			元の売上伝票のv_free4（元伝票番号または赤伝票番号）を取得する
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Kayo
 * @since      2013/06/15
 * @version    2025/04/xx Tosaka bellmony版よりコピー
 * @filesource 
 */

/**
 * 入金伝票 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Kayo
 * @since      2013/06/15
 */
class DataMapper_NyukinDenpyo extends DataMapper_Abstract {

    /**
     * 入金伝票 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/06/15
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }
        // 会社情報を取得   2016/04/04 ADD Kayo
        if (!App_Utils::isFukusuKaisyaKanri()) {
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0                    
END_OF_SQL
            );
        } else {
            $kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2016/12/25 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0
AND   kaisya_cd = :kaisya_cd    
END_OF_SQL
                    , array('kaisya_cd' => $kaisyacd));
        }
        $nyukin_den_auto = 0;
        if (count($kaisyainfo) > 0) {
            $nyukin_den_auto = $kaisyainfo['nyukin_den_auto'];   // 互助会入金伝票自動作成 0：作成する 1:作成しない
        }
        $add_sql = null;
        if ($nyukin_den_auto == 1) {
            $add_sql = ' + COALESCE(u.sougi_keiyaku_prc,0) + COALESCE(u.sougi_harai_prc,0) + COALESCE(u.sougi_wari_prc,0) ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
            SELECT m.*
		,tt.tanto_nm
		,tt.tanto_knm
		,COALESCE(b.bumon_lnm,'')   AS  bumon_nm
		,COALESCE(b.bumon_snm,'')   AS  bumon_snm
	   	,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD')  AS   seikyu_ymd -- 売上日
	   	,COALESCE(su.seikyu_zan,u.seikyu_zan)   AS  seikyu_zan  -- 請求残高
	   	,COALESCE(su.nyukin_prc,COALESCE(u.nyukin_prc,0)) + COALESCE(su.uchikin_prc,COALESCE(u.uchikin_prc,0))   AS  sei_nyukin_prc  -- 入金金額
	   	,u.sekyu_cd AS  seikyu_cd    -- 請求先コード
	   	,u.sekyu_nm AS  seikyu_nm   -- 請求先名
	   	,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'')   AS  seikyu_addr -- 請求先住所
		,COALESCE(u.uri_prc_sum,0)
                    + COALESCE(u.uri_nebk_sum,0)
                    + COALESCE(u.uri_hepn_sum,0)
                    + COALESCE(u.hoshi_prc_sum,0)
                    + COALESCE(u.sougi_zei_sagaku_prc,0)
                    + COALESCE(u.sougi_meigi_chg_cost,0)
                    + COALESCE(u.sougi_early_use_cost,0)
                    + COALESCE(u.etc_early_use_cost,0)
                $add_sql    AS  seikyu_prc  -- 請求金額		
		,u.out_zei_prc + u.in_zei_prc   AS  zei_prc -- 消費税額 		
                ,CASE WHEN su.seikyu_den_no IS NOT NULL THEN 
                    su.uri_prc_sum
                    + su.uri_nebk_sum	  
                    + su.uri_hepn_sum
                    + su.hoshi_prc_sum
                    + su.out_zei_prc 
                    + su.sougi_keiyaku_prc + su.sougi_harai_prc
                    + su.sougi_keiyaku_zei
                    + su.sougi_wari_prc + su.sougi_wari_zei
                    + su.sougi_premium_service_prc
                    + su.sougi_meigi_chg_cost + su.sougi_meigi_chg_cost_zei
                    + su.sougi_early_use_cost + su.sougi_early_use_cost_zei
                    + su.sougi_tokuten_prc
                    + COALESCE(su.n_free9, 0) 
                    + COALESCE(su.n_free10, 0) 
                ELSE 
                    u.uri_prc_sum
                    + u.uri_nebk_sum	  
                    + u.uri_hepn_sum
                    + u.hoshi_prc_sum
                    + u.out_zei_prc 
                    + u.sougi_keiyaku_prc + u.sougi_harai_prc
                    + u.sougi_keiyaku_zei
                    + u.sougi_wari_prc + u.sougi_wari_zei
                    + u.sougi_premium_service_prc
                    + u.sougi_meigi_chg_cost + u.sougi_meigi_chg_cost_zei
                    + u.sougi_early_use_cost + u.sougi_early_use_cost_zei
                    + u.sougi_tokuten_prc
                    + COALESCE(u.n_free9, 0) 
                    + COALESCE(u.n_free10, 0) 
                END AS  seikyu_zei_prc  -- 請求金額（消費税込み）
		,u.denpyo_no    AS  juchu_denpyo_no -- 受注伝票№
		,m.uri_history_no   -- 売上履歴番号		
                ,'登録：' || ctt.tanto_nm || '　(' || TO_CHAR(m._cre_ts, 'YYYY/MM/DD HH24:MI:SS') || ')' AS  cre_user
                ,CASE WHEN m._cre_ts = m._mod_ts THEN NULL
                   ELSE '変更：' || utt.tanto_nm || '　(' || TO_CHAR(m._mod_ts, 'YYYY/MM/DD HH24:MI:SS') || ')'
                END AS  mod_user
                ,'財務連動：' || zrt.tanto_nm || '　(' || TO_CHAR(m.zaimu_rendo_date, 'YYYY/MM/DD HH24:MI:SS') || ')'   AS  zaimu_rendo_user
		,u.v_free4  AS  akashori_uri_den_no
		,m.zaimu_rendo_denno    AS  moto_nyukin_denpyo_no	
                ,su.seikyu_den_no
            FROM nyukin_denpyo m
            LEFT JOIN tanto_mst tt
                ON tt.delete_flg = 0
                AND m.tanto_cd = tt.tanto_cd   
                $dateWhere2
            LEFT JOIN bumon_mst b
                ON b.delete_flg = 0
                AND m.bumon_cd = b.bumon_cd   
                $dateWhere3
            LEFT JOIN uriage_denpyo u
                ON u.delete_flg = 0
                AND u.uri_den_no = m.uri_den_no
            LEFT JOIN seikyu_denpyo su
                ON su.delete_flg = 0
                AND su.seikyu_den_no = m.seikyu_no
            LEFT JOIN login_mst cltm
                ON CASE WHEN LENGTH(m._cre_user)-10 > 0 THEN
                    SUBSTR(m._cre_user,10, LENGTH(m._cre_user)-9 )
                    ELSE NULL
                END = cltm.login_cd
                AND 0 = cltm.delete_flg          
            LEFT JOIN tanto_mst ctt         
                ON cltm.tanto_cd = ctt.tanto_cd
                AND 0 = ctt.delete_flg
            LEFT JOIN login_mst ultm
                ON CASE WHEN LENGTH(m._mod_user)-10 > 0 THEN
                    SUBSTR(m._mod_user,10, LENGTH(m._mod_user)-9 )
                    ELSE NULL
                END = ultm.login_cd
                AND 0 = ultm.delete_flg          
            LEFT JOIN tanto_mst utt     
                ON ultm.tanto_cd = utt.tanto_cd
                AND 0 = utt.delete_flg
            LEFT JOIN tanto_mst zrt 
                ON m.zaimu_tanto_cd = zrt.tanto_cd
                AND 0 = zrt.delete_flg
            WHERE m.delete_flg = 0   
            $dateWhere1
        ) T
        WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        foreach ($select as &$rec) {
            $dtl = static::findDenpyoMsi($db, $rec['denpyo_no']);
            $rec['_dtl_'] = $dtl;

            foreach (Msi_Sys_Utils::strArrayify_qw('nyukin_ymd') as $key) {
                if (strlen($rec[$key]) > 0) {
                    $rec[$key] = Msi_Sys_Utils::normYYYYMMDD($rec[$key], '/');
                }
            }
        }

        return $select;
    }

    /**
     * 入金明細 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/06/15
     * @version 2015/08/23 科目別補助マスタを追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no 入金伝票番号
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi($db, $denpyo_no) {
        $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";

        $select = $db->easySelect(<<< END_OF_SQL
SELECT  m.*,
		COALESCE(b.bumon_lnm,'')	AS	bumon_nm,
		kamoku_nm,		-- 科目名
		taishaku_kbn,	-- 貸借区分
		zei_cd_inp_kbn, -- 税コード入力区分
		bumon_inp_kbn,	-- 部門入力区分
		kh.hojo_nm,		-- 補助名	2015/08/23 ADD Kayo
		km.hojo_inp_kbn	-- 科目別補助入力区分	2015/08/23 ADD Kayo
FROM nyukin_denpyo_msi m
LEFT JOIN nyukin_denpyo nh          -- 2016/12/25 ADD Kayo
ON  m.denpyo_no =   nh.denpyo_no
AND 0           =   nh.delete_flg    
LEFT JOIN bumon_mst b
   ON b.delete_flg		=	0
   AND m.bumon_cd		=	b.bumon_cd   $dateWhere3
LEFT JOIN kamoku_mst km
   ON	3				=	km.kamoku_kbn
   AND	m.kamoku_cd		=	km.kamoku_cd
   AND  nh.kaisya_cd    =   km.kaisya_cd    -- 2016/12/25 ADD Kayo             
   AND	0				=	km.delete_flg
LEFT JOIN kamoku_hojo_mst kh		-- 科目別補助　2015/08/23 ADD Kayo
	ON	m.kamoku_cd		=	kh.kamoku_cd
    AND nh.kaisya_cd    =   kh.kaisya_cd    -- 2016/12/25 ADD Kayo             
	AND	m.hojo_cd		=	kh.hojo_cd
	AND	0				=	kh.delete_flg
 WHERE m.delete_flg	=	0
   AND m.denpyo_no	=	:denpyo_no
 ORDER BY disp_no
END_OF_SQL
                , array('denpyo_no' => $denpyo_no));

        return $select;
    }

    /**
     * 入金伝票（科目指定） 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/07/04
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no,T.disp_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        // 会社情報を取得   2016/04/04 ADD Kayo
        if (!App_Utils::isFukusuKaisyaKanri()) {
            // 会社情報を取得   2016/04/04 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0                    
END_OF_SQL
            );
        } else {
            $kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2016/12/25 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0
AND   kaisya_cd = :kaisya_cd    
END_OF_SQL
                    , array('kaisya_cd' => $kaisyacd));
        }
        $nyukin_den_auto = 0;
        if (count($kaisyainfo) > 0) {
            $nyukin_den_auto = $kaisyainfo['nyukin_den_auto'];   // 互助会入金伝票自動作成 0：作成する 1:作成しない
        }
        $add_sql = null;
        if ($nyukin_den_auto == 1) {
            $add_sql = '+ u.sougi_keiyaku_prc + u.sougi_harai_prc + u.sougi_wari_prc';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
            SELECT n.*
                ,tt.tanto_nm    AS  tanto_nm    -- 担当者名
		,tt.tanto_knm   AS  tanto_knm   -- 担当者名カナ
		,COALESCE(b.bumon_lnm,'')   AS  bumon_nm    -- 部門名
		,COALESCE(b.bumon_snm,'')   AS  bumon_snm   -- 部門簡略名
	   	,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD')  AS  seikyu_ymd  -- 売上日
	   	,su.keijo_ymd  -- 計上日
	   	,COALESCE(su.seikyu_zan,u.seikyu_zan)   AS  seikyu_zan  -- 請求残高
	   	,COALESCE(su.nyukin_prc,u.nyukin_prc) + COALESCE(su.uchikin_prc,u.uchikin_prc)   AS  sei_nyukin_prc  -- 入金金額
	   	,u.sekyu_cd AS  seikyu_cd   -- 請求先コード
	   	,CASE WHEN COALESCE(sssi.free_kbn1,COALESCE(ussi.free_kbn1,jssi.free_kbn1)) = 1 THEN COALESCE(sssi.sekyu_nm,COALESCE(ussi.sekyu_nm,jssi.sekyu_nm))
                ELSE COALESCE(sssi.sekyu_nm1 || COALESCE(' ' || sssi.sekyu_nm2, ''),COALESCE(ussi.sekyu_nm1 || COALESCE(' ' || ussi.sekyu_nm2, ''),jssi.sekyu_nm1 || COALESCE(' ' || jssi.sekyu_nm2, '')))
		END AS  seikyu_nm   -- 請求先名
                ,u.sekyu_knm    AS  seikyu_knm  -- 請求先名カナ
	   	,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'')   AS seikyu_addr  -- 請求先住所
		,u.uri_prc_sum 
                    + u.uri_nebk_sum 
                    + u.uri_hepn_sum 
                    + u.hoshi_prc_sum 
                    + u.sougi_zei_sagaku_prc 
                    + u.sougi_meigi_chg_cost
                    + u.sougi_early_use_cost
                    + u.etc_early_use_cost
                $add_sql    AS  seikyu_prc  -- 請求金額		
		,u.out_zei_prc + u.in_zei_prc   AS  zei_prc -- 消費税額 			
                ,CASE WHEN su.seikyu_den_no IS NOT NULL THEN 
                    su.uri_prc_sum
                    + su.uri_nebk_sum	  
                    + su.uri_hepn_sum
                    + su.hoshi_prc_sum
                    + su.out_zei_prc 
                    + su.sougi_keiyaku_prc + su.sougi_harai_prc
                    + su.sougi_keiyaku_zei
                    + su.sougi_wari_prc + su.sougi_wari_zei
                    + su.sougi_premium_service_prc
                    + su.sougi_meigi_chg_cost + su.sougi_meigi_chg_cost_zei
                    + su.sougi_early_use_cost + su.sougi_early_use_cost_zei
                    + su.sougi_tokuten_prc
                    + COALESCE(su.n_free9, 0) 
                    + COALESCE(su.n_free10, 0) 
                ELSE 
                    u.uri_prc_sum
                    + u.uri_nebk_sum	  
                    + u.uri_hepn_sum
                    + u.hoshi_prc_sum
                    + u.out_zei_prc 
                    + u.sougi_keiyaku_prc + u.sougi_harai_prc
                    + u.sougi_keiyaku_zei
                    + u.sougi_wari_prc + u.sougi_wari_zei
                    + u.sougi_premium_service_prc
                    + u.sougi_meigi_chg_cost + u.sougi_meigi_chg_cost_zei
                    + u.sougi_early_use_cost + u.sougi_early_use_cost_zei
                    + u.sougi_tokuten_prc
                    + COALESCE(u.n_free9, 0) 
                    + COALESCE(u.n_free10, 0) 
                END AS  seikyu_zei_prc  -- 請求金額（消費税込み）
		,m.disp_no
		,m.kamoku_cd		
		,km.kamoku_nm																		-- 科目名
		,n.nyu_kbn AS nyukin_kbn
		,nyu_kbn_cnm.kbn_value_lnm AS  nyukin_kbn_nm
		,seikyu_kbn_cnm.kbn_value_lnm    AS  seikyu_kbn_nm   -- 請求区分名
		,TO_CHAR(n._cre_ts,'YYYY/MM/DD HH24:MI:SS') AS  syori_ymd_disp  -- 処理日	
		,TO_CHAR(n._cre_ts,'YYYY/MM/DD')    AS  syori_ymd   -- 処理日
		,n.uri_history_no   -- 売上履歴番号	
                ,su.seikyu_den_no
                ,n.uri_den_no
            FROM nyukin_denpyo n
            LEFT JOIN nyukin_denpyo_msi m
                ON n.denpyo_no = m.denpyo_no
                AND 0 = m.delete_flg				
            LEFT JOIN tanto_mst tt
                ON tt.delete_flg = 0
                AND n.tanto_cd = tt.tanto_cd   
                $dateWhere2
            LEFT JOIN bumon_mst b
                ON b.delete_flg = 0
                AND n.bumon_cd = b.bumon_cd   
                $dateWhere3
            LEFT JOIN juchu_sekyu_saki_info jssi
                ON jssi.seko_no=n.seko_no
		AND jssi.seq_no = 1
		AND jssi.data_kbn in (1,2)
		AND jssi.delete_flg = 0
            LEFT JOIN uriage_denpyo u
                ON u.delete_flg = 0
                AND u.uri_den_no = n.uri_den_no
            LEFT JOIN uriage_sekyu_saki_info ussi
                ON ussi.uri_den_no=u.uri_den_no
		AND ussi.seq_no = 1
		AND ussi.delete_flg = 0
            LEFT JOIN seikyu_denpyo su
                ON su.delete_flg = 0
                AND su.seikyu_den_no = n.seikyu_no
            LEFT JOIN seikyu_sekyu_saki_info sssi
		ON sssi.seikyu_den_no = su.seikyu_den_no
		AND sssi.seq_no = 1
		AND sssi.delete_flg = 0
            LEFT JOIN kamoku_mst km
                ON 3 = km.kamoku_kbn
                AND n.kaisya_cd = km.kaisya_cd    -- 2016/12/25 ADD Kayo             
                AND m.kamoku_cd = km.kamoku_cd
                AND 0 = km.delete_flg
            LEFT JOIN code_nm_mst seikyu_kbn_cnm
                ON 0 = seikyu_kbn_cnm.delete_flg
                AND '8320' = seikyu_kbn_cnm.code_kbn
                AND n.data_kbn = seikyu_kbn_cnm.kbn_value_cd_num				
            LEFT JOIN code_nm_mst nyu_kbn_cnm
                ON 0 = nyu_kbn_cnm.delete_flg
                AND '8579' = nyu_kbn_cnm.code_kbn
                AND n.nyu_kbn = nyu_kbn_cnm.kbn_value_cd_num			
            WHERE n.delete_flg = 0   
            $dateWhere1
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        // 科目名を編集
        $kamoku_nm = '';  // 名札
        $init_flg = 0;  // 初回フラグ
        $new_key = '';  // NEWキー
        $old_key = '';  // OLDキー
        $count = 0;  // カウンター
        $retArray = array();
        $i = 0;
        foreach ($select as $nyukin_rec) {
            $new_key = $nyukin_rec['denpyo_no'];
            if ($init_flg == 0) {
                $old_key = $new_key;
                $init_flg = 1;
            }
            //伝票番号がブレークしたとき
            if ($new_key !== $old_key) {
                $retArray[$i]['kamoku_nm'] = $kamoku_nm; // 科目名を再設定
                $old_key = $new_key;
                $kamoku_nm = '';  // 科目名
                $count = 0;  // カウンター
                ++$i;
            }
            ++$count;
            if ($count == 1) {
                $retArray[$i] = $nyukin_rec;
            }
            // 名札を編集
            if (strlen($nyukin_rec['kamoku_nm']) > 0) {
                if ($kamoku_nm == '') {
                    $kamoku_nm = $nyukin_rec['kamoku_nm'];
                } else {
                    $kamoku_nm = $kamoku_nm . '、' . $nyukin_rec['kamoku_nm'];
                }
            }
        }
        if ($count > 0) {
            $retArray[$i]['kamoku_nm'] = $kamoku_nm; // 科目名を再設定
        }

        foreach ($retArray as &$rec) {
            $dtl = static::findDenpyoMsi($db, $rec['denpyo_no']);
            $rec['_dtl_'] = $dtl;
            // 請求先情報を設定する
            $sekyuSaki = array();
            if (isset($rec['seikyu_den_no'])) {
                $sekyuSaki = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $rec['seikyu_den_no']));
            } else if (isset($rec['uri_den_no'])) {
                $sekyuSaki = DataMapper_SekyuSakiInfo::findUriagesekyu($db, array('uri_den_no' => $rec['uri_den_no']));
            } else if (isset($rec['seko_no']) && ($rec['data_kbn'] == '1' || $rec['data_kbn'] == '2')) {
                $sekyuSaki = DataMapper_SekyuSakiInfo::findUriagesekyu($db, array('seko_no' => $rec['seko_no'], 'data_kbn' => $rec['data_kbn']));
            }
            if (count($sekyuSaki) > 0) {
                $sekyu_addr = null;
                $rec['seikyu_cd'] = $sekyuSaki[0]['sekyu_cd'];
                if (isset($sekyuSaki[0]['sekyu_addr1']) && isset($sekyuSaki[0]['sekyu_addr2'])) {
                    $sekyu_addr = $sekyuSaki[0]['sekyu_addr1'] . ' ' . $sekyuSaki[0]['sekyu_addr2'];
                } else if (isset($sekyuSaki[0]['sekyu_addr1'])) {
                    $sekyu_addr = $sekyuSaki[0]['sekyu_addr1'];
                } else if (isset($sekyuSaki[0]['sekyu_addr2'])) {
                    $sekyu_addr = $sekyuSaki[0]['sekyu_addr2'];
                }
                $rec['seikyu_addr'] = $sekyu_addr;
            }

            foreach (Msi_Sys_Utils::strArrayify_qw('nyukin_ymd') as $key) {
                if (strlen($rec[$key]) > 0) {
                    $rec[$key] = Msi_Sys_Utils::normYYYYMMDD($rec[$key], '/');
                }
            }
        }

        return $retArray;
    }

    /**
     * 入金明細 取得(科目指定)
     *
     * <AUTHOR> Kayo
     * @since      2014/06/15
     * @version 2015/08/23 科目別補助マスタを追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      string  $seko_no			施行番号
     * @param      string  $seko_no_sub		施行番号（枝番）
     * @param      array   $kamoku_cd
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi_Kamoku($db, $seko_no, $seko_no_sub, $kamoku_cd) {
        // 科目コードのIN条件を作成
        $where1 = '';
        $i = 0;
        foreach ($kamoku_cd as $kamoku) {
            if ($i == 0) {
                $where1 = "'" . $kamoku['kamoku_cd'] . "'";
            } else {
                $where1 = $where1 . ",'" . $kamoku['kamoku_cd'] . "'";
            }
            $i++;
        }

        $dateWhere2 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";

        $select = $db->easySelect(<<< END_OF_SQL
SELECT  m.*,
		COALESCE(b.bumon_lnm,'')	AS	bumon_nm,
		kamoku_nm,		-- 科目名
		taishaku_kbn,	-- 貸借区分
		zei_cd_inp_kbn, -- 税コード入力区分
		bumon_inp_kbn,	-- 部門入力区分
		h.seko_no,		-- 施行番号
		h,seko_no_sub,	-- 施行番号（枝番）
		kh.hojo_nm,		-- 補助名	2015/08/23 ADD Kayo
		km.hojo_inp_kbn	-- 科目別補助入力区分	2015/08/23 ADD Kayo
FROM nyukin_denpyo_msi m
LEFT JOIN nyukin_denpyo h
	ON	m.denpyo_no = h.denpyo_no
	AND	0			= h.delete_flg				
LEFT JOIN bumon_mst b
   ON b.delete_flg		=	0
   AND m.bumon_cd		=	b.bumon_cd   $dateWhere2
LEFT JOIN kamoku_mst km
   ON	3				=	km.kamoku_kbn
   AND  h.kaisya_cd     =   km.kaisya_cd    -- 2016/12/25 ADD Kayo             
   AND	m.kamoku_cd		=	km.kamoku_cd
   AND	0				=	km.delete_flg
LEFT JOIN kamoku_hojo_mst kh		-- 科目別補助　2015/08/23 ADD Kayo
	ON	m.kamoku_cd		=	kh.kamoku_cd
   AND  h.kaisya_cd     =   kh.kaisya_cd    -- 2016/12/25 ADD Kayo             
	AND	m.hojo_cd		=	kh.hojo_cd
	AND	0				=	kh.delete_flg
 WHERE m.delete_flg	=	0
   AND h.seko_no		=	:seko_no
   AND h.seko_no_sub	=	:seko_no_sub
   AND m.kamoku_cd		IN 	($where1)
 ORDER BY disp_no
END_OF_SQL
                , array('seko_no' => $seko_no
            , 'seko_no_sub' => $seko_no_sub
        ));

        return $select;
    }

    /**
     * 入金明細 取得(互助会入金)
     *
     * <AUTHOR> Kayo
     * @since      2014/06/15
     * @version 2015/08/23 科目別補助マスタを追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      string  $seko_no			施行番号
     * @param      string  $seko_no_sub		施行番号（枝番）
     * @param      string $history_no 売上履歴番号
     * @param      array   $kamoku_cd
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi_gojokai($db, $seko_no, $seko_no_sub, $history_no, $kamoku_cd) {
        // 科目コードのIN条件を作成
        $where1 = '';
        $i = 0;
        foreach ($kamoku_cd as $kamoku) {
            if ($i == 0) {
                $where1 = "'" . $kamoku['kamoku_cd'] . "'";
            } else {
                $where1 = $where1 . ",'" . $kamoku['kamoku_cd'] . "'";
            }
            $i++;
        }

        $dateWhere2 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";

        $select = $db->easySelect(<<< END_OF_SQL
SELECT  m.*,
		COALESCE(b.bumon_lnm,'')	AS	bumon_nm,
		kamoku_nm,          -- 科目名
		taishaku_kbn,       -- 貸借区分
		zei_cd_inp_kbn,     -- 税コード入力区分
		bumon_inp_kbn,      -- 部門入力区分
		h.seko_no,          -- 施行番号
		h,seko_no_sub,      -- 施行番号（枝番）
        h.uri_history_no,   -- 売上履歴番号
		kh.hojo_nm,			-- 補助名	2015/08/23 ADD Kayo
		km.hojo_inp_kbn		-- 科目別補助入力区分	2015/08/23 ADD Kayo
FROM nyukin_denpyo_msi m
LEFT JOIN nyukin_denpyo h
	ON	m.denpyo_no = h.denpyo_no
	AND	0			= h.delete_flg				
LEFT JOIN bumon_mst b
   ON b.delete_flg		=	0
   AND m.bumon_cd		=	b.bumon_cd   $dateWhere2
LEFT JOIN kamoku_mst km
   ON	3				=	km.kamoku_kbn
   AND  h.kaisya_cd     =   km.kaisya_cd    -- 2016/12/25 ADD Kayo             
   AND	m.kamoku_cd		=	km.kamoku_cd
   AND	0				=	km.delete_flg
LEFT JOIN kamoku_hojo_mst kh		-- 科目別補助　2015/08/23 ADD Kayo
	ON	m.kamoku_cd		=	kh.kamoku_cd
    AND  h.kaisya_cd    =   kh.kaisya_cd    -- 2016/12/25 ADD Kayo             
	AND	m.hojo_cd		=	kh.hojo_cd
	AND	0				=	kh.delete_flg
 WHERE m.delete_flg	=	0
   AND h.seko_no		=	:seko_no
   AND h.seko_no_sub	=	:seko_no_sub
   AND h.uri_history_no =   :uri_history_no             
   AND h.nyu_kbn        =   99             
   AND m.kamoku_cd		IN 	($where1)
 ORDER BY disp_no
END_OF_SQL
                , array('seko_no' => $seko_no
            , 'seko_no_sub' => $seko_no_sub
            , 'uri_history_no' => $history_no
        ));

        return $select;
    }

    /**
     * 入金明細 取得(互助会入金)・・・見積確定取消用
     *
     * <AUTHOR> Kayo
     * @since      2014/11/25
     * @version 2015/08/23 科目別補助マスタを追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      string  $seko_no			施行番号
     * @param      string  $seko_no_sub		施行番号（枝番）
     * @param      array   $kamoku_cd
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi_gojokai2($db, $seko_no, $seko_no_sub, $kamoku_cd) {
        // 科目コードのIN条件を作成
        $where1 = '';
        $i = 0;
        foreach ($kamoku_cd as $kamoku) {
            if ($i == 0) {
                $where1 = "'" . $kamoku['kamoku_cd'] . "'";
            } else {
                $where1 = $where1 . ",'" . $kamoku['kamoku_cd'] . "'";
            }
            $i++;
        }

        $dateWhere2 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";

        $select = $db->easySelect(<<< END_OF_SQL
SELECT  m.*,
		COALESCE(b.bumon_lnm,'')	AS	bumon_nm,
		kamoku_nm,          -- 科目名
		taishaku_kbn,       -- 貸借区分
		zei_cd_inp_kbn,     -- 税コード入力区分
		bumon_inp_kbn,      -- 部門入力区分
		h.seko_no,          -- 施行番号
		h,seko_no_sub,      -- 施行番号（枝番）
        h.uri_history_no,   -- 売上履歴番号
		kh.hojo_nm,			-- 補助名	2015/08/23 ADD Kayo
		km.hojo_inp_kbn		-- 科目別補助入力区分	2015/08/23 ADD Kayo
FROM nyukin_denpyo_msi m
LEFT JOIN nyukin_denpyo h
	ON	m.denpyo_no = h.denpyo_no
	AND	0			= h.delete_flg				
LEFT JOIN bumon_mst b
   ON b.delete_flg		=	0
   AND m.bumon_cd		=	b.bumon_cd   $dateWhere2
LEFT JOIN kamoku_mst km
   ON	3				=	km.kamoku_kbn
   AND  h.kaisya_cd     =   km.kaisya_cd    -- 2016/12/25 ADD Kayo             
   AND	m.kamoku_cd		=	km.kamoku_cd
   AND	0				=	km.delete_flg
LEFT JOIN kamoku_hojo_mst kh		-- 科目別補助　2015/08/23 ADD Kayo
	ON	m.kamoku_cd		=	kh.kamoku_cd
   AND  h.kaisya_cd     =   kh.kaisya_cd    -- 2016/12/25 ADD Kayo             
	AND	m.hojo_cd		=	kh.hojo_cd
	AND	0				=	kh.delete_flg
 WHERE m.delete_flg	=	0
   AND h.seko_no		=	:seko_no
   AND h.seko_no_sub	=	:seko_no_sub
   AND h.nyu_kbn        =   99             
   AND m.kamoku_cd		IN 	($where1)
 ORDER BY disp_no
END_OF_SQL
                , array('seko_no' => $seko_no
            , 'seko_no_sub' => $seko_no_sub
        ));

        return $select;
    }

    /**
     * 入金伝票 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/06/29
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findsum($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
        } else {
            $dateWhere1 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
	SELECT
		 seikyu_no			AS	seikyu_no	-- 請求番号
        ,nyu_kbn            AS  nyu_kbn     -- 入金区分        
		,max(nyukin_ymd)	AS	nyukin_ymd	-- 入金日
		,sum(nyukin_prc)	AS	nyukin_prc	-- 入金金額合計
FROM nyukin_denpyo
WHERE delete_flg		=	0   $dateWhere1
GROUP BY seikyu_no,	nyu_kbn			
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 入金伝票 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/06/29
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findsum2($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
        } else {
            $dateWhere1 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
	SELECT
		 seikyu_no						AS	seikyu_no	-- 請求番号
		,TO_CHAR(nyukin_ymd,'YYYYMM')	AS	nyukin_ym	-- 入金年月			
		,max(nyukin_ymd)				AS	nyukin_ymd	-- 入金日
		,sum(nyukin_prc)				AS	nyukin_prc	-- 入金金額合計
FROM nyukin_denpyo m
 WHERE m.delete_flg		=	0   $dateWhere1
 GROUP BY seikyu_no,TO_CHAR(nyukin_ymd,'YYYYMM')				
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 入金伝票（フロント入出金） 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/11/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findFront($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no,T.disp_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        // 会社情報を取得   2016/04/04 ADD Kayo
        if (!App_Utils::isFukusuKaisyaKanri()) {
            // 会社情報を取得   2016/04/04 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0                    
END_OF_SQL
            );
        } else {
            $kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2016/12/25 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
SELECT nyukin_den_auto
FROM kaisya_info
WHERE delete_flg = 0
AND   kaisya_cd = :kaisya_cd    
END_OF_SQL
                    , array('kaisya_cd' => $kaisyacd));
        }
        $nyukin_den_auto = 0;
        if (count($kaisyainfo) > 0) {
            $nyukin_den_auto = $kaisyainfo['nyukin_den_auto'];   // 互助会入金伝票自動作成 0：作成する 1:作成しない
        }
        $add_sql = null;
        if ($nyukin_den_auto == 1) {
            $add_sql = '+ u.sougi_keiyaku_prc + u.sougi_harai_prc + u.sougi_wari_prc';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (
    SELECT 
         n.*
        ,tt.tanto_nm                                                            AS  tanto_nm        -- 担当者名
        ,tt.tanto_knm                                                           AS  tanto_knm       -- 担当者名カナ
        ,COALESCE(b.bumon_lnm,'')                                               AS  bumon_nm        -- 部門名
        ,COALESCE(b.bumon_snm,'')                                               AS  bumon_snm       -- 部門簡略名
        ,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD')                                      AS  seikyu_ymd      -- 売上日
        ,u.seikyu_zan                                                           AS  seikyu_zan      -- 請求残高
        ,u.nyukin_prc                                                           AS  sei_nyukin_prc  -- 入金金額
        ,u.sekyu_cd                                                             AS  seikyu_cd       -- 請求先コード
        ,u.sekyu_nm                                                             AS  seikyu_nm       -- 請求先名
        ,u.sekyu_knm                                                            AS  seikyu_knm      -- 請求先名カナ
        ,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'')               AS  seikyu_addr     -- 請求先住所
        ,u.uri_prc_sum 
            + u.uri_nebk_sum 
            + u.uri_hepn_sum 
            + u.hoshi_prc_sum 
            + u.sougi_zei_sagaku_prc 
            + u.sougi_meigi_chg_cost 
            + u.sougi_early_use_cost
            + u.etc_early_use_cost
            $add_sql                                                            AS  seikyu_prc      -- 請求金額		
        ,u.out_zei_prc + u.in_zei_prc                                           AS  zei_prc         -- 消費税額 	
        ,u.uri_prc_sum
            + u.uri_nebk_sum	  
            + u.uri_hepn_sum
            + u.hoshi_prc_sum
            + u.out_zei_prc 
            + u.sougi_keiyaku_prc + u.sougi_harai_prc
            + u.sougi_keiyaku_zei
            + u.sougi_wari_prc
            + u.sougi_premium_service_prc
            + u.sougi_meigi_chg_cost + u.sougi_meigi_chg_cost_zei
            + u.sougi_early_use_cost + u.sougi_early_use_cost_zei
            + u.etc_harai_prc                                              AS  seikyu_zei_prc  -- 請求金額（消費税込み）
        ,m.disp_no
        ,m.kamoku_cd
        ,km.kamoku_nm                                                           -- 科目名
        ,CASE WHEN n.nyu_kbn = 99 THEN 2
              ELSE 1
         END                                                                    AS  nyukin_kbn		
        ,CASE WHEN n.nyu_kbn = 99 THEN '互助会入金'
              ELSE '通常入金'
         END                                                                    AS  nyukin_kbn_nm
        ,c.kbn_value_lnm                                                        AS  seikyu_kbn_nm   -- 請求区分名
        ,TO_CHAR(COALESCE(n._mod_ts,n._cre_ts),'YYYY/MM/DD HH24:MI:SS')         AS  syori_ymd_disp  -- 処理日	
        ,TO_CHAR(COALESCE(n._mod_ts,n._cre_ts),'YYYY/MM/DD')			AS  syori_ymd       -- 処理日
        ,n.uri_history_no                                                       -- 売上履歴番号		
    FROM nyukin_denpyo n
    LEFT JOIN nyukin_denpyo_msi m
    ON  n.denpyo_no = m.denpyo_no
    AND	0           = m.delete_flg				
    LEFT JOIN tanto_mst tt
    ON  tt.delete_flg = 0
    AND	n.tanto_cd    = tt.tanto_cd $dateWhere2
    LEFT JOIN bumon_mst b
    ON b.delete_flg   = 0
    AND n.bumon_cd    = b.bumon_cd  $dateWhere3
    LEFT JOIN uriage_denpyo u
    ON  u.delete_flg  = 0
    AND u.uri_den_no  = n.seikyu_no
    LEFT JOIN kamoku_mst km
    ON  3             = km.kamoku_kbn
    AND n.kaisya_cd   = km.kaisya_cd    -- 2016/12/25 ADD Kayo             
    AND	m.kamoku_cd   = km.kamoku_cd
    AND	0             = km.delete_flg
    LEFT JOIN code_nm_mst c
    ON  0             = c.delete_flg
    AND	'0920'        = c.code_kbn
    AND	u.data_kbn    = c.kbn_value_cd_num			
    LEFT JOIN code_nm_mst c_kamoku -- 搬送入金科目・補助科目 支払方法:現金　現金-送迎部のみ結合
    ON  '7999' 	      = c_kamoku.code_kbn
    AND 1             = c_kamoku.kbn_value_cd_num
    AND m.kamoku_cd   = c_kamoku.kbn_value_lnm
    AND m.hojo_cd     = c_kamoku.kbn_value_snm
    WHERE n.delete_flg = 0 $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        // 科目名を編集
        $kamoku_nm = '';  // 名札
        $init_flg = 0;  // 初回フラグ
        $new_key = '';  // NEWキー
        $old_key = '';  // OLDキー
        $count = 0;  // カウンター
        $retArray = array();
        $i = 0;
        foreach ($select as $nyukin_rec) {
            $new_key = $nyukin_rec['denpyo_no'];
            if ($init_flg == 0) {
                $old_key = $new_key;
                $init_flg = 1;
            }
            //伝票番号がブレークしたとき
            if ($new_key !== $old_key) {
                $retArray[$i]['kamoku_nm'] = $kamoku_nm; // 科目名を再設定
                $old_key = $new_key;
                $kamoku_nm = '';  // 科目名
                $count = 0;  // カウンター
                ++$i;
            }
            ++$count;
            if ($count == 1) {
                $retArray[$i] = $nyukin_rec;
            }
            // 名札を編集
            if (strlen($nyukin_rec['kamoku_nm']) > 0) {
                if ($kamoku_nm == '') {
                    $kamoku_nm = $nyukin_rec['kamoku_nm'];
                } else {
                    $kamoku_nm = $kamoku_nm . '、' . $nyukin_rec['kamoku_nm'];
                }
            }
        }
        if ($count > 0) {
            $retArray[$i]['kamoku_nm'] = $kamoku_nm; // 科目名を再設定
        }
        foreach ($retArray as &$rec) {
            $dtl = static::findDenpyoMsi($db, $rec['denpyo_no']);
            $rec['_dtl_'] = $dtl;
            foreach (Msi_Sys_Utils::strArrayify_qw('nyukin_ymd') as $key) {
                if (strlen($rec[$key]) > 0) {
                    $rec[$key] = Msi_Sys_Utils::normYYYYMMDD($rec[$key], '/');
                }
            }
        }
        return $retArray;
    }

    /**
     * 
     * 入金伝票ダイアログCSV出力用
     * 
     * @param type $db
     * @param type $keyHash
     */
    public static function findNyukinCsv($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no,T.disp_no ';
        }
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
     T2.denpyo_no           AS "入金伝票No"
    ,TO_CHAR(T2.nyukin_ymd,'YYYY/MM/DD')          AS "入金日"
    ,T2.seikyu_no           AS "請求No"
    ,CASE WHEN T2.data_kbn IN (1,2) THEN T2.seko_no ELSE NULL END   AS "施行No"
    ,T2.nyukin_kbn          AS "入金区分"
    ,T2.sekyu_kbn           AS "請求区分"
    ,T2.sekyu_nm            AS "請求先"
    ,T2.nyukin_prc          AS "入金金額"
    ,T2.tanto_cd_nm         AS "入力担当"
    ,T2.bumon_cd_nm         AS "売掛部門"
    ,T2.syori_ymd           AS "処理日"
    ,T2.denpyo_biko1        AS "備考"
    ,T2.denpyo_biko2        AS "入金備考"
    ,T2.zaimu_rendo_kbn_nm  AS "会計連携"
    ,T2.n_free2             AS "会費消費税入金額"
    ,T2.n_free3             AS "早期利用費消費税入金額"
    ,T2.n_free4             AS "早期利用費入金額"
    ,T2.n_free5             AS "互助会精算入金額"
    ,T2.mod_tanto_nm        AS "最終変更担当者"
    ,T2.mod_ts              AS "最終更新日"
    ,T2.msi_no              AS "明細No"
    ,T2.nyukn_kbn_nm        AS "入金区伝票区分"
    ,T2.kamoku_nm           AS "科目名"
    ,T2.hojo_nm             AS "補助名"
    ,T2.msi_nyukin_prc      AS "入金金額2"
    ,T2.msi_biko1           AS "摘要"
    ,TO_CHAR(T2.keijo_ymd,'YYYY/MM/DD')           AS "計上日"
FROM (
    SELECT 
        T.* 
    FROM (
        SELECT 
            -- ヘッダ
             nd.data_kbn
            ,nd.seko_no
            ,nd.denpyo_no
            ,nd.nyukin_ymd
            ,nd.seikyu_no
            ,n_kbn.kbn_value_lnm                            AS nyukin_kbn
            ,s_kbn.kbn_value_lnm                            AS sekyu_kbn
            ,nd.nyu_kbn
            ,CASE WHEN COALESCE(sssi.free_kbn1,ussi.free_kbn1) = 1 THEN 
                    COALESCE(sssi.sekyu_nm,ussi.sekyu_nm)
                  ELSE 
                    COALESCE(sssi.sekyu_nm1 || COALESCE(' ' || sssi.sekyu_nm2, ''),ussi.sekyu_nm1 || COALESCE(' ' || ussi.sekyu_nm2, '')) 
             END                                            AS  sekyu_nm   -- 請求先名
            ,nd.nyukin_prc
            ,ud.sekyu_cd
            ,tanto.tanto_cd
            ,CONCAT(tanto.tanto_cd,':',tanto.tanto_nm)      AS tanto_cd_nm
            ,nd.bumon_cd
            ,CONCAT(nd.bumon_cd,':',bumon.bumon_lnm)        AS bumon_cd_nm
            ,TO_CHAR(nd._cre_ts,'YYYY/MM/DD HH24:MI:SS')    AS syori_ymd
            ,TO_CHAR(nd._cre_ts,'YYYY/MM/DD')               AS s_syori_ymd
            ,nd.denpyo_biko1
            ,nd.denpyo_biko2
            ,nd.zaimu_rendo_kbn
            ,CASE nd.zaimu_rendo_kbn 
                  WHEN 0 THEN '0:未出力'
                  WHEN 1 THEN '1:出力済'
                  ELSE null 
             END                                            AS zaimu_rendo_kbn_nm
            ,nd.n_free2
            ,nd.n_free3
            ,nd.n_free4
            ,nd.n_free5
            ,CONCAT(tanto2.tanto_cd,':',tanto2.tanto_nm)    AS mod_tanto_nm
            ,TO_CHAR(nd._mod_ts,'YYYY/MM/DD HH24:MI:SS')    AS mod_ts
            ,nd.n_free1 -- 入金入力
            -- 明細
            ,ndm.disp_no
            ,ndm.msi_no
            ,ndm.nyukn_kbn
            ,nd_kbn.kbn_value_lnm                           AS nyukn_kbn_nm
            ,ndm.kamoku_cd
            ,kamoku.kamoku_nm
            ,ndm.hojo_cd
            ,hojo.hojo_nm
            ,ndm.nyukin_prc                                 AS msi_nyukin_prc
            ,ndm.msi_biko1
            ,nd.d_import_kbn
            ,sd.keijo_ymd
        FROM nyukin_denpyo_msi ndm  -- 入金伝票明細
        INNER JOIN nyukin_denpyo nd -- 入金伝票
        ON  nd.denpyo_no  = ndm.denpyo_no
        AND nd.delete_flg = 0
        LEFT JOIN seikyu_denpyo sd  -- 請求伝票
        ON  sd.seikyu_den_no = nd.seikyu_no
        AND sd.delete_flg    = 0
        LEFT JOIN uriage_denpyo ud  -- 売上伝票
        ON  ud.uri_den_no = nd.uri_den_no
        AND ud.delete_flg = 0
        LEFT JOIN code_nm_mst n_kbn -- 入金区分
        ON  n_kbn.code_kbn         = '8579'
        AND n_kbn.kbn_value_cd_num = nd.nyu_kbn
        AND n_kbn.delete_flg       = 0
        LEFT JOIN code_nm_mst s_kbn -- 請求区分
        ON  s_kbn.code_kbn         = '8320'
        AND s_kbn.kbn_value_cd_num = nd.data_kbn
        AND s_kbn.delete_flg       = 0
        LEFT JOIN login_mst login   -- ログインマスタ
        ON  login.login_cd   = substr(nd._cre_user, strpos(nd._cre_user,'.')+1,char_length(nd._cre_user))
        AND login.delete_flg = 0
        LEFT JOIN tanto_mst tanto   -- 担当マスタ
        ON  tanto.tanto_cd   = login.tanto_cd
        AND tanto.delete_flg = 0
        LEFT JOIN login_mst login2   -- ログインマスタ
        ON  login2.login_cd   = substr(nd._mod_user, strpos(nd._mod_user,'.')+1,char_length(nd._mod_user))
        AND login2.delete_flg = 0
        LEFT JOIN tanto_mst tanto2  -- 担当マスタ
        ON  tanto2.tanto_cd   = login2.tanto_cd
        AND tanto2.delete_flg = 0
        LEFT JOIN bumon_mst bumon   -- 部門マスタ
        ON  bumon.bumon_cd   = nd.bumon_cd
        AND bumon.delete_flg = 0
        LEFT JOIN code_nm_mst nd_kbn -- 入金伝票区分
        ON  nd_kbn.code_kbn         = '2220'
        AND nd_kbn.kbn_value_cd_num = ndm.nyukn_kbn
        AND nd_kbn.delete_flg       = 0
        LEFT JOIN kamoku_mst kamoku  -- 科目マスタ
        ON  kamoku.kamoku_cd  = ndm.kamoku_cd
        AND kamoku.kamoku_kbn = 3 -- 入金科目
        AND kamoku.delete_flg = 0
        LEFT JOIN kamoku_hojo_mst hojo -- 科目補助マスタ
        ON  hojo.kamoku_cd  = ndm.kamoku_cd
        AND hojo.hojo_cd    = ndm.hojo_cd
        AND hojo.delete_flg = 0
        LEFT JOIN seikyu_sekyu_saki_info sssi   -- 請求請求先
        ON  sssi.seikyu_den_no = sd.seikyu_den_no
        AND sssi.seq_no        = 1
        AND sssi.delete_flg    = 0
        LEFT JOIN uriage_sekyu_saki_info ussi   -- 売上請求先
        ON  ussi.uri_den_no = ud.uri_den_no
        AND ussi.seq_no     = 1
        AND ussi.delete_flg = 0
        WHERE ndm.delete_flg = 0
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
) T2
END_OF_SQL
                , $param);
        return $select;
    }

}
