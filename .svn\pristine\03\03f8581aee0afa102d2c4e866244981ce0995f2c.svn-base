<?php

/**
 * Ju<PERSON>_MitsuController
 *
 * 見積 コントローラクラス
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR>
 * @since      2014/xx/xx
 * @filesource 
 */

/**
 * 見積 コントローラクラス
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR>
 * @since      2014/xx/xx
 */
class Juchu_MitsuController extends Msi_Zend_Controller_Action {

    /**
     * index アクション
     *
     * <AUTHOR>
     * @since 2013/09/30
     */
    public function indexAction() {
        // $this->_forward( 'juchu' );
    }

    /**
     * アクション 
     *
     * <AUTHOR>
     * @since 2014/03/03
     */
    public function estimate2Action() {
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('estimate');
        App_Smarty::pushJsFile('app/sidemenu.js', 'app/juchu.mitsu.input.js');
    }

    /**
     * お客様情報初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/03
     */
    public function inputAction() {
        $params = Msi_Sys_Utils::webInputs();
        $sidemenukey = null;
        if (isset($params['sidemenukey']) && strlen($params['sidemenukey']) > 0) {
            $sidemenukey = $params['sidemenukey'];
        }
        App_Utils::setCustomerinfo($this, $sidemenukey);
    }

    /**
     * お客様情報新規作成アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/17
     */
    public function newAction() {
        // 施行番号クリア
        App_Utils::setSessionData('seko_no_mitsu', null);

        $req = $this->getRequest();
        $module = $req->getModuleName();
        $controller = $req->getControllerName();
        $this->_redirect($module . '/' . $controller . '/' . 'input');
    }
    
    /**
     * タイムスケジュール初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/03
     * @version 2016/07/17 他の担当者が変更できない様に修正 Kayo
     */
    public function timescheduleAction() {
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo
        App_Utils::setTimeschedule($this);
    }

    /**
     * タイムスケジュール初期情報取得アクション(PDF表示版) 
     *
     * <AUTHOR> Kayo
     * @since 2016/11/11
     */
    public function timeschedulepdfdispAction() {
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo
        App_Utils::setTimeschedulePdfDisp($this);
    }

    /**
     * 基本プラン初期画面表示 アクション
     *
     * <AUTHOR> Mihara
     * @since 2014/04/04
     */
    public function planAction() {
        $plan = new Juchu_JuchuPlan();
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo
        // 初期データを取得する
        $jsonData = $plan->getInitData();
        $this->view->plan_json = $jsonData;
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('plan');

        // プランデータを取得する
        list($planData, $kihonPlanData) = $plan->getPlanData();
        // $planData = array_slice($planData, 0, 2);
        $this->view->planData = $planData;
        $this->view->kihonPlanData = $kihonPlanData;
        $this->view->kihonPlanOffset = 9999;
        if ($kihonPlanData && count($kihonPlanData) > 0) {
            $this->view->kihonPlanCaption = $kihonPlanData[0]['planname'] . ' 明細';
        }

        // Msi_Sys_Utils::debug( '*** planData=>' . Msi_Sys_Utils::dump($planData) ) ;
        // Msi_Sys_Utils::debug( '*** kihonPlanData=>' . Msi_Sys_Utils::dump($kihonPlanData) ) ;

        App_Smarty::pushCssFile('app/sais.css', 'app/juchu.plan.css');
//        App_Smarty::pushJsFile('app/lib.swiper.js');
        App_Smarty::pushJsFile('app/sidemenu.js', 'app/juchu.plan.js');
    }
    
    /**
     * 対象のコード区分の中身を取得する処理
     *
     * <AUTHOR> Tosaka
     * @since 2014/05/22
     * @return array 年齢　['man']:満XX歳 ['kyonen']:享年XX歳
     */
    public function getcodenmAction() {
        $req = $this->getRequest();
        $db = Msi_Sys_DbManager::getMyDb();
        $dataAry = array();
        if ($req->isPost()) {
            $codeKbn = $req->getPost('codeKbn');
            $orderby = $req->getPost('orderby');
            if (isset($orderby) && strlen($orderby) > 0) {
                $codeNmData = DataMapper_CodeNmMst::find($db, array('code_kbn' => $codeKbn, '__etc_orderby' => array($orderby)));
            } else {
                $codeNmData = DataMapper_CodeNmMst::find($db, array('code_kbn' => $codeKbn));
            }
            $dataAry['status'] = 'OK';
            $dataAry['data_ary'] = $codeNmData;
            Msi_Sys_Utils::outJson($dataAry);
        }
    }

    /**
     * 基本プラン初期画面表示 アクション
     *
     * <AUTHOR> Mihara
     * @since 2014/04/04
     */
    public function searchplanAction() {
        $plan = new Juchu_JuchuPlan();

        $this->_helper->viewRenderer->setScriptAction( 'plansearch' );
        // Jsonデータを取得する
        $params = Msi_Sys_Utils::webInputs();
        // プランデータを取得する
        // 初期データを取得する
        $jsonData = $plan->getInitData();
        $this->view->plan_json = $jsonData;
        list($planData, $kihonPlanData) = $plan->getPlanDataFromJson($params);
        $this->view->planData = $planData;
        $this->view->kihonPlanData = $kihonPlanData;
        $this->view->kihonPlanOffset = 9999;
        if ($kihonPlanData && count($kihonPlanData) > 0) {
            $this->view->kihonPlanCaption = $kihonPlanData[0]['planname'] . ' 明細';
        }
    }

    /**
     * 別注品一覧画面表示 アクション
     *
     * <AUTHOR> Kyounan
     * @since 2014/04/15
     */
    public function bechuichiranAction() {
        App_Smarty::pushCssFile([
            'app/bechuichiran.all.css',
            'lib_jq.css',
            'app/gridtablestyle.css',
            'app/main_overwrite.css',
        ]);
    }

    /**
     * 別注品入力画面表示 アクション
     *
     * <AUTHOR> Kyounan
     * @since 2014/04/15
     */
    public function bechunyuuryokuAction() {
        App_Smarty::pushCssFile([
            'app/bechunyuuryoku.all.css',
            'lib_jq.css',
            'app/gridtablestyle.css',
            'app/main_overwrite.css',
        ]);
    }

    /**
     * 基本プランsave アクション
     *
     * <AUTHOR> Mihara
     * @since 2014/04/04
     */
    public function plansaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $plan = new Juchu_JuchuPlan();
            $dataMain = Msi_Sys_Utils::json_decode($req->getPost('dataMain'));
            $plan->save($dataMain);
        }
    }

    /**
     * 基本プラン img取得 アクション
     * cf. Juchu_JuchuhenkoController::imgAction()
     *
     * <AUTHOR> Mihara
     * @since 2014/04/08
     */
    public function planimgAction() {
        $params = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( '*** params=>' . Msi_Sys_Utils::dump($params) );
        $oid = $params['imgid'];
        $db = Msi_Sys_DbManager::getMyDb();
        $cont = $db->readBlobCont($oid);
        Msi_Sys_Utils::outBinary($cont); // , 'image/jpeg' );
    }

    /**
     * 見積書初期画面表示 アクション
     *
     * <AUTHOR> Sai
     * @since 2014/03/25
     * @version 2016/07/17 他の担当者が変更できない様に修正 Kayo
     */
    public function estimateAction() {
        $estimate = new Juchu_JuchuEstimate();
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo
        // 初期データを取得する
        $jsonData = $estimate->getInitData($this);
        $this->view->estimate_json = $jsonData;
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('estimate');

        App_Smarty::pushCssFile('app/sais.css', 'app/juchu.estimate.css');

        App_Smarty::pushCssFile('app/juchu/juchu.estimate.keigen.css'); // 軽減税率対応 mihara 2019/04/30 keigen
        App_Smarty::pushJsFile('app/lib.keigen_f01.js'); // app/juchu.estimate.js 等より前に呼ぶ 軽減税率対応 mihara 2019/04/30

        App_Smarty::pushJsFile('app/sidemenu.js', 'app/juchu.estimate.js', 'app/lib.mitsu.seikyu.js');

        App_Smarty::pushJsFile('app/juchu/juchu.estimate.keigen.js'); // 軽減税率対応 mihara 2019/04/30 keigen
    }

    /**
     * 見積書saveアクション
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     */
    public function estimatesaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->save();
        }
    }

    /**
     * 見積書確定アクション
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     */
    public function estimatekakuteiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->mitsukakutei();
        }
    }

    /**
     * 見積書取消アクション
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     */
    public function estimatetorikesiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->mitsutorikesi();
        }
    }

    /**
     * 再見積アクション
     *
     * <AUTHOR> Sai
     * @since 2015/06/08
     */
    public function reestimateAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->saimitsu();
        }
    }

    /**
     * 履歴作成アクション
     *
     * <AUTHOR> Tosaka
     * @since 2020/03/30
     */
    public function historymakeAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->historymake();
        }
    }
    
    /**
     * 履歴コピーアクション
     *
     * <AUTHOR> Tosaka
     * @since 2020/03/30
     */
    public function historycopyAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->historycopy();
        }
    }
    
    /**
     * 見積履歴取得アクション
     *
     * <AUTHOR> Sai
     * @since 2015/06/08
     */
    public function historyAction() {
        $estimate = new Juchu_JuchuEstimate();
        // 初期データを取得する
        $jsonData = $estimate->getHistoryData();
        $this->view->data_json = $jsonData;
        App_Smarty::pushCssFile('app/sais.css', 'app/juchu.estimate.history.css');
        App_Smarty::pushJsFile('app/juchu.estimate.history.js');
    }

    /**
     * お礼状 初期画面表示 アクション
     *
     * <AUTHOR> Mihara
     * @since 2014/05/xx
     */
    public function thanksAction() {
        $thanks = new Juchu_JuchuThanks();
        // 初期データを取得する
        $jsonData = $thanks->getInitData();
        $this->view->thanks_json = $jsonData;
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('thanks');
        App_Smarty::pushCssFile('app/sais.css', 'app/juchu.inv.thx.css', 'app/juchu.thanks.css');
        App_Smarty::pushJsFile('app/sidemenu.js', 'app/juchu.inv.thx.teikei.js', 'app/juchu.thanks.js');
    }

    /**
     * お礼状 saveアクション
     *
     * <AUTHOR> Mihara
     * @since 2014/05/xx
     */
    public function thankssaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataOrei'));
            $thanks = new Juchu_JuchuThanks($dataApp['seko_no']);
            $thanks->save();
        }
    }

    /**
     * お礼状 deleteアクション
     *
     * <AUTHOR> Sai
     * @since 2014/06/24
     */
    public function thanksdeleteAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // お礼状データ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataOrei'));
            $invi = new Juchu_JuchuThanks($dataApp['seko_no']);
            $invi->delete();
        }
    }

    /**
     * その他必要なもの初期画面表示アクション
     *
     * <AUTHOR> Kayo
     * @since 2014/05/08
     */
    public function otherAction() {
        //商品数が0のカテゴリも表示したい場合はtrueにする
        $show_empty_category = false;


        $this->view->page = "other";
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('item');

        App_Smarty::pushCssFile('app/juchu.item.css');
        App_Smarty::pushJsFile('app/sidemenu.js', 'app/juchu.mitsu.input.js', 'app/juchu.item.js', 'app/juchu.item.other.js');

        $iteminfo = new Juchu_JuchuIteminfo('other');
        $item_list = $iteminfo->getOtherItem();

        // 初期データを取得する
        $jsonData = $iteminfo->getInitData();
        $this->view->iteminfo_json = $jsonData;
        $this->view->gazo_shohin_zei_kbn = $iteminfo->getGazoShohinZeiKbn(); // 画像商品税込表示区分 2015/04/30 ADD Kayo

        $item_lists = array();
        $shohin_kbn_list = $iteminfo->getShohinKbnList(15);

        foreach ($shohin_kbn_list as $key => $val) {
            $item_lists[$key]['val'] = array();
            $item_lists[$key]['shohin_kbn'] = $key;
        }

        foreach ($item_list as $val) {
            $item_lists[$val['shohin_kbn']]['val'][] = $val;
        }

        if ($show_empty_category == false) {
            $delete_kbn = array();
            foreach ($shohin_kbn_list as $key => $shohin_kbn) {
                if (count($item_lists[$key]['val']) == 0) {
                    $delete_kbn[] = $key;
                }
            }
            foreach ($delete_kbn as $val) {
                unset($shohin_kbn_list[$val]);
                unset($item_lists[$val]);
            }
        }

        $this->view->shohin_kbn_list = $shohin_kbn_list;
        $this->view->item_lists = $item_lists;
        $this->view->categorykbn = $iteminfo->getCategoryKbn();
    }

    // 保存
    /**
     * 選択商品保存
     *
     * <AUTHOR> Kayo
     * @since 2014/05/08
     */
    public function selectsaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataApp'));

            $page_type = $req->getPost('page_type');

            $sekoNo = $req->getPost('seko_no');
            $iteminfo = new Juchu_JuchuIteminfo($page_type, $sekoNo);
            $data = $iteminfo->save($dataApp);
            if (!$data) { // 見積確定時など
                return;
            }
            $retdata = array(
                'status' => $data['status'],
                'dataSideMenu' => $data['dataSideMenu'],
                'msg' => $data['msg']
                    //	'dataMain'		=> $item_list
            );
            Msi_Sys_Utils::outJson($retdata);
        }
    }

    /**
     * 年齢計算処理 （満、享年）
     *
     * <AUTHOR> Sai
     * @since 2014/05/22
     * @return array 年齢　['man']:満XX歳 ['kyonen']:享年XX歳
     */
    public function calcnenreiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $seinengappi = $req->getPost('seinengappi');
            $nakunariymd = $req->getPost('nakunariymd');
            $nenrei = App_DateCalc::GetNenreiCalc2($nakunariymd, $seinengappi);
            $nenrei['status'] = 'OK';
            Msi_Sys_Utils::outJson($nenrei);
        }
    }

    /**
     * ファイル取得 アクション
     *
     * <AUTHOR> Sai
     * @since 2014/08/21
     */
    public function filedownloadAction() {
        $params = Msi_Sys_Utils::webInputs();
        $oid = $params['oid'];
        $db = Msi_Sys_DbManager::getMyDb();
        $cont = $db->readBlobCont($oid);
        Msi_Sys_Utils::outBinary($cont); // , 'image/jpeg' );
//        Msi_Sys_Utils::outBinary($cont, 'application/octet-stream'); // , 'image/jpeg' );
    }

    /**
     * 添付ファイル登録処理 
     *
     * <AUTHOR> Sai
     * @since 2014/08/21
     */
    public function writeblobAction() {
        $req = $this->getRequest();
        if ($req->isPost() && isset($_FILES['file'])) {
            $params = Msi_Sys_Utils::webInputs();
            $fileMaxSize = $params['filemaxsize'];
            $file_size = $_FILES["file"]["size"];
            if ($file_size > $fileMaxSize * 1024 * 1024) {
                Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => ('ファイルサイズが大きいすぎます')));
                return;
            }
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $file_temp = Msi_Sys_Utils::getUploadFh("file");
            $customerinfo->writeBlob($file_temp);
        }
    }

    /**
     * 一般化商品選択(商品選択,返礼品,料理)画面表示 Action
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @version 2016/07/17 他の担当者が変更できない様に修正 Kayo
     */
    public function itemexAction() {
        $this->_params = $params = Msi_Sys_Utils::webInputs();

        // Msi_Sys_Utils::debug( '*** itemexAction=>' . Msi_Sys_Utils::dump($params) );
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo

        @ $lvl0 = Msi_Sys_Utils::checkVarOrDefault($params['_0'], 'DIGIT', null);
        @ $lvl1 = Msi_Sys_Utils::checkVarOrDefault($params['_1'], 'DIGIT', null);
        @ $lvl2 = Msi_Sys_Utils::checkVarOrDefault($params['_2'], 'DIGIT', null);
        @ $showStyle = Msi_Sys_Utils::checkVarOrDefault($params['_s'], '/^(b|l)$/', null);

        if ($showStyle === null) {
            $showStyle = Juchu_JuchuItemex::getCookieForPref($lvl0 . '.bol');
            if (!$showStyle)
                $showStyle = 'b';
        }
        $showStyle = 'b'; // 遷移時は強制的にブロック表示
        //
        // Ajax Request(コンテンツ取得)
        //
        if (isset($params['action'])) {
            @ $dataApp = Msi_Sys_Utils::json_decode($params['dataApp']);
            @ $dataCol = Msi_Sys_Utils::json_decode($params['dataCol']);
            @ $dataEx = Msi_Sys_Utils::json_decode($params['dataEx']);
            // Msi_Sys_Utils::debug( '*** itemexAction dataApp=>' . Msi_Sys_Utils::dump($dataApp) );
            // Msi_Sys_Utils::debug( '*** itemexAction dataCol=>' . Msi_Sys_Utils::dump($dataCol) );
            // Msi_Sys_Utils::debug( '*** itemexAction dataEx=>' . Msi_Sys_Utils::dump($dataEx) );
            if ($params['action'] === 'genitempicker') {
                $lvl1 = $params['catLvl1'];
                @ $lvl2 = $params['catLvl2'];
                // Msi_Sys_Utils::debug( '*** lvl1=>' . $lvl1 . ' lvl2=>' . $lvl2 );
                $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2,
                    'dataApp' => $dataApp, 'dataCol' => $dataCol, 'dataEx' => $dataEx));
                $obj->genItemPicker();
                return;
            } else if ($params['action'] === 'genitemlist') {
                $lvl1 = $params['catLvl1'];
                @ $lvl2 = $params['catLvl2'];
                // Msi_Sys_Utils::debug( '*** lvl1=>' . $lvl1 . ' lvl2=>' . $lvl2 );
                $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2, 'isListCtxt' => true,
                    'dataApp' => $dataApp, 'dataCol' => $dataCol));
                $obj->genItemList();
                return;
            }
        }

        //
        // 初期メイン画面
        //

        // fdn_itemex クッキー削除. メニュー遷移を跨っては状態を管理しない
        Msi_Sys_Utils::delCookie('fdn_itemex');
        $lvl1 = null; // 2015/05/21
        $lvl2 = null; // 先頭に固定はしない // 2015/05/21 1=>null  for saien

        $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2, 'noDfltCookie' => true));

        // ブロック表示とリスト表示の切替をするか
        $this->view->is_view_style_opt = true;

        // ブロック表示 or リスト表示
        $this->view->is_view_style_block = $showStyle === 'b';

        // レベル１タブの読み込み
        $optCatLvl1 = $obj->optCatLvl1();

        // レベル１タブ選択肢の設定
        $this->view->opt_lvl1 = $optCatLvl1;

        // レベル１タブ現在値
        $this->view->cur_lvl1 = $obj->curCatLvl1();

        // レベル１タブ幅の設定
        $isAbleMultiLine = true;
        $maxTabOneLine = 8; // 6,7,8,99
        $cntOfTabItems = count($optCatLvl1);
        if ($cntOfTabItems <= 4) {
            $this->view->cat_lvl1_width = '20%';
        } else if ($cntOfTabItems <= $maxTabOneLine || !$isAbleMultiLine) {
            $this->view->cat_lvl1_width = sprintf("%0.1f%%", 85 / $cntOfTabItems); // 15% for list/block buttons. 均等割
        } else {
            $this->view->is_lvl1_tab_arrow = true;
            $this->view->lvl1_tab_width = "90%"; // for arrow etc. not .lvl1_tab height: 28px 
            if ($maxTabOneLine == 8) { // 7 tabs
                $this->view->cat_lvl1_width = "12.7%";
            } else if ($maxTabOneLine == 7) { // 6 tabs
                $this->view->cat_lvl1_width = "15%";
            } else { // 5 tabs
                $this->view->cat_lvl1_width = "17%";
            }
        }

        // サイドメニュー設定
        $cssClass = App_SideMenuUtil::getCssClass($lvl0);
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson($cssClass);

        // 返却データ
        $data = array(
            'dataApp' => $obj->getAppData(),
            'dataCol' => $obj->getColData(),
                // 'dataMstr' => $obj->getMstrData(),
        );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;

        App_Smarty::pushCssFile(['app/juchu.itemex.css', 'app/juchu.item.dialog.css']);

        App_Smarty::pushJsFile(['app/sidemenu.js',
            'app/juchu.item.dialog.js',
            'app/juchu.mitsu.input.js',
            'app/juchu.itemex.js']);
    }

    /**
     * 一般化商品選択(商品選択,返礼品,料理)登録 Action
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    public function itemexsaveAction() {
        $req = $this->getRequest();

        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $lvl0 = $dataApp['lvl0'];
            $lvl1 = $dataApp['lvl1'];
            $lvl2 = $dataApp['lvl2'];
            $seko_no = $dataApp['seko_no'];
            // Msi_Sys_Utils::debug( '*** itemexsaveAction() lvl0=>' . $lvl0 . ' lvl1=>' . $lvl1 . ' lvl2=>' . $lvl2 );

            $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2), $seko_no);

            $obj->save();
        }
    }

    /**
     * お礼状 初期画面表示 アクション
     *
     * <AUTHOR> Sai
     * @since 2015/05/18
     * @version 2016/07/17 他の担当者が変更できない様に修正 Kayo
     */
    public function thanksexAction() {
        $thanks = new Juchu_JuchuThanksex();
        // 他の担当者が変更できない様に制御
        $seko_no = App_Utils::getSekoNoEasy('mitsu');   // 2016/07/17 ADD Kayo
        $db = Msi_Sys_DbManager::getMyDb();      // 2016/07/17 ADD Kayo
        App_ClsAuthority::CheckTanto($db, $this, $seko_no); // 2016/07/17 ADD Kayo
        // 初期データを取得する
        $jsonData = $thanks->getInitData();
        $this->view->thanks_json = $jsonData;
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson('thanks');
        App_Smarty::pushCssFile('app/sais.css', 'app/juchu.thanksex.css', 'app/lib.fileupload.css');
        App_Smarty::pushJsFile('app/lib.fileupload.js', 'app/sidemenu.js', 'app/juchu.thanksex.js');
    }

    /**
     * お礼状 saveアクション
     *
     * <AUTHOR> Sai
     * @since 2015/05/xx
     */
    public function thanksexsaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataOrei'));
            $thanks = new Juchu_JuchuThanksex($dataApp['seko_no']);
            $thanks->save();
        }
    }

    /**
     * 相談履歴取得アクション
     *
     * <AUTHOR> Sai
     * @since 2016/01/25
     */
    public function consultAction() {
        $custom = new Juchu_JuchuCustomerinfo();
        // 初期データを取得する
        $jsonData = $custom->getconsultData();
        $this->view->data_json = $jsonData;
        App_Smarty::pushCssFile([
            'app/sais.css',
            'app/gridtablestyle.css',
            'app/mstr.css',
            'app/juchu.consult.css',
            'app/lib.fileupload.css',
        ]);
        App_Smarty::pushJsFile(['app/lib.fileupload.js', 'app/juchu.consult.js']);
    }

    /**
     * 事前相談saveアクション
     *
     * <AUTHOR> Sai
     * @since 2016/02/02
     */
    public function consultsaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $custom = new Juchu_JuchuCustomerinfo();
            $custom->consultsave($req);
        }
    }

    /**
     * 施行コピーアクション
     *
     * <AUTHOR> Sai
     * @since 2016/02/09
     */
    public function sekocopyAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $custom = new Juchu_JuchuCustomerinfo();
            $custom->sekocopy($req);
        }
    }
    
    /**
     * 事前相談情報コピーアクション
     *
     * <AUTHOR> Tosaka
     * @since 2020/10/08
     */
    public function consultinfocopyAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $custom = new Juchu_JuchuCustomerinfo();
            $custom->consultcopy($req);
        }
    }
    
    /**
     * 搬送情報コピーアクション
     *
     * <AUTHOR> Tosaka
     * @since 2020/10/08
     */
    public function hansoinfocopyAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $custom = new Juchu_JuchuCustomerinfo();
            $custom->hansocopy($req);
        }
    }

    /**
     * 見積一括削除アクション
     *
     * <AUTHOR> Tosaka
     * @since 2017/2/17
     */
    public function estimatedeleteAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $estimate = new Juchu_JuchuEstimate();
            $estimate->mitsuAllDelete();
        }
    }

    /**
     * なまえ文deleteアクション
     *
     * <AUTHOR> kegai
     * @since 2017/10/05
     */
    public function namaebundeleteAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataApp'));
            $press = new Juchu_JuchuPhoto($dataApp['seko_no']);
            $press->deleteNamaebunTab();
        }
    }

    /**
     * 商品選択ダイアログaction
     *
     * <AUTHOR> Sai
     * @since 2017/10/30
     * @return array ダイアログデータ
     */
    public function itemdialogAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $dataItemDialog = Msi_Sys_Utils::json_decode($req->getPost('dataItemDialogJson'));
            $lvl0 = $dataApp['lvl0'];
            $lvl1 = $dataApp['lvl1'];
            $lvl2 = $dataApp['lvl2'];
            $seko_no = $dataApp['seko_no'];

            $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2), $seko_no);
            $dialogAppData = $obj->getItemDialogAppData($dataItemDialog);
            $dialogData = $obj->getItemDialogData($dataItemDialog);
            $db = Msi_Sys_DbManager::getMyDb();
            $niteiData = DataMapper_SekoNitei::find2($db, array('seko_no' => $seko_no));
            if (count($dialogData) > 0 && empty($dialogData['seko_no'])) {
                $dialogData['seko_no'] = $seko_no;
            }
            $data['dialogData'] = $dialogData;
            $data['kbns'] = $dialogAppData;
            $data['niteis'] = $niteiData;
            $data['status'] = 'OK';
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 商品選択ダイアログ保存
     *
     * <AUTHOR> Sai
     * @since 2017/10/30
     * @return 
     */
    public function savedialogitemAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $dataItemDialog = Msi_Sys_Utils::json_decode($req->getPost('dataItemDialogJson'));
            $dataDialoContent = Msi_Sys_Utils::json_decode($req->getPost('dataDialoContentJson'));
            $lvl0 = $dataApp['lvl0'];
            $lvl1 = $dataApp['lvl1'];
            $lvl2 = $dataApp['lvl2'];
            $seko_no = $dataApp['seko_no'];

            $obj = new Juchu_JuchuItemex(array('lvl0' => $lvl0, 'lvl1' => $lvl1, 'lvl2' => $lvl2), $seko_no);
            $obj->saveItemDialogData($dataItemDialog, $dataDialoContent);
            $data['status'] = 'OK';
            $data['msg'] = '保存しました';
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 見積式場・施行式場取得処理
     *
     * <AUTHOR> Tosaka
     * @since 2020/06/24
     * @return 
     */
    public function getcurdataAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = array();
            $dataApp['seko_no'] = $req->getPost('seko_no');
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $data = $customerinfo->getCurData($dataApp);
            $data['status'] = 'OK';
            Msi_Sys_Utils::outJson($data);
        }
    }
    
    /**
     * お客様情報初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/03
     */
    public function preconsultAction() {
//        App_Utils::setCustomerinfo($this);
        $sekoNo = '';
        $request = Msi_Sys_Utils::getRequestObject();
        $controllerName = $request->getControllerName();
        $customerinfo = new Juchu_JuchuCustomerinfo();
        // 初期データを取得する
        $jsonData = $customerinfo->getconsultData($controllerName);
        $this->view->customerinfo_json = $jsonData;
        $this->view->sidemenu_key = '';
        $this->view->s_file_memo_disp = 1; // みつわファイル添付タブの備考表示区分
        // 利用 CSS 設定
        App_Smarty::pushCssFile(['app/sais.css', 'app/juchu.preconsultinfo.css', 'app/juchu.customerinfo.report.css', 'app/lib.fileupload.css']);
        // 利用 JavaScript 設定        
        App_Smarty::pushJsFile(['app/juchu.customerinfo.fileup.js'
            , 'app/lib.fileupload.js'
            , 'app/juchu.customerinfo.seikyu.js'
            , 'app/juchu.preconsultinfo.js'
            , 'app/hakuexif/hakuexif.cifdlg.js'  // add for CIF search dialog
            , 'app/lib.bb.viewUtils.js']);
    }

}
