@charset "UTF-8";
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.small {
  font-size: 85%;
}

.small2 {
  font-size: 80%;
}

.small3 {
  font-size: 75%;
}

.smallest {
  font-size: 70%;
}

.select2-container .select2-choice {
  background-image: none;
}

.select2-drop-mask {
  background-image: none;
}

.select2-dropdown-open .select2-choice {
  background-image: none;
}

.select2-container-multi .select2-choices {
  background-image: none;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background-image: none;
}

.my-error {
  border: 1px solid red !important;
}

#order {
  position: fixed;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  top: 40px;
  left: 0;
  background-color: #D4E5F4;
  box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
  line-height: 1;
  z-index: 2;
}
#order a {
  text-decoration: none;
  color: inherit;
}
#order DELh2 {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 16px;
  color: #266CA5;
}
#order input {
  background-color: transparent;
}
#order input:disabled, #order input[readonly] {
  color: #545454;
  pointer: auto;
}
#order .page-title {
  margin-top: 5px;
  margin-bottom: 10px;
  float: left;
}
#order .page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #ff9900;
  font-size: 1.2em;
}
#order #searchbtnarea {
  width: 100%;
  height: 35px;
  white-space: nowrap;
  margin-top: 5px;
  margin-bottom: 10px;
  /** 検索ボタン */
  /** クリアボタン */
}
#order #searchbtnarea div {
  float: left;
}
#order #searchbtnarea input[type="button"] {
  float: right;
}
#order #searchbtnarea #btn_search {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #296FA7;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94B9D6;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 0 0 5px;
  background-image: url(../../../img/ico_search.png);
  background-repeat: no-repeat;
  background-position: 33px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_search:active {
  background-color: #369;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img/ico_search_2.png);
}
#order #searchbtnarea #btn_clear {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #296FA7;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94B9D6;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 5px 0 5px;
  background-image: url(../../../img/ico_search.png);
  background-repeat: no-repeat;
  background-position: 33px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_clear:active {
  background-color: #369;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img/ico_search_2.png);
}
#order fieldset.base_1 {
  border: 1px solid #88B1D1;
  background-color: white;
}
#order fieldset.base_2 {
  border: 1px solid #88B1D1;
  border-top: none;
  background-color: white;
  margin-bottom: 10px;
}
#order fieldset.base_3 {
  border: 1px solid #88B1D1;
  border-top: none;
  background-color: white;
  margin-bottom: 0px;
}
#order #s_uketuke_from,
#order #s_kuyo_ymd_from  {
  width: 7.5%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
  padding: 0;
}
#order #s_uketuke_to,
#order #s_kuyo_ymd_to {
  width: 7.5%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
  padding: 0;
}
#order #s_name_1 {
  width: 13%;
  border-bottom: none;
  border-top: none;
}
#order #s_name_2 {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_name_3 {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_name_4 {
  width: 11.5%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_kaijyosiki {
  width: 19%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_code_1 {
  width: 7.95%;
  border-bottom: none;
  border-top: none;
  background-color: #FFF;
}
#order #s_code_2 {
  width: 3%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_code_3 {
  border-bottom: none;
  border-top: none;
  width: 6%;
}
#order .s_apply-cls {
  width: 9%;
  float: left;
}
#order #s_staff_2 {
  width: 11%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_bumon {
  width: 13%;
  border-bottom: none;
  border-top: none;
}
#order .s_status_kbn-cls {
  width: 9%;
  float: left;
}

#order .dlg_common {
  width: 3%;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
#order .s_staff_2-ref
{
	width: 2%;
    border-right: 1px solid #CCC;
}
#order .s_kaijyosiki-ref
{
	width: 2%;
    border-right: 1px solid #CCC;
}
#order .lbl_sougi_bi{
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_name_1 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
}
#order .lbl_s_name_2 {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_name_3 {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_name_4 {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_kaijyosiki {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_code {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_apply {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_staff_2 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
  border-left: 1px solid #CCC;
}
#order .lbl_s_bumon {
  width: 5%;
  border-bottom: none;
  border-top: none;
  border-left: none;
}
#order #s_bumon {
  border-right: none;
}
#order .s_bumon-ref {
  border-right: none;
  width: 2%;
}
#order .lbl_s_status_kbn {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 11px;
}
#order .day-bar {
  width: 1%;
  text-align: center;
  padding-right: 0;
  padding-left: 0;
  border: none;
  color: #999;
}
#order .code-bar {
  width: 0.5%;
  text-align: center;
  padding-right: 0;
  padding-left: 0;
  border: none;
  color: #999;
}
#order .result-list {
  height: 85%;
  overflow: hidden;
}
#order .result-list .header {
  overflow-y: scroll;
}
#order .result-list .list {
  overflow-y: scroll;
  height: 80%;
  padding-bottom: 1px;
}
#order .result-list #divHeader {
  height: 100%;
  background-color: #D4E5F4 !important;
}
#order .header table, #order .list table {
  width: 100%;
  height: 30px !important;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background: none;
  background-color: #E8F3FB;
  color: #FFF;
  font-size: 13px;
}

#order .list table tr.result-list-sel {
    height: 40px !important;
}

#order .header table td, #order .list table td {
  border: 1px solid #88B1D1;
  width: 5%;
  height: 30px !important;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .header .sougi_bi, #order .list .sougi_bi {
  width: 20%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .hall, #order .list .hall {
  width: 20%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .bumon, #order .list .bumon {
  width: 10%;
}
#order .header .seko_tanto_nm
,#order .list .seko_tanto_nm 
{
    width: 10% !important;
}

#order .header .moshu_nm, #order .list .moshu_nm {
  width: 12%;
}

/*result-list*/
#order .header .row, #order .list .row 
{
  width: 5% !important;
  border-left: 1px solid #88B1D1;
  border-top: 1px solid #88B1D1;
}
#order .header .uketuke_no
, #order .list .uketuke_no 
{
    width: 6% !important;
    padding: 0;
}
#order .header .uketuke_msi_no
, #order .list .uketuke_msi_no 
{
    width: 3% !important;
    padding: 0;
}
#order .header .souke_nm
, #order .list .souke_nm 
{
    width: 8% !important;
}
#order .header .k_nm
, #order .list .k_nm 
{
    width: 10% !important;
}
#order .header .m_nm
, #order .list .m_nm 
, #order .header .iraisya_nm 
, #order .list .iraisya_nm 
{
    width: 10% !important;
}
#order .header .uketuke_ymd 
,#order .list .uketuke_ymd
{
    width: 8% !important;
}
#order .header .sougei_ymd
,#order .list .sougei_ymd
{
    width: 8% !important;
}
#order .header .kuyo_ymd
,#order .list .kuyo_ymd
{
    width: 8% !important;
}
#order .header .hikiwatashi_kbn_nm
,#order .list .hikiwatashi_kbn_nm 
{
    width: 5% !important;
}
#order .header .kaisyu_kakinin_kbn_nm
,#order .list .kaisyu_kakinin_kbn_nm 
{
    width: 5% !important;
}
#order .header .nyukin_kbn_nm
,#order .list .nyukin_kbn_nm 
{
    width: 5% !important;
}
#order .header .h_biko
,#order .list .biko	{
    width: 20% !important;
}
#order .list .biko {
    text-align: left;
}
#order .header .h_biko {
    text-align: center;
}
#order .list table {
  background-color: #FFF;
}
#order .list table tr.even {
  background-color: #F5F9FD;
}
#order .header .sikijyo
, #order .list .sikijyo 
{
    width: 10% !important;
}

#order .list table .hall
, #order .list table .uke_tanto
, #order .list table .kojin_nm
, #order .list table .bumon
, #order .list table .seko_tanto
, #order .list table .moshu_nm
, #order .list table .souke_nm 
, #order .list table .k_nm 
, #order .list table .m_nm 
, #order .list table .iraisya_nm
{
  text-align: center;
}
#order .list table tr.row-selected {
  background-color: #6CACD7;
}
#order .list table tr.row-selected td {
  color: white;
  border-color: white;
}
#order .list table tr.row-selected td.row {
  border-left: 1px solid #88B1D1;
}
#order .list table tr.row-selected td.kojin_nm, #order .list table tr.row-selected td.moshu_nm {
  border-right: 1px solid #88B1D1;
}
#order .list table tr.row-selected td.row {
  background-image: url(../../../img/check_1.png);
  background-repeat: no-repeat;
  background-position: center top;
}
#order table td.msg {
  border: 1px solid #88B1D1;
}
#order table td.my-hover {
  cursor: pointer;
  background-color: peachpuff !important;
}
#order .select2-choice, #order .select-container {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
  border-left: none;
}
#order .select2-choices {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
}
#order .select2-arrow {
  border-radius: 0px !important;
}
#order .select2-container-multi .select2-choices 
,.result-list-sel .souke_nm
,.result-list-sel .k_nm
,.result-list-sel .m_nm
,.result-list-sel .row
,.result-list-sel .souke_nm
,.result-list-sel .iraisya_nm
{
  height: 98% !important;
}
#order fieldset.base_1 .select2-choice, #order fieldset.base_1 .select-container, #order fieldset.base_2 .select2-choice, #order fieldset.base_2 .select-container {
  height: 30px !important;
  border-top: none;
  border-bottom: none;
  border-right: none;
}
#order .my-bold {
  font-weight: bold;
}
#order .my-error {
  border: 1px solid red !important;
}
#order input[type="button"]:disabled {
  opacity: .5;
}
#order input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94B9D6;
  box-shadow: 1px 1px 0 #FFF;
  color: #296FA7;
}
#order .dlg_from{
    border-bottom: none;
    border-right: none;
    width: 2%;
}
#order .dlg_to{
    /*border-left: 1px solid #ccc;*/
    border-right: 1px solid #ccc;
    border-bottom: none;
    width: 2%;
    padding-right: 2%;
}

#order #searchbtnarea #btn_clear {
    background-position: 27px 9px;
}

#order .header table, #order .list table {
    width: 120%;
    table-layout: fixed;
    min-width: 100%;
}

/*ラベル*/
#order .lbl_sougi_bi
,#order .lbl_uketsuke_bi
,#order .lbl_s_code_1
,#order .lbl_kuyo_ymd {
    width: 7%;
    border-left: none;
    border-bottom: none;
}

#order .lbl_s_bumon {
    width: 7%;
}

#order .lbl_s_name_3 {
    width: 7%;
}

#order .lbl_s_name_2 {
    width: 7%;
}

#order .lbl_s_staff_2 {
    width: 7%;
}
#order .lbl_s_moushi {
    width: 7%;
    border-bottom: 0px;
}
#order .lbl_dummy{
    width: 6%;
    border-bottom: none;
    border-top: none;
    border-left: 1px solid #CCC;
    border-right: 0px solid #88B1D1;
}

#order .s_sort-cls {
    width: 6%;
    float: left;
    background-color: white;
    font-size: 13px;
    border-bottom: none;
    border-right: none;
}

/*スクロール*/
#divHeader {
    overflow-x: auto;
    width: 100%;
}

#divBottom {
    overflow-x: hidden;
    width: 100%;
}
#order .buttons #btn_juchu
,#order .buttons #btn_zaiko
{
    width: 165px;
    text-indent: 0px;
}

#order .buttons #btn_print{
    width: 195px;
}

#order #btn_print_misi{
    width: 185px !important;
}
#order .base_2 input[type="checkbox"] {
    display: none;
}

#order  #hakko_ymd
,#order #hakko_label{
    display: inline-block;
    float: none;
    width: 170px;
    height: 32px;
    position: relative;
}      
#order #hakko_label {
    margin: 5px 5px 0 -5px;
    padding-top: 10px;
    vertical-align: middle;
    left:10px;
}
#order #hakko_ymd {
    margin-left: 0px;
    text-align: center;
    padding-top: 7px;
    text-indent: 0px;
    border-right: 1px solid #CCC;
    border-bottom: 1px solid #CCC;
    box-shadow: none;
    background-color: #FFF;
    border-radius: 0px;
    color: black;
}
#order .lbl_select_kbn{
	width: 9%;
	line-height: 1;
	margin: 0;
	border-right: none;
	background-color: #FAFAFA;
	background-image: url(../../../img-m1/check_2.png);
	background-repeat: no-repeat;
	background-position: 10% center;
	background-size: 16px 12px;
	border: none;
	border-bottom: 1px solid #CCC;
	border-radius: 0;
	text-align: center;
	color: #999;
}
#order .lbl_select_kbn.ui-state-active{
	display: block;
	background-color: #6CACD7;
	background-image: url(../../../img/check_1.png);
	text-shadow: -1px -1px 0 #69C;
	color: #FFF;   
}
/* ヘッダの area, hall 部門, 会社を非表示にする */
.account .area, .account #s2id_hall_cd, .account #s2id_hdr_kaisya_cd {
    visibility: hidden;
}
#order label{
    padding-right: 0;
}
#order .result-list .header {
    font-weight: bold;
    background-color: #E8F3FB;
}
#order .select2-container-multi
,#order .select2-choices
{
    width: 100%;
	height: 100%;
	border: none;
    background-color: transparent;
}
.select2-container-multi .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 18px;
	margin: 1px 0 1px 1px;
}
#order #btn_juchu {
    background-image: url(../../../img/ico_save.png);
    background-repeat: no-repeat;
    background-position: 31px 8px;
    background-size: 14px 13px;
}
#order #btn_save, #detail #btn_save {
    background-image: url(../../../img/ico_save.png);
    background-repeat: no-repeat;
    background-position: 12px 8px;
    background-size: 14px 13px;
}