<?php
  /**
   * Logic_JuchuDenpyoTanpin
   *
   * 受注伝票（単品） 処理
   *
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Arai
   * @since      2020/11/05 nise02を元にコピー
   * @filesource 
   */

  /**
   * 受注伝票（単品） 処理
   * 
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Mihara
   * @since      2014/03/31
   */
class Logic_JuchuDenpyoTanpin
{
    /** 受注伝票（単品） データ区分
     * 3：単品 
     */
    static protected $_data_kbn = 3;

    /** 受注伝票（単品） 施行番号 */
    static protected $_seko_no = '**********';

    /** 受注伝票（単品） 施行番号（枝番） */
    static protected $_seko_no_sub = '00';

    /** 受注伝票（単品） 施行プランコード */
    static protected $_seko_plan_cd = '00000';


    /**
     * 受注伝票（単品） の更新・登録
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @version 2014/10/03 外税、内税額をクリアするように修正 Kayo
	 * @version 2015/01/18 施行の請求先も検索、更新できるように修正 Kayo
	 * @version 2015/01/18 請求書送付先を更新できるように修正 Kayo
     * @param      array  $data データ. 明細は $data['_dtl_']に含まれる
     * @return     array        登録データ
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function upsert($data)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        // Msi_Sys_Utils::debug( '** upsert() data=>' . Msi_Sys_Utils::dump($data) );

        //
        // 入力値チェック
        //

        // 会社コード   2016/12/25 ADD Kayo
        $kaisya_cd = $data['kaisya_cd'];

        // 受注日
        $juchu_ymd = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['juchu_ymd']), 'DATE2' );
        if ( strlen($juchu_ymd) <= 0 ) {
            throw new Msi_Sys_Exception_InputException( "受注日エラー" );
        }

        // 売上計上日
        $keijo_ymd = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['keijo_ymd']), 'DATE2' );
        if ( strlen($keijo_ymd) <= 0 ) {
            $keijo_ymd = $juchu_ymd;
        }

        // 伝票番号
        $denpyo_no = Msi_Sys_Utils::emptyToNull($data['denpyo_no']);

        // 既存データ検索
        if ( strlen($denpyo_no) > 0 ) {
            // Msi_Sys_Utils::debug( '** denpyo_no cur=>' . $denpyo_no );
            $dataCur = static::_findHdr($db, $denpyo_no);
            if ( $dataCur === null ) {
                throw new Msi_Sys_Exception_InputException( "伝票No ($denpyo_no) のデータが存在しません" );
            }
            $isAddCtxt = false; // 新規フラグ
        } else {
            $denpyo_no = App_ClsGetCodeNo::GetCodeNo($db, 'juchu_denpyo', 'denpyo_no', $juchu_ymd);
            $isAddCtxt = true; // 新規フラグ

            // Msi_Sys_Utils::debug( '** denpyo_no(new)=>' . $denpyo_no );
        }

        // データ区分
        $data_kbn = static::$_data_kbn;

        // 施行番号
        $seko_no = static::$_seko_no;

        // 施行番号（枝番）
        $seko_no_sub = static::$_seko_no_sub;

        // 売上部門コード
        $bumon_cd = $data['bumon_cd'];
        if (strlen($bumon_cd) > 0)  {
           if ( ! $db->isRefDataExists('bumon_mst', array('bumon_cd'=>$bumon_cd)) ) {
                throw new Msi_Sys_Exception_InputException( "部門 ($bumon_cd) のデータが存在しません" );
           }    
        } else {    // 2017/03/08 ADD Kayo
            throw new Msi_Sys_Exception_InputException( "部門が選択されていません。部門を選択して下さい。" );
        }    

        // 担当者コード
        $tanto_cd = $data['tanto_cd'];
        if ( ! $db->isRefDataExists('tanto_mst', array('tanto_cd'=>$tanto_cd)) ) {
            throw new Msi_Sys_Exception_InputException( "担当者 ($tanto_cd) のデータが存在しません" );
        }

        // 施行プランコード
        $seko_plan_cd = static::$_seko_plan_cd;

        // 納品書発行
        $nohinsho_pirint_kbn = $data['nohinsho_pirint_kbn'];
        if ( ! $db->isRefDataExists('code_nm_mst', array('kbn_value_cd'=>$nohinsho_pirint_kbn, 'code_kbn'=>'0110')) ) {
            throw new Msi_Sys_Exception_InputException( "納品書発行 ($nohinsho_pirint_kbn) のデータが存在しません" );
        }
        // 回収区分
        $kaishu_kbn = $data['kaishu_kbn'];
        if ( ! $db->isRefDataExists('code_nm_mst', array('kbn_value_cd'=>$kaishu_kbn, 'code_kbn'=>'0430')) ) {
            throw new Msi_Sys_Exception_InputException( "回収区分 ($kaishu_kbn) のデータが存在しません" );
        }

        // 受注先
        $juchusaki_kbn = $data['juchusaki_kbn'];
        if ( ! $db->isRefDataExists('code_nm_mst', array('kbn_value_cd'=>$juchusaki_kbn, 'code_kbn'=>'1140')) ) {
            throw new Msi_Sys_Exception_InputException( "受注先区分 ($juchusaki_kbn) のデータが存在しません" );
        }

        // 支払方法
        $pay_method_cd = $data['pay_method_cd'];
        if ( ! $db->isRefDataExists('code_nm_mst', array('kbn_value_cd'=>$pay_method_cd, 'code_kbn'=>'1130')) ) {
            throw new Msi_Sys_Exception_InputException( "受注先区分 ($pay_method_cd) のデータが存在しません" );
        }

        // 回収予定日
        $kaishu_ymd = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['kaishu_ymd']), 'DATE2' );
        if ( $kaishu_ymd === false ) {
            throw new Msi_Sys_Exception_InputException( "回収予定日が不正です" );
        }

        // 納入予定日
        $nonyu_dt = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['nonyu_dt']), 'DATE2' );
        if ( $nonyu_dt === false ) {
            throw new Msi_Sys_Exception_InputException( "納入予定日が不正です" );
        }
        // 振込先
        $v_free5 =  Msi_Sys_Utils::emptyToNull($data['v_free5']);
        // 請求先
        // $sekyu_cd = Msi_Sys_Utils::emptyToNull( $data['sekyu_cd'] );
        $sekyuData = Msi_Sys_Utils::remapArrayFlat( $data, <<< END_OF_TXT
sekyu_cd  sekyu_nm  sekyu_knm  sekyu_yubin_no sekyu_addr1 
sekyu_addr2  sekyu_tel  sekyu_fax  sekyu_biko1  sekyu_biko2 seko_no sekyu_soufu_nm 
END_OF_TXT
                                                    , array( 'sekyu_yubin_no' => 'yubin_no',
                                                             'sekyu_addr1'    => 'addr1',
                                                             'sekyu_addr2'    => 'addr2',
                                                             'sekyu_tel'      => 'tel',
                                                             'sekyu_fax'      => 'fax',
                                                             'sekyu_biko1'    => 'biko1',
                                                             'sekyu_biko2'    => 'biko2', )
                                                    );
		if (strlen($sekyuData['seko_no']) <= 0) {
		    $sekyuData['seko_no'] = static::$_seko_no;
		}
        $sekyuRec = DataMapper_SekyuSakiInfo::upsert( $db, $sekyuData );

        $sekyu_cd       = $sekyuRec['sekyu_cd'];
        $sekyu_nm       = $sekyuRec['sekyu_nm'];
        $sekyu_knm      = $sekyuRec['sekyu_knm'];
        $sekyu_soufu_nm = null;
        $sekyu_yubin_no = $sekyuRec['yubin_no'];
        $sekyu_addr1    = $sekyuRec['addr1'];
        $sekyu_addr2    = $sekyuRec['addr2'];
        $sekyu_tel      = $sekyuRec['tel'];
        $sekyu_fax      = $sekyuRec['fax'];
		$sekyu_soufu_nm = $sekyuRec['sekyu_soufu_nm']; // 2015/01/18 AD Kayo

        // 納品場所区分 code_nm_mst.code_kbn:'0500'
        // $delivery_kbn = null;  // 9:その他

        // 納入先
        // $nonyu_cd = Msi_Sys_Utils::emptyToNull( $data['nonyu_cd'] );
        if ( strlen($data['nonyu_nm']) > 0 ) {
            $nonyuData = Msi_Sys_Utils::remapArrayFlat( $data, <<< END_OF_TXT
nonyu_cd  nonyu_nm  nonyu_knm  nonyu_yubin_no nonyu_addr1 
nonyu_addr2  nonyu_tel  nonyu_fax  nonyu_biko1  nonyu_biko2
nonyu_delivery_kbn
END_OF_TXT
                                                        , array( 'nonyu_yubin_no' => 'yubin_no',
                                                                 'nonyu_addr1'    => 'addr1',
                                                                 'nonyu_addr2'    => 'addr2',
                                                                 'nonyu_tel'      => 'tel',
                                                                 'nonyu_fax'      => 'fax',
                                                                 'nonyu_biko1'    => 'biko1',
                                                                 'nonyu_biko2'    => 'biko2',
                                                                 'nonyu_delivery_kbn' => 'delivery_kbn')
                                                        );
            $nonyuData['seko_no'] = static::$_seko_no;
            $nonyuData['delivery_code_kbn'] = '0500'; // 納品場所区分(0500)
            // $nonyuData['delivery_cd'] = 

            $nonyuRec = DataMapper_NonyuInfo::upsert( $db, $nonyuData );

            $nonyu_cd       = $nonyuRec['nonyu_cd'];
            $nonyu_nm       = $nonyuRec['nonyu_nm'];
            $nonyu_knm      = $nonyuRec['nonyu_knm'];
            $nonyu_yubin_no = $nonyuRec['yubin_no'];
            $nonyu_addr1    = $nonyuRec['addr1'];
            $nonyu_addr2    = $nonyuRec['addr2'];
            $nonyu_tel      = $nonyuRec['tel'];
            $nonyu_fax      = $nonyuRec['fax'];
            $delivery_kbn   = $nonyuRec['delivery_kbn'];
        }

        // 税区分  0(非課税), 1(内税), 2(外税)
        $tax_kbn = $data['tax_kbn'];
        $taxRec = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=>'0150', 'kbn_value_cd'=>$tax_kbn));
        if ( $taxRec === null ) {
            throw new Msi_Sys_Exception_InputException( "税区分 ($tax_kbn) のデータが存在しません" );
        }
        $tax_code_kbn = $taxRec['code_kbn'];
        $tax_cd = $taxRec['kbn_value_cd'];

        // 消費税コード
        $zei_cd = $data['zei_cd'];
        $zeiRec = DataMapper_ZeiMst::findOne($db, array('zei_cd'=>$zei_cd), false);
        if ( $zeiRec === null ) {
            throw new Msi_Sys_Exception_InputException( "消費税コード ($zei_cd) のデータが存在しません" );
        }
        $zei_rtu = $zeiRec['zei_rtu'];

        // 伝票備考１
        $denpyo_biko1 = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['denpyo_biko1']), '/^.{0,30}/u' );
        if ( $denpyo_biko1 === false ) {
            throw new Msi_Sys_Exception_InputException( "伝票備考１が不正です" );
        }

        // 伝票備考２
        $denpyo_biko2 = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($data['denpyo_biko2']), '/^.{0,30}$/u' );
        if ( $denpyo_biko2 === false ) {
            throw new Msi_Sys_Exception_InputException( "伝票備考２が不正です" );
        }

        // 以下の項目は、明細登録後、別途 update する  ==> (#1)金額設定処理
        $juchu_prc_sum  = 0;         // 受注金額合計
        $genka_prc_sum  = 0;         // 原価金額合計
        $juchu_hepn_sum = 0;         // 受注返品合計
        $juchu_nebk_sum = 0;         // 受注値引合計
        $hoshi_prc_sum  = 0;         // 奉仕料合計
        $szei_katax_taisho_prc  = 0; // 外税課税対象額
        $uzei_katax_taisho_prc  = 0; // 内税課税対象額
        $hitax_katax_taisho_prc = 0; // 非税課税対象額
        $out_zei_prc = 0;            // 外税消費税額
        $in_zei_prc  = 0;            // 内税消費税額
        $arari_prc   = 0;            // 粗利益額
        $pBaika = 0;                 // 売価
        // $pKazeiKbn = null;  =>$tax_kbn      // 課税区分(0:非課税 1:内税 2:外税）
        $pZeiNo = null;              // OUT 消費税コード
        $pZeiRt = null;              // OUT 消費税率
        $pShouhiZeiPrc = null;       // OUT 消費税額
        $pRuikeiBaikaPrc = 0;        // REF 累計売価額
        $pRuikeiShohiZeiPrcSoto = 0; // 累計外税消費税額
        $pRuikeiShohiZeiPrcUchi = 0; // 累計内税消費税額
        $pRuikeiShohiZeiPrc = 0;     // REF 累計消費税額
        $pChoseiPrc = 0;             // OUT 調整額

        // 明細登録用データ
        $dtlIns = array();
        $dtlUpd = array();
        $dtlDel = array();
        $aUpdMsiNo = array(); // update される msi_no を保存しておく

        // 受注伝票No 最大値
        $maxMsiNo = static::_getMaxdenpyoMsiNo($db, $denpyo_no);

        $dtlRecFirst = null; // 1行目

        // 新規、更新明細
        foreach ( $data['_dtl_'] as $dtl ) {
            $dtlRec = array();

            // 受注伝票No
            $dtlRec['denpyo_no'] = $denpyo_no;

            // 受注明細No
            $msi_no = Msi_Sys_Utils::emptyToNull($dtl['msi_no']);
            if ( $msi_no === null ) {   // 明細行は追加
                ++$maxMsiNo;
                $msi_no = $maxMsiNo;
                $isDtlUpdCtxt = false;
            } else {                    // 明細行は更新
                $dtlCur = static::_findDtl($db, $denpyo_no, $msi_no);
                if ( $dtlCur === null ) {
                    throw new Msi_Sys_Exception_InputException( "明細No ($msi_no) のデータが存在しません" );
                }
                $isDtlUpdCtxt = true;
            }
            $dtlRec['msi_no'] = $msi_no;

            // 表示順
            $disp_no = $dtl['line_no'];
            $dtlRec['disp_no'] = $disp_no;

            // データ種別
            // 1：葬送儀礼 2：返礼品　3：料理 4：壇払い 5：別途費用 6：立替金 7：値引き 8：別注品 9:単品
            $dtlRec['data_sbt'] = 9; // null;

            // 売上部門コード
            $dtlRec['bumon_cd'] = $bumon_cd;

            // 施行番号
            $dtlRec['seko_no'] = $seko_no;

            // 施行番号（枝番）
            $dtlRec['seko_no_sub'] = $seko_no_sub;

            // 受注日
            $dtlRec['juchu_ymd'] = $juchu_ymd;

            // 伝票区分  1：受注、2：返品、3:値引き 5：破損返品、90：記事
            $denpyo_kbn = $dtl['denku'];
            if ( ! $db->isRefDataExists('code_nm_mst', array('kbn_value_cd'=>$denpyo_kbn, 'code_kbn'=>'0420')) ) {
                $errMsg = sprintf( "%d行目: 伝票区分(%s)のデータが存在しません", $disp_no, $denpyo_kbn );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['denpyo_kbn'] = $denpyo_kbn;

            // 使用目的区分
            // （2：返礼品）1：会葬 2：あつらい 3:忌中 ４：遺物 
            // （3：料理）　１0：通夜用　20：当日昼食 30：壇払い
            $dtlRec['mokuteki_kbn'] = null;

            // 会社コード   2016/12/25 ADD Kayo
            $dtlRec['kaisya_cd'] = $data['kaisya_cd'];
            // 商品部門コード   2016/12/25 ADD Kayo
            $dtlRec['shohin_bumon_cd'] = '00000';

            // 商品
            $shohin_cd = $dtl['shohin_cd'];
            $shohinRec = DataMapper_Shohin::findOne( $db, array('shohin_cd'=>$shohin_cd) );
            if ( $shohinRec === null ) {
                $errMsg = sprintf( "%d行目: 商品(%s)のデータが存在しません", $disp_no, $shohin_cd );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }

            // 商品コード, 大分類コード, 中分類コード, 商品区分
            $dtlRec = array_merge( $dtlRec,
                                   Msi_Sys_Utils::remapArrayFlat($dtl,
                                                                 'shohin_cd  dai_bunrui_cd  chu_bunrui_cd  shohin_kbn') );

            // 商品名
            $shohin_nm = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['shohin_nm']), '/^.{1,40}$/u' );
            if ( $shohin_nm === null || $shohin_nm === false ) {
                $errMsg = sprintf( "%d行目: 商品名(%s)が不正です", $disp_no, $shohin_nm );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['shohin_nm'] = $shohin_nm;

            // 商品摘要名
            $shohin_tkiyo_nm = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['shohin_tekiyo']), '/^.{0,40}$/u' );
            if ( $shohin_tkiyo_nm === false ) {
                $errMsg = sprintf( "%d行目: 商品摘要名(%s)が不正です", $disp_no, $shohin_tkiyo_nm );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['shohin_tkiyo_nm'] = $shohin_tkiyo_nm;

            // 消費税区分
            // $dtlRec['zei_kbn'] = $tax_kbn; // keigen old
            $zei_kbn = +$dtl['zei_kbn'];
            $wkRec = DataMapper_CodeNmMstExEasy::codeKbnRec('0150', $zei_kbn, null, $db);
            if ( $wkRec === null ) {
                throw new Msi_Sys_Exception_InputException( "税区分 ($zei_kbn) のデータが存在しません" );
            }
            $dtlRec['zei_kbn'] = $zei_kbn;

            // 消費税コード
            $zei_cd00 = +$dtl['zei_cd'];
            $wkRec = DataMapper_ZeiMstEasy::getRec($zei_cd00, null, $db);
            if ( $wkRec === null ) {
                throw new Msi_Sys_Exception_InputException( "消費税コード ($zei_cd00) のデータが存在しません" );
            }
            $dtlRec['zei_cd'] = $zei_cd00;

            // 軽減税率区分
            $reduced_tax_rate = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['reduced_tax_rate']), '/^\d+$/' );
            if ( $reduced_tax_rate === null ) {
                $reduced_tax_rate = 1;
            }
            list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate00) = DataMapper_ZeiMstEasy::getRtuAndEtc($dtlRec['zei_cd']);
            if ( $reduced_tax_rate == 2 && $reduced_tax_rate00 != $reduced_tax_rate ) {
                $msg = sprintf("(66fbf2e8)軽減税率区分が不正です(zei_cd=%s, reduced_tax_rate=%s",
                               $dtlRec['zei_cd'], $reduced_tax_rate );
                throw new Msi_Sys_Exception_InputException( $msg );
            }
            $dtlRec['reduced_tax_rate'] = $reduced_tax_rate;

            // 数量
            $juchu_suryo = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['suryo']), 'INT' );
            if ( $juchu_suryo === null || $juchu_suryo === false ) {
                $errMsg = sprintf( "%d行目: 数量(%s)が不正です", $disp_no, $juchu_suryo );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            // if ( $denpyo_kbn == '2' ) { // 伝票区分 2:返品 の場合、符号をひっくり返す
            //    if ( $juchu_suryo > 0 ) {
            //        $juchu_suryo = -1 * $juchu_suryo;
            //    }
            // }
            $dtlRec['juchu_suryo'] = $juchu_suryo;

            // 単位コード
            $tani_cd = $dtl['tani_cd'];
            if ( ! $db->isRefDataExists('tani_mst', array('tani_cd'=>$tani_cd)) ) {
                $errMsg = sprintf( "%d行目: 単位コード(%s)が不正です", $disp_no, $tani_cd );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['tani_cd'] = $tani_cd;

            // 単価
            $juchu_tnk = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['juchu_tanka']), 'INT' );
            if ( $juchu_tnk === null || $juchu_tnk === false ) {
                $errMsg = sprintf( "%d行目: 単価(%s)が不正です", $disp_no, $juchu_tnk );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['juchu_tnk'] = $juchu_tnk;

            // cf. Logic_JuchuBechuDenpyo
            // 値引額
            $tgt_nebiki = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['nebiki_gaku']), 'INT' );
            if ( strlen($tgt_nebiki) < 1 ) {
                $tgt_nebiki = 0;
            }
            $nebiki_prc = $dtlRec['nebiki_prc'] = $tgt_nebiki;

            // 受注金額
            $tgt_prc = $juchu_tnk * $juchu_suryo;  // 値引調整はここでは反映されない. 返品額(明細)もここに設定
            $juchu_prc = $dtlRec['juchu_prc'] = $tgt_prc;

            // 原価単価
            $gen_tnk = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['genka_tanka']), 'INT' );
            if ( $gen_tnk === false ) {
                $errMsg = sprintf( "%d行目: 原価単価(%s)が不正です", $disp_no, $gen_tnk );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            if ( $gen_tnk === null ) {
                $gen_tnk = 0;
            }
            $dtlRec['gen_tnk'] = $gen_tnk;

            // 原価金額
            $tgt_gen = $gen_tnk * $juchu_suryo;
            $gen_gaku = $dtlRec['gen_gaku'] = $tgt_gen;

            // 粗利益額
            $arari_gaku = $juchu_prc + $nebiki_prc - $gen_gaku;
            $dtlRec['arari_gaku'] = $arari_gaku;

            // 倉庫コード
            $soko_cd = Msi_Sys_Utils::emptyToNull($dtl['soko_cd']);
            $dtlRec['soko_cd'] = $soko_cd;
        
            // 明細備考１
            $msi_biko1 = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['msi_biko1']), '/^.{0,30}/u' );
            if ( $msi_biko1 === false ) {
                $errMsg = sprintf( "%d行目: 明細備考１(%s)が不正です", $disp_no, $msi_biko1 );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['msi_biko1'] = $msi_biko1;

            // 明細備考２
            $msi_biko2 = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dtl['msi_biko2']), '/^.{0,30}/u' );
            if ( $msi_biko2 === false ) {
                $errMsg = sprintf( "%d行目: 明細備考２(%s)が不正です", $disp_no, $msi_biko2 );
                throw new Msi_Sys_Exception_InputException( $errMsg );
            }
            $dtlRec['msi_biko2'] = $msi_biko2;


            // 消費税額
            // 軽減税率対応 keigen  cf. Juchu_JuchuAbstract::setZeiInfo()
            // 後ほど伝票ベースで計算しなおし、再設定される
            $pBaika = $tgt_prc + $tgt_nebiki;
            $zei = App_ClsTaxLib::CalcTax($pBaika, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            if ($zei_kbn == 1) {
                $dtlRec["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
                $dtlRec['out_zei_prc'] = 0;    // 行外税消費税額
                $szei_katax_taisho_prc += $pBaika;
                $pRuikeiShohiZeiPrcUchi += $zei["ZeiPrc"];
            } else if ($zei_kbn == 2) {
                $dtlRec['in_zei_prc'] = 0;     // 行内税消費税額
                $dtlRec["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
                $uzei_katax_taisho_prc += $pBaika;
                $pRuikeiShohiZeiPrcSoto += $zei["ZeiPrc"];
            } else {
                $dtlRec['in_zei_prc'] = 0;
                $dtlRec["out_zei_prc"] = 0;
                $hitax_katax_taisho_prc += $pBaika;
            }

            /* OLD  軽減税率対応
            // 消費税額
            // list($dtlRec['out_zei_prc'], $dtlRec['in_zei_prc']) = static::_myCalcTax($tgt_prc + $tgt_nebiki, $tax_kbn, $zei_rtu);
            $pBaika = $tgt_prc + $tgt_nebiki;
            if ( $dtlRec['zei_kbn'] == 1 ) { // 1(内税)
                if ( App_ClsTaxLib::GetTaxMsiInfoUchiZei( $db, $juchu_ymd, $pBaika, $dtlRec['zei_kbn'],
                                                   $pZeiNo, $pZeiRt, $pShouhiZeiPrc,
                                                   $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrc ) !== true ) {
                    $errMsg = sprintf( "%d行目: 消費税計算エラー", $disp_no );
                    throw new Msi_Sys_Exception_InputException( $errMsg );
                }
                $dtlRec['out_zei_prc'] = 0;
                $dtlRec['in_zei_prc'] = $pShouhiZeiPrc;
                $uzei_katax_taisho_prc += $pBaika;
                // $in_zei_prc += $pShouhiZeiPrc;
            } else if ( $dtlRec['zei_kbn'] == 2 ) { // 2(外税)
                if ( App_ClsTaxLib::GetTaxMsiInfo( $db, $juchu_ymd, $pBaika, $dtlRec['zei_kbn'],
                                                   $pZeiNo, $pZeiRt, $pShouhiZeiPrc,
                                                   $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrc ) !== true ) {
                    $errMsg = sprintf( "%d行目: 消費税計算エラー", $disp_no );
                    throw new Msi_Sys_Exception_InputException( $errMsg );
                }
                $dtlRec['out_zei_prc'] = $pShouhiZeiPrc;
                $dtlRec['in_zei_prc'] = 0;
                $szei_katax_taisho_prc += $pBaika;
                // $out_zei_prc += $pShouhiZeiPrc;
            } else {
                if ( App_ClsTaxLib::GetTaxMsiInfo( $db, $juchu_ymd, $pBaika, $dtlRec['zei_kbn'],
                                                   $pZeiNo, $pZeiRt, $pShouhiZeiPrc,
                                                   $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrc ) !== true ) {
                    $errMsg = sprintf( "%d行目: 消費税計算エラー", $disp_no );
                    throw new Msi_Sys_Exception_InputException( $errMsg );
                }
                $hitax_katax_taisho_prc += $pBaika;
            }
            */

            if ( $denpyo_kbn == 1 ) { // 1:受注
                $juchu_prc_sum += $dtlRec['juchu_prc'];
            } else if ( $denpyo_kbn == 2 ) { // 2:返品
                $juchu_hepn_sum += $dtlRec['juchu_prc'];
            }
            $genka_prc_sum += $dtlRec['gen_gaku'];
            $juchu_nebk_sum += $dtlRec['nebiki_prc'];
            $arari_prc += $dtlRec['arari_gaku'];

            // 更新か新規かで分類する
            if ( $isDtlUpdCtxt ) {
                $dtlUpd[] = $dtlRec;
                $aUpdMsiNo[] = $msi_no;
                if ( $dtlRecFirst === null ) $dtlRecFirst = &$dtlUpd[0];
            } else {
                $dtlIns[] = $dtlRec;
                if ( $dtlRecFirst === null ) $dtlRecFirst = &$dtlIns[0];
            }

        } // foreach _dtl_

        // 消費税総額設定
        if ( $tax_kbn == 1 ) { //  1(内税)
            $in_zei_prc = $pRuikeiShohiZeiPrcUchi;
            // $in_zei_prc = $pRuikeiShohiZeiPrc + $pChoseiPrc;
        } else if ( $tax_kbn == 2 ) { //   2(外税)
            $out_zei_prc = $pRuikeiShohiZeiPrcSoto;
            // $out_zei_prc = $pRuikeiShohiZeiPrc + $pChoseiPrc;
        }

        /* // 消費税調整. １行目のレコードで調整する */
        /* if ( $pChoseiPrc ) { */
        /*     if ( $tax_kbn == 1 ) { // 1(内税) */
        /*         $dtlRecFirst['in_zei_prc'] += $pChoseiPrc; */
        /*     } else if ( $tax_kbn == 2 ) { // 2(外税) */
        /*         $dtlRecFirst['out_zei_prc'] += $pChoseiPrc; */
        /*     } */
        /* } */

        // 削除対象明細を調べる
        foreach ( static::_searchDtl($db, $denpyo_no) as $dtl ) {
            $_msi_no = $dtl['msi_no'];
            if ( in_array($_msi_no, $aUpdMsiNo) ) {
                continue;
            }
            // update 対象に入っていない場合は削除対象
            $dtlRec = array();
            $dtlRec['msi_no'] = $_msi_no;

            $dtlRec['denpyo_no'] = $denpyo_no;
            $dtlRec['seko_no'] = $seko_no;
            $dtlRec['delete_flg'] = 1;

            $dtlDel[] = $dtlRec;
        } // foreach 削除対象

        // 伝票登録用データ
        $hdrData = array();
        foreach ( Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
denpyo_no    juchu_ymd   data_kbn       seko_no         seko_no_sub kaisya_cd
bumon_cd     tanto_cd    seko_plan_cd   kaishu_kbn      kaishu_ymd
pay_method_cd  juchusaki_kbn
sekyu_cd     sekyu_nm    sekyu_knm      sekyu_soufu_nm  sekyu_yubin_no  sekyu_addr1
sekyu_addr2  sekyu_tel   sekyu_fax      delivery_kbn    nonyu_cd
nonyu_nm     nonyu_knm   nonyu_yubin_no nonyu_addr1     nonyu_addr2
nonyu_tel    nonyu_fax   nonyu_dt
juchu_prc_sum   genka_prc_sum  juchu_hepn_sum  juchu_nebk_sum   hoshi_prc_sum
szei_katax_taisho_prc  uzei_katax_taisho_prc  hitax_katax_taisho_prc 
tax_code_kbn  tax_cd  tax_kbn  zei_cd
out_zei_prc  in_zei_prc  arari_prc
denpyo_biko1  denpyo_biko2 nohinsho_pirint_kbn v_free5
END_OF_TXT
                                                 ) as $key ) {
            @ $hdrData[ $key ] = $$key;
        }

        //
        // データ登録更新
        //

        // 伝票データ登録更新
        if ( $isAddCtxt ) { // 登録
            list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'juchu_denpyo', $hdrData );
        } else { // 更新
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'juchu_denpyo', $hdrData,
                                                                   array('denpyo_no', 'seko_no') );
        }

        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        $cnt = $db->easyExecute($sql, $param);

        // 明細削除データ
        foreach ( $dtlDel as $data00 ) {
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'juchu_denpyo_msi', $data00,
                                                                   array('denpyo_no', 'seko_no', 'msi_no') );
            // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
            $cnt = $db->easyExecute($sql, $param);
        }

        // 明細更新データ
        foreach ( $dtlUpd as $data00 ) {
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'juchu_denpyo_msi', $data00,
                                                                   array('denpyo_no', 'seko_no', 'msi_no') );
            // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
            $cnt = $db->easyExecute($sql, $param);
        }

        // 明細登録データ
        foreach ( $dtlIns as $data00 ) {
            list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'juchu_denpyo_msi', $data00 );
            // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
            $cnt = $db->easyExecute($sql, $param);
        }

        //
        // (#1)金額設定処理
        //
        /*
        $kinUpdData = array( 'denpyo_no' => $denpyo_no,
                             'seko_no'   => $seko_no );

        $juchuSum = App_Utils::getJuchuPrcSum($denpyo_no);
        $juchuSum00 = Msi_Sys_Utils::remapArrayFlat( $juchuSum, <<< END_OF_TXT
juchu_prc_sum genka_prc_sum juchu_hepn_sum
juchu_nebk_sum hoshi_prc_sum arari_prc_sum
END_OF_TXT
                                                     , array( 'arari_prc_sum' => 'arari_prc' ) );
        $kinUpdData = array_merge( $kinUpdData, $juchuSum00 );
        $juchuTaxSum = App_Utils::getJuchuZeiSum($denpyo_no);
        $juchuTaxSum00 = Msi_Sys_Utils::remapArrayFlat( $juchuTaxSum, <<< END_OF_TXT
szei_katax_taisho_prc uzei_katax_taisho_prc hitax_katax_taisho_prc
END_OF_TXT
                                                        );
        $kinUpdData = array_merge( $kinUpdData, $juchuTaxSum00 );
        / *
        list($kinUpdData['out_zei_prc'], $kinUpdData['in_zei_prc'])
            = static::_myCalcTax($juchuTaxSum00['szei_katax_taisho_prc'] + $juchuTaxSum00['uzei_katax_taisho_prc']
                                 + $juchuTaxSum00['hitax_katax_taisho_prc'],
                                 $tax_kbn, $zei_rtu);
        * /
        $_szei_adj = App_ClsTaxLib::CalcTax( $juchuTaxSum00['szei_katax_taisho_prc'], 2, $zei_rtu );
        $kinUpdData['out_zei_prc'] = 0 + $_szei_adj['ZeiPrc'];
        $_uzei_adj = App_ClsTaxLib::CalcTax( $juchuTaxSum00['uzei_katax_taisho_prc'], 2, $zei_rtu ); // 2:外税 is OK
        $kinUpdData['in_zei_prc'] = 0 + $_uzei_adj['ZeiPrc'];
        */

        $kinUpdData = array( 'denpyo_no'      => $denpyo_no,
                             'seko_no'        => $seko_no,
                             'juchu_prc_sum'  => $juchu_prc_sum,
                             'genka_prc_sum'  => $genka_prc_sum,
                             'juchu_hepn_sum' => $juchu_hepn_sum,
                             'juchu_nebk_sum' => $juchu_nebk_sum,
                             'hoshi_prc_sum'  => $hoshi_prc_sum,
                             'arari_prc'      => $arari_prc,
                             'szei_katax_taisho_prc'  => $szei_katax_taisho_prc,
                             'uzei_katax_taisho_prc'  => $uzei_katax_taisho_prc,
                             'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc,
                             'out_zei_prc' => $out_zei_prc,
                             'in_zei_prc'  => $in_zei_prc,
                             );

        // 消費税額は伝票ベースで再設定  軽減税率対応 keigen
        $juchuPrcSum2 = App_KeigenUtils::calcJuchuZeiDenpyo($db, $denpyo_no);
        $kinUpdData = array_merge( $kinUpdData, $juchuPrcSum2 );

        list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'juchu_denpyo', $kinUpdData,
                                                               array('denpyo_no', 'seko_no') );
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        $cnt = $db->easyExecute($sql, $param);
        if ( $cnt !== 1 ) {
            throw new Msi_Sys_Exception_InputException( "金額更新エラー" );
        }

        // 受注伝票の明細とヘッダーの消費税差額設定処理 軽減税率対応 keigen
        App_KeigenUtils::setJuchuDenpyoAdjTax($db, $denpyo_no);

        // 売上伝票保存
        DataMapper_UriageFromJuchu::upsert($db, $denpyo_no, array('keijo_ymd'=>$keijo_ymd));

        // データ確定
        $db->commit();

        $dataNew = DataMapper_JuchuDenpyo::findOne( $db, array('denpyo_no'=>$denpyo_no) );
        
        $dataNew['_isNew'] = $isAddCtxt;

        return $dataNew;
    }

    /**
     * 受注伝票（単品） 削除
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
	 * @version 2014/09/07 入金伝票の削除処理、履歴テーブルの削除処理を追加 Kayo
	 * @version 2015/01/17 入庫出庫伝票、明細のの削除処理 2015/01/17 ADD Kayo
     * @param      array   $data
     * @return     array   データ一式
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function delete($data)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $denpyo_no = Msi_Sys_Utils::emptyToNull($data['denpyo_no']);

        if ( $denpyo_no === null ) {
            throw new Msi_Sys_Exception_InputException( "伝票No が指定されていません" );
        }

        // 既存データ検索
        if ( strlen($denpyo_no) > 0 ) {
            $dataCur = static::_findHdr($db, $denpyo_no);
            if ( $dataCur === null ) {
                throw new Msi_Sys_Exception_InputException( "伝票No ($denpyo_no) のデータが存在しません" );
            }
        }

        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE juchu_denpyo
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND denpyo_no = :denpyo_no
   AND seko_no = :seko_no
END_OF_SQL
                                 , array('denpyo_no' => $denpyo_no,
                                         'seko_no' => static::$_seko_no) );

        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE juchu_denpyo_msi
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND denpyo_no = :denpyo_no
END_OF_SQL
                                 , array('denpyo_no' => $denpyo_no) );

        $uri_den_no = $db->getOneVal( <<< END_OF_SQL
SELECT uri_den_no 
  FROM uriage_denpyo
 WHERE delete_flg = 0
   AND denpyo_no = :denpyo_no
   AND seko_no = :seko_no
END_OF_SQL
                                      , array('denpyo_no' => $denpyo_no,
                                              'seko_no' => static::$_seko_no) );
        if ( $uri_den_no !== null ) {
            $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE uriage_denpyo
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND uri_den_no = :uri_den_no
END_OF_SQL
                                     , array('uri_den_no' => $uri_den_no) );

            $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE uriage_denpyo_msi
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND uri_den_no = :uri_den_no
END_OF_SQL
                                     , array('uri_den_no' => $uri_den_no) );
			// 売上伝票、売上伝票明細履歴の削除処理	2014/09/07 ADD Kayo
			$ret = Logic_DenpyoHistoryMake::UriageHistoryDelete($db, $uri_den_no);
            // 入庫出庫伝票、明細のの削除処理 2015/01/17 ADD Kayo
            $ret = Logic_SyukoDenpyoMake::NyukoShukoDenpyo_Delete($db, $uri_den_no);
        }

		// 入金伝票、入金伝票明細の削除	2014/09/07 ADD Kayo
        if ( $uri_den_no !== null ) {
			// 入金伝票を取得
			$select_nyukin = $db->easySelect( <<< END_OF_SQL
SELECT denpyo_no
FROM nyukin_denpyo
WHERE seikyu_no = :seikyu_no
END_OF_SQL
		, array('seikyu_no' => $uri_den_no));
			if (count($select_nyukin) > 0)	{
				// 入金伝票を削除
				$cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE nyukin_denpyo
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND denpyo_no = :denpyo_no
END_OF_SQL
                , array('denpyo_no' => $select_nyukin[0]['denpyo_no']) );
				// 入金伝票を削除	
				$cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE nyukin_denpyo_msi
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND denpyo_no = :denpyo_no
END_OF_SQL
                , array('denpyo_no' => $select_nyukin[0]['denpyo_no']) );
                // 入金伝票、入金伝票明細履歴の削除処理
                $ret = Logic_DenpyoHistoryMake::NyukinHistoryDelete($db, $select_nyukin[0]['denpyo_no']);
			}
        }

        $db->commit();

        return $data;
    }

    /**
     * 受注伝票（単品） の検索
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no 伝票番号
     * @return     array|null(存在しない)
     */
    protected static function _findHdr($db, $denpyo_no)
    {
        $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        $select = $db->easySelect( <<< END_OF_SQL
SELECT m.*,
       tt.tanto_nm, tt.tanto_knm,
       b.bumon_lnm, b.bumon_snm, b.bumon_lknm, b.bumon_sknm, b.bumon_kbn
  FROM juchu_denpyo m
 INNER JOIN tanto_mst tt
    ON tt.delete_flg=0
   AND m.tanto_cd=tt.tanto_cd
 INNER JOIN bumon_mst b
    ON b.delete_flg=0
   AND m.bumon_cd=b.bumon_cd   $dateWhere3
 WHERE m.delete_flg=0
   AND m.denpyo_no = :denpyo_no
   AND m.seko_no = :seko_no
   FOR UPDATE
END_OF_SQL
                                   , array('denpyo_no'=>$denpyo_no,
                                           'seko_no'=>static::$_seko_no) );

        if ( count($select) === 0 ) {
            return null;
        }

        return $select[0];
    }

   /**
     * 受注伝票明細 の１行検索
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no 伝票番号
     * @param      integer $msi_no    受注明細No
     * @return     array|null(存在しない)
     */
    protected static function _findDtl($db, $denpyo_no, $msi_no)
    {
        $select = $db->easySelect( <<< END_OF_SQL
SELECT m.*,
       s.shohin_nm, s.kani_shohin_nm
  FROM juchu_denpyo_msi m
 INNER JOIN juchu_denpyo hdr
    ON hdr.denpyo_no = m.denpyo_no
   AND hdr.delete_flg=0
 INNER JOIN shohin_mst s
    ON s.delete_flg=0
   AND s.shohin_cd=m.shohin_cd
   AND m.kaisya_cd          = s.kaisya_cd      -- 2016/12/25 ADD Kayo
   AND m.shohin_bumon_cd    = s.bumon_cd       -- 2016/12/25 ADD Kayo
 WHERE m.delete_flg=0
   AND m.denpyo_no = :denpyo_no
   AND m.msi_no = :msi_no
   FOR UPDATE
END_OF_SQL
                                   , array('denpyo_no'=>$denpyo_no,
                                           'msi_no'=>$msi_no) );

        if ( count($select) === 0 ) {
            return null;
        }

        return $select[0];
    }

   /**
     * 受注伝票明細 の検索
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no 伝票番号
     * @return     array
     */
    protected static function _searchDtl($db, $denpyo_no)
    {
        return DataMapper_JuchuDenpyo::findDenpyoMsi($db, $denpyo_no);
    }

    /**
     *
     * 最大受注明細Noを取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/3/31
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no 伝票番号
     * @return int 最大受注明細No
     */
    protected static function _getMaxdenpyoMsiNo($db, $denpyo_no)
    {
        // cf. Juchu_JuchuAbstract#getMaxdenpyoMsiNo()
        $sql1 = <<< END_OF_SQL
SELECT
    COALESCE(MAX(msi_no),0) AS msi_no
FROM
    juchu_denpyo_msi
WHERE
    denpyo_no = :denpyo_no
END_OF_SQL;
        // OK not delete_flg

        $max = $db->getOneVal($sql1, array('denpyo_no' => $denpyo_no));

        return $max;
    }

    /**
     *
     * 外税消費税額,内税消費税額を返す
     *
     * <AUTHOR> Mihara
     * @since 2014/04/10
     * @param   int $prc        対象金額
     * @param   string $zeiKbn  課税区分(0:非課税 1:内税 2:外税）
     * @param   int $zei_rtu    消費税率
     * @return  array  (外税消費税額, 内税消費税額)
     */
    protected static function DEL_myCalcTax($prc, $zeiKbn, $zei_rtu)
    {
        $zei = App_ClsTaxLib::CalcTax($prc, $zeiKbn, $zei_rtu);
        if ($zeiKbn == 1) { // 1:内税
            $in_zei_prc  = $zei["ZeiPrc"];
            $out_zei_prc = 0;
        } else if ($zeiKbn == 2) { // 2:外税
            $in_zei_prc  = 0;
            $out_zei_prc = $zei["ZeiPrc"];
        } else {
            $in_zei_prc  = 0;
            $out_zei_prc = 0;
        }
        return array($out_zei_prc, $in_zei_prc);
    }

}
