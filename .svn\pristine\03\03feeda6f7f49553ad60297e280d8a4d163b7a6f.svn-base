/* 
    棚卸・在庫管理表表示順序登録
    Created on : 2015/04/27
    Author     : MSI Kayo
*/
#main #t_head 
,#main #t_dtl
{
    table-layout: fixed;/*テーブル列幅を固定レイアウトにする*/
}
/*表全体*/
.items{
    margin-top: 20px;
    width:50%;
    height:85%
}
/*明細部分*/
.list{
    overflow-y:auto;/*スクロールバー*/
    height:90%;
    padding-bottom:1px;
}
/*テーブル列の幅調整*/
#main table .w5 {
    width: 5%!important;	
}
#main table .w7 {
    width: 7%!important;	
}
#main table .w8 {
    width: 8%!important;	
}
#main table .w10 {
    width: 10%!important;
}
#main table .w15 {
    width: 15%!important;
}
#main table .w20 {
    width: 20%!important;
}

/*テーブル見出し部分*/
#main table .tbhd{
    color: #244b20!important;
    background-color: transparent!important;
    border:none!important;
}
/*テーブル明細部分*/
#main table .tbdt {
    padding: 0 8px;
    border-bottom: 1px solid #88B1D1;
    border-right: 1px solid #88B1D1;
}
/*テーブル明細 右端部分*/
#main table .tbdtr {
    padding: 0 8px;
    border-bottom: 1px solid #88B1D1;
    border-right: none;
}
/*行追加、行削除アイコン*/
#main table .iconcolor{
text-decoration: none;
color: inherit;
}
/*入力項目*/
#main  table td .shohin_cd, 
#main  table td .shohin_nm, 
#main  table td .maker_hinban, 
#main  table td .disp_no1 
{
    width: 100%;
    height: 90%;
    border: none;
}
#main  table td .disp_no1 
{
    text-align: right;
}


