{include file="fdn_head_std.tpl"}
{include file="fdn_header_12.tpl"}

<div class="container div-fixed-for-spin" id="main-container">
    <form id="juhen-form-id" method="post">
        {include file="header_info.tpl"}
        {include file="side_menu.tpl"}
        <div id="detail" >
            {include file="juchu/itemex/dialog-item-input.tpl"}
            <div id = "juhen-div-wrapper" style="display: none">
                <fieldset class="base_2">
                    {*                    <label for="seko_plan_cd" id="lbl_seko_plan_cd" class="require done">コース商品名</label>
                    <select id="seko_plan_cd" class = "cls_seko_plan_cd">
                    </select>*}
                    <label for="uriage_keijo_ymd" id="lbl_uriage_keijo_ymd" class="require done">売上計上日</label>
                    <input name="uriage_keijo_ymd" id="uriage_keijo_ymd" type="text" class="txt" value="" maxlength = "10"/>
                    <div id = "uriage_keijo_dlg" class="label dlg_date"></div>
                    <div id = "kakutei_info" class="update"></div>
                </fieldset>
                <div id = "grid-div-wrapper">
                    <div id ="grid-div-hdr">
                        <table class="text-center font-16" id="juhen-hdr" >
                            <thead>
                                <tr class="text-center">
                                    <th rowspan="2" class = "col-row">行</th>
                                    <th rowspan="2" class = "col-copy">ｺﾋﾟ-</th>
                                    <th class = "col-add">追加</th>
                                    <th rowspan="2" style="display: none" class="col-nouse">未使用</th>
                                    <th class = "col-kbn">伝区</th>
                                    <th class = "col-shohin">商品名称</th>
                                    <th class = "col-tanka">単価</th>
                                    <th rowspan="2" style="display: none" class="hoshi_umu_kbn_disp col-hoshi">{$service_nm}</th>
                                    {*<th rowspan="2" style="display: none" class="tokuten_kbn_disp col-tokuten">特典</th>*}
                                    <th rowspan="1" class = "col-waribiki_kbn">割引</th>
                                    <th rowspan="2" class = "col-nebiki">値引</th>
                                    <th class = "col-sum">合計</th>
                                    <th class = "col-bumon">部門</th>
                                    {*<th rowspan="2" class = "col-dialog-item"></th>*}
                                    {*<th class = "col-nohin">納品場所</th>*}
                                </tr>
                                <tr class="text-center">
                                    <th>削除</th>
                                    <th>商品区分</th>
                                    <th>摘要</th>
                                    <th>数量</th>
                                    <th>割引金額</th>
                                    <th>目的</th>
                                    <th>発注先／依頼先</th>
                                    {*<th>納品日時</th>*}
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div id ="grid-div-dtl">
                        <table id="juhen-dtl" >
                        </table>
                    </div>
                    <fieldset class="den_biko">
                        <label for="biko" id="lbl_biko" class="require done">入金備考</label>
                        <input name="biko" id="biko" type="text" class="txt" value="" maxlength = "30"/>
                    </fieldset>
                </div>
            </div>
            <div class="buttons">
                <input type="button" name="btn_save" id="btn_save" value="保存" />
{*                <input type="button" name="btn_print" id="btn_print" value="印刷" />*}
                {*<input type="button" name="btn_delete" id="btn_delete" value="削除" />*}
                <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
            </div>
        </div>
    </form><!-- /#juhen-form-id-->
</div><!-- /#main-container -->


{literal}
    <script type="text/template" id="tmpl-juchuhenko">
        <tr class="text-center">
            <td rowspan="2" class="disp_no col-row"></td>
            <td rowspan="2" class="col-copy">
                <a href="javascript:void(0)" class="add my-readonly-hidden"><i title="直下に行コピーします" class="glyphicon glyphicon-copyright-mark row-copy"></i></a>
            </td>
            <td class="col-add">
                <input type="button" class="shohin_row_add shohin_row_btn" value="＋" />
            </td>
            <td rowspan="2" style="display: none" class="col-nouse">
                <input type="checkbox" class="nouse_check" value = "1" />
            </td>
            <td class="col-kbn">
                <input type="hidden" class="denpyo_kbn text-left grid-select"/>
            </td>
            <td class="col-shohin">
                <input type="hidden" class="shohin_cd text-left grid-select"/>
                <div class="shohin_nm_input_div" ><div><input type="hidden" class="shohin_nm_input" maxlength = "20"></div></div>
            </td>
            <td class="col-tanka">
                <div class = "juhen-dtl-input-wrapper"><input type="text" class="juchu_tnk juhen-txt1 text-right padding-right1 font-16 ime-off" disabled = "disabled" value="" onblur="$.msiJqlib.commaFilterTemp($(this))" maxlength = "8"></div>
            </td>
            <td rowspan="2" style="display: none" class="hoshi_umu_kbn_disp col-hoshi">
                <input type="hidden" class="hoshi_umu_kbn text-left grid-select"/>
            </td>
            <td rowspan="1" class="col-waribiki_kbn">
                <input type="hidden" class="waribiki_kbn text-left grid-select"/>
            </td>
            <td rowspan="2" class="col-nebiki">
                <div class = "juhen-dtl-input-wrapper"><input type="text" class="nebiki_prc2 juhen-txt1 text-right padding-right1 font-16 ime-off" value="" onblur="$.msiJqlib.commaFilterTemp($(this))" maxlength = "8"></div>
            </td>
            <td class="juchu_gokei text-right padding-right1 padding-top1 font-16 col-sum keigen_disp"></td>
            <td class="zei_cd" style="display:none;width:0"><input type="text" class="zei_cd"/></td>
            <td class="reduced_tax_rate" style="display:none;width:0"><input type="text" class="reduced_tax_rate"/></td>
            <td class="col-bumon">
                <input type="hidden" class="bumon_cd text-left grid-select"/>
            </td>
        </tr>
        <tr class="text-center">
            <td class="col-del">
                <input type="button" class="shohin_row_del shohin_row_btn" value="－" />
            </td>
            <td>
                <input type="hidden" class="shohin_kbn text-left grid-select"/>
            </td>
            <td class="text-left padding-left1">
                <span class="shohin_tkiyo_nm_disp juhen-txt1"></span>
                <div><input type="hidden" class="shohin_tkiyo_nm juhen-txt1 text-left" value="" maxlength = "20"></div>
            </td>
            <td>
                <div class = "juhen-dtl-input-wrapper"><input type="text" class="juchu_suryo juhen-txt1 text-right padding-right1 font-16 ime-off" value="" onblur="$.msiJqlib.commaFilterTemp($(this))" maxlength = "4"></div>
            </td>
            <td class="col-nebiki">
                <div class = "juhen-dtl-input-wrapper"><input type="text" class="nebiki_prc juhen-txt1 text-right padding-right1 font-16 ime-off" value="" onblur="$.msiJqlib.commaFilterTemp($(this))" maxlength = "8" disabled="disabled"></div>
            </td>
            <td>
                <input type="hidden" class="mokuteki_kbn text-left grid-select"/>
            </td>
            <td>
                <input type="hidden" class="siire_cd text-left grid-select"/>
            </td>
        </tr>
    </script>
{/literal}
<script id="data-juchuhenko" type="application/json">
    {$juchuhenko_json|smarty:nodefaults}
</script>
{include file="fdn_footer_std.tpl"}
