<?php

/**
 * Kanri_SalesforceController
 *
 * SalesforceCSV出力指示画面　コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Sugiyama
 * @since      2019/03/22
 * @filesource 
 */

/**
 * SalesforceCSV出力指示画面 コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Sugiyama
 * @since      2020/07/xx
 */
class Kanri_SalesforceController extends Msi_Zend_Controller_Action {
    private static $bindings = array(
        '#report_kbn' => 'report_kbn',		// 出力区分
    );
    /**
     * SalesforceCSV出力指示画面 コントローラクラス
     *
     * @category   App
     * @package    controller\kanri
     * <AUTHOR> Sugiyama
     * @since      2020/07/xx
     */
    public function printAction() {
        $params = Msi_Sys_Utils::webInputs();
        $db = Msi_Sys_DbManager::getMyDb();
        // 初期値
        $_dataApp = $this->_defaultDataApp();
        $report_kbn_ary = $this->getCodeKbn($db, "8030");
        // 出力区分取得
        
        // 返却パラメータ
        $data = array(
            'dataApp' => $_dataApp,
            'bindings' => self::$bindings,
            'select2DataAry' => array(
                array('id' => '#report_kbn', 'data' => $report_kbn_ary),
            ),
            'status' => 'OK',
            'msg' => ''
        );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;

        // 読込ファイル
        App_Smarty::pushCssFile(['app/kanri.print.siji.css']);
        App_Smarty::pushJsFile(['app/kanri.print.siji.js']);
    }

    /**
     * 初期値を返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/07/xx
     */
    protected function _defaultDataApp() {
        $dataApp = array(
            'report_cd'     => 'pdf0756',
            'print_ymd'     => Msi_Sys_Utils::getDate(),                    // 印刷年月日
            'taisho_st_ymd' => Msi_Sys_Utils::getDate(strtotime('-1 day')), // 対象年月日（自）
            'taisho_ed_ymd' => Msi_Sys_Utils::getDate(),                    // 対象年月日（至）
            'report_kbn'    => 1,
        );
        return $dataApp;
    }
    
    /**
     * コード名称取得
     * @param type $db
     * @param type $code_kbn
     */
    private function getCodeKbn ($db, $code_kbn) {
        $select = $db->easySelect( <<< END_OF_SQL
        SELECT 
             kbn_value_cd_num AS id
            ,kbn_value_lnm    AS text
        FROM 
            code_nm_mst 
        WHERE 
            code_kbn = :code_kbn
END_OF_SQL
        ,array('code_kbn' => $code_kbn));
        return $select;
    }
    
}
