<div class="component-root {$ctxt_readonly}" id="order" style="display:none">
  <div class="comp-selpart01">
    <div class="comp-part-main">
      <div class="header-part">
        <div class="searchbtnarea-cls">
	      <input name="btn_search" class="btn_search" type="button" value="検索" />
	      <input name="btn_clear" class="btn_clear" type="button" value="クリア" />
          <input name="btn_search_next" style="visibility:hidden"
                 class="btn_search_next sp-act-button-search-next" type="button" title="次ページ" value="次" />
          <input name="btn_search_prev" style="visibility:hidden"
                 class="btn_search_prev sp-act-button-search-prev" type="button" title="前ページ" value="前" />
{* <!--
          <input name="btn_search_redo" style="visibility:hidden"
                 class="btn_search_redo sp-act-btn-search-redo" type="button" title="同じ条件で再検索します" value="画面更新" />
--> *}
        </div>
      </div><!-- /.header-part -->

      <div class="search-cond-part">
{*
        <!-- 検索条件 -->
        <fieldset class="search_added">
          <label for="s_mysort_1" class="lbl_s_sort w34 ">表示順</label>
          <input type="hidden" id="s_mysort_1" name="s_mysort_1" class="s_mysort_1 cls_dd_std s_sort-cls w65"/>
        </fieldset>
*}

        <!-- 検索条件 -->
	    <fieldset class="base_1 fieldset-line1-cls">
		  <label for="sougi_bi" class="lbl_sougi_bi w6 option">{'葬儀日'|msi_name_map}</label>
          <input name="s_sougi_from" id="s_sougi_from" type="text" class="txt my-type-date to_alpha_num date_auto_slash nl-right w8" value="" maxlength="10"/>
          <div class="label dlg_date dlg_from"></div>
          <div class="label day-bar">～</div>
          <input name="s_sougi_to" id="s_sougi_to" type="text" class="txt my-type-date to_alpha_num date_auto_slash nl-right w8" value="" maxlength="10"/>
          <div class="label dlg_date dlg_to"></div>
          <label for="s_name_1" class="lbl_s_name_1 l-left w6 option">{'葬家名'|msi_name_map}</label>
          <input name="s_name_1" id="s_name_1" type="text" class="txt w8" value="" />
		  <label for="s_name_2" class="lbl_s_name_2 w6">故人名</label>
		  <input type="text" name="s_name_2" id="s_name_2" class="txt w8" value="" />
          <label for="s_name_3" class="lbl_s_name_3 w6 option">{'喪主名'|msi_name_map}</label>
          <input type="text" name="s_name_3" id="s_name_3" class="txt w8" value="" />
          <label for="s_seko_no" class="lbl_s_seko_no option">施行番号</label>
          <input name="s_seko_no" id="s_seko_no" type="text" class="txt w8 to_alpha_num nl-right" value="" />
          <!-- div class="label dlg_common s_seko_no-ref w2 nl-left cursor-pointer"></div -->
          <label for="s_order_kbn_ex" class="lbl_s_order_kbn_ex w6 option l-left">注文区分</label>
          <input type="hidden" id="s_order_kbn_ex" name="s_order_kbn_ex" class="s_order_kbn_ex-cls cls_dd_std w10" />
          <!-- label class="lbl_dummy_filler nl-btm nl-right w10"></label -->
	    </fieldset>

	    <fieldset class="base_2 fieldset-line2-cls">
          <label for="s_tel" class="lbl_s_tel option" >電話番号</label>
          <input name="s_tel" id="s_tel" type="text" class="txt w22" value="" />
          <label for="s_staff_2" class="lbl_s_staff_2 option">{'施行担当'|msi_name_map}</label>
		  <input name="s_staff_2" id="s_staff_2" type="text" class="txt w11 nl-right" value="" readonly/>
		  <input name="s_staff_2_cd" id="s_staff_2_cd" type="hidden" value="" />
		  <div class="label dlg_common s_staff_2-ref"></div>
		  <label for="s_bumon" class="lbl_s_bumon w5 l-left option">部門</label>
		  <input name="s_bumon" id="s_bumon" type="text" class="txt w11 nl-right" value="" readonly/>
		  <input name="s_bumon_cd" id="s_bumon_cd" type="hidden" value="" />
		  <div class="label dlg_common s_bumon-ref"></div>
          <label for="s_foc_seko_id" class="lbl_s_foc_seko_id l-left w9 option">FOC葬儀ID</label>
          <input name="s_foc_seko_id" id="s_foc_seko_id" type="text" class="txt w8 to_alpha_num" value="" />
          <label for="s_foc_rendo_sumi" class="lbl_s_foc_rendo_sumi w6 option ft10 nl-left">ステータス</label>
          <input type="hidden" id="s_foc_rendo_sumi" name="s_foc_rendo_sumi" class="s_foc_rendo_sumi-cls cls_dd_std w10" />
	    </fieldset>

{if $is_dev_env}
	    <fieldset class="base_2">
          <label for="s_entry_id" class="lbl_s_entry_id option nl-btm">申込番号</label>
          <input name="s_entry_id" id="s_entry_id" type="text" class="txt w12 to_alpha_num nl-btm" value="" />
          <label for="s_entry_detail_id" class="lbl_s_entry_detail_id option nl-btm">明細番号</label>
          <input name="s_entry_detail_id" id="s_entry_detail_id" type="text" class="txt w12 to_alpha_num nl-btm" value="" />
          <label for="s_shohin_ex" class="lbl_s_shohin_ex option nl-btm">商品</label>
          <input name="s_shohin_ex" id="s_shohin_ex" type="text" class="txt w16 to_alpha_num nl-btm" value="" />
          <label for="s_addr_ex" class="lbl_s_addr_ex option nl-btm">住所</label>
          <input name="s_addr_ex" id="s_addr_ex" type="text" class="txt w16 to_alpha_num nl-btm" value="" />
          <label for="s_nafuda_ex" class="lbl_s_nafuda_ex option nl-btm">名札</label>
          <input name="s_nafuda_ex" id="s_nafuda_ex" type="text" class="txt w14 to_alpha_num nl-right nl-btm" value="" />
          <!-- label class="lbl_dummy_filler nl-btm nl-right w10"></label -->
	    </fieldset>
{/if}{* is_dev_env *}
      </div><!-- /.search-cond-part -->

      <!-- 結果リスト -->
      <div class="result-part">
        <div class="result-list">
          <div class="header">
	        <table>
              <colgroup>
		        <col class="f11">
                <col class="f12">
                <col class="f13">
                <col class="f14">
                <col class="f15">
                <col class="f16">
                <col class="f17">
                <col class="f18">
                <col class="f19">
                <col class="f1a">
              </colgroup>
	          <tr class="result-list-tr1">
		        <td class="row chkAllToggle f11" rowspan="3">選択</td>
                <td class="f12">施行番号</td>
                <td class="f13">通夜日</td>
                <td class="f14">葬家名</td>
                <td class="f15">喪主</td>
                <td class="f16">部門</td>
                <td class="f17">申込番号</td>
                <td class="f18">申込者名</td>
                <td class="f19">申込者住所</td>
                <td class="f1a" rowspan="3">ステータス</td>
	          </tr>
	          <tr class="result-list-tr2">
                <td class="">FOC葬儀ID</td>
                <td class="">葬儀日</td>
                <td class="">故人名</td>
                <td class="">電話番号</td>
                <td class="">式場</td>
                <td class="">明細番号</td>
                <td class="">申込日時</td>
                <td class="">名札</td>
	          </tr>
	          <tr class="result-list-tr3">
                <td class="">キャンセル</td>
                <td class="">商品コード</td>
                <td class="">商品名</td>
                <td class="">数量</td>
                <td class="">単価</td>
                <td class="">金額</td>
                <td class="">注文区分</td>
                <td class="">申込者電話番号</td>
	          </tr>
	        </table>
          </div><!-- /.header -->
          <div class="list"></div><!-- /.list -->
        </div><!-- /.result-list -->
      </div><!-- /.result-part -->

{* ---- *}
{if $is_dev_env}
      <div class="footer-part">
        <div class="footer-part-1">
          <div class="footer-part-left">

            {msi_picker_mstrkey label="表示順１" key="s_order_by_1" mstrKey="sortKey"   label_class="w10" class="nl-top"}
            {msi_picker_mstrkey label="表示順２" key="s_order_by_2" mstrKey="sortKey"   label_class="w10" class="nl-top"}
            {msi_picker_mstrkey label="表示順３" key="s_order_by_3" mstrKey="sortKey"   label_class="w10" class="nl-top"}
            {msi_picker_mstrkey label="表示件数" key="s_limit_cnt"  mstrKey="limitCnt"  label_class="w10" class="nl-top"}
          </div><!-- /.footer-part-left -->
          <div class="footer-part-right">
            <div class="search-cnt-disp"><span class="search-cnt-disp-txt"></span></div>
          </div><!-- /.footer-part-right -->
        </div><!-- /.footer-part-1 -->
      </div><!-- /.footer-part -->
{/if}{* is_dev_env *}
{* ---- *}

      <script class="my-data-init-cls" type="application/json">
        {$mydata_json|smarty:nodefaults}
      </script>
    </div><!-- /.comp-selpart01 -->
  </div><!-- /.comp-part-main -->

  <div class="comp-part-bottom">
    <div class="footer-part-2">
      <div class="footer-part-left">
        {msi_btn_sel_cmd  _btn_key="btn_save" _btn_cap="保存" _btn_cls="btn_save"}
        <!-- input type="button" id="btn_csvout" class="btn_csvout" value="一覧CSV出力" / -->
      </div><!-- /.footer-part-left -->
    </div><!-- /.footer-part-2 -->
  </div><!-- /.comp-part-bottom -->

</div><!-- /.component-root -->

      
{literal}
<script type="text/template" class="item-template-colgroup">
  <colgroup>
	<col class="f11">
    <col class="f12">
    <col class="f13">
    <col class="f14">
    <col class="f15">
    <col class="f16">
    <col class="f17">
    <col class="f18">
    <col class="f19">
    <col class="f1a">
  </colgroup>
</script>

<script type="text/template" class="item-template">
  <tr class="result-list-sel result-list-tr1 item_id_<%- myid %>" data-item_id="<%- item_id %>">
	<td class="row f11" rowspan="3"><%- line_no %><!-- line_no_p --></td>
    <td class="f12 seko_no_td_pos"><span class="seko_no"><%- seko_no %></span>
      <button type="button" name="btn_seko_no_link" class="btn_seko_no_link my_button_ex2 btn_seko_no_link-cls" style="display:none"
              title="「FOC葬儀ID」をFDN「施行番号」に関連づける画面を表示します">リンク..</button>
      <i title="施行番号の対応付けをクリアします" class="glyphicon glyphicon-copyright-mark btn_seko_no_clear" style="display:none"></i>
    </td>
    <td class="f13"><%- tsuya_date_ymd %> <%- tsuya_begin_time_hm %></td>
    <td class="f14 text-left"><%- soke_name_disp %></td>
    <td class="f15 text-left"><%- moshu_name %></td>
    <td class="f16 text-left"><%- bumon_nm %></td>
    <td class="f17 text-right"><%- entry_id %></td>
    <td class="f18 text-left"><%- entry_name %></td>
    <td class="f19 text-left"><%- entry_addr_disp %></td>
    <td class="f1a" rowspan="3"><div class="my_rendo_disp_pos"><span class="my_rendo_status_disp"></span><div 
                                                                     class="my_henrei_proc_type_pos"></div></td>
  </tr>
  <tr class="result-list-sel result-list-tr2 item_id_<%- myid %>" data-item_id="<%- item_id %>">
    <td class="text-right"><%- foc_seko_id %></td>
    <td class=""><%- sogi_date_ymd %> <%- sogi_begin_time_hm %></td>
    <td class="text-left"><%- kojin_name %></td>
    <td class="text-left"><%- tel_disp %></td>
    <td class="text-left"><%- sogi_hall_name %></td>
    <td class="text-right"><%- entry_detail_id %></td>
    <td class=""><%- entry_ts_ymdhm %></td>
    <td class="text-left"><%- nafuda_disp %></td>
  </tr>
  <tr class="result-list-sel result-list-tr3 item_id_<%- myid %>" data-item_id="<%- item_id %>">
    <td class="my_cancel_disp_td_pos"><%- my_cancel_disp %><span class="my_rendo_torikesi_tgt_disp"></span></td>
    <td class=""><%- fdn_item_code_disp %></td>
    <td class="text-left"><%- item_name_disp %></td>
    <td class="text-right"><% print(msiLib2.fComma(quantity)) %></td>
    <td class="text-right"><% print(msiLib2.fComma(item_unit_price)) %></td>
    <td class="text-right keigen_disp shohin_kingaku"><span class="kingaku_zei_type"></span><% print(msiLib2.fComma(item_price)) %></td>
    <td class=""><%- service_name_disp %></td>
    <td class="text-left"><%- entry_tel %></td>
  </tr>
</script>

<script type="text/template" class="list-msg-template">
<table class="spe-msg">
  <tr class="odd" style="width:100%">
    <td class="msg" style="text-align:center"><%- msg %></td>
  </tr>
</table>
</script>
{/literal}

