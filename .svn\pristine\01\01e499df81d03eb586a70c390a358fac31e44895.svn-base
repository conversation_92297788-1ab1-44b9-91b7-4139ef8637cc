@charset "UTF-8";
.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.text-center {
	text-align: center;
}

.text-justify {
	text-align: justify;
}

.small {
	font-size: 85%;
}

.small2 {
	font-size: 80%;
}

.small3 {
	font-size: 75%;
}

.smallest {
	font-size: 70%;
}

.select2-container .select2-choice {
	background-image: none;
}

.select2-drop-mask {
	background-image: none;
}

.select2-dropdown-open .select2-choice {
	background-image: none;
}

.select2-container-multi .select2-choices {
	background-image: none;
}

.select2-container-multi .select2-choices .select2-search-choice {
	background-image: none;
}

.my-error {
	border: 1px solid red !important;
}

#order {
	position: fixed;
	width: 100%;
	height: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 10px 25px;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	top: 40px;
	left: 0;
	background-color: #D4E5F4;
	box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
	line-height: 1;
	z-index: 2;
	/* cf. juchu.estimate.css */
	/*disable状態のアクティブを元のボタンの状態にする*/
}
#order a {
	text-decoration: none;
	color: inherit;
}
#order h2 {
	position: absolute;
	top: 20px;
	left: 25px;
	font-size: 16px;
	color: #266CA5;
}
#order input {
	font-weight: bold;
}
#order input:disabled, #order input[readonly] {
	color: #545454;
	pointer: auto;
}
#order input {
	width: 32%;
}
#order .page-title {
	display: inline;
	margin-top: 5px;
	margin-bottom: 10px;
	float: left;
}
#order .page-title span {
	display: inline;
	padding: .3em .6em .3em;
	font-weight: bold;
	line-height: 1;
	color: white;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: .25em;
	background-color: #ff9900;
	font-size: 1.2em;
}
#order fieldset.type_1,
#order fieldset2.type_1 {
	margin-top: -20px;
	display: inline;
	margin-bottom: 0;
}
#order fieldset.base_1 {
	border: 0px solid #88B1D1;
}
#order fieldset.base_2,
#order fieldset.base_3,
#order fieldset2.base_1,
#order fieldset2.base_2,
#order fieldset2.base_3	{
	border: 0px solid #88B1D1;
}
#order fieldset.base_2,
#order fieldset2.base_2 {
	margin-bottom: 1px;
	border-top: none;
}
#order fieldset.base_3,
#order fieldset2.base_3 {
	margin-bottom: -1px;
	border: 1px solid #88B1D1;
	border-top: none;
}
#order .dlg_date {
	width: 3%;
	background-image: url(../../../img/ico_dialog_2.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 12px 10px;
	border-top: none;
	border-right: 1px solid #CCC;
	border-bottom: none;
	border-left: 1px solid #CCC;
}
#order .header .kanri_no,
#order .list .td_kanri_no {		
	width: 5%;
}
#order .list .td_kanri_no {		
	width: 5%;
}
#order .header  .fix_kbn_nm	{
	width: 8%;
}
#order .list    .td_fix_kbn_nm	{
	width: 8%;
}
#order .header  .fix_date_ym	{
	width: 8%;
}
#order .list    .td_fix_date_ym	{
	width: 8%;
}
#order .header .syori_ymd {
	width: 12%;
	border-right: none;
}	
#order .list .td_syori_ymd	{
	width: 11.23%;
}
#order .list .kanri_no,
#order .list .fix_kbn_nm,
#order .list .fix_date_ym,
#order .list .syori_ymd {
	border: none !important;
	width: 90%;
	vertical-align: middle;
	border-radius: 4px;
	padding: 1px 4px;
	background-color: inherit;
	height: 100%;
}
#order .list .kanri_no,		
#order .list .fix_kbn_nm,
#order .list .fix_date_ym,
#order .list .syori_ymd {
    text-align: center;
}
#order .list .kanri_no:enabled,		
#order .list .fix_kbn_nm:enabled,
#order .list .fix_date_ym:enabled,
#order .list .syori_ymd:enabled {
	cursor: pointer;
}
#order .header table {
	width: 100%;
	border-bottom: 1px solid #88B1D1;
	border-collapse: collapse;
	background: none;
	background-color: #E8F3FB;
	font-size: 13px;
}
#order .header table td {
	width: 5%;
	height: 30px;
	background-color: transparent;
	text-align: center;
	color: #286EA6;
	font-weight: bold;
	border-right: 1px solid #88B1D1;
	border-bottom: 1px solid #88B1D1;
}
#order .lbl_taisho_ym { 
	width: 20%;
	border-bottom: 1px solid #88B1D1;
	border-top: 1px solid #88B1D1;
	border-left: 1px solid #88B1D1;
}
#order .lbl_fix_kbn{
	width: 20%;
	height: 27px;
	border-bottom: none;
	border-left: 1px solid #88B1D1;
	border-top: none;
}
#order #taisho_ym {
	border-right: none;
	border-bottom: none;
	border-top: none;
}
#order .fix_kbn-cls {
	border-right: none;
	border-bottom: none;
	border-top: none;
}
#order .list table {
	width: 100%;
	border-bottom: 1px solid #88B1D1;
	border-collapse: collapse;
	background-color: #FFF;
}
#order .list table tbody:nth-child(even) {
	background-color: #F5F9FD;
}
#order .list table td {
	width: 5%;
	height: 27px;
	background-color: transparent;
	text-align: center;
	color: #286EA6;
	font-weight: bold;
	border-right: 1px solid #88B1D1;
	border-bottom: 1px solid #88B1D1;
}
#order .list .no-border-bottom {
	border-bottom: none !important;
}
#order .list .my-border-bottom {
	border-bottom: 1px solid #88B1D1;
}
#order .list .my-input-like {
	padding-right: 7px;
}

#order .list .my-error {
	border: 1px solid red !important;
}
#order fieldset {
	clear: both;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative;
	display: block;
	width: 40.08%;
	overflow: hidden;
}
#order fieldset2 {
	clear: both;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative;
	display: block;
	width: 40.08%;
	overflow: hidden;
}
#order .my-akaji {
	color: red;
	font-weight: bold;
}
#order .my-bold {
	font-weight: bold;
}
#order .my-error {
	border: 1px solid red !important;
}
#order .my-disabled {
	background-color: #f4f4f4 !important;
	cursor: auto;
}
/*
#order input[readonly], #order input:disabled {
	background-color: white !important;
	cursor: auto;
}
*/
#order .my-type-date[readonly],
#order .my-type-date:disabled,
#order .my-type-datetime-local[readonly],
#order .my-type-datetime-local:disabled {
	cursor: auto;
}
#order input[type="button"]:disabled {
	opacity: .5;
}
#order input[type="button"]:disabled:active {
	background: none;
	border: 1px solid #94B9D6;
	box-shadow: 1px 1px 0 #FFF;
	color: #296FA7;
}
#order .lbl_print.ui-state-active {
	background-image: none;	
}
#order .lbl_print.checked.ui-state-active {
	background-image: url(../../../img/check_1.png);
}
#fix_list {
	float: left;
}
#fix_list {
	width: 45%;
	height:90%;
}
#order #btn_fix {
	background-image: url(../../../img/ico_save.png);
	background-repeat: no-repeat;
	background-position: 10px 8px;
	background-size: 18px 15px;
}
#order #btn_fix:active {
	background-image: url(../../../img/ico_save.png);
}
#order #btn_del {
	background-image: url(../../../img/ico_close.png);
	background-repeat: no-repeat;
	background-position: 10px 8px;
	background-size: 18px 15px;
}
#order #btn_del:active {
	background-image: url(../../../img/ico_close.png);
}
.select2-container-multi .select2-choices{
    height: 27px !important;
    width:300px ;
}