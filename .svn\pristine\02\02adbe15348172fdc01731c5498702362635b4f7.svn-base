/** 施行ボード */
var appsch = appsch || {};
var appsch_adj = appsch_adj || {};
$(function () {
    "use strict";
    var utils = window.msiBbUtils;
	appsch.ext = {
        access_kbn: 2, // 2：施行情報
        init: function () {
        }
    };
    $.extend(appsch.ext, appsch_adj);
	// 画面クラスとモデルのプロパティのオブジェクト 
	appsch.pro = {
        kaisya_cd			: '.kaisya_cd',			// 会社コード 
        bumon_cd			: '.bumon_cd',			// 部門コード
        bumon_lnm			: '.bumon_lnm',			// 部門名
		seko_no				: '.seko_no',			// 施行番号 
		status_kbn_nm		: '.status_kbn_nm',		// ステータス表示名	
		sougi_ymd_sort		: '.sougi_ymd_sort',	// 葬儀日(ソート)			
        hall_cd				: '.hall_cd',			// ホールコード
        hall_nm				: '.hall_nm',			// ホール名 
        hospital_nm			: '.hospital_nm',		// 病院 
		uketuke_tanto_nm2	: '.uketuke_tanto_nm2',	// 受付担当者
		uketuke_tanto_nm	: '.uketuke_tanto_nm',	// プランナー
		seko_tanto_nm		: '.seko_tanto_nm',		// コーディネーター
		kasoba_nm			: '.kasoba_nm',			// 火葬場　
		kaso_time			: '.kaso_time',			// 火葬時間
		souke_knm			: '.souke_knm',			// 喪家名カナ
		souke_nm			: '.souke_nm',			// 葬家名
		k_knm				: '.k_knm',				// 故人名カナ
		k_nm				: '.k_nm',				// 故人名
		k_sex_kbn_nm		: '.k_sex_kbn_nm',		// 故人性別
		k_nenrei_man		: '.k_nenrei_man',		// 年齢（満）
		m_knm				: '.m_knm',				// 喪主名カナ
		m_nm				: '.m_nm',				// 喪主名
		m_zoku_kbn_nm		: '.m_zoku_kbn_nm',		// 続柄名
		death_nm			: '.death_nm',			// 命日名
		death_date			: '.death_date',		// 命日
		death_time			: '.death_time',		// 命日時間
		tuya_nm				: '.tuya_nm',			// 通夜名
		tuya_date			: '.tuya_date',			// 通夜日
		tuya_time			: '.tuya_time',			// 通夜時間
		tuya_biko			: '.tuya_biko',			// 通夜備考
		sougi_nm			: '.sougi_nm',			// 葬儀名
		sougi_ymd			: '.sougi_ymd',			// 葬儀日
		sougi_time			: '.sougi_time',		// 葬儀時間
		sougi_biko			: '.sougi_biko',		// 葬儀備考
		syonanoka			: '.syonanoka',			// 初七日
		syuha_nm			: '.syuha_nm',			// 宗派名
		jyusho_nm			: '.jyusho_nm',			// 寺院名
		kg_addr				: '.kg_addr',			// 故人住所
		syoujinage_nm		: '.syoujinage_nm',		// 精進上げ
		hs_anchi_nm			: '.hs_anchi_nm',		// 安置場所
		kaiin_nm1			: '.kaiin_nm1',			// 会員区分1
		kaiin_nm2			: '.kaiin_nm2',			// 会員区分2
		nitei_nm3			: '.nitei_nm3',			// 予定名3
		nitei_nm4			: '.nitei_nm4',			// 予定名4
		yote_date1			: '.yote_date1',		// 予定日1
		yote_date2			: '.yote_date2',		// 予定日3
		yote_date3			: '.yote_date3',		// 予定日3
		nitei_date_time3	: '.nitei_date_time3',	// 予定時間1
		nitei_date_time4	: '.nitei_date_time4',	// 予定時間2
		reikyusya			: '.reikyusya',			// 霊柩車
		syoukai_kbn			: '.syoukai_kbn',		// 紹介区分
		board_biko1			: '.board_biko1',		// ボード備考1
		board_biko2			: '.board_biko2',		// ボード備考2
		board_biko3			: '.board_biko3',		// ボード備考3
		board_biko4			: '.board_biko4',		// ボード備考4
        juchu_prc_sum       : '.juchu_prc_sum',     // 受注金額合計
        uri_prc_sum         : '.uri_prc_sum'        // 売上金額合計
	};
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                t_id: 1,					// タブID 1:施行予定表
                s_current_bumon_cd: null,	// サイドメニューの選択された部門コード
                date_term: null,			// 表示期間
                s_display_term: null,		// 表示日数
                date_today: null,			// 今日の日付 y/m/d
                s_tgt_day: null,			// 表示期間開始 y/m/d
                s_bumon: null				// 検索部門
            };
        },
        validation: {
        }
    }); // AppModel
    
    /**
     * 時刻型かチェック
     * @param value 対象文字列
     * @returns エラーメッセージ
     */
    var _chkTime  = function(value) {
      var times = value;
      var msg = null;
      if ($.msiJqlib.isNullEx2(times))  {
            return  msg;
        }      
      if ( !times.match(/^\d{1,2}\:\d{2}$/) ) {
        msg = '時刻の形式エラーです';
        return msg;
      }
      var al = times.split(":");
      if ( parseInt(al[0],10) < 0 || parseInt(al[0],10) > 23 || parseInt(al[1],10) < 0 || parseInt(al[1],10) > 59 ) {
        msg = '時刻の形式エラーです';
        return msg;
      }
      return msg;
    };
    
    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#wrapper"),
        url: '/cale/sekoyoteiseko/',
        events: {
            "click .week li a": "dateChange",
            "click #btn_reload": "refresh",
            "change #division": "refresh",
            "click #btn_today": "showToday"
        },
        bindings: {
            '#header .week .term': 'date_term',
            '#division': {
                observe: 's_bumon',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    $el.select2("val", vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            }
        },
        initialize: function () {
            this.listenTo(appsch.sekoMsiNone, 'reset', this.addAllSekoMsiNone);
            this.listenTo(appsch.sekoYtCol, 'reset', this.addAllSekoYtCol);
            this.render();
        },
        render: function () {
            this.stickit();
            // エリアコード（元々は部門コード）
            $.msiJqlib.setSelect2Com1($("#division"), ($.extend({data: $.msiJqlib.objToArray3(appsch.initData.area_select2)}, $.msiJqlib.setSelect2Default1)));
            return this;
        },
        addAllSekoMsiNone: function () {
            this.$("#calendar .data_msi ul").remove();
            this.$("#calendar .data_msi tbody").remove();
            var v = new SekoMsiViewNone();
            this.$("#calendar .data_msi").append(v.render().el);
        },
        addSekoYtOne: function (m) {
            var cls_nm = null;
            if (m.get('by_kaijo_row_end') === '0') {
                cls_nm += ' b_b_none';
            }
            if (m.get('by_hall_row_end') === '0') {
                cls_nm += ' b_b_none_hall';
            }
            var v = new SekoYtView({model: m, className: cls_nm});
            this.$("#calendar #seko_msi").append(v.render().el);
        },
        addAllSekoYtCol: function (collection) {
            this.$("#calendar #seko_msi tbody").remove();
            collection.each(this.addSekoYtOne, this);
        },
        dateChange: function (e) {
            var day = $(e.currentTarget).closest("li").data("day");
            var s_tgt_day = appsch.appModel.get("s_tgt_day");
            s_tgt_day = $.msiJqlib.addDays(s_tgt_day, day);
            appsch.appModel.set("s_tgt_day", s_tgt_day);
            this.refresh();
        },
        showToday: function () {
            appsch.appModel.set("s_tgt_day", null);
            this.refresh();
        },
        refresh: function () {
            var URL = this.url + 'list';
            $.ajax({
                url: $.msiJqlib.baseUrl() + URL,
                data: {
                    s_tgt_day: appsch.appModel.get("s_tgt_day"),
                    s_bumon: appsch.appModel.get("s_bumon"),
                    s_display_term: appsch.appModel.get("s_display_term"),
                    s_current_bumon_cd: appsch.appModel.get("s_current_bumon_cd"),
                    tab_id: appsch.appModel.get("t_id"),
                    access_kbn: appsch.ext.access_kbn
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // データ再設定
                        appsch.resetData(mydata);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        }
    });

    // 施行予定モデル
    var SekoYtModel = Backbone.Model.extend({
        defaults: function () {
            return {
                kaisya_cd			: null,	// 会社コード 
                bumon_cd			: null,	// 部門コード
                bumon_lnm			: null,	// 部門名
				seko_no				: null,	// 施行番号 
				status_kbn_nm		: null,	// ステータス表示名	
				sougi_ymd_sort		: null,	// 葬儀日(ソート)			
                hall_cd				: null,	// ホールコード
                hall_nm				: null,	// ホール名 
                hospital_nm			: null, // 病院 
				uketuke_tanto_nm2	: null,	// 受付担当者
				uketuke_tanto_nm	: null,	// プランナー
				seko_tanto_nm		: null,	// コーディネーター
				kasoba_nm			: null,	// 火葬場　
				kaso_time			: null,	// 火葬時間
				souke_knm			: null,	// 喪家名カナ
				souke_nm			: null,	// 葬家名
				k_knm				: null,	// 故人名カナ
				k_nm				: null,	// 故人名
				k_sex_kbn_nm		: null,	// 故人性別
				k_nenrei_man		: null,	// 年齢（満）
				m_knm				: null,	// 喪主名カナ
				m_nm				: null,	// 喪主名
				m_zoku_kbn_nm		: null,	// 続柄名
				death_nm			: null,	// 命日名
				death_date			: null,	// 命日
				death_time			: null,	// 命日時間
				tuya_nm				: null,	// 通夜名
				tuya_date			: null,	// 通夜日
				tuya_time			: null,	// 通夜時間
				tuya_biko			: null,	// 通夜備考
				sougi_nm			: null,	// 葬儀名
				sougi_ymd			: null,	// 葬儀日
				sougi_time			: null,	// 葬儀時間
				sougi_biko			: null,	// 葬儀備考
				syonanoka			: null,	// 初七日
				syuha_nm			: null,	// 宗派名
				jyusho_nm			: null,	// 寺院名
				kg_addr				: null,	// 故人住所
				syoujinage_nm		: null,	// 精進上げ
				hs_anchi_nm			: null,	// 安置場所
				kaiin_nm1			: null,	// 会員区分1
				kaiin_nm2			: null,	// 会員区分2
				nitei_nm3			: null,	// 予定名1
				nitei_nm4			: null,	// 予定名2
				yote_date1			: null,	// 予定日1
				yote_date2			: null,	// 予定日3
				yote_date3			: null,	// 予定日3
				nitei_date_time3	: null,	// 予定時間1
				nitei_date_time4	: null,	// 予定時間2
				reikyusya			: null,	// 霊柩車
				syoukai_kbn			: null,	// 紹介区分
				board_biko1			: null,	// ボード備考1
				board_biko2			: null,	// ボード備考2
				board_biko3			: null,	// ボード備考3
				board_biko4			: null,	// ボード備考4
                juchu_prc_sum       : null, // 受注金額合計
                uri_prc_sum         : null  // 売上金額合計
            };
		},	
		validation: {
            seko_no:    {                      // 施行番号
				required: true,
			},
			'death_nm'			: {				// 命日名
				required: false,
                maxLength: 30
			},			
			'tuya_nm'			: {				// 通夜名
				required: false,
                maxLength: 30
			},			
			'tuya_biko'		: {				// 通夜備考
				required: false,
                maxLength: 30
			},			
			'sougi_nm'		: {					// 葬儀名
				required: false,
                maxLength: 30
			},			
			'sougi_biko'		: {				// 葬儀備考
				required: false,
			},			
			'kaiin_nm2'		: {				// 会員区分2
				required: false,
                maxLength: 30
			},			
			'nitei_nm3'		: {				// 予定名1
				required: false,
                maxLength: 30
			},			
			'nitei_nm4'		: {				// 予定名2
				required: false,
                maxLength: 30
			},			
			'nitei_date_time3'	: {					// 予定時間1
                required: false,
                customFun: function(value) {
                    return _chkTime(value);
                }
			},			
			'nitei_date_time4'	: {					// 予定時間2
                required: false,
                customFun: function(value) {
                    return _chkTime(value);
                }
            },			
            'syoukai_kbn': {							// 紹介区分
				required: false,
                maxLength: 30
            },
			'board_biko1'		: {						// 備考1
				required: false,
                maxLength: 30
			},			
			'board_biko2'		: {						// 備考2
				required: false,
                maxLength: 30
			},			
			'board_biko3'		: {						// 備考3
				required: false,
                maxLength: 30
			},			
			'board_biko4'		: {						// 備考4
				required: false,
                maxLength: 30
			},			
		},
		labels: {
            seko_no         : '施行番号',
			death_nm		: '命日名',
			tuya_nm			: '通夜名',
			tuya_biko		: '通夜備考',
			sougi_nm		: '葬儀名',
			sougi_biko		: '葬儀備考',
			kaiin_nm2		: '会員備考',
			nitei_nm3		: '予定名1',
			nitei_nm4		: '予定名2',
			nitei_date_time3: '予定時間1',
			nitei_date_time4: '予定時間2',
            syoukai_kbn     : '紹介区分',
			board_biko1		: '備考1',
			board_biko2		: '備考2',
			board_biko3		:' 備考3',
			board_biko4		:' 備考4',
        }
    });

    // 施行予定コレクション
    var SekoYtCollection = Backbone.Collection.extend({
        model: SekoYtModel
    });
    //施行予定明細ビュー
    var SekoYtView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-seko-yotei').html()),
        events: {
        },
        bindings: {
            '.seko_no'			: 'seko_no',			// 施行番号
			'.status_kbn_nm'	: 'status_kbn_nm',		// ステータス表示名	
            '.hall_nm'			: 'hall_nm',			// ホール名 
			'.hospital_nm'		: 'hospital_nm',		// 病院 
			'.uketuke_tanto_nm2': 'uketuke_tanto_nm2',	// 受付担当者
			'.uketuke_tanto_nm'	: 'uketuke_tanto_nm',	// プランナー
			'.seko_tanto_nm'	: 'seko_tanto_nm',		// コーディネーター
			'.kasoba_nm'		: 'kasoba_nm',			// 火葬場　
			'.kaso_time'		: 'kaso_time',			// 火葬時間
			'.souke_knm'		: 'souke_knm',			// 喪家名カナ
			'.souke_nm'			: 'souke_nm',			// 葬家名
			'.k_knm'			: 'k_knm',				// 故人名カナ
			'.k_nm'				: 'k_nm',				// 故人名
			'.k_sex_kbn_nm'		: 'k_sex_kbn_nm',		// 故人性別
			'.death_nm'			: {	// 命日名
                observe: 'death_nm',
				events: ['change']
			},
			'.death_date'		: 'death_date',			// 命日
			'.death_time'		: 'death_time',			// 命日時間
			'.k_nenrei_man'		: 'k_nenrei_man',		// 年齢（満）
			'.m_knm'			: 'm_knm',				// 喪主名カナ
			'.m_nm'				: 'm_nm',				// 喪主名
			'.m_zoku_kbn_nm'	: 'm_zoku_kbn_nm',		// 続柄名
			'.tuya_nm'			: {						// 通夜名
                observe: 'tuya_nm',
				events: ['change']
			},			
			'.tuya_date'		: 'tuya_date',			// 通夜日
			'.tuya_time'		: 'tuya_time',			// 通夜時間
			'.tuya_biko'		: {						// 通夜備考
				observe: 'tuya_biko',
				events: ['change']
			},			
			'.sougi_nm'		: {						// 葬儀名
				observe: 'sougi_nm',
				events: ['change']
			},			
			'.sougi_ymd'		: 'sougi_ymd',			// 葬儀日
			'.sougi_time'		: 'sougi_time',			// 葬儀時間
			'.sougi_biko'		: {						// 葬儀備考
				observe: 'sougi_biko',
				events: ['change']
			},			
			'.syonanoka'		: 'syonanoka',			// 初七日
			'.syuha_nm'			: 'syuha_nm',			// 宗派名
			'.jyusho_nm'		: 'jyusho_nm',			// 寺院名
			'.kg_addr'			: 'kg_addr',			// 故人住所
			'.syoujinage_nm'	: 'syoujinage_nm',		// 精進上げ
			'.hs_anchi_nm'		: {						// 安置場所
				observe: 'hs_anchi_nm',
				events: ['change']
			},			
			'.kaiin_nm1'		: 'kaiin_nm1',			// 会員区分1
			'.kaiin_nm2'		: {						// 会員区分2
				observe: 'kaiin_nm2',
				events: ['change']
			},			
			'.nitei_nm3'		: {						// 予定名1
				observe: 'nitei_nm3',
				events: ['change']
			},			
			'.nitei_nm4'		: {						// 予定名2
				observe: 'nitei_nm4',
				events: ['change']
			},			
			'.nitei_date_time3'	: {					// 予定時間1
				observe: 'nitei_date_time3',
				events: ['change']
			},			
			'.nitei_date_time4'	: {					// 予定時間2
				observe: 'nitei_date_time4',
				events: ['change']
			},			
            '.reikyusya': 'reikyusya',					// 霊柩車
            '.syoukai_kbn': {							// 紹介区分
                observe: 'syoukai_kbn',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    $.msiJqlib.setSelect2Val($el, vals);
                },
                onGet: function (val) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        val = this.model.get('syoukai_kbn');
                    }
                    return val;
                }
            },
			'.board_biko1'		: {						// 備考1
				observe: 'board_biko1',
				events: ['change']
			},			
			'.board_biko2'		: {						// 備考2
				observe: 'board_biko2',
				events: ['change']
			},			
			'.board_biko3'		: {						// 備考3
				observe: 'board_biko3',
				events: ['change']
			},			
			'.board_biko4'		: {						// 備考4
				observe: 'board_biko4',
				events: ['change']
			},			
            '.juchu_prc_sum'    : {     // 受注金額合計
				observe: 'juchu_prc_sum',
				events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},			
            '.uri_prc_sum'     : {					// 売上金額合計
				observe: 'uri_prc_sum',
				events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},			
        },
        initialize: function () {
			Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appsch.pro, "error1"));
            this.listenTo(this.model, 'change:tuya_nm', this.saveTuyaNm);
            this.listenTo(this.model, 'change:sougi_nm', this.saveSougiNm);
            this.listenTo(this.model, 'change:death_nm', this.saveDeathNm);
            this.listenTo(this.model, 'change:tuya_biko change:sougi_biko change:kaiin_nm2', this.saveSekoBoard);
            this.listenTo(this.model, 'change:nitei_nm3 change:nitei_nm4', this.saveSekoBoard);
            this.listenTo(this.model, 'change:nitei_date_time3 change:nitei_date_time4', this.saveSekoBoard);
            this.listenTo(this.model, 'change:board_biko1 change:board_biko2', this.saveSekoBoard);
            this.listenTo(this.model, 'change:board_biko3 change:board_biko4', this.saveSekoBoard);
            this.listenTo(this.model, 'change:hs_anchi_nm', this.saveSekoBoard);
            this.listenTo(this.model, 'change:syoukai_kbn', this.saveSekoBoard);
            this.listenTo(this.model, 'change:uri_prc_sum', function () {
                utils.decoMinus(this, 'uri_prc_sum'); // 赤字を表示
            });
        },
        render: function () {
            this.$el.html(this.tmpl(this.model.toJSON()));
            this.stickit();
			// 紹介区分コンボ
            if (!$.msiJqlib.isNullEx2(appsch.initData.syoukai_kbn_sel2)) {
                var bbv = this;
				$.msiJqlib.setSelect2Com1(bbv.$(".syoukai_kbn"),
					($.extend({data: $.msiJqlib.objToArray3(appsch.initData.syoukai_kbn_sel2)}
					, $.msiJqlib.setSelect2Default1)));
			}
			var uri_prc_sum = this.model.get('uri_prc_sum')
            if (!$.msiJqlib.isNullEx2(uri_prc_sum)) {
				this.model.set('uri_prc_sum', uri_prc_sum)
			}
			var s_tgt_day = appsch.appModel.get("s_tgt_day");
			var syukan_ymd= this.model.get("syukan_ymd");
			var syukan_time= this.model.get("syukan_time");
			var sougi_ymd= this.model.get("sougi_ymd_sort");
			if (s_tgt_day == syukan_ymd)	{
				var bbv = this;
				bbv.$(".sougi_ymd").addClass("chgcolor");
				bbv.$(".sougi_time").addClass("chgcolor");
				if (syukan_ymd != sougi_ymd) {
					this.model.set('sougi_ymd', syukan_ymd)
					this.model.set('sougi_time', syukan_time)
				}	
			} else {
                if (s_tgt_day == sougi_ymd)	{
                    var bbv = this;
                    bbv.$(".sougi_ymd").addClass("chgcolor2");
                    bbv.$(".sougi_time").addClass("chgcolor2");
                }    
            }
            return this;
        },
        // 通夜名の保存処理
        saveTuyaNm: function () {
            var url = '/cale/sekoyoteiseko/savescheduletuyanm';
            this.doSave(url, {});
        },
        // 命日名の保存処理
        saveDeathNm: function () {
            var url = '/cale/sekoyoteiseko/savescheduledeathnm';
            this.doSave(url, {});
        },
        // 葬儀名の保存処理
        saveSougiNm: function () {
            var url = '/cale/sekoyoteiseko/saveschedulesouginm';
            this.doSave(url, {});
        },
        // 葬儀名の保存処理
        saveSekoBoard: function () {
            var url = '/cale/sekoyoteiseko/saveschedulesekoboard';
            this.doSave(url, {});
        },
        doSave: function (url, opt) {
            if (!this.isInputOk()) {
                return false;
            }
            var data = {
                s_tgt_day: appsch.appModel.get("s_tgt_day"),
                s_bumon: appsch.appModel.get("s_bumon"),
                s_display_term: appsch.appModel.get("s_display_term"),
                s_current_bumon_cd: appsch.appModel.get("s_current_bumon_cd"),
                tab_id: appsch.appModel.get("t_id"),
                dataSekoYt: JSON.stringify(this.model.toJSON())
            };
            data = $.extend(data, opt);
            $.ajax({
                url: $.msiJqlib.baseUrl() + url,
                data: data,
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                } );
            }
            // NG
            if ( aMsg.length > 0 ) {
                $.msiJqlib.showErr(aMsg);
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },
        doClearErr: function() {
            this.clearErr();
        },

        clearErr: function() {
            this.$el.msiErrClearAll();
        },
    });
    // スケジュールなしビュー
    var SekoMsiViewNone = Backbone.View.extend({
        tagName: 'ul',
        className: 'schedule',
        tmpl3: _.template($('#tmpl-seko-msi-none').html()), // データなし
        initialize: function () {
        },
        render: function () {
            this.$el.html(this.tmpl3());
            return this;
        }
    });
    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-json').text()));
        appsch.initData = data;
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    // 初期化処理
    appsch.sekoYtCol = new SekoYtCollection(); // 施行予定表明細
    appsch.sekoMsiNone = new SekoYtCollection();// 施行明細のデータなし
    appsch.appModel = new AppModel();
    appsch.appModel.set(data.dataApp);
    appsch.appView = new AppView({model: appsch.appModel, collection: appsch.sekoYtCol});
    // データ設定処理
    appsch.resetData = function (data) {
//        $("#wrapper").hide();
        // データ退避
        appsch.dataAll = data;
        appsch.dataVac = data.dataVac;
       // appsch.sagyoMst = data.dataSagyoMst;
        appsch.kijunYmd = data.kijunYmd;
        // APPデータを設定する
        appsch.appModel.set(data.dataApp);
		// 施行予定設定処理
		_setSekoYotei(data);
		$("#seko_msi").show();
        appsch.ext.init();
        // リサイズ発火
        setTimeout('$(window).trigger("resize")', 0);
        $("#wrapper").show();
    };
    // 施行予定表設定処理
    var _setSekoYotei = function (data) {
        if (data.dataSekoYt.length === 0) {
            appsch.sekoMsiNone.reset(); // 明細無し（スケジュール無し）
        } else {
			$("#calendar .data_msi ul").remove();
             appsch.sekoYtCol.reset(data.dataSekoYt);
        }
    };
    appsch.appView.refresh();
    // 5分間隔で自動更新
    var interval = 1000 * 60 * 5;
    var setInterval = function () {
        setTimeout(setInterval, interval);
        appsch.appView.refresh();
    };
    setInterval();
});