<?php
/// DataMapper_HachuTanpin::registerHachuDenNoOnJuchuMsi()で受注伝票明細と発注伝票の紐付けを設定する
/// 【東上】の場合は発注伝票番号(v_free1)・発注伝票明細番号(v_free5)となっている
/// このクラス内の各関数でも利用しているので書き換えること※受注伝票ヘッダのフリー項目と混同しないように

/// ※注意※複数会社管理は考慮されていない(会社コード00000000でハードコード)箇所が多数あります　完全に非対応です申し訳ない

// FIXME: (18/03/29)テストのため findHdr/findMsi のSQLで発注書区分(sbm.hachu_kbn)の絞り込みをコメントアウトしています
//          あとはHachu_HachushoritanpinControllerのsendAction処理内
//              $result = $this->sendIfValid($db, $jc_denpyo_no, $jc_msi_nos, $key, $hachu_time);
//          をコメントアウトすれば動作確認できるはず

/**
 * DataMapper_HachuCommon
 *
 * 単品発注処理 データマッパークラス【東上】
 *	■ライフシステム向け
 *		(1)	受注日の条件を追加（大量データだと遅いので）
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Otake
 * @since      2018/03/28
 * @filesource
 */

/**
 * 単品発注処理 データマッパークラス【東上】
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Otake
 * @since      2018/03/28
 */
class DataMapper_HachuTanpin extends DataMapper_Abstract {
    /**
     * ヘッダー情報取得
     * @param type $db
     * @param type $keyHash
     * @return type
     */
	public static function findHdr($db, $keyHash=[]){
		list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, [], 'refine');
		list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
		if (strlen($tailClause) <= 0) {
			$tailClause = '';
		}

//■ライフシステム	受注日の条件を追加
		$juchu_denpyo_where	=	null;
		if(isset($keyHash['__x3'])){
			$juchu_denpyo_where	=	$juchu_denpyo_where . ' AND ';
			$juchu_denpyo_where	=	$juchu_denpyo_where . ' jd.' . $keyHash['__x3'][1];
		}
		if(isset($keyHash['__x4'])){
			$juchu_denpyo_where	=	$juchu_denpyo_where . ' AND ';
			$juchu_denpyo_where	=	$juchu_denpyo_where . ' jd.' . $keyHash['__x4'][1];
		}

        $sql = <<< END_OF_SQL
WITH
-- 発注区分の存在する明細商品
-- 絞り込み用のサブクエリ
orderable_juchu_msi AS (
SELECT *
       -- 発注情報整理
       ,first_value(order_tanto_cd) OVER window_hachu_kbn AS first_order_tanto_cd -- 初期発注担当
       ,first_value(order_ymd)      OVER window_hachu_kbn AS first_order_ymd      -- 初期発注日
       ,last_value(order_tanto_cd)  OVER window_hachu_kbn AS last_order_tanto_cd  -- 最新発注担当
       ,last_value(order_ymd)       OVER window_hachu_kbn AS last_order_ymd       -- 最新発注日
  FROM (
       SELECT jd.kaisya_cd                                -- 会社コード
              ,jd.denpyo_no AS juchu_denpyo_no            -- 受注番号
              ,NULL::text AS moke_no                      -- 【東上】喪家番号 TO_CHAR(jd.n_free1, '99999999') 施行番号と1対1で紐付くユニークキーのようなもの
              ,ski.souke_nm AS moke_nm                    -- 喪家名
              ,jd.sekyu_cd                                -- 請求先コード
              ,jd.sekyu_nm                                -- 請求先名
              ,0 AS mitsumori_kbn                         -- 【東上】見積区分 jd.k_free3 0:売上 1:見積 コード名称5370
              ,CASE jd.k_free3 -- 見積区分
                    WHEN 0 THEN jd.v_free5   -- 0:売上の場合
                    WHEN 1 THEN jd.denpyo_no -- 1:見積の場合
                           ELSE NULL::text END AS mitsumori_no    -- 【東上】見積番号 単発入力で[見積区分:見積]の伝票をコピーして売上伝票作成した場合に保持するコピー元の受注伝票番号
              ,ud.uri_den_no                                -- 請求番号
              ,jdm.msi_no                                   -- 受注番号枝番
              ,jdm.shohin_cd || '：' || jdm.shohin_nm AS shohin_nm                               -- 商品名
              ,siire.siire_lnm
              ,jdm.siire_cd
              ,jd.bumon_cd                                  -- 受注部門
              ,jd.tanto_cd AS juchu_tanto_cd                -- 受注担当
              ,jd.juchu_ymd                                 -- 受注日

              ,COALESCE(sbm.hachu_kbn, -1) AS report_kbn    -- 発注書区分 コード名称0270
              ,COALESCE(rkm.ha_entry_kbn, -1) AS entry_kbn  -- 発注書入力区分 コード名称1630
              ,jdm.v_free1 AS hachu_denpyo_no               -- 【東上】発注番号
              ,jdm.v_free5 AS hachu_msi_no                  -- 【東上】発注番号枝番
              ,COALESCE(hd.sumi_order_flg, 0) AS order_flg  -- 発注フラグ 0:未発注 1:発注済
              ,hd.order_ymd                                 -- 発注日
              ,hd.tanto_cd AS order_tanto_cd                -- 発注担当
              ,COALESCE(hd.sumi_flg, 0) AS chk_order_flg    -- 発注確認フラグ
              ,hd.sumi_order_ymd AS chk_order_ymd           -- 発注確認日
              ,hd.sumi_order_tanto_cd AS chk_order_tanto_cd -- 発注確認担当

			  ,jd.v_free2	AS	nafuda_nm

         FROM juchu_denpyo jd
         LEFT JOIN seko_kihon_info ski -- 施行基本は喪家番号が設定されていれば紐付く
                ON ski.n_free1 = jd.n_free1
               AND ski.delete_flg = 0
         LEFT JOIN uriage_denpyo ud -- 【東上】売上伝票は[見積区分:売上]の場合に作成される
                ON ud.denpyo_no = jd.denpyo_no
               AND ud.delete_flg = 0
        INNER JOIN juchu_denpyo_msi jdm
                ON jdm.denpyo_no = jd.denpyo_no
               AND jdm.msi_no = jdm.msi_no
               AND jdm.delete_flg = 0

        INNER JOIN shohin_bunrui_mst sbm
                ON sbm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
               AND sbm.bumon_cd = jdm.shohin_bumon_cd
               AND sbm.dai_bunrui_cd = jdm.dai_bunrui_cd
               AND sbm.chu_bunrui_cd = jdm.chu_bunrui_cd
               AND sbm.shohin_kbn = jdm.shohin_kbn
               AND sbm.shohin_cd = jdm.shohin_cd
               AND sbm.delete_flg = 0
               AND sbm.hachu_kbn > 0 -- 発注書区分が0/NULLは除外

--2018/09/10	元に戻す	Okuyama
--        INNER JOIN		--発注書区分が設定されている商品を対象にする	2018/04/19	Okuyama
--				(
--					SELECT
--						kaisya_cd
--						,bumon_cd
--						,shohin_cd
--						,MAX(hachu_kbn)	AS	hachu_kbn
--					FROM
--						shohin_bunrui_mst
--					WHERE
--						delete_flg = 0
--					GROUP	BY
--						kaisya_cd
--						,bumon_cd
--						,shohin_cd
--				)	AS	sbm
--                ON sbm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
--               AND sbm.bumon_cd = jdm.shohin_bumon_cd
--               AND sbm.shohin_cd = jdm.shohin_cd
--               AND sbm.hachu_kbn > 0 -- 発注書区分が0/NULLは除外

         LEFT JOIN report_kanri_mst rkm
                ON rkm.ha_rp_kbn = sbm.hachu_kbn
               AND rkm.delete_flg = 0
         LEFT JOIN hachu_denpyo hd
                ON hd.denpyo_no = jdm.v_free1
               AND hd.delete_flg = 0
        LEFT JOIN siire_mst siire
                ON siire.siire_cd = jdm.siire_cd
                AND siire.delete_flg = 0
        WHERE jd.delete_flg = 0
          AND jd.data_kbn = '3' -- 単品のみ
          $juchu_denpyo_where
       ORDER BY jd.denpyo_no, jdm.disp_no
       ) refine
WHERE $whereStr
WINDOW window_hachu_kbn AS (PARTITION BY juchu_denpyo_no, order_flg ORDER BY order_ymd)
-- WINDOW window_hachu_kbn AS (PARTITION BY juchu_denpyo_no, report_kbn ORDER BY order_ymd)
),

-- 伝票番号・発注区分で集計する
--
-- > 発注伝票 フラグ系は最小値 それ以外は最新の値
-- 区分名等の付与は出力直前で
summary_orderable AS (
SELECT juchu_denpyo_no
--       ,report_kbn
       ,9 AS report_kbn --
       ,entry_kbn
       ,juchu_ymd
       ,MAX(last_order_tanto_cd) AS last_order_tanto_cd
       ,MAX(last_order_ymd) AS last_order_ymd
       ,bumon_cd
       ,juchu_tanto_cd
       ,moke_no
       ,moke_nm
       ,mitsumori_no
       ,uri_den_no
       ,sekyu_cd
       ,sekyu_nm
       ,mitsumori_kbn
       ,string_agg(shohin_nm, ', ')        AS shohin_list -- 商品を一つのカラムにまとめる
       ,MIN(COALESCE(hachu_denpyo_no, '')) AS hachu_denpyo_no -- 発注番号最小値 order_flg と同一の発注伝票とは限らない
       ,MIN(COALESCE(order_flg, 0))        AS order_flg -- 一つでも未発注があれば未発注に
       ,MIN(COALESCE(chk_order_flg, 0))    AS chk_order_flg -- 一つでも未確認があれば未確認に
       ,string_agg(siire_lnm, ', ')        AS siire_list -- 仕入を一つのカラムにまとめる

	   ,nafuda_nm

  FROM orderable_juchu_msi
 GROUP BY juchu_denpyo_no
 --         ,report_kbn
          ,entry_kbn
          ,juchu_ymd
          ,bumon_cd
          ,juchu_tanto_cd
          ,moke_no
          ,moke_nm
          ,mitsumori_no
          ,uri_den_no
          ,sekyu_cd
          ,sekyu_nm
          ,mitsumori_kbn
          ,nafuda_nm
 ORDER BY juchu_denpyo_no, report_kbn
)

-- 検索結果: 名称付与等
SELECT * FROM (
SELECT s.*,s.juchu_denpyo_no  -- 受注伝票番号
       ,s.hachu_denpyo_no -- 発注伝票番号

       ,s.juchu_ymd       -- 受注日
       ,5 AS moushi_kbn   -- 申込区分 5:単発
       ,s.report_kbn      -- 発注書区分
       ,s.entry_kbn       -- 発注書入力区分
       ,ck_report.kbn_value_lnm AS report_kbn_nm
       ,s.order_flg       -- 発注フラグ 0:未発注 1:発注済
       ,CASE s.order_flg
             WHEN 1 THEN '発注済'
                    ELSE '未発注' END  AS order_flg_nm -- 発注フラグ名
       ,s.chk_order_flg        -- 発注確認フラグ 0:未確認 1:確認済
       ,CASE s.chk_order_flg
             WHEN 1 THEN '確認済'
                    ELSE '未確認' END  AS chk_order_flg_nm -- 発注確認フラグ名
       ,s.last_order_tanto_cd  -- 発注担当者
       ,s.last_order_ymd       -- 発注日
       ,s.bumon_cd || '：' || bm.bumon_lnm AS bumon_cd      -- 受付部門名
       ,bm.bumon_lnm      -- 受付部門名
       ,s.juchu_tanto_cd || '：' || tm.tanto_nm	AS	juchu_tanto_cd  -- 受付担当者
       ,s.moke_no         -- 【東上】喪家番号
       ,s.mitsumori_no    -- 見積番号
       ,s.uri_den_no      -- 請求番号
       ,s.moke_nm         -- 喪家名
       ,s.sekyu_cd        -- 請求先コード
       ,s.sekyu_nm        -- 請求先名
       ,s.mitsumori_kbn   -- 【東上】見積区分 0:売上 1:見積 2017/12/14 ADD Otake
       ,ck_mitsu.kbn_value_lnm AS mitsumori_kbn_nm
       ,s.shohin_list     -- 商品明細
       ,s.siire_list   

	   ,s.nafuda_nm

  FROM summary_orderable s
  LEFT JOIN bumon_mst bm
    ON bm.bumon_cd = s.bumon_cd
   AND bm.delete_flg = 0
  LEFT JOIN code_nm_mst ck_mitsu
    ON ck_mitsu.code_kbn = '5370'
   AND ck_mitsu.kbn_value_cd_num = s.mitsumori_kbn
   AND ck_mitsu.delete_flg = 0
  LEFT JOIN code_nm_mst ck_report
    ON ck_report.code_kbn = '0270'
   AND ck_report.kbn_value_cd_num = s.report_kbn
   AND ck_report.delete_flg = 0

  LEFT JOIN tanto_mst	AS	tm
    ON s.juchu_tanto_cd = tm.tanto_cd
   AND tm.delete_flg = 0

) T
 $orderBy
 $tailClause
END_OF_SQL;
		$select = $db->easySelect($sql, $param);
		return $select;
	}
    /**
     * 明細情報取得
     * [見積区分:見積]は発注禁止 除外しておく
     * ウィンドウ window_hachu_divideでdense_rank()の値が同じ場合、同一の発注とみなす
     *
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public static function findMsi($db, $keyHash=[]) {
        // null,空文字 除外
        $_keyHash = array_filter($keyHash, function($value) {
            if (is_array($value)) {
                return count($value);
            } else {
                return strlen($value);
            }
        }); // null 除外
        if (array_key_exists('report_kbn', $_keyHash)) {
            $report_kbn = $_keyHash['report_kbn'];
            unset($_keyHash['report_kbn']);
        } else {
            $report_kbn = null;
        }
        list($whereStr_rk, $param_rk) = self::_setWhereReportKbn($report_kbn);
        list($whereStr, $param) = DataMapper_Utils::setWhere($_keyHash, $param_rk);
		$sql = <<<END_OF_SQL
SELECT row_number() OVER (ORDER BY jc_denpyo_no, jc_msi_no) AS row_no -- 行番号
       ,dense_rank() OVER window_hachu_divide AS bundle_id -- 同じ値の場合同一の発注書として扱う
       ,*
  FROM (
SELECT '00000000' AS kaisya_cd -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
       -- ヘッダー情報
       ,jd.denpyo_no AS jc_denpyo_no                -- 受注伝票番号
       ,jd.data_kbn AS data_kbn                     -- データ区分
       ,0 AS mitsumori_kbn                          -- 【東上】見積区分 jd.k_free3 0:売上 1:見積 コード名称5370
       ,ud.uri_den_no                               -- 売上伝番号
       ,ski.seko_no AS seko_no                      -- 施行番号
       ,NULL::text AS moke_no                       -- 【東上】喪家場号 TO_CHAR(ski.n_free1, '99999999')
       ,ski.souke_nm AS moke_nm                     -- 喪家名
       ,ski.souke_knm AS moke_knm                   -- 喪家カナ名
       ,tuya.nitei_ymd AS tuya_nitei                               -- 通夜日程
       ,COALESCE(sougi.nitei_ymd, kasou.nitei_ymd) AS sougi_nitei  -- 葬儀日程
       ,COALESCE(sougi.basho_nm, kasou.basho_nm) AS sougi_basho_nm -- 葬儀場所名

       ,bm_jc.bumon_cd AS jc_bumon_cd               -- 受付部門コード
       ,bm_jc.bumon_lnm AS jc_bumon_lnm             -- 受付部門名
       ,jd.tanto_cd AS jc_tanto_cd                  -- 受付担当コード
       ,ud.keijo_ymd

       -- 明細情報
       ,jdm.msi_no AS jc_msi_no                                   -- 受注明細番号
       ,jdm.disp_no                                               -- 表示順
       ,jdm.shohin_bumon_cd                                       -- 商品部門コード
       ,jdm.dai_bunrui_cd                                         -- 大分類コード
       ,jdm.chu_bunrui_cd                                         -- 中分類コード
       ,jdm.shohin_kbn                                            -- 商品区分
       ,skm.shohin_kbn_nm                                         -- 商品区分名
       ,jdm.shohin_cd                                             -- 商品コード
       ,jdm.shohin_nm                                             -- 商品名
       ,jdm.shohin_tkiyo_nm                                       -- 商品適用名
       --,stm.hanbai_tnk                                            -- 販売単価 juchu_tnkは参照しない(変更されている可能性がある為) ≒ 上代単価
       ,jdm.juchu_tnk                              AS hanbai_tnk  -- 販売単価
       ,hdm.hachu_tnk                                             -- 発注単価 販売単価か？
       ,COALESCE(hdm.hachu_suryo, jdm.juchu_suryo) AS hachu_suryo -- 発注数量
       ,COALESCE(hdm.tani_cd, jdm.tani_cd, sm.tani_cd) AS tani_cd -- 商品単位コード
       ,hdm.hachu_prc                                             -- 発注金額
       ,jdm.nebiki_prc                                            -- 値引額

       -- 発注情報
       ,hdm.denpyo_no AS hc_denpyo_no                                 -- 発注伝票番号
       ,hdm.msi_no AS hc_msi_no                                       -- 発注明細番号
       ,hdm.hachu_no                                                  -- 発注管理番号 使っていない
       ,hd.sumi_order_flg AS order_flg                                -- 発注フラグ
       ,hd.order_ymd                                                  -- 発注日時
       ,hd.tanto_cd AS order_tanto_cd                                 -- 発注担当者
       ,hd.sumi_flg                                                   -- 確認フラグ
       ,hd.sumi_order_ymd AS sumi_ymd                                 -- 確認日時
       ,hd.sumi_order_tanto_cd AS sumi_tanto_cd                       -- 確認担当者
       ,bm_hc.bumon_cd AS hc_bumon_cd                                 -- 発注部門コード
       ,bm_hc.bumon_lnm AS hc_bumon_lnm                               -- 発注部門名
       ,COALESCE(hd.denpyo_biko1, jd.denpyo_biko1) AS hc_denpyo_biko1 -- 発注伝票備考1
       ,COALESCE(hd.denpyo_biko2, jd.denpyo_biko2) AS hc_denpyo_biko2 -- 発注伝票備考2
       ,COALESCE(hdm.msi_biko1, jdm.msi_biko1) AS msi_biko1           -- 明細備考1
       ,COALESCE(hdm.msi_biko2, jdm.msi_biko2) AS msi_biko2           -- 明細備考2
       ,COALESCE(hdm.soko_cd, hd.soko_cd, jdm.soko_cd) AS soko_cd     -- 倉庫コード

       ,0::numeric AS chk_order_flg -- TODO: FIXME: XXX:

       -- 仕入先情報
       ,COALESCE(hd.siire_cd, jdm.siire_cd, sir.siire_cd, '') AS siire_cd    -- 仕入コード
       ,COALESCE(hd.siire_nm, jdm.siire_lnm, sir.siire_lnm, '') AS siire_lnm -- 仕入先名
       ,sir.siire_knm                                                    -- 仕入先カナ名
       ,sir.siire_snm                                                    -- 仕入先簡略名
       ,sir.tel AS siire_tel                                             -- 仕入先TEL
       ,sir.fax AS siire_fax                                             -- 仕入先FAX
       ,stm.siire_tnk                                                    -- 仕入単価
       ,sm.siire_kamoku_cd                                               -- 仕入科目コード
       ,sm.shiire_zei_kbn                                                -- 仕入課税区分

       -- 納入情報
       ,jd.v_free1 AS okuri_saki_nm -- 贈り先
       ,jd.v_free2 AS denpyo_nafuda -- 名札
       ,COALESCE(hd.delivery_kbn,   jdm.delivery_kbn,   jd.delivery_kbn)                      AS delivery_kbn   -- 納入場所区分
       ,COALESCE(hd.nonyu_cd,       jdm.nonyu_cd,       jd.nonyu_cd,       nonyu.nonyu_cd)    AS nonyu_cd       -- 納入先コード
       ,COALESCE(hd.nonyu_nm,       jdm.nonyu_nm,       jd.nonyu_nm,       nonyu.nonyu_nm)    AS nonyu_nm       -- 納入先名
       ,COALESCE(hd.nonyu_knm,      jdm.nonyu_knm,      jd.nonyu_knm,      nonyu.nonyu_knm)   AS nonyu_knm      -- 納入先名カナ
       ,COALESCE(hd.nonyu_yubin_no, jdm.nonyu_yubin_no, jd.nonyu_yubin_no, nonyu.yubin_no)    AS nonyu_yubin_no -- 納入先郵便番号
       ,COALESCE(hd.nonyu_addr1,    jdm.nonyu_addr1,    jd.nonyu_addr1,    nonyu.addr1)       AS nonyu_addr1    -- 納入先住所1
       ,COALESCE(hd.nonyu_addr2,    jdm.nonyu_addr2,    jd.nonyu_addr2,    nonyu.addr2)       AS nonyu_addr2    -- 納入先住所2
       ,COALESCE(hd.nonyu_tel,      jdm.nonyu_tel,      jd.nonyu_tel,      nonyu.tel)         AS nonyu_tel      -- 納入先電話番号
       ,COALESCE(hd.nonyu_fax,      jdm.nonyu_fax,      jd.nonyu_fax,      nonyu.fax)         AS nonyu_fax      -- 納入先FAX番号
       ,nonyu.biko1                                                                           AS nonyu_biko1    -- 納入先備考1
       ,nonyu.biko2                                                                           AS nonyu_biko2    -- 納入先備考2
       ,COALESCE(hdm.nonyu_ymd, jdm.nonyu_dt)                                                 AS nonyu_dt       -- 納入予定日時
       ,TO_CHAR(COALESCE(hdm.nonyu_ymd, hd.nonyu_dt, jdm.nonyu_dt, jd.nonyu_dt),'YYYY/MM/DD') AS ndate          -- 納入予定日
       ,TO_CHAR(COALESCE(hdm.nonyu_ymd, hd.nonyu_dt, jdm.nonyu_dt, jd.nonyu_dt), 'HH24:MI')   AS ntime          -- 納入予定時刻
       ,COALESCE(jd.v_free3)                                                                  AS nonyu_jikoku   -- 納入予定時刻
       ,jdm.nonyu_status_kbn                                                                                    -- 納入状態区分

       -- 発注書 pdf作成時に利用
       ,rkm.report_cd      -- 帳票コード
       ,rkm.ha_rp_code_kbn -- 発注書区分コード区分
       ,rkm.ha_rp_cd       -- 発注書区分コード
       ,rkm.ha_rp_kbn      -- 発注書区分
       ,rkm.ha_entry_kbn   -- 発注書入力区分
       ,rkm.ha_syori_kbn   -- 発注書処理区分

       -- TODO: FIXME: XXX: 以下施行発注情報項目
       /** 発注書その他区分
        * 0:なし
        * (12:壇払い(会場確認書)) 1:大皿 2:ランチ１ 3:ランチ２
        * (13:壇払い(出前)) 1:大皿 2:ランチ１ 3:ランチ２
        * (14:生花祭壇) 10:設営箇所 20:生花祭壇
        */
       ,0::numeric AS ha_etc_kbn -- 発注書その他区分 使っていない
       ,NULL::numeric AS hachu_den_kbn -- 発注伝票区分 0:なし(不要) 1:通常 2:赤伝 9:その他(立替金など)
       ,NULL::text AS hachu_no_moto -- コピー元発注管理番号 単品ではコピーしない
       ,NULL::numeric AS shi_k_free3 -- 返礼品：0:新規発注 1:予備使用 2:追加 （佐野）
       ,NULL::numeric AS shi_k_free4 -- 返礼品：0:通常　1:不要 2:希望　　　　　　（佐野）
       ,NULL::numeric AS shi_n_free5 -- セット商品の受注明細No.（セレモニー）
       ,0 AS shi_n_free1 -- 料理：箸の数量
       ,0 AS shi_n_free2 -- 料理：とり皿の数量
       ,0 AS shi_n_free3 -- 料理：グラスの数量

  FROM juchu_denpyo                        jd -- 受注伝票
  LEFT JOIN seko_kihon_info               ski -- 施行基本情報
         ON ski.n_free1 = jd.n_free1 -- 喪家番号
        AND ski.delete_flg = 0
  LEFT JOIN seko_nitei tuya
         ON tuya.seko_no = ski.seko_no
        AND tuya.nitei_kbn = 4 -- 4:通夜
  LEFT JOIN seko_nitei kasou
         ON kasou.seko_no = ski.seko_no
        AND kasou.nitei_kbn = 6 -- 6:火葬
  LEFT JOIN seko_nitei sougi
         ON sougi.seko_no = ski.seko_no
        AND sougi.nitei_kbn = 7 -- 7:告別式
  LEFT JOIN uriage_denpyo                  ud -- 売上伝票
         ON ud.denpyo_no = jd.denpyo_no
        AND ud.delete_flg = 0
  LEFT JOIN juchu_denpyo_msi              jdm -- 受注伝票明細
         ON jdm.denpyo_no = jd.denpyo_no
        AND jdm.delete_flg = 0
  LEFT JOIN hachu_denpyo                   hd -- 発注伝票
         ON hd.denpyo_no = jdm.v_free1 -- 【東上】発注伝票番号jdm.v_free1
        AND hd.delete_flg = 0
  LEFT JOIN hachu_denpyo_msi              hdm -- 発注伝票明細
         ON hdm.denpyo_no = jdm.v_free1 -- 【東上】発注伝票番号jdm.v_free1
        AND hdm.msi_no = jdm.v_free5::numeric -- 【東上】発注伝票明細番号jdm.v_free5
        AND hdm.delete_flg = 0
-- INNER JOIN shohin_bunrui_mst             sbm -- 商品分類マスタ
--         ON sbm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
--        AND sbm.bumon_cd = jdm.shohin_bumon_cd
--        AND sbm.dai_bunrui_cd = jdm.dai_bunrui_cd
--        AND sbm.chu_bunrui_cd = jdm.chu_bunrui_cd
--        AND sbm.shohin_kbn = jdm.shohin_kbn
--        AND sbm.shohin_cd = jdm.shohin_cd
--        AND sbm.delete_flg = 0
--        AND sbm.hachu_kbn > 0 -- 発注書区分が0/NULLは除外
INNER JOIN		--発注書区分が設定されている商品を対象にする	2018/04/19	Okuyama
		(
			SELECT
				kaisya_cd
				,bumon_cd
				,shohin_cd
				,MAX(hachu_kbn)	AS	hachu_kbn
			FROM
				shohin_bunrui_mst
			WHERE	
				delete_flg = 0
			GROUP	BY
				kaisya_cd
				,bumon_cd
				,shohin_cd
		)	AS	sbm
        ON sbm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
       AND sbm.bumon_cd = jdm.shohin_bumon_cd
       AND sbm.shohin_cd = jdm.shohin_cd
       AND sbm.hachu_kbn > 0 -- 発注書区分が0/NULLは除外

 INNER JOIN shohin_kbn_mst                skm -- 商品区分マスタ
         ON skm.shohin_kbn = jdm.shohin_kbn
--        AND skm.delete_flg = 0
 INNER JOIN shohin_mst                    sm -- 商品マスタ
         ON sm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
        AND sm.bumon_cd = jdm.shohin_bumon_cd
        AND sm.shohin_cd = jdm.shohin_cd
        AND jd.juchu_ymd BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
--        AND sm.delete_flg = 0
  LEFT JOIN shohin_tanka_mst              stm -- 商品単価マスタ
         ON stm.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
        AND stm.shohin_cd = jdm.shohin_cd
        AND stm.bumon_cd = jdm.shohin_bumon_cd
        AND jd.juchu_ymd BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
--        AND stm.delete_flg = 0
  LEFT JOIN siire_mst                     sir -- 仕入マスタ
         ON sir.siire_cd = COALESCE(hd.siire_cd, jdm.siire_cd, sm.siire_cd)
--        AND sir.delete_flg = 0
  LEFT JOIN report_kanri_mst              rkm -- 帳票管理マスタ
         ON rkm.ha_rp_kbn = sbm.hachu_kbn
        AND rkm.delete_flg = 0
  LEFT JOIN nonyu_info                   nonyu -- 納入情報
         ON nonyu.seko_no = ski.seko_no
        AND nonyu.nonyu_cd = COALESCE(hd.nonyu_cd, jdm.nonyu_cd, jd.nonyu_cd)
        AND nonyu.delete_flg = 0
  LEFT JOIN bumon_mst                    bm_jc -- 部門マスタ(受付)
         ON bm_jc.bumon_cd = jd.bumon_cd
        AND bm_jc.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
        AND bm_jc.delete_flg = 0
  LEFT JOIN bumon_mst                    bm_hc -- 部門マスタ(発注)
         ON bm_hc.bumon_cd = hd.bumon_cd
        AND bm_hc.kaisya_cd = '00000000' -- 2018/02/08 複数会社管理は考慮しない とりあえず作る
        AND bm_hc.delete_flg = 0
 WHERE jd.delete_flg = 0
   AND jd.data_kbn = 3
) T
 WHERE $whereStr
   AND $whereStr_rk
WINDOW window_hachu_divide AS ( -- このウィンドウで dense_rank() が異なる場合は別の発注書として出力する
--       ORDER BY jc_denpyo_no, hc_denpyo_no, siire_cd, report_cd, ha_syori_kbn, shiire_zei_kbn
       ORDER BY jc_denpyo_no, hc_denpyo_no, siire_cd, ha_syori_kbn, shiire_zei_kbn
)
END_OF_SQL;
		return $db->easySelect($sql, $param);
    }
    /**
     * 帳票区分ごとにwhere条件を変更したい場合利用 ※現在(18/03/22)は呼出し時引数にnullを渡しているので全区分が表示されている
     *
     * @param type $report_kbn
     * @return type
     */
    private static function _setWhereReportKbn($report_kbn) {
        $whereArr = [];
        $param = [];
        switch ($report_kbn) {
            case 9:
            case 10:
                $whereArr[] = 'ha_rp_kbn IN (9,10)';
                break;
            case 37:
                $whereArr[] = 'ha_rp_kbn = :report_kbn';
                $param['report_kbn'] = $report_kbn;
                break;
            default :
                if (!$report_kbn) {break;}
                $whereArr[] = 'ha_entry_kbn = 2';
                $whereArr[] = 'ha_rp_kbn = :report_kbn';
                $param['report_kbn'] = $report_kbn;
                break;
        }
        $whereStr = $whereArr ? implode(' AND ', $whereArr) : ' 1 = 1 ';
        return [$whereStr, $param];
    }
    /**
     * 受注番号・受注明細番号を指定して明細情報を取得(self::findMsi)する
     *
     * @param type $db
     * @param string $jc_denpyo_no 受注番号
     * @param array $jc_msi_nos 明細番号（複数指定可）
     * @return array
     */
    public static function getHachuInfo($db, $jc_denpyo_no, $jc_msi_nos) {
        $cond = ['jc_denpyo_no' => $jc_denpyo_no];
        if ($jc_msi_nos) {
            $cond += ['jc_msi_no' => DataMapper_Utils::condOneOf('jc_msi_no', $jc_msi_nos, 'jmn_')];
        }
        return self::findMsi($db, $cond);
    }
    /**
     * DataMapper_HachuTanpin::findMsiで取得したデータを同一発注書(同一の仕入先)ごとにまとめる
     * - bundle_id毎にデータをまとめる
     * - bundle_idはfind内SQLのWINDOW句参照
     *
     * @param type $findResult
     * @return type
     */
    public static function makeHachuBundles($findResult) {
        $hachuBundles = array_reduce($findResult, function ($hachuBundles, $recJuchu) {
            $hachuBundles[$recJuchu['bundle_id']][] = $recJuchu;
            return $hachuBundles;
        }, []);
        return $hachuBundles;
    }

    //**********************************************************
    // 発注伝票新規作成
    //**********************************************************
	/**
     * 発注伝票を作成する 18/03/29現在INSERTのみ実装
     * - INSERTのみなので発注済の伝票では発注取消(self::resetHachuNosOfJuchuDenpyo(),self::deleteHachuDenpyo())をしない限り再発注できないようにすること
	 * cf.App_HachuLib::makeDenpyoFromJuchu (面影なし)
     *
	 * @version 2018/03/15
	 * @param type $jc_denpyo_no 受注伝票番号
	 * @param type $jc_msi_nos 受注明細番号
	 * @param type $db_ref
	 * @return type
	 */
	public static function makeHachuDenpyo($jc_denpyo_no, $jc_msi_nos, $db_ref = null) {
		$db = $db_ref ?: Msi_Sys_DbManager::getMyDb();
		try {
            $recJuchuData = self::getHachuInfo($db, $jc_denpyo_no, $jc_msi_nos);
            if (count($recJuchuData) <= 0) {
                throw new Exception('受注データが存在しません');
            }
            // 仕入先が同じデータをまとめる このまとめたデータ毎に発注伝票を作成する
            $hachuBundles = self::makeHachuBundles($recJuchuData);
            // 発注伝票・明細作成用のデータ群精製
            $hachuDatas = array_map(function ($hachuBundle) use($db) {
                // ヘッダー精製
                $hachuHeader = self::makeHachuHeaderData($db, $hachuBundle);
                // 明細データ精製
                $HachuDetails = array_map(function ($hachuBundleMsi) use ($db) {
                    return self::makeHachuDetailData($db, $hachuBundleMsi);
                }, $hachuBundle);
                // ペアにする
                $hachuData =[
                    'hachu_denpyo' => $hachuHeader,
                    'hachu_denpyo_msi' => $HachuDetails,
                ];
                return $hachuData;
            }, $hachuBundles);
            // 発注日時 XXX:makeHachuHeaderDataの中でも行っている ちょっとずれる？
            $order_ymd = date_create()->format('Y/m/d');
            // 伝票番号付加してINSERTするだけ(UPDATEは未実装, 不要？)
            foreach ($hachuDatas as $hachu) {
                $hachuHeader = $hachu['hachu_denpyo'];
                $hachuDetails = $hachu['hachu_denpyo_msi'];
                $denpyo_no = App_ClsGetCodeNo::GetCodeNo($db, 'hachu_denpyo', 'denpyo_no', $order_ymd);
                $msi_no = 0;
                foreach ($hachuDetails as $hachuDetail) {
                    $msi_no += 1;
                    $hachuDetail['denpyo_no'] = $denpyo_no;
                    $hachuDetail['msi_no'] = $msi_no;
                    self::insertHachuDenpyoMsi($db, $hachuDetail);
                    self::registerHachuDenNoOnJuchuMsi($db, $hachuDetail);
                }
                if ($msi_no === 0) {throw new Exception('エラーが発生しました');}
                $hachuHeader['denpyo_no'] = $denpyo_no;
                self::insertHachuDepyo($db, $hachuHeader);
                unset($denpyo_no, $msi_no);
            }
		} catch (Exception $e) {
			echo $e->getTraceAsString();
			return false;
		}
		//コミット
		if (!$db_ref) {
			$db->commit();
		}
        return;
	}
    /**
     * 発注済の発注伝票insert用データを作成する
     * - 伝票番号はinsert前に設定すること
     * - 発注済みフラグ・発注担当者・発注日時をこの関数内で設定している
     * - FIXME: 特定の状況を考慮していない項目が多い
     * @param type $db
     * @param type $hachuBundle self::findの結果から同一のbundle_idのレコードを抜き出した配列 [['bundle_id'=> 11, ...], ['bundle_id'=> 11, ...], ['bundle_id'=> 11, ...], ...]
     */
    private static function makeHachuHeaderData($db, $hachuBundle) {
        if (!$hachuBundle || !$hachuBundle[0]) {throw new Exception('不正なデータです');}
        // １受注の中で異なる複数の値が存在しない項目は明細１件目を利用 発注直前に保存処理を実行しているので最新のはず
        $header = $hachuBundle[0];
        $denpyo_no = null; // insert時に設定する
        $bumon_cd  = App_Utils::getTantoBumonCd();
        $tanto_cd = App_Utils::getTantoCd();
        // 発注日時
        $order_ymd = date_create()->format('Y-m-d H:i:sP');
        // 発注金額合計（受注数量×仕入単価）
        // XXX: 積み上げてから消費税計算しているので明細側とずれる可能性あり
        // DOTO: 必要ならクロージャの中で税計算を行うこと
        // FIXME:
        $hachu_prc_sum = array_reduce($hachuBundle, function($sum, $msi) {
            return $sum + ($msi['hachu_suryo'] * $msi['siire_tnk']); // TODO: FIXME: XXX: hanbai_tnk?
        }, 0);
        // 税区分  0(非課税), 1(内税), 2(外税)
        $tax_kbn = $header['shiire_zei_kbn'];
        list($tax_code_kbn, $tax_cd) = self::getTaxCode($db, $tax_kbn, $order_ymd);
        // 消費税額の計算
        $tax_ary = App_ClsTaxLib::GetCalcTax($db, $header['keijo_ymd'], $hachu_prc_sum, $tax_kbn);
        $out_zei_prc_sum = $tax_ary['ZeiPrc'];
        $zei_cd = $tax_ary['zei_cd'];
        //仕入先の締日、支払予定日 算出
        $sime_ymd = App_DateCalc::getSiireShimeYmd($db, $order_ymd, $header['siire_cd']);
        $harai_yotei_ymd = App_DateCalc::getSiireHraiyoteiYmd($db, $sime_ymd, $header['siire_cd']);
        // 既存の発注伝票レコードがある場合
        // 基本的にこっちのデータを優先させればいいと思う 既存のデータは上書き
        // TODO: FIXME: XXX:
        // 金額関係 <-内税・非課税は考慮されていない
        $hachuHeaderData = [
            'denpyo_no' => $denpyo_no,
            'disp_denpyo_no' => 0, // xxx:？
            'data_kbn' => 3, // 単品
            'kaisya_cd' => '00000000', // 複数会社管理は考慮していない とりあえず作る
            'sumi_order_flg' => 1, // 発注フラグだと思う
            'order_ymd' => $order_ymd,
            'bumon_cd' => $bumon_cd, // 発注伝票が存在しなければhc_bumon_cdは存在しない $header['hc_bumon_cd'],
            'tanto_cd' => $tanto_cd, // 発注伝票が存在しなければorder_tanto_cdは存在しない $header['order_tanto_cd'],
            'siire_cd' => $header['siire_cd'],
            'siire_nm' => $header['siire_lnm'],
            'soko_cd' => $header['soko_cd'],
            'delivery_kbn' => $header['delivery_kbn'],
            'nonyu_cd' => $header['nonyu_cd'],
            'nonyu_nm' => $header['nonyu_nm'],
            'nonyu_knm' => $header['nonyu_knm'],
            'nonyu_yubin_no' => $header['nonyu_yubin_no'],
            'nonyu_addr1' => $header['nonyu_addr1'],
            'nonyu_addr2' => $header['nonyu_addr2'],
            'nonyu_tel' => $header['nonyu_tel'],
            'nonyu_fax' => $header['nonyu_fax'],
            'nonyu_dt' => $header['ndate'],
            'hachu_prc_sum' => $hachu_prc_sum,
            'hachu_nebk_sum' => 0, // とりあえず内・非は考慮していない
            'szei_katax_taisho_prc' => $hachu_prc_sum,
            'uzei_katax_taisho_prc' => 0, // とりあえず内・非は考慮していない
            'hitax_katax_taisho_prc' => 0, // とりあえず内・非は考慮していない
            'tax_code_kbn' => $tax_code_kbn, // "0150"固定?
            'tax_cd' => $tax_cd,
            'tax_kbn' => $tax_kbn,
            'zei_cd' => $zei_cd,
            'out_zei_prc' => $out_zei_prc_sum,
            'in_zei_prc' => 0, // とりあえず内税は考慮していない
            'denpyo_biko1' => $header['hc_denpyo_biko1'],
            'denpyo_biko2' => $header['hc_denpyo_biko2'],
            'sumi_flg' => 0, // 発注確認フラグだと思う
            'sumi_order_ymd' => null, // 発注確認日時だと思う
            'sumi_order_tanto_cd' => null, // 発注確認担当者だと思う
            'sime_ymd' => $sime_ymd,
            'harai_kbn' => 0, // 1:現金 とりあえず固定 $header参照したほうが良さげ
            'harai_yotei_ymd' => $harai_yotei_ymd,
            'sumi_hakko_flg' => 0,
            'sumi_hakko_ymd' => null,
            'sumi_hakko_tanto_cd' => null,
            'delete_flg' => 0,
        ];
        return $hachuHeaderData;
    }
    /**
     * 発注済の発注伝票明細insert用データを作成する
     * - 伝票番号,明細番号はinsert前に設定すること
     * - FIXME: 特定の状況を考慮していない項目が多い
     * - 受注伝票と紐付けのためにjc_denpyo_no,jc_msi_noを持っている unset() するか $except に指定すること
     *
     * @param type $db
     * @param type $hachuBundleMsi self::find結果のうちの１レコード
     */
    private static function makeHachuDetailData($db, $hachuBundleMsi) {
        $denpyo_no = null; //
        $msi_no = null; //
        // 発注金額合計（受注数量×仕入単価）
        $hachu_prc = $hachuBundleMsi['hachu_suryo'] * $hachuBundleMsi['siire_tnk']; // TODO: FIXME: XXX: hanbai_tnk?
        // 税区分  0(非課税), 1(内税), 2(外税)
        $tax_kbn = $hachuBundleMsi['shiire_zei_kbn'];
        // 消費税額の計算
        $tax_ary = App_ClsTaxLib::GetCalcTax($db, $hachuBundleMsi['keijo_ymd'], $hachu_prc, $tax_kbn);
        $out_zei_prc = $tax_ary['ZeiPrc'];
        $zei_cd = $tax_ary['zei_cd'];
        $hachuDetailData = [
            'denpyo_no' => $denpyo_no,
            'msi_no' => $msi_no,
            'disp_no' => $hachuBundleMsi['disp_no'],
            'denpyo_kbn' => 1, // xxx: 1でいいのか？
            'dai_bunrui_cd' => $hachuBundleMsi['dai_bunrui_cd'],
            'chu_bunrui_cd' => $hachuBundleMsi['chu_bunrui_cd'],
            'shohin_kbn' => $hachuBundleMsi['shohin_kbn'],
            'kaisya_cd' => $hachuBundleMsi['shohin_cd'],
            'shohin_bumon_cd' => $hachuBundleMsi['shohin_bumon_cd'],
            'shohin_cd' => $hachuBundleMsi['shohin_cd'],
            'shohin_nm' => $hachuBundleMsi['shohin_nm'],
            'shohin_tkiyo_nm' => $hachuBundleMsi['shohin_tkiyo_nm'],
            'shohin_tkiyo_nm2' => null,
            'hachu_suryo' => $hachuBundleMsi['hachu_suryo'],
            'tani_cd' => $hachuBundleMsi['tani_cd'],
            'jyodai_tnk' => $hachuBundleMsi['hanbai_tnk'],
            'jyodai_prc' => $hachuBundleMsi['hanbai_tnk'] * $hachuBundleMsi['hachu_suryo'],
            'hachu_tnk' => $hachuBundleMsi['siire_tnk'], // xxx: hanbai_tnk?
            'hachu_prc' => $hachu_prc,
            'nebiki_prc' => 0,
            'nonyu_ymd' => $hachuBundleMsi['nonyu_dt'],
            'nyuka_dt' => null,
            'msi_biko1' => $hachuBundleMsi['msi_biko1'],
            'msi_biko2' => $hachuBundleMsi['msi_biko2'],
            'zei_kbn' => $tax_kbn,
            'zei_cd' => $zei_cd,
            'out_zei_prc' => $out_zei_prc,
            'in_zei_prc' => 0,
            'out_zei_hasu_prc' => 0,
            'soko_cd' => $hachuBundleMsi['soko_cd'],
            'siire_kamoku_cd' => $hachuBundleMsi['siire_kamoku_cd'],
            'nyuka_suryo' => null,
            'kepin_suryo' => null,
            'henpin_suryo' => null,
            'kenpin_flg' => 0,
            'kenpin_dt' => null,
            'hachu_no' => 0, // つかわない
            'kepin_add_kbn' => 0,
            'kepin_add_msi_no' => 0,
            'delete_flg' => 0,
            // 以下,受注伝票紐付け用 unset() もしくは $except に指定すること
            'jc_denpyo_no' => $hachuBundleMsi['jc_denpyo_no'],
            'jc_msi_no' => $hachuBundleMsi['jc_msi_no'],
        ];
        return $hachuDetailData;
    }
    /**
     * 発注伝票を作成
     *
     * @param type $db
     * @param type $hachuData
     */
    private static function insertHachuDepyo($db, $hachuData) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('hachu_denpyo', $hachuData, ['jc_denpyo_no', 'jc_msi_no']);
        $db->easyExecute($sql, $param);
    }
    /**
     * 発注伝票明細を作成
     *
     * @param type $db
     * @param type $hachuMsiData
     */
    private static function insertHachuDenpyoMsi($db, $hachuMsiData) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('hachu_denpyo_msi', $hachuMsiData, ['jc_denpyo_no', 'jc_msi_no']);
        $db->easyExecute($sql, $param);
    }
    /**
     * 【東上】受注伝票明細に発注伝票番号(v_free1)と発注伝票明細番号(v_free5)を紐付ける
     *
     * @param type $db
     * @param type $recJuchu
     * @param type $hc_denpyo_no
     * @param type $hc_msi_no
     */
    private static function registerHachuDenNoOnJuchuMsi($db, $hachuMsiData) {
        $set = [
            'v_free1' => $hachuMsiData['denpyo_no'],
            'v_free5' => $hachuMsiData['msi_no'],
        ];
        $where = [
            'denpyo_no' => $hachuMsiData['jc_denpyo_no'],
            'msi_no' => $hachuMsiData['jc_msi_no']
        ];
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('juchu_denpyo_msi', $set, $where);
        $db->easyExecute($sql, $param);
    }
    /**
     * 税区分を取得する
     *
     * @param type $db
     * @param type $tax_kbn
     * @return type
     * @throws Msi_Sys_Exception_InputException
     */
    private static function getTaxCode($db, $tax_kbn) {
        $taxRec = DataMapper_CodeNmMst::findOne($db, ['code_kbn' => '0150', 'kbn_value_cd' => $tax_kbn]);
        if (!$taxRec) {
            throw new Msi_Sys_Exception_InputException("税区分 ($tax_kbn) のデータが存在しません");
        }
		$tax_code_kbn = $taxRec['code_kbn'];
        $tax_cd = $taxRec['kbn_value_cd'];

        return [$tax_code_kbn, $tax_cd];
    }

    //*******************************************************************
    // 発注取消設定
    //*******************************************************************
    /**
     * 【東上】受注伝票明細に保持されている発注伝票番号(v_free1)と発注伝票明細番号(v_free5)を空にする
     *
     * @param type $db
     * @param type $hc_denpyo_nos 発注伝票番号
     */
    public static function resetHachuNosOfJuchuDenpyo($db, $hc_denpyo_nos) {
        $keyHash = ['hc_denpyo_no' => DataMapper_Utils::condOneOf('v_free1', $hc_denpyo_nos)];
        list($whereStr, $param) = DataMapper_Utils::setWhere($keyHash, [], 'juchu_denpyo_msi');
        $sql = <<<END_OF_SQL
UPDATE juchu_denpyo_msi
   SET v_free1 = NULL
       ,v_free5 = NULL
 WHERE delete_flg = 0
   AND $whereStr
END_OF_SQL;
        return $db->easyExecute($sql,$param);
    }
    /**
     * 対象の発注伝票・明細を論理削除する
     *
     * @param type $db
     * @param type $hc_denpyo_nos
     * @return type
     */
    public static function deleteHachuDenpyo($db, $hc_denpyo_nos){
        $keyHash = ['denpyo_no' => DataMapper_Utils::condOneOf('denpyo_no', $hc_denpyo_nos)];
        list($whereStr, $param) = DataMapper_Utils::setWhere($keyHash, [], 'h');
        $sql_hd = <<<END_OF_SQL
UPDATE hachu_denpyo h
   SET delete_flg = 1
 WHERE $whereStr
END_OF_SQL;
        $sql_hdm = <<<END_OF_SQL
UPDATE hachu_denpyo_msi h
   SET delete_flg = 1
 WHERE $whereStr
END_OF_SQL;
        $db->easyExecute($sql_hd,$param);
        return $db->easyExecute($sql_hdm,$param);
    }

}
