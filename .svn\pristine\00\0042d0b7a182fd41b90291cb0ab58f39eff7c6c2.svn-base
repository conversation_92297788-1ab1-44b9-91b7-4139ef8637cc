@charset "UTF-8";
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.small {
  font-size: 85%;
}

.small2 {
  font-size: 80%;
}

.small3 {
  font-size: 75%;
}

.smallest {
  font-size: 70%;
}

.select2-container .select2-choice {
  background-image: none;
}

.select2-drop-mask {
  background-image: none;
}

.select2-dropdown-open .select2-choice {
  background-image: none;
}

.select2-container-multi .select2-choices {
  background-image: none;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background-image: none;
}

.my-akaji {
  color: red !important;
}

.my-bold {
  font-weight: bold;
}

.my-error {
  border: 1px solid red !important;
}

#order {
  position: fixed;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  top: 40px;
  left: 0;
  background-color: #dcecdc;
  box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
  line-height: 1;
  z-index: 2;
  /* cf. juchu.estimate.css */
  /*disable状態のアクティブを元のボタンの状態にする*/
}
#order a {
  text-decoration: none;
  color: inherit;
}
#order DELh2 {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 16px;
  color: #266CA5;
}
#order input {
  background-color: transparent;
}
#order input:disabled, #order input[readonly] {
  color: #545454;
  pointer: auto;
}
#order .page-title {
  margin-top: 5px;
  margin-bottom: 10px;
  float: left;
}
#order .page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #f7931e;
  font-size: 1.2em;
}
#order #searchbtnarea {
  width: 100%;
  height: 35px;
  white-space: nowrap;
  margin-top: 5px;
  margin-bottom: 10px;
  /** 検索ボタン */
  /** クリアボタン */
}
#order #searchbtnarea div {
  float: left;
}
#order #searchbtnarea input[type="button"] {
  float: right;
}
#order #searchbtnarea #btn_search {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #244b20;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94d69d;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 0 0 5px;
  background-image: url(../../../img-m1/ico_search.png);
  background-repeat: no-repeat;
  background-position: 33px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_search:active {
  background-color: #244b20;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img-m1/ico_search_2.png);
}
#order #searchbtnarea #btn_clear {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #244b20;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94d69d;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 5px 0 5px;
  background-image: url(../../../img-m1/ico_search.png);
  background-repeat: no-repeat;
  background-position: 33px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_clear:active {
  background-color: #244b20;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img-m1/ico_search_2.png);
}
#order fieldset.base_1 {
  border: 1px solid #88B1D1;
  background-color: white;
}
#order fieldset.base_2 {
  border: 1px solid #88B1D1;
  border-top: none;
  background-color: white;
  margin-bottom: 0;
}
#order fieldset.base_3 {
  border: 1px solid #88B1D1;
  border-top: none;
  background-color: white;
  margin-bottom: 10px;
}
#order #s_sougi_from {
  width: 8%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
}
#order #s_sougi_to {
  width: 8%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
}
#order #s_seikyu_ymd_from {
  width: 6%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
}
#order #s_seikyu_ymd_to {
  width: 6%;
  border-bottom: none;
  border-top: none;
  border-right: none;
  text-align: center;
  font-size: 13px;
  border-right: 1px solid #ccc;
}
#order #s_name_1 {
  width: 15%;
  border-bottom: none;
  border-top: none;
}
#order #s_code_1 {
  width: 3%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_code_2 {
  width: 3%;
  border-bottom: none;
  border-top: none;
  border-right: none;
}
#order #s_code_3 {
  border-bottom: none;
  border-top: none;
  width: 6%;
}
#order #s_name_2 {
  width: 10%;
  border-bottom: none;
  border-top: none;
}
#order .s_apply-cls {
  width: 10%;
  float: left;
}
#order .s_issue_st-cls {
  width: 10%;
  float: left;
}
#order .s_nyukin_st-cls {
  width: 10%;
  float: left;
}
#order #s_sekyu_nm {
  width: 10%;
  border-bottom: none;
  border-top: none;
}
#order #s_nafuda {
  width: 10%;
  border-bottom: none;
  border-top: none;
}
#order #s_kaijyosiki {
  width: 15%;
  border-bottom: none;
  border-top: none;
}
#order #s_staff_2 {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_staff_1 {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_staff_4 {
  width: 17%;
  border-bottom: none;
  border-top: none;
}
#order #s_bumon {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_name_3 {
  width: 10%;
  border-bottom: none;
  border-top: none;
}
#order #s_number {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order #s_seikyu_no {
  width: 10%;
  border-bottom: none;
  border-top: none;
}
#order #s_dummy_3 {
  width: 37%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
}
#order .s_status_kbn-cls {
  width: 9%;
  float: left;
}
#order .s_order_flg-cls {
  width: 9%;
  float: left;
}
#order .dlg_common {
  width: 3%;
  background-image: url(../../../img-m1/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
#order .lbl_sougi_bi {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_seikyu_ymd {
  width: 7%;
  border-left: 1px solid #CCC;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_name_1 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
}
#order .lbl_s_code {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_name_2 {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_apply {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_issue_st {
  width: 7%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
  font-size: 12px;
}
#order .lbl_s_nyukin_st {
  width: 7%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
  font-size: 12px;
}
#order .lbl_s_seikyu_no {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_sekyu_nm {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_nafuda {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
}
#order .lbl_s_kaijyosiki {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_staff_2 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
  border-left: 1px solid #CCC;
}
#order .lbl_s_staff_1 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
  border-left: 1px solid #CCC;
}
#order .lbl_s_staff_4 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 12px;
  border-left: 1px solid #CCC;
}
#order .lbl_s_bumon {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_name_3 {
  width: 7%;
  border-bottom: none;
  border-top: none;
  border-left: 1px solid #CCC;
}
#order .lbl_s_status_kbn {
  width: 7%;
  border-bottom: none;
  border-top: none;
  font-size: 11px;
}
#order .lbl_s_order_flg {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_s_number {
  width: 7%;
  border-left: 1px solid #CCC;
  border-bottom: none;
  border-top: none;
}
#order .day-bar {
  width: 2%;
  text-align: center;
  padding-right: 0;
  padding-left: 0;
  border: none;
  color: #999;
}
#order .code-bar {
  width: 0.5%;
  text-align: center;
  padding-right: 0;
  padding-left: 0;
  border: none;
  color: #999;
}
#order .result-list {
  height: 80%;
  overflow: hidden;
}
#order .result-list .header {
  overflow-y: scroll;
}
#order .result-list .list {
  overflow-y: scroll;
  height: 80%;
  padding-bottom: 1px;
}
#order .header table, #order .list table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background: none;
  background-color: #f4fbe8;
  font-color: #FFF;
  font-size: 13px;
}
#order .header table td, #order .list table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #244b20;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
  padding: 0 5px;
}
#order .header .row, #order .list .row {
  /* コントロール */
  width: 5%;
  border-left: 1px solid #88B1D1;
  border-top: 1px solid #88B1D1;
}
#order .header .seikyu_no, #order .list .seikyu_no {
  /* 請求No */
  width: 10%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .seikyu_ymd, #order .list .seikyu_ymd {
  /* 請求日 */
  width: 9%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .apply, #order .list .apply {
  /* 申込区分 */
  width: 5%;
  border-top: 1px solid #88B1D1;
}
#order .header .sekyu_cd, #order .list .sekyu_cd {
  /* 請求先コード */
  width: 10%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .sekyu_nm, #order .list .sekyu_nm {
  /* 請求先名 */
  width: 10%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .seikyu_addr, #order .list .seikyu_addr {
  /* 請求先住所 */
  width: 14%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .seikyu_prc, #order .list .seikyu_prc {
  /* 売掛金額 */
  width: 7%;
  border-top: 1px solid #88B1D1;
}
#order .header .nyukin_prc, #order .list .nyukin_prc {
  /* 入金額 */
  width: 7%;
  border-top: 1px solid #88B1D1;
}
#order .header .seikyu_zan, #order .list .seikyu_zan {
  /* 売掛残高 */
  width: 7%;
  border-top: 1px solid #88B1D1;
}
#order .header .bumon, #order .list .bumon {
  /* 売掛部門名 */
  width: 8%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .issue_st, #order .list .issue_st {
  /* 請求状況 */
  width: 5%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .sekkyu_kaisu, #order .list .sekkyu_kaisu {
  /* 請求回数 */
  width: 10%;
}
#order .header .sekyu_tel, #order .list .sekyu_tel {
  /* 電話番号 */
  width: 10%;
}
#order .header .zen_seikyu_ymd, #order .list .zen_seikyu_ymd {
  /* 前回請求日 */
  width: 9%;
}
#order .header .nafuda, #order .list .nafuda {
  /* 名札 */
  width: 14%;
}
#order .header .tanto, #order .list .tanto {
  /* 入力担当者 */
  width: 8%;
}
#order .header .nyukin_st, #order .list .nyukin_st {
  /* 入金区分 */
  width: 5%;
  border-bottom: 1px dotted #88B1D1;
}
#order .list table {
  background-color: #FFF;
}
#order .list table tr.even {
  background-color: #F5F9FD;
}
#order .list table .sekyu_nm, #order .list table .seikyu_addr, #order .list table .bumon, #order .list table .sekyu_tel, #order .list table .nafuda,
#order .list table .tanto {
  text-align: left;
}
#order .list table .seikyu_prc, #order .list table .nyukin_prc, #order .list table .seikyu_zan, #order .list table .sekkyu_kaisu {
  text-align: right;
}
#order .list table tr.row-selected {
  background-color: #65ab73;
}
#order .list table tr.row-selected td {
  color: white;
  border-color: white;
}
#order .list table tr.row-selected td.row {
  border-left: 1px solid #88B1D1;
}
#order .list table tr.row-selected td.kojin_nm, #order .list table tr.row-selected td.moshu_nm {
  border-right: 1px solid #88B1D1;
}
#order .list table tr.row-selected td.row {
  background-image: url(../../../img-m1/check_1.png);
  background-repeat: no-repeat;
  background-position: center top;
}
#order table td.chkAllToggle {
  background-image: url(../../../img-m1/check_1.png);
  background-repeat: no-repeat;
  background-position: center top;
  cursor: pointer;
}
#order table td.chkAllToggle.selected {
  background-image: url(../../../img-m1/check_3.png);
}
#order table td.msg {
  border: 1px solid #88B1D1;
}
#order table td.my-hover {
  cursor: pointer;
  background-color: peachpuff !important;
}
#order .select2-choice, #order .select-container {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
  border-left: none;
}
#order .select2-choices {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
}
#order .select2-arrow {
  border-radius: 0px !important;
}
#order .select2-container-multi .select2-choices {
  height: 28px !important;
}
#order fieldset.base_1 .select2-choice, #order fieldset.base_1 .select-container, #order fieldset.base_2 .select2-choice, #order fieldset.base_2 .select-container, #order fieldset.base_3 .select2-choice, #order fieldset.base_3 .select-container {
  height: 30px !important;
  border-top: none;
  border-bottom: none;
  border-right: none;
}
#order .my-bold {
  font-weight: bold;
}
#order .my-error {
  border: 1px solid red !important;
}
#order input[type="button"]:disabled {
  opacity: .5;
}
#order input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94d69d;
  box-shadow: 1px 1px 0 #FFF;
  color: #244b20;
}
#order .dlg_from{
    border-bottom: none;
    border-right: none;
}
#order .dlg_to{
    border-right: none;
    border-bottom: none;
}
