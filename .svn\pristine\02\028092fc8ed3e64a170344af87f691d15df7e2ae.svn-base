<?php

/**
 * Logic_SeikyuDenpyoBun
 *
 * 請求伝票（分割） 処理
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Kobayashi
 * @since      2020/08/xx copy from Logic_JuchuDenpyoBun
 * @filesource
 */

/**
 * 請求伝票（分割） 処理
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Sai
 * @since      2018/03/xx
 * @version    2020/08/xx Kobayashi 博全社向けに改修
 */
class Logic_SeikyuDenpyoBun {

    /** 受注伝票（分割） データ区分
     * 葬儀の場合 3：単品を使用　
     * 別注品の場合 4:別注品を使用
     */
    static private $_data_kbn_betu = 4;

    /** 処理モード 1:新規 */
    static private $_shori_mode_new = 1;
    /** 処理モード 2:変更 */
    static private $_shori_mode_chg = 2;

    /** 分割合算区分 0:通常 */
    static private $_bun_gas_kbn_tsujo = 0;
    /** 分割合算区分 1:分割元 */
    static private $_bun_gas_kbn_moto = 1;
    /** 分割合算区分 2:分割先 */
    static private $_bun_gas_kbn_saki = 2;
    /** 回収区分 array(支払方法=>回収区分) */
    static private $_kaishu_kbn_array = array('1' => '0','2' => '1','6' => '1');
    /** 税区分 1:内税 */
    static private $_zei_kbn_uchi = 1;

    /**
     * 請求伝票（分割） の更新・登録
     *
     * <AUTHOR> Kobayashi
     * @since      2020/10/05
     * @param      array  $data データ. 明細は $data['_dtl_']に含まれる 削除明細は$data['_dtl_del_']に含まれる
     * @return     array $data 登録データ
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function upsert($data) {
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyu_den_no_moto = $data['seikyu_den_no'];   // 元伝票No
        $shori_mode = $data['shori_mode'];              // 処理モード 1:新規分割　2:修正
        
        $seikyu_den_moto = array();
        $select = $db->easySelect( <<< END_OF_SQL
            SELECT *
            FROM seikyu_denpyo den
            WHERE den.seikyu_den_no = :seikyu_den_no
                AND den.delete_flg = 0
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no_moto));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $seikyu_den_moto = $select[0];
        } else {
            throw new Msi_Sys_Exception_InputException("元伝票が存在しません");
        }
        if($shori_mode == static::$_shori_mode_chg){
            $seikyu_den_saki = DataMapper_SeikyuDenpyo::findSeikyuDenpyo($db, array('bun_gas_kbn_num' =>2, 'bun_gas_seikyu_den_no' =>$seikyu_den_no_moto));
            if (Msi_Sys_Utils::myCount($select) <= 0) {
                throw new Msi_Sys_Exception_InputException("分割先伝票が存在しません");
            }
        }

        // 請求承認チェック
        if($shori_mode == static::$_shori_mode_new){
            if (isset($seikyu_den_moto['seikyu_approval_status']) && $seikyu_den_moto['seikyu_approval_status'] === '1') {
                throw new Msi_Sys_Exception_InputException("請求番号($seikyu_den_no_moto)は請求承認済のため処理できません");
            }
        } else if($shori_mode == static::$_shori_mode_chg) {
//            foreach ($seikyu_den_saki as $val_den) { -- 2021/08/31 承認済でも変更保存可能に変更
//                if (isset($val_den['seikyu_approval_status']) && $val_den['seikyu_approval_status'] === '1') {
//                    throw new Msi_Sys_Exception_InputException("請求番号($seikyu_den_no_moto)は請求承認済のため処理できません");
//                }
//            }
        }
        // 削除処理
        static::delete($db, $data['_dtl_del_']);
        
        // 金額保存フラグ
        $seikyu_approval_status = FALSE;
        if(in_array( '1', array_column( $data['_dtl_'], 'seikyu_approval_status'))){ // 分割先の請求承認フラグ(1件でも承認されていればTRUE)
            $seikyu_approval_status = TRUE;
        }
        
        // データ登録
        $hdrData = array();
        $cnt = 0;
        $moto_upd_flg = FALSE;
        // 請求伝票(分割先)の登録
        foreach ($data['_dtl_'] as $dtl) {
            $juchu_prc_sum = 0;   // 受注金額合計
            $uzei_katax_taisho_prc = 0; // 内税課税対象額
            $in_zei_prc = 0; // 内税額
            
            // 伝票番号
            $seikyu_den_no_saki = Msi_Sys_Utils::emptyToNull($dtl['seikyu_den_no']);
            if($seikyu_den_no_moto === $seikyu_den_no_saki){
                $seikyu_den_no_saki = null;
            }
            if (strlen($seikyu_den_no_saki) <= 0) { // 新規伝票
                $isDtlUpdCtxt = false;
                $seikyu_den_no_saki = App_ClsGetCodeNo::GetCodeNo($db, 'seikyu_denpyo', 'seikyu_den_no', $seikyu_den_moto['juchu_ymd']); // 新規請求番号取得
            } else {
                $isDtlUpdCtxt = true;
            }

            $sekyuData['seko_no'] = $seikyu_den_moto['seko_no'];
            if(isset($dtl['sekyu_dlg_flag']) && $dtl['sekyu_dlg_flag']){    //請求先データをダイアログの中から設定したときだけ「SEKYU_SAKI_INFO」テーブルに保存する
                $sekyuRec = DataMapper_SekyuSakiInfo::upsert($db, $sekyuData);
            }
            // 請求明細伝票No 最大値
            $maxMsiNo = static::_getMaxdenpyoMsiNo($db, $seikyu_den_no_saki);
            $dtlRec = array();
            
            // 消費税コード
            $zeiArray = array();
            if($data['head_h_cd']){
                $zeiArray[] = array('zei_kbn' =>0, 'reduced_tax_rate' =>1, 'zei_cd' => $data['head_h_cd'], 'prc' => $dtl['h_prc']);
            }
            if($data['head_hyojun_cd']){
                $zeiArray[] = array('zei_kbn' =>1, 'reduced_tax_rate' =>1, 'zei_cd' => $data['head_hyojun_cd'], 'prc' => $dtl['hyojun_prc']);
            }
            if($data['head_keigen_cd']){
                $zeiArray[] = array('zei_kbn' =>1, 'reduced_tax_rate' =>2, 'zei_cd' => $data['head_keigen_cd'], 'prc' => $dtl['keigen_prc']);
            }

            $save_cnt = 0; // 登録行
            $msi_cnt = $maxMsiNo; // 現時点の明細行
            foreach ($zeiArray as $zei) { // 税率毎に明細行を作成
                $save_cnt ++;
                $dtlRec['seikyu_den_no'] = $seikyu_den_no_saki;
                if(isset($dtl['msi_no'])){
                    $msi_no = Msi_Sys_Utils::emptyToNull($dtl['msi_no']);
                }
                // 請求伝票明細登録
                if ($save_cnt > $msi_cnt) {   // 明細行は追加
                    // 明細No生成
                    ++$maxMsiNo;
                    $msi_no = $maxMsiNo;

                    $dtlRec['msi_no'] = $msi_no;
                    $dtlRec['disp_no'] = $msi_no;
                    $dtlRec['data_sbt'] = 1;                // データ種別  1：葬送儀礼(ダミー)
                    $dtlRec['bumon_cd'] = $seikyu_den_moto['bumon_cd'];   // 売上部門コード
                    $dtlRec['kaisya_cd'] = $seikyu_den_moto['kaisya_cd']; // 会社コード
                    $dtlRec['seko_no'] = $seikyu_den_moto['seko_no'];          // 施行番号
                    $dtlRec['seko_no_sub'] = $seikyu_den_moto['seko_no_sub'];  // 施行番号（枝番）
                    $dtlRec['denpyo_kbn'] = 1;              // 伝票区分  1：受注

                    // 商品 -- ダミーを設定
                    $dtlRec['dai_bunrui_cd'] = '0000';
                    $dtlRec['chu_bunrui_cd'] = '0000';
                    $dtlRec['shohin_kbn'] = '0000';
                    $dtlRec['shohin_cd'] = '00000000';
                    $dtlRec['shohin_nm'] = '';

                    $dtlRec['zei_kbn'] = $zei['zei_kbn'];   // 消費税区分
                    $dtlRec['zei_cd'] = $zei['zei_cd'];     // 消費税コード

                    $dtlRec['juchu_suryo'] = 1;             // 数量
                    $dtlRec['uri_tnk'] = $zei['prc'];       // 単価
                    $dtlRec['nebiki_prc'] = 0;              // 値引額
                    $dtlRec['uri_prc'] = $zei['prc'];       // 受注金額
                    $dtlRec['gen_tnk'] = 0;                 // 原価単価
                    $dtlRec['gen_gaku'] = 0;                // 原価金額
                    $dtlRec['arari_gaku'] = 0;              // 粗利益額
                    $dtlRec['add_cost'] = 0;                // セット外費用
                    list($zei_rtu, $zei_hasu_kbn, $zei['reduced_tax_rate']) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei["zei_cd"]);
                    $zei_prc = App_ClsTaxLib::CalcTax($zei['prc'], $zei['zei_kbn'], $zei_rtu, $zei_hasu_kbn);
                    $dtlRec['in_zei_prc'] = $zei_prc['ZeiPrc'];  // 消費税額

                    list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo_msi', $dtlRec);
                    $db->easyExecute($sql, $param);
                } else {     // 明細行は更新
                    $dtlCur = static::_findDtl($db, $seikyu_den_no_saki, $save_cnt);
                    if ($dtlCur === null) {
                        throw new Msi_Sys_Exception_InputException("明細No ($save_cnt) のデータが存在しません");
                    }

                    $dtlRec['zei_kbn'] = $zei['zei_kbn'];   // 消費税区分
                    $dtlRec['zei_cd'] = $zei['zei_cd'];     // 消費税コード
                    $dtlRec['uri_tnk'] = $zei['prc'];       // 単価
                    $dtlRec['uri_prc'] = $zei['prc'];       // 受注金額
                    list($zei_rtu, $zei_hasu_kbn, $zei['reduced_tax_rate']) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei["zei_cd"]);
                    $zei_prc = App_ClsTaxLib::CalcTax($zei['prc'], $zei['zei_kbn'], $zei_rtu, $zei_hasu_kbn);
                    $dtlRec['in_zei_prc'] = $zei_prc['ZeiPrc'];  // 消費税額

                    $where = array('seikyu_den_no' => $seikyu_den_no_saki, 'msi_no' => $save_cnt);

                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo_msi', $dtlRec, $where);
                    $cnt += $db->easyExecute($sql, $param);
                }
                // ヘッダー用
                $juchu_prc_sum += $zei['prc'];
                $uzei_katax_taisho_prc += $zei['prc'];
                $in_zei_prc += $zei_prc['ZeiPrc'];
            }

            $dtl['seikyu_den_no'] = $seikyu_den_no_saki;
            $dtl['data_kbn'] = $seikyu_den_moto['data_kbn'];

            // 請求請求先情報
            $seikyuSeikyuSaki = static::_filterSeikyuSekyuSaki($dtl);
            // 除外データ
            $except1 = array('delete_flg', '_req_id', '_cre_user', '_cre_ts', '_mod_user', '_mod_ts', '_mod_cnt');
            
            // 請求伝票登録
            if($isDtlUpdCtxt){ // 更新
                // 請求伝票を更新
                if ($seikyu_approval_status === FALSE) { // 請求未承認
                    $hdrData['uri_prc_sum'] = $juchu_prc_sum;
                    $hdrData['seikyu_zan'] = $juchu_prc_sum;
                    $hdrData['uzei_katax_taisho_prc'] = $juchu_prc_sum;
                    $hdrData['in_zei_prc'] = $in_zei_prc;
                }
                $hdrData['kaishu_ymd'] = $dtl['kaishu_ymd'];
                if($dtl['soufu_kbn'] == '1'){ // 郵送
                    $seikyu_post_kbn = 0;
                } else if($dtl['soufu_kbn'] == '2'){ // 不要
                    $seikyu_post_kbn = 1;
                }
                if(isset($seikyu_post_kbn)){
                    $hdrData['seikyu_post_kbn'] = $seikyu_post_kbn;
                }

                $where1['seikyu_den_no'] = $seikyu_den_no_saki;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $hdrData, $where1);
                $cnt += $db->easyExecute($sql, $param);
                
                // 請求請求先情報を更新 (請求先住所を請求送付先住所に同期)
                $seikyuSeikyuSaki['soufu_yubin_no'] = $seikyuSeikyuSaki['sekyu_yubin_no'];
                $seikyuSeikyuSaki['soufu_addr1'] = $seikyuSeikyuSaki['sekyu_addr1'];
                $seikyuSeikyuSaki['soufu_addr2'] = $seikyuSeikyuSaki['sekyu_addr2'];
                $seikyuSeikyuSaki['soufu_tel'] = $seikyuSeikyuSaki['sekyu_tel'];
                list($sql2, $param2) = DataMapper_Utils::makeUpdateSQL('seikyu_sekyu_saki_info', $seikyuSeikyuSaki, $where1);
                $cnt += $db->easyExecute($sql2, $param2);
            } else { // 新規
                if($moto_upd_flg === FALSE){
                    // 分割元伝票のステータスを更新
                    $hdrData1['bun_gas_kbn_num'] = static::$_bun_gas_kbn_moto;
                    $where2['seikyu_den_no'] = $seikyu_den_no_moto;
                    list($sql1, $param1) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $hdrData1, $where2);
                    $cnt += $db->easyExecute($sql1, $param1);
                    $moto_upd_flg = TRUE;
                }

                // 請求伝票を追加
                $hdrData2 = $seikyu_den_moto;
                $hdrData2['seikyu_den_no'] = $seikyu_den_no_saki;
                $hdrData2['uri_prc_sum'] = $juchu_prc_sum;
                $hdrData2['seikyu_zan'] = $juchu_prc_sum;
                $hdrData2['uri_hepn_sum'] = 0;
                $hdrData2['uri_nebk_sum'] = 0;
                $hdrData2['szei_katax_taisho_prc'] = 0;
                $hdrData2['uzei_katax_taisho_prc'] = $juchu_prc_sum;
                $hdrData2['hitax_katax_taisho_prc'] = 0;
                $hdrData2['out_zei_prc'] = 0;
                $hdrData2['in_zei_prc'] = $in_zei_prc;
                $hdrData2['hoshi_prc_sum'] = 0;
                $hdrData2['sougi_keiyaku_prc'] = 0;
                $hdrData2['sougi_harai_prc'] = 0;
                $hdrData2['sougi_wari_prc'] = 0;
                $hdrData2['sougi_zei_sagaku_prc'] = 0;
                $hdrData2['sougi_meigi_chg_cost'] = 0;
                $hdrData2['sougi_meigi_chg_cost_zei'] = 0;
                $hdrData2['sougi_early_use_cost'] = 0;
                $hdrData2['sougi_early_use_cost_zei'] = 0;
                $hdrData2['etc_harai_prc'] = 0;
                $hdrData2['bun_gas_kbn_num'] = static::$_bun_gas_kbn_saki;
                $hdrData2['bun_gas_seikyu_den_no'] = $seikyu_den_no_moto;
                $hdrData2['kaishu_ymd'] = $dtl['kaishu_ymd'];
                if($dtl['soufu_kbn'] == '1'){ // 郵送
                    $seikyu_post_kbn = 0;
                } else if($dtl['soufu_kbn'] == '2'){ // 不要
                    $seikyu_post_kbn = 1;
                }
                if(isset($seikyu_post_kbn)){
                    $hdrData2['seikyu_post_kbn'] = $seikyu_post_kbn;
                }

                list($sql2, $param2) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo', $hdrData2, $except1);
                $cnt += $db->easyExecute($sql2, $param2);
                
                // 請求請求先情報を追加 (請求先住所を請求送付先住所に同期)
                $seikyuSeikyuSaki['seikyu_den_no'] = $seikyu_den_no_saki;
                $seikyuSeikyuSaki['seq_no'] = 1;
                $seikyuSeikyuSaki['seko_no'] = $seikyu_den_moto['seko_no'];
                $seikyuSeikyuSaki['soufu_yubin_no'] = $seikyuSeikyuSaki['sekyu_yubin_no'];
                $seikyuSeikyuSaki['soufu_addr1'] = $seikyuSeikyuSaki['sekyu_addr1'];
                $seikyuSeikyuSaki['soufu_addr2'] = $seikyuSeikyuSaki['sekyu_addr2'];
                $seikyuSeikyuSaki['soufu_tel'] = $seikyuSeikyuSaki['sekyu_tel'];
                list($sql3, $param3) = DataMapper_Utils::makeInsertSQL('seikyu_sekyu_saki_info', $seikyuSeikyuSaki);
                $cnt += $db->easyExecute($sql3, $param3);
            }
        }

        // データ確定
        $db->commit();

//        $dataNew = static::getUriageDenpyou(array('bun_gas_seikyu_den_no' => $seikyu_den_moto, 'bun_gas_kbn_num' => static::$_bun_gas_kbn_saki));
        $dataNew = static::getDateDEL($db, $seikyu_den_no_moto);

        return $dataNew;
    }

    /**
     * 分割先伝票取得
     *
     * <AUTHOR> Kobayashi
     * @since  2020/08/xx
     * @param array   $db
     * @param $seikyu_den_no_moto 分割元伝票No
     * @return void
     */
    public function getDateDEL($db, $seikyu_den_no_moto)
    {
        $where['bun_gas_kbn_num'] = 2; // 分割先伝票
        $where['bun_gas_seikyu_den_no'] = $seikyu_den_no_moto;
        $dataSeikyuDenpyo2 = DataMapper_SeikyuDenpyo::find($db, $where);
        
        $result = array();
        foreach ($dataSeikyuDenpyo2 as $val2) {
            $dataSeikyuSeikyu = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $val2['seikyu_den_no']));
            if(Msi_Sys_Utils::myCount($dataSeikyuSeikyu) > 0){
                $seikyuseikyu = $dataSeikyuSeikyu[0];
            }
            $dataSeikyuDenpyoMsi = DataMapper_SeikyuDenpyo::findDenpyoMsi($db, $val2['seikyu_den_no']);
            foreach ($dataSeikyuDenpyoMsi as $value) {
                $zei = App_ClsTaxLib::GetTaxInfoZeiCd2($db, $value['zei_cd']);
                if ($value['zei_kbn'] == 0) {  // 非課税
                    $seikyuseikyu['h_prc'] = $value['taisho_gaku'] + $value['zei_gaku'];
                } else {
                    if ($zei['reduced_tax_rate'] == 1) { // 標準税
                        $seikyuseikyu['hyojun_prc'] = $value['uri_prc'];
                    } else {    // 軽減税
                        $seikyuseikyu['keigen_prc'] = $value['uri_prc'];
                    }
                }
            }
            $seikyuseikyu['b_uri_prc'] = $val2['uri_prc_sum'];
            
            $seikyuseikyu['seikyu_approval_status'] = $val2['seikyu_approval_status'];
            
            $result[] = $seikyuseikyu;
        }
        return $result;
    }


    /**
     * 月次確定チェック処理
     *
     * <AUTHOR> Sai
     * @since 2018/03/xx
     * @param Msi_Sys_Db $db
     * @param string $keijo_ymd 売上計上日
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     * @return 
     */
    private static function checkDataFix($db, $keijo_ymd) {
        // 月次確定チェック
        $fixInfo = DataMapper_DataFixTable::findOne($db, array('fix_kbn' => 1));
        if (Msi_Sys_Utils::myCount($fixInfo) > 0) {
            if ($keijo_ymd <= $fixInfo['fix_date_ymd']) {
                throw new Msi_Sys_Exception_InputException("月次確定済みのため、処理できません");
            }
        }
    }

    /**
     * 受注伝票（分割） 削除
     *
     * <AUTHOR> Sai
     * @since      2018/03/xx
     * @param      Msi_Sys_Db $db
     * @param      string  $data データ. 明細は $data['_dtl_']に含まれる 削除明細は$data['_dtl_del_']に含まれる
     * @return     array $data データ
     */
    public static function clearAll($data) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyu_den_no = $data['seikyu_den_no'];
        $seikyu_den = array();
        $cond['seikyu_den_no'] = $seikyu_den_no;
        $select = DataMapper_SeikyuDenpyo::findSeikyuDenpyo($db, $cond); // 元伝票
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $seikyu_den = $select[0];
        } else {
            throw new Msi_Sys_Exception_InputException("請求番号($seikyu_den_no)の伝票(分割元伝票)が存在しません");
        }
        // 受注日
        $juchu_ymd = $seikyu_den['juchu_ymd_fm'];
        if (strlen($juchu_ymd) <= 0) {
            throw new Msi_Sys_Exception_InputException("受注日エラー");
        }
        // 売上計上日
        $keijo_ymd = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($seikyu_den['keijo_ymd_fm']), 'DATE2');
        if (strlen($keijo_ymd) <= 0) {
            $keijo_ymd = $juchu_ymd;
        }

        // 月次確定チェック
        static::checkDataFix($db, $keijo_ymd);

        // 分割明細削除
        $dataDel = DataMapper_SeikyuDenpyo::findSeikyuDenpyo($db, array('bun_gas_seikyu_den_no' => $seikyu_den_no, 'bun_gas_kbn_num' => static::$_bun_gas_kbn_saki));
        $cnt += static::delete($db, $dataDel);

        // 請求伝票の請求分割合算区分と分割合算伝票番号更新処理
        $seikyu_den_f['bun_gas_kbn_num'] = static::$_bun_gas_kbn_tsujo;
        list($sql_f1, $param_f1) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $seikyu_den_f, array('seikyu_den_no' => $seikyu_den_no));
        $cnt += $db->easyExecute($sql_f1, $param_f1);
        $db->commit();

        $dataNew = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $seikyu_den_no));
        return $dataNew;
    }

    /**
     * 受注伝票（分割） 削除
     *
     * <AUTHOR> Sai
     * @since      2018/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array  $data 明細削除データ
     * @return     
     */
    private static function delete($db, $data) {
        $cnt = 0;
        foreach ($data as $onemsi) {
            $seikyu_den_no = Msi_Sys_Utils::emptyToNull($onemsi['seikyu_den_no']);
            $seikyu_den = array();
            $cond['seikyu_den_no'] = $seikyu_den_no;
            $select = DataMapper_SeikyuDenpyo::findSeikyuDenpyo($db, $cond);
            if (Msi_Sys_Utils::myCount($select) > 0) {
                $seikyu_den = $select[0];
            } else {
                throw new Msi_Sys_Exception_InputException("分割請求番号($seikyu_den_no)請求明細が存在しません");
            }
            // 分割先に入金がある場合
            if ($seikyu_den['nyukin_prc'] > 0) {
                throw new Msi_Sys_Exception_InputException("分割請求番号($seikyu_den_no)は入金済みのため、処理できません");
            }
            if ($seikyu_den_no !== null) {
                $cnt += $db->easyExecute(<<< END_OF_SQL
UPDATE seikyu_denpyo
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no));

                $cnt += $db->easyExecute(<<< END_OF_SQL
UPDATE seikyu_denpyo_msi
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no));

                $cnt += $db->easyExecute(<<< END_OF_SQL
UPDATE seikyu_sekyu_saki_info
   SET delete_flg = 1
 WHERE delete_flg = 0
   AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no));
                // 請求伝票の削除処理	
                Logic_DenpyoHistoryMake::SeikyuHistoryDelete($db, $seikyu_den_no);
                // 入庫出庫伝票、明細のの削除処理 2015/01/17 ADD Kayo
//                Logic_SyukoDenpyoMake::NyukoShukoDenpyo_Delete($db, $onemsi['uri_den_no']);
            }
        }
        return $cnt;
    }

    /**
     * 請求伝票明細 の１行検索
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      string  $seikyu_den_no 伝票番号
     * @param      integer $msi_no    請求明細No
     * @return     array|null(存在しない)
     * @version    2020/08/xx Kobayashi 受注伝票→請求伝票に変更
     */
    private static function _findDtl($db, $seikyu_den_no, $msi_no) {
        $select = $db->easySelect(<<< END_OF_SQL
SELECT m.*
  FROM seikyu_denpyo_msi m
 WHERE m.delete_flg=0
   AND m.seikyu_den_no = :seikyu_den_no
   AND m.msi_no = :msi_no
   FOR UPDATE
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no,
            'msi_no' => $msi_no));

        if (Msi_Sys_Utils::myCount($select) === 0) {
            return null;
        }

        return $select[0];
    }

    /**
     *
     * 最大請求明細Noを取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/3/31
     * @param      Msi_Sys_Db $db
     * @param      string  $seikyu_den_no 伝票番号
     * @return int 最大請求明細No
     * @version    2020/08/xx Kobayashi 受注伝票→請求伝票に変更
     */
    private static function _getMaxdenpyoMsiNo($db, $seikyu_den_no) {
        // cf. Juchu_JuchuAbstract#getMaxdenpyoMsiNo()
        $sql1 = <<< END_OF_SQL
SELECT
    COALESCE(MAX(msi_no),0) AS msi_no
FROM
    seikyu_denpyo_msi
WHERE
    seikyu_den_no = :seikyu_den_no
END_OF_SQL;
        // OK not delete_flg

        $max = $db->getOneVal($sql1, array('seikyu_den_no' => $seikyu_den_no));

        return $max;
    }
    

    /**
     * 請求伝票（分割） の登録処理
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/xx
     * @param      string  $seikyu_den_no 請求伝票No
     * @return     array $data 登録データ
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function upsertFromBechu($seikyu_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $denpyoSelect = $db->easySelect( <<< END_OF_SQL
            SELECT *
            FROM seikyu_denpyo den
            WHERE den.seikyu_den_no = :seikyu_den_no
                AND den.delete_flg = 0
END_OF_SQL
                                        , array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($denpyoSelect) == 0) {
            throw new Msi_Sys_Exception_InputException( "請求伝票($seikyu_den_no)のデータが存在しません" );
        }
        $seikyuDenpyo = $denpyoSelect[0];
        if (($seikyuDenpyo['nyukin_prc'] + $seikyuDenpyo['uchikin_prc']) != 0) {
            return;
        }
        $seikyuMsi = $db->easySelect( <<< END_OF_SQL
            SELECT *
            FROM seikyu_denpyo_msi msi
            WHERE msi.seikyu_den_no = :seikyu_den_no
                AND msi.delete_flg = 0
END_OF_SQL
                                        , array('seikyu_den_no' => $seikyu_den_no));
        $sekyuSaki = $db->easySelect( <<< END_OF_SQL
            SELECT *
            FROM seikyu_sekyu_saki_info sekyu
            WHERE sekyu.seikyu_den_no = :seikyu_den_no
                AND sekyu.delete_flg = 0
END_OF_SQL
                                        , array('seikyu_den_no' => $seikyu_den_no));
        // 請求先情報が1件の場合は請求伝票の支払方法のみ更新する
        if (Msi_Sys_Utils::myCount($sekyuSaki) == 1) {
            $pay_kbn = $sekyuSaki[0]['pay_kbn'];
            // 支払方法に即した回収区分を設定する
            if (isset(static::$_kaishu_kbn_array[$pay_kbn])) {
                $kaishu_kbn = static::$_kaishu_kbn_array[$pay_kbn];
            } else {    // なければとりあえず0(売掛)を設定しておく
                $kaishu_kbn = '0';
            }
            $upd = array();
            $upd['pay_method_cd'] = $pay_kbn;
            $upd['kaishu_kbn'] = $kaishu_kbn;
            $upd['kaishu_ymd'] = $sekyuSaki[0]['kaishu_ymd'];
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $upd, array('seikyu_den_no' => $seikyu_den_no));
            $db->easyExecute($sql, $param);
            return;
        }
        foreach ($sekyuSaki as $sekyuOne) {
            $except = array();
            array_push($except, 'delete_flg');
            array_push($except, '_req_id');
            array_push($except, '_cre_user');
            array_push($except, '_cre_ts');
            array_push($except, '_mod_user');
            array_push($except, '_mod_ts');
            array_push($except, '_mod_cnt');
            $hdr = $seikyuDenpyo;
            $new_seikyu_den_no = App_ClsGetCodeNo::GetCodeNo($db, 'seikyu_denpyo', 'seikyu_den_no', $seikyuDenpyo['keijo_ymd']);
            $hdr['seikyu_den_no'] = $new_seikyu_den_no;
            $hdr['uri_den_no'] = null;
            $hdr['uri_prc_sum'] = $sekyuOne['sekyu_prc'];
            $hdr['seikyu_zan'] = $sekyuOne['sekyu_prc'];
            $hdr['uri_hepn_sum'] = 0;
            $hdr['uri_nebk_sum'] = 0;
            $hdr['szei_katax_taisho_prc'] = 0;
            $hdr['uzei_katax_taisho_prc'] = $sekyuOne['sekyu_prc'];
            $hdr['hitax_katax_taisho_prc'] = 0;
            list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($seikyuMsi[0]["zei_cd"]);
            // 分割時は税区分内税で固定 
            $zei = App_ClsTaxLib::CalcTax($sekyuOne['sekyu_prc'], static::$_zei_kbn_uchi, $zei_rtu, $zei_hasu_kbn); 
            $hdr['out_zei_prc'] = 0;
            $hdr['in_zei_prc'] = $zei['ZeiPrc'];
            $hdr['bun_gas_kbn_num'] = 2;
            $hdr['bun_gas_seikyu_den_no'] = $seikyu_den_no;
            $hdr['pay_method_cd'] = $sekyuOne['pay_kbn'];
            // 支払方法に即した回収区分を設定する
            if (isset(static::$_kaishu_kbn_array[$sekyuOne['pay_kbn']])) {
                $kaishu_kbn = static::$_kaishu_kbn_array[$sekyuOne['pay_kbn']];
            } else {    // なければとりあえず0(売掛)を設定しておく
                $kaishu_kbn = '0';
            }
            $hdr['kaishu_kbn'] = $kaishu_kbn;
            $hdr['kaishu_ymd'] = $sekyuOne['kaishu_ymd'];
            // 1行分のみ作成
            $msi = $seikyuMsi[0];
            $msi['seikyu_den_no'] = $new_seikyu_den_no;
            $msi['uri_tnk'] = $sekyuOne['sekyu_prc'];
            $msi['nebiki_prc'] = 0;
            $msi['juchu_suryo'] = 1;
            $msi['uri_prc'] = $sekyuOne['sekyu_prc'];
            $msi['gen_tnk'] = 0;
            $msi['gen_gaku'] = 0;
            $msi['arari_gaku'] = 0;
            $msi['out_zei_prc'] = 0;
            $msi['in_zei_prc'] = $zei['ZeiPrc'];
            $msi['zei_kbn'] = static::$_zei_kbn_uchi;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo_msi', $msi, $except);
            $db->easyExecute($sql, $param);
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo', $hdr, $except);
            $db->easyExecute($sql, $param);
            // 請求先情報作成
            $newSekyuSaki = $sekyuOne;
            $newSekyuSaki['seikyu_den_no'] = $new_seikyu_den_no;
            $newSekyuSaki['seq_no'] = 1;
            $newSekyuSaki['bun_gas_seikyu_den_no'] = $seikyu_den_no;
            $newSekyuSaki['bun_gas_seq_no'] = $sekyuOne['seq_no'];
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_sekyu_saki_info', $newSekyuSaki, $except);
            $db->easyExecute($sql, $param);
        }
        // 元伝票を分割元にステータスを更新
        $motoData = array();
        $where = array();
        $motoData['bun_gas_kbn_num'] = 1;
        $where['seikyu_den_no'] = $seikyu_den_no;
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $motoData, $where);
        $db->easyExecute($sql, $param);
        return;
    }

    /**
     * 請求伝票（分割） の更新・登録
     *
     * <AUTHOR> Sai
     * @since      2018/03/xx
     * @param      array  $data データ. 明細は $data['_dtl_']に含まれる 削除明細は$data['_dtl_del_']に含まれる
     * @return     array $data 登録データ
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function updateFromBechu($seikyu_den_no, $seikyuInfo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $motoSelect = $db->easySelect( <<< END_OF_SQL
            SELECT *
            FROM seikyu_denpyo den
            WHERE den.seikyu_den_no = :seikyu_den_no
                AND den.delete_flg = 0
END_OF_SQL
                                        , array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($motoSelect) == 0) {
            throw new Msi_Sys_Exception_InputException( "請求伝票のデータが存在しません" );
        }
        $motoDenpyo = $motoSelect[0];
        $motoSeikyuNo = $seikyu_den_no;
        // 分割されておらず請求先情報が1件の場合はそのまま請求伝票のみ更新を行う
        // 支払方法は請求先情報から更新する
        if ($motoDenpyo['bun_gas_kbn_num'] == self::$_bun_gas_kbn_tsujo && Msi_Sys_Utils::myCount($seikyuInfo) == 1) {
            DataMapper_SeikyuFromUriage::upsert($db, $motoDenpyo['uri_den_no'], array('keijo_ymd' => $motoDenpyo['keijo_ymd']));
            $pay_kbn = $seikyuInfo[0]['pay_kbn'];
            // 支払方法に即した回収区分を設定する
            if (isset(static::$_kaishu_kbn_array[$pay_kbn])) {
                $kaishu_kbn = static::$_kaishu_kbn_array[$pay_kbn];
            } else {    // なければとりあえず0(売掛)を設定しておく
                $kaishu_kbn = '0';
            }
            $upd = array();
            $upd['pay_method_cd'] = $pay_kbn;
            $upd['kaishu_kbn'] = $kaishu_kbn;
            $upd['kaishu_ymd'] = $seikyuInfo[0]['kaishu_ymd'];
            // 回収日を設定する
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $upd, array('seikyu_den_no' => $seikyu_den_no));
            $db->easyExecute($sql, $param);
            return;
        } else if ($motoDenpyo['bun_gas_kbn_num'] == self::$_bun_gas_kbn_moto && Msi_Sys_Utils::myCount($seikyuInfo) == 1) {
            // 既に分割されていて請求先情報が1件だったら分割を取り消して元に戻す
            $denpyoSelect = $db->easySelect( <<< END_OF_SQL
                SELECT *
                FROM seikyu_denpyo den
                WHERE den.bun_gas_seikyu_den_no = :seikyu_den_no
                    AND den.delete_flg = 0
END_OF_SQL
                                        , array('seikyu_den_no' => $motoSeikyuNo));
            foreach ($denpyoSelect as $seikyuDenpyo) {
                if (Msi_Sys_Utils::myCount($seikyuDenpyo) == 0) {
                    throw new Msi_Sys_Exception_InputException( "請求伝票のデータが存在しません" );
                }
                if (($seikyuDenpyo['nyukin_prc'] + $seikyuDenpyo['uchikin_prc']) != 0) {
                    throw new Msi_Sys_Exception_InputException( "入金データが存在してます。" );
                }
                $upd = array();
                $upd['delete_flg'] = 1;
                $where['seikyu_den_no'] = $seikyuDenpyo['seikyu_den_no'];
                // 請求先情報は物理削除
                $db->easyExecute( <<< END_OF_SQL
                DELETE FROM seikyu_sekyu_saki_info
                WHERE seikyu_den_no = :seikyu_den_no
END_OF_SQL
                    , array('seikyu_den_no' => $seikyuDenpyo['seikyu_den_no']) );
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $upd, $where);
                $db->easyExecute($sql, $param);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo_msi', $upd, $where);
                $db->easyExecute($sql, $param);
            }
            // 分割元を元の状態に戻す
            $upd = array();
            $upd['bun_gas_kbn_num'] = 0;
            $where['seikyu_den_no'] = $motoSeikyuNo;
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $upd, $where);
            $db->easyExecute($sql, $param);
        } else {
            $seikyuMsi = $db->easySelect( <<< END_OF_SQL
                SELECT *
                FROM seikyu_denpyo_msi msi
                WHERE msi.seikyu_den_no = :seikyu_den_no
                    AND msi.delete_flg = 0
END_OF_SQL
                    , array('seikyu_den_no' => $motoSeikyuNo));
            foreach ($seikyuInfo as $sekyuOne) {
                $seikyuSeikyuSaki = static::_filterSeikyuSekyuSaki($sekyuOne);
                $except = array();
                array_push($except, 'delete_flg');
                array_push($except, '_req_id');
                array_push($except, '_cre_user');
                array_push($except, '_cre_ts');
                array_push($except, '_mod_user');
                array_push($except, '_mod_ts');
                array_push($except, '_mod_cnt');
                // 既に分割されていれば分割先をベースにする
                $sekyuSelect = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('bun_gas_seikyu_den_no' => $seikyu_den_no, 'bun_gas_seq_no' => $sekyuOne['seq_no']));
                if (Msi_Sys_Utils::myCount($sekyuSelect) > 0) {
                    $sakiSelect = $db->easySelect( <<< END_OF_SQL
                        SELECT *
                        FROM seikyu_denpyo den
                        WHERE den.seikyu_den_no = :seikyu_den_no
                            AND den.delete_flg = 0
END_OF_SQL
                                                    , array('seikyu_den_no' => $sekyuSelect[0]['seikyu_den_no']));
                    $hdr = $sakiSelect[0];
                    $sakiMsi = $db->easySelect( <<< END_OF_SQL
                        SELECT *
                        FROM seikyu_denpyo_msi msi
                        WHERE msi.seikyu_den_no = :seikyu_den_no
                            AND msi.delete_flg = 0
END_OF_SQL
                            , array('seikyu_den_no' => $sekyuSelect[0]['seikyu_den_no']));
                    $msi = $sakiMsi[0];
                } else {
                    $hdr = $motoDenpyo;
                    $msi = $seikyuMsi[0];
                }
                $hdr['seikyu_den_no'] = $new_seikyu_den_no;
                $hdr['uri_den_no'] = null;
                $hdr['uri_prc_sum'] = $seikyuSeikyuSaki['sekyu_prc'];
                $hdr['seikyu_zan'] = $seikyuSeikyuSaki['sekyu_prc'];
                $hdr['uri_hepn_sum'] = 0;
                $hdr['uri_nebk_sum'] = 0;
                $hdr['szei_katax_taisho_prc'] = 0;
                $hdr['uzei_katax_taisho_prc'] = $seikyuSeikyuSaki['sekyu_prc'];
                $hdr['hitax_katax_taisho_prc'] = 0;
                list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($seikyuMsi[0]["zei_cd"]);
                $zei = App_ClsTaxLib::CalcTax($seikyuSeikyuSaki['sekyu_prc'], static::$_zei_kbn_uchi, $zei_rtu, $zei_hasu_kbn);
                $hdr['out_zei_prc'] = 0;
                $hdr['in_zei_prc'] = $zei['ZeiPrc'];
                $hdr['bun_gas_kbn_num'] = 2;
                $hdr['bun_gas_seikyu_den_no'] = $motoSeikyuNo;
                $hdr['pay_method_cd'] = $sekyuOne['pay_kbn'];
                // 支払方法に即した回収区分を設定する
                if (isset(static::$_kaishu_kbn_array[$sekyuOne['pay_kbn']])) {
                    $kaishu_kbn = static::$_kaishu_kbn_array[$sekyuOne['pay_kbn']];
                } else {    // なければとりあえず0(売掛)を設定しておく
                    $kaishu_kbn = '0';
                }
                $hdr['kaishu_kbn'] = $kaishu_kbn;
                $hdr['kaishu_ymd'] = $sekyuOne['kaishu_ymd'];
                // 1行分のみ作成
                $msi['uri_tnk'] = $seikyuSeikyuSaki['sekyu_prc'];
                $msi['nebiki_prc'] = 0;
                $msi['juchu_suryo'] = 1;
                $msi['uri_prc'] = $seikyuSeikyuSaki['sekyu_prc'];
                $msi['gen_tnk'] = 0;
                $msi['gen_gaku'] = 0;
                $msi['arari_gaku'] = 0;
                $msi['out_zei_prc'] = 0;
                $msi['in_zei_prc'] = $zei['ZeiPrc'];
                $msi['zei_kbn'] = static::$_zei_kbn_uchi;
                // 既に分割されていれば更新なければ登録
                if (Msi_Sys_Utils::myCount($sekyuSelect) > 0) {
                    $where = array();
                    $where['seikyu_den_no'] = $seikyuSeikyuSaki['seikyu_den_no'];
                    array_push($except, 'seikyu_den_no');
                    $upd_seikyu_den_no = $seikyuSeikyuSaki['seikyu_den_no'];
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $hdr, $where, $except);
                    list($msql, $mparam) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo_msi', $msi, $where, $except);
                } else {
                    $new_seikyu_den_no = App_ClsGetCodeNo::GetCodeNo($db, 'seikyu_denpyo', 'seikyu_den_no', $seikyuDenpyo['keijo_ymd']);
                    $hdr['seikyu_den_no'] = $new_seikyu_den_no;
                    $msi['seikyu_den_no'] = $new_seikyu_den_no;
                    $upd_seikyu_den_no = $new_seikyu_den_no;
                    list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo', $hdr, $except);
                    list($msql, $mparam) = DataMapper_Utils::makeInsertSQL('seikyu_denpyo_msi', $msi, $except);
                }
                $db->easyExecute($sql, $param);
                $db->easyExecute($msql, $mparam);
                // 請求残更新処理
                Logic_SyukeiTblUpdateSub::updNyukinPrcSeikyu2($db, $upd_seikyu_den_no);
                // 請求先情報
                $newSekyuSaki = $seikyuSeikyuSaki;
                // 削除→登録
                $db->easyExecute( <<< END_OF_SQL
                DELETE FROM seikyu_sekyu_saki_info
                WHERE seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyuSeikyuSaki['seikyu_den_no']) );
                $except = array();
                array_push($except, 'denpyo_no');
                array_push($except, 'line_no');
                $newSekyuSaki['seikyu_den_no'] = $upd_seikyu_den_no;
                $newSekyuSaki['seq_no'] = 1;
                $newSekyuSaki['data_kbn'] = static::$_data_kbn_betu;
                $newSekyuSaki['seko_no'] = $motoDenpyo['seko_no'];
                $newSekyuSaki['bun_gas_seikyu_den_no'] = $seikyu_den_no;
                $newSekyuSaki['bun_gas_seq_no'] = $seikyuSeikyuSaki['seq_no'];
                list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_sekyu_saki_info', $newSekyuSaki, $except);
                $db->easyExecute($sql, $param);
            }
            // 元伝票を分割元にステータスを更新
            $motoData = array();
            $where = array();
            $motoData['bun_gas_kbn_num'] = 1;
            $where['seikyu_den_no'] = $motoSeikyuNo;
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $motoData, $where);
            $db->easyExecute($sql, $param);

        }
        return;
    }
    
    /**
     * 請求請求先情報 項目フィルタ
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      array      $seikyuSekyuSaki
     * @return     array
     */
    protected static function _filterSeikyuSekyuSaki($seikyuSekyuSaki)
    {

        $newSeikyuSaki = Msi_Sys_Utils::remapArrayFlat($seikyuSekyuSaki, <<< END_OF_TXT
        seikyu_den_no seq_no seko_no data_kbn pay_kbn houjin_kbn houjin_sbt soufu_kbn
        sekyu_prc sekyu_cd sekyu_nm1 sekyu_nm2 sekyu_knm1 sekyu_knm2 sekyu_yubin_no
        sekyu_addr1 sekyu_addr2 sekyu_tel sekyu_mobile_tel sekyu_soufu_nm soufu_yubin_no
        soufu_addr1 soufu_addr2 soufu_tel rs_print_kbn rs_soufu_kbn ryosyu_prc ryosyusyo_meigi
        ryosyusyo_soufu_nm ryosyusyo_soufu_yubin_no ryosyusyo_soufu_addr1 ryosyusyo_soufu_addr2
        kaishu_ymd syorui_tenpu_kbn bun_gas_seikyu_den_no bun_gas_seq_no delete_flg
        v_free1 v_free2 v_free3 v_free4 v_free5
        free_kbn1 free_kbn2 free_kbn3 free_kbn4 free_kbn5
        n_free1 n_free2 n_free3 n_free4 n_free5
        d_free1 d_free2 d_free3
END_OF_TXT
                                                       , array());
        return $newSeikyuSaki;
    }

}
