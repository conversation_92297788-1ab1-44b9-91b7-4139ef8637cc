<?php

/**
 * App_AccCtrlImplAbst
 *
 * 会社・部門アクセス制御 実装クラス<br>
 *
 * App_AccCtrlImplFuku, App_AccCtrlImplSimple で分けて実装する計画だったが、
 * 分けないほうが見通しがよさそうなので、必要に迫られるまでサブクラス実装は保留。
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> Mihara
 * @since      2016/09/xx
 * @version    2016/09/xx MSI Mihara
 * @version    2017/08/07 MSI Mihara 'Tanto'の場合、_getMyAllowedBumons()で array() を返す(pattern-1)
 *                                   従来の pattern-2 を採用する場合は 対応する getMyAllKaisyas() も有効にすること
 * @version    2020/06/08 Mihara 抽象クラスへ引き上げ(for web-edi)
 * @filesource 
 */
abstract class App_AccCtrlImplAbst extends App_AccCtrlImplAbst00 {

    /**
     * (担当者の)デフォルト ホール、エリアを返す
     * org App_Utils
     *
     * <AUTHOR> Mihara
     * @since      2014/03/03
     * @return     array($hall_cd, $area_cd)
     */
    public function getDfltHallArea() {
        $hall_cd = null;
        $area_cd = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $bumons = $this->getTantoBumons();
        foreach ($bumons as $rec) {
            if ((int) $rec['bumon_kbn'] === (int) 1 || (int) $rec['bumon_kbn'] === (int) 2) { // 1:部門 2:式場
                $hall_cd00 = $rec['bumon_cd'];
                if ($this->isAllowBumon($hall_cd00)) {
                    $hall_cd = $hall_cd00;
                    break;
                }
            }
        }
        if (!isset($hall_cd)) { // ない場合は、適当に hall_cd を選ぶ.
            $tantoBumon = App_Utils::getTantoBumonCd();                             // ログイン者の部門コード取得
            $oyabumon   = DataMapper_BumonMst::findOyaBumonCd($db, $tantoBumon);    // 親部門コード取得
            $hall_cd    = DataMapper_BumonMst::findNotUriBumonCd($db, $oyabumon);   // 売上未定部門コードを取得
        }
        if (isset($hall_cd)) {
            $parBumon = App_Utils::parBumonOfHall($hall_cd);
            if ($parBumon) {
                $area_cd = $parBumon['bumon_cd'];
            }
        }

        // 確認のため再度、権限チェック
        if ($this->isAllowBumon($hall_cd)) {
            return array($hall_cd, $area_cd);
        }

        return array(null, null);
    }

    /**
     * $seko_no データのアクセス権限があるかを真偽値で返す
     *
     * <AUTHOR> Mihara
     * @since      2016/09/xx
     * @param      string   $seko_no
     * @return     boolean
     */
    public function canAccessSekoData($seko_no)
    {
        // 複数会社管理も部門アクセス制御もしない
        if ( App_Utils::isFukusuKaisyaLegacy() ) {
            return true;
        }

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoRec = DataMapper_SekoKihonInfo::findOne($db, array('seko_no' => $seko_no));
        if ( $sekoRec === null ) { // データがない
            return false;
        }

        $bumon_cd = $sekoRec['bumon_cd'];
        $kaisya_cd = null;
        if ( $bumon_cd !== null && strlen($bumon_cd) > 0 ) {
            $bumonRec = DataMapper_BumonEx2::findOne( $db, array('bumon_cd' => $bumon_cd) );
            if ( $bumonRec !== null ) {
                $kaisya_cd = $bumonRec['kaisya_cd']; // 00000000 ではなく具体的な会社CDが設定されている
            }
        }
        
        // 複数会社管理をするが部門アクセス制御をしない場合
        if ( App_Utils::isFukusuKaisyaKanri() && !App_Utils::isBumonAuthorityi() ) {
            $rtn = $this->isAllowKaisya($kaisya_cd);
            return $rtn;
        }

        // assert: 部門アクセス制御をする

        // Tanto 権限. 受付、担当者のいずれかにマッチすれば権限あり
        if ( $this->getMyAuthorityi() === 'Tanto' ) {
            $myTantoCd = (string)App_Utils::getTantoCd();
            if ( strlen($myTantoCd) > 1 ) { // 設定されている
                $uketuke_tanto_cd = (string)$sekoRec['uketuke_tanto_cd'];
                $seko_tanto_cd    = (string)$sekoRec['seko_tanto_cd'];
                if ( $myTantoCd === $uketuke_tanto_cd || $myTantoCd === $seko_tanto_cd ) {
                    return true;
                }
            }
            return false;
        }

        $rtn = $this->isAllowBumon($bumon_cd);
        return $rtn;
    }

    /**
     * 部門CDが属する会社CDを返す
     *
     * <AUTHOR> Mihara
     * @since      2016/09/xx
     * @return     string
     */
    public function getKaisyaCdByBumon($bumon_cd)
    {
        // 複数会社管理も部門アクセス制御もしない
        if ( App_Utils::isFukusuKaisyaLegacy() ) {
            $kaisya_cd = Msi_Sys_Utils::getKaisyaCd();
            return $kaisya_cd;
        }

        $db = Msi_Sys_DbManager::getMyDb();
        $cond = array( 'bumon_cd' => $bumon_cd );
        $rec = DataMapper_BumonEx2::findOne( $db, $cond); // 00000000 => xxxxxxx で利用

        if ( $rec === null ) {
            return null;
        }
        $kaisya_cd = $rec['kaisya_cd'];
        return $kaisya_cd;
    }

}
