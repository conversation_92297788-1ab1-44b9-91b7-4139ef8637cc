<?php

/**
 * PDF 見積書  軽減税率対応(keigen)
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Kobayashi
 * @since      2022/12/xx あいあーる版
 * @filesource 
 */

/**
 * PDF 見積書
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Kobayashi
 * @since      2022/12/xx
 */
class Juchu_Pdf0113Controller extends Zend_Controller_Action {

    private static $title = '見積書';

    /** テンプレート */
    private static $sourceFileName = array( // 葬儀
        'H'  => 'pdf_tmpl/0113_H.pdf',      // 表紙
        'K1' => 'pdf_tmpl/0113_K1.pdf',     // 鏡１
        'K2' => 'pdf_tmpl/0113_K2.pdf',     // 鏡２
        'K2n' => 'pdf_tmpl/0113_K2n.pdf',   // 鏡２(非会員)
        'M1' => 'pdf_tmpl/0113_M1.pdf',     // 明細１
        'M2' => 'pdf_tmpl/0113_M2.pdf',     // 明細２
        'K1H'=> 'pdf_tmpl/0113_K1H.pdf'     // 会社控え(施行申込書)
    );
    private static $sourceFileNameHouji = array( // 法事
        'K' => 'pdf_tmpl/0113_K_houji.pdf',      // 鏡
        'M' => 'pdf_tmpl/0113_M_houji.pdf',      // 明細
    );
    private static $sourceFileNameYosan = array( // 実行予算書
        'K1' => 'pdf_tmpl/0113_K1Y.pdf',     // 鏡１
        'M1' => 'pdf_tmpl/0113_M1Y.pdf',     // 明細１
        'M2' => 'pdf_tmpl/0113_M2Y.pdf',     // 明細２
    );

    /** 鏡 分類 (葬儀) */
    private static $kihon       = array('plan', 'plan_nebiki', 'keiyaku_prc', 'warimashi_prc', 'plan_gai', 'waribiki');
    private static $omotenashi  = array('henrei', 'hikidemono', 'ryori', 'other', 'nebiki_omt');
    private static $sonota      = array('betto', 'kojo_prc', 'no_use_prc', 'nebiki');
    private static $mitsumori   = array('early_use_cost', 'early_use_cost_zei', 'keiyaku_zei', 'keiyaku_zan', 'tatekae', 'kaiyaku_prc');
    private static $zei         = array('szei_katax_taisho_prc', 'out_zei_prc', 'uzei_katax_taisho_prc', 'in_zei_prc'
                                        ,'szei_katax_taisho_prc_keigen', 'out_zei_prc_keigen', 'uzei_katax_taisho_prc_keigen', 'in_zei_prc_keigen'
                                        , 'hitax_katax_taisho_prc', 'hitax_katax_taisho_prc_keigen');
    
    /** 鏡 分類 (法事) */
    private static $kihonH       = array('houyou', 'waribiki');
    private static $omotenashiH  = array('henrei', 'ryori', 'nebiki_omt');
    private static $sonotaH      = array('betto');
    private static $mitsumoriH   = array('syokeiC_zei', 'early_use_cost', 'early_use_cost_zei', 'keiyaku_zei', 'keiyaku_zan', 'kaiyaku_prc');
    private static $zeiH         = array('szei_katax_taisho_prc', 'out_zei_prc', 'uzei_katax_taisho_prc', 'in_zei_prc'
                                        ,'szei_katax_taisho_prc_keigen', 'out_zei_prc_keigen', 'uzei_katax_taisho_prc_keigen', 'in_zei_prc_keigen'
                                        , 'hitax_katax_taisho_prc', 'hitax_katax_taisho_prc_keigen');
    
    private static $_moushiKbn;
    private static $_kijun_ymd;
    private static $_p_seko_no;
    private static $_p_seko_no_sub;
    private static $_p_data_kbn;
    private static $_is_reduced_tax_rate_row_exists;
    
    private static $gedai_total;

    /**
     * アクション
     *
     * <AUTHOR> Kobayashi
     * @since   2020/07/01
     */
    public function indexAction($paramsData=array()) {
        if (Msi_Sys_Utils::myCount($paramsData) > 0) {
            $params = $paramsData;
        } else {
            $params = Msi_Sys_Utils::webInputs();
        }
        if (isset($params['preview'])) {
            $preview = htmlspecialchars($params['preview']);         // プレビュー有無
        } else {
            $preview = 'off';
        }
        $seko_no = htmlspecialchars($params['seko_no']);            // 施行番号
        if (array_key_exists('seko_no_sub', $params)) {
            $seko_no_sub = htmlspecialchars($params['seko_no_sub']);
        } // 施行番号（枝番）
        else {
            $seko_no_sub = '00';
        }
        $data_kbn = htmlspecialchars($params['data_kbn']);        // データ区分　1：葬儀 2：法事 3：単品 4：別注品
        self::$_moushiKbn = DataMapper_PdfCommon::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
        if (isset($params['printKbn'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_kbn = $params['printKbn'];
        } else {
            $print_kbn = 0;
        }
        if (isset($params['printSbt'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_sbt = $params['printSbt'];
        } else {
            $print_sbt = 'normal';
        }
        // 軽減税率対応 mihara keigen   基準日の設定
        self::_prepKijunYmdKeigen($seko_no, $seko_no_sub, $data_kbn);

        $issue_date = date('Y/m/d');

        $db = Msi_Sys_DbManager::getMyDb();
        if($print_sbt === 'yosan'){
            $pdfObj = new App_Pdf(self::getFileName($db, $seko_no, '実行予算書'));
        } else {
            $pdfObj = new App_Pdf(self::getFileName($db, $seko_no, self::$title));
        }

        if($print_sbt === 'normal'){ // 見積書
            // 履歴PDF表示
            if (isset($params['history_no']) && strlen($params['history_no']) > 0) {
                $history_states = $this->outDataHistory($db, $seko_no, $data_kbn, $seko_no_sub, $params['history_no']);
                if($history_states === FALSE){
                    $errMsg = '見積書データが存在しません';
                    $outData = array('status' => 'NG', 'msg' => $errMsg,);
                    Msi_Sys_Utils::outJson( $outData );
                }
                return;
            } else { // not履歴
                if (self::$_moushiKbn == 2){
                    //「法事」場合
                    self::outData_houji($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub);
                } else {
                    //「葬儀」の場合
                    $totalPage = self::outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub);
                    if ($totalPage !== null) { // 会社控え
                        self::outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $totalPage);
                    }
                }
            }
        } else if($print_sbt === 'history'){ // 見積履歴(事前相談)
            $rireki_flg = self::outDataMeisaiHistory($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);
            if($rireki_flg === FALSE){
                $errMsg = '見積履歴データが存在しません';
                $outData = array('status' => 'NG', 'msg' => $errMsg,);
                Msi_Sys_Utils::outJson( $outData );
                return;
            }
        } else  if($print_sbt === 'yosan'){ // 実行予算書
            self::outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, null, TRUE);
        }

        if ($preview == 'on') {
            $buf = $pdfObj->fileOutBuf();
            $file_name = $pdfObj->getFileName();
            $key = Msi_Sys_Utils::prepareMyTempFileEasy($buf, $file_name);
            $this->view->file = $file_name;
            $this->view->key = $key;
            $this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
            $this->_helper->viewRenderer->setScriptAction('pdf');
        } else if ($preview == 'save') {
            $buf = $pdfObj->fileOutBuf();
            return $buf;
        } else {
            $pdfObj->download();
        }
    }

    /**
     * 履歴PDF表示
     * <AUTHOR> Kobayashi
     * @since 2020/10/21
     */
	public function outDataHistory($db, $seko_no, $data_kbn, $seko_no_sub, $history_no)
    {
        $rec = DataMapper_Pdf0113::findHistoryDenpyo($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "history_no" => $history_no));
        
        if(Msi_Sys_Utils::myCount($rec) > 0){
            $recHistorty = $rec[0];
        } else {
            return FALSE;
        }
        //  PDFファイル
        if(isset($recHistorty['pdf_oid'])){
            $hachu_pdf = $recHistorty['pdf_oid'];
            $cont = $db->readBlobCont( $hachu_pdf );
        } else {
            return FALSE;
        }
        //  ファイル名
        if(isset($recHistorty['file_nm']) && $recHistorty['file_nm'] !== ''){
            $fname = $recHistorty['file_nm'].'.pdf';
        } else {
            $fname = $recHistorty['denpyo_no'].'-'.$recHistorty['history_no'].'.pdf';
        }
        
        Msi_Sys_Utils::out2wayPush($cont, $fname);
        // ファイルコンテンツを返す
        Msi_Sys_Utils::out2wayFlush();

        return TRUE;
	}

    /**
     * 見積書（葬儀）
     * <AUTHOR> Kobayashi
     * @since 2022/08/01
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param string $seko_no_sub	施行番号サブ
     * @param string $YosanFlg	 実行予算書フラグ
     * @return type
     */
    private function outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $totalPage = null, $YosanFlg = FALSE) {

        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        // 施行情報
        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
            $recHead['print_tanto'] = App_Utils::getTantoNm();
        }
        // 受注伝票
        $juchuDenpyo = DataMapper_JuchuDenpyo::findOne($db, array('seko_no' => $recHead['seko_no'], 'data_kbn' => $data_kbn));

        // 現在の総ページ数
        $numPagesCur = $pdfObj->getNumPages();
        $AdjPage = 0; // 調整ページ(現在ページ)
        $AdjPage2 = 0; // 調整ページ(全体ページ)
        $HikaeFlg = FALSE;
        if($totalPage !== null){
            $AdjPage += $totalPage;
            $AdjPage2 += $totalPage;
            $HikaeFlg = TRUE;
        }
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;
        // 実行予算書(合計下代金額)
        self::$gedai_total = 0;
        
        // 表紙
        if (!$YosanFlg && !$HikaeFlg) {
            $AdjPage ++;
            $AdjPage2 ++;
            self::outDataHyoshi($pdfObj, $db, $recHead, $juchuDenpyo);
        }
        
        // 鏡1
        self::outDataKagami1($pdfObj, $db, $recHead, $juchuDenpyo, $HikaeFlg, $YosanFlg);
        
        // 鏡2(互助会会員表)
        self::outDataKagami2($pdfObj, $db, $recHead);

		// 明細書1(プラン商品)
		self::outDataDetail1($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg);
        
        // 明細書2(プラン外商品)
		self::outDataDetail2($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg);
        
        // 明細書3(おもてなし費用)
		self::outDataDetail3($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg);
        
        // 明細書4(立替金その他)
		self::outDataDetail4($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg);

        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            // 鏡１
            if($page === 1 || $page === (1+$AdjPage2)){
                if($YosanFlg){ // 実行予算書：備考
                    self::setYosanBiko($db, $pdfObj, $recHead, $juchuDenpyo);
                } else if($HikaeFlg){ // 会社控え：電子署名（見積確定後）
                    $sign = DataMapper_ElectronicSignInfo::findOne($db, array('seko_no'=>$recHead['seko_no'], 'file_id' => $juchuDenpyo['n_free4']));
                    if(isset($sign['imgid'])){
                        $img = $db->readBlobCont($sign['imgid']);
                        if(isset($img)){
                            $pdfObj->write_image(array( 'x' =>90, 'y' => 740    , 'width' =>null, 'height' =>65, 'font_size' =>10), $img);
                        }
                    }
                }
            }

            if($page > (2+$AdjPage)){ // 鏡以外(明細のみ)
                // ヘッダー情報
                self::outHeader($pdfObj, $recHead);
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>30, 'y'=>792, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'※は軽減税率対象商品');
                }
            }
            if (!(!$YosanFlg && $page == 1)) {
                // ページ
                $pdfObj->write_string(array('x' => 45, 'y' => 820, 'width' => 530, 'height' => 9, 'font_size' => 9.5, 'align' => 'C'), ($page-$AdjPage2) . '/' . ($numPages-$AdjPage2));
            }
        }
//        if (isset($recHead['jichu_kakute_ymd'])) { // 見積確定後 -- 見積確定前も会社控え出力に変更
            return $numPages;
//        } else {
//            return null;
//        }
    }
    
    /**
     * 御見積書表紙(葬儀)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataHyoshi($pdfObj, $db, $recHead, $juchuDenpyo) {
        
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['H']);
        
        $seikyuRec = DataMapper_PdfCommon::getOneSekoSekyuInfo($recHead['seko_no']);
        $pdfObj->write_string(array('x' => 60, 'y' => 99, 'width' => 210, 'height' => 9, 'font_size' => 16, 'align' => 'C'), $seikyuRec['sekyu_nm']); // 請求先名
        $kaisya_info = DataMapper_PdfCommon::outKaisyaInfo2($db);
        if (isset($kaisya_info['syaban_img'])) {
            $img1 = $db->readBlobCont($kaisya_info['syaban_img']);
            $pdfObj->write_image(array( 'x' =>240, 'y' => 700, 'width' =>125, 'height' =>37, 'font_size' =>10), $img1);
        }
        
    }
    
    /**
     * 御見積書鏡1(葬儀)
	 * @param pdfObj $pdfObj PDFオブジェクト
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagami1($pdfObj, $db, $recHead, $juchuDenpyo, $HikaeFlg, $YosanFlg) {
        
        if ($HikaeFlg) { // 見積確定後は会社控え
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['K1H']);
        } else if ($YosanFlg){ // 実行予算書
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameYosan['K1']);
        } else { //見積書
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['K1']);
        }
        
        // お客様情報
        $seikyuRec = DataMapper_PdfCommon::getOneSekoSekyuInfo($recHead['seko_no']);
        $pdfObj->write_string(array('x' => 43, 'y' => 64, 'width' => 180, 'height' => 9, 'font_size' => 14), $seikyuRec['sekyu_nm']); // 請求先名
        $pdfObj->write_string(array('x' => 43, 'y' => 83, 'width' => 80, 'height' => 9, 'font_size' => 9), '〒'.$seikyuRec['yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 100, 'y' => 83, 'width' => 140, 'height' => 9, 'font_size' => 9), $seikyuRec['addr1']); // 住所1
        $pdfObj->write_string(array('x' => 100, 'y' => 93, 'width' => 140, 'height' => 9, 'font_size' => 9), $seikyuRec['addr2']); // 住所2
        $pdfObj->write_string(array('x' => 162, 'y' => 104, 'width' => 80, 'height' => 9, 'font_size' => 9), $seikyuRec['tel']); // TEL
        $pdfObj->write_date(array('x' => 162, 'y' => 116, 'width' => 80, 'height' => 9, 'font_size' => 9), $recHead['sougi_ymd'], "y年n月j日", 'gG', 'AUTO'); // 施行日

        // 会社情報
        DataMapper_PdfCommon::outKaisyaInfo2($db, $pdfObj);
        
        // データ取得
        $kagamiInfo = App_Utils2::getJuchuKagami($juchuDenpyo['denpyo_no']);
        
        // 開始位置
        $y1 = 166; // 御葬儀基本費用
        $y2 = 311; // おもてなし費用
        $y3 = 441; // その他費用
        $y4 = 616.5; // 御見積金額
        $y5 = 570.5; // 消費税内訳

        // 表出力
        foreach ($kagamiInfo as $key => $value) {

            // 1.御葬儀基本費用
            if((array_search($key, self::$kihon) !== FALSE) && $value['prc'] <> 0){
                if($key === 'plan_gai' && !isset($juchuDenpyo['seko_plan_cd'])){
                    $value['title'] = 'ご葬儀利用額';
                }
                $pdfObj->write_string(array('x' => 50, 'y' => $y1, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 337, 'y' => $y1, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y1 += 15.5;
            }
            if($key === 'syokeiA'){
                $pdfObj->write_num(array('x' => 337, 'y' => 259, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }

            // 2.おもてなし費用
            if((array_search($key, self::$omotenashi) !== FALSE) && $value['prc'] <> 0){
                $pdfObj->write_string(array('x' => 50, 'y' => $y2, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 337, 'y' => $y2, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y2 += 15.5;
            }
            if($key === 'syokeiB'){
                $pdfObj->write_num(array('x' => 337, 'y' => 389, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }

            // 3.その他費用
            if((array_search($key, self::$sonota) !== FALSE) && $value['prc'] <> 0){
                $pdfObj->write_string(array('x' => 50, 'y' => $y3, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 337, 'y' => $y3, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y3 += 15.5;
            }
            if($key === 'syokeiOther'){
                $pdfObj->write_num(array('x' => 337, 'y' => 503, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }

            // 4.御見積金額
            if($key === 'syokeiC'){
                $pdfObj->write_string(array('x' => 50, 'y' => 555, 'width' => 280, 'height' => 9, 'font_size' => 11), '合計（小計①＋小計②＋小計③）');
                $pdfObj->write_num(array('x' => 337, 'y' => 555, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }
            if($key === 'syokeiC_zei'){
                $pdfObj->write_string(array('x' => 50, 'y' => 570.5, 'width' => 280, 'height' => 9, 'font_size' => 11), '合計の消費税');
                $pdfObj->write_num(array('x' => 337, 'y' => 570.5, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }
            if($key === 'syokeiD'){
                $pdfObj->write_string(array('x' => 50, 'y' => 586, 'width' => 280, 'height' => 9, 'font_size' => 11), '合計金額');
                $pdfObj->write_num(array('x' => 337, 'y' => 586, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
            }
            if((array_search($key, self::$mitsumori) !== FALSE) && ($value['prc'] <> 0)){
                $pdfObj->write_string(array('x' => 50, 'y' => $y4, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 337, 'y' => $y4, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y4 += 15.5;
            }
            if($key === 'total'){
                $pdfObj->write_num(array('x' => 337, 'y' => 709, 'width' => 85, 'height' => 9, 'font_size' => 13), $value['prc']); // 金額
            }
            // 消費税内訳
            if((array_search($key, self::$zei) !== FALSE) && ($value['prc'] <> 0)){
                $pdfObj->write_string(array('x' => 425, 'y' => $y5, 'width' => 100, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 498, 'y' => $y5, 'width' => 60, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y5 += 15.5;
            }
        }
        $pdfObj->write_string(array('x' => 425, 'y' => 555, 'width' => 280, 'height' => 9, 'font_size' => 11), '合計金額の内訳');

        // フッダー
        $pdfObj->write_string(array('x'=>442, 'y'=>781, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['bumon_lnm']); // 受付部門
        if($recHead['moushi_kbn'] == 5){ // 事前相談
            $pdfObj->write_string(array('x'=>442, 'y'=>791, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['uketuke_tanto']); // 担当者
        } else {
            $pdfObj->write_string(array('x'=>442, 'y'=>791, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['seko_tanto']); // 担当者
        }
        $pdfObj->write_date(array('x' => 442, 'y' => 801, 'width' => 500, 'height' => 9, 'font_size' => 9), $recHead['issue_date'], "y年n月j日", 'gG', 'AUTO'); // 発行日
    }
    
    /**
     * 御見積書鏡2(葬儀)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagami2($pdfObj, $db, $recHead) {
        
        if($recHead['kaiin_cd'] == '1'){ // 会員区分が「会員」のみ
            // セット(4行)
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['K2']);
            $gojo = DataMapper_SekoGojokaiInfo::findOne($db, array('seko_no'=>$recHead['seko_no']));
            
            // セット(4行)
            $set = DataMapper_SekoGojokaiMember::find($db, array('seko_no'=>$recHead['seko_no'], 'yoto_kbn'=>'1'));
            $y1 = 393;
            foreach ($set as $value) {
                $pdfObj->write_string(array('x' => 45, 'y' => $y1, 'width' => 41, 'height' => 9, 'font_size' => 10), 'セット'); // 利用内容
                $pdfObj->write_date(array('x' => 86, 'y' => $y1, 'width' => 80, 'height' => 9, 'font_size' => 10), $value['kanyu_dt'], "y年n月j日", 'gG', 'AUTO'); // ご加入日
                $pdfObj->write_string(array('x' => 166, 'y' => $y1, 'width' => 63, 'height' => 9, 'font_size' => 10), $value['web_disp_moji'].'-'.trim($value['kain_no'])); // 会員番号
                $pdfObj->write_string(array('x' => 230, 'y' => $y1, 'width' => 78, 'height' => 9, 'font_size' => 10), $value['kanyu_nm'].' 様'); // 加入者名
                $pdfObj->write_string(array('x' => 312, 'y' => $y1, 'width' => 49, 'height' => 9, 'font_size' => 10), $gojo['v_free8']); // ご利用コース
                $pdfObj->write_num(array('x' => 361, 'y' => $y1, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['keiyaku_gaku']); // 契約金額
                $pdfObj->write_num(array('x' => 405, 'y' => $y1, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['harai_gaku']); // 払込金額
                $pdfObj->write_num(array('x' => 448, 'y' => $y1, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['zan_gaku']); // 掛金残金
                $pdfObj->write_num(array('x' => 491, 'y' => $y1, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['v_free2']); // 会員特典
                $pdfObj->write_num(array('x' => 534, 'y' => $y1, 'width' => 20, 'height' => 9, 'font_size' => 10), $value['mst_zei_rtu']); // 税率
                if($value['az_gojokai_kbn'] === '1'||$value['az_gojokai_kbn'] === '3'){
                    $pdfObj->write_string(array('x' => 558, 'y' => $y1, 'width' => 16, 'height' => 9, 'font_size' => 11), '○'); // 証書
                }
                $y1 += 19;
            }

            // 説明文
            $pdfObj->write_string(array('x' => 45, 'y' => 470, 'width' => 525, 'height' => 9, 'font_size' => 11), $gojo['text_free1']);

            // セット以外(25行)※利用外も除く
            $notset = DataMapper_SekoGojokaiMember::find($db, array('seko_no'=>$recHead['seko_no'], '__raw_1'=>'yoto_kbn IN (2,3,4)'));
            $y2 = 508;
            foreach ($notset as $value) {
                $pdfObj->write_string(array('x' => 45, 'y' => $y2, 'width' => 41, 'height' => 9, 'font_size' => 10), $value['yoto_nm']); // 利用内容
                $pdfObj->write_date(array('x' => 86, 'y' => $y2, 'width' => 80, 'height' => 9, 'font_size' => 10), $value['kanyu_dt'], "y年n月j日", 'gG', 'AUTO'); // ご加入日
                $pdfObj->write_string(array('x' => 166, 'y' => $y2, 'width' => 63, 'height' => 9, 'font_size' => 10), $value['web_disp_moji'].'-'.trim($value['kain_no'])); // 会員番号
                $pdfObj->write_string(array('x' => 230, 'y' => $y2, 'width' => 78, 'height' => 9, 'font_size' => 10), $value['kanyu_nm'].' 様'); // 加入者名
                $pdfObj->write_string(array('x' => 312, 'y' => $y2, 'width' => 49, 'height' => 9, 'font_size' => 10), '　　－'); // ご利用コース
                $pdfObj->write_num(array('x' => 361, 'y' => $y2, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['keiyaku_gaku']); // 契約金額
                $pdfObj->write_num(array('x' => 405, 'y' => $y2, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['harai_gaku']); // 払込金額
                $pdfObj->write_num(array('x' => 448, 'y' => $y2, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['zan_gaku']); // 掛金残金
                $pdfObj->write_num(array('x' => 491, 'y' => $y2, 'width' => 43, 'height' => 9, 'font_size' => 10), $value['v_free2']); // 会員特典
                $pdfObj->write_num(array('x' => 534, 'y' => $y2, 'width' => 20, 'height' => 9, 'font_size' => 10), $value['mst_zei_rtu']); // 税率
                if($value['az_gojokai_kbn'] === '1'||$value['az_gojokai_kbn'] === '3'){
                    $pdfObj->write_string(array('x' => 558, 'y' => $y2, 'width' => 16, 'height' => 9, 'font_size' => 11), '○'); // 証書
                }
                $y2 += 19.3;
            }
        } else { // 非会員
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['K2n']);
        }
        
        // 備考
        $gojo = DataMapper_SekoGojokaiInfo::findOne($db, array('seko_no'=>$recHead['seko_no']));
        if(isset($gojo['text_free2'])){
            $pdfObj->write_strings2(array('x'=>50, 'y'=>60, 'width'=>515, 'height'=>245, 'font_size' => 11), $gojo['text_free2']); // 見積書備考
        }
    }

    /**
     * 明細書1(プラン商品)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $juchuDenpyo	受注伝票
     */
    private function outDataDetail1($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg) {
        
        // プラン情報取得
        $recPlanInfo = DataMapper_SekoPlan::findOne($db, array("seko_plan_cd" => $juchuDenpyo['seko_plan_cd']), FALSE);
        if (Msi_Sys_Utils::myCount($recPlanInfo) == 0) {
            return;
        }
        if(!isset($recHead['gojokai_kbn'])){
            $recHead['gojokai_kbn'] = 0;
        }
        // 施行プラン明細取得基準日(依頼日→なければ現在日)
        if (isset($recHead['irai_ymd']) && strlen($recHead['irai_ymd']) > 0) {
            $kijun_ymd = $recHead['irai_ymd'];
        } else {
            $kijun_ymd = Msi_Sys_Utils::getDate();
        }
        $recPlanMsi = DataMapper_Pdf0113::findPlanMsi($db, $recHead['gojokai_kbn'], $kijun_ymd, array("seko_plan_cd" => $juchuDenpyo['seko_plan_cd']));
        
        $row_arr = array();
        $shohin_kbn = '';
        $total = 0;
        $gedai_total = 0;
        // プラン名(1行目)
//        $row_arr[] = array('', $recPlanInfo['seko_plan_nm'], '', '', '', '', '');

        foreach ($recPlanMsi as $value) {
            // 商品区分名
            if($shohin_kbn != $value['shohin_kbn_nm']){
                $koumoku = $value['shohin_kbn_nm'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn_nm'];
            // 伝票取得
            $plan_juchu_info = self::getJuchuInfo($db, $juchuDenpyo['denpyo_no'], $value);
            // 商品名
            $shohin_nm = $plan_juchu_info['shohin_nm'];
            // 摘要
            if(isset($plan_juchu_info['shohin_tkiyo_nm']) && $plan_juchu_info['shohin_tkiyo_nm'] !== ''){
                $shohin_nm = $shohin_nm.'　'.$plan_juchu_info['shohin_tkiyo_nm'];
            }
            // 金額
            $prc = $value['hanbai_tnk'] * $value['suryo'];
            // 掛金役務
            if($YosanFlg){ // 実行予算書(下代)
                $ekimu_disp = number_format($plan_juchu_info['gen_gaku']);
                $gedai_total += $plan_juchu_info['gen_gaku']*1;
                self::$gedai_total += $plan_juchu_info['gen_gaku']*1;
            } else { // 見積書(掛金役務)
                $ekimu_disp = '';
                $gedai_total = '';
                if($recHead['kaiin_cd'] == '1'){ // 会員の場合
                    $ekimu_prc = $value['nebiki_prc']*-1;
                    if($ekimu_prc == 0){
                        $ekimu_disp = '';
                    } else {
                        $ekimu_disp = '('.number_format($ekimu_prc) .')';
                    }
                }
            }
            
            $row_arr[] = array($koumoku, $shohin_nm, $value['hanbai_tnk'], $value['suryo'], $prc, '', $ekimu_disp);
            $total += $prc;
        }
        if($YosanFlg){
            $gedai_total = number_format($gedai_total);
        }
        $row_arr[] = array('', '　　　　　　　　　　　　　葬儀利用　小計', '', '', $total, '', $gedai_total);
        $row_arr[] = array('', '', '', '', $recPlanInfo['seko_prc']-$total, $recPlanInfo['seko_prc'], '');
        
        // 出力
        $M1 = self::getSetArrM1();
        if($YosanFlg){ // 実行予算書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileNameYosan['M1']);
        } else { // 見積書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileName['M1']);
        }
    }
    
    /**
     * 明細書2(プラン外商品)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $juchuDenpyo	受注伝票
     */
    private function outDataDetail2($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg) {
        
        // 売上伝明細取得
        $recKihonMsi = DataMapper_Pdf0113::findMsi($db, array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0010', '__raw_1'=>'upgrade_kbn <> 1'));
        if (Msi_Sys_Utils::myCount($recKihonMsi) == 0) {
            return;
        }

        $row_arr = array();
        $shohin_kbn = '';
        $total = 0;
        $gedai_total = 0;
        // タイトル(1行目)
        if(isset($juchuDenpyo['seko_plan_cd'])){
            $row_arr[] = array('', 'プラン外費用', '', '', '', '', '');
        } else {
            $row_arr[] = array('', 'ご葬儀利用額', '', '', '', '', '');
        }

        foreach ($recKihonMsi as $value) {
            // 商品区分名
            if($shohin_kbn != $value['shohin_kbn_nm']){
                $koumoku = $value['shohin_kbn_nm'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn_nm'];

            // 商品名
            $shohin_nm = $value['shohin_nm'];
            if(isset($value['shohin_tkiyo_nm']) && $value['shohin_tkiyo_nm'] !== ''){
                $shohin_nm = $shohin_nm.'　'.$value['shohin_tkiyo_nm'];
            }

            // 金額
            $prc = $value['juchu_tnk'] * $value['juchu_suryo'];
            // 掛金役務
            if($YosanFlg){ // 実行予算書(下代)
                $ekimu_disp = number_format($value['gen_gaku']);
                $gedai_total += $value['gen_gaku']*1;
                self::$gedai_total += $value['gen_gaku']*1;
            } else { // 見積書(掛金役務)
                $ekimu_disp = '';
                $gedai_total = '';
            }
            // 割引
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0 || $value['nebiki_prc2'] <> 0){
                $row_arr[] = array($koumoku, $shohin_nm, $value['juchu_tnk'], $value['juchu_suryo'], $prc, '', $ekimu_disp);
                // 互助会割引
				if($value['gojokai_nebiki_prc'] <> 0) {
                    if($value['nebiki_prc'] <> 0 || $value['nebiki_prc2'] <> 0){
                        $row_arr[] = array('', '', '', '', $value['gojokai_nebiki_prc'], '', '');
                    } else {
                        $row_arr[] = array('', '', '', '', $value['gojokai_nebiki_prc'], $prc + $value['gojokai_nebiki_prc'], '');
                    }
				}
                // 割引１
                if($value['nebiki_prc'] <> 0){
                    if($value['nebiki_prc2'] <> 0){
                            $row_arr[] = array('', '　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', $value['nebiki_prc'], '', '');
                    } else {
                            $row_arr[] = array('', '　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', $value['nebiki_prc'], $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'], '');
                    }
                }
                // 割引２
                if($value['nebiki_prc2'] <> 0){
                    $row_arr[] = array('', '　　　　　　　　　　　　　　　＜割引＞', '', '', $value['nebiki_prc2'], $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'] + $value['nebiki_prc2'], '');
                }
            } else {
                $row_arr[] = array($koumoku, $shohin_nm, $value['juchu_tnk'], $value['juchu_suryo'], $prc, $prc, $ekimu_disp);
            }
            $total += $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'] + $value['nebiki_prc2'];
        }
        if($YosanFlg){
            $gedai_total = number_format($gedai_total);
        }
        $row_arr[] = array();
        if(isset($juchuDenpyo['seko_plan_cd'])){
            $row_arr[] = array('', '　　　　　　　　　　　　プラン外費用　計', '', '', '', $total, $gedai_total);
        } else {
            $row_arr[] = array('', '　　　　　　　　　　　　ご葬儀利用額　計', '', '', '', $total, $gedai_total);
        }
        
        // 出力
        $M1 = self::getSetArrM1();
        if($YosanFlg){ // 実行予算書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileNameYosan['M1']);
        } else { // 見積書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileName['M1']);
        }
    }

    /**
     * 明細書3(おもてなし費用)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $juchuDenpyo	受注伝票
     */
    private function outDataDetail3($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg) {
        
        $row_arr = array();

        // 返礼品
        $whereHenrei = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0020', "__raw_1"=>"shohin_kbn <> '0040'");
        $totalHenrei = self::printMsi($row_arr, $db, '返礼品', $whereHenrei, $YosanFlg);
        
        // 引出物
        $whereHikide = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0020', "shohin_kbn" => '0040');
        $totalHikide = self::printMsi($row_arr, $db, '引出物', $whereHikide, $YosanFlg);
        
        // 料理
        $whereTuya = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0030','__etc_orderby'=>array('use_kbn'));
        $totalTuya = self::printMsi($row_arr, $db, '料理', $whereTuya, $YosanFlg, TRUE);
        
        // その他
//        $whereSonota = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0030', "__raw_1"=>"(use_kbn <> '0' OR use_kbn IS NULL)");
//        $totalSonota = self::printMsi($row_arr, $db, 'その他', $whereSonota, $YosanFlg);
        $totalSonota = 0;
        
        // 出力
        if($YosanFlg){ // 実行予算書
            $M2 = self::getSetArrM2Y();
            $pdfObj->write_table($M2['set_arr'], $M2['meisai_row_height'], $row_arr, $M2['meisai_row_count'], __DIR__ . '/' . self::$sourceFileNameYosan['M2']);
        } else { // 見積書
            $M2 = self::getSetArrM2();
            $pdfObj->write_table($M2['set_arr'], $M2['meisai_row_height'], $row_arr, $M2['meisai_row_count'], __DIR__ . '/' . self::$sourceFileName['M2']);
        }
        
        // ヘッダー小計
        if(!($totalHenrei === 0 && $totalHikide === 0 && $totalTuya === 0 && $totalSonota === 0)){
            $pdfObj->write_num(array('x' => 25, 'y' => 122.5, 'width' => 72, 'height' => 9, 'font_size' => 10), $totalHenrei); // 返礼品利用額
            $pdfObj->write_num(array('x' => 101, 'y' => 122.5, 'width' => 72, 'height' => 9, 'font_size' => 10), $totalHikide); // 引出物利用額
            $pdfObj->write_num(array('x' => 180, 'y' => 122.5, 'width' => 72, 'height' => 9, 'font_size' => 10), $totalTuya); // 料理利用額
            $pdfObj->write_num(array('x' => 257, 'y' => 122.5, 'width' => 72, 'height' => 9, 'font_size' => 10), $totalSonota); // その他利用額
            $total = $totalHenrei + $totalHikide + $totalTuya + $totalSonota;
            if($YosanFlg){ // 実行予算書
                $pdfObj->write_num(array('x' => 325, 'y' => 122.5, 'width' => 78, 'height' => 9, 'font_size' => 10), $total); // 小計B
            } else { // 見積書
                $pdfObj->write_num(array('x' => 332, 'y' => 122.5, 'width' => 78, 'height' => 9, 'font_size' => 10), $total); // 小計B
            }
        }
    }
    
    /**
     * 明細書3表示用
	 * @param array $row_arr    出力配列
	 * @param Msi_Sys_Db $db	データベース
     * @param string $title 	タイトル
     * @param array $where      商品の対象条件
     * @return array $total    合計金額
     */
    private function printMsi(&$row_arr, $db, $title, $where, $YosanFlg, $UseFlg = FALSE) {
        $recData = DataMapper_Pdf0113::findMsi($db, $where);
        $cnt = 0;
        $hoshi = 0;
        $total = 0;
        $gedai_total = 0;
        $use_kbn = '';
        foreach ($recData as $value) {
            $cnt ++;
            // タイトル
            $koumoku = '';
            if($cnt === 1){
                $koumoku = $title;
            }
            // 用途
            if($UseFlg){
                if($value['use_kbn'] !== $use_kbn){
                    if(isset($value['use_kbn'])){
                        $cdnm = DataMapper_CodeNmMst::find($db, array('code_kbn'=>'4400', 'kbn_value_cd_num'=>$value['use_kbn']));
                        $koumoku = '料理（'.$cdnm[0]['kbn_value_lnm'].'）';
                    } else {
                        $koumoku = '料理（その他）';
                    }
                }
                $use_kbn = $value['use_kbn'];
            }
            // 商品名
            $shohin_nm = $value['shohin_nm'];
            if(isset($value['shohin_tkiyo_nm']) && $value['shohin_tkiyo_nm'] !== ''){
                $shohin_nm = $shohin_nm.'　'.$value['shohin_tkiyo_nm'];
            }
            // 税
            $zei_disp = '';
            if(isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] === '2'){ // 軽減税率
                $zei_disp .= '※';
            }
            switch ($value['zei_kbn']) {
                case 0:
                    $zei_disp .= '非';
                    break;
                case 1:
                    $zei_disp .= '内';
                    break;
            }
            // 金額
            $prc = $value['juchu_tnk'] * $value['juchu_suryo'];
            // サービス
            $hoshi_disp = '';
            if($YosanFlg){ // 実行予算書(下代)
                $hoshi_disp = number_format($value['gen_gaku']);
                $gedai_total += $value['gen_gaku']*1;
                self::$gedai_total += $value['gen_gaku']*1;
            } else { // 見積書(サービス)
                $hoshi_disp = $value['hoshi_disp'];
                $gedai_total = '';
            }
            // 割引
            if($value['nebiki_prc'] <> 0 || $value['nebiki_prc2'] <> 0){
                $row_arr[] = array($koumoku, $shohin_nm, $zei_disp, $value['juchu_tnk'], $value['juchu_suryo'], $prc, '', $hoshi_disp);
                // 割引１
                if($value['nebiki_prc'] <> 0){
                    if($value['nebiki_prc2'] <> 0){
                        $row_arr[] = array('', '　　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', '', $value['nebiki_prc'], '', '');
                    } else {
                        $row_arr[] = array('', '　　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', '', $value['nebiki_prc'], $prc+$value['nebiki_prc'], '');
                    }
                }
                // 割引２
                if($value['nebiki_prc2'] <> 0){
                    $row_arr[] = array('', '　　　　　　　　　　　　　　　　＜割引＞', '', '', '', $value['nebiki_prc2'], $prc+$value['nebiki_prc']+$value['nebiki_prc2'], '');
                }
            } else {
                $row_arr[] = array($koumoku, $shohin_nm, $zei_disp, $value['juchu_tnk'], $value['juchu_suryo'], $prc, $prc, $hoshi_disp);
            }
            $hoshi += $value['hoshi_prc'];
            $total += $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'] + $value['nebiki_prc2'];
        }
        if($cnt > 0){
            if($hoshi > 0){
                $row_arr[] = array('', '小計', '', '', '', '', $total, '');
                $row_arr[] = array('', 'サービス料(10%)', '', '', '', '', $hoshi, '');
                $total += $hoshi;
            }
            if($YosanFlg){
                $gedai_total = number_format($gedai_total);
            }
            $row_arr[] = array('', $title.'利用額小計', '', '', '', '', $total, $gedai_total);
            $row_arr[] = array();
        }

        return $total;
    }
    
    /**
     * 明細書4(立替金その他)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $juchuDenpyo	受注伝票
     */
    private function outDataDetail4($pdfObj, $db, $recHead, $juchuDenpyo, $YosanFlg) {
        
        $row_arr = array();

        // 立替金
        $whereTatekae = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0060', "__raw_1"=>"shohin_kbn <> '0040'");
        $totalTatekae = self::printMsi2($row_arr, $db, '立替金', $whereTatekae, $YosanFlg);
        
        // その他
        $whereSonota = array("denpyo_no" => $juchuDenpyo['denpyo_no'], "dai_bunrui_cd" => '0050');
        $totalSonota = self::printMsi2($row_arr, $db, 'その他', $whereSonota, $YosanFlg);
        
        // 出力
        $M1 = self::getSetArrM1();
        if($YosanFlg){ // 実行予算書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileNameYosan['M1']);
        } else { // 見積書
            $pdfObj->write_table($M1['set_arr'], $M1['meisai_row_height'], $row_arr, $M1['meisai_row_count'], __DIR__ . '/' . self::$sourceFileName['M1']);
        }
        
    }
    
    /**
     * 明細書4表示用
	 * @param array $row_arr    出力配列
	 * @param Msi_Sys_Db $db	データベース
     * @param string $title 	タイトル
     * @param array $where      商品の対象条件
     * @return array $total    合計金額
     */
    private function printMsi2(&$row_arr, $db, $title, $where, $YosanFlg) {
        $recData = DataMapper_Pdf0113::findMsi($db, $where);
        $cnt = 0;
        $hoshi = 0;
        $total = 0;
        $gedai_total = 0;
        foreach ($recData as $value) {
            $cnt ++;
            // タイトル
            $koumoku = '';
            if($cnt === 1){
                $koumoku = $title;
            }
            // 商品名
            $shohin_nm = $value['shohin_nm'];
            if(isset($value['shohin_tkiyo_nm']) && $value['shohin_tkiyo_nm'] !== ''){
                $shohin_nm = $shohin_nm.'　'.$value['shohin_tkiyo_nm'];
            }
            // 税
            $zei_disp = '';
            if(isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] === '2'){ // 軽減税率
                $zei_disp .= '※';
            }
            switch ($value['zei_kbn']) {
                case 0:
                    $zei_disp .= '非';
                    break;
                case 1:
                    $zei_disp .= '内';
                    break;
            }
            // 金額
            $prc = $value['juchu_tnk'] * $value['juchu_suryo'];
            // 掛金役務
            if($YosanFlg){ // 実行予算書(下代)
                $ekimu_disp = number_format($value['gen_gaku']);
                $gedai_total += $value['gen_gaku']*1;
                self::$gedai_total += $value['gen_gaku']*1;
            } else { // 見積書(掛金役務)
                $ekimu_disp = '';
                $gedai_total = '';
            }
            // 割引
            if($value['nebiki_prc'] <> 0 || $value['nebiki_prc2'] <> 0){
                $row_arr[] = array($koumoku, $shohin_nm, $value['juchu_tnk'], $value['juchu_suryo'], $prc, '', $ekimu_disp);
                // 割引１
                if($value['nebiki_prc'] <> 0){
                    if($value['nebiki_prc2'] <> 0){
                        $row_arr[] = array('', '　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', $value['nebiki_prc'], '', '');
                    } else {
                        $row_arr[] = array('', '　　　　　　　　　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', $value['nebiki_prc'], $prc + $value['nebiki_prc'], '');
                    }
                }
                // 割引２
                if($value['nebiki_prc2'] <> 0){
                    $row_arr[] = array('', '　　　　　　　　　　　　　　　＜割引＞', '', '', $value['nebiki_prc2'], $prc + $value['nebiki_prc'] + $value['nebiki_prc2'], '');
                }
            } else {
                $row_arr[] = array($koumoku, $shohin_nm, $value['juchu_tnk'], $value['juchu_suryo'], $prc, $prc, $ekimu_disp);
            }
            $hoshi += $value['hoshi_prc'];
            $total += $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'] + $value['nebiki_prc2'];
        }
        if($cnt > 0){
            if($hoshi > 0){
                $row_arr[] = array('', '小計', '', '', '', $total, '');
                $row_arr[] = array('', 'サービス料(10%)', '', '', '', $hoshi, '');
                $total += $hoshi;
            }
            if($YosanFlg){
                $gedai_total = number_format($gedai_total);
            }
            $row_arr[] = array('', $title.'利用額小計', '', '', '', $total, $gedai_total);
            $row_arr[] = array();
        }

        return $total;
    }

    /**
     * 見積書（法事）
     * <AUTHOR> Kobayashi
     * @since 2022/08/01
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outData_houji($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub) {

        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        // 施行情報
        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
            $recHead['print_tanto'] = App_Utils::getTantoNm();
        }
        // 受注伝票
        $juchuDenpyo = DataMapper_JuchuDenpyo::findOne($db, array('seko_no' => $recHead['seko_no'], 'data_kbn' => $data_kbn));

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;
        
        // 表紙
        self::outDataHyoshi($pdfObj, $db, $recHead, $juchuDenpyo);

        // 鏡
        self::outDataKagami_houji($pdfObj, $db, $recHead, $juchuDenpyo);
        
        // 明細書
        self::outDataDetail_houji($pdfObj, $db, $recHead, $juchuDenpyo);
        
        // 鏡に合計金額出力
        self::outDataKagamiSum_houji($pdfObj, $db, $recHead, $juchuDenpyo);

        // ページ等を出力
        $seikyuRec = DataMapper_PdfCommon::getOneSekoSekyuInfo($recHead['seko_no']);
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            if($page > 2){ // 明細のみ
                $pdfObj->write_string(array('x' => 47, 'y' => 53, 'width' => 180, 'height' => 9, 'font_size' => 14, 'align' => 'C'), $seikyuRec['sekyu_nm']); // 請求先名
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>50, 'y'=>802, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'※は軽減税率対象商品');
                }
                $pdfObj->write_date(array('x' => 450, 'y' => 804, 'width' => 500, 'height' => 9, 'font_size' => 9), $recHead['issue_date'], "y年n月j日", 'gG', 'AUTO'); // 発行日
            }
            if($page > 1){
                // ページ
                $pdfObj->write_string(array('x' => 45, 'y' => 820, 'width' => 530, 'height' => 9, 'font_size' => 9.5, 'align' => 'C'), ($page-1) . '/' . ($numPages-1));
            }
        }
    }
    
    /**
     * 御見積書鏡(法事)
	 * @param pdfObj $pdfObj PDFオブジェクト
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagami_houji($pdfObj, $db, $recHead, $juchuDenpyo) {
        
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji['K']);
        
        // お客様情報
        $seikyuRec = DataMapper_PdfCommon::getOneSekoSekyuInfo($recHead['seko_no']);
        $pdfObj->write_string(array('x' => 43, 'y' => 54, 'width' => 180, 'height' => 9, 'font_size' => 15, 'align' => 'C'), $seikyuRec['sekyu_nm']); // 請求先名
        $pdfObj->write_string(array('x' => 43, 'y' => 74, 'width' => 80, 'height' => 9, 'font_size' => 10), '〒'.$seikyuRec['yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 105, 'y' => 74, 'width' => 140, 'height' => 9, 'font_size' => 10), $seikyuRec['addr1']); // 住所1
        $pdfObj->write_string(array('x' => 105, 'y' => 84, 'width' => 140, 'height' => 9, 'font_size' => 10), $seikyuRec['addr2']); // 住所2
        $pdfObj->write_string(array('x' => 166, 'y' => 95, 'width' => 80, 'height' => 9, 'font_size' => 9), $seikyuRec['tel']); // TEL
        $pdfObj->write_date(array('x' => 166, 'y' => 106, 'width' => 80, 'height' => 9, 'font_size' => 9), $recHead['sougi_ymd'], "y年n月j日", 'gG', 'AUTO'); // 施行日
        
        // 会社情報
        DataMapper_PdfCommon::outKaisyaInfo2($db, $pdfObj);

        // フッダー
        $pdfObj->write_string(array('x'=>450, 'y'=>773, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['bumon_lnm']); // 受付部門
        $pdfObj->write_string(array('x'=>450, 'y'=>784, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['uketuke_tanto']); // 担当者
        $pdfObj->write_date(array('x' => 450, 'y' => 795, 'width' => 500, 'height' => 9, 'font_size' => 9), $recHead['issue_date'], "y年n月j日", 'gG', 'AUTO'); // 発行日
    }
    
    /**
     * 御見積書鏡合計(法事)
	 * @param pdfObj $pdfObj PDFオブジェクト
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagamiSum_houji($pdfObj, $db, $recHead, $juchuDenpyo) {

        $pdfObj->setPage(2);

        $y = 159.4;
        $y_zei = 467;
        $hight = 16.2;
        $titleA = $titleB = FALSE;

        // プラン
        $gojoMember = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $recHead['seko_no'], '__raw_yoto_kbn' => "yoto_kbn IN (1,2)"));
        if (Msi_Sys_Utils::myCount($gojoMember) > 0) {
            $cdnm = DataMapper_CodeNmMstEx::find($db, array('code_kbn' => '1490', 'kbn_value_cd_num' => $gojoMember[0]['yoto_kbn']));
            $index = $cdnm[0]['kbn_value_snm'].'　'.trim($gojoMember[0]['course_snm_cd']).'-'.trim($gojoMember[0]['kain_no']).'　'.$gojoMember[0]['kanyu_nm'].' 様';
            $pdfObj->write_string(array('x' => 50, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), $index); // 項目
            $pdfObj->write_date(array('x' => 440, 'y' => $y, 'width' => 135, 'height' => 9, 'font_size' => 11), $gojoMember[0]['kanyu_dt'], "加入日：Y年n月j日"); // 備考
            $y += $hight;
        }

        // データ取得
        $kagamiInfoHouji = App_Utils2::getJuchuKagamiHouji($juchuDenpyo['denpyo_no']);
        
        // 表出力
        foreach ($kagamiInfoHouji as $key => $value) {
            // 1.法要・墓参り費用
            if((array_search($key, self::$kihonH) !== FALSE) && $value['prc'] <> 0){
                if($titleA === FALSE && $recHead['kaiki_kbn'] == '1'){ // タイトル(回忌のみ)
                    $pdfObj->write_string(array('x' => 50, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), '法要・墓参り費用'); // タイトル
                    $y += $hight;
                    $titleA = TRUE;
                }
                $pdfObj->write_string(array('x' => 80, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 350, 'y' => $y, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y += $hight;
            }
            // 2.おもてなし費用
            if((array_search($key, self::$omotenashiH) !== FALSE) && $value['prc'] <> 0){
                if($titleB === FALSE && $recHead['kaiki_kbn'] == '1'){ // タイトル
                    $pdfObj->write_string(array('x' => 50, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), 'おもてなし費用'); // タイトル
                    $y += $hight;
                    $titleB = TRUE;
                }
                $pdfObj->write_string(array('x' => 80, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 350, 'y' => $y, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y += $hight;
            }
            if($key === 'syokeiAB'){
                $pdfObj->write_string(array('x' => 50, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), '小計'); // 項目
                $pdfObj->write_num(array('x' => 350, 'y' => $y, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                if($juchuDenpyo['hoshi_prc_sum'] <>0){
                    $pdfObj->write_string(array('x' => 440, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), '含サービス料：'.number_format($juchuDenpyo['hoshi_prc_sum']).'円');
                }
                $y += $hight;
            }
            // 3.その他費用
            if((array_search($key, self::$sonotaH) !== FALSE) && $value['prc'] <> 0){
                $pdfObj->write_string(array('x' => 80, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 350, 'y' => $y, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y += $hight;
            }

            if((array_search($key, self::$mitsumoriH) !== FALSE) && $value['prc'] <> 0){
                $pdfObj->write_string(array('x' => 80, 'y' => $y, 'width' => 280, 'height' => 9, 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 350, 'y' => $y, 'width' => 85, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y += $hight;
            }
            if((array_search($key, self::$zeiH) !== FALSE) && $value['prc'] <> 0){
                $pdfObj->write_string(array('x' => 50, 'y' => $y_zei, 'width' => 190, 'height' => 9, 'align'=> 'R', 'font_size' => 11), $value['title']); // 項目
                $pdfObj->write_num(array('x' => 245, 'y' => $y_zei, 'width' => 95, 'height' => 9, 'font_size' => 11), $value['prc']); // 金額
                $y_zei += $hight;
            }
        }
        // 御見積金額
        $pdfObj->write_num(array('x' => 350, 'y' => 418, 'width' => 85, 'height' => 9, 'font_size' => 11), $kagamiInfoHouji['total']['prc']); // 金額
        // 備考
        $gojo = DataMapper_SekoGojokaiInfo::findOne($db, array('seko_no'=>$recHead['seko_no']));
        if(isset($gojo['text_free2'])){
            $pdfObj->write_strings2(array('x'=>50, 'y'=>735, 'width'=>325, 'height'=>65), $gojo['text_free2']); // 見積書備考
        }
    }
    
    /**
     * 明細書(法事)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $juchuDenpyo	受注伝票
     */
    private function outDataDetail_houji($pdfObj, $db, $recHead, $juchuDenpyo) {
        
        // 受注伝明細取得
        $recKihonMsi = DataMapper_Pdf0113::findMsi($db, array("denpyo_no" => $juchuDenpyo['denpyo_no']));
        if (Msi_Sys_Utils::myCount($recKihonMsi) == 0) {
            return;
        }

        $cnt = 0;
        $row_arr = array();
        $chu_bunrui = '';
        $chu_bunrui_total_prc = 0;

        foreach ($recKihonMsi as $value) {
            $cnt ++;

            // 項目
            if($cnt === 1){ // 1行目
                if($recHead['kaiki_kbn'] == '1'){ // 回忌のみ
                    $row_arr[] = array('',$value['chu_bunrui_nm'], '', '', '', '', '', '');
                }
            } else if(($chu_bunrui !== '') && ($chu_bunrui !== $value['chu_bunrui_cd'])){ // 2行目以降
                // 合計
                $row_arr[] = array();
                $row_arr[] = array('','', '合計', '', '', '', '', $chu_bunrui_total_prc);
                $chu_bunrui_total_prc = 0;
                // 中分類名
                $row_arr[] = array();
                if($recHead['kaiki_kbn'] == '1'){ // 回忌のみ
                    $row_arr[] = array('',$value['chu_bunrui_nm'], '', '', '', '', '', '');
                }
            }
            $chu_bunrui = $value['chu_bunrui_cd'];

            // 税
            $zei_disp = '';
            if(isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] === '2'){ // 軽減税率
                $zei_disp .= '※';
            }
            switch ($value['zei_kbn']) {
                case 0:
                    $zei_disp .= '非';
                    break;
                case 1:
                    $zei_disp .= '内';
                    break;
            }
            
            // 金額
            $prc = $value['juchu_tnk'] * $value['juchu_suryo'];
            // 割引
            $row_arr[] = array($value['hoshi_disp'],'', $value['shohin_nm'], $value['shohin_tkiyo_nm'], $value['juchu_suryo'], $zei_disp, $value['juchu_tnk'], $prc);
            if($value['nebiki_prc'] <> 0){
                $row_arr[] = array('','', '　　　　　　　＜'.$value['waribiki_ritu'].'割引＞', '', '', '', '', $value['nebiki_prc']);
            }
            if($value['nebiki_prc2'] <> 0){
                $row_arr[] = array('','', '　　　　　　　＜割引＞', '', '', '', '', $value['nebiki_prc2']);
            }
            $chu_bunrui_total_prc += $prc + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'] + $value['nebiki_prc2'];
            $hoshi += $value['hoshi_prc'];
        }
        if($cnt > 0){
            $row_arr[] = array();
            if($hoshi > 0){
                $row_arr[] = array('','', '小計', '', '', '', '', $chu_bunrui_total_prc);
                $row_arr[] = array('','', 'サービス料(10%)', '', '', '', '', $hoshi);
                $chu_bunrui_total_prc += $hoshi;
            }
            $row_arr[] = array('','', '合計', '', '', '', '', $chu_bunrui_total_prc);
        }
        // 出力
        $M = self::getSetArrMhouji();
        $pdfObj->write_table($M['set_arr'], $M['meisai_row_height'], $row_arr, $M['meisai_row_count'], __DIR__ . '/' . self::$sourceFileNameHouji['M']);
    }
    
    /**
     * 見積履歴書(事前相談)
     * <AUTHOR> Kobayashi
     * @since 2022/08/04
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outDataMeisaiHistory($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei = false) {

        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
        }
        
        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;

        $denpyo_flg = 0; // 受注伝票の有無
        $recMsi = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
        if(Msi_Sys_Utils::myCount($recMsi) > 0){
            $denpyo_no = $recMsi[0]['denpyo_no'];
            $denpyo_flg += 1;
            $maxHistoryNo = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $denpyo_no) + 1;
        } else {
            $recMsiHis = DataMapper_Pdf0113::findMsiHistory($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
            if(Msi_Sys_Utils::myCount($recMsiHis) > 0){
                $denpyo_no = $recMsiHis[0]['denpyo_no'];
                $maxHistoryNo = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $denpyo_no);
            } else {
                return FALSE;
            }
        }

        $history_no = 0;
        for($h = $maxHistoryNo; $h > 0; $h--){
            if($denpyo_flg == 1){
                $rec = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
                $denpyo_flg = 0;
            } else {
                $history_no = $h;
                $rec = DataMapper_Pdf0113::findMsiHistory($db, array("seko_no" => $seko_no, "history_no" => $history_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
            }
            // 明細書
            self::outDataDetailHistory($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead, $rec, $history_no);
            
            if($h === $maxHistoryNo){
                $onePages = $pdfObj->getNumPages();
            }
        }
        $allPages = $pdfObj->getNumPages();
        $onePage = 1;
        for ($i = 0; $i < $allPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            // 軽減税率対応 凡例
            if (self::_isKeigenAppliedCtxt() ) {
                $pdfObj->write_string(array('x'=>470, 'y'=>732, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象');
            }
            // ページ
            $pdfObj->write_string(array('x' => 45, 'y' => 785, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $onePage . '/' . $onePages);
            if($onePage < $onePages){
                $onePage++;
            } else {
                $onePage = 1;
            }
        }
        return TRUE;
        
    }
    
    /**
     * 明細を出力(見積履歴書)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataDetailHistory($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead, $recMsi, $history_no) {
        
        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 145;
        static $meisai_row_height1 = 23.5;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 76;
        static $meisai_row_height2 = 23.5;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $dai_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    // 合計
                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, null, null, null, $total, null, null, null, null);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['dai_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            if (isset($value['nebiki_ritu']) && $value['nebiki_ritu'] !== '0') { // 割引率
                $nebiki_ritu = $value['nebiki_ritu'].'％';
            } else {
                $nebiki_ritu = '';
            }
            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $nebiki_prc; // 請求金額
//            if ($nebiki_prc == 0) {
//                $nebiki_prc = null;
//            }
            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if(isset($value['plan_shohin_nm'])){
                $biko1 .= $value['plan_shohin_nm'].'から変更';
            }
            if($value['hoshi_disp'] !== ''){
                $biko2 .= $value['hoshi_disp'].'　';
            }
            $biko2 .= $value['shohin_tkiyo_nm'];

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['juchu_suryo'], $value['tani_nm'], $value['juchu_tnk'], $value['juchu_prc'], $nebiki_ritu, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp']);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['juchu_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['juchu_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['juchu_prc'];
                }
            }

            $total_ippan += $value['juchu_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            // 合計
            $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, null, null, null, $total, null, null, null, null);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
//        self::outZeiUchiwake2($row_arr); // 内訳
        $out_tax_total = 0;
        if(Msi_Sys_Utils::myCount($taxArr) > 0){ // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
       }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $row_arr[] = array('【合　計】', null, null, null, null, null, null, null, $total_prc_all, null, null, null, null); // 合計
        $row_arr[] = array();
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no, '__etc_orderby'=>array('yoto_kbn', 'kain_no',)));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        
        // 御見積金額
        $mitsumori_prc = $total_prc_all + $kaiin_total;
        $row_arr[] = array('【御見積金額】', null, null, null, null, null, $zei_nebikimae_total, null, $mitsumori_prc, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, null, '以下余白', null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName['M1']);
        self::outHeaderHistory($pdfObj, $recHead, $history_no);
        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName['M2']);
        
    }
    
    /**
     * 予算実行書用備考
     * <AUTHOR> kobayashi
     * @since 2023/07/06
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function setYosanBiko($db, $pdfObj, $recHead, $juchuDenpyo)
    {
        // 見積金額
        $kagamiInfo = App_Utils2::getJuchuKagami($juchuDenpyo['denpyo_no']);
        $mitumori = $kagamiInfo['syokeiC']['prc'];
        // 仕入額
        $siire = 0;
        $siire += self::$gedai_total;
        $pdfObj->write_string(array('x' => 50, 'y' => 743, 'width' => 130, 'height' => 15, 'font_size' => 11), '仕入額：');
        $pdfObj->write_num(array('x' => 90, 'y' => 743, 'width' => 85, 'height' => 9, 'font_size' => 11), $siire);
        // 人件費：コード名称マスタ0040:葬儀形式の備考
        $jinken = 0;
        if(isset($recHead['keishiki_kbn'])){
            $cdnm = DataMapper_CodeNmMst::find($db, array('code_kbn'=>'0040', 'kbn_value_cd_num'=>$recHead['keishiki_kbn']));
            $jinken += $cdnm[0]['biko'];
        }
        $pdfObj->write_string(array('x' => 200, 'y' => 743, 'width' => 130, 'height' => 15, 'font_size' => 11), '人件費：');
        $pdfObj->write_num(array('x' => 240, 'y' => 743, 'width' => 85, 'height' => 9, 'font_size' => 11), $jinken);
        // 粗利額：見積金額 - 仕入額
        $arari = 0;
        $arari += $mitumori - $siire;
        $pdfObj->write_string(array('x' => 50, 'y' => 756, 'width' => 130, 'height' => 15, 'font_size' => 11), '粗利額：');
        $pdfObj->write_num(array('x' => 90, 'y' => 756, 'width' => 85, 'height' => 9, 'font_size' => 11), $arari);
        // 粗利率：粗利額 / 見積金額
        $arari_rts = 0;
        $arari_rts += round(($arari / $mitumori)*100, 1);
        $pdfObj->write_string(array('x' => 200, 'y' => 756, 'width' => 130, 'height' => 15, 'font_size' => 11), '粗利率：');
        $pdfObj->write_string(array('x' => 240, 'y' => 756, 'width' => 85, 'height' => 9, 'font_size' => 11, 'align' => 'R'), $arari_rts.'％');
        // 粗利額(掛金含む) ：見積金額 + セット金額 + 充当金額*-1 + 解約金額 - 仕入額
        $arari_kake = 0;
        $arari_kake += $mitumori + $juchuDenpyo['sougi_keiyaku_prc'] + ($juchuDenpyo['etc_harai_prc']*-1) + $juchuDenpyo['sougi_kaiyaku_prc '] - $siire;
        $pdfObj->write_string(array('x' => 50, 'y' => 769, 'width' => 130, 'height' => 15, 'font_size' => 11), '粗利額：');
        $pdfObj->write_num(array('x' => 90, 'y' => 769, 'width' => 85, 'height' => 9, 'font_size' => 11), $arari_kake);
        $pdfObj->write_string(array('x' => 50, 'y' => 782, 'width' => 130, 'height' => 15, 'font_size' => 11), '(掛金含む)');
        // 粗利率(掛金含む) ：粗利額(掛金含む) / 見積金額
        $arari_kake_rts = 0;
        $arari_kake_rts += round(($arari_kake / $mitumori)*100, 1);
        $pdfObj->write_string(array('x' => 200, 'y' => 769, 'width' => 130, 'height' => 15, 'font_size' => 11), '粗利率：');
        $pdfObj->write_string(array('x' => 240, 'y' => 769, 'width' => 85, 'height' => 9, 'font_size' => 11, 'align' => 'R'), $arari_kake_rts.'％');
        $pdfObj->write_string(array('x' => 200, 'y' => 782, 'width' => 130, 'height' => 15, 'font_size' => 11), '(掛金含む)');
        // 純利益：粗利額 - 人件費
        $junri = 0;
        $junri += $arari - $jinken;
        $pdfObj->write_string(array('x' => 50, 'y' => 795, 'width' => 130, 'height' => 15, 'font_size' => 11), '純利益：');
        $pdfObj->write_num(array('x' => 90, 'y' => 795, 'width' => 85, 'height' => 9, 'font_size' => 11), $junri);
        // 純利益率：純利益 / 見積金額
        $junri_rts = 0;
        $junri_rts += round(($junri / $mitumori)*100, 1);
        $pdfObj->write_string(array('x' => 200, 'y' => 795, 'width' => 130, 'height' => 15, 'font_size' => 11), '純利益率：');
        $pdfObj->write_string(array('x' => 240, 'y' => 795, 'width' => 85, 'height' => 9, 'font_size' => 11, 'align' => 'R'), $junri_rts.'％');
    }
    
    /**
     * 明細表ヘッダー
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeader($pdfObj, $recHead)
    {
        $seikyuRec = DataMapper_PdfCommon::getOneSekoSekyuInfo($recHead['seko_no']);
        $pdfObj->write_string(array('x' => 432, 'y' => 30, 'width' => 130, 'height' => 15, 'font_size' => 10), '発行日：');
        $pdfObj->write_date(array('x' => 475, 'y' => 30, 'width' => 500, 'height' => 9, 'font_size' => 10), $recHead['issue_date'], "y年n月j日", 'gG', 'AUTO'); // 発行日
        $pdfObj->write_date(array('x' => 70, 'y' => 54, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 10), $recHead['issue_date'], "Y年n月j日"); // 受付日
        $pdfObj->write_string(array('x' => 70, 'y' => 68, 'width' => 130, 'height' => 15, 'font_size' => 10), $seikyuRec['sekyu_nm'].'　様'); // お客様名
        $pdfObj->write_string(array('x' => 200, 'y' => 68, 'width' => 130, 'height' => 15, 'font_size' => 10), $seikyuRec['tel']); // TEL
        if(isset($recHead['mg_addr2'])){ // 住所
            $pdfObj->write_string(array('x' => 70, 'y' => 83, 'width' => 300, 'height' => 15, 'font_size' => 10), $seikyuRec['addr1'].$seikyuRec['addr2']);
        } else {
            $pdfObj->write_string(array('x' => 70, 'y' => 83, 'width' => 300, 'height' => 15, 'font_size' => 10), $seikyuRec['addr1']);
        }
    }
    
    /**
     * 明細表ヘッダー(見積履歴)
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeaderHistory($pdfObj, $recHead, $history_no)
    {
        $date_format = "Y年n月j日";
        
        // 施行情報
        $k_nm = '故　'.$recHead['k_nm'].'様　ご葬儀'; // 故人名
        if($history_no === 0){
            $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 130, 'height' => 15, 'font_size' => 9), '受付番号：'.$recHead['seko_no']); // 受付番号
        } else {
            $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 130, 'height' => 15, 'font_size' => 9), '受付番号：'.$recHead['seko_no'].'-'.$history_no); // 受付番号-履歴NO
        }
        $pdfObj->write_string(array('x' => 30, 'y' => 31, 'width' => 130, 'height' => 15, 'font_size' => 9), $k_nm); // 故人
        $pdfObj->write_string(array('x' => 30, 'y' => 42, 'width' => 130, 'height' => 15, 'font_size' => 9), '契約担当： '.$recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 30, 'y' => 58, 'width' => 130, 'height' => 15, 'font_size' => 9), '施行日');
        $pdfObj->write_date(array('type' => 'time', 'x' =>70, 'y' => 58, 'width' =>  130, 'height' =>  15, 'font_size' => 9), $recHead['kokubetsu_ymd'], $date_format); // 施行日
        
        // 見積書発行日
        $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['issue_date'], $date_format);
        
        // 取扱店舗(部門)
        $pdfObj->write_string(array('x'=>355, 'y'=>746, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['bumon_lnm']); // 取扱店舗名
        $pdfObj->write_string(array('x'=>355, 'y'=>757, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['bumon_addr1_nm'].$recHead['bumon_addr2_nm']); // 取扱店舗住所
        $pdfObj->write_string(array('x'=>355, 'y'=>768, 'width'=>500, 'height'=>9, 'font_size' => 9), '電話 '.$recHead['bumon_tel']); // 取扱店舗TEL
        
        // 契約団体
        $pdfObj->write_string(array('x'=>400, 'y'=>93, 'width'=>500, 'height'=>9, 'font_size' => 9), $recHead['keiyaku_nm']); // 契約団体1
    }
    
    /**
     * 明細表の表示情報取得(M1)
     *
     * <AUTHOR> kobayashi
     * @since 2020/12/15
     * @return array
     */
    protected function getSetArrM1()
    {
        static $meisai_top = 121.5;
        $set_arr[] = array('x' => 23, 'y' => $meisai_top, 'width' => 88, 'height' => 15); // 項目
        $set_arr[] = array('x' => 116, 'y' => $meisai_top, 'width' => 207, 'height' => 15); // 品目
        $set_arr[] = array('x' => 334, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 提供単価
        $set_arr[] = array('x' => 388, 'y' => $meisai_top, 'width' => 22, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 415, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 提供金額
        $set_arr[] = array('x' => 468, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 差引額
        $set_arr[] = array('x' => 520, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'align' => 'R'); // 掛金役務

        return array('set_arr' => $set_arr, 'meisai_row_height' => 16.65, 'meisai_row_count' => 40);
    }

    /**
     * 明細表の表示情報取得(M2)
     *
     * <AUTHOR> kobayashi
     * @since 2020/12/15
     * @return array
     */
    protected function getSetArrM2()
    {
        static $meisai_top = 171.5;
        $set_arr[] = array('x' => 23, 'y' => $meisai_top, 'width' => 88, 'height' => 15); // 項目
        $set_arr[] = array('x' => 116, 'y' => $meisai_top, 'width' => 225, 'height' => 15); // 品目
        $set_arr[] = array('x' => 342, 'y' => $meisai_top, 'width' => 20, 'height' => 15); // 軽減税率マーク
        $set_arr[] = array('x' => 359, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 提供単価
        $set_arr[] = array('x' => 412, 'y' => $meisai_top, 'width' => 26, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 444, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 提供金額
        $set_arr[] = array('x' => 497, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 差引額
        $set_arr[] = array('x' => 551, 'y' => $meisai_top, 'width' => 20, 'height' => 15, 'align' => 'C'); // サービス

        return array('set_arr' => $set_arr, 'meisai_row_height' => 16.65, 'meisai_row_count' => 37);
    }
    /**
     * 明細表の表示情報取得(M2)実行予算書
     *
     * <AUTHOR> kobayashi
     * @since 2020/12/15
     * @return array
     */
    protected function getSetArrM2Y()
    {
        static $meisai_top = 171.5;
        $set_arr[] = array('x' => 23, 'y' => $meisai_top, 'width' => 88, 'height' => 15); // 項目
        $set_arr[] = array('x' => 116, 'y' => $meisai_top, 'width' => 220, 'height' => 15); // 品目
        $set_arr[] = array('x' => 337, 'y' => $meisai_top, 'width' => 20, 'height' => 15); // 軽減税率マーク
        $set_arr[] = array('x' => 358, 'y' => $meisai_top, 'width' => 45, 'height' => 15, 'type' => 'num'); // 提供単価
        $set_arr[] = array('x' => 407, 'y' => $meisai_top, 'width' => 26, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 436, 'y' => $meisai_top, 'width' => 45, 'height' => 15, 'type' => 'num'); // 提供金額
        $set_arr[] = array('x' => 482, 'y' => $meisai_top, 'width' => 45, 'height' => 15, 'type' => 'num'); // 差引額
        $set_arr[] = array('x' => 527, 'y' => $meisai_top, 'width' => 45, 'height' => 15, 'align' => 'R'); // 下代

        return array('set_arr' => $set_arr, 'meisai_row_height' => 16.65, 'meisai_row_count' => 37);
    }

    /**
     * 明細表の表示情報取得(法事)
     *
     * <AUTHOR> kobayashi
     * @since 2020/12/15
     * @return array
     */
    protected function getSetArrMhouji()
    {
        static $meisai_top = 92;
        $set_arr[] = array('x' =>  30, 'y' => $meisai_top, 'width' => 125, 'height' => 15); // サービス料
        $set_arr[] = array('x' =>  45, 'y' => $meisai_top, 'width' => 125, 'height' => 15); // 項目
        $set_arr[] = array('x' => 168, 'y' => $meisai_top, 'width' => 109, 'height' => 15); // 品目
        $set_arr[] = array('x' => 276, 'y' => $meisai_top, 'width' => 100, 'height' => 15); // 摘要
        $set_arr[] = array('x' => 357, 'y' => $meisai_top, 'width' =>  60, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 398, 'y' => $meisai_top, 'width' =>  66, 'height' => 15, 'align' => 'C');  // 税
        $set_arr[] = array('x' => 445, 'y' => $meisai_top, 'width' =>  58, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 505, 'y' => $meisai_top, 'width' =>  68, 'height' => 15, 'type' => 'num'); // 金額
        
        return array('set_arr' => $set_arr, 'meisai_row_height' => 15.4, 'meisai_row_count' => 46);
    }
    
    /**
     * PDFのファイル名を施行No + 葬家名 + タイトル 形式で取得する
     * 
     * <AUTHOR> Matsuyama
     * @since 2016/09/06
     * @return string   PDFのファイル名
     */
    public function getFileName($db, $seko_no, $title) {
        $file_name = $title;
        $rec = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$seko_no) );
        if (Msi_Sys_Utils::myCount($rec) > 0) {
			//葬家名の特殊文字対応
			$file_name = Msi_Sys_Utils::normFilename($seko_no.$rec['souke_nm'].$title);
        }
        return $file_name;
    }

    /**
     * 消費税金額の内訳を出力する(明細)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param App_Pdf  $pdfObj
     */
    protected function outZeiUchiwake2(&$row_arr)
    {
        $seko_no     = self::$_p_seko_no;
        $seko_no_sub = self::$_p_seko_no_sub;
        $data_kbn    = self::$_p_data_kbn;

        $db = Msi_Sys_DbManager::getMyDb();

        $shohizei = App_KeigenUtils::getMitsuShohizeiEasy3($db, $seko_no, $seko_no_sub, $data_kbn);

        foreach ($shohizei as $value) {
            if($value[1] !== 0){
                $row_arr[] = array(' '.$value[0], null, null, null, null, null, null, null, $value[1], null, null, null, null);
            }
        }
    }

    /**
     * 消費税基準日(sougi_ymd)を設定する
     * juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param $pdfObj
     * @return void
     */
    protected function _prepKijunYmdKeigen($seko_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );
        if($sougi_ymd === null){
            self::$_kijun_ymd = Msi_Sys_Utils::getDate();
        } else {
            self::$_kijun_ymd = $sougi_ymd;
        }
    }
    
    /**
     * 伝票情報を取得
     *
     * <AUTHOR> kobayashi
     * @since 2023/07/05
     * @param $db
     * @param $denpyo_no
     * @param $plan
     * @return $shohin_tkiyo_nm
     */
    protected function getJuchuInfo($db, $denpyo_no, $plan)
    {
        $plan_juchu_info = array();
        $rec = DataMapper_Pdf0113::findMsi($db, array("denpyo_no" => $denpyo_no
                , "dai_bunrui_cd" => $plan['dai_bunrui_cd'], "chu_bunrui_cd" => $plan['chu_bunrui_cd'], "shohin_kbn" => $plan['shohin_kbn'], "shohin_cd" => $plan['shohin_cd'], "upgrade_kbn" => '1'));
        if (isset($rec[0])) {
            $plan_juchu_info = $rec[0];
        } else {
            $plan_juchu_info = $plan;
        }
        return $plan_juchu_info;
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @return boolean
     */
    protected function _isKeigenAppliedCtxt()
    {
        $kijun_ymd = self::$_kijun_ymd;
        if ( $kijun_ymd == null ) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ( $keigenBgnYmd <= $kijun_ymd ) {
            return true;
        }
        return false;
    }
    
}
