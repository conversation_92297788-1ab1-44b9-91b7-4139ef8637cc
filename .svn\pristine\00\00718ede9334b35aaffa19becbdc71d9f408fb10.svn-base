/** 
 * @fileoverview 法事打合せ 一般化商品(商品選択,返礼品,料理)購入
 *   cf. juchu.itemex.js
 */

var appitemex = appitemex || {};
$(function() {
    "use strict";

    var utils = window.msiBbUtils;
    var myImageLoaded = false;
    var myImagePreClicked = null;
    var mySwiper = null;
    var myPickerType = 'swiper-slide'; // swiper-slide, swiper-cube, classic
    if ( msiLib2.iPadPatch() ) { // XXX temp
        myPickerType = 'swiper-slide';
    }

    var curLvl0 = null; // category区分に相当. ?
    var curLvl1 = null; // 最上位タブ(通夜、初七日料理等)のindex  ex.1,2,...
    var curLvl2 = null; // 絞り込み条件のindex  ex.1,2,...
    var tabLvl2Top = null; // tabBtn offset
    var sekoNo = null;
    var orgShohinData = {}; // 変更前商品データ  1回でも変更が掛かったもの
    var newShohinData = {}; // 変更後商品データ
    var orgEtcData = { allergy:{} }; // 変更前(allergyメモ等)データ  1回でも変更が掛かったもの
    var newEtcData = { allergy:{} }; // 変更後(allergyメモ等)データ
    var scrollTopLast = null; // 商品選択スクロール位置保存
    var isNoChangeCheck = false; // true: isChanged() チェックをしない
    var isForceChanged = false; // 変更されたものとみなす


    var msg_kakutei = '見積確定済みのため変更できません';

    var MeisaiModel = Backbone.Model.extend({
        defaults: function() {
            return {
                seq_no: meisaiList.nextSeqNo(),
                line_no: -1,
            };
        },

        validation: {
        },

        labels: {
        },
    }); // MeisaiModel

    var MeisaiCollection = Backbone.Collection.extend({
        model: MeisaiModel,

        nextSeqNo: function() {
            ++ MeisaiCollection.seq_cnt;
            return MeisaiCollection.seq_cnt;
        },

        resetLineNo: function() {
            var i, max, m;
            for ( i = 0, max=this.length ; i < max ; i++ ) {
                m = this.at(i);
                m.set( 'line_no', i+1 );
            }
        },
    },
    { // classProperties
        seq_cnt: 0
    }); // MeisaiCollection

    var AppModel = Backbone.Model.extend({

        defaults: function() {
            return {
/*
                catLvl0: null,
                catLvl1: null,
                catLvl2: null,
                showStyle: 'b', // b:block, l:list
*/
            }
        },

        validation: {
        },

        labels: {
        },
    }); // AppModel

    var AppView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click .lvl1_tab li a": "changeTab",
            "click .lvl2_tab li a": "changeTabLvl2",
            "click .view_style_opt li a": "changeTab2", // block|list 表示切り替え処理
            "change #item-cond2-id": "changeCond2",
            "click #btn_opt_1": "toggleOpt1",
            "click #btn_save": "doSave",
            "click #btn_cancel": "doCancel",
			"change #hanbai_tnk":	"changeHanbaiTnk",
			"click #item-picker-cmt-id .price_list": "showPriceList",
            // "click #btn_print": "doPrint",
            // "click #btn_delete": "doDelete",
        },

        render: function() {
            // cf. juchu.bechu.list.js
            // this.stickit();

            this.scrollAdj();

            // this.renderCheck();
            
            this.renderBtnEtc();

            return this;
        },

        // // ボタン表示等調整
        // renderBtnEtc: function() {
        //     if ( this.isKakutei() ) {
        //         this.btnDisabled('#btn_save', '見積確定済みです');
        //     } else {
        //         this.btnEnabled('#btn_save');
        //     }
        // },

        // ボタン非活性化
        btnDisabled: function(elem, msg) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled").addClass("disable_btn");
            if ( msg ) {
                $(elem).attr("title", msg);
            }
        },

        // ボタン活性化
        btnEnabled: function(elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled").attr("title", '').removeClass('disable_btn');;
        },

        // 見積り確定済みか
        isKakutei: function() {
            var is_kakutei = app.model.get( 'is_kakutei' );
            // console.log( 'is_kakutei =>' + is_kakutei );
            return !!is_kakutei;
        },

        // 更新可否
        isUpdCtxt: function() {
            if ( this.isKakutei() ) {
                return false;
            }
            return true;
        },

        // 更新可否
        isUpdCtxtWithMsg: function(e) {
            if ( this.isKakutei() ) {
                msiLib2.showInfo( msg_kakutei );
                 e.stopImmediatePropagation();
                return false;
            }
            return true;
        },
        
        // 個別更新可否
        isProhibitItemUpd: function(e) {
            var $el = $(e.currentTarget).closest('li');
            // console.log( 'isProhibitItemUpd =>' + $el.data('order_flg') + ' ' + $el.data('article') );
            if ( $el.data('order_flg') == "1" ) {
                msiLib2.showWarn( 'この商品は「発注済み」のため変更できません' );
                e.stopImmediatePropagation();
                return true;
            }
            if ( $el.data('fixed_by_plan') == "1" ) {
                msiLib2.showWarn( 'この商品は「変更不可プラン商品」です' );
                e.stopImmediatePropagation();
                return true;
            }
            return false;
		},

        // 個別更新可否(択一選択)
        isProhibitItemUpdItemOne: function(e) {
            if ( $('#goods .list-item-one ul').find('li[data-order_flg="1"]').length ) {
                msiLib2.showWarn( '「発注済み」の択一商品があるため変更できません' );
                e.stopImmediatePropagation();
                return true;
            }
            if ( $('#goods .list-item-one ul').find('li[data-fixed_by_plan="1"]').length ) {
                msiLib2.showWarn( '「変更不可プラン商品」の択一商品があるため変更できません' );
                e.stopImmediatePropagation();
                return true;
            }
            return false;
		},

        // 確定可否による描画等
        renderBtnEtc: function() {
            var msg = '';
            if ( this.isKakutei() ) {
                msg = '見積確定済';
                this.btnDisabled('#btn_save', msg_kakutei);
                this.btnDisabled('#btn_cancel', '');
            } else {
                msg = '';
                this.btnEnabled('#btn_save');
                this.btnEnabled('#btn_cancel');
            }
            if ( this.isChanged() ) {
                msg = [msg, 'データ変更あり'].join(' ');
            } else {
                if ( msg == '' ) {
                    this.btnDisabled('#btn_save', '変更がありません');
                    this.btnDisabled('#btn_cancel', '変更がありません');
                }
            }
            this.$('#kakutei_info').text( msg );

            if ( ! this.isUpdCtxt() ) {
                this.$el.find(':input:not(.my-no-readonly)').attr('disabled', 'disabled').end()
                    .find('.my-readonly-hidden').hide();
            } else {
                $('#goods .list-item-plmi ul li, #goods .list-item-multi ul li, #goods .list-item-food ul li')
                    .each(function(i, obj) {
                        var $el = $(obj);
                        if ( $el.data('order_flg') == "1" ) {
                            // console.log( 'order_flg=>' + $el.data('item') + ' ' + $el.data('article') );
                            $el.find(':input').attr('disabled', 'disabled');
                            $el.on('click', 'input, span', function(e) {
                                msiLib2.showWarn( 'この商品は「発注済み」のため変更できません' );
                                e.stopImmediatePropagation();
                                return false;
                            } );
                        }
                        if ( $el.data('fixed_by_plan') == "1" ) {
                            // console.log( 'order_flg=>' + $el.data('item') + ' ' + $el.data('article') );
                            $el.find(':input').attr('disabled', 'disabled');
                            $el.on('click', 'input, span', function(e) {
                                msiLib2.showWarn( 'この商品は「変更不可プラン商品」です' );
                                e.stopImmediatePropagation();
                                return false;
                            } );
                        }
                    });
            }
        },

        // スクロールバー表示調整
        scrollAdj: function() {
            // lvl2 タブ表示で選択がない場合は表示しない
            var $lvl2Tabl = this.$('#lvl2_tab_container .lvl2_tab li');
            if ( $lvl2Tabl && $lvl2Tabl.length <= 1 ) {
                this.$('#lvl2_tab_container').css({'visibility': 'hidden', 'height': '1px'});
            }

            var $cont =  this.$('#item-picker-id'),
                cont_h = $('#detail').height(),
                tab_h = $('#lvl1_tab_container').height(),
                tab2_h = $('#lvl2_tab_container').height(),
                top_h = $('#item-picker-cond2').height(),
                cmt_h = $('#item-picker-cmt-id').height(),
                footer_h = 0, // $('#item-picker-footer-cont-id').height(),
                opt_h = 0,
                adj_h = 85, // 110, // button, etc.
                my_h;
            // console.log( 'lvl2_tab_container=>' + tab2_h );
            if ( top_h <= 0 && $('#sub-button-area').height() > 0 ) {
                opt_h = $('#sub-button-area').height() + 5;
            }
            // if ( $('#item-picker-footer-cont-id').is(':visible') ) {
            if ( $('#item-picker-footer-cont-id') ) {
                footer_h = 10;
            }
            my_h = cont_h - adj_h - top_h - opt_h - cmt_h - footer_h - tab_h - tab2_h;
            // console.log( 'height=>' + [cont_h,top_h,adj_h].join(', ') + ' my_h=>' + my_h );
            $cont.height( my_h );

            this.scrollAdjList();
        },

        // リスト表示用スクロールバー表示調整
        scrollAdjList: function() {
            var $list = this.$('#estimate.meisai'),
                $header = this.$('.estimate-head'),
                $footer = this.$('.estimate-foot'),
                sc_of,
                hh;
            if ( $list.length <= 0 ) return;

            var cont_h = $('#detail').height(),
                top_h = $('#item-picker-cond2').height(),
                adj_h = 170, // button, etc.
                my_h;
            my_h = cont_h - adj_h - top_h;
            // console.log( 'height=>' + [cont_h,top_h,adj_h].join(', ') + ' my_h=>' + my_h );
            $list.height( my_h );

            // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
            if ( Math.abs($list[0].scrollHeight - $list[0].clientHeight) <= 1 ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                $footer.css("overflow-y", sc_of);
                // $('#note_txt_x').css('width', sc_w);
            } else {
                sc_of = 'scroll';
                hh = $header.height();
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                $footer.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome. XXX
                //$('#note_txt_x').css('width', sc_w);
            }
        },

        // 変更データをクリア
        resetShohinData: function() {
            orgShohinData = {};
            newShohinData = {};
            orgEtcData = { allergy:{} };
            newEtcData = { allergy:{} };
            $('#allergy').data('org_allergy_memo', $('#allergy_memo').val());
        },

        _getLvl0Ctxt: function() { // experimantal
            var ctxt = 'unknown';
            switch ( curLvl0 ) {
            case  '9': ctxt = 'henrei'; break;
            case '10': ctxt = 'ryori'; break;
            case '11': ctxt = 'shohin'; break;
            }
            return ctxt;
        },

        _isPickerTypeSwiper: function() {
            return !!myPickerType.match(/^swiper/);
        },

        _mySwiperKind: function() {
            var m = myPickerType.match(/^swiper-(.+)$/);
            if ( m && m[1] ) {
                return m[1];
            }
            return null;
        },

        // 商品選択画面要素表示
        showItemPicker: function() {
            if ( this._isPickerTypeSwiper() ) {
                this._showItemPickerSwiper();
            } else {
                this._showItemPickerClassic();
            }
        },

        _swiperClear: function() {
            $('#itemex-picker-dlg .content').empty();
            $('#itemex-picker-dlg .footer-picker').empty();
            myImageLoaded = false;
            if ( mySwiper ) {
                mySwiper.destroy();
                mySwiper = null;
            }
            $('#itemex-picker-dlg').width( '90%' ); // patch.  reset #itemex-picker-dlg's orig width
        },

        _showSwiperDialog: function(clickedIndex) {
            var that = this;

            // $('#itemex-shade, #itemex-picker-dlg').show();
            $('#itemex-shade, #itemex-picker-dlg').fadeIn(200);

            if ( mySwiper ) {
                mySwiper.destroy();
                mySwiper = null;
            }

            var maxWidth = 0;
            $('#itemex-picker-dlg .content img').map( function(ele) { var w = $(this).width();
                                                                      if ( w > maxWidth ) maxWidth = w; } );
            // var curWidth = $('#itemex-picker-dlg .content img').eq(clickedIndex).width();
            // console.log( ' maxWidth=>' + maxWidth + ' curWidth=>' + curWidth );
            $('#itemex-picker-dlg').width( maxWidth );

            mySwiper = new Swiper('#itemex-picker-dlg .swiper-container', {
                initialSlide: clickedIndex,
                pagination: '.swiper-pagination',
                slidesPerView: 1,
                paginationClickable: true,
                spaceBetween: 0,
                keyboardControl: true,
                nextButton: '.swiper-button-next',
                prevButton: '.swiper-button-prev',
                loop: true,
                effect: that._mySwiperKind(), // "slide", "fade", "cube" or "coverflow"  fade:buggy?
                paginationBulletRender: function (index, className) {
                    var styleHtml = '';
                    if ( $('a.my-popup-img').length <= 30 ) {
                        styleHtml = ' style="margin-left:5px"';
                    }
                    return '<span' + styleHtml + ' class="' + className + '">' + (index + 1) + '</span>';
                },
                // calculateHeight: true,
                // centeredSlides: true,
                // grabCursor: true,
                onDestroy: function(swiper00) {
                    // console.log('onDestroy called.');
                },
                onSlideChangeEnd: function(swiper00) {
                    var myIndex = swiper00.activeIndex -1,
                        maxIndex = $('a.my-popup-img').length -1,
                        $elTgt,
                        $elTgt2,
                        cap;
                    if ( myIndex > maxIndex ) myIndex = 0; // patch
                    if ( myIndex < 0 ) myIndex = maxIndex; // patch
                    // console.log( 'onSlideChangeEnd ### maxIndex=>' + maxIndex );
                    if ( !$.msiJqlib.isNullEx(myIndex) ) {
                        $elTgt = $('a.my-popup-img').eq(myIndex);
                        // NG $elTgt2 = $('.swiper-container .swiper-slide img').eq(myIndex);
                        // cap = $elTgt2.attr('data-cap');
                        cap = $elTgt.attr('title');
                        // cap =  $elTgt.find('img').attr('alt');
                        if ( $elTgt ) {
                            $elTgt.focus();
                        }
                        $('#itemex-picker-dlg-caption').text( cap );
                    }
                    // console.log( 'onSlideChangeEnd ### myIndex=>' + myIndex );

                    $('#itemex-picker-dlg .footer-picker').prepend( $('.swiper-container .swiper-pagination') );
                },
                NG_onClick: function(swiper00, ev) {
                    // console.log('onSlideClick called.');
                    swiper00.slideNext();
                },
                XXXonSlideClick : function(swiper00) {
                    //Do something when you touch the slide
                    var i = mySwiper.clickedSlideIndex;
                    var a = mySwiper.activeSlide();
                    // console.log('clickedSlideIndex=>' + i);
                    // console.log('activeSlide=>' + a);
                },
            });
        },

        // 商品選択画面要素表示
        _showItemPickerSwiper: function() {
            var that = this;
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataCol = { org: orgShohinData, new: newShohinData, etcOrg: orgEtcData, etcNew: newEtcData };
            var dataColJson = JSON.stringify( dataCol );

            this._swiperClear();
            
            $.ajax({ // url: location.href,
                data: {
                    dataApp: dataAppJson,
                    dataCol: dataColJson,
                    action: 'genitempicker',
                    catLvl1: this.curLvl1(),
                    catLvl2: this.curLvl2(),
                },
                type: 'POST',
                success: function( html ) {
                    $('#item-disp-area').html(html);
                    that.scrollAdj();
                    msiLib2.msiPrepareParts('#item-disp-area');

                    $('#itemex-picker-dlg .content').prepend( $('#item-disp-area .swiper-container') );

                    $('#itemex-picker-dlg .content').removeClass('my-picker-swiper-slide my-picker-swiper-cube');
                    $('#itemex-picker-dlg .content').addClass( 'my-picker-swiper-' + that._mySwiperKind() );

                    // if ( that._getLvl0Ctxt() == 'ryori' ) {
                    //    $('#itemex-picker-dlg .content').addClass( 'ryori' );
                    // }

                    var imgCnt = 0;
                    // $('.swiper-container', document).imagesLoaded()
                    $('.swiper-container', $('#itemex-picker-dlg')).imagesLoaded()
                        .progress( function(instance, image) {
                            imgCnt++;
                            var pct = Math.floor(100*imgCnt/instance.images.length),
                                result = image.isLoaded ? 'loaded' : 'broken',
                                i_w = image.img.width,
                                i_h = image.img.height,
                                base_w = $(window).width(),
                                base_h = $(window).height(),
                                ratio_w = i_w / base_w,
                                ratio_h = i_h / base_h;
                            /* console.log( '%=>' + pct + ' len=>' + instance.images.length + ' cnt=>' + imgCnt
                                         + ' image is ' + result + ' for ' + image.img.src
                                         + ' width=>' + i_w + ' height=>' + i_h
                                         + ' base_w=>' + base_w + ' base_h=>' + base_h 
                                         + ' ratio_w=>' + ratio_w + ' ratio_h=>' + ratio_h );  */
                            if ( image.img.width < 400 ) {
                                $(image.img).addClass( 'my-img-no-trans' ); // dummy image
                            } else if ( ratio_h > ratio_w ) {
                                $(image.img).addClass( 'my-img-height100' ); // adjust by height
                            } else {
                                $(image.img).addClass( 'my-img-width100' ); // adjust by width
                            }
                        } )
                        .done( function(instance) {
                            myImageLoaded = true;
                            // console.log( 'imagesLoaded done' );

                            if ( myImagePreClicked ) {
                                var _myImagePreClicked = myImagePreClicked;
                                myImagePreClicked = null;
                                setTimeout( function() {
                                    that._showSwiperDialog( _myImagePreClicked );
                                    // var $ele = $('a.my-popup-img').eq(_myImagePreClicked);
                                    // $ele.click();
                                }, 0 );
                            }

                            $('#itemex-picker-dlg .swiper-container').show();
                        } );

                    if ( scrollTopLast &&
                         !( $('#isItemListDispByQuantityOrder') && $('#isItemListDispByQuantityOrder').val()=='1' ) ) {
                        // 数量順に表示しているときはスクロール位置を設定しない
                        $('#goods').scrollTop( scrollTopLast );
                        scrollTopLast = null;
                    }

                    $('#view_style_opt_list').empty().append('<a href="javascript:void(0)">リスト表示</a>');
                    $('#view_style_opt_block').empty().append('<span>ブロック表示</span>');

                    $('a.my-popup-img').on('click', function(ev) {
                        ev.preventDefault();
                        ev.stopImmediatePropagation();
                        var $ele = $(ev.currentTarget),
                            clickedIndex = $('a.my-popup-img').index($ele);

                        if ( ! myImageLoaded ) {
                            myImagePreClicked = clickedIndex;
                            return; // 画像ファイル読み込みが終わっていない
                        }

                        that._showSwiperDialog( clickedIndex );
                    } );

                    var _lvl2 = $('#item-cond2-id').select2('val');
                    if ( _.isNumber(_lvl2) || _.isString(_lvl2) ) {
                        curLvl2 = _lvl2;
                    }

                    if ( tabLvl2Top !== null ) {
                        var $ele = $('#detail .lvl2_tab');
                        $ele.css({marginTop: tabLvl2Top});
                    }

                    app.render();
                },
            });
        },

        // タブをまたがった、拡張版択一選択の更新処理
        // サーバでデータの調整をしてもらう
        updSelOneEx: function( addInfo ) {
            this._showItemPickerClassic( addInfo );
        },

        // 商品選択画面要素表示
        _showItemPickerClassic: function( addInfo ) {
            var that = this;
            var dataApp = this.model.toJSON();
            var dataAppJson = JSON.stringify( dataApp );
            var dataCol = { org: orgShohinData, new: newShohinData, etcOrg: orgEtcData, etcNew: newEtcData };
            var dataColJson = JSON.stringify( dataCol );

            var data = {
                dataApp: dataAppJson,
                dataCol: dataColJson,
                action: 'genitempicker',
                catLvl1: this.curLvl1(),
                catLvl2: this.curLvl2(),
            };
            if ( addInfo ) {
                data['dataEx'] = JSON.stringify( addInfo );
            }

            $.ajax({ // url: location.href,
                data: data,
                type: 'POST',
                success: function( html ) {
                    $('#item-disp-area').html(html);

                    // 拡張版択一選択の調整
                    if ( $('#my-data-ex-picker-id').length ) {
                        var rtnNewShoinData = msiLib2.getJsonFromHtml( $('#my-data-ex-picker-id') );
                        // console.log( 'rtnNewShoinData==>' + JSON.stringify(rtnNewShoinData) );
                        // cf. _fSaveChanged_sub
                        _.each( rtnNewShoinData, function(rec, shohinCd) {
                            // console.log( 'shohinCd==>' + shohinCd );
                            // console.log( 'rec=>' + JSON.stringify(rec) );
                            // console.log( 'orgShohinData=>' + JSON.stringify(orgShohinData[shohinCd]) );
                            newShohinData[shohinCd] = rec;
                            isForceChanged = true;
                            if ( _.has(orgShohinData, shohinCd) && $.msiJqlib.isEqual(orgShohinData[shohinCd], rec[shohinCd]) ) {
                                delete orgShohinData[shohinCd];
                                delete newShohinData[shohinCd];
                                return;
                            }
                        } );
                    }

                    that.scrollAdj();
                    msiLib2.msiPrepareParts('#item-disp-area');

                    if ( scrollTopLast &&
                         !( $('#isItemListDispByQuantityOrder') && $('#isItemListDispByQuantityOrder').val()=='1' ) ) {
                        // 数量順に表示しているときはスクロール位置を設定しない
                        $('#goods').scrollTop( scrollTopLast );
                        scrollTopLast = null;
                    }

                    $('#view_style_opt_list').empty().append('<a href="javascript:void(0)">リスト表示</a>');
                    $('#view_style_opt_block').empty().append('<span>ブロック表示</span>');

                    if ( window.screen.width <= 1024 ) { // iPad, ...
                        $('.my-popup-img').colorbox( {photo:true, opacity:0.7, top:'40px',
                                                      // height:"90%", width:"100%", maxHeight:"90%", maxWidth:"100%", // iPad
                                                      maxHeight:"90%", maxWidth:"100%", // iPad
                                                      current:'{current}/{total}'} );
                    } else {
                        $('.my-popup-img').colorbox( {photo:true, opacity:0.7, top:'40px',
                                                      // height:"90%", width:"90%", maxHeight:"90%", maxWidth:"100%", // normal
                                                      maxHeight:"90%", maxWidth:"100%", // innerWidth:"60%",
                                                      current:'{current}/{total}'} );
                    }
                    /* include jquery.event.mode.js, jquery.event.swipe.js if using the following swipeleft/swiperight.
                    $('#cboxContent').on( 'swipeleft',function(e){
                        $.colorbox.next();
                    }).on( 'swiperight',function(e){
                        $.colorbox.prev();
                    });
                     */

                    var _lvl2 = $('#item-cond2-id').select2('val');
                    if ( _.isNumber(_lvl2) || _.isString(_lvl2) ) {
                        curLvl2 = _lvl2;
                    }

                    if ( tabLvl2Top !== null ) {
                        var $ele = $('#detail .lvl2_tab');
                        $ele.css({marginTop: tabLvl2Top});
                    }

                    app.render();
                },
            });
        },

        // 選択商品リスト表示
        showItemList: function() {
            var that = this;
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataCol = { org: orgShohinData, new: newShohinData, etcOrg: orgEtcData, etcNew: newEtcData };
            var dataColJson = JSON.stringify( dataCol );


            $.ajax({ // url: location.href,
                data: {
                    dataApp: dataAppJson,
                    dataCol: dataColJson,
                    action: 'genitemlist',
                    catLvl1: this.curLvl1(),
                    catLvl2: this.curLvl2(),
                },
                type: 'POST',
                success: function( html ) {
                    $('#item-disp-area').html(html);
                    that.scrollAdj();
                    msiLib2.msiPrepareParts('#item-disp-area');

                    $('#view_style_opt_list').empty().append('<span>リスト表示</span>');
                    $('#view_style_opt_block').empty().append('<a href="javascript:void(0)">ブロック表示</a>');

                    app.render();
                },
            });
        },

        // 選択中のレベル１カテゴリを返す
        curLvl1: function() {
            return curLvl1;
            /*
            var $span = this.$('.lvl1_tab').find('span'),
                t = $span.text(),
                val = $span.attr('data-val');
            console.log( '選択中のレベル１カテゴリを返す=>' + t + ' val=>' + val );
            return val;
             */
        },

        // 選択中のレベル２カテゴリを返す
        curLvl2: function() {
            return curLvl2;
            /*
            var $ele = $('#item-cond2-id'),
                val = $ele.val(),
                t = $('[name=' + $ele.attr('name') + '] option:selected').text();
            console.log( '選択中のレベル２カテゴリを返す=>' + t + ' val=>' + val);
            return val;
             */
        },

        // 選択中の表示タイプ(block|list)を返す
        curShowType: function() {
            var $span = this.$('.view_style_opt').find('span'),
                t = $span.text();
            if ( /リスト/.test(t) ) {
                return 'list';
            } else {
                return 'block';
            }
        },

        // 前回値保持用 cookie 設定
        setCookieForPref: function(pKey, pVal) {
            // Cookie    lvl0.lvl1:lvl2[,lvl0.lvl1:lvl2]*
            var ck_itemex,
                a_itemex,
                str_itemex,
                is_set=false,
                lvlKey = '' + pKey;
            ck_itemex = msiLib2.uiPrefCookie('fdn_itemhj');
            if ( !ck_itemex ) ck_itemex = '';
            a_itemex = ck_itemex.split(',');
            a_itemex = _.reduce( a_itemex, function(d, val) {
                if ( ! val ) return d;
                val = '' + val;
                var i = val.split(':', 2), k = ''+i[0], v = ''+i[1];
                if ( v.length <= 0 ) return d;
                if ( k === lvlKey ) {
                    is_set = true;
                    val = lvlKey + ':' + pVal;
                }
                d.push(val);
                return d;
            }, [] );
            if ( ! is_set ) a_itemex.push(lvlKey + ':' + pVal);
            str_itemex = a_itemex.join(',');
            msiLib2.uiPrefCookie('fdn_itemhj', str_itemex);

        },

        // 絞込条件 変更
        changeCond2: function(e) {
            var $ele = $(e.currentTarget),
                val = $ele.val(),
                t = $('[name=' + $ele.attr('name') + '] option:selected').text();
            
            // console.log( 'changeCond2 called=>' + val + ' text=>' + t + ' name=>' + $ele.attr('name') );

            // XXX
            // 変更ある場合、確認して遷移する
            /*
            if ( this.isChanged() ) {
                if ( ! confirm('データが変更されています. この変更はクリアされますがよろしいですか？') ) {
                    e.stopImmediatePropagation();
                    if ( curLvl2 !== null ) {
                        $('#item-cond2-id').select2('val', curLvl2);
                    }
                    return false;
                }
                // 変更データをクリア
                app.resetShohinData();
            }
             */

            curLvl2 = val;

            this.setCookieForPref( curLvl0 + '.' + curLvl1, curLvl2 );

            this.showItemPicker();
        },

		// 販売単価を変更した時 2015/04/21 ADD Kayo
		changeHanbaiTnk: function(e) {
			var $target = $(e.currentTarget).parent().parent();
            this.calZeikomi($target);                               // 税込単価計算
		},
		// 端数処理関数 端数区分  0:切捨て 1:四捨五入 2:切上げ
		_getZeiHasuFunc: function (zei_hasu_kbn) {
			var hasu = 0
			hasu = zei_hasu_kbn;
			if (hasu == 1)
				return Math.round;
			else if (hasu == 2)
				return Math.ceil;
			else
				return Math.floor;
		},
        // 税込単価計算
		calZeikomi: function ($target) {
			var uri_zei_kbn     = $target.data('uri_zei_kbn');      // 売上課税区分
			var zei_rtu         = $target.data('zei_rtu');          // 消費税率
			var zei_hasu_kbn	= $target.data('zei_hasu_kbn');		// 消費税端数区分
			var hanbai_tnk      = utils.commaOmit( $target.find("#hanbai_tnk").val() );
			var hasuFunc        = this._getZeiHasuFunc(zei_hasu_kbn);

			//課税区分が外税の場合のみ
			if (uri_zei_kbn == 2) {
				var zeikomiTnk = 0;
				zeikomiTnk = parseInt(hanbai_tnk) + parseInt(hasuFunc(hanbai_tnk * zei_rtu / 100));
                // 数値区切りカンマ(,)を付ける
                var zeikomiTnkComma =  $.msiJqlib.commaAdd(zeikomiTnk);
                $target.find(".n2").text(zeikomiTnkComma);
			}
		},

        // タブ切り替え処理
        changeTab: function(e) {
            // var clickedIndex = this.$('.lvl1_tab li').index($(e.currentTarget).closest("li"));
            // console.log( 'changeTab called=>' + $(e.currentTarget).text() + ' index=>' + clickedIndex );

            var $ele = $(e.currentTarget).closest("li");
            if ( !$ele ) return;
            var clickedIndex = this.$('.lvl1_tab li').index($ele);
            var _lvl1 = $ele.attr('data-val');

            // XXX
            // 変更ある場合、確認して遷移する
            /*
            if ( this.isChanged() ) {
                if ( ! confirm('データが変更されています. この変更はクリアされますがよろしいですか？') ) {
                    e.stopImmediatePropagation();
                    return false;
                }
                // 変更データをクリア
                app.resetShohinData();
            }
             */

            $.msiJqlib.changeTab(this.$('.tab-contents-dummy'), this.$('.lvl1_tab li'), $(e.currentTarget));
//            this.setButtonsStatus();

            curLvl1 = _lvl1; // clickedIndex+1;
            curLvl2 = null;
            tabLvl2Top = null;

            this.setCookieForPref( curLvl0, curLvl1 );

            if ( this.curShowType() === 'list' ) {
                // this.showItemList();
                this.showItemPicker(); // list ではなく picker 画面を表示 
            } else {
                this.showItemPicker();
            }

            // XXX
            // var url = location.href;
            // url = url.replace(/\/_1\/\d+\//, '/_1/'+(clickedIndex+1)+'/');
            // location.href = url;
        },

        // タブ レベル２ 切り替え処理
        changeTabLvl2: function(e) {
            // var clickedIndex = this.$('.lvl2_tab li').index($(e.currentTarget).closest("li"));
            // console.log( 'changeTabLvl2 called=>' + $(e.currentTarget).text() + ' index=>' + clickedIndex );

            var $ele = $(e.currentTarget).closest("li");
            if ( !$ele ) return;
            var clickedIndex = this.$('.lvl2_tab li').index($ele);
            var _lvl2 = $ele.attr('data-val');

            curLvl2 = _lvl2;

            // console.log( 'changeTabLvl2 called=>' + $(e.currentTarget).text() +' index=>' + clickedIndex +' curLvl2=>' + curLvl2 );

            this.setCookieForPref( curLvl0 + '.' + curLvl1, curLvl2 );

            this.showItemPicker();
        },

        // block|list 表示切り替え処理
        changeTab2: function(e) {
            var clickedIndex = this.$('.view_style_opt li').index($(e.currentTarget).closest("li"));
            // console.log( 'changeTab2 called=>' + $(e.currentTarget).text() + ' index=>' + clickedIndex );

            // XXX lvl2 検索などもあるので、いずれにせよ、newShohinData を読み込んだデータに展開する必要がある.
            // list 表示では、変更ある場合、確認して遷移する
            /*
            if ( clickedIndex == 0 ) {
                if ( this.isChanged() ) {
                    if ( ! confirm('データが変更されています. この変更はクリアされますがよろしいですか？') ) {
                        e.stopImmediatePropagation();
                        return false;
                    }
                    // 変更データをクリア
                    app.resetShohinData();
                }
            }
             */

            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.view_style_opt li'), $(e.currentTarget));

            this.setCookieForPref( curLvl0 + '.' + 'bol', clickedIndex==0 ? 'l' : 'b' ); // list|block

            if ( clickedIndex == 0 ) {
                this.showItemList();
            } else if ( clickedIndex == 1 ) {
                this.showItemPicker();
            }

            // XXX
            // var url = location.href;
            // var _s = clickedIndex==0 ? 'l' : 'b';
            // url = url.replace(/\/_s\/(b|l)\//, '/_s/'+_s+'/');
            // location.href = url;
        },

        // 追加情報表示 toggle
        toggleOpt1: function(e) {
            var $ele = $(e.currentTarget),
                st = $ele.attr('data-state'),
                val = $ele.val();

            // console.log( 'toggleOpt1 called=>' + val + ' state=>' + st );

            this.showOpt1Tab( st != 'on' );
        },

        // 追加情報表示
        showOpt1Tab: function(bShow) {
            var $ele = $('#btn_opt_1'), val;
            if ( bShow ) {
                $('#item-picker-lvl2-opt-id').show();
                $('#item-picker-id').hide();
                $ele.attr('data-state', 'on');
                val = $ele.attr('data-cap-0');
                $ele.val( val );
            } else {
                $('#item-picker-lvl2-opt-id').hide();
                $('#item-picker-id').show();
                $ele.attr('data-state', 'off');
                val = $ele.attr('data-cap-1');
                $ele.val( val );
            }
        },

        // 初期状態から変更されているか
        isChanged: function() {
            if ( _fIsChanged && _fIsChanged() ) {
                return true;
            }
            return false;

            /*
            if ( !orgDataApp || !orgDataCol ||
                 ( $.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) &&
                   $.msiJqlib.isEqual(orgDataCol, this.collection.toJSON()) ) ) {
                return false;
            }
            return true;
             */
        },

        clearErr: function() {
            this.$el.msiErrClearAll();
        },

        isInputOk: function() {
            var aMsg = [];
            // モデルチェック
            var result = null; //    appthanks.oreiModel.validate();
            if (result) {
                _.each(result, function(v, k) {
                    aMsg.push(v);
                });
            }

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#thanks-std-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#thanks-org-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#thanks-env-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            // OK 
            $.msiJqlib.clearAlert();
            return true;
        },

        doSave: function(e, options) {
            if ( ! this.isUpdCtxt() ) {
                return;
            }

            $.msiJqlib.clearAlert();
            if (!this.isInputOk()) {
                return;
            }

            if ( !this.isChanged() ) { // 更新で変更なし
                if ( ! confirm('データが変更されていませんが、更新しますか？') ) {
                    return;
                }
            }

            this.clearErr();

            var that = this;
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataCol = { org: orgShohinData, new: newShohinData, etcOrg: orgEtcData, etcNew: newEtcData };
            var dataColJson = JSON.stringify( dataCol );

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/itemhjsave',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '保存',
                },
                type: 'POST',
                success: function(mydata) {
                    // console.log( 'ajax return' + JSON.stringify(mydata));
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});

                        $.msiJqlib.showInfo(mydata.msg);

                        // 変更データをクリア
                        app.resetShohinData();
                        scrollTopLast = $('#goods').scrollTop();

                        if ( app.curShowType() === 'list' ) {
                            app.showItemList();
                            // app.showItemPicker(); // list ではなく picker 画面を表示 
                        } else {
                            app.showItemPicker();
                        }
                        isForceChanged = false;
                        return;
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        }, // doSave

        // 取消処理（再ロードして初期化）
        doCancel: function() {
            if ( !this.isChanged() ) return;
            if ( this.isChanged() && !confirm('初期状態に戻してよろしいですか？') ) {
                return;
            }
            isNoChangeCheck = true;
            window.location.reload();
        },

        // 価格表表示
        showPriceList: function(e) {
            var $ele = $(e.currentTarget),
                ryoukin_kbn = $ele.attr('data-ryoukin_kbn');
            e.stopImmediatePropagation();
            // $.msiJqlib.showInfo('料金区分=>' + ryoukin_kbn);
            // msiLib2.setPickHelper( 'pricelist', '#dummy' );

            var opts = { mydata:{} };
            opts.mydata['init_search'] = 1;
            opts.mydata['limit'] = 100;
            opts.mydata['no_cond'] = 1;
            opts.mydata['no_footer'] = 1;
            opts.mydata['s_ryoukin_kbn'] = ryoukin_kbn;
            msiLib2.setPickHelperX( '/mref/ryoukindlg', '#dummy', opts );
        },

    }); // AppView



    var app;
    var orgDataApp, orgDataCol;
    var _resetData;
    var _setInitData;
    // var _zeiMst = [];

    var meisaiList = new MeisaiCollection;

    app = new AppView( { model: new AppModel, collection: meisaiList } );

    _resetData = function( myApp, myCol ) {
        // console.log( '_resetData myApp=>' + JSON.stringify(myApp) );
        app.model.set( myApp );
        app.collection.reset( myCol );
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        orgDataCol = app.collection.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();

        curLvl0 = myApp.lvl0;
        curLvl1 = myApp.lvl1;
        curLvl2 = myApp.lvl2;
        sekoNo  = myApp.seko_no;

        if ( app.curShowType() == 'list' ) {
            this.showItemList();
        } else {
            app.showItemPicker();
        }

        app.render();

        app.model.trigger('change'); // patch
    };

    _setInitData = function() {
        var mydata = msiLib2.getJsonFromHtml( $('#my-data-init-id') );
        // console.log( '_setInitData mydata =>' + JSON.stringify(mydata) );
        //console.log( '_setInitData mydata ### dataApp =>' + JSON.stringify(mydata.dataApp) );
//        console.log( '_setInitData mydata ### dataCol =>' + JSON.stringify(mydata.dataCol) );
//        console.log( '_setInitData mydata ### dataMstr =>' + JSON.stringify(mydata.dataMstr) );

        // _zeiMst = mydata.mst.zei;
        // console.log( 'mydata.mst.zei=>' + JSON.stringify(_zeiMst) );

        // サイドメニュー
        // $.msiSideMenuLib.setSideMenu({showFooter: true});

        _resetData( mydata.dataApp, mydata.dataCol );
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function() {
        if ( !isNoChangeCheck && app.isChanged() ) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on( 'resize', function() { app.render(); } );

    // 参照専用
    if ( $('#my-form-id').hasClass('my-ctxt-readonly') ) {
        var $form = $('#my-form-id');
        $form.msiInputReadonly()
            .msiCalReadonly()
            .find('.my-readonly-hidden').hide();

        $form.find('.my-readonly-visible').show();

        $('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_cancel').hide();
        // .attr("disabled", "disabled");

        utils.setReadOnlyCtxt();
    }

    $.msiJqlib.initDone( function() { _setInitData(); } ); // 処理完了

    // $('#order').fadeIn('fast'); // ちらつきのごまかし

    // APP初期化処理
    // appitemex.appModel = new AppModel();
    // appitemex.appView = new AppView({model: appitemex.appModel});

    //
    // orginal: main.js  -- begin
    //
	/* 商品をプラス */
	$('#detail').on('click', '#goods .list-item-plmi ul li .plus', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // if ( app.isProhibitItemUpd(e) ) return;
        // console.log( '#goods 111 .list-item-plmi plus. ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) );

        var myOld;
		var num = $(this).parent('span').children('.num');
		var n = parseInt(num.text());
        myOld =n;
		var sub_id = $(this).parent().parent().parent().attr('data-count');
		var badge = $('#'+ sub_id).children('.badge');
		var bn = 0;
		n++;
		num.text(n);
		if(badge.text()!=""){
			bn = parseInt(badge.text());
		}
		bn++;
		badge.text(bn).addClass('on');

        _fSaveChanged( $(this), { quantity: n } );
        // console.log( '** +/- plus: ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + n );
	});

	/* 商品をマイナス */
	$('#detail').on('click', '#goods .list-item-plmi ul li .minus', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // if ( app.isProhibitItemUpd(e) ) return;
        // console.log( '#goods 111 .list-item-plmi minus. ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) );

        var myOld;
		var num = $(this).parent('span').children('.num');
		var n = parseInt(num.text());
        myOld =n;
		var sub_id = $(this).parent().parent().parent().attr('data-count');
		var badge = $('#'+ sub_id).children('.badge');
        var bn;
		if(n > 0){
			n--;
			num.text(n);
			bn = parseInt(badge.text());
			bn--;
			badge.text(bn);
			if(bn < 1){
				badge.removeClass('on');
			}
		}

        _fSaveChanged( $(this), { quantity: n } );
        // console.log( '** +/- minus: ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + n );
	});

	/* 返礼品・料理カウント プラス */
	$('#detail').on('click', '#goods .list-item-multi ul li .plus, #goods .list-item-food ul li .plus', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // if ( app.isProhibitItemUpd(e) ) return;
        // console.log( '#goods 222 .list-item-multi plus ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) );

        var myOld;
		var p = $(this).parent();
		var pp = p.parent();
		var d = parseInt(p.attr('data-digit'));
		var q = pp.children('.quantity');
		var qt = parseInt(q.val());
        myOld = qt;
		qt += d;
		q.val(qt);
		var n = pp.children().children('.num');
		var l = n.length;
		var t = ('00000' + qt).slice(-1*l);
        var i;
		for(i=0;i<l;i++){
			n.eq(i).text(t.substr(i,1));
		}
		pp.addClass('on');

        _fSaveChanged( $(this), { quantity: qt } );
        // console.log( '** counter plus: ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + qt );
	});

	/* 返礼品・料理カウント マイナス */
	$('#detail').on('click', '#goods .list-item-multi ul li .minus, #goods .list-item-food ul li .minus', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // if ( app.isProhibitItemUpd(e) ) return;
        // console.log( '#goods 222 .list-item-multi minus ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) );

        var myOld;
		var p = $(this).parent();
		var pp = p.parent();
		var d = parseInt(p.attr('data-digit'));
		var q = pp.children('.quantity');
		var qt = parseInt(q.val());
        myOld = qt;
		if(qt >= d){
			qt -= d;
			q.val(qt);
		}
		if(qt > 0){
			pp.addClass('on');
		}else{
			pp.removeClass('on');
		}
		var n = pp.children().children('.num');
		var l = n.length;
		var t = ('00000' + qt).slice(-1*l);
        var i;
		for(i=0;i<l;i++){
			n.eq(i).text(t.substr(i,1));
		}

        _fSaveChanged( $(this), { quantity: qt } );
        // console.log( '** counter minus: ' + $(this).text()  + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + qt );
	});

	/* 択一商品 */
	$('#detail').on('click', '#goods .list-item-one ul li .select', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // console.log( '#goods li selected. ' + $(this).text() );

		var li = $(e.currentTarget).closest("li");
        var myOld;
		if (li.find(".select-one").length) { // 他をすべて off にする
            if ( app.isProhibitItemUpdItemOne(e) ) return;
			if(li.hasClass('selected')){
                myOld =li;
				li.removeClass('selected');
                _fSaveChanged( li, { quantity: 0 } );
			}else{
                myOld = $('#goods li.item.selected');
                if ( myOld.length ) {
                    _fSaveChanged( myOld, { quantity: 0 } );
                }
				$('#goods li').removeClass('selected');
				li.addClass('selected');
                _fSaveChanged( li, { quantity: 1 } );
			}
            // console.log( '** 択一(ex): ' + ' ' + _fGetShohinCd(myOld) + '=>' + _fGetShohinCd(li) + ' ' + li.hasClass('selected'));

            if ( $(e.currentTarget).attr('data-selone-ex-chubunrui') ) {
                var selone_ex_chubunrui = $(e.currentTarget).attr('data-selone-ex-chubunrui');
                var shohin_cd = li.attr('data-item');
                // console.log( 'data-selone-ex-chubunrui=>' + selone_ex_chubunrui + ' shohin_cd=>' + shohin_cd );
                app.updSelOneEx( { ex_type: 'selone-ex',
                                   chu_bunrui_cd: selone_ex_chubunrui,
                                   shohin_cd: shohin_cd } );
                return;
            }
		} else if ( $(e.currentTarget).attr('data-sub-select-one-kbn') ) { // 同じ商品区分の他商品を off にする
            if ( app.isProhibitItemUpdItemOne(e) ) return;
            var shohin_kbn_sel = $(e.currentTarget).attr('data-sub-select-one-kbn');
            // console.log( '### *** ### shohin_kbn_sel=>' + shohin_kbn_sel );
			if(li.hasClass('selected')){
                myOld =li;
				li.removeClass('selected');
                _fSaveChanged( li, { quantity: 0 } );
			}else{
                myOld = $('#goods li.item.selected span.select[data-sub-select-one-kbn="' + shohin_kbn_sel + '"]');
                if ( myOld.length ) {
                    _fSaveChanged( myOld, { quantity: 0 } );
                }
				// $('#goods li').removeClass('selected');
                myOld.closest("li").removeClass('selected');
				li.addClass('selected');
                _fSaveChanged( li, { quantity: 1 } );
			}
            // console.log( '** 択一(ex): ' + ' ' + _fGetShohinCd(myOld) + '=>' + _fGetShohinCd(li) + ' ' + li.hasClass('selected'));

            if ( $(e.currentTarget).attr('data-selone-ex-chubunrui') ) {
                var selone_ex_chubunrui = $(e.currentTarget).attr('data-selone-ex-chubunrui');
                var shohin_cd = li.attr('data-item');
                // console.log( 'data-selone-ex-chubunrui=>' + selone_ex_chubunrui + ' shohin_cd=>' + shohin_cd );
                app.updSelOneEx( { ex_type: 'selone-ex',
                                   chu_bunrui_cd: selone_ex_chubunrui,
                                   shohin_cd: shohin_cd } );
                return;
            }
        } else {
            if ( app.isProhibitItemUpd(e) ) return;
			if(li.hasClass('selected')){ // 自身だけの on/off
				li.removeClass('selected');
                _fSaveChanged( li, { quantity: 0 } );
			} else {
				li.addClass('selected');
                _fSaveChanged( li, { quantity: 1 } );
			}
            // console.log( '** 択一(?): ' + ' cd=>' + _fGetShohinCd($(this)) + ' ' + li.hasClass('selected') );
		}

        // console.log( '択一 #goods li selected.' + ' cd=>' + _fGetShohinCd($(this)) );
	});

    // orginal: main.js   end --

	/* 奉仕料 */
	$('#detail').on('click', '#goods .list-item-food ul li .lbl_service_charge', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;
        // if ( app.isProhibitItemUpd(e) ) return;

        var myOld = $(this).hasClass('ui-state-active');
        var orgChargeKbn = $(this).closest("li[data-item]").data('org-chargekbn');

        _fSaveChanged( $(this), { chargekbn: myOld ? 0 : 1 } ); // 2017/03/01 mihara revert again
        // _fSaveChanged( $(this), { chargekbn: myOld ? 0 : orgChargeKbn } ); // toggle   2015/06/22 org: chargekbn: myOld ? 0 : 1
        // console.log( '奉仕料 clicked. ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + !myOld );
	});

	/* 価格 */
	$('#detail').on('change', '#goods .list ul li input[name="price"]', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;

        var $ele = $(this);

        var myOld = parseInt($ele.data('org-price')),
            myNew = parseInt(utils.commaOmit($ele.val()));

        _fSaveChanged( $(this), { price: myNew } );
        // console.log( '価格 changed. ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + myNew );
	});

	/* 商品名 */
	$('#detail').on('change', '#goods .list ul li input[name="shohin_nm"]', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;

        var $ele = $(this);

        var myOld = $ele.data('org-shohin_nm'),
            myNew = $ele.val();

        _fSaveChanged( $(this), { shohinnm: myNew } );
        // console.log( '価格 changed. ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + myNew );
	});

    // from juchu.food.js
    /* 飲み物(概算) */
	$('#detail').on('change', '#drink_tanka, #drink_quantity', function(e){
		var Drinkquantity	= $('#drink_quantity').val();			// 人数
		Drinkquantity = Drinkquantity.replace(',','');				// カンマを削除
		Drinkquantity = Drinkquantity.replace(/\D/g, '');			// 数値以外を削除
        $('#drink_quantity').val( Drinkquantity );                  // 強制的に設定
		var Drinktanka	= $('#drink_tanka').val();					// 単価
		Drinktanka = Drinktanka.replace(',','');					// カンマを削除
		Drinktanka = Drinktanka.replace(/\D/g, '');					// 数値以外を削除
        $('#drink_tanka').val(Drinktanka);		                    // 強制的に設定
		var Drinkprc  = Drinkquantity * Drinktanka;					// 人数×単価
		Drinkprc = $.msiJqlib.commaAdd(Drinkprc,null);				// 金額にカンマを付加
		$('#drink_prc').val(Drinkprc);								// 金額
        _fSaveChanged2( $(this), { price: +Drinktanka, quantity: +Drinkquantity } );
    });
    /* 席料 */
	$('#detail').on('change', '#charge_tanka, #charge_quantity', function(e){
		var ChargeQuantity	= $('#charge_quantity').val();			// 人数
		ChargeQuantity = ChargeQuantity.replace(',','');			// カンマを削除
        ChargeQuantity = ChargeQuantity.replace(/\D/g, '');         // 数値以外を削除
		$('#charge_quantity').val( ChargeQuantity );			    // 強制的に設定
		var Chargetanka	= $('#charge_tanka').val();					// 単価
		Chargetanka = Chargetanka.replace(',','');					// カンマを削除
		Chargetanka = Chargetanka.replace(/\D/g, '');				// 数値以外を削除
		$('#charge_tanka').val( Chargetanka );					    // 強制的に設定
		var Chargeprc  = ChargeQuantity * Chargetanka;				// 人数×単価
		Chargeprc = $.msiJqlib.commaAdd(Chargeprc,null);			// 金額にカンマを付加
		$('#charge_prc').val(Chargeprc);							// 金額

        _fSaveChanged2( $(this), { price: +Chargetanka, quantity: +ChargeQuantity } );
    });

    /* アレルギー確認 */
	$('#detail').on('change', '#allergy_memo', function(e){
		var curV = $('#allergy_memo').val(),
            $d = $(this).closest("div.data-in"),
            orgV = $d.data('org_allergy_memo');
        // console.log( 'curV=>' + curV + ' orgV=>' + orgV );
        if ( !$.msiJqlib.isStrEqual(curV, orgV) ) { // 変更あり
            newEtcData['allergy'][curLvl1] = { txt: curV,
                                               category_kbn: $d.data('category_kbn') };
            orgEtcData['allergy'][curLvl1] = { txt: orgV,
                                               category_kbn: $d.data('category_kbn') };
        } else {
            delete newEtcData['allergy'][curLvl1];
            delete orgEtcData['allergy'][curLvl1];
        }
        app.render();
    });

    // 現在選択された要素から対象商品CDを返す
    var _fGetShohinCd = function($pos) {
        var obj = _fGetShohin($pos);
        if ( ! obj ) return null;
        return obj['shohincd'];
    }

    // 現在選択された要素から対象商品情報を返す
    var _fGetShohin = function($pos) {
        if ( !$pos ) return null;
        var $li = $pos.closest("li[data-item]"),
            obj = null;
        if ( $li ) {
            obj = {
                shohincd:        $li.data('item'),              // 商品CD
                daibunruicd:     $li.data('dai_bunrui_cd'),     // 大分類コード
                chubunruicd:     $li.data('chu_bunrui_cd'),     // 中分類コード
                shohinkbn:       $li.data('shohin_kbn'),        // 商品区分コード
                referuchiwkno:   $li.data('refer_uchiwk_no'),   // 参照先明細№
                sagakukeisangrp: $li.data('sagaku_keisan_grp'), // 差額計算分類
                price:           parseInt($li.data('org-price')),
                quantity:        parseInt($li.data('org-quantity')),
                shohinnm:        $li.data('org-shohin_nm'),
                chargekbn:       $li.data('org-chargekbn') || 0, // 2015/06/22 org $li.data('org-chargekbn') ? 1 : 0
                category_kbn:    $li.data('category_kbn'),
                shohintkiyonm:   $li.data('org-summary'),
                shohintkiyonm2:  $li.data('org-summary2'),
                mokuteki_kbn:    $li.data('mokuteki_kbn'), // 目的区分
            };
        }
        return obj;
    }

    // データ変更時の設定(通常の場合)
    var _fSaveChanged = function($ele, newVals) {
        var obj = _fGetShohin($ele),
            shohinCd = _fGetShohinCd($ele),
            orgObj;
        return _fSaveChanged_sub(newVals, obj, shohinCd, orgObj);
    }

    // 現在選択された要素から対象商品CDを返す(飲物(概算)等の場合)
    var _fGetShohinCd2 = function($pos) {
        var obj = _fGetShohin2($pos);
        if ( ! obj ) return null;
        return obj['shohincd'];
    }

    // 現在選択された要素から対象商品情報を返す(飲物(概算)等の場合)
    var _fGetShohin2 = function($pos) {
        if ( !$pos ) return null;
        var $d = $pos.closest("div.data-in"),
            obj = null;
        if ( $d ) {
            obj = {
                shohincd:        $d.data('item'),              // 商品CD
                daibunruicd:     $d.data('dai_bunrui_cd'),     // 大分類コード
                chubunruicd:     $d.data('chu_bunrui_cd'),     // 中分類コード
                shohinkbn:       $d.data('shohin_kbn'),        // 商品区分コード
                referuchiwkno:   '', // $d.data('refer_uchiwk_no'),   // 参照先明細№
                sagakukeisangrp: '', // $d.data('sagaku_keisan_grp'), // 差額計算分類
                price:           parseInt($d.data('org-price')),
                quantity:        parseInt($d.data('org-quantity')),
                shohinnm:        $d.data('org-shohin_nm'),
                chargekbn:       $d.data('org-chargekbn') || 0, // 2015/06/22 org $li.data('org-chargekbn') ? 1 : 0
                category_kbn:    $d.data('category_kbn'),
                shohintkiyonm:   $d.data('org-summary'),
                shohintkiyonm2:  $d.data('org-summary2'),
                kbn2         :   $d.data('kbn2'),
                mokuteki_kbn:    $d.data('mokuteki_kbn'), // 目的区分
            };
        }
        return obj;

    }

    // データ変更時の設定(飲物(概算)等の場合)
    var _fSaveChanged2 = function($ele, newVals) {
        var obj = _fGetShohin2($ele),
            shohinCd = _fGetShohinCd2($ele),
            orgObj;
        return _fSaveChanged_sub(newVals, obj, shohinCd, orgObj);
    }

    // データ変更時の設定(下請け関数)
    var _fSaveChanged_sub = function(newVals, obj, shohinCd, orgObj) {

        // newShohinData にデータがないときは登録
        if ( ! _.has(newShohinData, shohinCd) ) {
            newShohinData[shohinCd] = _.extend({}, obj);
        }
        newShohinData[shohinCd] = _.extend(newShohinData[shohinCd], newVals);

        // orgShohinData 調整
        if ( ! _.has(orgShohinData, shohinCd) ) {
            // orgShohinData にデータがないときは登録
            obj['shohintkiyonm2'] = ''; // patch XXX
            orgShohinData[shohinCd] = _.extend({}, obj);
        }

        // 最初のデータに戻ったときは削除
        if ( $.msiJqlib.isEqual(orgShohinData[shohinCd], newShohinData[shohinCd]) ) {
            delete orgShohinData[shohinCd];
            delete newShohinData[shohinCd];
            // console.log( '_fSaveChanged orgShohinData 000=>' + JSON.stringify(orgShohinData) );
            // console.log( '_fSaveChanged newShohinData 000=>' + JSON.stringify(newShohinData) );
            app.render();
            return false;
        }

        // console.log( '_fSaveChanged orgShohinData 111=>' + JSON.stringify(orgShohinData) );
        // console.log( '_fSaveChanged newShohinData 111=>' + JSON.stringify(newShohinData) );
        app.render();
        return true;
    }

    // データ変更あり?
    var _fIsChanged = function() {
        if ( isForceChanged ) {
            return true;
        }

        if ( Object.keys(orgShohinData).length > 0 ) {
            return true;
        }
        if ( Object.keys(newEtcData.allergy).length > 0 ) {
            return true;
        }
        return false;
    }

    /* アレルギー確認等 toggle */
	$('#detail').on('click', '#item-picker-footer-toggle-id span', function(e){
		var $ele = $('#item-picker-footer-toggle-id'); // $(e.currentTarget);
        if ( $ele.hasClass('st-open') ) {
            $ele.removeClass('st-open');
            // console.log( 'curLvl0=>' + curLvl0 + ' curLvl1=>' + curLvl1 + ' curLvl2=>' + curLvl2 );
            app.setCookieForPref( curLvl0 + '.' + curLvl1 + '.tg1', 0 );
            $('#item-picker-footer-cont-id').hide();
            // $('#item-picker-footer-cont-id').slideUp(200, function(){app.render()});
        } else {
            $ele.addClass('st-open');
            app.setCookieForPref( curLvl0 + '.' + curLvl1 + '.tg1', 1 );
            $('#item-picker-footer-cont-id').height( $('#item-picker-footer-cont-id').data('height') );
            $('#item-picker-footer-cont-id').show();
            // $('#item-picker-footer-cont-id').slideDown(200, function(){app.render()});
        }
        app.render();
    });

    /* 隠れ複数行タブ */
	$('#detail').on('click', '#lvl1_tab_arrow_prev, #lvl1_tab_arrow_next', function(e){
        var $ele = $('#detail .lvl1_tab'),
            // top = $ele.position().top,
            top = parseInt($ele.css('marginTop')),
            off = $('#detail .lvl1_tab li').height(),
            boundary = $('#detail .lvl1_tab').height() - off,
            id = $(e.currentTarget).attr('id'),
            newTop, newTop2;
        if ( id == 'lvl1_tab_arrow_prev') {
            newTop = top + off;
        } else {
            newTop = top - off;
        }
        // console.log( id + ' => ' + [top,off,newTop,boundary].join(',') );
        $('#lvl1_tab_arrow_prev').removeClass("off");
        $('#lvl1_tab_arrow_next').removeClass("off");
        if ( newTop >= 0 ) {
            $('#lvl1_tab_arrow_prev').addClass("off");
            newTop2 = 0;
        // } else if ( -newTop > boundary ) { // org
        } else if ( -newTop > boundary+off/2 ) { // patch for chrome zoom(expand/shrink)
            $('#lvl1_tab_arrow_next').addClass("off");
            return;
        } else {
            newTop2 = newTop;
        }
        // $ele.css( {marginTop: newTop2 } );
        $ele.animate({marginTop: newTop2}, {duration:'fast'});
    });

    /* 隠れ複数行タブ２ */
	$('#detail').on('click', '#lvl2_tab_arrow_prev, #lvl2_tab_arrow_next', function(e){
        var $ele = $('#detail .lvl2_tab'),
            // top = $ele.position().top,
            top = parseInt($ele.css('marginTop')),
            off = $('#detail .lvl2_tab li').height(),
            boundary = $('#detail .lvl2_tab').height() - off,
            id = $(e.currentTarget).attr('id'),
            newTop, newTop2;
        if ( id == 'lvl2_tab_arrow_prev') {
            newTop = top + off;
        } else {
            newTop = top - off;
        }
        // console.log( id + ' => ' + [top,off,newTop,boundary].join(',') );
        $('#lvl2_tab_arrow_prev').removeClass("off");
        $('#lvl2_tab_arrow_next').removeClass("off");
        if ( newTop >= 0 ) {
            $('#lvl2_tab_arrow_prev').addClass("off");
            newTop2 = 0;
        // } else if ( -newTop > boundary ) { // org
        } else if ( -newTop > boundary+off/2 ) { // patch for chrome zoom(expand/shrink)
            $('#lvl2_tab_arrow_next').addClass("off");
            return;
        } else {
            newTop2 = newTop;
        }
        // $ele.css( {marginTop: newTop2 } );
        $ele.animate({marginTop: newTop2}, {duration:'fast'});
        tabLvl2Top = newTop2; // save
    });

    /* 摘要入力 */
	$('#detail').on('click', '.tkiyo-label', function(e){
        var $ele = $(e.currentTarget),
            $ctrl = $ele.closest('.tkiyo-ctrl'),
            $input = $ctrl.find('.tkiyo-input');
        if ( $ctrl.hasClass('my-close') ) {
            $input.show();
            $ctrl.removeClass('my-close'); // no animation
            // $ctrl.stop().animate( {left: 0}, 200, function(){ $ctrl.removeClass('my-close'); } ); // animation
        } else {
            $input.hide(); $ctrl.addClass('my-close'); // no animation
            // $ctrl.stop().animate( {left: "80%"}, 200, function(){ $input.hide(); $ctrl.addClass('my-close'); } ); // animation
        }
        // console.log( 'class=>' + $ctrl.hasClass('my-close') );
    });

	$('#detail').on('focus', 'input.tkiyo-input', function(e){
        // if (!app.isUpdCtxtWithMsg(e)) return;
        var $ele = $(e.currentTarget),
            $ctrl = $ele.closest('.tkiyo-ctrl'),
            $input = $ctrl.find('.tkiyo-input');
        if ( $ctrl.hasClass('my-close') ) {
            $input.show();
            $ctrl.removeClass('my-close'); // no animation
            // $ctrl.stop().animate( {left: 0}, 200, function(){ $ctrl.removeClass('my-close'); } ); // animation
        }
    });

	$('#detail').on('change', 'input.tkiyo-input', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;

        var $ele = $(e.currentTarget),
            $ctrl = $ele.closest('.tkiyo-ctrl'),
            $label = $ctrl.find('.tkiyo-label'),
            $input = $ele,
            $li = $ctrl.closest("li[data-item]"),
            val = $input.val(),
            myOld = $li.data('org-summary'),
            myNew = val;

        if ( !$.msiJqlib.isNullEx(val) && val.length > 0 ) {
            $label.addClass( 'my-val-set' );
            $label.attr('title', val);
        } else {
            $label.removeClass( 'my-val-set' );
            $label.attr('title', '');
        }

        // console.log( ".tkiyo-input change val=>" + val );
        // $li.attr('data-summary', val);

        _fSaveChanged( $(this), { shohintkiyonm: myNew } );
        // console.log( '摘要 changed. ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + myNew );
    });

    // 2015/10/28 商品摘要名2
	$('#detail').on('change', 'input.tkiyo-input2', function(e){
        if (!app.isUpdCtxtWithMsg(e)) return;

        var $ele = $(e.currentTarget),
            $ctrl = $ele.closest('.tkiyo-ctrl'),
            $label = $ctrl.find('.tkiyo-label'),
            $input = $ele,
            $li = $ctrl.closest("li[data-item]"),
            val = $input.val(),
            myOld = $li.data('org-summary2'),
            myNew = val;

        if ( !$.msiJqlib.isNullEx(val) && val.length > 0 ) {
            $label.addClass( 'my-val-set' );
            $label.attr('title', val);
        } else {
            $label.removeClass( 'my-val-set' );
            $label.attr('title', '');
        }

        // console.log( ".tkiyo-input change val=>" + val );
        // $li.attr('data-summary', val);

        _fSaveChanged( $(this), { shohintkiyonm2: myNew } );
        // console.log( '摘要 changed. ' + $(this).text() + ' cd=>' + _fGetShohinCd($(this)) + ' ' + myOld + '=>' + myNew );
    });

    var _picker_dlg_close = function() {
        if ( mySwiper ) {
            mySwiper.destroy();
            mySwiper = null;
        }
        $('#itemex-picker-dlg').hide();
        $('#itemex-shade').fadeOut(400);
    };

    $('#itemex-picker-dlg').on('click', function(ev) {
        // ev.stopImmediatePropagation();
        ev.originalEvent['itemex-picker-dlg-event'] = true;
        if ( mySwiper ) { ev.shiftKey ? mySwiper.slidePrev() : mySwiper.slideNext(); }
    } );
    $('#itemex-picker-dlg .close, #itemex-shade').on('click', function(ev) {
        if ( !ev.originalEvent['itemex-picker-dlg-event'] ) {
            _picker_dlg_close();
        }
    } );
    $(document).on( 'keydown', function(ev) {
        if ( $('#itemex-picker-dlg').is(':visible') ) {
            // console.log( 'keydown ev.keyCode=>' + ev.keyCode );
            if ( ev.keyCode == 27 ) { // esc key
                ev.stopImmediatePropagation();
                _picker_dlg_close();
            }
        }
    } );

// 2017.04.18 それぞれの部門に計上する
//葬儀・法事は計上部門は「00010：塩釜本社」のみなので、変更不可にする
//    $("#hall_cd").attr("disabled", "disabled");
//	$('#hall_cd').select2('val', '00010');

});
