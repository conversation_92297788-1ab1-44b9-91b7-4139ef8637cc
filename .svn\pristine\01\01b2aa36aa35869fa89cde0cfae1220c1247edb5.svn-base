<?php

/**
 * App_MitsuLib
 *
 * 見積書関連クラス
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> Sato
 * @since      2014/05/16
 * @filesource 
 */

/**
 * App 見積書関連クラス
 * 
 * @category   library
 * @package    library\App
 * <AUTHOR> Sato
 * @since      2014/05/16
 */
class App_MitsuLib {

    const ID_PLAN_PRC = 'plan';  // プラン
    const ID_PLAN_NEBIKI_PRC = 'plan_nebiki';    // プラン値引
    const ID_KEIYAKU_PRC = 'keiyaku_prc';    // 契約金額
    const ID_WARIMASHI_PRC = 'warimashi_prc';  // 割増金額
    const ID_PLAN_GAI_PRC = 'plan_gai';  // プラン外費用
    const ID_WARIBIKI_PRC = 'waribiki';  // 割引金額
    const ID_HENREI = 'henrei'; // 返礼品
    const ID_RYORI = 'ryori'; // 料理
    const ID_OTHER = 'other'; // その他
    const ID_WARIBIKI_OMT_PRC = 'nebiki_omt'; // 割引金額(返礼品＋料理＋その他)
    const ID_KOJO_PRC = 'kojo_prc'; // 控除金額
    const ID_TOKUTEN_PRC = 'tokuten_prc'; // 特典金額
    const ID_NOUSE_PRC = 'no_use_prc'; // 未使用商品金額
    const ID_NEBIKI_PRC = 'nebiki'; // 値引金額
    const ID_EARLYUSE = 'early_use_cost';    // 早期利用費
    const ID_EARLYUSEZEI = 'early_use_cost_zei'; // 早期利用費の消費税
    const ID_KAKEZEI = 'keiyaku_zei';   // 掛金利用費税
    const ID_KAKEZAN = 'keiyaku_zan';  // 掛金残
    const ID_TATEKAE = 'tatekae';  // 立替金
    const ID_KAIYAKU_PRC = 'kaiyaku_prc';  // 施行解約金
    const ID_ZEIPRC = 'zei_prc';    // 消費税
    const ID_OUT_ZEI_PRC = 'out_zei_prc';   // 外税
    const ID_OUT_ZEI_PRC_KEIGEN = 'out_zei_prc_keigen'; // 外税(軽減)
    const ID_IN_ZEI_PRC = 'in_zei_prc'; // 内税
    const ID_IN_ZEI_PRC_KEIGEN = 'in_zei_prc_keign';    // 内税(軽減)
    const ID_SZEI_TAISHO_PRC = 'szei_katax_taisho_prc'; // 外税課税対象額
    const ID_SZEI_TAISHO_PRC_KEIGEN = 'szei_katax_taisho_prc_keigen'; // 外税課税対象額(軽減)
    const ID_UZEI_TAISHO_PRC = 'uzei_katax_taisho_prc'; // 内税課税対象額
    const ID_UZEI_TAISHO_PRC_KEIGEN = 'uzei_katax_taisho_prc_keigen'; // 内税課税対象額(軽減)
    const ID_HZEI_TAISHO_PRC = 'hzei_katax_taisho_prc'; // 非課税課税対象額
    const ID_HZEI_TAISHO_PRC_KEIGEN = 'hzei_katax_taisho_prc_keigen'; // 非課税課税対象額(軽減)
    const ID_COURSESNM = 'course_snm_cd';      // コース名
    const ID_TOTAL_PRC = 'total';

    /**
     * 見積書関連の金額を取得
     * 
     * <AUTHOR> Sato
     * @since 2014/05/16
     * @param type $db
     * @param string $seko_no 施行番号
     * @param string $seko_no_sub 施行番号（枝番）
     * @param int $data_kbn データ区分
     * @param int $history_no 履歴No
     * @return     array      見積書関連の金額
     */

    public static function getKingaku($db, $seko_no, $seko_no_sub, $data_kbn = 1, $history_no = null) {
        $ret_ary = array(
            self::ID_PLAN_PRC => 0,
            self::ID_PLAN_NEBIKI_PRC => 0,
            self::ID_KEIYAKU_PRC => 0,
            self::ID_WARIMASHI_PRC => 0,
            self::ID_PLAN_GAI_PRC => 0,
            self::ID_WARIBIKI_PRC => 0,
            self::ID_HENREI => 0,
            self::ID_RYORI => 0,
            self::ID_OTHER => 0,
            self::ID_WARIBIKI_OMT_PRC => 0,
            self::ID_KOJO_PRC => 0,
            self::ID_TOKUTEN_PRC => 0,
            self::ID_NOUSE_PRC => 0,
            self::ID_NEBIKI_PRC => 0,
            self::ID_EARLYUSE => 0,
            self::ID_EARLYUSEZEI => 0,
            self::ID_KAKEZEI => 0,
            self::ID_KAKEZAN => 0,
            self::ID_TATEKAE => 0,
            self::ID_KAIYAKU_PRC => 0,
            self::ID_ZEIPRC => 0,
            self::ID_OUT_ZEI_PRC => 0,
            self::ID_OUT_ZEI_PRC_KEIGEN => 0,
            self::ID_IN_ZEI_PRC => 0,
            self::ID_IN_ZEI_PRC_KEIGEN => 0,
            self::ID_SZEI_TAISHO_PRC => 0,
            self::ID_SZEI_TAISHO_PRC_KEIGEN => 0,
            self::ID_UZEI_TAISHO_PRC => 0,
            self::ID_UZEI_TAISHO_PRC_KEIGEN => 0,
            self::ID_HZEI_TAISHO_PRC => 0,
            self::ID_HZEI_TAISHO_PRC_KEIGEN => 0,
            self::ID_TOTAL_PRC => 0,
        );

        if (is_null($history_no)) {
            $rec = DataMapper_JuchuDenpyo::findDenpyo($db, array('seko_no' => $seko_no, 'data_kbn' => $data_kbn));
        } else {
            $rec = DataMapper_MitsuCalc::getZeiPrcFromHistory($db, array('data_kbn' => $data_kbn, 'seko_no' => $seko_no, 'seko_no_sub' => $seko_no_sub, 'history_no' => $history_no));
        }
        if (count($rec) > 0) {
            if ($data_kbn == '2') {
                $kagamiInfo = App_Utils2::getJuchuKagamiHouji($rec[0]['denpyo_no']);
            } else {
                $kagamiInfo = App_Utils2::getJuchuKagami($rec[0]['denpyo_no']);
            }
            $ret_ary[self::ID_PLAN_PRC] = $kagamiInfo[self::ID_PLAN_PRC]['prc'];
            $ret_ary[self::ID_PLAN_NEBIKI_PRC] = $kagamiInfo[self::ID_PLAN_NEBIKI_PRC]['prc'];
            $ret_ary[self::ID_KEIYAKU_PRC] = $kagamiInfo[self::ID_KEIYAKU_PRC]['prc'];
            $ret_ary[self::ID_WARIMASHI_PRC] = $kagamiInfo[self::ID_WARIMASHI_PRC]['prc'];
            $ret_ary[self::ID_PLAN_GAI_PRC] = $kagamiInfo[self::ID_PLAN_GAI_PRC]['prc'];
            $ret_ary[self::ID_WARIBIKI_PRC] = $kagamiInfo[self::ID_WARIBIKI_PRC]['prc'];
            $ret_ary[self::ID_HENREI] = $kagamiInfo[self::ID_HENREI]['prc'];
            $ret_ary[self::ID_RYORI] = $kagamiInfo[self::ID_RYORI]['prc'];
            $ret_ary[self::ID_OTHER] = $kagamiInfo[self::ID_OTHER]['prc'];
            $ret_ary[self::ID_WARIBIKI_OMT_PRC] = $kagamiInfo[self::ID_WARIBIKI_OMT_PRC]['prc'];
            $ret_ary[self::ID_KOJO_PRC] = $kagamiInfo[self::ID_KOJO_PRC]['prc'];
            $ret_ary[self::ID_TOKUTEN_PRC] = $kagamiInfo[self::ID_TOKUTEN_PRC]['prc'];
            $ret_ary[self::ID_NOUSE_PRC] = $kagamiInfo[self::ID_NOUSE_PRC]['prc'];
            $ret_ary[self::ID_NEBIKI_PRC] = $kagamiInfo[self::ID_NEBIKI_PRC]['prc'];
            $ret_ary[self::ID_EARLYUSE] = $kagamiInfo[self::ID_EARLYUSE]['prc'];
            $ret_ary[self::ID_EARLYUSEZEI] = $kagamiInfo[self::ID_EARLYUSEZEI]['prc'];
            $ret_ary[self::ID_KAKEZEI] = $kagamiInfo[self::ID_KAKEZEI]['prc'];
            $ret_ary[self::ID_KAKEZAN] = $kagamiInfo[self::ID_KAKEZAN]['prc'];
            $ret_ary[self::ID_TATEKAE] = $kagamiInfo[self::ID_TATEKAE]['prc'];
            $ret_ary[self::ID_KAIYAKU_PRC] = $kagamiInfo[self::ID_KAIYAKU_PRC]['prc'];
            $ret_ary[self::ID_ZEIPRC] = $kagamiInfo['out_zei_prc']['prc'];
            $ret_ary[self::ID_OUT_ZEI_PRC] = $kagamiInfo[self::ID_OUT_ZEI_PRC]['prc'];
            $ret_ary[self::ID_OUT_ZEI_PRC_KEIGEN] = $kagamiInfo[self::ID_OUT_ZEI_PRC_KEIGEN]['prc'];
            $ret_ary[self::ID_IN_ZEI_PRC] = $kagamiInfo[self::ID_IN_ZEI_PRC]['prc'];
            $ret_ary[self::ID_IN_ZEI_PRC_KEIGEN] = $kagamiInfo[self::ID_IN_ZEI_PRC_KEIGEN]['prc'];
            $ret_ary[self::ID_SZEI_TAISHO_PRC] = $kagamiInfo[self::ID_SZEI_TAISHO_PRC]['prc'];
            $ret_ary[self::ID_SZEI_TAISHO_PRC_KEIGEN] = $kagamiInfo[self::ID_SZEI_TAISHO_PRC_KEIGEN]['prc'];
            $ret_ary[self::ID_SZEI_TAISHO_PRC] = $kagamiInfo[self::ID_SZEI_TAISHO_PRC]['prc'];
            $ret_ary[self::ID_UZEI_TAISHO_PRC_KEIGEN] = $kagamiInfo[self::ID_UZEI_TAISHO_PRC_KEIGEN]['prc'];
            $ret_ary[self::ID_HZEI_TAISHO_PRC] = $kagamiInfo[self::ID_HZEI_TAISHO_PRC]['prc'];
            $ret_ary[self::ID_HZEI_TAISHO_PRC_KEIGEN] = $kagamiInfo[self::ID_HZEI_TAISHO_PRC_KEIGEN]['prc'];
            $ret_ary[self::ID_TOTAL_PRC] = $kagamiInfo[self::ID_TOTAL_PRC]['prc'];
        }
        return $ret_ary;
    }

}
