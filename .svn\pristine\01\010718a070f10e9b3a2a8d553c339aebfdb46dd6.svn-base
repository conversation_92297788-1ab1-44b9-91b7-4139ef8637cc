{include file="fdn_head_std.tpl"}
{include file="fdn_header_hachu2.tpl"}
<form action="#" ondragover="return false"  onMouseMove = "return false;">
    <div id="main">
        {include file="header_info.tpl"} 
        <!-- サイドメニュー開始 -->
        {include file="side_menu.tpl"}
        <!-- サイドメニュー終了 -->
        <!-- 本体開始 -->
            <div id="detail">
            <!-- タブメニュー開始 -->
            <ul id="tab" class="margin70 current">

            </ul>
            <!-- タブメニュー終了 -->
            <div class="content_wrap" style="height: 80%;">
                <div id="estimate_other" >
                <div class="mylist">
                    <table  style="width: 100%;">
                        <tbody>
                            <tr>
                                <td rowspan="2" class="chk_select"style="width:4.05%;">選択</td>
                                <td rowspan="2" class="w15 hachusaki" style="width: 17.9%">発注先</td>
                                <td style="border-right:1px solid #88B1D1;" class="nouhinbasho">納品場所</td>
                                <td class="t_kmk nouhinkbn">項目</td>
                                <td class=" hinpin">品目</td>
                                <td class=" tekiyo">摘要</td>
                                <td class=" tankaTitle" rowspan="2" style="width:6.9%">単価</td>
                                <td class=" suriyoTitle" rowspan="2" style="width:7%">数量</td>
                                <td  class="yobi" rowspan="2" >発注</td>
                            </tr>
                            <tr>                               
                                <td>納品日時</td>             
                                
                                <td colspan ="3"style="border-top: 1px solid #88B1D1;">備考</td>
                                 
                            </tr>
                        </tbody>
                    </table>
                </div>

                {literal}
                <div id ="dataContent2">
                </div>
                 <!-- 明細データ行 テンプレート -->
                <script type="text/template" id="item-template2">
                        <dl>
                        <dt class="field_left">
                        <span class="radio_set">
                            <label for="i_chk" class="lbl_stamp_check mycheckBox"></label>
                             <input type="checkbox"  id="i_chk" name="i_chk" value=""  />
                            </label>
                        </span>
                        </dt>
                        <dt>
                        <input type="text"  name="hinmk" class="txt hinmkLabel" value="" readonly = "readonly" />
                       
                         <div class="label dlg_date hachusk_button"></div>
                          <input type ="hidden" name ="siire_cd" class="siire_cd" value ="" />
                         </dt>
                         <dt>
                            <div class="product_detail">
                            <select class="komokku" data-id="">
                            </select>
                           <input type="text" name="n_place" class="txt i_n_place"   readonly="readonly"/>
                           <div class="label dlg_place"></div>
                           <input type="text"  name="date" class="txt i_date date_auto_slash to_alpha_num" value="" />
                            <div class="label dlg_date ymdcalendar"></div>
                            <input type="text"  name="time"  class="txt i_time time_auto_colon to_alpha_num" value="" />
                            <label class="labeltime">～</label>
                            <input type="text"  name="time"  class="txt i_time2 time_auto_colon to_alpha_num" />
                         <!--   <div class="label dlg_time"></div> -->
                             </div>
                        </dt>
                        <dt>
                            <input type ="text" name ="shohin_kbn" value ="" class= "txt shohin_kbn" readonly = "readonly">
                            <input type="text" name="hin_pin" class="txt i_hinpin" value="" readonly = "readony" />
                            <input type="text"  name="tekiyo" class="txt i_tekiyo" value=""  readonly="readonly" style="width:9.05%" />
                            <input type="text" name="bikou" class="txt i_bikou"  maxlength="30"style="width:32.85%;"/>
                        </dt>
                        <dt>
                            <input type="text"  name="tanka" class="txt i_tanka" value="" readonly="readonly" onblur="$.msiJqlib.commaFilterTemp($(this));"style="width:6.85%"/>
                            <input type="text" id="hachu_suryo_1" name="hachu_suryo"  class="txt i_amount" value=""  onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                                              
<!--	2017/07/13	Okuyama width修正                            <input type="text" id="hachu" name="hachu" class="txt i_stats" value="" readonly="readonly" style=width:6.9% />	-->
                            <input type="text" id="hachu" name="hachu" class="txt i_stats" value="" readonly="readonly" style=width:5% />

                        </dt>
                        </dl>
                        <dl>                            
                </script>
                {/literal}  

                </div>
                
                <div class="function">
                    {*<input type="button" name="btn_add" id="btn_add" value="追加" />
                    <input type="button" name="btn_del" id="btn_del" value="削除" />*}
                    <label for="memo" class="lbl_memo option">備考</label>
                    <textarea name="memo" id="memo" class="txt" cols="1" rows="10" maxlength="300"></textarea>
                </div> 
                
            </div>
            <!-- tab2 -->
                <div class="content_wrap disnon" style="height: 80%;">
                <div id="headlabelarea">
                    <div class="w100">
                      <!--  <label for="change_account" class="lbl_change_account" style="width:12%;">受注金額</label> -->
                        <label for="apply_type" class="lbl_apply_type require done">第一タイトル</label>
                        <input name="hope1" type="text" class="txt lsmall otherhope1" value="">
                        <label for="apply_type" class="lbl_apply_type require done">第二タイトル</label>
                        <input name="hope2" type="text" class="txt lsmall otherhope2" value="">
                        <label for="apply_type" class="lbl_apply_type require done">第三タイトル</label>
                        <input name="hope3" type="text" class="txt lsmall otherhope3" value="">
                        
                    <label for="juchusum" class="lbl_apply_type require done lbljuchu">受注金額　合計</label>
                    <input name="juchusum" type="text" class="txt" id="juchusum" readonly="ture">
                    </div>
                    <div class="w100">
                        <label for="apply_type" class="lbl_apply_type require done">備考</label>
                        <input name="biko" type="text" class="txt" id="lblbiko" value="" style="width:63%;">
                    </div>
                </div>                   
                <div id="estimate">
                    <div class="list">
                        <table>
                            <thead>
                                <tr>
                                    <th rowspan="2" class="chk_select">選択</th>
                                    <th class="w25" style="width:18.5%">発注先</th>
                                    <th class="w15" style="width:21.4%">品名</th>
                                    <th class="w10">設営箇所</th>
                                    <th class="width2">発注金額</th>
                                    <th class="width2">数量</th>
                                    <th class="width2">発注</th>
                                      <td  class="yobi" style="yobiborder-right: none">予備</td>
                                </tr>
                                <tr>
                                    <th>納品場所</th>
                                    <th >納品日時</th>
                                    <th colspan="4">備考</th>
                                </tr>
                                 <tr>                               
                                <td colspan ="1"style="width: 6%; border-top: 1px solid #88B1D1;" >発注</td>
                               
                            </tr>
                            </thead>
                        </table>
                    </div>
                    {literal}
                    <div id ="dataContent">

                    </div>
                     <!-- 明細データ行 テンプレート -->
                        <script type="text/template" id="item-template">
                        <dl>
                            <dt class="field_left">
                            <span class="radio_set">
                                <label for="i_chk" class="lbl_stamp_check" ></label>
                                <input type="checkbox" id="i_chk" name="i_chk" />
                            </span>
                            </dt>
                            <dt>
                            <input type="text" name="hachusk" class="txt hinmk2" readonly="readonly" />                            
                            <div class="label dlg_date hachusk_button"></div>
                             <input type ="hidden" name ="siire_cd" class="siire_cd" value ="" />
                            <input type="text" id="hinmk" name="hinmk" class="txt shashu"  readonly="readonly"/>
                           <!-- <div class="label dlg_date hinmk_button" ></div> -->
                             <input type ="hidden" name ="shohin_cd" class="shohin_cd" value ="" />                             
                            <input type="text" id="koudn" name="koudn" class="txt youto"  readonly="readonly"/>
                            <input type="text" id="tanka" name="tanka" class="txt tanka" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                            <input type="text" id="amount" name="i_amount"  class="txt amount" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                            <input type="text" id="hachu" name="hachu" class="txt i_stats"readonly="readonly"/>
                            </dt>
                            <dt>
                             <select class="komokku">
                                </select>
                            <input type="text" id="n_place" name="n_place" class="txt i_n_place2"   readonly="readonly"/>
                            <div class="label dlg_place"></div>
                            <input type="text" name="date" class="txt i_date " />
                            <div class="label dlg_date ymdcalendar"></div>
                            <input type="text" name="time"  class="txt i_time i_time3" />
                            <label class="labeltime">～</label>
                            <input type="text"  name="time"  class="txt i_time4" />
                         <!--   <div class="label dlg_time"></div> -->
                            <input type="text" id="koutei1" name="koutei1" class="txt i_biko2" />
                            </dt>
                        </dl> 
                    </script>
                    {/literal}
                </div>
                <div class="function">
                    <input type="button" name="btn_add" id="btn_add" value="行コピー" />
                    <input type="button" name="btn_del" id="btn_del" value="削除" />
                </div>
                <div id="foot" class="base_3">
                    <fieldset>
                    <label for="hachusum" class="lbl_apply_type require done">発注金額　合計</label>
                    <input name="hachusum" type="text" class="txt" id="undersum" readonly="true">
                    <label id="kikakunaiyo" for="memo" class="require done underbiko">企画内容<br>（備考）</label>
                    <textarea name="memo" id="memo" class="txt" cols="1" rows="10"></textarea>
                    </fieldset>
                </div>        

            </div>                 
            {include file="hachu/hachushori/hachu_footer.tpl"}
            </div>


	</div>
    </div>
    <!-- タブ用隠しテキスト -->
    <input id="tab1txt" type="hidden" value="その他" readonly="readonly"/>
    <input id="tab2txt" type="hidden" value="遺品装飾" readonly="readonly" />
</form>
<script id="my-data-init-id" type="application/json">
    {$mydata_json|smarty:nodefaults}
</script>
<script id="my-data-init-seko" type="application/json">
    {$mydata_seko|smarty:nodefaults}                
</script>

{include file="fdn_footer_std.tpl"}
