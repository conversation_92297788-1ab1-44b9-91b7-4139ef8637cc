<?php
/**
 * Logic_WebEdi_HachuCancelLogic
 *
 * 発注取消処理
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2020/08/xx
 * @filesource 
 */

/**
 * 発注取消処理
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2020/08/xx
 */
class Logic_WebEdi_HachuCancelLogic
{
    // EDIステータス(7835) 0:-,1:未発注,2:受注ＯＫ,3:未回答,4:受注ＮＧ,5:発注取消
    const EDI_STATUS_OK = 2; // 受注ＯＫ
    const EDI_STATUS_MI = 3; // 未回答
    const EDI_STATUS_NG = 4; // 受注ＮＧ
    const EDI_STATUS_CANCEL = 5; // 発注取消

    /**
     * 施行関連発注取消処理
     *   cf. Kanri_DispController::cancelhachuAction() in DispController.hakuzen.php 
     *
     * <AUTHOR> Mihara
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
	 * @param      string $seko_no 施行番号
	 * @param      string $hachu_no 発注番号
     * @return     void
     */
    public static function cancelSekoHachu($db, $seko_no, $hachu_no)
    {
//         //仕入計上済の場合は、保存できないように修正
//         $select_siire = $db->easySelect(<<< END_OF_SQL
// SELECT b.siire_den_no
// FROM  seko_hachu_info   a
// LEFT JOIN siire_denpyo_msi b
// ON  a.hc_denpyo_no  =   b.hachu_denpyo_no
// AND a.hachu_no      =   b.hachu_msi_no
// AND 0               =   b.delete_flg
// WHERE a.delete_flg = 0
// AND a.seko_no   = :seko_no
// AND a.hachu_no  = :hachu_no
// AND b.hachu_denpyo_no is not null
// END_OF_SQL
//                                         , array(
//                                             'seko_no' => $seko_no
//                                             , 'hachu_no' => $hachu_no
//                                         ));
//         if (count($select_siire) > 0) {
//             $msg = '既に検品・仕入確定済みです。修正は検品・仕入確定を「未検品」にしてから修正して下さい。';
//             throw new Exception($msg);
//         }

        $select = static::_getSekoHachuInfoEx($db, $seko_no, $hachu_no, 1);
        if (count($select) > 0) {
            $hachuinfo = array();
            $hachuinfo['order_ymd'] = null;
            $hachuinfo['order_flg'] = '0'; // 未発注(0)
            // $hachuinfo['hachu_cancel_note'] = $hc_del_com; // 発注取消コメント
            // 受注明細№で更新するように修正
            $where = array();
            $where['seko_no'] = $select['seko_no'];  // 2015/11/01 ADD Kayo
            $where['data_kbn'] = $select['data_kbn']; // 2015/11/01 ADD Kayo
            $where['delete_flg'] = $select['delete_flg']; // 2015/11/01 ADD Kayo
            $where['hachu_no'] = $hachu_no;			  // 2015/12/15 UPD Kayo
            // 発注管理情報更新
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hachu_info", $hachuinfo, $where);
            $db->easyExecute($sql, $param);

            //施行発注管理情報の発注伝票を全て取得する
            $select_ex = $db->easySelect(<<< END_OF_SQL
SELECT a.hc_denpyo_no,a.hachu_no
FROM  seko_hachu_info   a
WHERE a.delete_flg = 0
AND a.seko_no   = :seko_no
AND a.data_kbn  = :data_kbn
AND a.hachu_no  = :hachu_no         -- 2015/12/15 UPD Kayo
order by a.hc_denpyo_no, a.hachu_no
END_OF_SQL
							, array(
                                'seko_no' => $select['seko_no']
                                , 'data_kbn' => $select['data_kbn']
                                , 'hachu_no' => $select['hachu_no']	  // 2015/12/15 UPD Kayo
                            ));

            foreach ($select_ex as $rec) {
                //発注伝票明細に削除フラグを立てる
                $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo_msi
set		delete_flg = 1
WHERE	denpyo_no	=:denpyo_no
AND		hachu_no	=:hachu_no
END_OF_SQL
                                 , array('denpyo_no' => $rec['hc_denpyo_no']
                                         , 'hachu_no' => $rec['hachu_no']));

                //ヘッダのデータを確保
                $selectGetHead = $db->easySelect(<<< END_OF_SQL
SELECT	hachu_prc_sum
	, hachu_nebk_sum
	, szei_katax_taisho_prc
	, uzei_katax_taisho_prc
	, hitax_katax_taisho_prc
	, out_zei_prc
	, in_zei_prc
FROM hachu_denpyo
WHERE denpyo_no		=	:denpyo_no
AND   delete_flg	=	0
END_OF_SQL
                                                 , array('denpyo_no' => $rec['hc_denpyo_no']));
                //削除対象のデータを確保
                $selectGetMsi = $db->easySelect(<<< END_OF_SQL
SELECT    hachu_prc
		, nebiki_prc
		, zei_kbn
		, out_zei_prc
		, in_zei_prc
FROM hachu_denpyo_msi
WHERE	denpyo_no	=	:denpyo_no
AND		delete_flg	=	0
AND		hachu_no	=	:hachu_no
END_OF_SQL
                                                , array('denpyo_no' => $select['hc_denpyo_no']
                                                        , 'hachu_no' => $hachu_no));
                $hachu_prc_sum = 0;
                $hachu_nebk_sum = 0;
                $szei_katax_taisho_prc = 0;
                $uzei_katax_taisho_prc = 0;
                $hitax_katax_taisho_prc = 0;
                $out_zei_prc = 0;
                $in_zei_prc = 0;
                foreach ($selectGetMsi as $msi_rec) {
                    //明細の内容をヘッダから減算
                    $hachu_prc_sum = $hachu_prc_sum + $msi_rec["hachu_prc"];
                    $hachu_nebk_sum = $hachu_nebk_sum + $msi_rec["nebiki_prc"];

                    if ($selectGetMsi[0]["zei_kbn"] == 0) {//非課税
                        $hitax_katax_taisho_prc = $hitax_katax_taisho_prc + $msi_rec["hachu_prc"];
                    } else if ($selectGetMsi[0]["zei_kbn"] == 1) {//内税
                        $uzei_katax_taisho_prc = $uzei_katax_taisho_prc + $msi_rec["hachu_prc"];
                    } else {//外税
                        $szei_katax_taisho_prc = $szei_katax_taisho_prc + $msi_rec["hachu_prc"];
                    }

                    $out_zei_prc = $out_zei_prc + $msi_rec["out_zei_prc"];
                    $in_zei_prc = $in_zei_prc + $msi_rec["in_zei_prc"];
                }
                $selectCnt = $db->easySelect(<<< END_OF_SQL
SELECT denpyo_no
FROM hachu_denpyo_msi
WHERE	denpyo_no=:denpyo_no
and		delete_flg = 0
END_OF_SQL
                                             , array('denpyo_no' => $rec['hc_denpyo_no']));

                //発注伝票の更新
                //データが0件の場合、削除フラグを立てる
                if (count($selectCnt) == 0) {
                    $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo
set		delete_flg = 1
WHERE	denpyo_no	=:denpyo_no
END_OF_SQL
                                            , array('denpyo_no' => $rec['hc_denpyo_no']));
                } else {
                    $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo set
 hachu_prc_sum			=:hachu_prc_sum
,hachu_nebk_sum			=:hachu_nebk_sum
,szei_katax_taisho_prc	=:szei_katax_taisho_prc
,uzei_katax_taisho_prc	=:uzei_katax_taisho_prc
,hitax_katax_taisho_prc	=:hitax_katax_taisho_prc
,out_zei_prc			=:out_zei_prc
,in_zei_prc				=:in_zei_prc
 WHERE denpyo_no		=:denpyo_no
END_OF_SQL
                                            , array('denpyo_no' => $rec['hc_denpyo_no']
                                                    , 'hachu_prc_sum' => $hachu_prc_sum
                                                    , 'hachu_nebk_sum' => $hachu_nebk_sum
                                                    , 'szei_katax_taisho_prc' => $szei_katax_taisho_prc
                                                    , 'uzei_katax_taisho_prc' => $uzei_katax_taisho_prc
                                                    , 'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc
                                                    , 'out_zei_prc' => $out_zei_prc
                                                    , 'in_zei_prc' => $in_zei_prc));
                }
                if ($cnt > 1) {
                    $msg = '実行エラーが発生しました';
                    throw new Exception($msg);
                }
            }
        }
    }

	/**
	 * copied from Kanri_DispController::getSekoHachuInfoEx()  in DispController.hakuzen.php
	 * 施行発注情報を取得する(拡張版)
	 *
	 * <AUTHOR> Kayo
	 * @since  2015/12/16
	 * @param string $seko_no 施行番号
	 * @param string $hachu_no 発注番号
	 * @param string $order_flg 注文フラグ
	 * @return array 施行発注情報
	 */
	protected static function _getSekoHachuInfoEx($db, $seko_no, $hachu_no, $order_flg) {
		$select = $db->easySelOne(<<< END_OF_SQL
SELECT
    seko_no
    ,hachu_no
    ,delete_flg
    ,hc_denpyo_no
	,jc_msi_no			-- 2015/11/31 ADD Kayo
	,data_kbn			-- 2015/11/01 ADD Kayo
FROM
    seko_hachu_info shi
WHERE
        shi.seko_no = :seko_no
    AND shi.hachu_no = :hachu_no
    AND shi.order_flg = :order_flg
    AND shi.delete_flg = 0
END_OF_SQL
				, array('seko_no' => $seko_no
                        , 'hachu_no' => $hachu_no
                        , 'order_flg' => $order_flg
                ));
		return $select;
	}

    /**
     * EDI伝票明細(edi_juchu_denpyo_msi)のDB項目データを更新する
     *
     * <AUTHOR> Mihara
     * @since      2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $hachu_den_no   発注伝票No
     * @param      int        $hachu_no       発注管理番号
     * @param      array      $data           更新データ項目   edi_status,hachu_cancel_note
     * @return     integer    更新行数
     */
    protected static function _updEdiMsiData($db, $hachu_den_no, $hachu_no, $data)
    {
        // edi_juchu_denpyo_msi.edi_status を 5:発注取消(7835:EDIステータス) に設定
        // edi_juchu_denpyo_msi.hachu_cancel_note を設定

        $edi_status        = $data['edi_status'];
        $hachu_cancel_note = $data['hachu_cancel_note'];

        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE edi_juchu_denpyo_msi
   SET edi_status = :edi_status
      ,hachu_cancel_note = :hachu_cancel_note
 WHERE delete_flg = 0
   AND (edi_juchu_den_no, edi_juchu_msi_no) IN 
          ( SELECT ejd.edi_juchu_den_no, msi.edi_juchu_msi_no
              FROM edi_juchu_denpyo_msi msi
             INNER JOIN edi_juchu_denpyo ejd
                ON ejd.edi_juchu_den_no = msi.edi_juchu_den_no
               AND ejd.delete_flg=0
             WHERE ejd.hachu_den_no = :hachu_den_no
               AND msi.hachu_no IS NOT DISTINCT FROM :hachu_no
               AND msi.delete_flg = 0 )
END_OF_SQL
                                 , array( 'edi_status'        => $edi_status, 
                                          'hachu_cancel_note' => $hachu_cancel_note,
                                          'hachu_den_no'      => $hachu_den_no,
                                          'hachu_no'          => $hachu_no ) );

        return $cnt;
    }

    /**
     * EDI伝票明細(edi_juchu_denpyo_msi)のDB項目データを更新する
     *
     * <AUTHOR> Mihara
     * @since      2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $hachu_den_no   発注伝票No
     * @param      int        $hachu_msi_no   発注明細No
     * @param      array      $data           更新データ項目   edi_status,hachu_cancel_note
     * @return     integer    更新行数
     */
    protected static function _updEdiMsiData2($db, $hachu_den_no, $hachu_msi_no, $data)
    {
        // edi_juchu_denpyo_msi.edi_status を 5:発注取消(7835:EDIステータス) に設定
        // edi_juchu_denpyo_msi.hachu_cancel_note を設定

        $edi_status        = $data['edi_status'];
        $hachu_cancel_note = $data['hachu_cancel_note'];

        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE edi_juchu_denpyo_msi
   SET edi_status = :edi_status
      ,hachu_cancel_note = :hachu_cancel_note
 WHERE delete_flg = 0
   AND edi_juchu_den_no IN 
          ( SELECT ejd.edi_juchu_den_no
              FROM edi_juchu_denpyo_msi msi
             INNER JOIN edi_juchu_denpyo ejd
                ON ejd.edi_juchu_den_no = msi.edi_juchu_den_no
               AND ejd.delete_flg=0
             WHERE ejd.hachu_den_no = :hachu_den_no
               AND msi.delete_flg = 0 )
  AND edi_juchu_msi_no = :hachu_msi_no    -- 発注伝票明細Noと同一
END_OF_SQL
                                     , array( 'edi_status'        => $edi_status, 
                                              'hachu_cancel_note' => $hachu_cancel_note,
                                              'hachu_den_no'      => $hachu_den_no,
                                              'hachu_msi_no'      => $hachu_msi_no ) );

        return $cnt;
    }

    /**
     * EDI伝票明細(edi_juchu_denpyo_msi)のDB項目データを 発注伝票明細(hachu_denpyo_msi)/施行発注情報(seko_hachu_info) へ反映させる
     *
     * <AUTHOR> Mihara
     * @since      2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $hachu_den_no   発注伝票No
     * @param      int        $hachu_no       発注管理番号
     * @param      array      $data           更新データ項目   edi_status,hachu_cancel_note
     * @return     void
     */
    protected static function _syncEdiMsiDataToHachu($db, $hachu_den_no, $hachu_no, $data)
    {
        if ( $hachu_no != null ) {
            $dataHachuMsi = array_merge( array('denpyo_no'=>$hachu_den_no, 'hachu_no'=>$hachu_no), $data );
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'hachu_denpyo_msi', $dataHachuMsi, array('denpyo_no', 'hachu_no') );
        } else {
            $dataHachuMsi = array_merge( array('denpyo_no'=>$hachu_den_no), $data );
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'hachu_denpyo_msi', $dataHachuMsi, array('denpyo_no') );
        }
        $cnt = $db->easyExecute($sql, $param);

        if ( $hachu_no != null ) {
            $dataSekoHachu = array_merge( array('hc_denpyo_no'=>$hachu_den_no, 'hachu_no'=>$hachu_no), $data );
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'seko_hachu_info', $dataSekoHachu, array('hc_denpyo_no', 'hachu_no') );
            $cnt = $db->easyExecute($sql, $param);
        }
    }

    /**
     * EDI伝票明細(edi_juchu_denpyo_msi)のDB項目データを 発注伝票明細(hachu_denpyo_msi)/施行発注情報(seko_hachu_info) へ反映させる
     *
     * <AUTHOR> Mihara
     * @since      2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $hachu_den_no   発注伝票No
     * @param      int        $hachu_msi_no   発注明細No
     * @param      int        $hachu_no       発注管理番号
     * @param      array      $data           更新データ項目   edi_status,hachu_cancel_note
     * @return     void
     */
    protected static function _syncEdiMsiDataToHachu2($db, $hachu_den_no, $hachu_msi_no, $hachu_no, $data)
    {
        $dataHachuMsi = array_merge( array('denpyo_no'=>$hachu_den_no, 'msi_no'=>$hachu_msi_no), $data );
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'hachu_denpyo_msi', $dataHachuMsi, array('denpyo_no', 'msi_no') );
        $cnt = $db->easyExecute($sql, $param);

        if ( $hachu_no != null ) {
            $dataSekoHachu = array_merge( array('hc_denpyo_no'=>$hachu_den_no, 'hachu_no'=>$hachu_no), $data );
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'seko_hachu_info', $dataSekoHachu, array('hc_denpyo_no', 'hachu_no') );
            $cnt = $db->easyExecute($sql, $param);
        }
    }

    /**
     * 施行発注管理情報ベースの発注伝票取消 処理
     * 対応するEDI伝票明細ほかを更新する
     *   called by application/modules/kanri/controllers/DispController.hakuzen.php
     *
     * <AUTHOR> Mihara
     * @since      2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $select_ex     取消する施行発注管理情報
     * @return     void
     */
    public static function hachuCancelFromShiToEdiEx($db, $select_ex)
    {
        foreach ( $select_ex as $rec ) {
            $hachu_den_no = $rec['hc_denpyo_no'];
            $hachu_no     = $rec['hachu_no'];

            // 発注取消コメント取得
            $recHachuMsi = $db->easySelOne( <<< END_OF_SQL
SELECT * 
  FROM hachu_denpyo_msi
 WHERE denpyo_no = :denpyo_no
   AND hachu_no	= :hachu_no  -- null はない(seko_hachu_info 経由なので)
--   AND delete_flg = 0
END_OF_SQL
                                            , array('denpyo_no' => $hachu_den_no, 'hachu_no' => $hachu_no) );
            if ( $recHachuMsi === null ) {
                $msg = sprintf("(b7625321)発注伝票明細(%s.%s)が存在しません", $hachu_den_no, $hachu_no);
                throw new Exception($msg);
            }
            $hachu_cancel_note = $recHachuMsi['hachu_cancel_note'];

            // EDI受注伝票明細更新
            $pData1 = array( 'edi_status'        => static::EDI_STATUS_CANCEL, // 5:発注取消(7835)
                             'hachu_cancel_note' => $hachu_cancel_note );
            $cnt = static::_updEdiMsiData($db, $hachu_den_no, $hachu_no, $pData1);

            // 発注伝票明細, 施行発注管理情報 を更新する
            if ( $cnt > 0 ) {
                $pData2 = array( 'edi_status'        => static::EDI_STATUS_CANCEL ); // 5:発注取消(7835)
                static::_syncEdiMsiDataToHachu($db, $hachu_den_no, $hachu_no, $pData2);
            }
        }
    }

    /**
     * @obsolete 発注伝票取消で対応するEDI伝票を取消する(明示的設定版)
     * (2021/01/05)発注伝票単位で渡されるが、削除(=取消)単位は明細.
     *   called by application/modules/hachu/controllers/HachushoritanpinController.hakuzen.php
     *
     * <AUTHOR> Mihara
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $hc_denpyo_nos   ex. array(<hachu_den_no_1>, <hachu_den_no_2>, ...)
     * @return     void
     */
    public static function hachudenCancelToEdi($db, $hc_denpyo_nos)
    {
        $curr_req_id = $db->getOneVal( "SELECT CURRVAL('x_req_id_seq')" ); // 本リクエストの req_id

        foreach ( $hc_denpyo_nos as $hachu_den_no ) {
            // 発注伝票明細の取得
            $hachuMsies = $db->easySelect( <<< END_OF_SQL
 SELECT *
   FROM hachu_denpyo_msi hmsi
  WHERE hmsi.denpyo_no = :hachu_den_no
    AND delete_flg=1
    AND _req_id = :curr_req_id
END_OF_SQL
                                           , array( 'hachu_den_no' => $hachu_den_no,
                                                    'curr_req_id'  => $curr_req_id ) );

            foreach ( $hachuMsies as $hachuMsiRec ) {
                $hachu_cancel_note = $hachuMsiRec['hachu_cancel_note'];
                $hachu_no          = $hachuMsiRec['hachu_no'];
                $hachu_msi_no      = $hachuMsiRec['msi_no'];

                // EDI受注伝票明細更新
                $pData1 = array( 'edi_status'        => static::EDI_STATUS_CANCEL, // 5:発注取消(7835)
                                 'hachu_cancel_note' => $hachu_cancel_note );
                $cnt = static::_updEdiMsiData2($db, $hachu_den_no, $hachu_msi_no, $pData1);

                if ( $cnt > 0 ) {
                    // 発注伝票明細の edi_status 更新
                    $pData2 = array( 'edi_status'        => static::EDI_STATUS_CANCEL ); // 5:発注取消(7835)
                    static::_syncEdiMsiDataToHachu2($db, $hachu_den_no, $hachu_msi_no, $hachu_no, $pData2);
                }
            }
        }
    }

    /**
     * 発注伝票取消で対応するEDI伝票を取消する(明示的設定版)
     * (2021/01/05)発注伝票単位で渡されるが、削除(=取消)単位は明細.
     *   called by application/modules/hachu/controllers/HachushoritanpinController.hakuzen.php
     *
     * <AUTHOR> Mihara
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $hc_denpyomsi_nos   ex. array(<hachu_den_no_1-msi_no_1>, <hachu_den_no_2-msi_no_2>, ...)
     * @return     void
     */
    public static function hachudenCancelToEdiMsi($db, $hc_denpyomsi_nos)
    {
        foreach ($hc_denpyomsi_nos as $denpyo_msino) {
            $hachu_den_no = preg_replace('/-.*$/', '', $denpyo_msino);
            $hachu_msi_no = preg_replace('/^.*-/', '', $denpyo_msino);

            // 発注伝票明細の取得
            $hachuMsiRec = $db->easySelOne( <<< END_OF_SQL
 SELECT *
   FROM hachu_denpyo_msi hmsi
  WHERE hmsi.denpyo_no = :hachu_den_no
    AND hmsi.msi_no = :hachu_msi_no
    AND delete_flg=1  
END_OF_SQL
                                           , array( 'hachu_den_no' => $hachu_den_no,
                                                    'hachu_msi_no' => $hachu_msi_no ) );
            if ( $hachuMsiRec === null ) {
                $msg = sprintf('取消対象の発注伝票明細(%s.%s)が存在しません', $hachu_den_no, $hachu_msi_no);
                throw new Exception($msg);
            }

            $hachu_cancel_note = $hachuMsiRec['hachu_cancel_note'];
            $hachu_no          = $hachuMsiRec['hachu_no'];

            // EDI受注伝票明細更新
            $pData1 = array( 'edi_status'        => static::EDI_STATUS_CANCEL, // 5:発注取消(7835)
                             'hachu_cancel_note' => $hachu_cancel_note );
            $cnt = static::_updEdiMsiData2($db, $hachu_den_no, $hachu_msi_no, $pData1);

            if ( $cnt > 0 ) {
                // 発注伝票明細の edi_status 更新
                $pData2 = array( 'edi_status'        => static::EDI_STATUS_CANCEL ); // 5:発注取消(7835)
                static::_syncEdiMsiDataToHachu2($db, $hachu_den_no, $hachu_msi_no, $hachu_no, $pData2);
            }
        }
    }

    /**
     * 発注伝票取消で対応するEDI伝票を取消する(明示的設定版, 発注伝票明細単位)
     *   called by application/models/Logic/HachuDenpyoTanpin.hakuzen.php
     *   発注(個別)=発注伝票入力 から呼ばれる処理で、施行発注情報(seko_hachu_info)は関係しない.
     *
     * <AUTHOR> Mihara
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $aHachuCancelForEdi   ex. array( array('denpyo_no'=><denpyo_no>, 'msi_no'=><msi_no>), ...)
     * @return     void
     */
    public static function hachuTanpinCancelToEdi($db, $aHachuCancelForEdi)
    {
        foreach ( $aHachuCancelForEdi as $rec ) {
            $hachu_den_no = $rec['denpyo_no'];
            $msi_no       = $rec['msi_no'];

            // 発注取消コメント, 取得
            $recHachuMsi = $db->easySelOne( <<< END_OF_SQL
SELECT * 
  FROM hachu_denpyo_msi
 WHERE denpyo_no = :denpyo_no
   AND msi_no = :msi_no
   AND delete_flg = 0
END_OF_SQL
                                            , array('denpyo_no' => $hachu_den_no, 'msi_no' => $msi_no) );
            if ( $recHachuMsi === null ) {
                $msg = sprintf("(1964725b)発注伝票明細(%s.%s)が存在しません", $hachu_den_no, $msi_no);
                throw new Exception($msg);
            }
            $hachu_cancel_note = $recHachuMsi['hachu_cancel_note'];
            $edi_status = static::EDI_STATUS_CANCEL; // 5:発注取消(7835)
            $pData1 = array( 'edi_status'        => $edi_status,
                             'hachu_cancel_note' => $hachu_cancel_note );

            // EDI受注伝票明細更新
            $cnt = static::_updEdiMsiData2($db, $hachu_den_no, $msi_no, $pData1);

            // 発注伝票明細 を更新する
            if ( $cnt > 0 ) {
                $dataHachuMsi = array( 'denpyo_no'  => $hachu_den_no,
                                       'msi_no'     => $msi_no,
                                       'edi_status' => $edi_status );
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL2( 'hachu_denpyo_msi', $dataHachuMsi, array('denpyo_no', 'msi_no') );
                $cnt = $db->easyExecute($sql, $param);

                // 施行発注情報 更新はなし
            }
        }
    }

    /**
     * アフター(単品) 発注取消処理
     *
     * <AUTHOR> Mihara
     * @since      2020/09/xx
     * @param      Msi_Sys_Db $db
	 * @param      string  $edi_juchu_den_no
	 * @param      integer $edi_juchu_msi_no
     * @return     void
     */
    public static function cancelHachuMsiTanpinFromEdi($db, $edi_juchu_den_no, $edi_juchu_msi_no)
    {
        // // cf. Hachu_HachushoritanpinController::cancelAction() in HachushoritanpinController.hakuzen.php ただし伝票単位
        // $hc_denpyo_nos = [ $hachu_no ];
        // // 受注明細の発注伝票番号・発注伝票明細番号を空に
        // DataMapper_HachuTanpin::resetHachuNosOfJuchuDenpyo($db, $hc_denpyo_nos);
        // // 発注伝票に削除フラグをたてる
        // DataMapper_HachuTanpin::deleteHachuDenpyo($db, $hc_denpyo_nos);

        $hachuMsiRec = $db->easySelOne(<<< END_OF_SQL
SELECT *
  FROM hachu_denpyo_msi  hdm
 WHERE EXISTS ( SELECT * FROM edi_juchu_denpyo_msi emsi
                   INNER JOIN edi_juchu_denpyo eden
                           ON eden.edi_juchu_den_no = emsi.edi_juchu_den_no
                          AND eden.delete_flg = 0
                        WHERE emsi.edi_juchu_den_no = :edi_juchu_den_no
                          AND emsi.edi_juchu_msi_no = :edi_juchu_msi_no
                          AND emsi.delete_flg = 0
                          AND eden.hachu_den_no = hdm.denpyo_no
                          AND emsi.edi_juchu_msi_no = hdm.msi_no
                          AND emsi.delete_flg = 0 )
END_OF_SQL
                                       , array('edi_juchu_den_no' => $edi_juchu_den_no,
                                               'edi_juchu_msi_no' => $edi_juchu_msi_no) );

        if ( $hachuMsiRec === null ) {
            $err = sprintf("%s-%s: EDI受注に対応する発注明細データが存在しません", $edi_juchu_den_no, $edi_juchu_msi_no);
            Msi_Sys_Utils::warn($err);
            throw new Msi_Sys_Exception_InputException( $err );
            return;
        }

        $hachu_denpyo_no = $hachuMsiRec['denpyo_no'];
        $hachu_msi_no    = $hachuMsiRec['msi_no'];

        Logic_WebEdi_HachuCancelLogic::cancelHachuMsiTanpin($db, $hachu_denpyo_no, $hachu_msi_no);
    }

    /**
     * アフター(単品) 発注取消処理
     *
     * <AUTHOR> Mihara
     * @since      2020/09/xx
     * @param      Msi_Sys_Db $db
	 * @param      string  $hachu_denpyo_no
	 * @param      integer $hachu_msi_no
     * @return     void
     */
    public static function cancelHachuMsiTanpin($db, $hachu_denpyo_no, $hachu_msi_no)
    {
        // #4202: [EDI]アフター発注時の発注先NGコメント、EDIステータス
        // 2021/02/02 mihara add -- begin
        $hachuMsiRec = $db->easySelOne( <<< END_OF_SQL
SELECT *
  FROM hachu_denpyo_msi  hdm
 WHERE denpyo_no=:hachu_denpyo_no
   AND msi_no=:hachu_msi_no
END_OF_SQL
                                       , array('hachu_denpyo_no' => $hachu_denpyo_no,
                                               'hachu_msi_no'    => $hachu_msi_no) );
        if ( $hachuMsiRec === null ) {
            $msg = sprintf('(f12798ca)取消対象の発注伝票明細(%s.%s)が存在しません', $hachu_denpyo_no, $hachu_msi_no);
            throw new Exception($msg);
        }
        $edi_status       = $hachuMsiRec['edi_status'];
        $hachu_ng_note    = $hachuMsiRec['hachu_ng_note'];
        // 2021/02/02 end --

        // 受注伝票明細に保持されている発注伝票番号(v_free1)と発注伝票明細番号(n_free5)を空にする   (2020/01/07 v_free5=>n_free5)
        // cf. DataMapper_HachuTanpin::resetHachuNosOfJuchuDenpyo($db, $hc_denpyo_nos); in HachushoritanpinController.hakuzen.php
        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE juchu_denpyo_msi
   SET v_free1 = NULL
      ,n_free5 = NULL
      ,k_free3=:edi_status         -- 2021/02/02 add
      ,v_free4=:hachu_ng_note      -- 2021/02/02 add
 WHERE delete_flg = 0
   AND v_free1 = :hachu_denpyo_no
   AND n_free5 = :hachu_msi_no
END_OF_SQL
                                , array( 'hachu_denpyo_no' => $hachu_denpyo_no,
                                         'hachu_msi_no'    => $hachu_msi_no,
                                         'edi_status'      => $edi_status,
                                         'hachu_ng_note'   => $hachu_ng_note ) );

        //発注伝票明細に削除フラグを立てる
        $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo_msi
   SET delete_flg = 1
 WHERE denpyo_no = :hachu_denpyo_no
  AND msi_no = :hachu_msi_no
END_OF_SQL
                                , array( 'hachu_denpyo_no' => $hachu_denpyo_no,
                                         'hachu_msi_no'    => $hachu_msi_no ) );


        // 発注伝票
        $selectGetHead = $db->easySelect(<<< END_OF_SQL
SELECT	hachu_prc_sum
	, hachu_nebk_sum
	, szei_katax_taisho_prc
	, uzei_katax_taisho_prc
	, hitax_katax_taisho_prc
	, out_zei_prc
	, in_zei_prc
FROM hachu_denpyo
WHERE denpyo_no		=	:hachu_denpyo_no
AND   delete_flg	=	0
END_OF_SQL
                                         , array('hachu_denpyo_no' => $hachu_denpyo_no));

        // 発注明細データ
        $selectGetMsi = $db->easySelect(<<< END_OF_SQL
SELECT    hachu_prc
		, nebiki_prc
		, zei_kbn
		, out_zei_prc
		, in_zei_prc
FROM hachu_denpyo_msi
WHERE	denpyo_no	=	:hachu_denpyo_no
AND		delete_flg	=	0
END_OF_SQL
                                        , array('hachu_denpyo_no' => $hachu_denpyo_no));

        $hachu_prc_sum = 0;
        $hachu_nebk_sum = 0;
        $szei_katax_taisho_prc = 0;
        $uzei_katax_taisho_prc = 0;
        $hitax_katax_taisho_prc = 0;
        $out_zei_prc = 0;
        $in_zei_prc = 0;
        foreach ($selectGetMsi as $msi_rec) {
            $hachu_prc_sum = $hachu_prc_sum + $msi_rec["hachu_prc"];
            $hachu_nebk_sum = $hachu_nebk_sum + $msi_rec["nebiki_prc"];

            if ($selectGetMsi[0]["zei_kbn"] == 0) {//非課税
                $hitax_katax_taisho_prc = $hitax_katax_taisho_prc + $msi_rec["hachu_prc"];
            } else if ($selectGetMsi[0]["zei_kbn"] == 1) {//内税
                $uzei_katax_taisho_prc = $uzei_katax_taisho_prc + $msi_rec["hachu_prc"];
            } else {//外税
                $szei_katax_taisho_prc = $szei_katax_taisho_prc + $msi_rec["hachu_prc"];
            }

            $out_zei_prc = $out_zei_prc + $msi_rec["out_zei_prc"];
            $in_zei_prc = $in_zei_prc + $msi_rec["in_zei_prc"];
        }

        $selectCnt = count($selectGetMsi);

        //発注伝票の更新
        //データが0件の場合、削除フラグを立てる
        if ($selectCnt == 0) {
            $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo
set		delete_flg = 1
WHERE	denpyo_no	=:hachu_denpyo_no
END_OF_SQL
                                    , array('hachu_denpyo_no' => $hachu_denpyo_no));
        } else {
            $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE hachu_denpyo set
 hachu_prc_sum			=:hachu_prc_sum
,hachu_nebk_sum			=:hachu_nebk_sum
,szei_katax_taisho_prc	=:szei_katax_taisho_prc
,uzei_katax_taisho_prc	=:uzei_katax_taisho_prc
,hitax_katax_taisho_prc	=:hitax_katax_taisho_prc
,out_zei_prc			=:out_zei_prc
,in_zei_prc				=:in_zei_prc
 WHERE denpyo_no		=:hachu_denpyo_no
END_OF_SQL
                                    , array('hachu_denpyo_no' => $hachu_denpyo_no
                                            , 'hachu_prc_sum' => $hachu_prc_sum
                                            , 'hachu_nebk_sum' => $hachu_nebk_sum
                                            , 'szei_katax_taisho_prc' => $szei_katax_taisho_prc
                                            , 'uzei_katax_taisho_prc' => $uzei_katax_taisho_prc
                                            , 'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc
                                            , 'out_zei_prc' => $out_zei_prc
                                            , 'in_zei_prc' => $in_zei_prc));
        }
        if ($cnt > 1) {
            $msg = '実行エラーが発生しました';
            throw new Exception($msg);
        }
    }

}
