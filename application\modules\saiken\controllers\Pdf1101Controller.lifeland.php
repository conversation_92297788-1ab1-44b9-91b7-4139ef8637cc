<?php

/**
 * PDF 請求書
 *
 * @category   App
 * @package    controller
 * <AUTHOR>
 * @since      2020/xx/xx
 * @filesource 
 */

/**
 * PDF 請求書
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Kobayashi
 * @since      2020/xx/xx
 */
class Saiken_Pdf1101Controller extends Zend_Controller_Action {

    // 帳票テンプレート
    private static $sourceFileName = array(
        'SOUFU' => 'pdf_tmpl/1101_01_S.pdf', // 送付案内
        'KAGAMI' => 'pdf_tmpl/1101_01_K.pdf', // 鏡
        'MEISAI1' => 'pdf_tmpl/1101_01_M1.pdf', // 明細(1枚目)
        'MEISAI2' => 'pdf_tmpl/1101_01_M2.pdf', // 明細(2枚目以降)
        'AFTER' => 'pdf_tmpl/1101_01_after.pdf', // アフター・供花(1枚目)
        'AFTER2' => 'pdf_tmpl/1101_01_after2.pdf', // アフター・供花(2枚目以降)
        'HAGAKI' => 'pdf_tmpl/1101_01_afterH.pdf', // アフター・供花(ハガキ)
    );

    // データ区分
    const KBN_SOUGI = '1'; // 葬儀
    const KBN_HOUJI = '2'; // 法事
    const KBN_AFTER = '3'; // アフター(単品)
    const KBN_KYOKA = '4'; // 供花供物(別注)
    const POST_KBN_YUSO = '0';  // 郵送区分: 0=>郵送
    const PTN_SEKOKAKUTEI = 'sekokakutei';
    const PTN_KAKUTEIOUTPUT = 'kakuteiout'; // 施行金額確定一覧画面：施行金額確定
    const PTN_KAKUTEICONFIRM = 'confirm';   // 施行金額確定一覧画面：請求書確認書出力
    const PTN_SHONINMSI = 'shoninmsi';      // 請求書承認一覧画面：確認用請求書出力

    // 軽減税率対応
    private static $_kijun_ymd;
    private static $_is_reduced_tax_rate_row_exists;
    private static $_p_seikyu_den_no;
    private static $_ptn = null;
    // 発行対象件数
    private static $_cnt = 0;

    /** バーコード関連項目 */

    /** バーコード項目①：AI */
    const BARCODE_ITEM1 = '91';

    /** バーコード項目②：国・商品メーカーコード */
    const BARCODE_ITEM2 = '908173';

    /** バーコード項目⑤：再発行区分 */
    const BARCODE_ITEM5 = '0';

    /** バーコード項目⑥：支払期限 */
    const BARCODE_ITEM6 = '999999';

    /** コード区分: 8554=>委託者番号 */
    const CODE_KBN_ITAKU = '8554';

    /** 印紙税区分: 1=>領収証 */
    const INSHI_ZEI_RYOSYU = '1';

    /** 請求伝票更新処理フラグ */
    private static $_update_flg = true;

    /** 印刷種別(print_sbt)内訳 1:請求書作成 2:月次請求 3:再発行 4:明細発行 */

    /**
     * アクション
     *
     * <AUTHOR> Kobayashi
     * @since  2020/12/xx
     */
    public function indexAction($paramsData = array()) {
        try {
            $db = Msi_Sys_DbManager::getMyDb();
            if (Msi_Sys_Utils::myCount($paramsData) > 0) {
                $params = $paramsData;
            } else {
                $params = Msi_Sys_Utils::webInputs();
            }
            // 再発行の時は請求先情報のチェックを行う
            if (isset($params['print_sbt']) && $params['print_sbt'] === '3') {
                self::checkSekyuInfo($db, $params);
            }
            // 発行日時
            $print_ymd = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i:s');
            // 発行担当者
            $tanto_nm = App_Utils::getTantoNm();
            if (isset($params['ptn']) && $params['ptn'] === static::PTN_KAKUTEIOUTPUT) { // 施行金額確定時のPDF出力処理
                self::$_update_flg = false;
                self::$_ptn = $params['ptn'];
                // データ取得
                $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
                $where = array('seko_no' => $dataApp['seko_no'], 'data_kbn' => $dataApp['data_kbn']);
                $Head = DataMapper_Pdf1101::findHead($db, $where);
                $data = $Head[0];
                // 軽減税率対応 mihara keigen   基準日の設定
                self::_prepKijunYmdKeigen($data['seko_no']);

                $title = '請求明細書' . $data['seikyu_den_no'] . '_' . str_replace(array('/', ' ', ':'), '', $print_ymd);
                if ($data['data_kbn'] == static::KBN_HOUJI) {
                    $pdfObj = self::outDataHouji($db, $data, $title, $print_ymd, $tanto_nm);
                } else {
                    $pdfObj = self::outDataSougi($db, $data, $title, $print_ymd, $tanto_nm);   
                }
                $pdfObj->download();
            } else if (isset($params['ptn']) && $params['ptn'] === static::PTN_KAKUTEICONFIRM) { // 施行金額確定画面の請求確認書時のPDF出力処理
                self::$_update_flg = false;
                self::$_ptn = $params['ptn'];
                // データ取得
                $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
                $where = array('seko_no' => $dataApp['seko_no'], 'data_kbn' => $dataApp['data_kbn']);
                $Head = DataMapper_Pdf1101::findHeadForTemp($db, $where);
                $data = $Head[0];
                // 軽減税率対応 mihara keigen   基準日の設定
                self::_prepKijunYmdKeigen($data['seko_no']);

                $title = '請求明細書' . $data['seikyu_den_no'] . '_' . str_replace(array('/', ' ', ':'), '', $print_ymd);
                if ($data['data_kbn'] == static::KBN_HOUJI) {
                    $pdfObj = self::outDataHouji($db, $data, $title, $print_ymd, $tanto_nm);
                } else {
                    $pdfObj = self::outDataSougi($db, $data, $title, $print_ymd, $tanto_nm);   
                }
                $pdfObj->download();
            } else if (isset($params['ptn']) && $params['ptn'] === static::PTN_SHONINMSI) {    // 請求承認画面の明細発行処理
                self::$_update_flg = false;
                self::$_ptn = $params['ptn'];
                // ファイル取得(PDF)
                $file_data = $this->outData($db, $print_ymd, $params);
                if (isset($file_data['err'])) {
                    throw new Exception($file_data['err']);
                }
                // 形式に従ってクライアントに返す
                if (Msi_Sys_Utils::myCount($file_data['pdf_file']) > 0) {
                    $outMsg = '';
                    $isOutMsgErr = false;
                    $files = $file_data['pdf_file'];
                    // ファイル名
                    $bdl_filename = $file_data['file_title'];
                    // 複数ファイル出力準備
                    $outdata = Msi_Sys_Utils::genBdlFileData($files, 'ryoshudbl');
                    Msi_Sys_Utils::out2wayPush($outdata, $bdl_filename, 'application/x-msi-bdl');
                    // 複数ファイル出力
                    Msi_Sys_Utils::out2wayFlush(array('outMsg' => $outMsg, 'isOutMsgErr' => $isOutMsgErr));
                }
            } else {
                // ファイル取得(PDF)
                $file_data = $this->outData($db, $print_ymd, $params);
                if (isset($file_data['err'])) {
                    throw new Exception($file_data['err']);
                }
                // 形式に従ってクライアントに返す
//                if (Msi_Sys_Utils::myCount($file_data['pdf_file']) > 0) {
                $outMsg = '';
                $isOutMsgErr = false;
                $files = $file_data['pdf_file'];
                // ファイル名
                $bdl_filename = $file_data['file_title'];
                // 複数ファイル出力準備
                $outdata = Msi_Sys_Utils::genBdlFileData($files, 'ryoshudbl');
                Msi_Sys_Utils::out2wayPush($outdata, $bdl_filename, 'application/x-msi-bdl');
                // 複数ファイル出力
                Msi_Sys_Utils::out2wayFlush(array('outMsg' => $outMsg, 'isOutMsgErr' => $isOutMsgErr));
            }
            // コミット
            $db->commit();

        } catch (Exception $e) {
            $err = $e->getMessage();
            if ($err) {
                $msg = $err;
            } else {
                $msg = '内部エラーです';
            }
            $rtnData = array(
                'status' => 'NG',
                'msg' => $msg,
            );
            Msi_Sys_Utils::outJson($rtnData);
            return;
        }
    }

    /**
     * 請求書データ取得
     * @param type $db
     * @param type $print_ymd
     * @param type $params
     * @return type
     */
    private function outData($db, $print_ymd, $params) {

        // データ取得条件
        if (!(isset($params['ptn']) && $params['ptn'] === static::PTN_SHONINMSI)) { // 請求書類一括発行
            $where['seikyu_approval_status'] = 1;               // 請求承認済
            $where['__raw_1'] = "T.seikyu_zan > 0";             // 請求残高あり
        }
        $where['__raw_2'] = "T.bun_gas_kbn_num IN (0, 2, 20)";  // 通常、分割先、合算先
        
        // 請求番号指定(個別印刷)
        if (isset($params['print_sbt']) && $params['print_sbt'] == 4 && isset($params['seikyu_no_arr'])) {    // 請求承認画面の明細発行処理
            self::$_update_flg = false;
            // 明細発行の場合は請求伝票番号のみが条件
            $where = array();   // 初期化
            $seikyu_no_arr = $params['seikyu_no_arr'];
            if (Msi_Sys_Utils::myCount($seikyu_no_arr) == 1) {
                $where['seikyu_den_no'] = $seikyu_no_arr[0];
            } else {
                $where['__raw_5'] = "T.seikyu_den_no IN ('" . implode("', '", $seikyu_no_arr) . "')";
            }
        } else if (isset($params['seikyu_no_arr'])) {
            $seikyu_no_arr = $params['seikyu_no_arr'];
            if (Msi_Sys_Utils::myCount($seikyu_no_arr) == 1) {
                $where['seikyu_den_no'] = $seikyu_no_arr[0];
            } else {
                $where['__raw_5'] = "T.seikyu_den_no IN ('" . implode("', '", $seikyu_no_arr) . "')";
            }
        }

        // データ取得
        $Head = DataMapper_Pdf1101::findHead($db, $where);
        if (Msi_Sys_Utils::myCount($Head) == 0) {
            return array('err' => '請求データがありません');
        }
        $seko_no = null;
        if(isset($Head['seko_no'])){
            $seko_no = $Head['seko_no'];
        }

        // 発行担当者
        $tanto_nm = App_Utils::getTantoNm();

        // 軽減税率対応 mihara keigen   基準日の設定
        self::_prepKijunYmdKeigen($seko_no);

        // 帳票タイトル用日付
        $target = array('/', ' ', ':');
        $print_ymd_title = str_replace($target, '', $print_ymd);

        $pdf_file = array(); // PDFファイル
            foreach ($Head as $data) {
                // -- 請求書明細書 --
                $title = '請求書' . $data['seikyu_den_no'] . '_' . $print_ymd_title;
                switch ($data['data_kbn']) {
                    case 1: // 葬儀
                        $pdf_file[] = $this->outDataSougi($db, $data, $title, $print_ymd, $tanto_nm);
                        break;
                    case 2: // 法事
                        $pdf_file[] = $this->outDataHouji($db, $data, $title, $print_ymd, $tanto_nm);
                        break;
                    case 3: // アフター
                    case 4: // 供花供物
                        $pdf_file[] = $this->outDataAfter($db, $data, $title, $print_ymd, $tanto_nm);
                        break;
                }
            }
        if (self::$_cnt == 0) { // 対象データなし
            return array('err' => '請求データがありません');
        }
        return array('file_title' => $print_ymd_title . '.bdl', 'pdf_file' => $pdf_file);
    }

    /**
     * 御請求書 (葬儀)
     * @param type $db          DB
     * @param array $data        ヘッダー情報
     * @param string $title       帳票タイトル
     * @param string $print_ymd   発行日
     * @param string $tanto_nm    発行担当者
     * @return array $files      PDFデータ
     */
    private function outDataSougi($db, &$data, $title, $print_ymd, $tanto_nm) {
        // 帳票作成START
        $pdfObj = new App_Pdf($title);
//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf1101::findKagami($db, array("seko_no" => $data['seko_no']));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $print_ymd;
            $recHead['print_tanto_nm'] = $tanto_nm;
        }

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        $bumon_logo = $db->readBlobCont($data['bumon_logo']); // 部門ロゴ
        self::$_p_seikyu_den_no = $data['seikyu_den_no']; // 軽減税率対応のためデータ設定
        
        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)) {
            
            // 鏡
            self::outDataKagami($pdfObj, $db, $data['seko_no'], $recHead, $data);
            // 明細書
            $sumArr = self::outDataDetail($pdfObj, $db, $data['seko_no'], $data['data_kbn'], $recHead, $data);
            
        } else { // 請求書一式発行
        
            // 送付状
            self::outDataSoufu($pdfObj, $recHead, $data);
            // 鏡
            self::outDataKagami($pdfObj, $db, $data['seko_no'], $recHead, $data);
            // 明細書
            $sumArr = self::outDataDetail($pdfObj, $db, $data['seko_no'], $data['data_kbn'], $recHead, $data);
//            // 振込依頼書
//            self::outDataIrai($pdfObj, $recHead, $data);
//            // 納品書
//            self::outDataNouhin($pdfObj, $recHead, $data);
            
        }

        // 請求額計算(入金分を引く)
        $seikyu_gaku = $sumArr['total'] - $data['nyukin_prc'];
        
        // ページ等を出力
        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)) {
            $add_page = 0;
        } else { // 請求書一式
            $add_page = 1;
        }
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            // -- 送付書 --
            if($page == $add_page){
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 380, 'y' => 670, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($data['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>384, 'y' => 711, 'width' => 500, 'height' => 11, 'font_size' => 7), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>384, 'y' => 720, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>384, 'y' => 711, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                }
            }
            // -- 鏡 --
            if($page == 1+$add_page){
                // 御請求金額
                $pdfObj->write_string(array('x' => 210, 'y' => 392, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), '￥'.number_format($seikyu_gaku).'－');
                // 内訳(大分類毎)
                $y1 = 448;
                $y2 = 668;
                $dai_total1 = 0;
                $dai_total2 = 0;
                foreach ($sumArr as $daibunrui_cd => $value) {
                    if(preg_match('/^([0-9]{4})$/', $daibunrui_cd)){ // キーが大分類(数字4桁)のみ対象
                        if($daibunrui_cd !== '0060' && $daibunrui_cd !== '0070'){ // 0060:立替費用, 0070:特別値引
                            $pdfObj->write_string(array('x' => 90, 'y' => $y1, 'width' => 85, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 大分類名
                            $pdfObj->write_num(array('x' => 185, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                            $y1 += 24.5;
                            $dai_total1 += $value['sum'];
                        } else {
                            $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 大分類名
                            $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                            $y2 += 24.5;
                            $dai_total2 += $value['sum'];
                        }
                    }
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 595, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $dai_total1); // 総計
                // 御請求金額
                $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '　　  小計'); // 小計
                $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $dai_total1+$dai_total2+$sumArr['tax']); // 小計
                if($sumArr['kaiin'] !== 0){ // 互助会利用合計
                    $y2 += 24.5;
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '互助会利用合計');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['kaiin']);
                }
                if ($data['uchikin_prc'] > 0 || $data['nyukin_prc'] > 0) { // お預り金額(内金+入金)
                    $y2 += 24.5;
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), 'お預り金額');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), ($data['uchikin_prc']+$data['nyukin_prc'])*-1); // 金額
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 765, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['total']-$data['nyukin_prc']); // 御請求金額
                // 部門情報(大)
                $pdfObj->write_image(array('x' => 348, 'y' => 708, 'width' => 220, 'height' => 65, 'font_size' => 12), $bumon_logo);
                if($data['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>353, 'y' => 784, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $data['bumon_number']);
                }
                $pdfObj->write_string(array('x' =>331, 'y' => 798, 'width' => 500, 'height' => 11, 'font_size' => 8), '※御請求書の人名用漢字はシステムの関係上いくつかの旧字体が');
                $pdfObj->write_string(array('x' =>339, 'y' => 808, 'width' => 500, 'height' => 11, 'font_size' => 8), '常用漢字表記となります。ご容赦下さい。');
            }
            // -- 明細 --
            if($page > 1+$add_page){
                $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 300, 'height' => 15, 'font_size' =>11), '請求番号：'.$data['seikyu_den_no'].'（受付番号：'.$data['seko_no'].'）'); // 請求番号
                if($sumArr['nebiki_flg'] <> 0){
                    if($page == 2+$add_page){
                        $pdfObj->write_string(array('x' =>309, 'y' => 119, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    } else {
                        $pdfObj->write_string(array('x' =>309, 'y' => 50, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    }
                }
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>390, 'y'=>742, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象、「内」は内税対象');
                }
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 420, 'y' => 760, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($data['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>424, 'y' => 810, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                }
                // ページ
                $disp_page = $page-1-$add_page;
                $disp_numPages = $numPages-1-$add_page;
                $pdfObj->write_string(array('x' => 45, 'y' => 800, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $disp_page . '/' . $disp_numPages);
            }
        }

        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM)) {
            return $pdfObj;
        } else {
            // ファイルの作成と連携システムへの保存
            $files = $this->makeDataFile($db, $title, $pdfObj, $data, $print_ymd);
            return $files;
        }
    }
    
    /**
     * 御請求書送付状
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     */
    private function outDataSoufu($pdfObj, $recHead, $data) {

        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['SOUFU']);

//        $date_format = "Y年n月j日";
//        $pdfObj->write_string(array('x' => 440, 'y' => 53, 'width' => 200, 'height' => 9, 'font_size' => 9), '発行日');
//        $pdfObj->write_date(array('x' => 430, 'y' => 53, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $data['d_free1'], $date_format);  // 請求書発行日
        
        $pdfObj->write_string(array('x' => 120, 'y' => 112, 'width' => 185, 'height' => 9, 'font_size' => 11), '〒' . $data['soufu_yubin_no']); // 請求書送付先郵便番号
        $pdfObj->write_string(array('x' => 120, 'y' => 127, 'width' => 185, 'height' => 9, 'font_size' => 11), $data['soufu_addr1']); // 請求書送付先　住所1
        $atena_y = 0;
        if(isset($data['soufu_addr2']) && $data['soufu_addr2'] !== ''){
            $pdfObj->write_string(array('x' => 120, 'y' => 139, 'width' => 185, 'height' => 9, 'font_size' => 11), $data['soufu_addr2']); // 請求書送付先　住所2
            $atena_y += 11;
        }
        if(isset($data['sekyu_soufu_nm2']) && isset($data['sekyu_soufu_nm2']) !== ''){
            $pdfObj->write_string(array('x' => 120, 'y' => 151 + $atena_y, 'width' => 185, 'height' => 9, 'font_size' => 11), $data['sekyu_soufu_nm']); // 請求書送付先　宛名1
            $pdfObj->write_string(array('x' => 120, 'y' => 163 + $atena_y, 'width' => 185, 'height' => 9, 'font_size' => 11), $data['sekyu_soufu_nm2'] . '　様'); // 請求書送付先　宛名2
        } else {
            $pdfObj->write_string(array('x' => 120, 'y' => 151 + $atena_y, 'width' => 185, 'height' => 9, 'font_size' => 11), $data['sekyu_soufu_nm'] . '　様'); // 請求書送付先　宛名1
        }
        
        // 文章
        $text = array();
        if ($data['data_kbn'] == static::KBN_SOUGI) { // 葬儀
            $text[] = '拝啓';
            $text[] = '先日のご葬儀の際は大変お疲れ様でございました。';
            $text[] = '早速ではございますが、本日下記書類をご送付させていただきます。';
            $text[] = '恐れ入りますがご査証いただきますよう、何卒よろしくお願い申し上げます。';
            $text[] = 'ご不明な点がございましたら、下記までご連絡をお願いいたします。';
            $text[] = '                                                 　　　　　　　　　　　　　　　　敬具';
        } else { // 法事
            $text[] = '拝啓';
            $text[] = '先日のご法要の際は大変お疲れ様でございました。';
            $text[] = '早速ではございますが、本日下記書類をご送付させていただきます。';
            $text[] = '恐れ入りますがご査証いただきますよう、何卒よろしくお願い申し上げます。';
            $text[] = 'ご不明な点がございましたら、下記までご連絡をお願いいたします。';
            $text[] = '                                                 　　　　　　　　　　　　　　　　敬具';
        }
        $text_y = 280;
        foreach ($text as $val) {
            $pdfObj->write_string(array('x' => 90, 'y' => $text_y, 'width' => 500, 'height' => 9, 'font_size' => 11), $val);
            $text_y += 16;
        }

        // 項目
        $item = array();
        if ($data['data_kbn'] == static::KBN_SOUGI) { // 葬儀
            $item[] = '御請求書';
        } else { // 法事
            $item[] = '御請求書';
        }
        $item_y = 483.5;
        foreach ($item as $val) {
            $pdfObj->write_string(array('x' => 70, 'y' => $item_y, 'width' => 500, 'height' => 9, 'font_size' => 12), $val);
            $pdfObj->write_string(array('x' => 330, 'y' => $item_y, 'width' => 500, 'height' => 9, 'font_size' => 12), '一式');
            $item_y += 30.5;
        }

        $pdfObj->write_string(array('x' => 380, 'y' => 750, 'width' => 500, 'height' => 9, 'font_size' =>11), '担当者：' . $recHead['print_tanto_nm']); // 担当者
    }

    /**
     * 御請求書鏡(葬儀)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     */
    private function outDataKagami($pdfObj, $db, $seko_no, $recHead, $data) {

        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['KAGAMI']);

        $date_format = "Y年n月j日";
        $date_format_time = "G時i分";

        // タイトル
        if(isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)){
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 書【確認用】');
        } else {
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 書');
        }

        $pdfObj->write_string(array('x' => 450, 'y' => 35, 'width' => 125, 'height' => 9, 'align' => 'R', 'font_size' => 9), '請求番号：'.$data['seikyu_den_no']); // 請求番号
        $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['issue_date'], $date_format);  // 請求書発行日
        $pdfObj->write_string(array('x' => 313, 'y' => 110, 'width' => 103, 'height' => 9, 'font_size' => 10), '見積担当 ' . $recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 313, 'y' => 121, 'width' => 103, 'height' => 9, 'font_size' => 10), '施行担当 ' . $recHead['seko_tanto']); // 施行担当
        // 施主
        $pdfObj->write_string(array('x' => 52, 'y' => 108, 'width' => 125, 'height' => 9, 'font_size' => 10), '施主');
        $pdfObj->write_string(array('x' => 80, 'y' => 108, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['sekyu_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 119, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['s_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 130, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 141, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['s_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['s_mobile_tel']); // 携帯電話
        // 喪主
        $pdfObj->write_string(array('x' => 52, 'y' => 185, 'width' => 125, 'height' => 9, 'font_size' => 10), '喪主');
        $pdfObj->write_string(array('x' => 80, 'y' => 185, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['m_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 196, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['mg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 206, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['mg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 217, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['mg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 235, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['mg_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 235, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['mg_m_tel']); // 携帯電話
        // 故人
        $pdfObj->write_string(array('x' => 52, 'y' => 262, 'width' => 125, 'height' => 9, 'font_size' => 10), '故人');
        $pdfObj->write_string(array('x' => 80, 'y' => 262, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['k_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 273, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['kg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 284, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['kg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 295, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['kg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 306, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['kg_tel']); // TEL
        $pdfObj->write_string(array('x' => 80, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご年齢：' . $recHead['k_nenrei_man']. '歳'); // 満年齢
        $pdfObj->write_string(array('x' => 150, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), '喪主とのご関係：' . $recHead['m_zoku_nm']); // 続柄
        $pdfObj->write_string(array('x' => 80, 'y' => 328, 'width' => 125, 'height' => 9, 'font_size' =>10), 'ご逝去日：'); // 死亡日ラベル
        $pdfObj->write_date(array('x' => 130, 'y' => 328, 'width' => 125, 'height' => 9, 'type' => 'time', 'font_size' => 10), $recHead['sibou_ymd'], $date_format);  // 死亡日
        // 施行日程
        $recNitei = DataMapper_SekoNiteiEx::find($db, array('seko_no' => $seko_no));
        $y = 127;
        $sikijyo_flg = 0;
        $kasoba_flg = 0;

        foreach ($recNitei as $value) {
            if (isset($value['nitei_ymd_ymd'])) {
                switch ($value['nitei_kbn']) {
//                    case 3: // 納棺
                    case 4: // 通夜
                    case 5: // 出棺
                    case 11: // 葬儀
                        // 日程区分名
                        if (isset($value['v_free4'])) {
                            $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['v_free4'] . '：');
                        } else {
                            $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['nitei_kbn_nm'] . '：');
                        }
                        $pdfObj->write_date(array('x' => 405, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_ymd_ymd'], $date_format);  // 日付
                        $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_s_time'], $date_format_time); // 開始時間
//                        if($value['nitei_kbn'] !== '5'){ // より（出棺以外）
//                            $pdfObj->write_string(array('x' => 540, 'y' => $y, 'width' => 100, 'height' => 9, 'font_size' => 11), 'より');
//                        }
//                        if (($value['nitei_kbn'] == '4' || $value['nitei_kbn'] == '11') && isset($value['nitei_e_time'])) { // 終了時間（通夜・葬儀のみ）
//                            $y += 12;
//                            $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 11), $value['nitei_e_time'], $date_format_time);
//                        }
//                        if ($value['nitei_kbn'] == '3' && isset($value['basho_kbn'])) { // 場所（納棺のみ）
//                            $y += 11;
//                            if ($value['basho_kbn'] === '0') {
//                                $basho_nm = '自宅';
//                            } else {
//                                $basho_nm = $value['basho_nm'];
//                            }
//                            $pdfObj->write_string(array('x' => 405, 'y' => $y, 'width' => 150, 'height' => 9, 'font_size' => 11), $basho_nm);
//                        }
                        if ($sikijyo_flg == 0 && isset($value['shiki_basho_nm'])) { // 式場
                            $sikijyo_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 221, 'width' => 82, 'height' => 9, 'font_size' => 10), '式場：');
                            $pdfObj->write_string(array('x' => 405, 'y' => 221, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 233, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_zip']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 245, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_addr1']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 257, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_addr2']);
                        }
                        if ($kasoba_flg == 0 && isset($value['kasoba_nm'])) { // 火葬場所
                            $kasoba_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 278, 'width' => 160, 'height' => 9, 'font_size' => 10), '火葬場所：');
                            $pdfObj->write_string(array('x' => 405, 'y' => 278, 'width' => 160, 'height' => 9, 'font_size' => 10), $value['kasoba_nm']);
                        }
                        break;
                    default:
                        break;
                }
            }
            $y += 12;
        }

        if (isset($recHead['todokede']) && $recHead['todokede'] !== '') { // 届出役所
            $pdfObj->write_string(array('x' => 325, 'y' => 290, 'width' => 160, 'height' => 9, 'font_size' => 11), '届出役所：');
            $pdfObj->write_string(array('x' => 405, 'y' => 290, 'width' => 160, 'height' => 9, 'font_size' => 11), $recHead['todokede']);
        }

        if(isset($recHead['seko_plan_nm'])){ // 利用コース
            $pdfObj->write_string(array('x'=>325, 'y'=>312, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['seko_plan_nm'].'　'.number_format($recHead['seko_prc']).'円');
            $pdfObj->write_string(array('x'=>325, 'y'=>324, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['plan_summary_nm']);
        }

        // 請求金額文
        $word1 = '葬儀費用として、';
        $word2 = '下記の通りご請求申し上げます。';
        $pdfObj->write_string(array('x' => 52, 'y' => 356, 'width' => 500, 'height' => 9, 'font_size' => 11), '故　' . $recHead['k_nm'] . '　様　' . $word1);
        $pdfObj->write_string(array('x' => 52, 'y' => 369, 'width' => 500, 'height' => 9, 'font_size' => 11), $word2);
        // 消費税
        $outzei = self::outZeiUchiwake1($pdfObj, 0, -65);
        $pdfObj->write_num(array('x' => 185, 'y' => 629, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $outzei); // 外税消費税
        // 互助会消費税
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no,"yoto_kbn" => '1', '__etc_orderby'=>array('yoto_kbn', 'kain_no',)));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            self::outGojoZeiUchiwake($pdfObj, $db, $kaiin_info);
        }
        // 振込口座情報
//        self::outBankInfo($pdfObj, $db, $data, 0, -45);
    }

    /**
     * 明細を出力
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     * @return array $sumArr   明細金額を集計した配列
     */
    private function outDataDetail($pdfObj, $db, $seko_no, $data_kbn, $recHead, $data) {

        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 146;
        static $meisai_row_height1 = 23.25;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 77;
        static $meisai_row_height2 = 23.25;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $dai_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        $sumArr['nebiki_flg'] = 0;

        if (isset(self::$_ptn) && self::$_ptn === static::PTN_KAKUTEICONFIRM) {
            $recMsi = DataMapper_Pdf1101::findMsiForTemp($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "__raw_1" => "T2.kashidashi_kbn <> 2"));
        } else {
            $recMsi = DataMapper_Pdf1101::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "__raw_1" => "T2.kashidashi_kbn <> 2"));
        }
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if($total_tokuten == 0){
                        $total_tokuten = '';
                    } else {
                        $sumArr['nebiki_flg'] ++;
                    }
                    // 合計
                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['dai_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['uri_prc'] + $nebiki_prc; // 請求金額
            $gojokai_nebiki_prc = '';
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0){
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc'];
            }

            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if($value['dai_bunrui_cd'] === '0080'){ // 供花供物の場合は名札を表示
                $biko1 .= $value['nafuda_nm'];
                if($value['nafuda_nm2'] !== ''){
                    $biko1 .= '/'.$value['nafuda_nm2'];
                }
                $biko2 .= $value['nafuda_nm3'];
                if($value['nafuda_nm4'] !== ''){
                    $biko2 .= '/'.$value['nafuda_nm4'];
                }
            } else {
                if (isset($value['plan_shohin_nm'])) {
                    $biko1 .= $value['plan_shohin_nm'] . 'から変更';
                }
                if ($value['hoshi_disp'] !== '') {
                    $biko2 .= $value['hoshi_disp'] . '　';
                }
                $biko2 .= $value['shohin_tkiyo_nm'];
            }

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['uri_tnk'], $value['juchu_suryo'], $value['uri_prc'], $gojokai_nebiki_prc, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp'], null);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['uri_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['uri_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['uri_prc'];
                }
            }

            $total_ippan += $value['uri_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if($total_tokuten == 0){
                $total_tokuten = '';
            } else {
                $sumArr['nebiki_flg'] ++;
            }
            // 合計
            $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
        $tax_info = self::outZeiUchiwake2($row_arr); // 内訳
        $sumArr['tax'] = $tax_info['tax_total'];
        $total_prc_all = $tax_info['total']; // UPD 2025/05/23 消費税内訳から合計金額上書きに変更
        $out_tax_total = 0;
        if (Msi_Sys_Utils::myCount($taxArr) > 0) { // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
        }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $total_gojokai_nebiki_prc = $total_prc_all-$zei_nebikimae_total;
        if($total_gojokai_nebiki_prc == 0){
            $total_gojokai_nebiki_prc = '';
        }
        $row_arr[] = array('【合　計】', null, null, null, null, $zei_nebikimae_total, $total_gojokai_nebiki_prc, null, null, null, null, null, $total_prc_all); // 合計
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no
                , '__etc_orderby_raw'=>"T.yoto_kbn, CASE WHEN ( T.point=0 and T.riyoken=0 ) THEN 1 WHEN ( T.point>0 ) THEN 2 WHEN ( T.riyoken>0 ) THEN 3 ELSE 4 END, T.kain_no"));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        $sumArr['kaiin'] = $kaiin_total;
        

        // 内金
        $uchikin_prc = 0;
        if ($data['uchikin_prc'] > 0) {
            $row_arr[] = array('【内金金額】', null, null, null, null, null, null, $data['uchikin_prc'] * -1, null, null, null, null, null);
            $uchikin_prc = $data['uchikin_prc'] * -1;
        }

        // 御請求金額
        $mitsumori_prc = $total_prc_all + $kaiin_total + $uchikin_prc;
        $row_arr[] = array('【御請求金額】', null, null, null, null, null, null, $mitsumori_prc, null, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, '以下余白', null, null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName['MEISAI1']);
        // ヘッダー
        if(isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)){
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 明 細 書【確認用】');
        } else {
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 明 細 書');
        }
        self::outHeader($pdfObj, $recHead, $data);
//        self::outPlanSummaryNm($pdfObj, $recHead);
// 2022/10/21 削除        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName['MEISAI2']);
        
        return $sumArr;
    }
    
    /**
     * 御請求書 (法事)
     * @param type $db          DB
     * @param array $data        ヘッダー情報
     * @param string $title       帳票タイトル
     * @param string $print_ymd   発行日
     * @param string $tanto_nm    発行担当者
     * @return array $files      PDFデータ
     */
    private function outDataHouji($db, $data, $title, $print_ymd, $tanto_nm) {
        // 帳票作成START
        $pdfObj = new App_Pdf($title);
//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf1101::findKagami($db, array("seko_no" => $data['seko_no']));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $print_ymd;
            $recHead['print_tanto_nm'] = $tanto_nm;
        }

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        $bumon_logo = $db->readBlobCont($data['bumon_logo']); // 部門ロゴ
        self::$_p_seikyu_den_no = $data['seikyu_den_no']; // 軽減税率対応のためデータ設定
        
        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)) {
            
            // 鏡
            self::outDataKagami_houji($pdfObj, $db, $data['seko_no'], $recHead, $data);
            // 明細書
            $sumArr = self::outDataDetail_houji($pdfObj, $db, $data['seko_no'], $data['data_kbn'], $recHead, $data);
        
        } else { // 請求書一式発行
        
            // 送付状
            self::outDataSoufu($pdfObj, $recHead, $data);
            // 鏡
            self::outDataKagami_houji($pdfObj, $db, $data['seko_no'], $recHead, $data);
            // 明細書
            $sumArr = self::outDataDetail_houji($pdfObj, $db, $data['seko_no'], $data['data_kbn'], $recHead, $data);
//            // 振込依頼書
//            self::outDataIrai($pdfObj, $recHead, $data);
//            // 納品書
//            self::outDataNouhin($pdfObj, $recHead, $data);
            
        }

        // 請求額計算(入金分を引く)
        $seikyu_gaku = $sumArr['total'] - $data['nyukin_prc'];

        // ページ等を出力
        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)) {
            $add_page = 0;
        } else { // 請求書一式
            $add_page = 1;
        }
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            // -- 送付書 --
            if($page == $add_page){
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 380, 'y' => 670, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($data['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>384, 'y' => 711, 'width' => 500, 'height' => 11, 'font_size' => 7), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>384, 'y' => 720, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>384, 'y' => 711, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                }
            }
            // -- 鏡 --
            if($page == 1+$add_page){
                // 御請求金額
                $pdfObj->write_string(array('x' => 210, 'y' => 392, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), '￥'.number_format($seikyu_gaku).'－');
                // 内訳(中分類毎)
                $y1 = 448;
                $y2 = 668;
                $chu_total1 = 0;
                $chu_total2 = 0;
                $chubun1 = array('法要費用'=>0, '法要返礼品'=>0, '法要会食'=>0);
                foreach ($sumArr as $chubunrui_cd => $value) {
                    if(preg_match('/^([0-9]{4})$/', $chubunrui_cd)){ // キーが中分類(数字4桁)のみ対象
                        if($chubunrui_cd !== 1130 && $chubunrui_cd !== 1150){ // 1130:法要立替金, 1150:法要値引き
                            if($chubunrui_cd == 1090){ // 1090:法要返礼品
                                $chubun1['法要返礼品'] += $value['sum'];
                            } else if($chubunrui_cd == 1120){ // 1120:法要会食
                                $chubun1['法要会食'] += $value['sum'];
                            } else {
                                $chubun1['法要費用'] += $value['sum'];
                            }
                        } else {
                            if($chubunrui_cd == 1130){
                                $value['name'] = '立替費用';
                            }
                            if($chubunrui_cd == 1150){
                                $value['name'] = '特別値引';
                            }
                            $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 中分類名
                            $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                            $y2 += 24.5;
                            $chu_total2 += $value['sum'];
                        }
                    }
                }
                foreach ($chubun1 as $name => $prc) {
                    if($prc <> 0){
                        $pdfObj->write_string(array('x' => 90, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $name); // 中分類名
                        $pdfObj->write_num(array('x' => 185, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $prc); // 金額
                        $y1 += 24.5;
                        $chu_total1 += $prc;
                    }
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 595, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $chu_total1); // 総計
                // 御請求金額
                $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '　　  小計'); // 小計
                $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $chu_total1+$chu_total2+$sumArr['tax']); // 小計
                if($sumArr['kaiin'] !== 0){ // 互助会利用合計
                    $y2 += 24.5;
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '互助会利用合計');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['kaiin']);
                }
                if ($data['uchikin_prc'] > 0 || $data['nyukin_prc'] > 0) { // お預り金額(内金+入金)
                    $y2 += 24.5;
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), 'お預り金額');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), ($data['uchikin_prc']+$data['nyukin_prc'])*-1); // 金額
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 765, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['total']-$data['nyukin_prc']); // 御請求金額
                // 部門情報(大)
                $pdfObj->write_image(array('x' => 348, 'y' => 708, 'width' => 220, 'height' => 65, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>353, 'y' => 784, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $data['bumon_number']);
                }
                $pdfObj->write_string(array('x' =>331, 'y' => 798, 'width' => 500, 'height' => 11, 'font_size' => 8), '※御請求書の人名用漢字はシステムの関係上いくつかの旧字体が');
                $pdfObj->write_string(array('x' =>339, 'y' => 808, 'width' => 500, 'height' => 11, 'font_size' => 8), '常用漢字表記となります。ご容赦下さい。');
            }
            // -- 明細 --
            if($page > 1+$add_page){
                $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 250, 'height' => 15, 'font_size' =>11), '請求番号：'.$data['seikyu_den_no'].'（受付番号：'.$data['seko_no'].'）'); // 請求番号
                if($sumArr['nebiki_flg'] <> 0){
                    if($page == 2+$add_page){
                        $pdfObj->write_string(array('x' =>309, 'y' => 119, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    } else {
                        $pdfObj->write_string(array('x' =>309, 'y' => 50, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    }
                }
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>390, 'y'=>742, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象、「内」は内税対象');
                }
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 420, 'y' => 760, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), $data['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>424, 'y' => 810, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $data['bumon_number']);
                }
                // ページ
                $disp_page = $page-1-$add_page;
                $disp_numPages = $numPages-1-$add_page;
                $pdfObj->write_string(array('x' => 45, 'y' => 800, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $disp_page . '/' . $disp_numPages);
            }
        }

        if (isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM)) {
            return $pdfObj;
        } else {
            // ファイルの作成と連携システムへの保存
            $files = $this->makeDataFile($db, $title, $pdfObj, $data, $print_ymd);
            return $files;
        }

    }
    
    /**
     * 御請求書鏡(法事)
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     */
    private function outDataKagami_houji($pdfObj, $db, $seko_no, $recHead, $data) {

        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName['KAGAMI']);

        $date_format = "Y年n月j日";
        $date_format_time = "G時i分";

        // タイトル
        if(isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)){
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 書【確認用】');
        } else {
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 書');
        }

        $pdfObj->write_string(array('x' => 450, 'y' => 35, 'width' => 125, 'height' => 9, 'align' => 'R', 'font_size' => 9), '請求番号：'.$data['seikyu_den_no']); // 請求番号
        $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['issue_date'], $date_format);  // 請求書発行日
        $pdfObj->write_string(array('x' => 318, 'y' => 110, 'width' => 107, 'height' => 9, 'font_size' => 10), '見積担当 ' . $recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 318, 'y' => 121, 'width' => 107, 'height' => 9, 'font_size' => 10), '施行担当 ' . $recHead['seko_tanto']); // 施行担当
        // 施主
        $pdfObj->write_string(array('x' => 52, 'y' => 108, 'width' => 125, 'height' => 9, 'font_size' => 10), '施主');
        $pdfObj->write_string(array('x' => 80, 'y' => 108, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['sekyu_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 119, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['s_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 130, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 141, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['s_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['s_mobile_tel']); // 携帯電話
        // 故人
        $pdfObj->write_string(array('x' => 52, 'y' => 262, 'width' => 125, 'height' => 9, 'font_size' => 10), '故人');
        $pdfObj->write_string(array('x' => 80, 'y' => 262, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['hk_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 273, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['hkg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 284, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['hkg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 295, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['hkg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 306, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['hkg_tel']); // TEL
        $pdfObj->write_string(array('x' => 80, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご年齢：' . $recHead['hk_nenrei_man']. '歳'); // 満年齢
        $pdfObj->write_string(array('x' => 80, 'y' => 328, 'width' => 125, 'height' => 9, 'font_size' =>10), 'ご逝去日：'); // 死亡日ラベル
        $pdfObj->write_date(array('x' => 130, 'y' => 328, 'width' => 125, 'height' => 9, 'type' => 'time', 'font_size' => 10), $recHead['hk_death_ymd'], $date_format);  // 死亡日
        // 施行日程
        $recNitei = DataMapper_SekoNiteiHouji::find($db, array('seko_no' => $seko_no));
        $y = 127;
        $sikijyo_flg = 0;

        foreach ($recNitei as $value) {
            if (isset($value['nitei_ymd'])) {
                switch ($value['nitei_kbn']) {
                    case 1: // ご法要
                    case 3: // ご法宴
                    case 5: // 納骨
                        // 日程区分名
                        $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['nitei_kbn_nm'] . '：');
                        $pdfObj->write_date(array('x' => 405, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_ymd'], $date_format);  // 日付
                        $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_time'], $date_format_time); // 開始時間
//                        $pdfObj->write_string(array('x' => 540, 'y' => $y, 'width' => 100, 'height' => 9, 'font_size' => 11), 'より');
//                        if (($value['nitei_kbn'] == '1' || $value['nitei_kbn'] == '3') && isset($value['nitei_ed_time'])) { // 終了時間（ご法要・ご法宴のみ）
//                            $y += 12;
//                            $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 11), $value['nitei_ed_time'], $date_format_time);
//                        }
                        if ($value['nitei_kbn'] == '5') { // 場所（納骨のみ）
                            $y += 12;
                            $pdfObj->write_string(array('x' => 405, 'y' => $y, 'width' => 150, 'height' => 9, 'font_size' => 10), $value['basho_nm']);
                        }
                        if ($sikijyo_flg == 0 && isset($recHead['h_shikijyo'])) { // 式場
                            $sikijyo_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 241, 'width' => 82, 'height' => 9, 'font_size' => 10), '式場：');
                            $pdfObj->write_string(array('x' => 400, 'y' => 241, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 253, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_zip']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 265, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_addr1']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 277, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_addr2']);
                        }
                        break;
                    default:
                        break;
                }
            }
            $y += 12;
        }

        if(isset($recHead['seko_plan_nm'])){ // 利用コース
            $pdfObj->write_string(array('x'=>325, 'y'=>312, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['seko_plan_nm'].'　'.number_format($recHead['seko_prc']).'円');
            $pdfObj->write_string(array('x'=>325, 'y'=>324, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['plan_summary_nm']);
        }

        // 請求金額文
        $word1 = '法要費用として、';
        $word2 = '下記の通りご請求申し上げます。';
        $pdfObj->write_string(array('x' => 52, 'y' => 356, 'width' => 500, 'height' => 9, 'font_size' => 11), '故　' . $recHead['hk_nm'] . '　様　' . $word1);
        $pdfObj->write_string(array('x' => 52, 'y' => 369, 'width' => 500, 'height' => 9, 'font_size' => 11), $word2);
        // 消費税
        $outzei = self::outZeiUchiwake1($pdfObj, 0, -65);
        $pdfObj->write_num(array('x' => 185, 'y' => 629, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $outzei); // 外税消費税
        // 互助会消費税
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no,"yoto_kbn" => '1', '__etc_orderby'=>array('yoto_kbn', 'kain_no',)));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            self::outGojoZeiUchiwake($pdfObj, $db, $kaiin_info);
        }
        // 振込口座情報
//        self::outBankInfo($pdfObj, $db, $data, 0, -45);
    }

    /**
     * 明細を出力（法事）
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     * @return array $sumArr   明細金額を集計した配列
     */
    private function outDataDetail_houji($pdfObj, $db, $seko_no, $data_kbn, $recHead, $data) {

        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 146;
        static $meisai_row_height1 = 23.25;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 77;
        static $meisai_row_height2 = 23.25;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $chu_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        $sumArr['nebiki_flg'] = 0;

        if (isset(self::$_ptn) && self::$_ptn === static::PTN_KAKUTEICONFIRM) {
            $recMsi = DataMapper_Pdf1101::findMsiForTemp($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "__raw_1" => "T2.kashidashi_kbn <> 2"));
        } else {
            $recMsi = DataMapper_Pdf1101::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "__raw_1" => "T2.kashidashi_kbn <> 2"));
        }

        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['chu_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if($total_tokuten == 0){
                        $total_tokuten = '';
                    } else {
                        $sumArr['nebiki_flg'] ++;
                    }
                    // 合計
                    $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, $total, null, null, null, null, null);
                    $sumArr[$breakKey]['name'] = $chu_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['chu_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['uri_prc'] + $nebiki_prc; // 請求金額
            $gojokai_nebiki_prc = '';
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0){
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']+ $value['nebiki_prc'];
            }

            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if($value['dai_bunrui_cd'] === '0080'){ // 供花供物の場合は名札を表示
                $biko1 .= $value['nafuda_nm'];
                if($value['nafuda_nm2'] !== ''){
                    $biko1 .= '/'.$value['nafuda_nm2'];
                }
                $biko2 .= $value['nafuda_nm3'];
                if($value['nafuda_nm4'] !== ''){
                    $biko2 .= '/'.$value['nafuda_nm4'];
                }
            } else {
                if (isset($value['plan_shohin_nm'])) {
                    $biko1 .= $value['plan_shohin_nm'] . 'から変更';
                }
                if ($value['hoshi_disp'] !== '') {
                    $biko2 .= $value['hoshi_disp'] . '　';
                }
                $biko2 .= $value['shohin_tkiyo_nm'];
            }

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['uri_tnk'], $value['juchu_suryo'], $value['uri_prc'], $gojokai_nebiki_prc, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp'], null);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['uri_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['uri_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['uri_prc'];
                }
            }

            $total_ippan += $value['uri_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if($total_tokuten == 0){
                $total_tokuten = '';
            } else {
                $sumArr['nebiki_flg'] ++;
            }
            // 合計
            $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, $total, null, null, null, null, null);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['chu_bunrui_cd']]['name'] = $rec['chu_bunrui_nm'];
            $sumArr[$rec['chu_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['chu_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['chu_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['chu_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
        $tax_info = self::outZeiUchiwake2($row_arr); // 内訳
        $sumArr['tax'] = $tax_info['tax_total'];
        $total_prc_all = $tax_info['total']; // UPD 2025/05/23 消費税内訳から合計金額上書きに変更
        $out_tax_total = 0;
        if (Msi_Sys_Utils::myCount($taxArr) > 0) { // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
        }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $total_gojokai_nebiki_prc = $total_prc_all-$zei_nebikimae_total;
        if($total_gojokai_nebiki_prc == 0){
            $total_gojokai_nebiki_prc = '';
        }
        $row_arr[] = array('【合　計】', null, null, null, null, $zei_nebikimae_total, $total_gojokai_nebiki_prc, $total_prc_all, null, null, null, null, null); // 合計
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no
                , '__etc_orderby_raw'=>"T.yoto_kbn, CASE WHEN ( T.point=0 and T.riyoken=0 ) THEN 1 WHEN ( T.point>0 ) THEN 2 WHEN ( T.riyoken>0 ) THEN 3 ELSE 4 END, T.kain_no"));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        $sumArr['kaiin'] = $kaiin_total;

        // 内金
        $uchikin_prc = 0;
        if ($data['uchikin_prc'] > 0) {
            $row_arr[] = array('【内金金額】', null, null, null, null, null, null, $data['uchikin_prc'] * -1, null, null, null, null, null);
            $uchikin_prc = $data['uchikin_prc'] * -1;
        }

        // 御請求金額
        $mitsumori_prc = $total_prc_all + $kaiin_total + $uchikin_prc;
        $row_arr[] = array('【御請求金額】', null, null, null, null, null, null, $mitsumori_prc, null, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, '以下余白', null, null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName['MEISAI1']);
        // ヘッダー
        if(isset(self::$_ptn) && (self::$_ptn === static::PTN_KAKUTEIOUTPUT || self::$_ptn === static::PTN_KAKUTEICONFIRM || self::$_ptn === static::PTN_SHONINMSI)){
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 明 細 書【確認用】');
        } else {
            $pdfObj->write_string(array('x' => 150, 'y' => 55, 'width' => 320, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 請 求 明 細 書');
        }
        self::outHeader_houji($pdfObj, $recHead, $data);
//        self::outPlanSummaryNm($pdfObj, $recHead);
// 2022/10/21 削除        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName['MEISAI2']);
        
        return $sumArr;
    }

    /**
     * 御請求書（アフター・A4）
     * @param type $db           DB
     * @param array $data        ヘッダー情報
     * @param string $title      帳票タイトル
     * @param string $print_ymd  発行日
     * @param string $tanto_nm   発行担当者
     * @return array $files      PDFデータ
     */
    private function outDataAfter($db, &$data, $title, $print_ymd, $tanto_nm) {
        // 帳票作成START
        $pdfObj = new App_Pdf($title);
//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数

        $recHeadInfo = DataMapper_SeikyuDenpyo::find($db, array("seikyu_den_no" => $data['seikyu_den_no']));
        if (count($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $print_ymd;
            $recHead['print_tanto_nm'] = $tanto_nm;
        }
        // 請求請求先情報から請求先名を設定する
        $seikyuSekyuInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array("seikyu_den_no" => $data['seikyu_den_no']));
        if(!isset($recHead['sekyu_nm'])){
            if (isset($seikyuSekyuInfo[0]['sekyu_nm2'])) {
                $recHead['sekyu_nm'] = $seikyuSekyuInfo[0]['sekyu_nm1'].'　'.$seikyuSekyuInfo[0]['sekyu_nm2'];
            } else {
                $recHead['sekyu_nm'] = $seikyuSekyuInfo[0]['sekyu_nm1'];
            }
        }
        $bumon_logo = $db->readBlobCont($data['bumon_logo']); // 部門ロゴ
        self::$_p_seikyu_den_no = $data['seikyu_den_no']; // 軽減税率対応のためデータ設定

        // 明細出力
        $total_prc_all = self::outDataDetail_after($pdfObj, $db, $data);

        // 消費税内訳
        self::outZeiUchiwake1($pdfObj, -10, 20);

        // 請求残額計算
        $seikyu_gaku = $total_prc_all - $data['uchikin_prc'] - $data['nyukin_prc'];

        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            
            if($page === 1){ // 明細書1ページ目のみ表示
                // 窓あき封筒宛名
                $pdfObj->write_string(array('x' => 85, 'y' => 34, 'width' => 222, 'height' => 9, 'font_size' => 10), '〒'.$seikyuSekyuInfo[0]['soufu_yubin_no']); // 郵便番号
                $pdfObj->write_string(array('x' => 85, 'y' => 46, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['soufu_addr1']); // 住所1
                $pdfObj->write_string(array('x' => 85, 'y' => 58, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['soufu_addr2']); // 住所2
                $pdfObj->write_string(array('x' => 85, 'y' => 80, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['sekyu_soufu_nm'].'　'.$seikyuSekyuInfo[0]['v_free2'].'様'); // 請求送付先名
                
                $pdfObj->write_string(array('x' => 28, 'y' => 129, 'width' => 210, 'height' => 9, 'font_size' => 13), $recHead['sekyu_nm'] . '　様'); // 請求先名
                $pdfObj->write_line(array('x1' => 25, 'y1' => 147, 'x2' => 243, 'y2' => 147, 'width' => 1));
                
                // ご請求金額
                $pdfObj->write_string(array('x' => 90, 'y' => 208.5, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), '￥' . number_format($seikyu_gaku) . '－');
                
                // 葬家名
                if($recHead['data_kbn'] === '4'){
                    $sekoKihonInfo = DataMapper_SekoKihonInfo::find($db, array("seko_no" => $data['seko_no']));
                    $pdfObj->write_string(array('x' => 320, 'y' => 208.5, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), $sekoKihonInfo[0]['souke_nm']);
                }

                // 振込口座情報
                self::outBankInfoAfter($pdfObj, $db, $data, -300, 170);
                
                // 部門情報
                $pdfObj->write_image(array('x' => 348, 'y' => 720, 'width' => 220, 'height' => 65, 'font_size' => 12), $bumon_logo);
                $pdfObj->write_string(array('x' =>353, 'y' => 790, 'width' => 500, 'height' => 11, 'font_size' => 9), $data['bumon_kaisya_nm']);
                $pdfObj->write_string(array('x' =>353, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $data['bumon_number']);
                $pdfObj->write_string(array('x' =>331, 'y' => 814, 'width' => 500, 'height' => 11, 'font_size' => 8), '※御請求書の人名用漢字はシステムの関係上いくつかの旧字体が');
                $pdfObj->write_string(array('x' =>339, 'y' => 824, 'width' => 500, 'height' => 11, 'font_size' => 8), '常用漢字表記となります。ご容赦下さい。');
                $y = 58;
            } else {
                $pdfObj->write_string(array('x' => 28, 'y' => 54, 'width' => 223, 'height' => 9, 'font_size' => 13), $recHead['sekyu_nm'] . '　様'); // 請求先名
                $y = 0;
            }
            
            // 全ページに表示
            if(isset(self::$_ptn) && (self::$_ptn === static::PTN_SHONINMSI)){
                $pdfObj->write_string(array('x'=>28, 'y'=>89+$y, 'width'=>545, 'height'=>9, 'font_size' => 20, 'align' => 'C'), '御 請 求 書【確認用】');
            } else {
                $pdfObj->write_string(array('x'=>28, 'y'=>89+$y, 'width'=>545, 'height'=>9, 'font_size' => 20, 'align' => 'C'), '御 請 求 書');
            }
            $pdfObj->write_string(array('x'=>28, 'y'=>111+$y, 'width'=>550, 'height'=>9, 'font_size' =>11), $recHead['denpyo_biko1']);
            $pdfObj->write_string(array('x'=>28, 'y'=>123+$y, 'width'=>550, 'height'=>9, 'font_size' =>11), $recHead['denpyo_biko2']);
            $pdfObj->write_string(array('x'=>28, 'y'=>135+$y, 'width'=>220, 'height'=>9, 'font_size' =>11), '下記の通り御請求申し上げます。');
            $pdfObj->write_string(array('x' => 460, 'y' => 25, 'width' => 120, 'height' => 9, 'font_size' => 8.9, 'align' => 'R'), '請求番号：' . $recHead['seikyu_den_no']); // 請求伝票番号
            $pdfObj->write_date(array('x' => 460, 'y' => 40, 'width' => 120, 'height' => 9, 'font_size' => 9, 'type' => 'time', 'align' => 'R'), $recHead['issue_date'], "Y年n月j日"); // 請求書発行日

            // ページ
            $pdfObj->write_string(array('x' => 45, 'y' => 785, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $page . '/' . $numPages);
        }

        $files = $this->makeDataFile($db, $title, $pdfObj, $data, $print_ymd);

        return $files;
    }

    /**
     * 明細を出力（アフター・A4）
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $recHead	ヘッダーデータ
     * @param array $data	ヘッダーデータ
     * @return num $total_prc_all  合計請求金額
     */
    private function outDataDetail_after($pdfObj, $db, $data) {

        // -- レイアウト設定 --
        static $meisai_row_height = 57.5;
        static $meisai_row_count1 = 7;
        static $meisai_row_count2 = 8;
        $set_arr1 = self::getSetArrAfter(58);
        $set_arr2 = self::getSetArrAfter();

        // -- 表示データ --
        $total_ippan = 0;
        $total_tokuten = 0;
        $total = 0;
        $total_out_zei = 0;
        $total_prc_all = 0;
        $row_arr = array();

        $recMsi = DataMapper_Pdf1101::findMsiAfter($db, array("seikyu_den_no" => $data['seikyu_den_no'], "__raw_1" => "T.kashidashi_kbn <> 2"));

        // 分割伝票の場合は元伝票の単価・金額・数量・値引率を取得
        $bunkatu = FALSE;
        if($data['bun_gas_kbn_num'] === '2' && isset($data['bun_gas_seikyu_den_no'])){
            $motoDenpyo = DataMapper_Pdf1101::findMsiAfter($db, array("seikyu_den_no" => $data['bun_gas_seikyu_den_no']));
            if(Msi_Sys_Utils::myCount($motoDenpyo) > 0){
                $bunkatu = TRUE;
                $uri_tnk = $motoDenpyo[0]['uri_tnk'];
//                $uri_prc = $motoDenpyo[0]['uri_prc'];
                $juchu_suryo = $motoDenpyo[0]['juchu_suryo'];
                $gojokai_nebiki_prc = $motoDenpyo[0]['gojokai_nebiki_prc'];
                $nebiki_prc = $motoDenpyo[0]['nebiki_prc'];
            }
        }

        // 合算伝票の場合は元伝票の明細情報を取得
        if($data['bun_gas_kbn_num'] === '20'){
            $motoDenpyo = DataMapper_Pdf1101::findMsiAfter($db, array("bun_gas_seikyu_den_no" => $data['seikyu_den_no'], '__etc_orderby' => array('nonyu_dt', 'seikyu_den_no', 'disp_no')));
            if(Msi_Sys_Utils::myCount($motoDenpyo) > 0){
                unset($recMsi);
                $recMsi = $motoDenpyo;
            }
        }

        foreach ($recMsi as $value) {

            // 名札
            $nafuda1 = '';
            $nafuda1 .= $value['nafuda_nm'];
            if($value['nafuda_nm2'] !== ''){
                $nafuda1 .= '/'.$value['nafuda_nm2'];
            }
            $nafuda2 = '';
            $nafuda2 .= $value['nafuda_nm3'];
            if($value['nafuda_nm4'] !== ''){
                $nafuda2 .= '/'.$value['nafuda_nm4'];
            }

            // 分割伝票の場合は元伝票の単価・金額・数量・値引率を参照
            if($bunkatu == FALSE){
                $uri_tnk = $value['uri_tnk'];
//                $uri_prc = $value['uri_prc'];
                $juchu_suryo =  $value['juchu_suryo'];
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'];
                $nebiki_prc = $value['nebiki_prc'];
            }
            // 請求金額(分割の場合も分割先の値を表示)
            $nebiki_total = $gojokai_nebiki_prc + $nebiki_prc; // 割引金額
            $seikyu_prc = $value['uri_prc'] + $value['nebiki_prc'] + $value['gojokai_nebiki_prc']; // 請求金額
            if ($nebiki_total == 0) {
                $nebiki_total = null;
            }

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt()) {
                if (isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array('', $value['nonyu_dt'], $value['nonyu_nm'], $value['shohin_tkiyo_nm'], $value['shohin_nm'], '札名', $nafuda1, $nafuda2, $uri_tnk, number_format($value['juchu_suryo']), '', $seikyu_prc, $value['zei_disp'], $value['keigen_disp'], null);

            $total_ippan += $value['uri_prc']; // 値引前合計
            $total_tokuten += $nebiki_total;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_out_zei += $value['out_zei_prc'];                // 外税合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
        }
        
        // 外税消費税
        if($total_out_zei > 0){
            $row_arr[] = array('外税消費税', null, null, null, null, null, null, null, null, null, null, $total_out_zei, null, null, null);
        }
        
        // 合計
        $row_arr[] = array('合計', null, null, null, null, null, null, null, null, null, null, $total_prc_all, null, null, null);
        
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, null, null, null, null, null, '以下余白');

        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName['AFTER']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName['AFTER2']);
        
        return $total_prc_all;
    }
    
    /**
     * 互助会消費税金額の内訳を出力する(鏡)
     *
     * <AUTHOR> kobayashi
     * @since 2022/09/12
     * @param $pdfObj
     * @param $db
     * @param $kaiin_info 会員情報
     * @param $x1 x座標調整
     * @param $y1 y座標調整
     */
    protected function outGojoZeiUchiwake($pdfObj, $db, $kaiin_info, $x1 = 0, $y1 = 0) {
        
        $pdfObj->write_string(array('x' => 323 + $x1, 'y' => 636 + $y1, 'width' => 150, 'height' => 9, 'font_size' => 8.5), '【互助会利用　消費税内訳】');
        
        $gojozei = array();
        foreach ($kaiin_info as $kaiin) {
            // 会費消費税
            if(isset($kaiin['zei_cd'])){
                $k_rtu = App_ClsTaxLib::GetTaxInfoZeiCd($db, $kaiin['zei_cd']);
                $k_prc = $kaiin['minou_gaku']; // 残金額
                $k_tax = $kaiin['kanyu_tax'] - $kaiin['wari_gaku_tax'];
                if(isset($gojozei[$k_rtu['zei_rtu']])){
                    $gojozei[$k_rtu['zei_rtu']]['prc'] += $k_prc;
                    $gojozei[$k_rtu['zei_rtu']]['tax'] += $k_tax;
                } else {
                    $gojozei[$k_rtu['zei_rtu']]['prc'] = $k_prc;
                    $gojozei[$k_rtu['zei_rtu']]['tax'] = $k_tax;
                }
            }
            // 早期利用費消費税
            if($kaiin['early_use_cost'] <> 0 || $kaiin['early_use_cost_zei'] <> 0){
                $s_rtu = App_ClsTaxLib::GetTaxInfoZeiCd($db, $kaiin['early_use_cost_zei_cd']);
                $s_prc = $kaiin['early_use_cost'];
                $s_tax = $kaiin['early_use_cost_zei'];
                if(isset($gojozei[$s_rtu['zei_rtu']])){
                    $gojozei[$s_rtu['zei_rtu']]['prc'] += $s_prc;
                    $gojozei[$s_rtu['zei_rtu']]['tax'] += $s_tax;
                } else {
                    $gojozei[$s_rtu['zei_rtu']]['prc'] = $s_prc;
                    $gojozei[$s_rtu['zei_rtu']]['tax'] = $s_tax;
                }
            }
        }
        
        $y = 645.5 + $y1;
        foreach ($gojozei as $zei_rtu => $value) {
            if($zei_rtu !== 0){
                $cap = $zei_rtu.'％税抜対象額';
                $pdfObj->write_string(array('x' => 322 + $x1, 'y' => $y, 'width' => 70, 'height' => 9, 'align' => 'R', 'font_size' => 9), $cap); // 文言
                $pdfObj->write_string(array('x' => 390 + $x1, 'y' => $y, 'width' => 65, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['prc']) . '－'); // 金額
                $pdfObj->write_string(array('x' => 456 + $x1, 'y' => $y, 'width' => 60, 'height' => 9, 'align' => 'L', 'font_size' => 9), '(会費消費税)'); // 文言
                $pdfObj->write_string(array('x' => 512 + $x1, 'y' => $y, 'width' => 55, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['tax']) . '－'); // 金額
            } else {
                $pdfObj->write_string(array('x' => 322 + $x1, 'y' => $y, 'width' => 70, 'height' => 9, 'align' => 'R', 'font_size' => 9), '非課税対象契約額'); // 文言
                $pdfObj->write_string(array('x' => 390 + $x1, 'y' => $y, 'width' => 65, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['prc']) . '－'); // 金額
            }
            $y += 10;
        }
    }

    /**
     * 消費税金額の内訳を出力する(鏡)
     *
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @param $pdfObj
     * @param $x1 x座標調整
     * @param $y1 y座標調整
     */
    protected function outZeiUchiwake1($pdfObj, $x1 = 0, $y1 = 0) {
        $seikyu_den_no = self::$_p_seikyu_den_no;

        $db = Msi_Sys_DbManager::getMyDb();

        if (isset(self::$_ptn) && self::$_ptn === static::PTN_KAKUTEICONFIRM) { // 請求確認書印刷用
            $shohizei = App_KeigenUtils::getTempSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no);
        } else {
            $shohizei = App_KeigenUtils::getSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no);
        }

        $pdfObj->write_string(array('x' => 323 + $x1, 'y' => 636 + $y1, 'width' => 150, 'height' => 9, 'font_size' => 8.5), '【消費税内訳】');
        $y = 645.5 + $y1;
        $outzei = 0;
        foreach ($shohizei as $value) {
            $cap = $value[0];
            $prc = $value[1];
            $detail = $value[2];
            if ($prc !== 0) {
                if ($detail['type'] === 'price') { // 対象額
                    $pdfObj->write_string(array('x' => 322 + $x1, 'y' => $y, 'width' => 70, 'height' => 9, 'align' => 'L', 'font_size' => 9), $cap); // 文言
                    $pdfObj->write_string(array('x' => 390 + $x1, 'y' => $y, 'width' => 65, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($prc) . '－'); // 金額
                    if($detail['zei_kbn'] == 0){ // 非課税
                        $y += 10;
                    }
                } else if ($detail['type'] === 'tax') { // 消費税
                    $pdfObj->write_string(array('x' => 456 + $x1, 'y' => $y, 'width' => 60, 'height' => 9, 'align' => 'L', 'font_size' => 9), $cap); // 文言
                    $pdfObj->write_string(array('x' => 512 + $x1, 'y' => $y, 'width' => 55, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($prc) . '－'); // 金額
                    $y += 10;
                    if ($detail['zei_kbn'] == 2) { // 外税
                        $outzei += $prc;
                    }
                }
            }
        }
        return $outzei;
    }

    /**
     * 消費税金額の内訳を出力する(明細)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param $row_arr
     */
    protected function outZeiUchiwake2(&$row_arr) {
        $seikyu_den_no = self::$_p_seikyu_den_no;

        $db = Msi_Sys_DbManager::getMyDb();

        if (isset(self::$_ptn) && self::$_ptn === static::PTN_KAKUTEICONFIRM) { // 請求確認書印刷用
            $shohizei = App_KeigenUtils::getTempSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no);
        } else {
            $shohizei = App_KeigenUtils::getSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no);
        }

        $tax_total = 0;
        $total = 0;
        foreach ($shohizei as $value) {
            if ($value[1] !== 0) {
                $row_arr[] = array(' '.$value[0], null, null, null, null, null, null, $value[1], null, null, null, null, null);
                if($value[2]['type'] === 'tax' && $value[2]['zei_kbn'] === '2'){ // 外税合計
                    $tax_total += $value[1];
                }
                if(!($value[2]['type'] === 'tax' && $value[2]['zei_kbn'] === '1')){ // 合計金額(内税消費税金額を除く)
                    $total += $value[1];
                }
            }
        }
        
        return array('tax_total' => $tax_total, 'total' => $total);
    }

    /**
     * 振込口座情報表示
     *
     * <AUTHOR> kobayashi
     * @since 2020/08/22
     * @param $pdfObj
     * @param $data ヘッダーデータ
     * @param $x x座標調整
     * @param $y y座標調整
     */
    protected function outBankInfo($pdfObj, $db, $data, $x = 0, $y = 0) {
        $bank = DataMapper_Pdf1101::getBankInfo($db, $data['bumon_cd']);
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 478 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), 'お振込先');
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 491 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), '口座名：');
        $pdfObj->write_string(array('x' => 373 + $x, 'y' => 491 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), $bank[0]['koza_meigi_nm']); // 振込銀行口座名義
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 504 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), '銀行名：');
        $pdfObj->write_string(array('x' => 373 + $x, 'y' => 504 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), $bank[0]['bank_nm'].'　'.$bank[0]['shiten_nm']); // 振込銀行名1、支店名1
        $pdfObj->write_string(array('x' => 373 + $x, 'y' => 517 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), $bank[0]['yokin_sbt_nm'].'　'.$bank[0]['st_br_koza_no']); // 口座種別1、口座番号1
        $pdfObj->write_string(array('x' => 373 + $x, 'y' => 535 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), $bank[1]['bank_nm'].'　'.$bank[1]['shiten_nm']); // 振込銀行名2、支店名2
        $pdfObj->write_string(array('x' => 373 + $x, 'y' => 548 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), $bank[1]['yokin_sbt_nm'].'　'.$bank[1]['st_br_koza_no']); // 口座種別2、口座番号2
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 564 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), '※金融機関の振込受領証をもって、領収書に代えさせて');
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 577 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), '頂きます。');
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 590 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), '大変恐縮でございますが、御請求書到着後10日以内に');
        $pdfObj->write_string(array('x' => 318 + $x, 'y' => 603 + $y, 'width' => 262, 'height' => 9, 'font_size' =>11), 'お振込をお願い申し上げます。');
    }
    
    /**
     * 振込口座情報表示(アフター)
     *
     * <AUTHOR> kobayashi
     * @since 2020/08/22
     * @param $pdfObj
     * @param $data ヘッダーデータ
     * @param $x x座標調整
     * @param $y y座標調整
     */
    protected function outBankInfoAfter($pdfObj, $db, $data, $x = 0, $y = 0) {
        $bank = DataMapper_Pdf1101::getBankInfo($db, $data['bumon_cd']);
        $seikyu_den_no = substr( $data['seikyu_den_no'], 4);
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 476 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), 'お振込先');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 489 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), '口座名：');
        $pdfObj->write_string(array('x' => 380 + $x, 'y' => 489 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), $bank[0]['koza_meigi_nm']); // 振込銀行口座名義
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 502 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), '銀行名：');
        $pdfObj->write_string(array('x' => 380 + $x, 'y' => 502 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), $bank[0]['bank_nm'].'　'.$bank[0]['shiten_nm']); // 振込銀行名1、支店名1
        $pdfObj->write_string(array('x' => 380 + $x, 'y' => 515 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), $bank[0]['yokin_sbt_nm'].'　'.$bank[0]['st_br_koza_no']); // 口座種別1、口座番号1
        $pdfObj->write_string(array('x' => 380 + $x, 'y' => 533 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), $bank[1]['bank_nm'].'　'.$bank[1]['shiten_nm']); // 振込銀行名2、支店名2
        $pdfObj->write_string(array('x' => 380 + $x, 'y' => 545 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), $bank[1]['yokin_sbt_nm'].'　'.$bank[1]['st_br_koza_no']); // 口座種別2、口座番号2
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 564 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '※振込の際は依頼者名の先頭に「'.$seikyu_den_no.'」を付けて');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 576 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '　入力をお願いします。');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 588 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '※振込時のお名前は請求名義でお願いします。');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 600 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '※金融機関の振込受領証をもって、領収書に代えさせて頂きます。');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 612 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '※領収書をご希望のお客様は、弊社までご連絡下さい。');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 624 + $y, 'width' => 275, 'height' => 9, 'font_size' =>10), '　後日、弊社よりご郵送致します。');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 636 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), '大変恐縮でございますが、御請求書到着後10日以内に');
        $pdfObj->write_string(array('x' => 325 + $x, 'y' => 649 + $y, 'width' => 275, 'height' => 9, 'font_size' =>11), 'お振込をお願い申し上げます。');
    }
    
    /**
     * 施行プラン摘要名表示
     *
     * <AUTHOR> kobayashi
     * @since 2020/02/18
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outPlanSummaryNm($pdfObj, $recHead) {
        if (isset($recHead['plan_summary_nm'])) {
            $plan_summary_strlen = mb_strlen($recHead['plan_summary_nm'], 'UTF-8');
            if ($plan_summary_strlen > 34) { // 圧縮あり(1行目備考欄に2行で表示)
                $pdfObj->write_string(array('x' => 405, 'y' => 136, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 0, $plan_summary_strlen / 2, 'UTF-8'));
                $pdfObj->write_string(array('x' => 405, 'y' => 144, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], $plan_summary_strlen / 2, $plan_summary_strlen - 1, 'UTF-8'));
            } else {
                $pdfObj->write_string(array('x' => 405, 'y' => 136, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 0, 17, 'UTF-8'));
                $pdfObj->write_string(array('x' => 405, 'y' => 144, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 17, $plan_summary_strlen - 1, 'UTF-8'));
            }
        }
    }
    
    /**
     * 消費税基準日(sougi_ymd)を設定する
     * juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param $pdfObj
     * @return void
     */
    protected function _prepKijunYmdKeigen($seko_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );
        if($sougi_ymd === null){
            self::$_kijun_ymd = Msi_Sys_Utils::getDate();
        } else {
            self::$_kijun_ymd = $sougi_ymd;
        }
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @return boolean
     */
    private function _isKeigenAppliedCtxt() {
        $kijun_ymd = self::$_kijun_ymd;
        if ($kijun_ymd == null) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ($keigenBgnYmd <= $kijun_ymd) {
            return true;
        }
        return false;
    }

    /**
     * 明細表の表示位置取得
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/10
     * @value $meisai_top
     * @return $set_arr
     */
    protected function getSetArr($meisai_top)
    {
        $set_arr[] = array('x' => 23, 'y' => $meisai_top, 'width' => 148, 'height' => 15); // 商品名（１行）
        $set_arr[] = array('x' => 23, 'y' => $meisai_top - 5, 'width' => 148, 'height' => 15); // 商品名（２行上）
        $set_arr[] = array('x' => 23, 'y' => $meisai_top + 6, 'width' => 148, 'height' => 15, 'font_size' => 8); // 商品名（２行下）
        $set_arr[] = array('x' => 174, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 商品単価
        $set_arr[] = array('x' => 230, 'y' => $meisai_top, 'width' => 20, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 252, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 商品価格
        $set_arr[] = array('x' => 300, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 会員特典
        $set_arr[] = array('x' => 350, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 御利用価格
        $set_arr[] = array('x' => 401, 'y' => $meisai_top - 5, 'width' => 157, 'height' => 15); // 備考（上）
        $set_arr[] = array('x' => 401, 'y' => $meisai_top + 5, 'width' => 157, 'height' => 15); // 備考（下）
        $set_arr[] = array('x' => 558, 'y' => $meisai_top - 5, 'width' => 20, 'height' => 15, 'align' => 'C'); // 税（上）
        $set_arr[] = array('x' => 558, 'y' => $meisai_top + 5, 'width' => 20, 'height' => 15, 'align' => 'C'); // 税（下）
        $set_arr[] = array('x' => 349, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num', 'font_size' => 12); // 御利用価格(サイズ大)
        
        return $set_arr;
    }

    /**
     * 明細表の表示位置取得(アフター)
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/10
     * @value $meisai_top
     * @return $set_arr
     */
    protected function getSetArrAfter($y = 0) {
        $set_arr[] = array('x' => 30, 'y' => 203+$y, 'width' => 80, 'height' => 15, 'font_size' =>11); // タイトル
        $set_arr[] = array('x' => 30, 'y' => 182+$y, 'width' => 80, 'height' => 15, 'type' => 'date', 'font_size' =>11, 'format' => 'Y年m月d日'); // 納品予定日
        $set_arr[] = array('x' => 107, 'y' => 182+$y, 'width' => 110, 'height' => 15, 'font_size' =>11); // 納品場所名
        $set_arr[] = array('x' => 215, 'y' => 182+$y, 'width' => 120, 'height' => 15, 'font_size' =>11); // 商品摘要
        $set_arr[] = array('x' => 30, 'y' => 195+$y, 'width' => 300, 'height' => 15, 'font_size' =>11); // 商品名
        $set_arr[] = array('x' => 30, 'y' => 208+$y, 'width' => 80, 'height' => 15, 'font_size' =>11); // 札名(ラべル)
        $set_arr[] = array('x' => 70, 'y' => 208+$y, 'width' => 265, 'height' => 15, 'font_size' =>11); // 名札1、2
        $set_arr[] = array('x' => 70, 'y' => 221+$y, 'width' => 265, 'height' => 15, 'font_size' =>11); // 名札3、4
        $set_arr[] = array('x' => 376, 'y' => 202+$y, 'width' => 44, 'height' => 15, 'type' => 'num', 'font_size' =>11); // 商品単価
        $set_arr[] = array('x' => 431, 'y' => 202+$y, 'width' => 35, 'height' => 15, 'align' => 'C', 'font_size' =>11); // 数量
        $set_arr[] = array('x' => 446, 'y' => 202+$y, 'width' => 24, 'height' => 15, 'font_size' =>11); // 単位
        $set_arr[] = array('x' => 495, 'y' => 204+$y, 'width' => 50, 'height' => 15, 'type' => 'num', 'font_size' =>11); // 商品価格
        $set_arr[] = array('x' => 553, 'y' => 195+$y, 'width' => 20, 'height' => 15, 'font_size' =>11); // 税（上）
        $set_arr[] = array('x' => 553, 'y' => 213+$y, 'width' => 20, 'height' => 15, 'font_size' =>11); // 税（下）
        $set_arr[] = array('x' => 250, 'y' => 215+$y, 'width' => 80, 'height' => 15, 'align' => 'R', 'font_size' =>11); // 以下余白
        return $set_arr;
    }

    /**
     * 明細表ヘッダー
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeader($pdfObj, $recHead, $data) {
        $date_format = "Y年n月j日";

        // 施行情報
        $k_nm = '故　' . $recHead['k_nm'] . '様　ご葬儀'; // 故人名
        $pdfObj->write_string(array('x' => 30, 'y' => 32, 'width' => 180, 'height' => 15, 'font_size' =>11), $k_nm); // 故人
        $pdfObj->write_string(array('x' => 30, 'y' => 44, 'width' => 180, 'height' => 15, 'font_size' =>11), '見積担当： ' . $recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 30, 'y' => 64, 'width' => 180, 'height' => 15, 'font_size' =>11), '施行日');
        $pdfObj->write_date(array('type' => 'time', 'x' => 70, 'y' => 64, 'width' => 130, 'height' => 15, 'font_size' =>11), $recHead['kokubetsu_ymd'], $date_format); // 施行日
        // 請求書発行日
        $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['issue_date'], $date_format);
        // 契約団体
        $pdfObj->write_string(array('x' => 400, 'y' => 93, 'width' => 175, 'height' => 9, 'font_size' =>10), $recHead['keiyaku_nm']); // 契約団体1
    }

    /**
     * 明細表ヘッダー（法事）
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeader_houji($pdfObj, $recHead, $data) {
        $date_format = "Y年n月j日";

        // 施行情報
        $pdfObj->write_string(array('x' => 30, 'y' => 32, 'width' => 180, 'height' => 15, 'font_size' =>11), $recHead['sekyu_nm'].'様'); // 施主
        $pdfObj->write_string(array('x' => 30, 'y' => 44, 'width' => 180, 'height' => 15, 'font_size' =>11), '見積担当： ' . $recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 30, 'y' => 64, 'width' => 180, 'height' => 15, 'font_size' =>11), '施行日');
        $pdfObj->write_date(array('type' => 'time', 'x' => 70, 'y' => 64, 'width' => 130, 'height' => 15, 'font_size' =>11), $recHead['houyou_ymd'], $date_format); // 施行日
        // 請求書発行日
        $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['issue_date'], $date_format);
        // 契約団体
        $pdfObj->write_string(array('x' => 400, 'y' => 93, 'width' => 175, 'height' => 9, 'font_size' =>10), $recHead['keiyaku_nm']); // 契約団体1
    }

    /**
     * 会員情報（明細）を出力する
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/05
     * @param array  $row_arr
     * @return $kaiin_total
     */
    protected function outKaiinInfoMsi($db, &$row_arr, $kaiin_info)
    {
        $kaiin_row = 0;
        $kaiin_total = 0;
        $break_yoto = '';
        $row_arr[] = array();
        
        // 消費税情報（葬儀日→現在日）
        foreach ($kaiin_info as $value) {
            $kaiin_row++;
            if(isset($value['zei_cd'])){ // 消費税コード
                $tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['zei_cd']);
            }
            if(isset($value['early_use_cost_zei_cd'])){ // 早期利用費消費税コード
                $early_tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['early_use_cost_zei_cd']);
            }
            if(isset($value['meigi_chg_cost_zei_cd'])){ // 名義変更手数料消費税コード
                $meigi_tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['meigi_chg_cost_zei_cd']);
            }
//            if(($break_yoto !== $value['yoto_kbn']) && $value['yoto_kbn'] !== '4'){
            if($value['yoto_kbn'] !== '4'){
                $row_arr[] = array('◆' . $value['yoto_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);
            }
//            }
            if(($value['yoto_kbn'] === '1')){ // 1:コース施行
                $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, null, '契約金額：'.self::_dispPrice($value['keiyaku_gaku']), '　入金額：'.self::_dispPrice($value['harai_gaku']), null, null, null);
                if($value['wari_gaku'] <> 0){ // 割引額(前納割引額)
                    $ikkatu = '';
                    if($value['n_free3'] <> 0 && $value['n_free3'] !== null){ // 全額一括支払割引額
                        $ikkatu = '※内、全額一括支払割引額：'.self::_dispPrice($value['n_free3']);
                    }
                    $row_arr[] = array('　割引額', null, null, null, null, null, null, null, self::_dispPrice($value['wari_gaku']*-1), $ikkatu, null, null, null);
                }
                $row_arr[] = array('　残金額', null, null, null, null, null, null, $value['minou_gaku'], '残回数：'.$value['minou_no'], null, null, null, null);
                $kaiin_total += $value['minou_gaku'];
                if($value['kanyu_tax'] <> 0){
                    $row_arr[] = array('　会費消費税('.$tax['zei_rtu'].'％)', null, null, null, null, null, null, $value['kanyu_tax']-$value['wari_gaku_tax'], null, null, null, null, null);
                    $kaiin_total += $value['kanyu_tax']-$value['wari_gaku_tax'];
                }
                if($value['early_use_cost'] <> 0){ // 早期利用費
                    $row_arr[] = array('　早期利用費', null, null, null, null, null, null, $value['early_use_cost'], null, null, null, null, null);
                    $kaiin_total += $value['early_use_cost'];
                    if($value['early_use_cost_zei'] <> 0){
                        $row_arr[] = array('　早期利用費消費税('.$early_tax['zei_rtu'].'％)', null, null, null, null, null, null, $value['early_use_cost_zei'], null, null, null, null, null);
                        $kaiin_total += $value['early_use_cost_zei'];
                    }
                }
                if($value['warimashi_gaku'] <> 0){ //  割増金額
                    $row_arr[] = array('　割増金額', null, null, null, null, null, null, null, self::_dispPrice($value['warimashi_gaku']*-1), null, null, null, null);
                }
                } else if($value['yoto_kbn'] === '3'){ // 3:完納充当
                if($value['point']  == 0 && $value['riyoken']  == 0){ // ポイントが0かつ利用券が0
                    $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, $value['keiyaku_gaku']*-1, '契約金額：'.self::_dispPrice($value['keiyaku_gaku']), null, null, null, null);
                    $kaiin_total += $value['keiyaku_gaku']*-1;
                    if($value['wari_gaku'] <> 0){ // 割引額(前納割引額)
                        $row_arr[] = array('　割引額', null, null, null, null, null, null, null, self::_dispPrice($value['wari_gaku']*-1), null, null, null, null);
                    }
                    $row_arr[] = array('　残金額', null, null, null, null, null, null, $value['minou_gaku'], '残回数：'.$value['minou_no'], null, null, null, null);
                    $kaiin_total += $value['minou_gaku'];
                } else if($value['point'] > 0){ // ポイント
                    $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, $value['point']*-1, '契約金額：'.self::_dispPrice($value['point']*-1), null, null, null, null);
                    $kaiin_total += $value['point']*-1;
                } else if($value['riyoken'] > 0){ // 利用券
                    $row_arr[] = array(null, null, null, null, null, null, null, $value['riyoken']*-1, '施設利用券', null, null, null, null);
                    $kaiin_total += $value['riyoken']*-1;
                } 
                
            }
            $break_yoto = $value['yoto_kbn'];
        }
        if($kaiin_row > 0){
            $row_arr[] = array('【互助会利用合計】', null, null, null, null, null, null, $kaiin_total, null, null, null, null, null);
        }
        $row_arr[] = array();
        
        return $kaiin_total;
    }
    
    /**
     * 備考に表示用の金額変換
     *
     * <AUTHOR> Kobayashi
     * @return $disp_prc 変換後金額
     */
    protected function _dispPrice($prc)
    {
        $disp_prc = str_replace('-', '△', number_format($prc));
        return $disp_prc;
    }

    /**
     * PDFファイルデータの作成＆データの保存
     *
     * <AUTHOR> kobayashi
     * @since 2020/09/24
     * @value $pdfObj
     * @value $db
     * @value $recHead ヘッダーデータ
     * @return
     */
    private function makeDataFile($db, $title, $pdfObj, $data, $print_ymd) {
        self::$_cnt ++; // データ件数
        // -- PDFファイルデータ作成 --
        $buf = $pdfObj->fileOutBuf();
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);

        // -- 請求書発行フラグの更新 --
        if (self::$_update_flg) {
            $seikyu['sekkyu_kaisu']          = $data['sekkyu_kaisu'] + 1;
            $seikyu['seikyu_print_tanto_cd'] = App_Utils::getTantoCd();         //請求書発行者
            if ($data['sekkyu_kaisu'] == 0) {
                $seikyu['seikyu_print_date']     = $print_ymd;                      //請求書発行日
                $seikyu['d_free1']     = $print_ymd;                      
            } else {
                $seikyu['d_free1']     = $print_ymd;                      
            }
            $seikyu['seikyu_ymd']            = $print_ymd;                      //請求日
            $where['seikyu_den_no']          = $data['seikyu_den_no'];
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL( 'seikyu_denpyo', $seikyu, $where );
            $cnt = $db->easyExecute($sql, $param);
        }


        return array('path' => $title . '.pdf', 'file' => $temp_file);
    }

    /**
     * 
     * 再発行時の必須チェック処理
     * 
     * @param db $db
     * @param array $params
     */
    private function checkSekyuInfo($db, $params) {

        $msg = '';
        $errMsg = '';
        foreach ($params['seikyu_no_arr'] as $seikyu_den_no) {
            // 請求伝票データと請求先情報データを取得する
            $seikyuDenpyo = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $seikyu_den_no));
            $seikyuSakiInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $seikyu_den_no));
            if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
                $msg .= "請求伝票No：" . $seikyu_den_no . "のデータがありません";
            } else {
                // 郵送区分が「郵送」の場合、請求先名・請求書送付先名・送付先郵便番号・送付先住所1のいずれかがNULLのときはエラー
                if ($seikyuDenpyo[0]['data_kbn'] === static::KBN_SOUGI || $seikyuDenpyo[0]['data_kbn'] === static::KBN_HOUJI) {
                    if ($seikyuDenpyo[0]['seikyu_post_kbn'] === static::POST_KBN_YUSO) {
                        if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
                            $msg .= "請求先情報(" . $seikyu_den_no . ")のデータがありません";
                        } else {
                            if (!isset($seikyuSakiInfo[0]['sekyu_nm1']) && isset($seikyuSakiInfo[0]['sekyu_nm2'])) {
                                $msg .= '請求先名,';
                            }
                            if (!isset($seikyuSakiInfo[0]['sekyu_soufu_nm'])) {
                                $msg .= '請求書送付先名,';
                            }
                            if (!isset($seikyuSakiInfo[0]['soufu_yubin_no']) || !isset($seikyuSakiInfo[0]['soufu_addr1'])) {
                                $msg .= '請求書送付先住所,';
                            }
                            if (strlen($msg) > 0) {
                                $errMsg .= trim($msg, ',') . "が設定されていません。(請求伝票番号：" . $seikyu_den_no . ")";
                            }
                        }
                    }
                }
            }
        }
        if (strlen($errMsg) > 0) {
            throw new Exception(sprintf($errMsg));
        }
    }
}
