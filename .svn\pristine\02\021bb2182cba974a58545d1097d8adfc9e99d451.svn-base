{include file="fdn_head_std.tpl"}
{include file="fdn_header_hachu2.tpl"}
<form action="#" ondragover="return false"  onMouseMove = "return false;">
    <div id="main">
        {include file="header_info.tpl"} 
        <!-- サイドメニュー開始 -->
        {include file="side_menu.tpl"}
        <!-- サイドメニュー終了 -->
        <!-- 本体開始 -->
        <div id="detail">
            <div id="estimate" style="height:56.8%">
                <div class="mylist">
                    <table>
                        <tbody>
                            <tr>
                                <td class="chk_select c_all" style ="width: 4%">選択</td>
                                <td style ="width:18%">発注先</td>
                                <td class="w25" style ="width:29%">納品場所</td>
                                <td 　class="w5" style="width:24.3%;">品名</td>
                                <td   class="w5" style="max-width:5%;">単価</td>
                                <td   class="w5" style="max-width:5%;">数量</td>
                                <td >発注</td> 
                            </tr>
                        </tbody>
                    </table>
                </div>
                {literal}
                    <div id ="dataContent">
                    </div>
                    <!-- 明細データ行 テンプレート -->
                    <script type="text/template" id="item-template">
                        <dl>
                            <dt class="field_left">
                            <span class="radio_set">
                                <label for="i_chk"set class="lbl_stamp_check" ></label>
                                <input type="checkbox" id="i_chk" name="i_chk" value="1"  />
                            </span>
                        </dt>
                            <dt>
                                <input type="text"  name="hinmk" class="txt hinmkLabel" value="" readonly = "readonly" />
                                <div class="label dlg_date hachusk_button"></div>
                                <input type ="hidden" name ="siire_cd" class="siire_cd" value ="" />
                            </dt>
                            <dt>
                                <div class="product_detail">
                                    <select id="komokku" class="komokku"></select>
                                    <input type="text" name="n_place" class="txt i_n_place"  readonly="readonly" />
                                    <div class="label dlg_place"></div>
                                </div>
                            </dt>
                            <div class="product_detail2">
                                <dt>
                                    <input type ="text" name ="i_hinpin"  class= "txt i_hinpin" readonly = "readonly">
                                    <input type="text" name="tanka" class="txt i_tanka i_tanka_2" readonly="readonly"/>
                                    <input type="text" id="hachu_suryo" name="hachu_suryo"  class="txt i_qty i_qty_2" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                                </dt>
                            </div>
                            <dt>
                                <div style="width:8.23%;float:left" name ="product_option">
                                </div>
                            </dt>
                            <div>
                                <dt>
                                    <input type="text" id="hachu" name="hachu" class="txt i_stats" readonly="readonly"/>
                                </dt>
                            </div>
                        </dl>
                    </script>
                {/literal}
                <script id="my-data-init-id" type="application/json">
                    {$mydata_json|smarty:nodefaults}
                </script>
                <!-- 本体終了 -->
            </div>
            <div class="function">
               
            </div>
            <div id="foot" class="base_3"style="top:10%">
                <fieldset>
                    <label id="biko1" for="memo" class="require done underbiko" >備考</label>
                    <textarea name="memo" id="memo" class="txt memo" cols="1" rows="10" maxlength="200"></textarea>
                </fieldset>
            </div>
            {include file="hachu/hachushori/hachu_footer.tpl"}

        </div>
    </div>
</form>
<script id="my-data-init-seko" type="application/json">
    {$mydata_seko|smarty:nodefaults}                
</script>
{include file="fdn_footer_std.tpl"}

<!-- /div>
</body>
</html --><!-- mihara deleted -->
