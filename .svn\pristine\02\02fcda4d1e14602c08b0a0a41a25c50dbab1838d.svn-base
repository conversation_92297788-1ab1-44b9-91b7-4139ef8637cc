<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\SQLAdmin;

class InstancesDemoteMasterRequest extends \Google\Model
{
  protected $demoteMasterContextType = DemoteMasterContext::class;
  protected $demoteMasterContextDataType = '';

  /**
   * @param DemoteMasterContext
   */
  public function setDemoteMasterContext(DemoteMasterContext $demoteMasterContext)
  {
    $this->demoteMasterContext = $demoteMasterContext;
  }
  /**
   * @return DemoteMasterContext
   */
  public function getDemoteMasterContext()
  {
    return $this->demoteMasterContext;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(InstancesDemoteMasterRequest::class, 'Google_Service_SQLAdmin_InstancesDemoteMasterRequest');
