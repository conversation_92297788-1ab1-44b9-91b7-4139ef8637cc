{include file="fdn_head_std.tpl"}
{include file="fdn_header_1.tpl"}
<div class="container div-fixed-for-spin" id="main-container">
    <form  id="customerinfo-form-id">
        <div id="main">
            {include file="header_info.tpl"}
            {include file="side_menu.tpl"}
            <div id="detail" style="height: 92%; box-shadow: -2px 0 0px rgba(0,0,0,0.2);">
                <div id = "customer-div-wrapper" style="display: none">
                    <div id="base-tab">
                        <fieldset class="base_1">
                            <div class="protect">
                                <label for="apply_type" class="lbl_apply_type require done">申込区分</label>
                                <input type="hidden" name="apply_type" id="apply_type" class="cls_apply_type"/>
                                <label for="funeral_type" class="lbl_funeral_type require done">葬儀区分</label>
                                <input type="hidden" name="funeral_type" id="funeral_type" class="cls_funeral_type"/>
                                <label for="code" class="lbl_code require done">受付番号</label>
                                <input name="code_1" id="code_1" type="text" class="txt to_alpha_num" value="" maxlength = "6" disabled="disabled"/>
                                <!--
                                <div class="label code">-</div>
                                <input name="code_2" id="code_2" type="text" class="txt" value="" maxlength = "2"/>
                                <div class="label code">-</div>
                                <input name="code_3" id="code_3" type="text" class="txt" value="" maxlength = "3"/>
                                -->	
                                <span  class="radio_set">
                                    <label for="tyoku" class="lbl_select_kbn" id="lbl_tyoku">直</label>
                                    <input id="tyoku" type="checkbox" value="1" />
                                </span>
                                <input name="code_4" id="code_4" type="text" class="txt to_alpha_num ime-off" value="" maxlength = "6"/>
                            </div>
                            <label for="personal_info" class="lbl_personal_info require done">個人情報保護</label>
                            <input type="hidden" name="personal_info" id="personal_info" class="cls_personal_info ime-off"/>
                        </fieldset>
                        <fieldset class="base_2">
                            <div class="protect">
                                <label for="member" class="lbl_member require done">会員</label>
                                <input type="hidden" name="member" id="member" class="cls_member"/>
                            </div>
                            <input name="member_detail" id="member_detail" type="text" class="txt ime-on" value="" maxlength = "20" placeholder="理由"/>
                            <div class="protect">
                                <div id="plan_area">
                                    <label for="plan" class="lbl_plan require done">プラン選択</label>
                                    <input type="hidden" name="plan" id="plan" class="cls_plan ime-off" placeholder="(一般のみ選択)" disabled="disabled"/>
                                </div>
                            </div>
                            <label for="staff_1" class="lbl_staff require">打合せ担当者</label>
                            <input name="staff_1" id="staff_1" type="text" class="txt ime-on" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff1 cursor-pointer"></div>
                            <label for="staff_2" class="lbl_staff require done">施行担当者</label>
                            <input name="staff_2" id="staff_2" type="text" class="txt ime-on" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff2 cursor-pointer"></div>
                        </fieldset>
                    </div>
                    <ul class="tab">
                        {*                        <li id="tab-input-info1"><span>受付情報①</span></li>*}
                        <li id="tab-input-info2"><span>受付情報</span></li>
                        <li id="tab-seikyu-info"><a href="javascript:void(0)">喪主・御請求情報</a></li>
                        <li id="tab-seko-info"><a href="javascript:void(0)">施行情報</a></li>
                        <li id="tab-kaiin-info"><a href="javascript:void(0)">会員情報</a></li>
                            {*                        <li id="tab-other-info"><a href="javascript:void(0)">その他</a></li>*}
                            {if $kashidashi_disp == 1}<li id="tab-kashidasi-info"><a href="javascript:void(0)">貸出備品</a></li>{/if}
                        <li id="tab-report-info"><a href="javascript:void(0)">報告書</a></li>
                    </ul>
                </div>
                <div id = "customer-tab">
                    {*受付情報タブ*}
                    <div id = "input-tab" class = "tab-contents">
                        <fieldset class="base_1">
                            <label for="uketuke_date" class="lbl_uketuke_date require done">受付日</label>
                            <input name="uketuke_date" id="uketuke_date" type="text" class="txt ime-off date_auto_slash to_alpha_num ime-off" value="" maxlength = "10"/>
                            <div class="label dlg_date"></div>
                            <input name="uketuke_time" id="uketuke_time" type="text" class="txt ime-off time_auto_colon to_alpha_num ime-off" value="" maxlength = "5"/>
                            <div class="label dlg_time"></div>
                            <label for="uketuke_danto" class="require done">受付者</label>
                            <input name="uketuke_danto" id="uketuke_danto" type="text" class="txt ime-on" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_uketuke_danto cursor-pointer"></div>
                            <label for="uchiawase_date" class="lbl_uchiawase_date require done">お打合せ日時</label>
                            <input name="uchiawase_date" id="uchiawase_date" type="text" class="txt ime-off date_auto_slash to_alpha_num ime-off" value="" maxlength = "10"/>
                            <div class="label dlg_date"></div>
                            <input name="uchiawase_time" id="uchiawase_time" type="text" class="txt ime-off time_auto_colon to_alpha_num ime-off" value="" maxlength = "5"/>
                            <div class="label dlg_time"></div>
                        </fieldset>
                        <fieldset class="base_0">
                            <label for="renraku_name" class="lbl_name done lbl_replace_kojin">連絡者のお名前</label>
                            <input name="renraku_name" id="renraku_name" type="text" class="txt to_alpha_num_em ime-on" value="" maxlength = "30"/>
                            <div class="label lbl_name_sama">様</div>
                            <label for="renraku_zoku" class="lbl_renraku_zoku done">続柄</label>
                            <input name="renraku_zoku" id="renraku_zoku" type="text" class="txt cls_renraku_zoku to_alpha_num_em ime-on" value="" maxlength = "10"/>
                            <label for="renraku_tel" class="lbl_renraku_tel done">携帯番号<span class="star_color">★</span></label>
                            <input name="renraku_tel" id="renraku_tel" type="text" class="txt ime-off to_alpha_num ime-off" value="" maxlength = "15" placeholder="000-0000-0000"/>
                        </fieldset>
                        <fieldset class="person_1 radio_set">
                            <label for="name" class="lbl_name require lbl_replace_kojin done">故人お名前</label>
                            <input name="name" id="name" type="text" class="txt to_alpha_num_em ime-on" value="" maxlength = "30"/>
                            <div class="label lbl_name_sama">様</div>
                            <input id = "name_clip ime-off" data-clip ="1" style="display: none" type=file accept="image/*">
                            <div class="label dlg_clip cursor-pointer"></div>
                            <div id = "name_clip_link" class="label"></div>
                            <div id = "name_clip_clear" class = ""></div>
                            <div class="lbl_sex subtitle require done">性別</div>
                            <span id="sex_set" class="radio_set">
                                <label for="male" class="lbl_male">男性</label><input name="sex" class="radio_sex ime-off" id="male" type="radio" value="1" checked="checked" />
                                <label for="female" class="lbl_female">女性</label><input name="sex" class="radio_sex ime-off" id="female" type="radio" value="2" />
                            </span>
                            <label for="spouse" class="lbl_spouse require done">配偶者</label>
                            <input type="hidden" name="spouse" id="spouse" class="cls_spouse ime-off"/>
                        </fieldset>
                        <fieldset class="person_2">
                            <label for="kana" class="lbl_kana require done">ふりがな<span class="star_color">★</span></label>
                            <input name="kana" id="kana" type="text" class="txt to_alpha_num_em ime-on" value="" maxlength = "30"/>
                            <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                            <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era ime-off"/>
                            <input name="birthday_date" id="birthday_date" type="text" class="txt date_auto_slash_s to_alpha_num ime-off" value="" maxlength = "8" placeholder="00/00/00" />
                            <input name="birthday_date_y" id="birthday_date_y" type="text" class="txt date_auto_slash to_alpha_num ime-off" value="" maxlength = "10" placeholder="0000/00/00" />
                            <div class="label" id="age"></div>
                        </fieldset>
                        <fieldset class="person_2">
                            <div class="lbl_address subtitle require done">現住所<span class="star_color">★</span>
                                <span class="radio_set">
                                    <input name="print_addr_k" id="print_addr_k" type="checkbox" value="1" />
                                    <label for="print_addr_k" class="lbl_print_address lbl_print_addr_k">礼状住所</label>
                                </span>
                            </div>
                            <label for="zip_1" class="lbl_zip done">〒</label>
                            <input name="zip_1" id="zip_1" type="text" data-zip = "k1" class="txt zip_helper to_alpha_num ime-off" value="" maxlength = "10"/>
                            <div class="label dlg_zip cursor-pointer"></div>
                            <input name="address_1_1" id="address_1_1" type="text" class="txt to_alpha_num_em ime-on" value=""  maxlength = "30"/>
                            <div id="m0" class="label dlg_map map_btn mb0"></div>
                            <label for="tel_1" class="require done">TEL<span class="star_color">★</span></label>
                            <input name="tel_1" id="tel_1" type="text" class="txt to_alpha_num ime-off" value="" maxlength = "15" placeholder="************"/>
                            <input name="address_1_2" id="address_1_2" type="text" class="txt to_alpha_num_em ime-on" value=""  maxlength = "30"  placeholder="ビル、マンション"/>
                            <label for="head_1" class="require done">世帯主</label>
                            <input type="hidden" name="head_1" id="head_1" class="cls_head_1 ime-off"/>
                        </fieldset>
                        <fieldset class="base_0">
                            <label for="pacemaker" class="lbl_kana">ペースメーカー</label>
                            <input type="hidden" id="pacemaker" name="pacemaker" class="cls_pacemaker"/>
                            <span  class="radio_set">
                                <label for="wait_flg" class="lbl_select_kbn" id="lbl_wait">連絡待ち</label>
                                <input id="wait_flg" type="checkbox" value="1" />
                            </span>
                            <input name="btn_search" class="btn_gaiji" type="button" class="my-no-readonly" value="外字確定メール"/>
                        </fieldset>
                        <div id="map_area_0" class="map_area_disappear_0"></div>
                        <fieldset class="base_1">
                            <label for="sibo_basho" class="lbl_sibo_basho require done">亡くなられた場所<span class="star_color">★</span></label>
                            <input type="hidden" name="sibo_basho" id="sibo_basho" class="cls_sibo_basho"/>
                            <input name="sibo_basho_name" id="sibo_basho_name" type="text" class="txt" value="" maxlength = "30"/>
                            <div class="label dlg_sibo_basho cursor-pointer"></div>
                            <input type="hidden" name="shiin" id="shiin" class="cls_shiin" placeholder="(未設定)"/>
                            <input name="byouto" id="byouto" type="text" class="txt  ime-on" value="" maxlength = "20" placeholder="〇病棟/〇階/〇号室/霊安室 等"/>
                            {*                                <div class="label lbl_name_sama lbl_name_byouto">病棟</div>*}
                            {*<input name="byouto_gai" id="byouto_gai" type="text" class="txt to_alpha_num" value="" maxlength = "4"/>
                            <div class="label lbl_name_sama">階</div>*}
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="pickup_date" class="lbl_pickup_date done">お迎え予定時間<span class="star_color">★</span></label>
                            <input name="pickup_date" id="pickup_date" type="text" class="txt ime-off date_auto_slash to_alpha_num" value="" maxlength = "10"/>
                            <div class="label dlg_date date_omukae"></div>
                            <div class="label dlg_date_dis"></div>
                            <input name="pickup_time" id="pickup_time" type="text" class="txt ime-off time_auto_colon to_alpha_num" value="" maxlength = "5"/>
                            <div class="label dlg_time time_omukae"></div>
                            <div class="label dlg_time_dis"></div>
                            <label for="dropoff" class="lbl_dropoff require done">安置先</label>
                            <input type="hidden" name="dropoff_type" id="dropoff_type" class="cls_dropoff_type"/>
                            <input name="dropoff_name" id="dropoff_name" type="text" class="txt" value="" maxlength = "30"/>
                            <div class="label dlg_dropoff_name cursor-pointer"></div>
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="iso_kbn1" class="lbl_iso_kbn require done">お迎え搬送者<span class="star_color">★</span></label>
                            <input type="hidden" name="iso_kbn1" id="iso_kbn1" class="iso_kbn cls_iso_knn1"/>
                            <input name="staff_3" id="staff_3" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff3 cursor-pointer"></div>
                            <input name="staff_3_2" id="staff_3_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff3_2 cursor-pointer"></div>
                            <label for="car_number" class="lbl_car_number done">車輛番号</label>
                            <input name="car_number" id="car_number" type="text" class="txt" value="" maxlength = "20"/>
                            <div class="label dlg_car dlg_car_number1 cursor-pointer"></div>
                            <div class="label disable_number1 cursor-pointer"></div>
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="pickup_date2" class="lbl_pickup_date done">移動予定時間</label>
                            <input name="pickup_date2" id="pickup_date2" type="text" class="txt ime-off date_auto_slash to_alpha_num" value="" maxlength = "10"/>
                            <div class="label dlg_date"></div>
                            <input name="pickup_time2" id="pickup_time2" type="text" class="txt ime-off time_auto_colon to_alpha_num" value="" maxlength = "5"/>
                            <div class="label dlg_time"></div>
                            <label for="pickup" class="lbl_pickup done">お連れする場所</label>
                            <input type="hidden" name="pickup_type" id="pickup_type" class="cls_pickup_type"/>
                            <input name="pickup_name" id="pickup_name" type="text" class="txt" value="" maxlength = "30"/>
                            <div class="label dlg_pickup_name cursor-pointer"></div>
                        </fieldset>
                        <fieldset class="base_0">
                            <label for="iso_kbn2" class="lbl_iso_kbn done">移動搬送者</label>
                            <input type="hidden" name="iso_kbn2" id="iso_kbn2" class="iso_kbn"/>
                            <input name="staff_4" id="staff_4" type="text" class="txt ime-on" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff4 cursor-pointer"></div>
                            <input name="staff_4_2" id="staff_4_2" type="text" class="txt ime-on" value="" maxlength = "20" readonly="readonly"/>
                            <div class="label dlg_staff dlg_staff4_2 cursor-pointer"></div>
                            <label for="car_number2" class="lbl_car_number done">車輛番号</label>
                            <input name="car_number2" id="car_number2" type="text" class="txt" value="" maxlength = "20"/>
                            <div class="label dlg_car dlg_car_number2 cursor-pointer"></div>
                            <div class="label disable_number2 cursor-pointer"></div>
                        </fieldset>
                        <fieldset class="funeral_2">
                            <label for="family_name" class="lbl_family_name require done">葬家</label>
                            <input name="family_name" id="family_name" type="text" class="txt to_alpha_num_em ime-on" value=""  maxlength = "10"/>
                            <div class="label" id="lbl_family_name2">家</div>
                            <label for="family_name_kana" class="lbl_family_name_soke option require done to_alpha_num_em ime-on">ひらがな</label>
                            <input name="family_name_kana" id="family_name_kana" type="text" class="txt" value="" maxlength = "10"/>
                            <label for="funeral_style" class="lbl_funeral_style require done">形式</label>
                            <input type="hidden" name="funeral_style" id="funeral_style" class="cls_funeral_style"/>
                            {*                                <label for="keishiki_cd" class="lbl_keishiki_cd option require done">発注住所</label>
                            <input type="hidden" id="keishiki_cd" name="keishiki_cd" class="cls_keishiki_cd"/>
                            <label for="family_tel" class="lbl_family_tel require done">発注TEL</label>
                            <input name="family_tel" id="family_tel" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>*}
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="syushi_cd" class="lbl_religion option require done">宗旨</label>
                            <input type="hidden" id="syushi_cd" name="syushi_cd" class="cls_syushi_cd"/>
                            <label for="syuha_cd" class="lbl_denomination require done">宗派</label>
                            <div id="syuha_nm_div">
                                <input type="hidden" id="syuha_cd" name="syuha_cd" class="cls_syuha_cd" disabled="disabled"/>
                                <input type="text" id="syuha_nm_other" name="syuha_nm_other" class="txt ime-on" maxlength="20"/>
                            </div>
                            <div class="label" id="syuha_knm"></div>
                            <input type="text" id="syuha_knm2" name="syuha_knm2" class="txt ime-on" maxlength="40" placeholder="その他の詳細"/>
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="temple" class="lbl_temple require done">寺院名</label>
                            <input name="temple" id="temple" type="text" class="txt ime-on" value="" maxlength = "30" class="cls_temple" data-kind2 = "1" data-code = "jyusho_cd" data-name = "jyusho_nm" data-kname = "jyusho_knm" readonly="readonly"/>
                            <div class="label dlg_temple cursor-pointer"></div>
                            <input type="text" id="temple_sub" name="temple_sub" class="txt cls_temple_sub" placeholder="寺院マスタにない場合はここに入力"/>
                            <label for="sel_bodaiji" class="lbl_sel_bodaiji require done">菩提寺</label>
                            <input type="hidden" id="sel_bodaiji" name="sel_bodaiji" class="cls_sel_bodaiji" disabled/>
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="bodaiji_kaimyo" class="lbl_bodaiji_kaimyo require done">菩提寺戒名</label>
                            <input type="hidden" id="bodaiji_kaimyo" name="bodaiji_kaimyo" class="cls_bodaiji_kaimyo" disabled/>
                            <label for="tuya_souryo" class="lbl_tuya_souryo require done">通夜僧侶</label>
                            <input name="tuya_souryo" id="tuya_souryo" type="text" class="txt ime-on" value="" maxlength = "30" class="cls_tuya_souryo" placeholder="表示のみ" disabled="disabled"/>
                            <label for="sogi_souryo" class="lbl_sogi_souryo require done">お葬式僧侶</label>
                            <input name="sogi_souryo" id="sogi_souryo" type="text" class="txt ime-on" value="" maxlength = "30" class="cls_sogi_souryo" placeholder="表示のみ" disabled="disabled"/>
                        </fieldset>
                        <fieldset class="base_1">
                            <label for="zoku_name" class="lbl_zoku_name require done">俗名・戒名</label>
                            {*                                <input name="zoku_name" id="zoku_name" type="text" class="txt" value=""  maxlength = "20"/>*}
                            <input type="hidden" name="zoku_kai" id="zoku_kai" class="cls_zoku_kai"/>
                            {*                                <label for="zoku_name" class="lbl_zoku_name require done">戒名</label>*}
                            <input type="hidden" name="kai_name1" id="kai_name1" class="cls_kai_name1" disabled="disabled"  placeholder="戒名の場合は種類を選択"/>
                            {*                                <input type="hidden" name="kai_name2" id="kai_name2" class="cls_kai_name2" disabled="disabled"/>*}
                            {*                   <label for="zoku_name" class="lbl_zoku_name require done">戒名</label>
                            <input name="zoku_name" id="zoku_name" type="text" class="txt" value=""  maxlength = "20"/>
                            <input name="zoku_name" id="zoku_name" type="text" class="txt" value=""  maxlength = "20"/>*}
                            <label for="tera_shokai" class="lbl_tera_shokai option require done">紹介</label>
                            <input type="hidden" id="tera_shokai" name="tera_shokai" class="cls_tera_shokai"/>
                        </fieldset>
                        <fieldset class="funeral_3">
                            <label for="temple_memo" class="lbl_temple_memo require done">寺院依頼書</label>
                            <input type="hidden" id="temple_jyusho_disp" name="temple_jyusho_disp" class="cls_temple_jyusho_disp" placeholder="故人・喪主の連絡先"/>
                            <input type="hidden" id="temple_syuha_disp" name="temple_syuha_disp" class="cls_temple_syuha_disp" placeholder="宗派"/>
                            <input name="temple_memo" id="temple_memo" type="text" class="txt" value=""  maxlength = "100" placeholder="備考"/>
                        </fieldset>
                        <fieldset class="funeral_3">
                            <label for="careful_memo" class="lbl_memo require done">注意事項</label>
                            <textarea name="careful_memo" id="careful_memo" type="text" class="txt careful_memo" value="" placeholder="※社内連絡事項など"/></textarea>
                        </fieldset>
                        <fieldset class="memo memo_mar_f">
                            <label for="memo" class="lbl_memo option">メモ（出棺経路・納骨・親族数など）※葬家連絡事項など</label>
                            <label for="kaiindata" class="lbl_kaiindata option">充当金 (編集不可)</label>
                            <textarea name="memo" id="memo" class="txt memo_mar" cols="1" rows="10" maxlength = "56" placeholder="改行せず最大56文字、2行まで"></textarea>
                            <textarea name="kaiindata" id="kaiindata" class="txt kaiindata" cols="1" rows="10" maxlength = "200" disabled="disabled"></textarea>
                        </fieldset>                            
                    </div>
                    {*喪主タブ*}
                    <div id = "infochief-tab" class = "tab-contents off">
                        <div id="chief" class="info_area">
                            <h3 class = "lbl_replace_moshu">喪主</h3>
                            <fieldset class="person_1 radio_set">
                                <label for="name" class="lbl_name done lbl_replace_moshu">喪主お名前</label>
                                <input name="name" id="name" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <input id = "name_clip"  data-clip ="2" style="display: none" type=file accept="image/*">
                                <div class="label dlg_clip cursor-pointer"></div>
                                <div id = "name_clip_link" class="label"></div>
                                <div id = "name_clip_clear" class = ""></div>
                                <label for="chief_relationship" class="lbl_relationship require done to_alpha_num_em">続柄</label>
                                <input name="s_chief_relationship" id="s_chief_relationship" type="text" class="txt cls_s_chief_relationship" value="" maxlength = "10"/>
                                <label for="chief_relationship2" class="lbl_relationship require done to_alpha_num_em">喪主様からみた続柄</label>
                                <input name="s_chief_relationship2" id="s_chief_relationship2" type="text" class="txt cls_s_chief_relationship" value="" maxlength = "10" placeholder="亡"/>
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="kana" class="lbl_kana require done">ふりがな</label>
                                <input name="kana" id="kana" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                                <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
                                <input name="birthday_date" id="birthday_date" type="text" class="txt date_auto_slash_s to_alpha_num" value="" maxlength = "8" placeholder="00/00/00"/>
                                <input name="birthday_date_y" id="birthday_date_y" type="text" class="txt date_auto_slash to_alpha_num" value="" maxlength = "10" placeholder="0000/00/00" />
                                <div class="label" id="age"></div>
                            </fieldset>
                            <fieldset class="address_1">
                                <div class="lbl_address subtitle option done">現住所
                                    <span class="radio_set">
                                        <input name="print_addr_m" id="print_addr_m" type="checkbox" value="2" />
                                        <label for="print_addr_m" class="lbl_print_address lbl_print_addr_m">礼状住所</label>
                                    </span>
                                    <br />
                                    <span class="radio_set">
                                        <input name="as_address_4" id="as_address_4" type="checkbox" value="1" />
                                        <label for="as_address_4" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                    </span>
                                </div>
                                <label for="zip_1" class="lbl_zip done">〒</label>
                                <input name="zip_1" id="zip_1" type="text" data-zip = "m1" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_1_1" id="address_1_1" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div id="m1" class="label dlg_map map_btn mb1"></div>
                                <label for="tel_1" class="option done">TEL</label>
                                <input name="tel_1" id="tel_1" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_1_2" id="address_1_2" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"  placeholder="ビル、マンション"/>
                                <label for="mobile_tel_1" class="option done">携帯</label>
                                <input name="mobile_tel_1" id="mobile_tel_1" type="text" class="txt ime-off to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                                <label for="mail" class="lbl_mail option">メールアドレス</label>
                                <input name="mail" id="mail" type="text" class="txt to_alpha_num ime-off" value="" maxlength = "60" placeholder="<EMAIL>"/>
                                <input name="btn_search" class="btn_gaiji" type="button" class="my-no-readonly" value="外字確定メール" style="margin-top: 10px;"/>
                            </fieldset>
                            <div id="map_area_1" class="map_area_disappear_1"></div>
                            <!--
                            <fieldset class="address_2">
                                <div class="lbl_address subtitle option sub2">住民登録住所<br />
                                    <span class="radio_set">
                                        <input name="as_address_5" id="as_address_5" type="checkbox" value="1" />
                                        <label for="as_address_5" class="lbl_as_address lbl_as_address_5 lbl_replace_kojin">故人に同じ</label>
                            {*                                            喪主に同じ*}
                            {*                                            <input name="as_address_5_2" id="as_address_5_2" type="checkbox" value="1" />
                            <label for="as_address_5_2" class="lbl_as_address lbl_as_address_5_2">現住所に同じ</label>*}
                        </span>
                    </div>
                    <label for="zip_2" class="lbl_zip done">〒</label>
                    <input name="zip_2" id="zip_2" type="text" data-zip = "m2" class="txt zip_helper"  value="" maxlength = "10"/>
                    <div class="label dlg_zip cursor-pointer"></div>
                    <input name="address_2_1" id="address_2_1" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_map"></div>
                    <label for="tel_2" class="option">TEL</label>
                    <input name="tel_2" id="tel_2" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                    <input name="address_2_2" id="address_2_2" type="text" class="txt" value="" maxlength = "30"/>
                </fieldset>
                <fieldset class="address_3" style="display:none"> --><!-- cstm:sano display:none -->
                            <!-- 
                    <div class="lbl_address subtitle option">本籍<br />
                        <span class="radio_set">
                            <input name="as_address_6" id="as_address_6" type="checkbox" value="1" />
                            <label for="as_address_6" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                        </span>
                    </div>
                    <label for="zip_3" class="lbl_zip done">〒</label>
                    <input name="zip_3" id="zip_3" type="text" data-zip = "m3" class="txt zip_helper"  value="" maxlength = "10"/>
                    <div class="label dlg_zip cursor-pointer"></div>
                    <input name="address_3_1" id="address_3_1" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_map"></div>
                    <input name="address_3_2" id="address_3_2" type="text" class="txt" value="" maxlength = "30"/>
                </fieldset>
                <fieldset class="business_1">
                    <label for="company" class="option">勤務先</label>
                    <input type="hidden" name="employee" id="employee" class="cls_employee"/>
                    <input name="company" id="company" type="text" class="txt" value="" maxlength = "30"/>
                    <label for="company_tel" class="option">TEL</label>
                    <input name="company_tel" id="company_tel" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                </fieldset>
                <fieldset class="business_2">
                    <label for="position" class="option">役職／職種</label>
                    <input name="position" id="position" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_position"></div>
                    <label for="company_fax" class="option">FAX</label>
                    <input name="company_fax" id="company_fax" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                </fieldset>
                            -->
                        </div>
                        <div id="bill" class="info_area">
                            <h3>請求名義</h3>
                            <span class="radio_set">
                                <input name="as_chief" id="as_chief" type="checkbox" value="1" />
                                <label for="as_chief" class="lbl_as_chief lbl_replace_moshu">喪主に同じ</label>
                                <label for="kaishu_ymd" class="lbl_seisan_ymd my-require done">ご精算予定日</label>
                                <input name="kaishu_ymd" id="kaishu_ymd" type="text" class="txt my-type-date cls_kaishu_ymd ime-off  date_auto_slash to_alpha_num" value=""/>
                                <div class="label dlg_date_seisan my-type-date-trg dlg_date" data-ref-rel="#kaishu_ymd"></div>
                                <label for="payment" class="lbl_payment done">お支払方法</label>
                                <input type="hidden" name="payment" id="payment" class="cls_payment" placeholder="(未設定)"/>
                            </span>
                            <fieldset class="person_1 radio_set clip_seikyu">
                                <label for="name" class="lbl_name done">請求先名</label>
                                <input name="name" id="name" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <input id = "name_clip"  data-clip ="3" style="display: none" type=file accept="image/*">
                                <div class="label dlg_clip cursor-pointer"></div>
                                <div id = "name_clip_link" class="label"></div>
                                <div id = "name_clip_clear" class = ""></div>
                                <label for="bill_relationship" class="lbl_bill_relationship require done lbl_replace_moshu">喪主との関係</label>
                                <input type="hidden" name="s_bill_relationship" id="s_bill_relationship" class="cls_s_bill_relationship"/>
                                <input name="bill_relationship_name" id="bill_relationship_name" type="text" class="txt" value="" maxlength = "10"/>
                                {*                                    <div class="label dlg_relationship"></div>*}
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="kana" class="lbl_kana require done">ふりがな</label>
                                <input name="kana" id="kana" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="bill_address">
                                <div class="lbl_address subtitle require done" style="padding-top: 10px;">
                                    <span>現住所</span>
                                    <span class="radio_set">
                                        <input name="print_addr_s" id="print_addr_s" type="checkbox" value="3" />
                                        <label for="print_addr_s" class="lbl_print_address lbl_print_addr_m">礼状住所</label>
                                    </span>
                                    <span class="radio_set">
                                        <input name="sekyu_as_kojin" id="sekyu_as_kojin" type="checkbox" value="1" />
                                        <label for="sekyu_as_kojin" class="lbl_sekyu_as_kojin">故人に同じ</label>
                                    </span>
                                    <br>
                                    {*<span class="radio_set">
                                    <input name="sekyu_addr_kojin" id="sekyu_addr_kojin" type="checkbox" value="1" />
                                    <label for="sekyu_addr_kojin" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                    </span>*}
                                </div>
                                <label for="zip_4" class="lbl_zip done">〒</label>
                                <input name="zip_4" id="zip_4" type="text" data-zip = "s1" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_4_1" id="address_4_1" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div id="m2" class="label dlg_map map_btn mb2"></div>
                                <label for="tel_4" class="done">TEL</label>
                                <input name="tel_4" id="tel_4" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_4_2" id="address_4_2" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"  placeholder="ビル、マンション"/>
                                <label for="mobile_tel_2" class="option done">携帯</label>
                                <input name="mobile_tel_2" id="mobile_tel_2" type="text" class="txt ime-off to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                            </fieldset>
                            <div id="map_area_2" class="map_area_disappear_2"></div>
                            <fieldset class="memo">
                                <div id="print_address_area">
                                    <label for="memo" class="lbl_memo option">備考</label>
                                    <input name="memo" id="memo" type="text" class="txt mosyu_tab" value="" maxlength = "60"/>
                                </div>
                            </fieldset>
                            <fieldset class="memo">
                                <label for="chairman" class="lbl_chairman option done">葬儀委員長</label>
                                <input name="chairman" id="chairman" type="text" class="txt mosyu_tab to_alpha_num_em" value="" maxlength = "100"/>
                            </fieldset>
                        </div>
                        <div id="bill" class="info_area">
                            <fieldset class="memo">
                                <h3>送付先</h3>
                                <span class="radio_set">
                                    <input name="soufu_as_kojin" id="soufu_as_kojin" type="checkbox" value="3" />
                                    <label for="soufu_as_kojin" class="lbl_soufu_as_kojin lbl_replace_moshu">故人に同じ</label>
                                </span>
                                <span class="radio_set">
                                    <input name="soufu_as_chief" id="soufu_as_chief" type="checkbox" value="1" />
                                    <label for="soufu_as_chief" class="lbl_soufu_as_chief lbl_replace_moshu">喪主に同じ</label>
                                </span>
                                <span class="radio_set">
                                    <input name="soufu_as_sekyu" id="soufu_as_sekyu" type="checkbox" value="2" />
                                    <label for="soufu_as_sekyu" class="lbl_soufu_as_sekyu lbl_replace_moshu">請求名義に同じ</label>
                                </span>
                            </fieldset>
                            <fieldset class="person_1 radio_set clip_soufu">
                                <label for="sekyu_soufu_nm" class="lbl_name done">送付先名</label>
                                <input name="sekyu_soufu_nm" id="sekyu_soufu_nm" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <input id = "name_clip"  data-clip ="4" style="display: none" type=file accept="image/*">
                                <div class="label dlg_clip cursor-pointer"></div>
                                <div id = "name_clip_link" class="label"></div>
                                <div id = "name_clip_clear" class = ""></div>
                                <label for="bill_relationship" class="lbl_bill_relationship require done lbl_replace_moshu">喪主との関係</label>
                                <input type="hidden" name="soufu_moshu_kankei_kbn" id="soufu_moshu_kankei_kbn" class="cls_soufu_moshu_kankei_kbn"/>
                                <input name="soufu_moshu_kankei_nm" id="soufu_moshu_kankei_nm" type="text" class="txt" value="" maxlength = "10"/>
                                {*                                    <div class="label dlg_relationship"></div>*}
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="sekyu_soufu_knm" class="lbl_kana require done">ふりがな</label>
                                <input name="sekyu_soufu_knm" id="sekyu_soufu_knm" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="bill_address">
                                <div class="lbl_address subtitle require done" style="padding-top: 10px;">
                                    <span>現住所</span>
                                </div>
                                <label for="soufu_yubin_no" class="lbl_zip done">〒</label>
                                <input name="soufu_yubin_no" id="soufu_yubin_no" type="text" data-zip = "s2" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="soufu_addr1" id="soufu_addr1" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div id="m2" class="label dlg_map map_btn mb2"></div>
                                <label for="soufu_tel" class="done">TEL</label>
                                <input name="soufu_tel" id="soufu_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="soufu_addr2" id="soufu_addr2" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"  placeholder="ビル、マンション"/>
                                <label for="soufu_mobile_tel" class="option done">携帯</label>
                                <input name="soufu_mobile_tel" id="soufu_mobile_tel" type="text" class="txt ime-off to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                            </fieldset>
                        </div>
                        <div id="bill" class="info_area">
                            <h3 class = "lbl_todoke">届出人</h3>
                                <span class="radio_set">
                                    <input name="todoke_as_chief" id="todoke_as_chief" type="checkbox" value="1" />
                                    <label for="todoke_as_chief" class="lbl_todoke_as_chief lbl_replace_moshu">喪主に同じ</label>
                                </span>
                                <span class="radio_set">
                                    <input name="todoke_as_sekyu" id="todoke_as_sekyu" type="checkbox" value="2" />
                                    <label for="todoke_as_sekyu" class="lbl_todoke_as_sekyu lbl_replace_moshu">請求名義に同じ</label>
                                </span>
                            <fieldset class="person_1 radio_set">
                                <label for="todoke_name" class="lbl_name">届出人氏名</label>
                                <input name="todoke_name" id="todoke_name" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <label for="todoke_relationship" class="lbl_relationship">続柄</label>
                                <input name="todoke_zoku" id="todoke_zoku" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="todoke_kana" class="lbl_kana">ふりがな</label>
                                <input name="todoke_kana" id="todoke_kana" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <label for="todoke_birthday_date" class="option">生年月日</label>
                                <input type="hidden" name="todoke_birthday_era" id="todoke_birthday_era" class="cls_todoke_birthday_era"/>
                                <input name="todoke_birthday_date" id="todoke_birthday_date" type="text" class="txt date_auto_slash_s to_alpha_num" value="" maxlength = "8" placeholder="00/00/00"/>
                                <input name="todoke_birthday_date_y" id="todoke_birthday_date_y" type="text" class="txt date_auto_slash to_alpha_num" value="" maxlength = "10" placeholder="0000/00/00" />
                                <div class="label" id="todoke_age"></div>
                            </fieldset>
                            <fieldset class="address_4">
                                <div class="lbl_address subtitle">現住所<br />
                                    <span class="radio_set">
                                        <input name="todoke_as_address_1" id="todoke_as_address_1" type="checkbox" value="1" />
                                        <label for="todoke_as_address_1" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                    </span>
                                </div>
                                <label for="todoke_zip_1" class="lbl_zip done">〒</label>
                                <input name="todoke_zip_1" id="todoke_zip_1" type="text" data-zip = "t1" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="todoke_address_1_1" id="todoke_address_1_1" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="todoke_tel" class="option">TEL</label>
                                <input name="todoke_tel" id="todoke_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="todoke_address_1_2" id="todoke_address_1_2" type="text" class="txt to_alpha_num" value="" maxlength = "30" placeholder="ビル、マンション"/>
                                <label for="todoke_mobile_tel" class="option">携帯</label>
                                <input name="todoke_mobile_tel" id="todoke_mobile_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                            </fieldset>
                            <fieldset class="address_4">
                                <div class="lbl_address subtitle option">本籍<br />
                                    <span class="radio_set">
                                        <input name="todoke_as_address_2" id="todoke_as_address_2" type="checkbox" value="1" />
                                        <label for="todoke_as_address_2" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                    </span>
                                </div>
                                <label for="todoke_zip_3" class="lbl_zip done">〒</label>
                                <input name="todoke_zip_3" id="todoke_zip_3" type="text" data-zip = "t2" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="todoke_address_3_1" id="todoke_address_3_1" type="text" class="txt to_alpha_num_em" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="todoke_head" class="option">筆頭者</label>
                                <input name="todoke_head_nm" id="todoke_head_nm" type="text" class="txt to_alpha_num_em" value="" maxlength = "20"/>
                                <input name="todoke_address_3_2" id="todoke_address_3_2" type="text" class="txt to_alpha_num_em" value="" maxlength = "30" placeholder="ビル、マンション"/>
                            </fieldset>
                        </div>

                        <div id="seko_relationship" class="info_area">
                            {*                                <h3>関係者</h3>*}
                            <div id="relationship_header" class="option done">
                                <ul>
                                    <li class="h_msi_no">No.</li>
                                    <li class="h_kankei_nm">氏名</li>
                                    <li class="h_kankei_age">年齢</li>
                                    <li class="h_kankei_zoku">続柄</li>
                                    <li class="h_kankei_kaisya">勤務先（会社名）</li>
                                    <li class="h_kankei_yakushoku">役職</li>
                                    <li class="h_kankei_zip">郵便番号</li>
                                    <li class="h_kankei_addr1">住所及び連絡先</li>
                                    <li class="h_kankei_tel">電話番号</li>
                                </ul>
                            </div>
                            <div id="relationship_msi" class="option done">
                            </div>

                        </div>
                    </div>

                    {*施行情報タブ*}
                    <div id = "infodate-tab" class = "tab-contents off">
                        <div id="infodate">
                        </div>
                        <div id="add_charge">
                            {*                               <h3>別途費用</h3>
                            <fieldset class="add_charge">
                            <label for="add_place_1" class="lbl_add_place require done">式場使用料</label>
                            <input name="add_place_1" id="add_place_1" type="text" class="txt place" value=""  maxlength = "30" readonly="readonly"/>
                            <div class="label dlg_place add_place_1 cursor-pointer"></div>
                            <input name="total_1" id="total_1" type="text" class="txt add_total" value=""  maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                            <label for="kaikan_use" class="lbl_kaikan_use">会館使用</label>
                            <input type="hidden" id="kaikan_use" class="cls_kaikan_use"/>
                            </fieldset>

                            <fieldset class="add_charge">
                            <label for="add_unei" class="lbl_place require done">施行運営費</label>
                            <input name="add_unei" id="add_unei" type="text" class="txt ime-off" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                            </fieldset>*}
                            {*                                <fieldset class="add_charge">
                            <label for="add_place_2" class="lbl_place require done">通夜会場使用料</label>
                            <input name="add_place_2" id="add_place_2" type="text" class="txt place" value=""  maxlength = "30" readonly="readonly"/>
                            <div class="label dlg_place add_place_2 cursor-pointer"></div>
                            <input name="nights" id="nights" type="text" class="txt nights" value="" maxlength = "3" readonly="readonly"/>
                            <div class="label lbl_nights">泊</div>
                            <input name="price_2" id="price_2" type="text" class="txt add_price" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                            <div class="label lbl_add_eq">＝</div>
                            <input name="total_2" id="total_2" type="text" class="txt add_total" value="" readonly="readonly" />
                            </fieldset>
                            <fieldset class="add_charge">
                            <input name="nights2" id="nights2" type="text" class="txt nights" value="" maxlength = "3" readonly="readonly"/>
                            <div class="label lbl_nights">泊</div>
                            <input name="price_3" id="price_3" type="text" class="txt add_price" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                            <div class="label lbl_add_eq">＝</div>
                            <input name="total_3" id="total_3" type="text" class="txt add_total" value="" readonly="readonly" />
                            </fieldset>*}
                            <fieldset class="tojitsu">
                                <label class="point_label">★「御通夜」「御葬式」の式表記は変更可能です。</label>
                            </fieldset>
                            <fieldset class="tojitsu syonanoka">
                                <label for="shikichu_modori2" class="lbl_shikichu_modori require done">初七日(十日祭)</label>
                                <input type="hidden" name="shikichu_modori2" id="shikichu_modori2" class="cls_shikichu_modori2" placeholder="(未設定)"/>
                                <input name="shikichu_modori" id="shikichu_modori" type="text" class="txt ime-off commafy to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                                <label class="part_label">円</label>
                                <label for="tojitsu_maisou" class="lbl_tojitsu_maisou require done">当日埋葬</label>
                                <input type="hidden" name="tojitsu_maisou" id="tojitsu_maisou" class="cls_tojitsu_maisou" placeholder="(未設定)"/>
                                <input name="tojitsu_free" id="tojitsu_free" type="text" class="txt cls_tojitsu_free" value="" maxlength="60" disabled="disabled"/>
                                <label for="doukou" class="lbl_doukou require done">埋葬／墓同行</label>
                                <input type="hidden" name="doukou" id="doukou" class="cls_doukou" placeholder="(未設定)"/>
                            </fieldset>
                            <fieldset class="tojitsu shiboubi">
                                <label for="shikyu_nichi" class="lbl_shikyu_nichi require done">４９日(５０日祭)</label>
                                <input type="hidden" name="shikyu_nichi" id="shikyu_nichi" class="cls_shikyu_nichi" placeholder="(未設定)"/>
                                <label for="shiboubi_memo" class="lbl_shiboubi_memo require done">死亡日備考</label>
                                <input name="shiboubi_memo" id="shiboubi_memo" type="text" class="txt" value=""  maxlength = "100" placeholder="死亡日時が不明または曖昧な場合は詳細をここに入力"/>
                            </fieldset>
                            <fieldset class="tojitsu">
                                <input name="btn_tachiai" id="btn_tachiai" type="button" class="my-no-readonly" value="立会い美粧スケジュール参照" />
                            </fieldset>
                            <!-- <h3 class = "lbl_replace_microbus">マイクロバス経由</h3>
                            <fieldset class="microbus">
                                <label for="microbus_keiyu_tsuya" class="lbl_microbus_keiyu require done">お通夜</label>
                                <input name="microbus_keiyu_tsuya" id="microbus_keiyu_tsuya" type="text" class="txt cls_microbus_keiyu" value="" maxlength = "60"/>
                            </fieldset>
                            <fieldset class="microbus">
                                <label for="microbus_keiyu_syukan_1" class="lbl_microbus_keiyu require done">出棺１</label>
                                <input name="microbus_keiyu_syukan_1" id="microbus_keiyu_syukan_1" type="text" class="txt cls_microbus_keiyu" value="" maxlength = "60"/>
                            </fieldset>
                            <fieldset class="microbus">
                                <label for="microbus_keiyu_syukan_2" class="lbl_microbus_keiyu require done">出棺２</label>
                                <input name="microbus_keiyu_syukan_2" id="microbus_keiyu_syukan_2" type="text" class="txt cls_microbus_keiyu" value="" maxlength = "60"/>
                            </fieldset> -->
                        </div>
                    </div>
                    {*互助会タブ*}
                    {include file="juchu/customerinfo/gojokai_kanyu_tmpl.tpl"}
                    {*その他*}
                    {*                        <div id = "infomisc-tab" class = "tab-contents off">
                    <div id="misc_1" class="misc">
                    <h3>診断書</h3>
                    <fieldset class="karte_1 radio_set">
                    <div class="lbl_publish subtitle">発行</div>
                    <span id="publish_set">
                    <label for="unfinished" class="lbl_unfinished" style="width: 10%;">未</label><input name="publish" id="unfinished" type="radio" value="2" checked="checked"/>
                    <label for="finished" class="lbl_finished" style="width: 10%;">済</label><input name="publish" id="finished" type="radio" value="1"  />
                    </span>
                    <label for="steps" class="lbl_steps">手続き</label>
                    <input type="hidden" name="steps" id="steps" class="cls_steps"/>
                    <label for="desaki" class="lbl_desaki option">出金先ホール</label>
                    <input name="desaki" id="desaki" type="text" class="txt" value="" maxlength = "" />
                    </fieldset>
                    <fieldset class="karte_2">
                    <label for="datetime" class="lbl_datetime option">発行予定日時</label>
                    <input name="date" id="date" type="text" class="txt  date_auto_slash to_alpha_num" value="" maxlength = "10"/>
                    <div class="label dlg_date"></div>
                    <input name="time" id="time" type="text" class="txt time_auto_colon to_alpha_num" value="" maxlength = "5"/>
                    <div class="label dlg_time"></div>
                    <label for="copy" class="lbl_copy option">コピー</label>
                    <input type="hidden" name="copys" id="copys" class="cls_copys"/>
                    </fieldset>
                    </div>
                    <div id="misc_2" class="misc">
                    <h3>搬送業務</h3>
                    <fieldset class="transport_1">
                    <div class="lbl_transport subtitle option">搬送</div>
                    <span id="transport_set" class="radio_set">
                    <label for="transport_yes" class="lbl_transport_check">有</label><input name="transport" id="transport_yes" type="radio" value="1" />
                    <label for="transport_no" class="lbl_transport_check">無</label><input name="transport" id="transport_no" type="radio" value="2" checked="checked" />
                    </span>
                    </fieldset>
                    <fieldset class="transport_4">
                    <label for="crematorium" class="lbl_crematorium">火葬場</label>
                    <input name="crematorium" id="crematorium" type="text" class="txt" value="" maxlength = "30" data-kind2 = "3" data-code = "kasoba_cd" data-name = "kasoba_nm" readonly="readonly"/>
                    <div class="label dlg_crematorium cursor-pointer"></div>
                    </fieldset>
                    </div>
                    <div id="misc_6" class="misc">
                    <h3>お預り品</h3>
                    <fieldset class="deposit_1">
                    <label for="certificate" class="lbl_certificate option">死亡診断書</label>
                    <input type="hidden" name="certificate" id="certificate" class="cls_certificate"/>
                    <div class="lbl_stamp subtitle option">印鑑</div>
                    <span id="stamp_set" class="radio_set">
                    <input name="stamp" id="stamp" type="checkbox" value="1" /><label for="stamp" class="lbl_stamp_check"></label>
                    </span>
                    </fieldset>
                    <fieldset class="deposit_1">
                    <label for="portrait" class="lbl_portrait option">御写真</label>
                    <input type="hidden" name="portrait" id="portrait" class="cls_portrait"/>
                    <div id="portrait_up" class="file_upload">
                    <input name="file" id = "file_1" class = "file_up" style="display: none" type = 'file' accept="image/*">
                    <div id = "file_link_portrait" class="file_link label">
                    <div class = "file_name"></div>
                    <div id = "file_clear_1" class = "file_clear"></div>
                    </div>
                    <div id = "file_clip_portrait" class="file_clip label dlg_clip cursor-pointer"></div>
                    </div>
                    </fieldset>
                    <fieldset class="deposit_4">
                    <label for="membership" class="lbl_membership option">その他</label>
                    <input name="membership" id="membership" type="text" class="txt" value="" maxlength = "30" />
                    </fieldset>
                    </div>
                    <div id="misc_7" class="misc">
                    <h3>立替金</h3>
                    <fieldset class="custody_1">
                    <label for="hall" class="lbl_hall option">ホール</label>
                    <input name="hall" id="hall_name" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_place dlg_hall_name cursor-pointer"></div>
                    <!--<div class="label dlg_pickup_name3 cursor-pointer"></div>-->
                    <label for="uchikin" class="lbl_uchikin option">内金</label>
                    <input name="uchikin" id="uchikin" type="text" class="txt ime-off my-select-all commafy" style="text-align:right;" value="" maxlength = "10"/>
                    <label style="border: none; background-color: transparent;" >円</label>
                    </fieldset>
                    <fieldset class="custody_2">
                    <label for="price" class="lbl_price option">金額</label>
                    <input type="hidden" name="price" class="cls_price"/>
                    <input name="price" id="price" type="text ime-off" class="txt ime-off my-select-all commafy" style="text-align:right;" value="" maxlength = "10"/>
                    <label style="border: none; background-color: transparent;" >円</label>
                    </fieldset>
                    <fieldset class="custody_3">
                    <label for="staff_name" class="lbl_staff_name option">担当者</label>
                    <input name="staff_name" id="staff_name" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_staff dlg_staff_name cursor-pointer"></div>
                    </fieldset>
                    </div>
                    <div id="misc_8" class="misc">
                    <h3>当日</h3>
                    <fieldset class="today_1 etc_1">
                    <label for="temple_2" class="lbl_temple_2 option">お寺</label>
                    <input name="temple_2" id="temple_2" type="text" class="txt ime-off my-select-all commafy" style="text-align:right;" value="" maxlength = "10"/>
                    <label style="border: none; background-color: transparent;" >円</label>
                    </fieldset>
                    </div>
                    <div id="misc_9" class="misc">
                    <h3>訪問承諾</h3>
                    <fieldset class="visit_consent">
                    <label for="pamphlet_setsumei" class="lbl_pamphlet_setsumei option">パンフ送付説明</label>
                    <input type="hidden" name="pamphlet_setsumei" id="pamphlet_setsumei" class="cls_pamphlet_setsumei" placeholder="(未設定)"/>
                    </fieldset>
                    <fieldset class="visit_consent">
                    <label for="butsudan" class="lbl_butsudan option">仏壇</label>
                    <input type="hidden" name="butsudan" id="butsudan" class="cls_butsudan" placeholder="(未設定)"/>
                    <label for="visit_consent" class="lbl_visit_consent">訪問承諾</label>
                    <input type="hidden" name="visit_consent" id="visit_consent" class="cls_visit_consent" placeholder="(未設定)"/>
                    <label for="butsudan_kaikae" class="lbl_butsudan_kaikae option">買換希望</label>
                    <input type="hidden" name="butsudan_kaikae" id="butsudan_kaikae" class="cls_butsudan_kaikae" placeholder="(未設定)" disabled="disabled"/>
                    </fieldset>
                    <fieldset class="visit_consent">
                    <label for="boseki" class="lbl_boseki option">墓石</label>
                    <input type="hidden" name="boseki" id="boseki" class="cls_boseki" placeholder="(未設定)"/>
                    <label for="visit_consentb" class="lbl_visit_consentb">訪問承諾</label>
                    <input type="hidden" name="visit_consentb" id="visit_consentb" class="cls_visit_consentb" placeholder="(未設定)"/>
                    </fieldset>
                    <fieldset class="visit_consent">
                    <label for="nokotsusaki_umu" class="lbl_nokotsusaki_umu">納骨先</label>
                    <input type="hidden" name="nokotsusaki_umu" id="nokotsusaki_umu" class="cls_nokotsusaki_umu" placeholder="(未設定)"/>
                    <label for="visit_consentn" class="lbl_visit_consentn">訪問承諾</label>
                    <input type="hidden" name="visit_consentn" id="visit_consentn" class="cls_visit_consentn" placeholder="(未設定)"/>
                    <label for="nokotsusaki" class="lbl_nokotsusaki">納骨先</label>
                    <input name="nokotsusaki" id="nokotsusaki" type="text" class="txt cls_nokotsusaki" value="" maxlength="60" disabled="disabled"/>
                    </fieldset>
                    <fieldset class="visit_consent">
                    <label for="memo_2" class="lbl_memo_2 option">備考</label>
                    <input name="memo_2" id="memo_2" type="text" class="txt" value="" maxlength="60"/>
                    </fieldset>
                    </div>
                    <div id="misc_10" class="misc">
                    <h3>その他</h3>
                    <fieldset class="visit_consent">
                    <label for="bereaved" class="lbl_bereaved option">遺族・親族</label>
                    <input type="hidden" name="bereaved" class="cls_bereaved"/>
                    <input name="bereaved" id="bereaved" type="text" class="txt ime-off my-select-all commafy" style="text-align:right;" value="" maxlength = "10"/>
                    <label style="border: none; background-color: transparent;" >名位</label>
                    </fieldset>
                    </div>
                    <div id="misc_4" class="misc" style="display:none">
                    <h3>隣組</h3>
                    <fieldset class="neighborhood_1">
                    <label for="association" class="lbl_association option">町内会名</label>
                    <input name="association" id="association" type="text" class="txt" value="" maxlength = "30"/>
                    <div class="label dlg_association"></div>
                    </fieldset>
                    <fieldset class="neighborhood_2">
                    <label for="leader" class="lbl_leader option">隣組長様</label>
                    <input name="leader" id="leader" type="text" class="txt" value="" maxlength = "30"/>
                    <input type="text" name="doors" id="doors" class="txt cls_doors" maxlength = "2"/>
                    <div class="label lbl_name_sama">軒</div>
                    <input type="text" name="persons" id="persons" class="txt cls_persons" maxlength = "2"/>
                    <div class="label lbl_name_sama">名</div>
                    </fieldset>
                    <fieldset class="neighborhood_3">
                    <label for="area" id="lbl_area" class="lbl_area option">地区</label>
                    <input type="hidden" id="area" class="cls_area"/>
                    <label for="group" id="lbl_group" class="lbl_group option">組</label>
                    <input type="hidden" id="group" class="cls_group"/>
                    </fieldset>
                    </div>
                    <div id="misc_5" class="misc" style="display:none">
                    <h3>目録手配</h3>
                    <fieldset class="catalog_1 radio_set">
                    <div class="lbl_catalog subtitle option">依頼</div>
                    <span id="catalog_set">
                    <label for="catalog_yes" class="lbl_catalog_check">有</label><input name="catalog" id="catalog_yes" type="radio" value="1"  />
                    <label for="catalog_no" class="lbl_catalog_check">無</label><input name="catalog" id="catalog_no" type="radio" value="2" checked="checked" />
                    </span>
                    </fieldset>
                    <fieldset class="catalog_2">
                    <label for="catalog_area" class="lbl_catalog_area option">対象エリア</label>
                    <input type="hidden" name="catalog_area" id="catalog_area" class="cls_catalog_area"/>
                    </fieldset>
                    <fieldset class="catalog_3">
                    <label for="catalog_item" class="lbl_catalog_item option">手配品目</label>
                    <input type="hidden" name="catalog_item" id="catalog_item" class="cls_catalog_item"/>
                    </fieldset>
                    <fieldset class="catalog_4">
                    <label for="catalog_datetime" class="lbl_datetime option">搬入日時</label>
                    <input name="catalog_date" id="catalog_date" type="text" class="txt ime-off" value="" />
                    <div class="label dlg_date"></div>
                    <input name="catalog_time_from" id="catalog_time_from" type="text" class="txt ime-off time_auto_colon" value="" />
                    <div class="label dlg_time" id="lbl_dlg_time_from"></div>
                    <div class="label lbl_catalog_time">?</div>
                    <input name="catalog_time_to" id="catalog_time_to" type="text" class="txt ime-off time_auto_colon" value="" />
                    <div class="label dlg_time"></div>
                    </fieldset>
                    <fieldset class="catalog_5">
                    <label for="delivery" class="lbl_catalog_place">搬入場所</label>
                    <input type="hidden" id="delivery" class="cls_delivery"/>
                    <input name="catalog_place" id="catalog_place" type="text" class="txt" value="" maxlength="30"/>
                    <div class="label dlg_catalog_place cursor-pointer"></div>
                    </fieldset>
                    <fieldset class="catalog_6">
                    <label for="delivery_memo_cd" class="lbl_catalog_memo">備考</label>
                    <input type="hidden" id="delivery_memo_cd" class="cls_delivery_memo_cd"/>
                    </fieldset>
                    <fieldset class="catalog_7 foot-margin-bottom">
                    <textarea name="delivery_memo" id="delivery_memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                    </fieldset>
                    </div>
                    </div>*}
                    {*貸出備品タブ*}
                    {if $kashidashi_disp == 1}
                        <div id = "inforental-tab" class = "tab-contents off">
                            <div id="items">
                                <ul>
                                    <div id="kashidasi-bihin">
                                    </div>
                                    <li class="add"><a href="javascript:void(0)" id = "item_add" class="item_ad">+</a></li>
                                </ul>
                            </div>
                        </div>
                    {/if}

                    {*報告書タブ*}
                    {include file="juchu/customerinfo/report_tmpl.tpl"}
                </div>
                <div class="buttons">
{*                    <input type="button" name="btn_save" id="btn_save" value="保存" />*}
{*                    <input type="button" name="btn_print" id="btn_print" value="印刷" />*}
                    {*                    <input type="button" name="btn_print_m_new" id="btn_print_m_new" value="印刷(工事中)" style="display: none;" />*}
{*                    <input type="button" name="btn_delete" id="btn_delete" value="削除" />*}
{*                    <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />*}
                </div>
            </div>
        </div>
</div>
</form>
</div>

{literal}
    <!-- 日程タブ 亡日テンプレート -->
    <script type="text/template" id="tmpl-nitei-1">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" data-kind2 = "5" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 湯灌テンプレート -->
    <script type="text/template" id="tmpl-nitei-2">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn basho_veryshort_nm" value="" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">立会</label>
        <input type="hidden" class="sutra tachiai" placeholder="(未設定)"/>
    </script>
    <!-- 日程タブ 納棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-3">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn basho_veryshort_nm" value=""  maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">立会</label>
        <input type="hidden" class="sutra tachiai" id="nokanval" placeholder="(未設定)"/>
    </script>
    <!-- 日程タブ 通夜テンプレート(名称編集不可) -->
    <script type="text/template" id="tmpl-nitei-4">
        <input type="text" class="txt ts_based_nm" value="" maxlength = "20" readonly="readonly" /><label class="lbl_star"><span class="star_color">★変更可</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt time nitei_ed_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn basho_short_nm" value="" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd sikijo" placeholder="(未設定)"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
    </script>
    <!-- 日程タブ 通夜テンプレート -->
    <script type="text/template" id="tmpl-nitei-4-2">
        <input type="text" class="txt ts_based_nm" value="" maxlength = "20"/><label class="lbl_star"><span class="star_color">★変更可</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10" disabled="disabled"/>
        <div class="label tsuya_date dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5" disabled="disabled"/>
        <div class="label tsuya_time dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd" disabled="disabled"/>
        <input type="text" class="txt place basho_nm haskbn basho_short_nm" value="" maxlength = "30" readonly="readonly" disabled="disabled"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd sikijo" placeholder="(式場を選択)" disabled="disabled"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
    </script>
    <!-- 日程タブ 出棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-5">
        <label class="lbl_date done"><%=ts_based_nm%><span class="star_color">★</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 火葬テンプレート -->
    <script type="text/template" id="tmpl-nitei-6">
        <label class="lbl_date done"><%=ts_based_nm%><span class="star_color">★</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm kaso_place" value="" data-kind2 = "3" maxlength = "30" readonly="readonly" placeholder="(火葬場を選択)"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">炉前読経</label>
        <input type="hidden" class="sutra kaso_kyo"/>
    </script>
    <!-- 日程タブ 告別式テンプレート -->
    <script type="text/template" id="tmpl-nitei-7">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value=""  data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 初七日テンプレート -->
    <script type="text/template" id="tmpl-nitei-8">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn shonanoka_place" value="" data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">法事</label>
        <input type="hidden" class="sutra nanoka"/>
    </script>
    <!-- 日程タブ 葬儀テンプレート(名称編集不可) -->
    <script type="text/template" id="tmpl-nitei-11">
        <input type="text" class="txt ts_based_nm" value="" maxlength = "20" readonly="readonly" /><label class="lbl_star"><span class="star_color">★変更可</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt time nitei_ed_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn basho_short_nm" value=""  data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd sikijo" placeholder="(未設定)"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
    </script>
    <!-- 日程タブ 葬儀テンプレート -->
    <script type="text/template" id="tmpl-nitei-11-2">
        <input type="text" class="txt ts_based_nm" value="" maxlength = "20"/><label class="lbl_star"><span class="star_color">★変更可</span></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date sougi_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time sougi_time"></div>
        <input type="text" class="txt time nitei_ed_time time_auto_colon to_alpha_num nitei_short_time" value="" maxlength = "5"/>
        <div class="label dlg_time sougi_ed_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn basho_short_nm" value=""  data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd sikijo" placeholder="(式場を選択)"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
    </script>
    <!-- 日程タブ お迎え搬送テンプレート -->
    <script type="text/template" id="tmpl-nitei-13">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label omukae_date dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label omukae_time dlg_time"></div>
        <label for="hanso_kbn1" class="lbl_iso_kbn require done">担当者</label>
        <input type="hidden" name="hanso_kbn1" id="hanso_kbn1" class="iso_kbn"/>
        <input name="hanso1" id="hanso1" type="text" class="txt hanso1 hanso" value="" maxlength = "20" readonly="readonly"/>
        <div class="label dlg_staff dlg_staff5 cursor-pointer"></div>
        <input name="hanso2" id="hanso2" type="text" class="txt hanso2 hanso" value="" maxlength = "20" readonly="readonly"/>
        <div class="label dlg_staff dlg_staff6 cursor-pointer"></div>
    </script>
    <!-- 日程タブ 移動搬送テンプレート -->
    <script type="text/template" id="tmpl-nitei-14">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon to_alpha_num" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <label for="hanso_kbn2" class="lbl_iso_kbn done">担当者</label>
        <input type="hidden" name="hanso_kbn1" id="hanso_kbn1" class="iso_kbn"/>
        <input name="hanso1" id="hanso1" type="text" class="txt hanso1 hanso" value="" maxlength = "20" readonly="readonly"/>
        <div class="label dlg_staff dlg_staff5 cursor-pointer"></div>
        <input name="hanso2" id="hanso2" type="text" class="txt hanso2 hanso" value="" maxlength = "20" readonly="readonly"/>
        <div class="label dlg_staff dlg_staff6 cursor-pointer"></div>
    </script>
    <!-- 貸出備品テンプレート -->
    <script type="text/template" id="tmpl-kashidasi-bihin">
        <div class = "delete"></div>
        <% if (nm_input_kbn == '2' || nm_input_kbn == '4') { %>
        <input class="name nm_input" maxlength = "30"></input>
        <% } else { %>
        <span class="name"></span>
        <% }%>
        <span class="num">0</span>
        <span class="minus">-</span>
        <span class="plus">+</span>
        <fieldset class="f_return">
        <label class="lbl_return">回収<br />予定</label>
        <input class="return txt" />
        </fieldset>
    </script>
    <!-- 関係者テンプレート -->
    <script type="text/template" id="tmpl-kankei">
        <input type="text" class="txt msi_no" value="" readonly="readonly"/>
        <input type="text" class="txt kankei_nm" value="" maxlength = "20"/>
        <input type="text" class="txt kankei_age to_alpha_num ime-off" value="" maxlength = "3"/>
        <div class="label lbl_name_sama lbl_name_age">歳</div>
        <input type="text" class="txt kankei_zoku" value="" maxlength = "10"/>
        <input type="text" class="txt kankei_kaisya" value="" maxlength = "30"/>      
        <input type="text" class="txt kankei_yakushoku" value="" maxlength = "20"/>       
        <input type="text" class="txt kankei_zip to_alpha_num ime-off" value="" maxlength = "10"/>    
        <div class="label dlg_place cursor-pointer"></div>       
        <input type="text" class="txt kankei_addr1 to_alpha_num" value="" maxlength = "40"/>     
        <input type="text" class="txt kankei_tel to_alpha_num ime-off" value="" maxlength = "15" placeholder="************"/>    
    </script>
{/literal}

<script id="data-customerinfo" type="application/json">
    {$customerinfo_json|smarty:nodefaults}
</script>
{include file="fdn_footer_std.tpl"}