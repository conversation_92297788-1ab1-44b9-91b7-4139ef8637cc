.comp-cart-div .container .inner .contents.empty {
  padding-top: 20px;
}
.comp-cart-div .container .inner .contents table {
  min-width: 400px;
  margin: 0 auto;
  font-size: 1.1rem;
  border-collapse: collapse;
}
.comp-cart-div .container .inner .contents table tr.divided {
  border-bottom: solid 1px #bdbdb8;
}
.comp-cart-div .container .inner .contents table tr.divided:last-child {
  border-bottom-width: 2px;
}
.comp-cart-div .container .inner .contents table td {
  vertical-align: top;
  text-align: left;
  padding-top: 6px;
  padding-bottom: 6px;
}
.comp-cart-div .container .inner .contents table td.name {
  min-width: 150px;
  padding-left: 15px;
}
.comp-cart-div .container .inner .contents table td.name .item_name {
  font-size: 1rem;
}
.comp-cart-div .container .inner .contents table td.name .item_name.remark::after {
  margin-left: 2px;
  content: "*";
}
.comp-cart-div .container .inner .contents table td.name .system_name {
  font-size: 1rem;
}
.comp-cart-div .container .inner .contents table td.quantity {
  min-width: 80px;
}
.comp-cart-div .container .inner .contents table td.price {
  min-width: 100px;
  text-align: right;
}
.comp-cart-div .container .inner .contents table td.price .system_price {
  font-size: 1rem;
}
.comp-cart-div .container .inner .contents table td.operate {
  width: 100px;
  padding-left: 30px;
}
.comp-cart-div .container .inner .contents table td.operate.sp {
  display: none;
}
.comp-cart-div .container .inner .contents table td.operate .button-area {
  margin-bottom: 0;
  font-size: 0.8rem;
  justify-content: flex-end;
}
.comp-cart-div .container .inner .contents table td.operate .button-area .button {
  width: 60px;
  height: 25px;
  line-height: 21px;
  margin-right: 10px;
  border-radius: 30px;
}
.comp-cart-div .container .inner .contents table td.operate .button-area .button.grey.light {
  background-color: #e9e9e9;
  color: black;
}
@media screen and (max-width: 560px) {
  .comp-cart-div .container .inner .contents table td.operate.sp {
    display: table-cell;
  }
  .comp-cart-div .container .inner .contents table td.operate.pc {
    display: none;
  }
}
.comp-cart-div .container .inner .contents table td.expired {
  padding-left: 15px;
  font-size: 0.7rem;
  color: #b86b8b;
}
.comp-cart-div .container .inner .contents table.summary tr {
  border-bottom: none;
  font-size: 0.95rem;
}
.comp-cart-div .container .inner .contents table.summary tr td.summary_name {
  position: relative;
  min-width: 200px;
  padding-left: 100px;
}
.comp-cart-div .container .inner .contents table.summary tr td.summary_name.remark::before {
  position: absolute;
  left: 90px;
  content: "*";
}
.comp-cart-div .container .inner .contents table.summary tr td.price {
  padding-right: 10px;
}
.comp-cart-div .container .inner .contents table.summary tr.total .summary_name {
  font-size: 1.2rem;
}
.comp-cart-div .container .inner .contents table.summary tr.total .price {
  font-size: 1.1rem;
}
@media screen and (max-width: 560px) {
  .comp-cart-div .container .inner .contents table {
    min-width: 90%;
  }
  .comp-cart-div .container .inner .contents table td.name {
    min-width: 100px;
    padding-left: 15px;
  }
  .comp-cart-div .container .inner .contents table td.name .item_name {
    font-size: 1rem;
  }
  .comp-cart-div .container .inner .contents table td.quantity {
    min-width: 60px;
  }
  .comp-cart-div .container .inner .contents table td.price {
    min-width: 70px;
    text-align: right;
  }
  .comp-cart-div .container .inner .contents table td.operate {
    width: 80px;
  }
  .comp-cart-div .container .inner .contents table.summary tr {
    border-bottom: none;
    font-size: 0.9rem;
  }
  .comp-cart-div .container .inner .contents table.summary tr td.summary_name {
    padding-left: 50px;
  }
  .comp-cart-div .container .inner .contents table.summary tr td.summary_name.remark::before {
    left: 42px;
  }
  .comp-cart-div .container .inner .contents table.summary tr td.price {
    padding-right: 10px;
  }
  .comp-cart-div .container .inner .contents table.summary tr.total .summary_name {
    font-size: 1.1rem;
  }
  .comp-cart-div .container .inner .contents table.summary tr.total .price {
    font-size: 1rem;
  }
}

.comp-cart-div .container .inner > .button-area {
  max-width: calc(100% - 40px);
  margin: auto;
  display: block;
  width: 350px;
}
.comp-cart-div .container .inner > .button-area .button {
  width: 100%;
  margin: 0 auto 10px;
}


.comp-cart-div .container .inner .contents table tr.nafuda-tr-top {
    border-top: 1px grey dotted;
}
.comp-cart-div .container .inner .contents table tr.nafuda-tr-biko {
    border-bottom: 2px grey solid;
}

.comp-cart-div .container .inner .contents table td.nafuda {
  min-width: 150px;
  padding-left: 15px;
}
.comp-cart-div .container .inner .contents table td.nafuda_val {
  font-size: 1rem;
  padding-right: 6px;
  word-break: break-all;
}

.comp-cart-div .container .inner .contents table td.biko {
  min-width: 150px;
  padding-left: 15px;
}
.comp-cart-div .container .inner .contents table td.biko_val {
  font-size: 1rem;
  padding-right: 6px;
  word-break: break-all;
}
