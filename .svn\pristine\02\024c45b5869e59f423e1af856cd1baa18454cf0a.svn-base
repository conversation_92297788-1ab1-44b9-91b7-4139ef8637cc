<?php
/**
 * Logic_ExtKyoka_ImpExtKyokaAbst
 *
 * 外部供花注文FDN取込 CSV 登録抽象クラス
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2021/11/xx
 * @filesource
 */

/**
 * 外部供花注文FDN取込 CSV 登録抽象クラス
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2021/11/xx
 */
abstract class Logic_ExtKyoka_ImpExtKyokaAbst extends Msi_Logic_InCsv
{
    /**
     * delete 処理タイプ
     */
    const DELETE_REC_TYPE = 'delete';

    // 対象テーブル名
    protected $_myTableName = 'dummy';

    // 対象処理名
    protected $_myImpKind = '???';

    // 現在日時
    protected $_cur_datetime = null;

    /**
     * 現在日時を取得する
     * <AUTHOR> Mihara
     * @since  2020/12/xx
     * @return string (yyyy/mm/dd hh:mi)
     */
    protected function getCurDatetime() {
        if ( $this->_cur_datetime === null ) {
            $this->_cur_datetime = $this->db()->getOneVal( "select TO_CHAR(CURRENT_TIMESTAMP, 'YYYY/MM/DD hh24:mi:ss')" );
        }
        return $this->_cur_datetime;
    }

    /**
     * タイトル行でない(実データ)と思われるなら真を返す
     *
     * <AUTHOR> Mihara
     * @since      2013/09/30
     * @param      array $data
     * @param      string $recType  処理タイプ
     * @return     boolean
     */
    protected function isNotTitleLine($data, $recType)
    {
        return false; // タイトル行必須
    }

    /**
     * 入力データをもとに、処理タイプを判断して返す
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      array       $data  入力データ
     * @return     string|null  string:処理タイプ, null(スキップ)
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function checkRecType($data)
    {
        if ( isset($data[0]) && Msi_Sys_Utils::beginsWith($data[0], '#') ) { // skip
            return null;
        }

        // if ( isset($data[0]) && $data[0] == 3 ) { // 3:delete
        //     return static::DELETE_REC_TYPE;
        // }

        return static::DEFAULT_REC_TYPE;
    }

    /**
     * 追加初期処理<br>
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @return     void
     */
    protected function hookPreMainLoop()
    {
        $db = $this->db();

        // 入力パラメタ設定
        $ufp_id = $this->getConfig('ufp_id'); // univ_file_pool.file_id
        $my_inFileName = $this->getConfig('inFileName'); // 入力時ファイル名
        $client_type   = $this->getConfig('client_type'); // 端末タイプ

        $this->_myInput_ufp_id      = $ufp_id;
        $this->_myInput_inFileName  = $my_inFileName;
        $this->_myInput_client_type = $client_type;

        $this->result['my_imp_kind']    = $this->_myImpKind;
        $this->result['my_ufp_id']      = $this->_myInput_ufp_id;
        $this->result['my_inFileName']  = $this->_myInput_inFileName;
        $this->result['my_client_type'] = $this->_myInput_client_type;

        $this->result['cnt_nop']   = 0; // 連動済みなどでスキップした件数

        return parent::hookPreMainLoop();
    }

    /**
     * 追加終了処理(commit前)
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @return     boolean
     */
    protected function hookPreCommit()
    {
        $db = $this->db();

        $imp_kind     = $this->_myImpKind; // 種類
        // $shori_cnt = $this->inRecCnt; // 入力行数
        $shori_cnt    = $this->outRecCnt; // (正常)処理件数
        $inFileName   = $this->_myInput_inFileName; // 入力時ファイル名
        $ufp_id       = $this->_myInput_ufp_id; // univ_file_pool.file_id
        $client_type  = $this->_myInput_client_type; // 端末タイプ

        $shori_result = sprintf( "入力:%s件　スキップ:%s件",
                                 Msi_Sys_Utils::filterComma($this->outRecCnt),
                                 Msi_Sys_Utils::filterComma($this->result['cnt_nop']) );

        // 取込履歴情報出力
        $cnt = $db->easyExecute( <<< END_OF_SQL
INSERT INTO upload_data_history(data_kbn, v_free1, v_free2, ts_free1, v_free3, v_free4, v_free5)
       VALUES(:data_kbn, :imp_kind, :shori_result, clock_timestamp(), :inFileName, :ufp_id, :client_type)
END_OF_SQL
                                 , array( 'data_kbn' => Logic_ExtKyoka_Utils::UPLOAD_DATA_HISTORY_DATA_KBN,
                                          'imp_kind'=>$imp_kind, 'shori_result'=>$shori_result,
                                          'inFileName' => $inFileName, 'ufp_id' => $ufp_id, 'client_type' => $client_type ) );
        // 処理開始時なら clock_timestamp() を CURRENT_TIMESTAMP にする

        $rtn = $cnt === 1;
        return $rtn;
    }

    /**
     * 登録データ追加的チェック
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @return     void
     * @throws     Msi_Logic_ExceptionLogic   エラーがある場合は例外送出
     */
    protected function _checkInsData()
    {
    }

    /**
     * フィルタメソッド: 前ゼロを加えて2桁にする
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function filterPadZero2($val, $rec=null)
    {
        $val = preg_replace('/(\s|　)+/u', '', $val);
        if ( strlen($val) <= 0 || $val == '0' ) {
            return null;
        }
        $val = sprintf("%02d", $val); // 前ゼロで2桁化
        return $val;
    }

    /**
     * 郵便番号フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2017/05/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function myFilterYubinNo($val, $rec=null)
    {
        $val = Msi_Sys_Utils::trim( $val );

        if ( strlen($val) > 0 ) {
            $val = mb_convert_kana( $val, 'a', "UTF-8" ); // 「全角」英数字を「半角」に変換します
        }

        if ( preg_match('/^(\d{3})(\d{4})$/', $val, $m) ) { // xxx-xxxx にする
            $val = sprintf("%s-%s", $m[1], $m[2]);
        }

        return $val;
    }

    /**
     * 電話番号フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2017/05/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function myFilterTel($val, $rec=null)
    {
        $val = Msi_Sys_Utils::trim( $val );

        if ( strlen($val) > 0 ) {
            $val = mb_convert_kana( $val, 'a', "UTF-8" ); // 「全角」英数字を「半角」に変換します
        }

        return $val;
    }

    /**
     * 数値フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2017/05/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function myFilterNum($val, $rec=null)
    {
        $val = Msi_Sys_Utils::trim( $val );

        $val = preg_replace('/,/', '', $val); // , をすべて削除

        return $val;
    }

    /**
     * 日付フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2017/05/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function myFilterDate($val, $rec=null)
    {
        $val = Msi_Sys_Utils::trim( $val );

        if ( strlen($val) <= 0 ) {
            $val = null;
        } else if ( Msi_Sys_Utils::isValid_DATE2($val) ) {
            $val = Msi_Sys_Utils::normYYYYMMDD2($val, '/');
        }

        return $val;
    }

    /**
     * 日時フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2017/05/xx
     * @param      mixed
     * @param      array
     * @return     mixed
     */
    protected function myFilterDatetime($val, $rec=null)
    {
        $val = Msi_Sys_Utils::trim( $val );

        if ( strlen($val) <= 0 ) {
            $val = null;
        } else { // if ( Msi_Sys_Utils::isValid_DATETIME2($val) ) {
            @ list($dt, $tm) = explode(' ', $val);
            if ( $dt ) {
                $dt = Msi_Sys_Utils::normYYYYMMDD2($dt, '/');
                if ( $dt !== null ) {
                    $val = $dt . ' ' . $tm;
                }
            }
        }

        return $val;
    }


    /**
     * 会社コードチェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myKaisyaCdChecker($val, $rec)
    {
        $kaisya_cd = $val;

        if ( $this->_myInput_yosan_kaisya_cd != $kaisya_cd ) {
            $msg = sprintf("会社コード(%s)が画面の会社(%s)と一致しません", $val, $this->_myInput_yosan_kaisya_cd);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 部門コードチェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myBumonCdChecker($val, $rec)
    {
        $bumon_cd = $val;

        $db = $this->db();
        $cond = array( 'bumon_cd' => $bumon_cd,
                       'bumon_kbn' => 1 ); // 部門区分　1:部門   (not: 0:親部門)
        if ( ! $db->isRefDataExists('bumon_mst', $cond ) ) {
                $msg = sprintf("部門コード(%s)が不正です", $bumon_cd);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 予算年度チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myYosanNendoChecker($val, $rec)
    {
        $yosan_year_int = +$val;
        $yosan_nendo_0 = $this->_myInput_yosan_nendo;
        $yosan_nendo_1 = $yosan_nendo_0 + 1;

        if ( ! in_array($yosan_year_int, [$yosan_nendo_0, $yosan_nendo_1], true) ) {
            $msg = sprintf("予算年度(%s)と画面の年度(%d)が対応しません", $val, $yosan_nendo_0);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 予算月度チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myYosanGetsudoChecker($val, $rec)
    {
        $yosan_month = $val;

        if ( ! in_array($yosan_month, ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'], true) ) {
            $msg = sprintf("予算月度(%s)が不正です", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 会計年度チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myFiscalYearChecker($val, $rec)
    {
        $fiscal_year = +$val;
        $yosan_nendo = $this->_myInput_yosan_nendo;

        if ( $fiscal_year != $yosan_nendo ) {
            $msg = sprintf("会計年度(%s)が画面の年度(%d)と一致しません", $val, $yosan_nendo);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 会計月度チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myFiscalMonthChecker($val, $rec)
    {
        $fiscal_month = +$val;

        if ( $fiscal_month < 1 || $fiscal_month > 12 ) {
            $msg = sprintf("会計月度(%s)が不正です", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 件数チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myKensuChecker($val, $rec)
    {
        $kensu = +$val;

        if ( $kensu < 0 ) {
            $msg = sprintf("マイナス値の件数(%d)は不可です", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        if ( $kensu > 999999999999 ) {
            $msg = sprintf("件数(%d)は12桁以内である必要があります", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * 金額チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myKingakuChecker($val, $rec)
    {
        $kingaku = +$val;

        if ( $kingaku < 0 ) {
            $msg = sprintf("マイナス値の金額(%d)は不可です", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        if ( $kingaku > 999999999999 ) {
            $msg = sprintf("金額(%d)は12桁以内である必要があります", $val);
            throw new Msi_Logic_ExceptionInput($msg);
        }

        return true;
    }

    /**
     * カテゴリーコード チェッカ
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      mixed
     * @param      array
     * @return     boolean
     * @throws     Msi_Logic_ExceptionInput (入力データ エラーの場合)
     */
    protected function myCategoryCdChecker($val, $rec)
    {
        $category_cd = +$val;

        $db = $this->db();

        $cond = array( 'code_kbn'         => '2340', // 印刷グループ(2340)
                       'kbn_value_cd_num' => $category_cd, 
                       '__raw_200'        => "biko = '1'" );
        $wkRec = DataMapper_CodeNmMst::findOne( $db, $cond );
        if ( $wkRec === null ) {
            $msg = sprintf("カテゴリーコード(%s) がマスタに存在しません", $val);
            throw new Msi_Logic_ExceptionLogic($msg);
        }

        return true;
    }

}
