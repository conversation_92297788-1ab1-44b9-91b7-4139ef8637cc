@charset "UTF-8";
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.small {
  font-size: 85%;
}

.small2 {
  font-size: 80%;
}

.small3 {
  font-size: 75%;
}

.smallest {
  font-size: 70%;
}

.select2-container .select2-choice {
  background-image: none;
}

.select2-drop-mask {
  background-image: none;
}

.select2-dropdown-open .select2-choice {
  background-image: none;
}

.select2-container-multi .select2-choices {
  background-image: none;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background-image: none;
}

.my-error {
  border: 1px solid red !important;
}

#order {
  position: fixed;
  width: 100%;
  height: 87.5%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  top: 40px;
  left: 0;
  background-color: #D4E5F4;
  box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
  line-height: 1;
  z-index: 2;
  /* cf. juchu.estimate.css */
  /*disable状態のアクティブを元のボタンの状態にする*/
}
#order a {
  text-decoration: none;
  color: inherit;
}
#order h2 {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 16px;
  color: #266CA5;
}
#order input:disabled, #order input[readonly] {
  color: #545454;
  pointer: auto;
}
#order .page-title {
  position: static;
  margin-top: 10px;
  margin-bottom: 10px;
  float: left;
}
#order .page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #ff9900;
  font-size: 1.2em;
}
#order fieldset.type_1 {
  margin-top: 31px;
  display: inline;
  margin-bottom: 0;
}
#order fieldset.base_1 {
  border: 1px solid #88B1D1;
}
#order fieldset.base_2 {
  border: 1px solid #88B1D1;
  border-top: none;
}
#order .dlg_note,
#order .dlg_date,
#order .dlg_date_keijo,
#order .dlg_deliv,
#order .dlg_denpyo_no,
#order .dlg_sekyu_nm,
#order .dlg_staff,
#order .dlg_item,
#order .dlg_bill,
#order .dlg_summary {
  width: 3%;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border-top: none;
  border-right: 1px solid #CCC;
  border-bottom: none;
  border-left: 1px solid #CCC;
}
#order .dlg_date_keijo,
#order .dlg_date_kaishu {
  width: 2%;
  border-top: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
  border-left: 1px solid #CCC;
}
#order .dlg_staff {
  border-right: none;
}
#order .lbl_zip {
  width: 3%;
  background-color: #FFF;
  border-right: 1px solid #CCC;
  border-bottom: none;
  color: #CCC;
}
#order fieldset.note_1 {
  margin-top: 10px;
}
#order .header .row {
  width: 4%;
}
#order .header .control {
  width: 4%;
}
#order .header .DELtype {
  width: 6%;
}
#order .header .juchu_bi {
  width: 9%;
}
#order .header .item_id {
  width: 9%;
  border-right: 1px solid #88B1D1;
}
#order .header .item, #order .header .tekiyo {
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}
#order .header .soko {
  border-bottom: 1px solid #88B1D1;
}
#order .header .price, #order .header .cost, #order .header .juchu_gaku, #order .header .nebiki {
  width: 9%;
  border-bottom: 1px solid #88B1D1;
}
#order .header .quantity {
  width: 7%;
  left: 50%;
}
#order .header .unit {
  width: 7%;
  left: 50%;
}
#order .header .memo1,
#order .header .memo2{
  width: 10%;
}
#order .header .memo3,
#order .header .memo4{
  width: 10%;
  border-right: none;
}
#order .header table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background: none;
  background-color: #E8F3FB;
  font-size: 13px;
}
#order .header table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .items {
  height: 53%;
  overflow: auto;
  border: 1px solid #88B1D1;
  background-color: #EBF3FA;
  -webkit-overflow-scrolling: touch;
}
#order .items #add {
  width: 100%;
  height: 60px;
  background-image: url(../../../img/plus_1.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 26px 26px;
  text-indent: -999px;
}
#order .items #add:active {
  background-image: url(../../../img/plus_1_on.png);
}
#order .lbl_denpyo_no,
#order .lbl_juchu_ymd,
#order .lbl_sekyu_nm,
#order .lbl_sekyu_soufu_nm,
#order .lbl_sekyu_addr,
#order .lbl_tax,
#order .lbl_collect_type {
  width: 8%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_collect_type {
  margin-left: 0;
}
#order .lbl_pay_method_cd {
  width: 7%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
  border-left: none;
}
#order .lbl_kaishu_kbn {
  width: 7%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
  border-left: none;
}
#order .lbl_keijo_ymd,
#order .lbl_kaishu_ymd {
  width: 7%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
  border-left: none;
}
#order .pay_method_cd-cls {
  float: left;
  width: 7%;
}
#order .lbl_sekyu_tel {
  width: 8%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_staff {
  width: 8%;
  border-bottom: none;
  border-top: none;
}
#order .note_1 .lbl_note,
#order .note_2 .lbl_note,
#order .note_x .lbl_note {
  width: 7%;
  border-bottom: 1px solid #88B1D1;
  border-left: 1px solid #88B1D1;
}
#order .note_x .lbl_note,
#order .note_x input {
  visibility: hidden;
}
#order .note_1 input,
#order .note_2 input,
#order .note_x input,
#order .total_title,
#order .total_price {
  border-bottom: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .note_1 .lbl_note,
#order .note_1 input,
#order .note_1 .label {
  border-top: 1px solid #88B1D1;
}
#order .total_title {
  border-left: 1px solid #88B1D1;
  width: 10%;
  text-align: center;
  padding: 10px 0;
  background-color: #E8F3FB;
}
#order .total_price {
  width: 9%;
  text-align: right;
  font-weight: normal;
  font-size: inherit;
  color: inherit;
  background-color: #f4f4f4;
}
#order .staff {
  width: 10%;
}
#order #sekyu_tel {
  width: 13%;
  border-bottom: none;
}
#order #note_id_1,
#order #note_id_2,
#order #note_id_x,
#order #juchu_ymd,
#order #keijo_ymd,
#order #nonyu_dt,
#order #kaishu_ymd,
#order #denpyo_no,
#order #sekyu_nm,
#order #sekyu_soufu_nm,
#order #tanto_cd,
#order #tanto_nm {
  border-right: none;
}
#order #juchu_ymd,
#order #nonyu_dt,
#order #kaishu_ymd,
#order #denpyo_no,
#order #sekyu_nm,
#order #sekyu_soufu_nm,
#order #tanto_cd,
#order #tanto_nm,
#order #sekyu_nm,
#order #nonyu_nm {
  border-bottom: none;
  border-top: none;
}
#order #tanto_nm {
  width: 15%;
}
#order #sekyu_nm {
  width: 15%;
  border-right: 1px solid #CCC;
}
#order #sekyu_soufu_nm {
  width: 19%;
  border-right: 1px solid #CCC;
}
#order #sekyu_yubin_no {
  width: 8%;
  border-bottom: none;
}
#order #sekyu_addr1 {
  width: 30%;
  border-bottom: none;
}
#order #sekyu_addr2 {
  width: 25%;
  border-bottom: none;
}
#order #denpyo_biko1,
#order #denpyo_biko2,
#order #note_txt_x {
  width: 43.3%;
  border-right: none;
}
#order #keijo_ymd {
  width: 8.9%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order #kaishu_ymd {
  width: 8.9%;
  border-top: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order #kaiin_sbt_nm {
  width: 5.8%;
  /*border-bottom: none;*/
}
#order .list table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background-color: #FFF;
}
#order .list table tbody:nth-child(even) {
  background-color: #F5F9FD;
}
#order .list table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: left;
  padding-left: 5px;
  color: #286EA6;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .list .row {
  width: 4%;
  text-align: center;
}
#order .list .control {
  width: 4%;
  text-align: center;
}
#order .list .control i {
  cursor: pointer;
}
#order .list .type {
  width: 6%;
  color: #000;
}
#order .list .juchu_bi {
  width: 9%;
  color: #000;
}
#order .list .item_id {
  width: 9%;
  border-right: 1px solid #88B1D1;
}
#order .list .dlg_item {
  width: 3%;
  left: 22%;
}
#order .list .item {
  width: 22%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .item input,
#order .list .item div {
  position: absolute;
  top: 0;
  left: 0;
  height: 30px;
  border-right: none;
}
#order .list .quantity {
  width: 7%;
}
#order .list .unit {
  width: 7%;
  color: #000;
}
#order .list .price {
  width: 9%;
}
#order .list .price input {
  text-align: right;
}
#order .list .juchu_gaku {
  width: 9%;
}
#order .list .juchu_gaku input {
  text-align: right;
}
#order .list .juchu_gaku_cal {
  width: 9%;
}
#order .list .juchu_gaku_cal input {
  text-align: right;
}
#order .list .nebiki {
  width: 9%;
}
#order .list .nebiki input {
  text-align: right;
}
#order .list .cost {
  width: 9%;
  border-bottom: 1px dotted #88B1D1;
}
#order .list .memo1,
#order .list .memo2 {
  width: 10%;
}
#order .list .memo3,
#order .list .memo4 {
  width: 10%;
  border-right: none;
}
#order .list .juchu_gaku, #order .list .genka_gaku {
  cursor: auto !important;
}
#order .list .dlg_shohin,
#order .list .dlg_soko,
#order .list .dlg_juchu_bi_v	{
  float: right;
  width: 25%;
  border: none;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  background-color: transparent;
}
#order .list .item_nm {
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .no-border-bottom {
  border-bottom: none !important;
}
#order .list .my-border-bottom {
  border-bottom: 1px solid #88B1D1;
}
#order .list .my-input-like {
  padding-right: 7px;
}
#order .list .my-txt2, #order .list .my-txt50, #order .list .my-txt60, #order .list .my-txt65, #order .list .my-txt70, #order .list .my-txt75, #order .list .my-txt80, #order .list .my-txt85, #order .list .my-txt90, #order .list .my-txt95 {
  border: none !important;
  width: 95%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  background-color: inherit;
  height: 28px;
}
#order .list .my-txt2:enabled, #order .list .my-txt50:enabled, #order .list .my-txt60:enabled, #order .list .my-txt65:enabled, #order .list .my-txt70:enabled, #order .list .my-txt75:enabled, #order .list .my-txt80:enabled, #order .list .my-txt85:enabled, #order .list .my-txt90:enabled, #order .list .my-txt95:enabled {
  cursor: pointer;
}
#order .list .my-txt50 {
  width: 50% !important;
}
#order .list .my-txt60 {
  width: 60% !important;
}
#order .list .my-txt65 {
  width: 65% !important;
}
#order .list .my-txt70 {
  width: 70% !important;
}
#order .list .my-txt75 {
  width: 75% !important;
}
#order .list .my-txt80 {
  width: 80% !important;
}
#order .list .my-txt85 {
  width: 85% !important;
}
#order .list .my-txt90 {
  width: 90% !important;
}
#order .list .my-txt95 {
  width: 95% !important;
}
#order .list .my-error {
  border: 1px solid red !important;
}
#order .DELlbl_collect_type {
  margin-left: 64%;
  width: 20%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
}
#order .kaishu_kbn-cls {
  float: left;
  width: 7%;
}
#order .tax_kbn-cls {
  float: left;
  width: 8%;
}
#order .zei_cd-cls {
  float: left;
  width: 10%;
}
#order .select2-choice, #order .select-container {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
  border-left: none;
}
#order .select2-choices {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
}
#order .select2-arrow {
  border-radius: 0px !important;
}
#order .select2-container-multi .select2-choices {
  height: 28px !important;
}
#order .list .select2-choice, #order .list .select-container {
  margin: 0;
  padding: 0;
  border: 1px solid #DDD;
  background-color: transparent;
  background-image: none;
}
#order fieldset.base_1 .select2-choice, #order fieldset.base_1 .select-container, #order fieldset.base_2 .select2-choice, #order fieldset.base_2 .select-container {
  height: 30px !important;
  border-top: none;
  border-bottom: none;
  border-right: none;
}
#order #s2id_tax_kbn {
  border-right: 1px solid #88B1D1;
}
#order .my-akaji {
  color: red !important;
}
#order .my-bold {
  font-weight: bold;
}
#order .my-error {
  border: 1px solid red !important;
}
#order .my-disabled {
  background-color: #f4f4f4 !important;
  cursor: auto;
}
#order input[readonly], #order input:disabled {
  background-color: #f4f4f4 !important;
  cursor: auto;
}
#order .my-type-date[readonly],
#order .my-type-date:disabled,
#order .my-type-datetime-local[readonly],
#order .my-type-datetime-local:disabled {
  cursor: auto;
}
#order input[type="button"]:disabled {
  opacity: .5;
}
#order input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94B9D6;
  box-shadow: 1px 1px 0 #FFF;
  color: #296FA7;
}
#order .list .soko_nm {
    text-align: left;
    padding: 0px 4px;
}
#order fieldset.upd_info {
  margin-top: 10px;
}
#order .upd_info .cre_user,
#order .upd_info .mod_user {
    width: 20%;
    border: none;
    background-color:  rgba(0,0,255,0);
}
#order .hall_cd:enabled {
  cursor: pointer;
}
#order .lbl_juchusaki_kbn {
  width: 7%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
}
#order .juchusaki_kbn-cls {
  float: left;
  width: 7%;
}
#order .lbl_kaiin_kbn_nm {
  width: 7%;
}
#order #kaiin_kbn_nm {
  width: 9%;
}
#order .lbl_furikomi_kbn {
    width: 7%;
    border: 1px solid #88B1D1;
    border-right: 3px solid #A1D7F4;
}
#order .cls_furikomi_kbn {
    float: left;
    width: 21%;
}
#order fieldset.type_1.non_margin_top {
    margin-top: 0px;
}
#order fieldset.type_1.field_head {
    top: 3px;
    margin-bottom: 0px;
}
#order #dtl-table-id .nafuda_nm,
#order #dtl-table-id .nafuda_nm2,
#order #dtl-table-id .nafuda_nm3,
#order #dtl-table-id .nafuda_nm4 {
    padding-right: 3px;
    padding-left: 0px;
}

/* keigen patch */
#order #sekyu_nm {
  width: 20%;
  border-right: none;
}
#order #sekyu_soufu_nm {
  width: 35%;
}
#order #sekyu_tel {
  width: 18%;
}

#order .list .juchu_gaku_cal-td {
  width: 9%;
  box-sizing: border-box;
  padding-left: 0;
}
#order .list .juchu_gaku_cal.input_field {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    text-align: right;
    padding-right: 5px;
    height: 32px;
    line-height: 32px;
}

#order .tax_kbn-cls {
  width: 18%;
}

#order #s2id_tax_kbn {
    border-right: none;
}

