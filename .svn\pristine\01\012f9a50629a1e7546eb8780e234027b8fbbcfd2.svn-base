<?php
  /**
   * Juchu_ThanksController
   *
   * お礼状 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR>
   * @since      2014/xx/xx
   * @filesource 
   */

  /**
   * お礼状 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR>
   * @since      2014/xx/xx
   */
class Juchu_ThanksController extends Msi_Zend_Controller_Action
{
    /**
     * index アクション
     *
     * <AUTHOR>
     * @since      2014/xx/xx
     */
	public function indexAction()
    {
        $this->_forward( 'standard' );
	}

    public function standardAction()
    {
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson( 'thanks' );
        App_Smarty::pushJsFile( 'app/sidemenu.js', 'app/juchu.mitsu.input.js' );
    }

    public function originalAction()
    {
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson( 'thanks' );
        App_Smarty::pushJsFile( 'app/sidemenu.js', 'app/juchu.mitsu.input.js' );
    }

    public function envelopeAction()
    {
        $this->view->juchuhenko_side_json = Juchu_Utils::getSideMenuDataJson( 'thanks' );
        App_Smarty::pushJsFile( 'app/sidemenu.js', 'app/juchu.mitsu.input.js' );
    }

}
