(function(a){a.myMsiApp=a.myMsiApp||{};a.myMsiApp.init=function(e){a("#user_cd").click(function(){a(this).msiPickup({action:"/dev/pick/user",data:{user_cd:a.msiJqlib.getCode(a("#user_cd").val())},onSelect:function(b){a("#user_cd").val(b.code+" "+b.name.replace(/^\s+/,""))},onClear:function(){a("#user_cd").val("")}})});a("#search_close").click(function(){a("#search .frame").slideUp(400,function(){a("#search_open").fadeIn(200)});a.msiJqlib.setCookie("bzr_tkn_list_cond_close",1,{path:a.msiJqlib.baseUrl()+
"/"})});a("#search_open").click(function(){a("#search .frame").slideDown(400,function(){a("#search_open").fadeOut(200)});a.msiJqlib.removeCookie("bzr_tkn_list_cond_close",{path:a.msiJqlib.baseUrl()+"/"})});a("a.br-drilldown").click(function(b){if(!b.ctrlKey){b.preventDefault();var c=a(b.currentTarget);b=c.attr("href");var c=c.attr("id").replace(/^\D+/,""),d=a('input[name="total_count"]').val();a.ajax({url:b,data:{seq_no:c,total:d,context:"drilldown",par_title:"\u901a\u52e4\u8cbb\u53f0\u5e33\uff08\u4e00\u89a7\uff09",
par_url:location.href},type:"GET",dataType:"html",success:function(b){a("#main").replaceWith(b)}})}})};a.myMsiApp.hideSearchCond=function(){a("#search .frame").hide();a("#search_open").show()};a(a.myMsiApp.init)})(jQuery);
