<?php
  /**
   * DataMapper_JuchuDenpyo
   *
   * 受注伝票 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2013/03/31
   * @filesource 
   */

  /**
   * 受注伝票 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2013/03/31
   */
class DataMapper_JuchuDenpyo extends DataMapper_Abstract
{
    /**
     * 受注伝票 取得
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array(), $isDateEffective=true)
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT m.*,
       tt.tanto_nm, tt.tanto_knm,
       b.bumon_lnm, b.bumon_snm, b.bumon_lknm, b.bumon_sknm, b.bumon_kbn,
       to_char(ur.keijo_ymd, 'YYYY/MM/DD') AS keijo_ymd,
       '登録：' || ctt.tanto_nm || '　(' || TO_CHAR(m._cre_ts, 'YYYY/MM/DD HH24:MI:SS') || ')' AS  cre_user,
       CASE WHEN m._cre_ts = m._mod_ts THEN
            NULL
        ELSE        
           '変更：' || utt.tanto_nm || '　(' || TO_CHAR(m._mod_ts, 'YYYY/MM/DD HH24:MI:SS') || ')'
        END                         AS  mod_user,
       '請求：' || skt.tanto_nm || '　(' || TO_CHAR(ur.seikyu_print_date, 'YYYY/MM/DD HH24:MI:SS') || ')' AS  seikyu_user
       ,ur.uri_den_no   -- 売上伝票番号 2016/12/28 ADD Kayo        
       ,ski.kaiin_kbn                              -- 会員区分
       ,cd1.kbn_value_lnm     AS   kaiin_kbn_nm    -- 会員区分名
       ,ski.kaiin_shubetsu                         -- 会員種別
       ,cd2.kbn_value_lnm     AS   kaiin_sbt_nm    -- 会員種別名
       ,m.n_free5             AS   kyouka_seq_no   -- 数字フリー項目５(連番)
       ,CASE WHEN info.keishiki_cd = '5' THEN
                    ''
                ELSE
                    TO_CHAR(ni.nitei_ymd,'YYYY/MM/DD')
        END AS sougi_ymd    --葬儀日
        ,CASE WHEN info.keishiki_cd = '1' or info.keishiki_cd = '5' THEN
                    ''
                ELSE
                    TO_CHAR(ni2.nitei_ymd,'YYYY/MM/DD')
        END AS tuya_ymd		--通夜日
        ,ur.pay_method_cd	-- 支払方法コード	
        ,CASE WHEN ur.n_free1 IS NULL THEN 1
              ELSE ur.n_free1 
         END                    AS keisyo_kbn         -- 敬称区分
        ,CASE WHEN ur.n_free2 IS NULL THEN 1
              ELSE ur.n_free2 
         END                    AS keisyo_kbn2        -- 敬称区分(送付先用)
  FROM juchu_denpyo m
  LEFT JOIN tanto_mst tt
    ON tt.delete_flg=0
   AND m.tanto_cd=tt.tanto_cd   $dateWhere2
  LEFT JOIN bumon_mst b
    ON b.delete_flg=0
   AND m.bumon_cd	=	b.bumon_cd   $dateWhere3
  LEFT JOIN uriage_denpyo ur
    ON ur.delete_flg=0
  AND m.denpyo_no=ur.denpyo_no
  LEFT JOIN login_mst cltm          -- 2016/06/17 ADD Kayo
      ON  case when length(m._cre_user)-10 > 0 THEN
            SUBSTR(m._cre_user,10, length(m._cre_user)-9 )
        ELSE
            null
        END           =  cltm.login_cd
      AND 0           =  cltm.delete_flg          
  LEFT JOIN tanto_mst ctt           -- 2016/06/17 ADD Kayo
    ON   cltm.tanto_cd  = ctt.tanto_cd
    AND  0              = ctt.delete_flg
  LEFT JOIN login_mst ultm          -- 2016/06/17 ADD Kayo
      ON  case when length(m._mod_user)-10 > 0 THEN
            SUBSTR(m._mod_user,10, length(m._mod_user)-9 )
        ELSE
            null
        END           =  ultm.login_cd
      AND 0           =  ultm.delete_flg          
  LEFT JOIN tanto_mst utt           -- 2016/06/17 ADD Kayo
    ON   ultm.tanto_cd  = utt.tanto_cd
    AND  0              = utt.delete_flg
  LEFT JOIN tanto_mst skt           -- 2016/07/01 ADD Kayo
    ON   ur.seikyu_print_tanto_cd  = skt.tanto_cd
    AND  0                         = skt.delete_flg
 LEFT JOIN sekyu_kaiin_info  ski    -- 請求先会員情報   2017/03/16 ADD Kayo
    ON '0000000000'     = ski.seko_no           
    AND m.sekyu_cd      = ski.sekyu_cd  
    AND 0				= ski.delete_flg
 LEFT JOIN code_nm_mst cd1	-- 会員区分 2017/03/16 ADD Kayo
    ON	'3760'			=	cd1.code_kbn
    AND ski.kaiin_kbn	=	cd1.kbn_value_cd_num
    AND 0				=	cd1.delete_flg
 LEFT JOIN code_nm_mst cd2	-- 会員種別 2017/03/16 ADD Kayo
    ON	'3770'              =	cd2.code_kbn
    AND ski.kaiin_shubetsu	=	cd2.kbn_value_cd_num
    AND 0                   =	cd2.delete_flg
 LEFT JOIN seko_kihon_info info 
    ON m.seko_no = info.seko_no
    AND info.delete_flg = 0
LEFT JOIN seko_nitei ni 
    ON m.seko_no = ni.seko_no
    AND ni.delete_flg = 0 
    AND ni.nitei_kbn = 11
LEFT JOIN seko_nitei ni2 
   ON m.seko_no = ni2.seko_no
   AND ni2.delete_flg = 0 
   AND ni2.nitei_kbn = 4
LEFT JOIN sekyu_saki_info ssi
   ON ssi.seko_no     = ur.seko_no
   AND ssi.sekyu_cd   = ur.sekyu_cd
   AND ssi.delete_flg = 0
 WHERE m.delete_flg	=	0   $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        foreach ( $select as &$rec ) {
            $dtl = static::findDenpyoMsi($db, $rec['denpyo_no']);
            $rec['_dtl_'] = $dtl;

            foreach ( Msi_Sys_Utils::strArrayify_qw('juchu_ymd kaishu_ymd nonyu_dt') as $key ) {
                if ( strlen($rec[$key]) > 0 ) {
                    $rec[$key] = Msi_Sys_Utils::normYYYYMMDD($rec[$key], '/');
                }
            }
        }

        return $select;
    }

    /**
     * 受注明細 取得
     *
     * <AUTHOR> Mihara
     * @since      2014/03/31
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi($db, $denpyo_no)
    {

        $select = $db->easySelect( <<< END_OF_SQL
SELECT m.*,
       sm.nm_input_kbn AS nm_input_kbn,
       sm.tnk_chg_kbn AS tnk_chg_kbn,
       sm.tani_ovr_kbn AS tani_ovr_kbn
       ,sm.zaiko_knri_kbn
       ,skm.soko_lnm as soko_nm
       ,skh.order_flg                           -- 発注済み 2015/12/15 ADD Kayo          
       ,m.n_free5             AS   kyouka_seq_no   -- 数字フリー項目５(連番)
  FROM juchu_denpyo_msi m
  LEFT JOIN shohin_mst sm
    ON m.shohin_cd          = sm.shohin_cd
   AND m.kaisya_cd          = sm.kaisya_cd      -- 2016/12/25 ADD Kayo
   AND m.shohin_bumon_cd    = sm.bumon_cd       -- 2016/12/25 ADD Kayo
   AND sm.delete_flg=0
  LEFT JOIN seko_hachu_info skh     -- 2015/12/15 ADD Kayo
      ON m.denpyo_no    = skh.jc_denpyo_no       
      AND m.msi_no      = skh.jc_msi_no
      AND 0             = skh.delete_flg
  LEFT JOIN soko_mst skm
    ON m.soko_cd = skm.soko_cd
   AND skm.delete_flg=0
 WHERE m.delete_flg=0
   AND m.denpyo_no = :denpyo_no
 ORDER BY disp_no
END_OF_SQL
                                   , array('denpyo_no'=>$denpyo_no) );

        foreach ( $select as &$rec ) {
            if ( strlen($rec['juchu_ymd']) > 0 ) {
                $rec['juchu_ymd'] = Msi_Sys_Utils::normYYYYMMDD($rec['juchu_ymd'], '/');
            }
        }

        return $select;
    }
    
    /**
     * 受注明細 取得
     *
     * <AUTHOR> Matsuyama
     * @since      2017/01/06
     * @param      Msi_Sys_Db $db
     * @param      array  $keyHash
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
	SELECT   m.*
		,sm.maker_hinban
		,sm.nm_input_kbn AS nm_input_kbn
		,sm.tnk_chg_kbn AS tnk_chg_kbn
		,sm.tani_ovr_kbn AS tani_ovr_kbn
		,sm.zaiko_knri_kbn
		,skm.soko_lnm as soko_nm
		,skh.order_flg
		,sbm.mitumori_print_seq
	FROM juchu_denpyo_msi m
	    LEFT JOIN shohin_mst sm
		ON m.shohin_cd	    = sm.shohin_cd
		AND sm.delete_flg   = 0
	    LEFT JOIN shohin_bunrui_mst sbm
		ON m.shohin_cd        = sbm.shohin_cd
	        AND m.kaisya_cd       = sbm.kaisya_cd
	        AND m.shohin_bumon_cd = sbm.bumon_cd
	        AND m.dai_bunrui_cd   = sbm.dai_bunrui_cd
	        AND m.chu_bunrui_cd   = sbm.chu_bunrui_cd
	        AND sbm.delete_flg    = 0
	    LEFT JOIN seko_hachu_info skh
		ON m.denpyo_no    = skh.jc_denpyo_no       
		AND m.msi_no      = skh.jc_msi_no
		AND 0             = skh.delete_flg
	    LEFT JOIN soko_mst skm
		ON m.soko_cd	    = skm.soko_cd
		AND skm.delete_flg  = 0
	WHERE m.delete_flg = 0
    ) T
 WHERE $whereStr
$orderBy
$tailClause
END_OF_SQL
	    , $param);

        foreach ( $select as &$rec ) {
            if ( strlen($rec['juchu_ymd']) > 0 ) {
                $rec['juchu_ymd'] = Msi_Sys_Utils::normYYYYMMDD($rec['juchu_ymd'], '/');
            }
            if ( strlen($rec['gs_nouki_dt']) > 0 ) {
                $rec['gs_nouki_dt'] = Msi_Sys_Utils::normYYYYMMDD($rec['gs_nouki_dt'], '/');
            }
        }
        return $select;
    }

	/**
     * 受注伝票(ヘッダーのみ) 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/07/02
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findDenpyo($db, $keyHash=array(), $isDateEffective=true)
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT	h.*
		,tt.tanto_nm
		,tt.tanto_knm
		,b.bumon_lnm
		,b.bumon_snm
		,b.bumon_lknm
		,b.bumon_sknm
		,b.bumon_kbn
  FROM juchu_denpyo h
  LEFT JOIN tanto_mst tt
    ON tt.delete_flg=0
   AND h.tanto_cd=tt.tanto_cd   $dateWhere2
  LEFT JOIN bumon_mst b
    ON b.delete_flg=0
   AND h.bumon_cd=b.bumon_cd   $dateWhere3
 WHERE h.delete_flg=0   $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
	
	/**
     * 受注伝票(ヘッダーのみ) 取得2
     *
     * <AUTHOR> Kayo
     * @since      2014/07/27
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findDenpyo2($db, $keyHash=array(), $isDateEffective=true)
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT	h.*
		,tt.tanto_nm
		,tt.tanto_knm
		,b.bumon_lnm
		,b.bumon_snm
		,b.bumon_lknm
		,b.bumon_sknm
		,b.bumon_kbn
		,CASE WHEN h.data_kbn = 4 THEN TO_CHAR(m.juchu_ymd,'YYYY/MM/DD')
			ELSE TO_CHAR(h.juchu_ymd,'YYYY/MM/DD') END	AS	juchu_ymd2 		
  FROM juchu_denpyo h
  LEFT JOIN (SELECT m.denpyo_no, min(m.juchu_ymd) as juchu_ymd 
	  FROM juchu_denpyo_msi m 
	  WHERE delete_flg	= 0 GROUP By  m.denpyo_no) m
		ON h.denpyo_no	= m.denpyo_no
  LEFT JOIN tanto_mst tt
    ON tt.delete_flg=0
   AND h.tanto_cd=tt.tanto_cd   $dateWhere2
  LEFT JOIN bumon_mst b
    ON b.delete_flg=0
   AND h.bumon_cd=b.bumon_cd   $dateWhere3
 WHERE h.delete_flg=0   $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
    
    /**
     * 受注明細Noの最大値を取得する
     *
     * <AUTHOR> Matsuyama
     * @since 2016/11/09
     * @param Msi_Sys_Db $db
     * @param string $denpyo_no 伝票番号
     * @return int 最大売上明細No
     */
    public static function getMaxdenpyoMsiNo($db, $denpyo_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    COALESCE(MAX(msi_no),0) AS msi_no
FROM
    juchu_denpyo_msi
WHERE
    denpyo_no = :denpyo_no
END_OF_SQL;
        $max = $db->getOneVal($sql, array('denpyo_no' => $denpyo_no));
        return $max;
    }
    
    /**
     * 受注した商品の個数を取得する
     *
     * <AUTHOR> Oka
     * @since 2017/06/06
     * @param Msi_Sys_Db $db
     * @param string $denpyo_no 伝票番号
     * @return int 最大売上明細No
     */
    public static function getJuchuNum($db, $denpyo_no)
    {
        $sql = <<< END_OF_SQL
 SELECT
     COALESCE(SUM(juchu_suryo),0) AS juchu_suryo
 FROM
     juchu_denpyo_msi
 WHERE
     denpyo_no = :denpyo_no
END_OF_SQL;
        $num = $db->getOneVal($sql, array('denpyo_no' => $denpyo_no));
        return $num;
    }
}
