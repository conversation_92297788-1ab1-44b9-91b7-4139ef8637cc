@charset "UTF-8";
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.small {
  font-size: 85%;
}

.small2 {
  font-size: 80%;
}

.small3 {
  font-size: 75%;
}

.smallest {
  font-size: 70%;
}

.select2-container .select2-choice {
/*  background-image: none;*/
}

.select2-drop-mask {
/*  background-image: none;*/
}

.select2-dropdown-open .select2-choice {
/*  background-image: none;*/
}

.select2-container-multi .select2-choices {
/*  background-image: none;*/
}

.select2-container-multi .select2-choices .select2-search-choice {
/*  background-image: none;*/
}

.my-error {
  border: 1px solid red !important;
}

#order {
  position: fixed;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  top: 40px;
  left: 0;
  background-color: #D4E5F4;
  box-shadow: -2px 0 2px rgba(0, 0, 0, 0.2);
  line-height: 1;
  z-index: 2;
  /* cf. juchu.estimate.css */
  /*disable状態のアクティブを元のボタンの状態にする*/
}
#order a {
  text-decoration: none;
  color: inherit;
}
#order h2 {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 16px;
  color: #266CA5;
}
#order input {
  font-weight: bold;
}

#order .page-title {
  display: inline;
  margin-top: 5px;
  margin-bottom: 10px;
  float: left;
}
#order .page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #ff9900;
  font-size: 1.2em;
}
#order fieldset.type_1 {
  margin-bottom: 0;
}
#order fieldset.base_1 {
}
#order fieldset.base_2 {
  border-top: none;
  margin-bottom: 0px;
}
#order fieldset.base_3 {
  border-top: none;
  margin-bottom: 10px;
}

#order #hachudate_s,
#order #hachudate_e{
  width: 10%;
  text-align: center;
}
#order .dlg_date {
    border: 1px solid #88B1D1;
}
#order .dlg_date2 {
    border-right : none;
}
#order input.txt{
  border: 1px solid #88B1D1;
  border-right: none;     
  border-left: none;  
}

#order #souke_nm2{
  width: 13%;  
    
}

#order .list .dlg_date {
  float: right;
  width: 15%;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border-top: none;
  border-right: 0px solid #CCC;
  border-bottom: none;
  border-left: 0px solid #CCC;
}
#order input {
	width: 10%;
}
#order .dlg_staff {
  border-right: none;
}
#order fieldset.note_1 {
  margin-top: 10px;
}
#order .header .row {
  width: 4%;
}
#order .header .control {
  width: 4%;
}
#order .header .type {
  width: 6%;
}
#order .header .item_id {
  width: 10%;
  border-right: 1px solid #88B1D1;
}
#order .header .item {
  width: 22%;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .quantity {
  width: 7%;
  left: 50%;
}
#order .header .unit {
  width: 7%;
  left: 50%;
}
#order .header .price, #order .header .cost {
  width: 9%;
  border-bottom: 1px dotted #88B1D1;
}
#order .header .nonyu_dt {
  width: 10%;
  left: 50%;
}
#order .header .soko_nm {
  width: 20%;
  left: 50%;
}
#order .header .nyuka_dt {
  width: 10%;
  left: 50%;
}
#order .header .memo {
  width: 20%;
  border-bottom: 1px dotted #88B1D1;
  border-right: none;
}
#order .header table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background: none;
  background-color: #E8F3FB;
  font-size: 13px;
}
#order .header table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  font-weight: bold;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .items {
  height: calc( 100% - 345px ); /* 65%; */
  overflow: auto;
  border: 1px solid #88B1D1;
  background-color: #EBF3FA;
  -webkit-overflow-scrolling: touch;
}
#order .items #add {
  width: 100%;
  height: 60px;
  background-image: url(../../../img/plus_1.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 26px 26px;
  text-indent: -999px;
}
#order .items #add:active {
  background-image: url(../../../img/plus_1_on.png);
}

#order #seikyu_nm {
    border-right: 1px solid #88B1D1;
    width: 13%;
}

#order .lbl_denpyo_no,
#order .lbl_seko_no,
#order .lbl_siire_nm,
#order .lbl_souke_nm,
#order .lbl_souke_nm3,
#order .lbl_kenpin,
#order .lbl_seikyu,
#order .lbl_tanto_nm,
#order .lbl_hachudate,
#order .lbl_kamoku_nm,
#order .lbl_bumon_nm,
#order .lbl_hachusaki_nm{
  width: 6%;
  border: 1px solid #88B1D1;    
  border-right: 3px solid #A1D7F4;

}
#order .lbl_siire_nm
,#order .lbl_denpyo_no{
    border-left: none;
}

#order .dummy{
  width :5%;  
  border : 0px;
  background-color: transparent;  
}

#order #denpyo_no,
#order #seko_no,
#order #siire_nm{
  border: 1px solid #88B1D1;   
  background-color: #ffffff !important;
  border-left: none; 
  border-right: none; 
  cursor: auto;
}

#order .dlg_hachudate_s,
#order .dlg_hachudate_e,
#order .dlg_denpyo_no,
#order .dlg_seko_no,
#order .dlg_siire,
#order .dlg_tanto,
#order .dlg_bumon,
#order .dlg_hachusaki,
#order .dlg_kamoku,
#order .dlg_seikyu_no
{
  width: 3%;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border: 1px solid #88B1D1;  
}

#order .dlg_seko_no,
#order .dlg_siire,
#order .dlg_tanto,
#order .dlg_bumon,
#order .dlg_hachusaki,
#order .dlg_seikyu_no
{
  border-right: none;   
}
#order .dlg_seko_no,
#order .dlg_tanto,
#order .dlg_seikyu_no,
#order .dlg_hachusaki,
#order .dlg_kamoku{
  border-top: none;        
}

#order .dlg_siire,
#order .lbl_siire_nm,
#order .lbl_tanto_nm,
#order .lbl_kamoku_nm,
#order .lbl_seko_no,
#order .lbl_seikyu,
#order .lbl_kenpin,
#order .lbl_hachusaki_nm,
#order #siire_nm,
#order #seko_no,
#order #hachusaki_nm,
#order #tanto_nm,
#order #seikyu_nm,
#order #kamoku_nm{   
  border-top: none;  
}

#order .kenpin_kbn-cls
,#order .data_kbn-cls{
  border: none;
  width: 13%;
  float: left;
}

#order .lbl_nonyu_dt {
  width: 9%;
  border-bottom: none;
  border-top: none;
}
#order .note_1 .lbl_note,
#order .note_2 .lbl_note,
#order .note_x .lbl_note {
  width: 7%;
  border-bottom: 1px solid #88B1D1;
  border-left: 1px solid #88B1D1;
}

#order .note_x .lbl_note,
#order .note_x input {
  visibility: hidden;
}
#order .note_1 input,
#order .note_2 input,
#order .note_x input,
#order .total_title,
#order .total_price {
  border-bottom: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .note_1 .lbl_note,
#order .note_1 input,
#order .note_1 .label {
  border-top: 1px solid #88B1D1;
}
#order .total_title {
  border-left: 1px solid #88B1D1;
  width: 10%; 
 text-align: center;
  padding: 10px 0;
  background-color: #E8F3FB;
}
#order .total_price {
  width: 9%;
  text-align: right;
  font-weight: normal;
  font-size: inherit;
  color: inherit;
  background-color: #f4f4f4;
}
#order #denpyo_no,
#order #siire_nm{
  border-right: none;
}


#order #souke_nm{
  border: 1px solid #88B1D1;
  border-left: none; 
  border-right: none;
  width: 10%;  

}


#order .lbl_souke_nm2{
    width: 3%;  
    background-color: #ffffff !important;    
    border: 1px solid #88B1D1;
    border-left: none;
    border-right: none;
}

#order .lbl_hachudate2{
    width: 6%;  
    background-color: #ffffff !important;    
    border: 1px solid #88B1D1;
    border-right: none;
    border-left: none;
    text-align: center;
}

#order #nonyu_nm {
  width: 34%;
}


#order #denpyo_biko2{
  width: 43.3%;
  border-right: none;
}
#order .list table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background-color: #FFF;
}
#order .list table tbody:nth-child(even) {
}
#order .list table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  font-weight: bold;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .list .row {
  width: 4%;
  text-align: center;
}
#order .list .control {
  width: 4%;
  text-align: center;
}
#order .list .control i {
  cursor: pointer;
}
#order .list .type {
  width: 6%;
  color: #000;
}
#order .list .item_id {
  width: 10%;
  border-right: 1px solid #88B1D1;
}
#order .list .dlg_item {
  width: 3%;
  left: 22%;
}
#order .list .item {
  width: 22%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .item input,
#order .list .item div {
  position: absolute;
  top: 0;
  left: 0;
  height: 30px;
  border-right: none;
}
#order .list .quantity {
  width: 7%;
}
#order .list .unit {
  width: 7%;
  color: #000;
}
#order .list .price {
  width: 9%;
  border-bottom: 1px dotted #88B1D1;
}
#order .list .cost {
  width: 9%;
  border-bottom: 1px dotted #88B1D1;
}
#order .list .nonyu_ymd {
  width: 10%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .nyuka_dt {
  width: 10%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .m_soko_cd {
  width: 4%;
  border-bottom: 1px dotted #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .list .soko_nm {
  width: 16%;
  border-bottom: 1px dotted #88B1D1;
  border-right: none;
}
#order .list .memo {
  width: 20%;
  border-right: none;
}
#order .list .hachu_gaku, #order .list .genka_gaku, #order .list .nebiki_gaku {
  cursor: auto !important;
}
#order .list .dlg_shohin {
  float: right;
  width: 25%;
  border: none;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  background-color: transparent;
}
#order .list .dlg_soko {
  float:right;
  width: 25%;
  border: none;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  background-color: transparent;
}
#order .list .item_nm {
  width: 22%;
  border-bottom: 1px dotted #88B1D1;
}

#order .list .no-border-bottom {
  border-bottom: none !important;
}
#order .list .my-border-bottom {
  border-bottom: 1px solid #88B1D1;
}
#order .list .my-input-like {
  padding-right: 7px;
}
#order .list .m_soko_cd {
  border: none !important;
  width: 4%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  background-color: inherit;
  height: 28px;
}
#order .list .soko_nm {
  border: none !important;
  width: 16%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  background-color: inherit;
  height: 28px;
}
#order .list .my-txt2, #order .list .my-txt30, #order .list .my-txt50, #order .list .my-txt60, #order .list .my-txt75, #order .list .my-txt80, #order .list .my-txt85, #order .list .my-txt90, #order .list .my-txt95 {
  border: none !important;
  width: 95%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  background-color: inherit;
  height: 28px;
}
#order .list .my-txt2:enabled, #order .list .my-txt50:enabled, #order .list .my-txt60:enabled, #order .list .my-txt75:enabled, #order .list .my-txt80:enabled, #order .list .my-txt85:enabled, #order .list .my-txt90:enabled, #order .list .my-txt95:enabled {
  cursor: pointer;
}
#order .list .my-txt30 {
  width: 30% !important;
}
#order .list .my-txt50 {
  width: 50% !important;
}
#order .list .my-txt60 {
  width: 60% !important;
}
#order .list .my-txt75 {
  width: 75% !important;
}
#order .list .my-txt80 {
  width: 80% !important;
}
#order .list .my-txt85 {
  width: 85% !important;
}
#order .list .my-txt90 {
  width: 90% !important;
}
#order .list .my-txt95 {
  width: 95% !important;
}
#order .list .my-error {
  border: 1px solid red !important;
}
#order .lbl_collect_type {
  margin-left: 64%;
  width: 20%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
}
#order .tax_kbn-cls {
  float: left;
  width: 8%;
}
#order .zei_cd-cls {
  float: left;
  width: 8%;
}
#order .select2-choice, #order .select-container {
  height: 31px !important;
  border-radius: 0px !important;
  padding-top: 0px !important;
  line-height: 30px;
  border-left: none;
  border-top: none;
}
#order .select2-choices {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
}
#order .select2-arrow {
  border-radius: 0px !important;
}
#order .select2-container-multi .select2-choices {
  height: 28px !important;
}
#order .list .select2-choice, #order .list .select-container {
  margin: 0;
  padding: 0;
  border: 1px solid #DDD;
  background-color: transparent;
}
#order fieldset.base_1 .select2-choice, #order fieldset.base_1 .select-container, #order fieldset.base_2 .select2-choice, #order fieldset.base_2 .select-container {
  height: 30px !important;
  border-top: none;
  border-bottom: none;
  border-right: none;
}
#order #s2id_tax_kbn {
  border-right: 1px solid #88B1D1;
}
#order .my-akaji {
  color: red;
  font-weight: bold;
}
#order .list .my-akaji {
  color: red;
  font-weight: bold;
}
#order .my-bold {
  font-weight: bold;
}
#order .my-error {
  border: 1px solid red !important;
}
#order .my-disabled {
  background-color: #f4f4f4 !important;
  cursor: auto;
}

#order .my-type-date[readonly],
#order .my-type-date:disabled,
#order .my-type-datetime-local[readonly],
#order .my-type-datetime-local:disabled {
  cursor: auto;
}
#order input[type="button"]:disabled {
  opacity: .5;
}
#order input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94B9D6;
  box-shadow: 1px 1px 0 #FFF;
  color: #296FA7;
}

#order .header tr{
    height: 32px;
}

#order .header .h_denpyo,
#order .header .h_hachuno,
#order #dtl-table-id .denpyo,
#order #dtl-table-id .seikyu{   
   width : 6.8%;
}

#order .header .h_bumon,
#order .header .h_hachu,
#order .header .h_tanto,
#order .header .h_seikyunm,
#order .header .h_souke,
#order #dtl-table-id .bumon,
#order #dtl-table-id .hachu,
#order #dtl-table-id .tanto,
#order #dtl-table-id .seikyuusaki,
#order #dtl-table-id .souke
{   
   width : 8%;
}

#order .header .h_shohin,
#order .header .h_tekiyo,
#order #dtl-table-id .tekiyo,
#order #dtl-table-id .shohin{
   width : 11%;   
}

#order .header .h_moushi,
#order #dtl-table-id .moushi{
   width : 3%;   
}

#order .header .h_biko,
#order .header .h_kamoku,
#order #dtl-table-id .kamoku,
#order #dtl-table-id .biko{
   width : 7%;   
}

#order .header .h_biko,
#order #dtl-table-id .biko { /* 2019/09/11 mihara #2642 */
   width : 6%;    
}

#order .header .h_shiharai,
#order #dtl-table-id .shiharai{
   width : 3.5%;   
}

#order #dtl-table-id .shiharai_kbn{
   width : 100%;  
   border-right:  none;
   border-bottom: none;
}

#order .header .h_hachu1,
#order .header .h_hachu2,
#order .header .h_hachu3,
#order .header .h_hachu4,
#order #dtl-table-id .hachu1,
#order #dtl-table-id .hachu2,
#order #dtl-table-id .hachu3,
#order #dtl-table-id .hachu4{
   width : 6%;   
}

#order .header .h_hachu2,
#order .header .h_hachu4,
#order #dtl-table-id .hachu2,
#order #dtl-table-id .hachu4{ /* 2019/09/11 mihara #2642 */
   width : 6.5%;   
}

#order .header .kamoku,
#order #dtl-table-id .biko,
#order #dtl-table-id .kamoku{
  border-right: none;
}


#order .header .h_suryo1,
#order .header .h_suryo2,
#order .header .h_suryo3,
#order .header .h_suryo4,
#order #dtl-table-id .suryo1,
#order #dtl-table-id .suryo2,
#order #dtl-table-id .suryo3,
#order #dtl-table-id .suryo4{
    width : 3%;    
}

#order .header .h_kenpin,
#order .header .h_sagaku,
#order #dtl-table-id .kenpin,
#order #dtl-table-id .sagaku
{
    width : 5%;     
}
#order .hachu_suryo,
#order .nyuka_suryo,
#order .keppin_suryo,
#order .henpin_suryo,
#order .jodai_tnk,
#order .hachu_tnk,
#order .hachu_prc,
#order .zeikomi,
#order .out_zei_hasu_prc{
    text-align: right;    
}

#order .header .h_hachudate,
#order .header .h_nohindate,
#order .header .h_kenpindate,
#order #dtl-table-id .hachudate,
#order #dtl-table-id .nohindate,
#order #dtl-table-id .kenpindate{
  width : 6.5%;  
}
#order .header .h_kenpindate,
#order #dtl-table-id .kenpindate{
  width : 8%;  
}
#order .header .h_hachudate,
#order #dtl-table-id .hachudate { /* 2019/09/11 mihara #2642 */
  width : 7%;  
}

#order .hachu_date,
#order .nohin_date,
#order .kenpin_date{
     text-align: center;
}


#order .list input{
    border : 0px;
    margin-left: 4%;
    margin-right: 4%;
    width  : 92%;
    height : 30px;
}

#order .list .kenpin_date {
    margin-left: 1%;
    margin-right: 1%;
    width  : 77%;
}

#order #dtl-table-id .kenpin input{
    width  : 1px;
    height : 1px;
}

#order .lbl_stamp_check.ui-state-active{
	display: block;
	background-color: #6CACD7;
	background-image: url(../../../img/check_1.png);
	text-shadow: -1px -1px 0 #69C;
	color: #FFF;
}

#order .lbl_stamp_check {
     background-color: transparent;
        width:100%;       
        margin-top:0;
        height: 32px;
	margin: 0;       
	background-image: url(../../../img/check_2.png);        
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 16px 12px;
	border: none;
	border-radius: 0;
	text-align: center;
	color: #999;
}

#order .header .h_denpyo,
#order .header .h_bumon,
#order .header .h_souke,
#order .header .h_shohin,
#order .header .h_suryo1,
#order .header .h_hachu,
#order .header .h_hachu3,
#order .header .h_hachu2,
#order .header .h_sagaku,
#order .header .h_suryo3,
#order .header .h_hachudate,
#order .header .h_biko,
#order .list .denpyo,
#order .list .bumon,
#order .list .souke,
#order .list .shohin,
#order .list .suryo1,
#order .list .hachu,
#order .list .hachu3,
#order .list .hachu2,
#order .list .sagaku,
#order .list .suryo3,
#order .list .hachudate,
#order .list .biko{
  border-bottom: 1px dotted #88B1D1;
}

#order .list .kamoku_text {
    width: 100%;
    height: 100%;
    border-right:  none;
    border-bottom: none;
   /* height: 64px;*/
}
#order .list .m_soko_cd {
	width: 99%;
	height: 100%;
	padding: 0px 0px;
}

#order #btn_save.disable_btn:active {
    background-image: url(../../../img/ico_save.png) !important ;
}

input.disable_btn {
    opacity: .5;
}

input:disabled, textarea:disabled {
}

#order #searchbtnarea {
  width: 100%;
  height: 35px;
  white-space: nowrap;
  margin-top: 5px;
  margin-bottom: 10px;
  /** 検索ボタン */
  /** クリアボタン */
}

#order #searchbtnarea #btn_search_k {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #296FA7;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94B9D6;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 0 0 5px;
  background-image: url(../../../img/ico_search.png);
  background-repeat: no-repeat;
  background-position: 28px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_search_k:active {
  background-color: #369;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img/ico_search_2.png);
}

#order #searchbtnarea #btn_clear_k {
  width: 120px;
  min-width: 120px;
  height: 32px;
  font-weight: bold;
  padding-top: 2px;
  color: #296FA7;
  letter-spacing: 0.1em;
  text-indent: 15px;
  background: none;
  border: 1px solid #94B9D6;
  border-radius: 3px;
  box-shadow: 1px 1px 0 #FFF;
  margin: 3px 5px 0 5px;
  background-image: url(../../../img/ico_close.png);
  background-repeat: no-repeat;
  background-position: 28px 9px;
  background-size: 13px 13px;
  /** ボタンオン（検索）*/
}
#order #searchbtnarea #btn_clear_k:active {
  background-color: #369;
  border: 1px solid #FFF;
  box-shadow: none;
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.2);
  color: #FFF;
  background-image: url(../../../img/ico_close_2.png);
}

#order #searchbtnarea input[type="button"] {
  float: right;
}
#order .dlg_seko_no
,#order .dlg_bumon{
    border-right: 1px solid #88B1D1;
}

#order .lbl_data_kbn{
    width: 6%;
}

#order .list table td.msg{
    font-weight: normal;
    font-size: 13px;
    border-right: none;
    border-top: 1px solid #88B1D1;
}


/* 軽減税率対応 keigen */

#order .list .hachu_prc.input_field {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    text-align: right;
    padding-right: 2px; /* 5px; 2019/09/11 mihara #2642 */
    height: 32px;
    line-height: 32px;
    color: black;
}

#order .list .hachu_prc.input_field.my-akaji {
    color: red;
}

#order .list input.zei_prc_edit {
    text-align: right;
}

#order .summary-footer {
    box-sizing: border-box;
    margin-top: 10px;
    left: 55%;
    width: 20%;
}

#order .summary-footer .note_1 {
  margin-top: 0;
}

#order .summary-footer .total_title {
    box-sizing: border-box;
    width: 40%;
}

#order .summary-footer .total_price {
    box-sizing: border-box;
    width: 60%;
    padding-right: 7px;
}

/* 2019/09/11 mihara add  cf. gashoen(arcbell) #2642 */
@media (max-width:1440px) { /* 1024,1280,1366,1440 */
    #order .hachu_date,
    #order .nohin_date,
    #order .kenpin_date{
        font-size: 12px;
    }
    #order .hachu_date,
    #order .nohin_date,
    #order .kenpin_date{
        font-size: 12px;
    }
} /* @media */

#order .list input.hachu_date {
    margin-left: 0;
    margin-right: 0;
    width  : 100%;
}
#order .list input.kenpin_date {
    margin-left: 0;
    margin-right: 0;
    width  : 84%;
}

#order #dtl-table-id td.hachu,
#order #dtl-table-id td.shohin,
#order #dtl-table-id td.hachudate,
#order #dtl-table-id td.biko {
  border-bottom: 1px solid #88B1D1;
}
