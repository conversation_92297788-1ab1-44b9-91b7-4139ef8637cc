{include file="fdn_head_std.tpl"}
{* include file="fdn_header_4.tpl" *}
{assign var='is_fdn_header_btn_new'           value=true }{* 新規入力 *}
{assign var='is_fdn_header_btn_menu'          value=false }{* サイドメニュー開閉 *}
{assign var='is_fdn_header_nowl_header_bumon' value=false }{* 部門 *}
{assign var='is_fdn_header_celemony_dialog'   value=false }{* 施行検索 *}
{assign var='is_fdn_header_btn_new'           value=true }{* 新規入力 *}
{assign var='is_fdn_header_btn_info'          value=false }{* インフォメーション *}
{assign var='is_fdn_header_btn_help'          value=false }{* ヘルプ *}
{assign var='is_fdn_header_btn_util'          value=false }{* 個人設定 *}
{assign var='is_fdn_header_btn_account'       value=true  }{* アカウント *}
{assign var='is_fdn_header_btn_logout'        value=true  }{* ログアウト *}
{include file="_fdn_header_base.tpl"}


<div class="container div-fixed-for-spin" id="main-container" style="display:none">
  <form  id="my-form-id">{* class="my-ctxt-readonly" *}
    <div id="main">
      <!-- div id="detail" class="no-sidemenu" -->
      <div id="order" class="no-sidemenu">

        <div id="page-header-line">
          <span class="page-title">{$page_title}</span>

          <div id="shori-mode">
            <span id="shori-new" class="shori-mode-cls" >新規</span>
            <span id="shori-change" class="shori-mode-cls" style="display:none" >修正</span>
          </div>

          <div id="last_upd_info" class="update"></div>
        </div><!-- /#page-header-line -->

        <div id="searchbtnarea" style="display:none">
{* <!--
          <input name="btn_search" id="btn_search" type="button" class="my-no-readonly" value="検索" />
		  <input type="button" name="btn_new2" id="btn_new2" value="新規" />
 --> *}
        </div><!-- /#searchbtnarea -->

<div id="my-form-div-wrapper">

  <div id="bloc_left" class="NO-main-adj-contents  base_1">

	<fieldset class="sepa_1">
	  <label for="kokyaku_kbn" class="cls_lbl_std require">顧客区分</label>
      <input type="hidden" name="kokyaku_kbn" id="kokyaku_kbn" class="cls_kokyaku_kbn cls_dd_std w20"/>

	  <label for="kokyaku_no" class="cls_lbl_std cls_lbl_kokyaku_no">顧客No.</label>
      <input name="kokyaku_no" id="kokyaku_no" type="text" class="txt w20 nl-right" value="" placeholder="(自動)" maxlength="10" />
      <div class="label dlg_pick_std  dlg_kokyaku_no no-bgc"></div>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="kokyaku_nm" class="cls_lbl_std">顧客名</label>
      <input name="kokyaku_nm1" id="kokyaku_nm1" type="text" class="txt w40" value="" tabindex="10" />
      <input name="kokyaku_nm2" id="kokyaku_nm2" type="text" class="txt w40" value="" tabindex="20" />
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="tokui_nm" class="cls_lbl_std cls_lbl_tokui_nm">得意先名</label>
      <input name="tokui_nm" id="tokui_nm" type="text" class="txt w80" value="" tabindex="50" />
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std" style="height:78px">住所</label>
      <div class="label lbl_zip w5">〒</div>
      <input type="text" data-zip="m1" id="yubin_no" class="txt zip_helper nl-right w20" value="" maxlength="8" tabindex="70" />
      <div id="" class="label dlg_zip cursor-pointer  dlg_yubin_no w10"></div>

      <input type="text" id="addr1" class="txt w80" value="" maxlength="40" tabindex="80" />
      <input type="text" id="addr2" class="txt l-right w80" value="" maxlength="40" tabindex="90" />
    </fieldset>

    <fieldset class="sepa_0" style="margin-top:10px">
      <label class="cls_caption_lbl w50">DM送付先</label>
      <button type="button" name="btn_copy_kokyaku_dm" class="btn_intl_easy btn_copy_kokyaku_dm" 
              title="">顧客の情報をコピー</button>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">宛名</label>
      <input name="dm_atena1" id="dm_atena1" type="text" class="txt w40" value=""/>
      <input name="dm_atena2" id="dm_atena2" type="text" class="txt w40" value=""/>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std" style="height:52px">住所</label>
      <div class="label lbl_zip dm_lbl_zip w5">〒</div>
      <input type="text" data-zip="m1" id="dm_yubin_no" class="txt zip_helper nl-right w20" value="" maxlength="8" />
      <div id="" class="label dlg_zip cursor-pointer  dlg_dm_yubin_no w10"></div>

      <input type="text" id="dm_addr1" class="dm_addr1 txt w45" value="" maxlength="40" />
      <input type="text" id="dm_addr2" class="dm_addr2 txt l-right w80" value="" maxlength="40" />
    </fieldset>

{* <!--
    <fieldset class="sepa_0" style="margin-top:10px">
	  <label for="" class="cls_lbl_std">紹介先</label>
      <input name="shokai_cd" id="shokai_cd" type="text" class="txt w20 nl-right" value="" readonly />
      <div class="label dlg_pick_std  dlg_shokai_cd"></div>
      <input name="shokai_nm" id="shokai_nm" type="text" class="txt w55" value="" readonly />
    </fieldset>
--> *}

    <fieldset class="sepa_0" style="margin-top:10px">
        <label for="" class="cls_lbl_std">取扱店</label>
        <input type="hidden" name="tenpo_cd" id="tenpo_cd" class="cls_tenpo_cd cls_dd_std w80"/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">契約団体１</label>
      <input name="dantai_cd" id="dantai_cd" type="text" class="txt w20 nl-right" value="" readonly />
      <div class="label dlg_pick_std  dlg_dantai_cd"></div>
      <input name="dantai_nm" id="dantai_nm" type="text" class="txt w55" value="" readonly />
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">契約団体２</label>
      <input name="dantai_cd2" id="dantai_cd2" type="text" class="txt w20 nl-right" value="" readonly />
      <div class="label dlg_pick_std  dlg_dantai_cd2"></div>
      <input name="dantai_nm2" id="dantai_nm2" type="text" class="txt w55" value="" readonly />
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">契約団体３</label>
      <input name="dantai_cd3" id="dantai_cd3" type="text" class="txt w20 nl-right" value="" readonly />
      <div class="label dlg_pick_std  dlg_dantai_cd3"></div>
      <input name="dantai_nm3" id="dantai_nm3" type="text" class="txt w55" value="" readonly />
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">勤務先</label>
      <input name="kimsaki_nm" id="kimsaki_nm" type="text" class="txt w80" value=""/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">勤務先TEL</label>
      <input name="kimsaki_tel" id="kimsaki_tel" type="text" class="txt w40" 
             placeholder="000-0000-0000" value="" maxlength="20" />
    </fieldset>

    <fieldset class="sepa_0" style="margin-top:5px">
	  <label for="" class="cls_lbl_std">会員No.</label>
      <input name="kaiin_no_disp" id="kaiin_no_disp" type="text" class="txt w30" readonly />
	  <label for="" class="cls_lbl_std">会員種別</label>
      <input name="kaiin_sbt_disp" id="kaiin_sbt_disp" type="text" class="txt w30" readonly />
    </fieldset>

    <fieldset class="sepa_0" >
	  <label for="" class="cls_lbl_std">顧客継承</label>
      <input name="keisho_cd" id="keisho_cd" type="text" class="txt w22 nl-right" value="" readonly />
      <div class="label dlg_pick_std  dlg_keisho_cd w8"></div>
      {* <!-- input name="keisho_nm" id="keisho_nm" type="text" class="txt w22" value="" readonly / --> *}

	  <label for="" class="cls_lbl_std">続柄</label>
      <input name="zoku_nm" id="zoku_nm" type="text" class="txt w30" readonly />
    </fieldset>

    <fieldset class="sepa_0" >
	  <label for="" class="cls_lbl_std">退会区分</label>
      <input name="my_taikai_kbn_nm" id="my_taikai_kbn_nm" type="text" class="txt w30" readonly />

	  <label for="" class="cls_lbl_std">退会日</label>
      <input type="text" id="my_taikai_ymd" class="txt date_auto_slash to_alpha_num my-type-date kaiin_taikai_etc_cls nl-right w24" value="" readonly />
      <div class="label dlg_date00 w6"></div>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">ブラック区分</label>
      <input type="hidden" name="black_kbn" id="black_kbn" class="cls_black_kbn cls_dd_std w30"/>
    </fieldset>

  </div><!-- /#bloc_left -->

  <div id="bloc_right" class="base_1">

	<fieldset class="sepa_1">
	  <label for="ref_kokyaku_no" class="cls_lbl_std">参照顧客No.</label>
      <input name="ref_kokyaku_no" id="ref_kokyaku_no" type="text" class="txt w20 nl-right" value="" placeholder="" readonly/>
      <div class="label dlg_pick_std  dlg_ref_kokyaku_no no-bgc"></div>

	  <label for="sakujyo_kbn" class="cls_lbl_std" style="margin-left:15%">削除区分</label>
      <input type="hidden" name="sakujyo_kbn" id="sakujyo_kbn" class="cls_sakujyo_kbn cls_dd_std w20"/>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="kokyaku_kana" class="cls_lbl_std require w20">顧客カナ</label>
      <input name="kokyaku_kana1" id="kokyaku_kana1" type="text" class="txt w40" value="" tabindex="30" maxlength="20" />
      <input name="kokyaku_kana2" id="kokyaku_kana2" type="text" class="txt w40" value="" tabindex="40" maxlength="20" />
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="tokui_busho_nm" class="cls_lbl_std">部署名</label>
      <input name="tokui_busho_nm" id="tokui_busho_nm" type="text" class="txt w80" value="" tabindex="60" />
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="sex_kbn" class="cls_lbl_std sex_kbn_deco_cls">性別</label>
      <span id="sex_kbn_radio_set" class="radio_set  sex_kbn_cls">
        <label for="sex_kbn_1" class="lbl_radio_std bg-pos-20 w12">男性</label>
        <input id="sex_kbn_1" name="sex_kbn" type="radio" value="1" tabindex="100" />
        <label for="sex_kbn_2" class="lbl_radio_std bg-pos-20 w12">女性</label>
        <input id="sex_kbn_2" name="sex_kbn" type="radio" value="2" />
        <label for="sex_kbn_0" class="lbl_radio_std bg-pos-20 w10">不明</label>
        <input id="sex_kbn_0" name="sex_kbn" type="radio" value="0" />
      </span>

      <label class="cls_lbl_std w16">生年月日</label>
      <input type="text" id="seinengappi_ymd" class="txt date_auto_slash to_alpha_num my-type-date nl-right w22" value="" tabindex="110" />
      <div class="label dlg_date00 w8"></div>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="tel" class="cls_lbl_std">TEL</label>
      <input name="tel1" id="tel1" type="text" class="txt w27" placeholder="000-0000-0000" value="" maxlength="20" tabindex="120" />
      <input name="tel2" id="tel2" type="text" class="txt w27" placeholder="000-0000-0000" value="" maxlength="20" tabindex="130" />
      <input name="tel3" id="tel3" type="text" class="txt w26" placeholder="000-0000-0000" value="" maxlength="20" tabindex="140" />
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="e_mail" class="cls_lbl_std">e-mail</label>
      <input name="e_mail" id="e_mail" type="text" class="txt w80" tabindex="150" />
    </fieldset>

    <fieldset class="sepa_0" style="margin-top:10px">
      <label class="cls_caption_lbl w50">請求書送付先</label>
      <button type="button" name="btn_copy_kokyaku_sekyu" class="btn_intl_easy btn_copy_kokyaku_sekyu" 
              title="">顧客の情報をコピー</button>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">宛名</label>
      <input name="sekyu_atena1" id="sekyu_atena1" type="text" class="txt w40" value=""/>
      <input name="sekyu_atena2" id="sekyu_atena2" type="text" class="txt w40" value=""/>
    </fieldset>

	<fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std" style="height:52px">住所</label>
      <div class="label lbl_zip w5">〒</div>
      <input type="text" data-zip="m1" id="sekyu_yubin_no" class="txt zip_helper nl-right w20" value="" maxlength="8" />
      <div id="" class="label dlg_zip cursor-pointer  dlg_sekyu_yubin_no w10"></div>

      <input type="text" id="sekyu_addr1" class="txt w45" value="" maxlength="40" />
      <input type="text" id="sekyu_addr2" class="txt l-right w80" value="" maxlength="40" />
    </fieldset>

    <fieldset class="sepa_0" style="margin-top:10px">
	  <label for="" class="cls_lbl_std">状態</label>
      <input type="hidden" name="jotai_kbn" id="jotai_kbn" class="cls_jotai_kbn cls_dd_std w30"/>

      <label class="cls_lbl_std">死亡日</label>
      <input type="text" id="shibo_ymd" class="txt date_auto_slash to_alpha_num my-type-date nl-right w22" value="" />
      <div class="label dlg_date00 w8"></div>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">請求書区分</label>
      <input type="hidden" name="seikyusho_kbn" id="seikyusho_kbn" class="cls_seikyusho_kbn cls_dd_std w30"/>

      <label class="cls_lbl_std">締日</label>
      <input type="text" id="simebi_ymd" class="txt text-right w10" value="" maxlength="2" />
      <label class="cls_text_lbl_std w20">日（月末 99）</label>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">請求書フォーマット</label>
      <input type="hidden" name="seikyu_format" id="seikyu_format" class="cls_seikyu_format cls_dd_std w30"/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">DM発行</label>
      <input type="hidden" name="dm_hakko_kbn" id="dm_hakko_kbn" class="cls_dm_hakko_kbn cls_dd_std w30"/>

	  <label for="" class="cls_lbl_std">仏壇DM発行</label>
      <input type="hidden" name="butsudan_dm_hakko_kbn" id="butsudan_dm_hakko_kbn" class="cls_butsudan_dm_hakko_kbn cls_dd_std w30"/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">仏壇有無</label>
      <input type="hidden" name="butsudan_kbn" id="butsudan_kbn" class="cls_butsudan_kbn cls_dd_std w30"/>
    </fieldset>

    <fieldset class="sepa_0" style="margin-top:10px">
	  <label for="" class="cls_lbl_std">宗旨</label>
      <input type="hidden" name="syushi_cd" id="syushi_cd" class="cls_syushi_cd cls_dd_std w30"/>

	  <label for="" class="cls_lbl_std">宗派</label>
      <input type="hidden" name="syuha_cd" id="syuha_cd" class="cls_syuha_cd cls_dd_std w30"/>
      <!-- input type="hidden" name="syuha_nm" id="syuha_nm" / -->
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">生前予約</label>
      <input type="hidden" name="seizenyoyaku_kbn" id="seizenyoyaku_kbn" class="cls_seizenyoyaku_kbn cls_dd_std w30"/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">生前予約入金額</label>
      <input type="text" id="seizenyoyaku_nyukin_prc" class="seizenyoyaku_nyukin_prc txt text-right my-select-all commafy w30" value="" />

      <label class="cls_lbl_std">生前予約契約日</label>
      <input type="text" id="seizenyoyaku_keiyaku_ymd" class="txt date_auto_slash to_alpha_num my-type-date nl-right w22" value="" />
      <div class="label dlg_date00 w8"></div>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std">振込先</label>
      <input name="transfer_bank_cd" id="transfer_bank_cd" type="text" class="txt w20 nl-right" value="" />
      <div class="label dlg_pick_std  dlg_transfer_bank_cd"></div>
      <input name="transfer_bank_info" id="transfer_bank_info" type="text" class="txt w55" value="" readonly />
    </fieldset>

  </div><!-- /#bloc_right -->

  <div id="bloc_footer" class="base_1" style="margin-top:10px;padding-bottom:10px">

    <fieldset class="sepa_0">
      <label for="" class="cls_lbl_std XX-cls_lbl_height_2 w10" style="height:52px">備考</label>
      <textarea id="biko" class="biko cls_textarea_height_2 l-btm l-right w70" maxlength="120"></textarea>
    </fieldset>


    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std cls_lbl_toki_jiko1 w10">特記事項１</label>
      <input name="toki_jiko1" id="toki_jiko1" type="text" class="txt w70" value=""/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std w10">特記事項２</label>
      <input name="toki_jiko2" id="toki_jiko2" type="text" class="txt w70" value=""/>
    </fieldset>

    <fieldset class="sepa_0">
	  <label for="" class="cls_lbl_std w10">仏壇特記事項</label>
      <input name="butsudan_toki_jiko" id="butsudan_toki_jiko" type="text" class="txt w70" value=""/>
    </fieldset>

  </div><!-- /#bloc_footer -->


  <div id="my-form-footer">
    <div class="buttons">
      <input type="button" name="btn_save"   id="btn_save"   value="保存" />
      <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
      {if $is_dev_env}
        <input type="button" name="btn_delete" id="btn_delete" value="削除(dev)" />
      {/if}{* is_dev_env *}
    </div>
  </div><!-- /#my-form-footer -->

</div><!-- /#my-form-div-wrapper -->


      </div><!-- /#order -->
    </div><!-- /#main -->

  </form><!-- /#my-form-id -->
</div><!-- main-container -->

<script id="my-data-json-id" type="application/json">
    {$my_data_json|smarty:nodefaults}
</script>

{include file="fdn_footer_std.tpl"}

