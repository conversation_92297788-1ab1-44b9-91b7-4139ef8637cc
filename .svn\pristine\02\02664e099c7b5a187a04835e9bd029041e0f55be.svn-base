<div id="goods" class="itemex h2">
<div class="list list-item-multi">
{if count($item_list|smarty:nodefaults) <= 0}<div class="update disp-no-data2"><span>該当する商品はありません</span></div>{/if}
<ul>
{foreach from=$item_list|smarty:nodefaults item=item name=foo}
<li data-item="{$item.shohin_cd}" 
        data-article="{$item.shohin_nm}" 
        data-unit_price="{$item.hanbai_tnk}" 
        data-dai_bunrui_cd="{$item.dai_bunrui_cd}" 
        data-chu_bunrui_cd="{$item.chu_bunrui_cd}" 
        data-mokuteki_kbn="{$itemex_mokuteki_kbn}"
        data-shohin_kbn="{$item.shohin_kbn}" 
        data-summary="{$item.shohin_tkiyo_nm}" 
        data-org-summary="{$item.shohin_tkiyo_nm}" 
        data-nm_input_kbn="{$item.nm_input_kbn}"
        data-org-price="{$item.hanbai_tnk}" 
        data-org-shohin_nm="{$item.shohin_nm}"
        data-org-quantity="{$item.quantity}" 
        data-org-chargekbn="{$item.chargekbn}" 
        data-category_kbn="{$item.category_kbn}"
        data-uri_zei_kbn="{$item.uri_zei_kbn}"
        data-zei_rtu="{$item.zei_rtu}"
        data-order_flg="{$item.order_flg}"
        data-fixed_by_plan="{$item.fixed_by_plan}"
        data-zei_hasu_kbn="{$item.zei_hasu_kbn}" {if $gazo_shohin_prc_kbn==1}style="height: 350px;"{/if}
        data-reduced_tax_rate="{$item.reduced_tax_rate}"
        data-zei_cd="{$item.zei_cd}"
        >
 <!-- cf. picker_std.tpl
	   <! - - コース名 - - >	
	   {if $item.is_course==1}
	   <div class="plan-course" style="opacity:0.9">
		 <div>
		   <div class="plan-course-1">{$item.course_str}</div>
		   <div class="plan-course-2">コース</div>
		 </div>
	   </div>
	   {/if}
	   <! - - プラン名 - - >	
	   {if $item.is_course==2}
	   <div class="plan-name" style="background-color:{$item.web_disp_color};opacity:0.9">
		 <div class="plan-name-1">{$item.web_disp_plan}</div>
	   </div>	
	   {/if}
	   <! - - 特典名 - - >	
	   {if $item.is_course==3}
	   <div class="gojokai_tkn" style="opacity:0.9">
		 <div>
		   <div class="gojokai_tkn-1">{$item.course_str}</div>
		 </div>
	   </div>
	   {/if}
       -->
  <span class="pic">
{if !$flg_no_disp_tkiyo && ($item.nm_input_kbn == 3 || $item.nm_input_kbn == 4)}{include file="juchu/itemex/tkiyo-ctrl-item.tpl"}{/if}
<a class="my-popup-img" href="{$app_base}mref/gazodlg/shohinimg/imgid/{$item.gazo_img}/v/{$item.shohin_mst_mod_cnt}/av/{$app_version}" title="{$item.shohin_nm}　{$item.hanbai_tnk|msi_number_format}円" rel="some-category-xxx"><img src="{$app_base}mref/gazodlg/shohinimg/imgid/{$item.gazo_img}/v/{$item.shohin_mst_mod_cnt}/av/{$app_version}" alt="{$item.shohin_nm}" width="360px" height="210px" ></a></span>
  {if !$flg_no_input_shohin_nm && ($item.nm_input_kbn == 2 || $item.nm_input_kbn == 4)}
      <span class="shohin_nm"><input type="text" name="shohin_nm" value="{$item.shohin_nm}" class="txt input_field" maxlength="40" data-org-shohin_nm="{$item.shohin_nm}" />
      </span>{else}<span class="name">{$item.shohin_nm}</span>{/if}
  <span class="price">{if $item.tnk_chg_kbn}
    <input type="text" name="price" id="hanbai_tnk" value="{$item.hanbai_tnk|msi_number_format}" 
           class="txt i_price input_field text-center to_alpha_num" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="10" data-org-price="{$item.hanbai_tnk}" />
    {else}<span class="n">{$item.hanbai_tnk|msi_number_format}</span>{/if}円
    {if $gazo_shohin_zei_kbn==1} 
        <span class="zeikomi">{if $item.uri_zei_kbn==2}&nbsp;(税込<span class="n2">{$item.hanbai_tnk_zei|msi_number_format}</span>円)
        {elseif $item.uri_zei_kbn==0}（非）{else}（内）{/if}</span></span>
    {elseif $gazo_kain_prc_kbn==1 && $item.gojokai_nebiki_prc!=0} 
            <span class="kaiin">
            &nbsp;(会員<span class="n3">{$item.kaiin_prc|msi_number_format}</span>円)
    {/if}           
        </span>
	{if $gazo_shohin_prc_kbn==1} 
		{if $gazo_kain_prc_kbn==1 && ($item.gojokai_nebiki_prc!="0" && $item.gojokai_nebiki_prc!="")} 
			<span class="pricex">
			  <span class="n">{$item.kaiin_prc|msi_number_format}</span>円 
			  {if $gazo_shohin_zei_kbn==1} 
				  <span class="zeikomi">{if $item.uri_zei_kbn==2}&nbsp;(税込<span class="n2">{$item.kaiin_prc_zei|msi_number_format}</span>円)
				  {elseif $item.uri_zei_kbn==0}（非）{else}（内）{/if}</span>
			  {/if}
			</span>  
		{/if}           
	{/if}           
  <span class="counter {if $item.quantity } on{/if}">
	<span class="c c100" data-digit="100">
	  <span class="plus"></span>
	  <span class="minus"></span>
	  <span class="num">{if $item.quantity}{$item.quantity/100|floor}{else}0{/if}</span>
	</span>
	<span class="c c10" data-digit="10">
	  <span class="plus"></span>
	  <span class="minus"></span>
	  <span class="num">{if $item.quantity}{$item.quantity/10%10}{else}0{/if}</span>
	</span>
	<span class="c c1" data-digit="1">
	  <span class="plus"></span>
	  <span class="minus"></span>
	  <span class="num">{if $item.quantity}{$item.quantity%10}{else}0{/if}</span>
	</span>
	<input type="hidden" name="quantity_{$item.shohin_cd}" id="quantity_{$item.shohin_cd}" class="quantity" value="{if $item.quantity}{$item.quantity}{else}0{/if}" />
  </span>
  {if !$flg_no_disp_keigen_deco && $item.reduced_tax_rate==2}{include file="juchu/itemex/keigen-deco-item.tpl"}{/if}{* keigen *}
</li>
{/foreach}
</ul>
</div><!-- /.list -->
</div><!-- /#goods -->

{include file="juchu/itemex/picker-item-pick.tpl"}
