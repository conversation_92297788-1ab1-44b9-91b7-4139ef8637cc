<?php
  /**
   * DataMapper_ZeiCodeMst
   *
   * 科目 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Kayo
   * @since      2014/06/29
   * @filesource 
   */

  /**
   * 税コードマスタ データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2013/xx/xx
   */
class DataMapper_ZeiCodeMst extends DataMapper_Abstract
{
    /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2013/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array(), $isDateEffective=true)
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
		$orderBy = str_replace('T.case', 'case', $orderBy);	// エラーになるので修正 2014/05/13 MSI Kayo
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.kamoku_cd, zei_cd ';
        }

        if ( $isDateEffective ) {
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
  SELECT 
		kamoku_cd		-- 科目コード
	   ,zei_cd			-- 税コード
	   ,zei_cd_mn		-- 税コード名
	   ,shoihi_zei_cd	-- 消費税コード
	   ,delete_flg		-- 削除フラグ
  FROM zei_code_mst
 WHERE delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
            , $param );

        return $select;
    }
}
