<?php
  /**
   * DataMapper_Pdf0701
   *
   * PDF出力 商品区分別売上一覧 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/12
   * @filesource 
   */

  /**
   * PDF出力 商品区分別売上一覧 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/12
   */
class DataMapper_Pdf0701 extends DataMapper_Abstract
{
	// 出力順ID
	const ORDERKBN_SEKONO = 1;	// 施行番号
	const ORDERKBN_SEKYUNO = 2;	// 請求先コード

	/**
     * PDF出力 商品区分別売上一覧 取得(商品区分別)
     * <AUTHOR> Sato
     * @since      2014/05/12
     * @param Msi_Sys_Db $db
     * @param type $ymd_st
     * @param type $ymd_ed
     * @param type $bum_ary 部門配列
     * @param type $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
	,T1.syukei_kbn		
    ,T1.tanto_cd
    ,T1.bumon_cd
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,CASE WHEN  T1.juchu_suryo = 0 THEN 
            0
        ELSE    
            round(T1.uri_prc / T1.juchu_suryo)
        END        AS  uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
    ,T1.nebiki_prcx
	,T1.gojokai_nebiki_prc	-- 互助会値引額	2016/03/20 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,tan.tanto_nm
    ,sho_kbn.shohin_kbn_nm
  FROM (
    SELECT
         0                  AS  record_kbn
		,0					AS	syukei_kbn		
        ,COALESCE(k.seko_tanto_cd, h.tanto_cd) AS tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,d.shohin_cd
        ,d.shohin_nm    
        ,SUM(d.juchu_suryo)       AS  juchu_suryo
        ,0                        AS  uri_tnk         
        ,SUM(d.uri_prc)           AS  uri_prc
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc) AS  nebiki_prc
        ,SUM(d.nebiki_prc)        AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)         AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY          
        COALESCE(k.seko_tanto_cd, h.tanto_cd)
       ,d.bumon_cd
       ,d.shohin_kbn
       ,d.shohin_cd
       ,d.shohin_nm    
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0    
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
   UNION ALL
        -- 合計
    SELECT
         0                   AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,'99999999'          AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY   d.bumon_cd
                ,d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc+ d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd, d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,d.bumon_cd
        ,NULL				 AS	 shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,'999999'            AS  bumon_cd
        ,d.shohin_kbn        AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,'999999'            AS  bumon_cd
        ,NULL				 AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR	SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
ORDER BY
		  T.bumon_cd
        , T.record_kbn
        , T.shohin_kbn
  		, T.syukei_kbn		
		, T.uri_prc DESC
        , T.tanto_cd
END_OF_SQL
            );

        return $select;
    }
    
    /**
     * PDF出力 商品区分別売上一覧 取得(施工先別)
     * <AUTHOR> Kayo
     * @since      2014/08/28
     * @param Msi_Sys_Db $db
     * @param string $ymd_st
     * @param string $ymd_ed
     * @param array $bum_ary 部門配列
     * @param array $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function findSeko($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary);
        $order = self::getOrder($order_kbn);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
    ,T1.syukei_kbn		
    ,T1.data_kbn
    ,T1.bumon_cd
    ,T1.uketuke_tanto_cd    AS mitu_tanto_cd -- 見積担当者CD
    ,mitu.tanto_nm          AS mitu_tanto_nm -- 見積担当者名
    ,T1.tanto_cd                             -- 施行担当者CD
    ,tan.tanto_nm                            -- 施行担当者名
    ,T1.uri_den_no
    ,T1.seko_no
    ,T1.sekyu_nm
    ,T1.souke_nm
    ,T1.juchu_ymd			
    ,T1.data_kbn_nm
    ,T1.data_kbn_nml
    ,T1.data_sbt
    ,T1.msi_no
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,T1.uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
    ,T1.nebiki_prcx		
    ,T1.gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,sho_kbn.shohin_kbn_nm
    ,T1.kaiin_nm
    ,T1.keishiki_nm
    ,T1.basho_nm
    ,T1.kaishu_kbn -- 回収区分
  FROM (
    SELECT
         0  AS  record_kbn
        ,0  AS  syukei_kbn		
        ,h.data_kbn
        ,h.bumon_cd
        ,COALESCE(seko_tanto_cd, h.tanto_cd) AS tanto_cd
        ,h.uri_den_no
        ,CASE   WHEN h.data_kbn = 3 THEN
                    h.uri_den_no
                ELSE 	
                    h.seko_no
                END         AS  seko_no
        ,h.sekyu_nm
        ,CASE   WHEN h.data_kbn = 3 THEN
                    null
                ELSE 	
                    k.souke_nm
                END         AS  souke_nm
        ,TO_CHAR(CASE WHEN h.data_kbn IN (1,2,4,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
                COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
         ELSE
                COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') AS	juchu_ymd			
        ,CASE   WHEN h.data_kbn = 1 THEN
                        '(葬)'
                WHEN h.data_kbn = 2 THEN
                        '(法)'
                WHEN h.data_kbn = 3 THEN
                        '(単)'
                WHEN h.data_kbn = 4 THEN
                        '(別)'
                ELSE '(生)' END     AS  data_kbn_nm
        ,CASE   WHEN h.data_kbn = 1 THEN
                        '1：葬儀'
                WHEN h.data_kbn = 2 THEN
                        '2：法事'
                WHEN h.data_kbn = 3 THEN
                        '3：単品'
                WHEN h.data_kbn = 4 THEN
                        '4：別注品'
                ELSE '5：生前依頼' END  AS  data_kbn_nml
        ,d.data_sbt
        ,d.msi_no
        ,d.shohin_kbn
        ,d.shohin_cd
        ,d.shohin_nm    
        ,d.juchu_suryo            AS  juchu_suryo
        ,d.uri_tnk                AS  uri_tnk         
        ,d.uri_prc                AS  uri_prc
        ,d.nebiki_prc + d.gojokai_nebiki_prc AS  nebiki_prc   
        ,d.nebiki_prc             AS  nebiki_prcx
        ,d.gojokai_nebiki_prc     AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,d.hoshi_prc              AS  hoshi_prc
        ,d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc  AS  gokei_uri_prc
        ,k.kaiin_kbn    || ':' || cnm_kaiin.kbn_value_lnm        AS kaiin_nm
        ,k.keishiki_kbn || ':' || cnm_keishiki.kbn_value_lnm     AS keishiki_nm
        ,CASE k.keishiki_kbn 
            WHEN 4 THEN     -- 火葬式
                sn5.basho_nm
            ELSE 
                sn7.basho_nm
         END                      AS basho_nm
        ,k.uketuke_tanto_cd -- 見積担当者CD
        ,CASE WHEN h.data_kbn IN (3,4) THEN cnm_kaishu.kbn_value_lnm
              ELSE null 
         END                                                    AS kaishu_kbn   -- 回収区分
      FROM uriage_denpyo h
        LEFT JOIN uriage_denpyo_msi d 
            ON    h.uri_den_no = d.uri_den_no 
            AND   0            = d.delete_flg
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no       = k.seko_no 
            AND 0              = k.delete_flg
        LEFT JOIN code_nm_mst cnm_kaiin
            ON cnm_kaiin.code_kbn          = '0030' -- 会員区分
            AND cnm_kaiin.kbn_value_cd_num = k.kaiin_kbn
            AND cnm_kaiin.delete_flg       = 0
        LEFT JOIN code_nm_mst cnm_keishiki
            ON cnm_keishiki.code_kbn          = '0040' -- 葬儀形式
            AND cnm_keishiki.kbn_value_cd_num = k.keishiki_kbn
            AND cnm_keishiki.delete_flg       = 0
        LEFT JOIN seko_nitei sn5 -- 出棺
        ON  sn5.seko_no    = k.seko_no
        AND sn5.nitei_kbn  = 5
        AND sn5.delete_flg = 0
        LEFT JOIN seko_nitei sn7 -- 葬儀
        ON  sn7.seko_no    = k.seko_no
        AND sn7.nitei_kbn  = 7
        AND sn7.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_kaishu
        ON  '0430'       = cnm_kaishu.code_kbn
        AND h.kaishu_kbn = cnm_kaishu.kbn_value_cd_num
        AND 0            = cnm_kaishu.delete_flg
      WHERE $where
      AND (d.juchu_suryo <> 0
      OR   d.uri_prc     <> 0           
      OR   d.nebiki_prc  <> 0           
      OR   d.gojokai_nebiki_prc <> 0 -- 2016/03/21 ADD Kayo	  
      OR   d.hoshi_prc   <> 0)           
   UNION ALL
    -- 施工合計
    SELECT
         0      AS  record_kbn
        ,1      AS  syukei_kbn		
        ,h.data_kbn     AS  data_kbn
        ,d.bumon_cd
        ,COALESCE(seko_tanto_cd, h.tanto_cd) AS tanto_cd
        ,h.uri_den_no
        ,CASE   WHEN h.data_kbn = 3 THEN
                    h.uri_den_no
                ELSE 	
                    h.seko_no
         END                            AS  seko_no
        ,NULL                           AS  sekyu_nm
        ,NULL                           AS  souke_nm
        ,NULL                           AS  juchu_ymd			
        ,NULL                           AS  data_kbn_nm
        ,NULL                           AS  data_kbn_nml
        ,9                              AS  data_sbt
        ,0                              AS  msi_no
        ,NULL                           AS  shohin_kbn
        ,'99999999'                     AS  shohin_cd
        ,NULL                           AS  shohin_nm    
        ,SUM(d.juchu_suryo)             AS  juchu_suryo
        ,NULL                           AS  uri_tnk         
        ,SUM(d.uri_prc)                 AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)              AS  nebiki_prcx
        ,SUM(d.gojokai_nebiki_prc)      AS  gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)               AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
        ,null                           AS kaiin_nm
        ,null                           AS keishiki_nm
        ,null                           AS basho_nm
        ,null                           AS uketuke_tanto_cd -- 見積担当者CD
        ,null                           AS kaishu_kbn -- 回収区分
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
        LEFT JOIN seko_kihon_info k 
          ON h.seko_no       = k.seko_no 
          AND 0              = k.delete_flg
      WHERE $where
      GROUP BY   d.bumon_cd
        ,h.uri_den_no
        ,h.seko_no
        ,COALESCE(seko_tanto_cd, h.tanto_cd)
        HAVING SUM(d.juchu_suryo) <> 0
        OR    SUM(d.uri_prc)     <> 0           
        OR    SUM(d.nebiki_prc)  <> 0           
        OR    SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
        OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                             AS  record_kbn
        ,0                              AS  syukei_kbn		
        ,0                              AS  data_kbn
        ,d.bumon_cd
        ,NULL                           AS  tanto_cd
        ,NULL                           AS  uri_den_no
        ,NULL                           AS  seko_no
        ,NULL                           AS  sekyu_nm
        ,NULL                           AS  souke_nm
        ,NULL                           AS  juchu_ymd			
        ,NULL                           AS  data_kbn_nm
        ,NULL                           AS  data_kbn_nml
        ,9                              AS  data_sbt
        ,0                              AS  msi_no
        ,d.shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
        ,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
        ,null           AS kaiin_nm
        ,null           AS keishiki_nm
        ,null           AS basho_nm
        ,null                           AS uketuke_tanto_cd -- 見積担当者CD
        ,null                           AS kaishu_kbn -- 回収区分
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
          ON h.seko_no  = k.seko_no 
         AND 0          = k.delete_flg
      WHERE $where
      GROUP BY d.bumon_cd, d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                     AS  record_kbn
        ,1                      AS  syukei_kbn		
        ,0                      AS  data_kbn
        ,d.bumon_cd
        ,NULL                   AS  tanto_cd
        ,NULL                   AS  uri_den_no
        ,NULL                   AS  seko_no
        ,NULL                   AS  sekyu_nm
        ,NULL                   AS  souke_nm
        ,NULL                   AS  juchu_ymd			
        ,NULL                   AS  data_kbn_nm
        ,NULL                   AS  data_kbn_nml
        ,9                      AS  data_sbt
        ,0                      AS  msi_no
        ,NULL                   AS  shohin_kbn
        ,NULL                   AS  shohin_cd
        ,NULL                   AS  shohin_nm    
        ,SUM(d.juchu_suryo)     AS  juchu_suryo
        ,NULL                   AS  uri_tnk         
        ,SUM(d.uri_prc)         AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)      AS  nebiki_prcx
        ,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)       AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
        ,null                   AS kaiin_nm
        ,null                   AS keishiki_nm
        ,null                   AS basho_nm
        ,null                           AS uketuke_tanto_cd -- 見積担当者CD
        ,null                           AS kaishu_kbn -- 回収区分
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no    =   k.seko_no 
            AND 0           =   k.delete_flg
      WHERE $where
      GROUP BY d.bumon_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR    SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                 AS  record_kbn
        ,0                  AS  syukei_kbn		
        ,0                  AS  data_kbn
        ,'999999'           AS  bumon_cd
        ,NULL               AS  tanto_cd
        ,NULL               AS  uri_den_no
        ,NULL               AS  seko_no
        ,NULL               AS  sekyu_nm
        ,NULL               AS  souke_nm
        ,NULL               AS  juchu_ymd			
        ,NULL               AS  data_kbn_nm
        ,NULL               AS  data_kbn_nml
        ,9                  AS  data_sbt
        ,0                  AS  msi_no
        ,d.shohin_kbn       AS  shohin_kbn   
        ,NULL               AS  shohin_cd
        ,NULL               AS  shohin_nm    
        ,SUM(d.juchu_suryo) AS  juchu_suryo
        ,NULL               AS  uri_tnk         
        ,SUM(d.uri_prc)     AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)  AS  nebiki_prcx
        ,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額   2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)   AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
        ,null               AS kaiin_nm
        ,null               AS keishiki_nm
        ,null               AS basho_nm
        ,null                           AS uketuke_tanto_cd -- 見積担当者CD
        ,null                           AS kaishu_kbn -- 回収区分
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no    =   k.seko_no 
            AND 0           =   k.delete_flg
      WHERE $where
      GROUP BY d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR    SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                 AS  record_kbn
        ,1                  AS  syukei_kbn		
        ,0                  AS  data_kbn
        ,'999999'           AS  bumon_cd
        ,NULL               AS  tanto_cd
        ,NULL               AS  uri_den_no
        ,NULL               AS  seko_no
        ,NULL               AS  sekyu_nm
        ,NULL               AS  souke_nm
        ,NULL               AS  juchu_ymd			
        ,NULL               AS  data_kbn_nm
        ,NULL               AS  data_kbn_nml
        ,9                  AS  data_sbt
        ,0                  AS  msi_no
        ,NULL               AS  shohin_kbn   
        ,NULL               AS  shohin_cd
        ,NULL               AS  shohin_nm    
        ,SUM(d.juchu_suryo) AS  juchu_suryo
        ,NULL               AS  uri_tnk         
        ,SUM(d.uri_prc)     AS  uri_prc   
        ,SUM(d.nebiki_prc + d.gojokai_nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)  AS  nebiki_prcx
        ,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額   2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)   AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc + d.gojokai_nebiki_prc)  AS  gokei_uri_prc
        ,null               AS kaiin_nm
        ,null               AS keishiki_nm
        ,null               AS basho_nm
        ,null                           AS uketuke_tanto_cd -- 見積担当者CD
        ,null                           AS kaishu_kbn -- 回収区分
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no    =   k.seko_no 
            AND 0           =   k.delete_flg
      WHERE $where
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
	  OR    SUM(d.gojokai_nebiki_prc) <> 0 -- 2016/03/21 ADD Kayo	  
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN tanto_mst mitu
      ON T1.uketuke_tanto_cd = mitu.tanto_cd 
      AND mitu.delete_flg    = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
$order
END_OF_SQL
            );

        return $select;
    }

    /**
     * WHERE句を取得
	 * 
     * <AUTHOR> Sato
     * @since      2014/05/14
     * @version 2015/11/13 コンバート区分の参照を追加、別注品が抽出されない件を修正 Kayo
     * @param string $ymd_st
     * @param string $ymd_ed
     * @param array $bum_ary 部門配列
     * @param array $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @return string WHERE句
     */
    private static function getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary)
    {
        $where = null;

        // 期間
        if (isset($ymd_st) && isset($ymd_ed)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
                COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') BETWEEN '".$ymd_st."' AND '".$ymd_ed."'";
        } else if (isset($ymd_st)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') >= '".$ymd_st."'";
        } else if (isset($ymd_ed)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') <= '".$ymd_ed."'";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }

        // 部門
        if (isset($bum_ary) && count($bum_ary) > 0) {
            $ary = array();
            foreach ($bum_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.bumon_cd IN (".implode(',', $ary).")";
        }

        // 商品区分
        if (isset($sho_kbn_ary) && count($sho_kbn_ary) > 0) {
            $ary = array();
            foreach ($sho_kbn_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.shohin_kbn IN (".implode(',', $ary).")";
        }

        // 大分類
        if (isset($daiBunrui_ary) && count($daiBunrui_ary) > 0) {
            $ary = array();
            foreach ($daiBunrui_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.dai_bunrui_cd IN (".implode(',', $ary).")";
        }

        if (isset($where)) { $where .= ' AND '; }
        $where .= 'h.delete_flg = 0
                AND h.convert_kbn = 0
        ';

        return $where;
    }

	/**
     * ORDER句を取得
	 * 
     * <AUTHOR> Kayo
     * @since      2014/08/17
     * @param string $order_kbn
     * @return string WORDER句
     */
    private static function getOrder($order_kbn) {
        $order = 'ORDER BY
					  T.bumon_cd
					, T.record_kbn
					, T.data_kbn
			';		
        switch ($order_kbn) {
		case self::ORDERKBN_SEKONO:
			$order .= '
				, T.seko_no
				, T.uri_den_no
			';	
			break;
		case self::ORDERKBN_SEKYUNO:
			$order .= '
				, T.uri_den_no
				, T.seko_no
			';	
			break;
        }
		$order .= '
			, T.data_sbt
			, T.syukei_kbn		
			, T.shohin_kbn
			, T.msi_no
		';	
        return $order;
    }
}
