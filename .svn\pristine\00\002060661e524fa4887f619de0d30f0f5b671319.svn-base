/** 
 * @fileoverview 領収書発行
 */
msiGlobalObj.doReadonlyByMyself = true; // 参照専用は独自処理
$(function () {
    "use strict";

    var msg01 = "データが変更されています. \nこのデータを保存せず、ページ移動してよろしいですか？";

    var utils = window.msiBbUtils;

    var hako_kbn_last = null;

    var never_show = 0;

    var MeisaiModel = Backbone.Model.extend({

        defaults: function () {
            return {
                ryoshusho_no: null,
                hako_count: null,
                hako_kbn: null,
                disp_kbn: null,
                syukin_tanto_cd: null,
                syukin_tanto_nm: null,
                jimu_tanto_cd: null,
                jimu_tanto_nm: null,
                atena: null,
                kin_1: null,
                kin_2: null,
                kin_3: null,
                way_1: null,
                way_2: null,
                way_3: null,
                zei_1: null,
                zei_2: null,
                zei_3: null,
                gokei: null,
                tadashikaki_cd: null, // 但し書きコード     2016/03/16 ADD Kayo
                tadashikaki: null,
                biko: null,
                hako_date: null,
                haki_date: null,
                haki_user: null,
                is_edit: false,
                seq_no: meisaiList.nextSeqNo(),
                line_no: -1,
            };
        },

        // 金額の集計を返す
        sumZan: function () {
            var sum = +this.get('kin_1') + +this.get('kin_2') + +this.get('kin_3');
            return sum;
        },

        // ある税CD の領収額を返す
        getKinAllWithZeiCd: function (zei_cd) {
            var that = this;
            var kin = _.reduce([1, 2, 3], function (accum, cnt) {
                if (that.get('zei_' + cnt) == zei_cd) {
                    accum += +that.get('kin_' + cnt);
                }
                return accum;
            }, 0);
            return kin;
        },

        validation: {
            hako_kbn: {
                required: true,
            },
            disp_kbn: {
                required: true,
            },
            syukin_tanto_cd: {
                required: false,
                pattern: 'number',
            },
            syukin_tanto_nm: {
                required: false,
            },
            jimu_tanto_cd: {
                required: false,
                pattern: 'number',
            },
            jimu_tanto_nm: {
                required: false,
            },
            atena: {
                required: false,
                maxLength: 40,
            },
            kin_1: [
                {
                    required: function (value, attr, computed) {
                        var way_1 = this.get('way_1');
                        if (way_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            kin_2: [
                {
                    required: function (value, attr, computed) {
                        var way_2 = this.get('way_2');
                        if (way_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            kin_3: [
                {
                    required: function (value, attr, computed) {
                        var way_3 = this.get('way_3');
                        if (way_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            way_1: [
                {
                    required: function (value, attr, computed) {
                        var kin_1 = this.get('kin_1');
                        if (kin_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            way_2: [
                {
                    required: function (value, attr, computed) {
                        var kin_2 = this.get('kin_2');
                        if (kin_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            way_3: [
                {
                    required: function (value, attr, computed) {
                        var kin_3 = this.get('kin_3');
                        if (kin_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            zei_1: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_1 = this.get('way_1');
                        if (curZan != 0 && way_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            zei_2: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_2 = this.get('way_2');
                        if (curZan != 0 && way_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            zei_3: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_3 = this.get('way_3');
                        if (curZan != 0 && way_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            gokei: {
                required: false, // チェックは処理内部で行う
                pattern: 'number',
            },
            tadashikaki_cd: {// 但し書きコード     2016/03/16 ADD Kayo
                required: true,
            },
            tadashikaki: {
                required: false,
                maxLength: 40,
            },
            biko: {
                required: false,
                maxLength: 40,
            },
            _all: function (val, attr, computed) {
                var sum = this.sumZan();
                var zan = app.model.getSeikyuZan();
                if (sum > zan) {
                    return '金額が請求残高を超えています';
                }
                return;
            },
        },

        labels: {
            hako_kbn: '発行区分',
            disp: '再発行文字印刷',
            syukin_tanto_cd: '集金担当者',
            syukin_tanto_nm: '集金担当者名',
            jimu_tanto_cd: '事務担当者',
            jimu_tanto_nm: '事務担当者名',
            atena: '宛名',
            kin_1: '金額1',
            kin_2: '金額2',
            kin_3: '金額3',
            gokei: '合計',
            tadashikaki_cd: '但し書きコード', // 2016/03/16 ADD Kayo
            tadashikaki: '但し書き',
            biko: '備考',
            hako_date: '発行日時',
            haki_date: '破棄日時',
        },

    }); // MeisaiModel

    var MeisaiCollection = Backbone.Collection.extend({

        model: MeisaiModel,

        nextSeqNo: function () {
            ++MeisaiCollection.seq_cnt;
            return MeisaiCollection.seq_cnt;
        },

        resetLineNo: function () {
            var i, max, m;
            for (i = 0, max = this.length; i < max; i++) {
                m = this.at(i);
                m.set('line_no', i + 1);
                // console.log( 'seq_no=>' + m.get('seq_no') + ' line_no=>' + m.get('line_no') + ' ' + m.get('msi_biko2') );
            }
        },
    },
            {// classProperties
                seq_cnt: 0
            }
    ); // MeisaiCollection

    var meisaiList = new MeisaiCollection;

    var AppModel = Backbone.Model.extend({

        defaults: function () {
            return {
                seikyu_nm: null,
                seikyu_tel: null,
                seikyu_addr1: null,
                seikyu_addr2: null,
                seikyu_gaku: null,
                nyukin_gaku: null,
                seikyu_zan: null,
                ryoshu_hako_gaku: null,
            };
        },

        // 請求残高を返す
        getSeikyuZan: function () {
            var zan = _.reduce(this.get('_uchiwakeData'), function (accum, rec) {
                var taisho_zan = +rec['taisho_zan'];
                accum += taisho_zan;
                return accum;
            }, 0);
            return zan;
        },

        // ある税CD の請求残高を返す
        getSeikyuZanWithZeiCd: function (zei_cd) {
            var zanRec = _.find(this.get('_uchiwakeData'), function (rec) {
                return rec.zei_cd == zei_cd;
            });
            var zan = zanRec ? zanRec['taisho_gaku'] : 0;
            return zan;
        },

        validation: {
            denpyo_no: {
                required: false,
            },
        },

        labels: {
        }

    }); // AppModel

    var MeisaiView = Backbone.View.extend({

        tagName: "tbody",

        template: _.template($('#item-template').html()),

        events: {
            "click a.destroy": "clear",
            "click a.add": "add",
            "click .syukin_tanto_cd,.syukin_tanto-ref": 'tantoHelper',
            "click .row" : "toggleChk",
        },
        // check トグル
        toggleChk: function () {
            if (this.model.get('ryoshusho_no') !== '(自動)') {	// 0:新規発行　1:変更発行　9:破棄
                if (this.model.get('hako_kbn') !== '9') {
                    var _chk = !this.model.get('_chk');
                    this.model.set('_chk', _chk);
                    if (_chk) {
                        this.$('.row').addClass('row-selected');
                    } else {
                        this.$('.row').removeClass('row-selected');
                    }
                }
            }
            var count = 0;
            this.model.collection.each(function (m, i) {
                if (m.get('_chk')) {
                    if (m.get('ryoshusho_no') !== '(自動)') {	// 0:新規発行　1:変更発行　9:破棄
                        if (m.get('hako_kbn') !== '9') {
                            count = count + 1;
                        }
                    }
                }
            });
            if (count > 0) {	// 0:新規発行　1:変更発行
                $('#btn_save').hide();
                $('#btn_cancel').show();
            } else {
                //this.$('.chkAllToggle').removeClass('selected');
                $('#btn_save').show();
                $('#btn_cancel').hide();
            }
        },

        tantoHelper: function () {
            if (utils.isReadOnlyCtxt())
                return; // 参照専用の場合は何もしない
            var bbm = this.model;
            if (!bbm.get('is_edit'))
                return; // 参照要素
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('syukin_tanto_cd', data.code);
                    bbm.set('syukin_tanto_nm', data.name);
                },
                onClear: function () {
                    bbm.set('syukin_tanto_cd', null);
                    bbm.set('syukin_tanto_nm', null);
                },
                hookSetData: function () {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return {s_bumon: bumon_cd};
                },
            });
        },

        checkHaki: function () {
            var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
            var total_amount = mydata.dataApp.seikyu_gaku;
            var gokei = 0;
            _.each(mydata.dataCol, function (m) {
                if (m.hako_kbn != 9) {// 破棄以外
                    var _mgokei = parseInt(m.gokei);
                    gokei += (_mgokei) ? _mgokei : 0;
                }
            });

            if (gokei >= total_amount) { // 残り金額がない場合破棄に変更
                var atena = this.model.attributes.ryoshusho_no;
                if (this.model.attributes.ryoshusho_no == "(自動)") {
                    this.model.attributes.hako_kbn = 9;
                    this.model.trigger('change');
                }
            }
            var length = mydata.dataCol.length; // mydataのdataColのlength
            var last = mydata.dataCol[length - 1];
        },

        // 新規発行(預)のときの処理
        setAzukariPrc: function () {
            var m = this.model;
            if (m.get('is_edit')) {
                var cnt = 1;
                var that = this;
                if (m.get('hako_kbn') == '2') {
                    var blk_flg = false;
                    var zei_data_ary = app.model.get('_optData').opt_zei_rev;
                    var azukari_prc = app.model.get('azukari_prc');
                    _.each(zei_data_ary, function (zei) {
                        if (blk_flg) {
                            return false; // break
                        }
                        var zei_cd = zei.id;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var way_k = "way_" + cnt;
                        var zei_k = "zei_" + cnt;
                        var kin_k = "kin_" + cnt;
                        // disabled
                        var way_kc = '.' + way_k;
                        var zei_kc = '.' + zei_k;
                        var kin_kc = '.' + kin_k;
                        that.$(way_kc).attr('disabled', 'disabled');
                        that.$(zei_kc).attr('disabled', 'disabled');
                        that.$(kin_kc).attr('disabled', 'disabled');
                        // それぞれのデータセット
                        m.set(way_k, '3'); // 支払方法は「振込」に設定
                        m.set(zei_k, zei_cd); // 税コードを設定
                        if (taisho_gaku >= azukari_prc) {
                            m.set(kin_k, azukari_prc);
                            cnt++;
                            blk_flg = true;
//                            return false; // break
                        } else {
                            m.set(kin_k, taisho_gaku);
                            azukari_prc -= taisho_gaku;
                            cnt++;
                            if (cnt === 4) {
                                blk_flg = true;
//                                return false; // break
                            } else {
                                return true; // continue
                            }
                        }
                    });
                }

                if (cnt <= 3) {
                    while (cnt < 4) {
                        var way_k = "way_" + cnt;
                        var zei_k = "zei_" + cnt;
                        var kin_k = "kin_" + cnt;
                        // remove disabled
                        var way_kc = '.' + way_k;
                        var zei_kc = '.' + zei_k;
                        var kin_kc = '.' + kin_k;
                        that.$(way_kc).removeAttr('disabled');
                        that.$(zei_kc).removeAttr('disabled');
                        that.$(kin_kc).removeAttr('disabled');
                        // modelにnullをセット
                        m.set(way_k, null);
                        m.set(zei_k, null);
                        m.set(kin_k, null);
                        cnt++;
                    }
                }
            }
        },

        // 発行区分を変更
        changeHakoKbn: function () {
            // 発行区分変更
            var m = this.model;
            var hako_kbn = m.get('hako_kbn');
            var disp_kbn = m.get('disp_kbn');
            // console.log( '*** hako_kbn=>' + hako_kbn );
            if (m.get('is_edit')) {
                switch (hako_kbn) {
                    case 0: // 新規発行
                        // Nyein Chan Aung
                        var gokei = 0;
                        var total_amount = app.model.get("seikyu_gaku");
                        var collection = app.collection;
                        _.each(collection.models, function (m) {
                            if (m.attributes.hako_kbn !== "9" && m.attributes.ryoshusho_no != "(自動)") {
                                var _mgokei = parseInt(m.attributes.gokei);
                                gokei += (_mgokei) ? _mgokei : 0;
                            }
                        });
                        if (gokei >= total_amount) {
                            msiLib2.showErr("請求金額をオーバーしているため新規発行出来ません。");
                            never_show = 1;
                            return;
                        }
                        if (hako_kbn_last != hako_kbn) {
                            m.set('atena', app.model.get('seikyu_nm'));
                            m.set('tadashikaki_cd', null);
                            m.set('tadashikaki', null);
                        }
                        break;
                    case 2: // 新規発行(預)
                        // Nyein Chan Aung
                        var gokei = 0;
                        var total_amount = app.model.get("seikyu_gaku");
                        var collection = app.collection;
                        _.each(collection.models, function (m) {
                            if (m.attributes.hako_kbn !== "9" && m.attributes.ryoshusho_no != "(自動)") {
                                var _mgokei = parseInt(m.attributes.gokei);
                                gokei += (_mgokei) ? _mgokei : 0;
                            }
                        });
                        if (gokei >= total_amount) {
                            msiLib2.showErr("請求金額をオーバーしているため新規発行出来ません。");
                            never_show = 1;
                            return;
                        }

                        if (hako_kbn_last != hako_kbn) {
                            m.set('atena', app.model.get('seikyu_nm'));
                            m.set('tadashikaki_cd', null);
                            m.set('tadashikaki', null);
                        }
                        break;
                }
                this.setAzukariPrc();
            } else {
                var hako_kbn_str = hako_kbn == 0 ? '新規発行' :
                        hako_kbn == 1 ? '変更発行' :
                        hako_kbn == 2 ? '新規発行(預)' :
                        hako_kbn == 9 ? '破棄' : '？？？';
                var disp_kbn_str = disp_kbn == 0 ? '印刷あり' :
                        disp_kbn == 1 ? '印刷なし' : '？？？';
                this.$el.find('.t-hako_kbn').html('<span>' + _.escape(hako_kbn_str) + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + _.escape(disp_kbn_str) + '</span>');
            }
            hako_kbn_last = hako_kbn;
        },

        // 但し書きコードを変更
        changeTadashikakiCd: function () {
            //データ取得            
            var m = this.model;
            var tadashikaki_cd = $.msiJqlib.getSelect2Val(this.$(".tadashikaki_cd"));
            if (tadashikaki_cd != 999) {
                var tadashikaki = $.msiJqlib.getSelect2Data(this.$(".tadashikaki_cd"));
                if (typeof tadashikaki != "undefined") {
                    if (!$.msiJqlib.isNullEx2(tadashikaki)) {
                        m.set('tadashikaki', tadashikaki['text']);
                    }
                }
            } else {
                m.set('tadashikaki', null);
            }
        },

        initialize: function () {
            Backbone.Validation.bind(this);

            // this.listenTo(this.model, 'change:genkin change:kogite change:furikomi', this.recalc);
            this.listenTo(this.model, 'change:kin_1 change:kin_2 change:kin_3', this.recalc);

            this.listenTo(this.model, 'change:hako_kbn', this.changeHakoKbn);
            this.listenTo(this.model, 'change:tadashikaki_cd', this.changeTadashikakiCd);

            this.listenTo(this.model, 'destroy', this.remove);
            this.checkHaki();
        },

        recalcAndRender: function () {
            this.recalc();
            this.render();
        },

        recalc: function () {
            var m = this.model,
                    hako_kbn = m.get('hako_kbn'),
                    is_set = false,
                    gokei = 0;

            // _.map( 'genkin kogite furikomi'.split(/\s+/),
            _.map('kin_1 kin_2 kin_3'.split(/\s+/),
                    function (k) {
                        if (!$.msiJqlib.isNullEx2(m.get(k))) {
                            is_set = true;
                            gokei = gokei + parseInt(m.get(k), 10);
                        }
                    });
            if (!is_set) { //  &&  gokei == 0 ) {
                m.set('gokei', null);
            } else {
                m.set('gokei', +gokei);
            }
        },

        render: function () {

            Backbone.Validation.bind(this, Backbone.Validation.msi_err_setting_std_2());

            if (this.template === null) {
                this.template = _.template($('#item-template').html());
            }
            this.$el.html(this.template(this.model.toJSON()));

            // 参照専用要素
            if (!this.model.get('is_edit') || utils.isReadOnlyCtxt()) {
                this.$el.msiInputReadonly();
                this.$el.find('td').addClass('my-readonly-dy');
                this.$el.find('.dlg_tanto').remove();
                this.$el.find('.syukin_tanto_nm').removeClass('my-txt75').addClass('my-txt90');
                // this.$el.find('.genkin,.kogite,.furikomi,.gokei').removeClass('my-bold');
                this.$el.find('.kin_1,.kin_2,.kin_3,.gokei').removeClass('my-bold');

                if (!this.model.get('kin_2')) {
                    this.$el.find('.div-kin2').hide();
                }
                if (!this.model.get('kin_3')) {
                    this.$el.find('.div-kin3').hide();
                }
            }

            // 軽減税率対応 keigen
            // var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));			
            if (app) {
                var optData = app.model.get('_optData');
                if (optData) {
                    var optZei = optData.opt_zei;
                    var optWay = optData.opt_way;
                    if (this.model.get('biko2') != null) {
                        optWay = optData.opt_way2;
                    }

                    var that = this;
                    _.each([1, 2, 3], function (cnt) {
                        var zei_k = "zei_" + cnt;
                        var way_k = "way_" + cnt;
                        var zei_kc = '.' + zei_k;
                        var way_kc = '.' + way_k;
                        $.msiJqlib.setSelect2Com1(that.$(zei_kc),
                                {data: optZei, dropdownAutoWidth: true, allowClear: true, placeholder: ' '});
                        $.msiJqlib.setSelect2Com1(that.$(way_kc),
                                {data: optWay, dropdownAutoWidth: true, allowClear: true, placeholder: ' '});
                        var zei = that.model.get(zei_k);
                        var way = that.model.get(way_k);
                        if (!$.msiJqlib.isNullEx2(zei)) {
                            that.$(zei_kc).select2('val', zei);
                        }
                        if (!$.msiJqlib.isNullEx2(way)) {
                            that.$(way_kc).select2('val', way);
                        }
                    });
                    $.msiJqlib.setSelect2Com1(this.$('.hako_kbn'), {data: $.msiJqlib.objToArray3(optData.hakko_kbn_ary), dropdownAutoWidth: true, allowClear: false, placeholder: ''});
                }
            }

            this.stickit();
            $('.msi-picker', this.$el).each(msiLib2.msiPickerBinder);

            if (utils.isReadOnlyCtxt()) {
                this.$el.find(':input').attr('disabled', 'disabled')
                        .end().find('.my-readonly-hidden').hide();
            }
            this.setAzukariPrc();

            return this;
        },

        // Remove the item, destroy the model.
        clear: function () {
            minDataApp = app.model.toJSON(); // not JSON, but Object
            minDataCol = app.collection.toJSON(); // not JSON, but Object
            this.model.destroy();
            min2DataApp = app.model.toJSON(); // not JSON, but Object
            min2DataCol = app.collection.toJSON(); // not JSON, but Object
        },

        // Create a item. ugly...
        add: function (e) {
            // shift を押した場合は上に追加
            var isUpside = e.shiftKey ? true : false;
            // Ctrl を押した場合はコピー
            var isCopy = (e.ctrlKey || e.metaKey) ? true : false;

            var off = this.model.get('line_no');
            var newModel, orgModel;
            if (isCopy) {
                orgModel = meisaiList.get(this.model);
                newModel = orgModel.clone(); // shallow copy
                newModel.set('msi_no', null);
            } else {
                newModel = new MeisaiModel;
            }

            // console.log( 'add line_no=>' + off + ' shift=>' + isUpside + ' isCopy=>' + isCopy );
            // newModel.set( 'msi_biko2',  ""+jQuery.format.date(new Date(), 'MM/dd hh:mm:ss') ); // XXX

            if (isUpside)
                off = off - 1;
            meisaiList.add(newModel, {at: off, silent: true}); // add event を挙げない
            meisaiList.resetLineNo(); // line_no を再設定
            meisaiList.trigger('add', newModel, meisaiList, {at: off}); // , options) // 改めて add event を挙げる
        },

        bindings: {
            '.row': 'line_no',
            '.line_no': 'line_no',
            '.ryoshusho_no': {
                observe: 'ryoshusho_no',
                events: ['change'],
            },
            '.hako_kbn': {
                observe: 'hako_kbn',
//                events: ['change'],
                getVal: utils.getValSel2,
                update: utils.updateSel2,
            },
            '.disp_kbn': {
                observe: 'disp_kbn',
                events: ['change'],
                getVal: utils.getValSel2,
                update: utils.updateSel2,
            },
            '.syukin_tanto_cd': {
                observe: 'syukin_tanto_cd',
                events: ['change'],
            },
            '.syukin_tanto_nm': {
                observe: 'syukin_tanto_nm',
                events: ['change'],
            },
            '.jimu_tanto_cd': {
                observe: 'jimu_tanto_cd',
                events: ['change'],
            },
            '.jimu_tanto_nm': {
                observe: 'jimu_tanto_nm',
                events: ['change'],
            },
            '.atena': {
                observe: 'atena',
                events: ['change'],
            },
            '.gokei': {
                observe: 'gokei',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_1': {
                observe: 'kin_1',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_2': {
                observe: 'kin_2',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_3': {
                observe: 'kin_3',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.way_1': {
                observe: 'way_1',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.way_2': {
                observe: 'way_2',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.way_3': {
                observe: 'way_3',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_1': {
                observe: 'zei_1',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_2': {
                observe: 'zei_2',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_3': {
                observe: 'zei_3',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.tadashikaki_cd': {
                observe: 'tadashikaki_cd',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.tadashikaki': {
                observe: 'tadashikaki',
                events: ['change'],
            },
            '.biko': {
                observe: 'biko',
                events: ['change'],
            },
            '.hako_date': {
                observe: 'hako_date',
                events: ['change'],
            },
            '.haki_date': {
                observe: 'haki_date',
                events: ['change'],
            },
            '.haki_user': {
                observe: 'haki_user',
                events: ['change'],
                update: function ($el, val, options) {
                    var haki_date = $el.closest('td').find('input.haki_date');
                    if (haki_date.val()) {
                        haki_date.attr('title', '破棄者: ' + val);
                    }
                    $el.val(val);
                },
            },

        },
    }); // MeisaiView

    var AppView = Backbone.View.extend({

        el: document, // '#my-form-id', // for #hall_cd

        events: {
            "click #add-meisai": "createMeisai",
            "click #btn_save": "doSave",
            "click #btn_cancel": "doCancel",
            "click #kin-imp-btn": "setKingaku",
            "click #btn_delete": "doDelete",
            "click #btn_new": "doNew",
            "click #btn_check": "doCheck",
            "click #btn_clear_err": "doClearErr",
        },

        denpyoHelper: function () {
            // this は Backbone.View
            // 参照専用モードでも伝票検索は抑制しない 
            // if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model,
                    bbv = this;
            this.$el.msiPickHelper({
                action: 'zaiko.ido.denpyo',
                onSelect: function (data) {
                    if (bbv.isChanged()) {
                        if (!confirm(msg01)) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', data.code);
                    var denpyo_no = data.code;
                    $.ajax({// url: location.href,
                        data: {
                            denpyo_no: denpyo_no,
                            action: '表示',
                        },
                        type: 'POST',
                        success: function (mydata) {
                            if (mydata.status == 'OK') {
                                // msiLib2.showInfo( mydata.msg );
                                _resetData(mydata.dataApp, mydata.dataCol);
                                // console.log( '* * * bumon_cd=>' + mydata.dataApp.bumon_cd );
                                // msiLib2.setHallCd( mydata.dataApp.bumon_cd ); // XXX auto?

                                // console.log( '* * * mydata.dlKey=>' + JSON.stringify(mydata.dlKey) );

                                if (mydata.dlKey) { // 領収書PDFファイルダウンロード
                                    var data = $.extend({}, mydata.dlKey, {action: 'PDF', preview: 'off', out_type: 1});
                                    msiLib2.fileDlAjax({
                                        url: $.msiJqlib.baseUrl() + '/saiken/ryoshu/order',
                                        data: data
                                    });
                                }
                            } else {
                                msiLib2.showErr(mydata.msg);
                                // if ( orgDataApp && orgDataCol ) {
                                //    _resetData( orgDataApp, orgDataCol );
                                // }
                            }
                        }
                    });
                },
                onClear: function () {
                    if (bbv.isChanged()) {
                        if (!confirm(msg01)) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', null);
                    $.ajax({// url: location.href,
                        data: {
                            action: '初期化',
                        },
                        type: 'POST',
                        success: function (mydata) {
                            if (mydata.status == 'OK') {
                                _resetData(mydata.dataApp, mydata.dataCol);
                            } else {
                                msiLib2.showErr(mydata.msg);
                            }
                        }
                    });
                },
                hookSetData: function () {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return {init_search: 0,
                        no_cond: 0,
                        // s_seko_no: '0000000000', // 単品
                        // 's_ido_ymd_from': $.msiJqlib.getStdDate(),
                        // 's_ido_ymd_to': $.msiJqlib.getStdDate(),
                        // ,s_bumon: bumon_cd
                    };
                }
            });
        },

        initialize: function () {

            Backbone.Validation.bind(this);

            this.listenTo(this.model, 'change', this.recalcAndRender); // for zei calc and cancel button

            this.listenTo(this.collection, 'add', this.addOne);
            this.listenTo(this.collection, 'change remove add', this.recalc);
            this.listenTo(this.collection, 'change remove add', this.render);
            this.listenTo(this.collection, 'reset', this.resetCol);

            // this.dtlFooter = this.$('#dtl-table-id tfoot');
            // this.dtlFooter.hide();
            // this.disableSave();
            this.recalc();
            this.render();
            // this.checkHaki();
        },

        checkHaki: function () {
            var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
            var total_amount = mydata.dataApp.seikyu_gaku;
            var gokei = 0;
            _.each(mydata.dataCol, function (m) {
                if (m.hako_kbn != 9) {// 破棄以外
                    var _mgokei = parseInt(m.gokei);
                    gokei += (_mgokei) ? _mgokei : 0;
                }
            });
            var length = mydata.dataCol.length; // mydataのdataColのlength
            var last = mydata.dataCol[length - 1];
            if (gokei >= total_amount) { // 残り金額がない場合破棄に変更
                last.hako_kbn = 9;
                mydata.dataCol[length - 1].hako_kbn = 9;
                var v = new MeisaiView({model: this.model});
                v.changeHakoKbn();
                //this.model.trigger('change'); // patch
            }
        },

        disableSave: function () {
            var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
            var total_amount = mydata.dataApp.seikyu_gaku;
            var gokei = 0;
            _.each(mydata.dataCol, function (m) {
                var _mgokei = parseInt(m.gokei);
                gokei += (_mgokei) ? _mgokei : 0;
            });
            if (gokei == total_amount) {
                $("#btn_save").hide();
            }
        },

        resetCol: function (collection, options) {
            var $table = this.$("#dtl-table-id");
            $table.find('tbody').remove();
            var gokei = 0;
            var total_amount = app.model.get("seikyu_gaku");
            _.each(collection.models, function (m) {
                // Nyein Chan Aung
                // if(gokei < total_amount)
                // if (m.attributes.hako_kbn !== "9") {
                // 	var _mgokei = parseInt(m.attributes.gokei);
                // 	gokei += (_mgokei) ? _mgokei : 0;
                // }
                // if (gokei > total_amount){
                // 	m.attributes.set("9");
                // }
                if (m.attributes.hako_kbn !== "9") {
                    var _mgokei = parseInt(m.attributes.gokei);
                    gokei += (_mgokei) ? _mgokei : 0;
                }
                var v = new MeisaiView({model: m});
                $table.append(v.render().el);
            });

            this.recalc();
            this.render();
            this.showInitialAmount(total_amount, gokei);
        },

        createMeisai: function (e) {
            this.collection.add(new MeisaiModel);
        },

        addOne: function (meisai, list, options) {
            var v = new MeisaiView({model: meisai});

            var line_no = meisai.get('line_no'), off;
            if (_.has(options, 'at')) {
                off = options['at'];
            }

            // console.log( 'line_no=>' + line_no + ' of=>' + off + ' addOne *** => ' + JSON.stringify(meisai) );

            if (off === undefined) {
                this.$("#dtl-table-id").append(v.render().el);
            } else if (off > 0) {
                this.$("#dtl-table-id").find('tbody').eq(off - 1).after(v.render().el);
            } else { // off === 0
                this.$("#dtl-table-id").prepend(v.render().el);
            }
        },

        recalcAndRender: function () {
            this.recalc();
            this.render();
        },

        recalc: function () {
            this.collection.each(function (m, i) {
                m.set('line_no', i + 1);
            });
        }, // recalc
        showInitialAmount: function (total_amount, current_amount) {
            return; // XXX
            var length_gk = $(".t-genkin").length;
            var last_gk = $(".t-genkin")[length_gk - 1];
            var before_last_gk = $(".t-genkin")[length_gk - 2];
            if ($(before_last_gk.children).val()) {
                // last.genkin = parseInt($(before_last_gk.children).val().replace(",",""));
                $(last_gk.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                $(last_gk.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
                // auto_change = 0;
            } else {
                var length_kgt = $(".t-kogite").length;
                var last_kgt = $(".t-kogite")[length_kgt - 1];
                var before_last_kgt = $(".t-kogite")[length_kgt - 2];
                if ($(before_last_kgt.children).val()) {
                    // last.kogite = parseInt($(before_last_kgt.children).val().replace(",",""));
                    $(last_kgt.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                    $(last_kgt.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
                    // auto_change = 0;
                } else {
                    var length_fkm = $(".t-genkin").length;
                    var last_fkm = $(".t-genkin")[length_fkm - 1];
                    var before_last_fkm = $(".t-genkin")[length_fkm - 2];
                    // last.furikomi = parseInt($(before_last_fkm.children).val().replace(",",""));
                    $(last_fkm.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                    $(last_fkm.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
                    // auto_change = 0;
                }
            }

        },
        render: function () {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind(this, Backbone.Validation.msi_err_setting_std());

            if (this.collection.length) {
                // this.dtlFooter.show();
            } else {
                this.collection.add(new MeisaiModel); // １行は表示する
                // this.dtlFooter.hide();
            }

            this.renderKeigen(); //軽減税率対応 keigen

            this.stickit();

            // utils.decoMinus( this, 'total_kingaku total_genka total_zei total_zeikomi' );

            // スクロール調整
            this.scrollAdj();

            // select2 調整。XXX 
            //this.select2Adj(); // patch

            // 参照専用モード
            if (utils.isReadOnlyCtxt()) {
                if (this.isSelectedCtxt()) {
                    this.btnEnabled('#btn_print');
                } else {
                    this.btnDisabled('#btn_print');
                }
                return this;
            }

            // ボタン
            if (app && app.collection) {
                var lastModel = app.collection.at(app.collection.length - 1),
                        hako_kbn = lastModel.get('hako_kbn');
                if (hako_kbn == 9) { // 破棄
                    $('#btn_save').hide();
                    $('#btn_cancel').show();
                } else {
                    $('#btn_save').show();
                    $('#btn_cancel').hide();
                }
                if (never_show === 1) {
                    $('#btn_save').hide();
                }
            }


            return this;
        },

        // 軽減税率関連 render  keigen
        renderKeigen: function () {
            if (app) {
                var uchiwakeData = app.model.get('_uchiwakeData');
                // console.log( '** uchiwakeData =>', uchiwakeData );
                var html = [];
                var aHdr = [];
                var aVal = [];
                var text;
                var htmlStr;
                var width = 0;
                html.push('<table id="seikyu-tbl-2" class="seikyu-tbl-2-cls">');
                _.each(uchiwakeData, function (rec) {
                    text = rec['taisho_cap'];
                    htmlStr = '<th class="seikyu-tbl-2-cap-cls">' + _.escape(text) + '</th>';
                    aHdr.push(htmlStr);

                    htmlStr = '<th class="seikyu-tbl-2-cap-cls">' + _.escape(text + '残高') + '</th>';
                    aHdr.push(htmlStr);

                    text = $.msiJqlib.commaAdd(rec['taisho_gaku']);
                    htmlStr = '<td class="seikyu-tbl-2-val-cls text-right">' + _.escape(text) + '</td>';
                    aVal.push(htmlStr);

                    text = $.msiJqlib.commaAdd(rec['taisho_zan']);
                    htmlStr = '<td class="seikyu-tbl-2-val-cls text-right">' + _.escape(text) + '</td>';
                    aVal.push(htmlStr);
                });
                if (aHdr.length) {
                    html.push('<tr>' + aHdr.join('') + '</tr>');
                    html.push('<tr>' + aVal.join('') + '</tr>');
                    width = 15 * aHdr.length;
                }
                html.push('</table>');
                var myHtml = html.join('');
                // console.log( '** myHtml =>', myHtml );
                var $myRoot = this.$('#seikyu-info-2');
                $myRoot.empty();
                $myRoot.css('width', width + '%');
                $myRoot.append(myHtml);
            }

        },

        // スクロールバー表示調整
        scrollAdj: function () {
            var $list = this.$('.items .list'),
                    $header = this.$('.items .header'),
                    sc_of,
                    sc_w,
                    hh;
            // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
            if ($list[0].scrollHeight === $list[0].clientHeight) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                sc_w = '44.4%';
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            } else {
                sc_of = 'scroll';
                sc_w = '43.3%';
                hh = $header.height();
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome. XXX
                var h0 = this.$('.items').height();
                $list.height(h0 - hh - 5);
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            }
        },

        btnDisabled: function (elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },

        btnEnabled: function (elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },

        isInputOk: function () {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                    // console.log( '*** err ' + k + ': ' + v );
                });
            }
            var maxlen = app.collection.length - 1;
            this.collection.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    line = i + 1;
                    // console.log( 'i=>' + i + ' maxlen=>' + maxlen );
                    if (maxlen > i)
                        return; // 最終行以外は読み飛ばす
                    _.each(resLine, function (v, k) {
                        // aMsg.push( '明細' + line + '行目 ' + v );
                        aMsg.push(v);
                        // console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                    });
                }
            });

            var lastModel = app.collection.at(app.collection.length - 1),
                    gokei = lastModel.get('gokei'),
                    atena = lastModel.get('atena');
            if ($.msiJqlib.isNullEx2(gokei)) {
                aMsg.push('金額が入力されていません');
            } else if (gokei == 0) {
                aMsg.push('金額がゼロになっています');
            } else if (gokei < 0) {
                aMsg.push('金額がマイナス値になっています');
            }
            if ($.msiJqlib.isNullEx2(atena) || lastModel.get('atena').length <= 0) {
                aMsg.push('宛名が入力されていません');
            }

            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },

        doCheck: function (ev) {
            if (this.isInputOk()) {
                if (ev)
                    ev.stopPropagation();
                msiLib2.showInfo('入力チェック OK');
            }
        },

        doClearErr: function () {
            this.clearErr();
        },

        clearErr: function () {
            this.$el.msiErrClearAll();
        },

        // 残高を金額欄に設定します
        setKingaku: function () {
            var zan = app.model.getSeikyuZan(),
                    curOff = app.collection.length - 1,
                    curModel = app.collection.at(curOff),
                    sum = curModel.sumZan(),
                    isDone = false;
            zan = zan - sum;
            if (zan > 0) {
                _.each([1, 2, 3], function (cnt) {
                    if (isDone)
                        return;
                    var fldName = 'kin_' + cnt;
                    var kin = +curModel.get(fldName);
                    if (kin == 0) {
                        curModel.set(fldName, zan);
                        isDone = true;
                    }
                });
            }
        },

        // 残高を項目に取り込みます
        _setZan: function (fldName) {
            var zan = app.model.get('seikyu_gaku') - app.model.get('ryoshu_hako_gaku'),
                    curOff = app.collection.length - 1,
                    curModel = app.collection.at(curOff),
                    prevModel;
            if (curModel.get('hako_kbn') == 1 && curOff > 0) { // 変更発行で前行あり
                prevModel = app.collection.at(curOff - 1);
                if (prevModel.get('hako_kbn') != 9) { // 破棄以外の場合
                    zan = zan + Number(prevModel.get('gokei'));
                }
            }
            if (zan > 0) {
                curModel = app.collection.at(app.collection.length - 1);
                curModel.set(fldName, zan);
            }
        },
        setGenkin: function () {
            this._setZan('genkin');
        },
        setKogite: function () {
            this._setZan('kogite');
        },
        setFurikomi: function () {
            this._setZan('furikomi');
        },

        doSave: function (mode) {
            var lastModel = app.collection.at(app.collection.length - 1),
                    hako_kbn = lastModel.get('hako_kbn');
            if (hako_kbn != 9) {
                if (!this.isInputOk()) {
                    return;
                }
            }
            // 新規発行(預)の場合
            if (hako_kbn == 2) {
                mode = 'azukari'; // modeを設定
                lastModel.set('biko2', '※');
            } else {
                mode = null;
                lastModel.set('biko2', null);
            }

            if (hako_kbn == 9) { // 破棄
                if (!confirm("直前の領収書を破棄します.\nなお、金額は考慮されません.\n直前の領収書を破棄しますか？")) {
                    return;
                }
                never_show = 0;
            } else {
                if (!confirm("領収書を発行します.\n領収書は一度発行すると取消できません.\n領収書を発行しますか？")) {
                    return;
                }
            }

            this.clearErr();

            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());

            var $this = this;

            $.ajax({// url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '保存',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        // console.log( 'dump dataApp *** ==>' + JSON.stringify(mydata.dataApp) );
                        // console.log( 'dump dataCol *** ==>' + JSON.stringify(mydata.dataCol) );
//                        _resetData(mydata.dataApp, mydata.dataCol);

                        if (mydata.dlKey) { // 領収書PDFファイルダウンロード
                            var data = $.extend({}, mydata.dlKey, {action: 'PDF', preview: 'off', out_type: 1, mode: mode, });
                            msiLib2.fileDlAjax({
                                url: location.href, data: data});
                        }
                        // var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
                        _resetData(mydata.dataApp, mydata.dataCol);
                        var total_amount = mydata.dataApp.seikyu_gaku;
                        var gokei = 0;
                        _.each(mydata.dataCol, function (m) {
                            if (m.hako_kbn != 9) {// 破棄以外
                                var _mgokei = parseInt(m.gokei);
                                gokei += (_mgokei) ? _mgokei : 0;
                            }
                        });
                        var curModel = app.collection.at(app.collection.length - 1);
                        if (gokei >= total_amount) { // 残り金額がない場合破棄に変更
                            $('#btn_save').hide();
                            $('#btn_cancel').show();
                            curModel.set('hako_kbn', 9);
                        } else {
                            $('#btn_save').show();
                            $('#btn_cancel').hide();                            
                            curModel.set('hako_kbn', mydata.dataCol[mydata.dataCol.length - 1].hako_kbn);
                        }
                        msiLib2.showInfo2(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                        $("#btn_save").hide();
                        app.model.trigger('change');
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                }
            });
        },

        doDelete: function (ev) {
            // console.log( 'doDelete called' );
            if (false && !this.isInputOk()) {
                return;
            }

            this.clearErr();

            if (!confirm('削除してよろしいですか？')) {
                return;
            }

            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());

            var $this = this;

            $.ajax({// url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '削除',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        msiLib2.showInfo(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                }
            });

        },

        _doNew: function (msg) {
            var _msg = msg;
            $.ajax({
                data: {
                    action: '新規',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        // msiLib2.showInfo( mydata.msg );
                        if (_msg) {
                            msiLib2.showInfo(_msg);
                        }
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },

        doNew: function (ev) {
            if (!confirm('新規作成画面へ移行します. よろしいですか？')) {
                return;
            }
            this._doNew();
        },
        // 破棄
        doCancel: function () {
            var count = 0;
            this.collection.each(function (m, i) {
                if (m.get('_chk')) {
                    count += 1;
                }
            });
            var action = '破棄';
            if (count <= 0) {
                msiLib2.showWarn("破棄する行を選択してください。");
                return;
            }
            if (!confirm("選択された領収書を破棄します。\n選択された領収書を破棄しますか？")) {
                return;
            }

            this.clearErr();

            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());

            $.ajax({// url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: action
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        var total_amount = mydata.dataApp.seikyu_gaku;
                        var gokei = 0;
                        _.each(mydata.dataCol, function (m) {
                            if (m.hako_kbn != 9) {// 破棄以外
                                var _mgokei = parseInt(m.gokei);
                                gokei += (_mgokei) ? _mgokei : 0;
                            }
                        });
                        var length = mydata.dataCol.length; // mydataのdataColのlength
                        var last = mydata.dataCol[length - 1];
                        if (gokei >= total_amount) { // 残り金額がない場合でも新規発行に変更
                            $("#btn_save").hide();
                            last.hako_kbn = 0;
                        } else {
                            $("#btn_cancel").hide();
                            last.hako_kbn = 0;
                        }
                        _resetData(mydata.dataApp, mydata.dataCol);
                        console.log(gokei + " = " + total_amount);
                        msiLib2.showInfo2(mydata.msg);
                        window.opener.location.reload()
                    } else {
                        msiLib2.showErr(mydata.msg);
                        $("#btn_save").hide();
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                }
            });
        },

        XXXdoCancel: function (ev) {
            // console.log( 'doCancel called' );

            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }

            if (ev)
                ev.stopPropagation();

            if (orgDataApp && orgDataCol) {
                _resetData(orgDataApp, orgDataCol);
            }

            msiLib2.showInfo('初期状態に戻しました');
        },

        // 選択モード(更新モード)か否か
        isSelectedCtxt: function () { // aka. isUpdCtxt
            var denpyo_no = this.model.get('denpyo_no');
            if (denpyo_no) {
                return true;
            }
            return false;
        },

        // 初期状態から変更されているか
        isChanged: function () {
            if (!orgDataApp || !orgDataCol ||
                    ($.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) &&
                            $.msiJqlib.isEqual(orgDataCol, this.collection.toJSON()))) {
                return false;
            }
            return true;
        },

        bindings: {
            '#seikyu_nm': 'seikyu_nm',
            '#seikyu_tel': 'seikyu_tel',
            '#seikyu_addr1': 'seikyu_addr1',
            '#seikyu_addr2': 'seikyu_addr2',
            '#seikyu_gaku': {
                observe: 'seikyu_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#nyukin_gaku': {
                observe: 'nyukin_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#seikyu_zan': {
                observe: 'seikyu_zan',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#ryoshu_hako_gaku': {
                observe: 'ryoshu_hako_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
        },
    }); // AppView

    var app;
    var orgDataApp, orgDataCol;
    var _resetData;
    var minDataApp, minDataCol, min2DataApp, min2DataCol;
    var _setInitData;
    var disableSave;

    app = new AppView({model: new AppModel, collection: meisaiList});

    disableSave = function () {
        var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
        var total_amount = mydata.dataApp.seikyu_gaku;
        var gokei = 0;
        _.each(mydata.dataCol, function (m) {
            var _mgokei = parseInt(m.gokei);
            gokei += (_mgokei) ? _mgokei : 0;
        });
        if (gokei == total_amount) {
            $("#btn_save").prop("disabled", true);
        }
    };

    _resetData = function (myApp, myCol) {
        // console.log( '_resetData myApp=>', myApp );
        app.model.set(myApp);
        app.collection.reset(myCol);
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        orgDataCol = app.collection.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();
        app.model.trigger('change'); // patch 
    };

    _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));

        if (_.has(mydata.dataApp, '_reload_denpyo_no')) { // cf. denpyoHelper()
            // 更新や参照で denpyo_no が指定されている場合
            $.ajax({
                data: {
                    denpyo_no: mydata.dataApp['_reload_denpyo_no'],
                    action: '表示',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        // msiLib2.setHallCd( mydata.dataApp.bumon_cd ); // XXX auto?
                        //app.select2Adj(); // patch
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
            return;
        } else {
            // 参照専用時は pickup 画面を表示する
            // if ( utils.isReadOnlyCtxt() ) {
            //     setTimeout( function() { app.denpyoHelper(); }, 1500 );
            // }

            _resetData(mydata.dataApp, mydata.dataCol);

            // IE NG?  setTimeout( function() { app.model.trigger('change'); }, 1500 ); // scrollbar patch
        }

        setTimeout(function () {
            app.model.trigger('change');
        }, 0); // scrollbar patch
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (app.isChanged()) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    // $(window).on( 'resize', function() { app.render(); } );

    // 参照専用
    if ($('#my-form-id').hasClass('my-ctxt-readonly')) {
        var $form = $('#my-form-id');
        $form.msiInputReadonly()
                .msiCalReadonly()
                .find('.my-readonly-hidden').hide();

        $form.find('.my-readonly-visible').show();

        $('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_cancel')
                .hide();
        // .attr("disabled", "disabled");

        utils.setReadOnlyCtxt();
    }

    // 付加的な disabled 付与
    $('#order').find('.label[data-ref-rel]').each(function () {
        // console.log( '.label[data-ref-rel]->' + $(this).attr('class') );
        var $this = $(this),
                ref = $this.attr('data-ref-rel'),
                $el = $(ref, $this.parent());
        // console.log( '.label[data-ref-rel]->' + $(this).attr('class') + ' ref=>' + $el.attr('class') + ' disabled=>' + $el.attr('disabled'));
        if ($el.attr('disabled')) { // || $el.attr('readonly') ) {
            $(this).addClass("my-disabled").attr("disabled", "disabled");
        } else {
            $(this).removeClass("my-disabled").removeAttr("disabled");
            ;
        }
    });

    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    $('#order').fadeIn('fast'); // ちらつきのごまかし

});
