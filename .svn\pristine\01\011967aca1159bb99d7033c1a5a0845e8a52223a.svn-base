<?php

/**
 * Cale_SheduleController
 *
 * スケジュール管理 コントローラクラス
 *
 * @category   App
 * @package    controllers\Cale
 * <AUTHOR> Sai
 * @since      2017/xx/xx cf. Cale_SekocalendarController
 * @version    2020/xx/xx 博全社カスタマイズ
 * @filesource 
 */

/**
 * 施行カレンダー コントローラクラス
 *
 * @category   App
 * @package    controllers\Cale
 * <AUTHOR> Sai
 * @since      2017/xx/xx
 */
class Cale_ScheduleController extends Msi_Zend_Controller_Action {

    protected $s_display_term = 1; // 表示日数

    /**
     * index アクション
     *
     * <AUTHOR> Sai
     * @since  2015/xx/xx
     */

    public function indexAction() {
        $this->_forward('main');
    }

    /**
     * index アクション
     *
     * <AUTHOR> Sai
     * @since  2017/xx/xx
     */
    public function tantoyoteiAction() {
        App_Smarty::pushCssFile(['app/cale.schedule.css'
            , 'app/cale.tantoschedule.css'
            , 'app/cale.dialog.css'
            , 'vendor/ctmenu/jquery.contextMenu.css']);
        App_Smarty::pushJsFile(['mylib/msiLibKyureki.js'
            , 'app/cale/cale.schedule.dialog.js'
            , 'app/cale/cale.tantoschedule.js'
            , 'vendor/ctmenu/jquery.ui.position.js'
            , 'vendor/ctmenu/jquery.contextMenu.js']);
        $this->setInit();

        Msi_Sys_Utils::setScriptPath('tantoyotei', 'schedule', 'cale');
    }

    /**
     * main アクション
     *
     * <AUTHOR> Sai
     * @since  2017/xx/xx
     */
    public function mainAction() {

        App_Smarty::pushCssFile(['app/cale.schedule.css', 'app/cale.dialog.css']);
        App_Smarty::pushJsFile(['mylib/msiLibKyureki.js', 'app/cale/cale.schedule.dialog.js', 'app/cale/cale.schedule.js']);
        $this->setInit();
    }

    /**
     * 初期処理
     *
     * <AUTHOR> Sai
     * @since  2017/xx/xx
     */
    private function setInit() {

        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        // no_cond_exp が設定されている場合は条件を展開しない
        if (isset($params['no_cond_exp']) && $params['no_cond_exp']) {
            $this->_no_cond_exp = true;
        } else {
            $this->_no_cond_exp = false;
        }

        // 最終検索条件の展開
        if (!$this->_no_cond_exp) {
            $this->_params = $this->_restoreLastCond($params);
        }
        $init_day = Msi_Sys_Utils::getDate();
        $init_mon = Msi_Sys_Utils::getDate(null, 'Y/m/01');
        $tanto_bumon_cd = App_Utils::getTantoBumonCd();
        $areaBumon = App_Utils::parBumonOfHall($tanto_bumon_cd);
        $oya_bumon_cd = null;
        if ($areaBumon) {
            $oya_bumon_cd = $areaBumon['bumon_cd'];
        }
        // 初期値
        $data = array(
            'dataApp' => array(
                's_bumon' => $oya_bumon_cd,
                's_ko_bumon' => null, // 式場
                's_display_term' => $this->s_display_term, //  表示日数
                's_tgt_day' => $init_day,
                's_tgt_mon' => $init_mon,
                's_tanto_cd' => App_Utils::getTantoCd()
            ),
        );

        if (isset($this->_params['s_bumon']) && strlen($this->_params['s_bumon']) > 0) {
            $data['dataApp']['s_bumon'] = $this->_params['s_bumon'];
        }
        if (isset($this->_params['s_ko_bumon']) && strlen($this->_params['s_ko_bumon']) > 0) {
            $data['dataApp']['s_ko_bumon'] = $this->_params['s_ko_bumon'];
        }
        if (isset($this->_params['s_current_bumon_cd']) && strlen($this->_params['s_current_bumon_cd']) > 0) {
            $data['dataApp']['s_current_bumon_cd'] = $this->_params['s_current_bumon_cd'];
        }
        if (isset($this->_params['s_tgt_day']) && strlen($this->_params['s_tgt_day']) > 0) {
            $data['dataApp']['s_tgt_day'] = $this->_params['s_tgt_day'];
        }
        if (isset($this->_params['s_tgt_mon']) && strlen($this->_params['s_tgt_mon']) > 0) {
            $data['dataApp']['s_tgt_mon'] = $this->_params['s_tgt_mon'];
        }
        if (isset($this->_params['s_display_term']) && strlen($this->_params['s_display_term']) > 0) {
            $data['dataApp']['s_display_term'] = $this->_params['s_display_term'];
        }
        if (isset($this->_params['s_tanto_cd']) && strlen($this->_params['s_tanto_cd']) > 0) {
            $data['dataApp']['s_tanto_cd'] = $this->_params['s_tanto_cd'];
        }
        $data['bumon_select2'] = Logic_Cale_ComLogic::get_bumon();
        $data['sikijyo_select2'] = Logic_Cale_ComLogic::get_bumon3();
        $data['tanto_select2'] = $this->getAllTantoMstData();
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
    }

    /**
     * 担当者マスタデータを取得する
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     * @return array
     */
    private function getAllTantoMstData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                tm.*
                ,tm.tanto_cd AS id
                ,tm.tanto_nm AS text
            FROM
                tanto_mst tm
            WHERE 
                tm.delete_flg = 0
            ORDER BY tm.tanto_cd
                ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     * setsession アクション
     *
     * <AUTHOR> Sai
     * @since  2015/xx/xx
     */
    public function setsessionAction() {
        $params = Msi_Sys_Utils::webInputs();
        $this->_saveLastCond($params);
        Msi_Sys_Utils::outJson($params);
    }

    /**
     * 検索条件保管のセッションキー名を返す
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     * @return  string
     */
    protected function _getSessCondKey() {
        $keySess = 'sekoScheduleCond';
        return $keySess;
    }

    /**
     * 最終検索条件の展開
     *
     * <AUTHOR> Sai
     * @since 2015/xx/xx
     * @param   array  $params     条件パラメタ
     * @return  void
     */
    private function _restoreLastCond(&$params) {
        $keySess = $this->_getSessCondKey();
        $data = Msi_Sys_Utils::getSessionVar($keySess);
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                if (!isset($params[$key])) {
                    $params[$key] = $val;
                }
            }
        }
        return $params;
    }

    /**
     * 検索条件保存
     *
     * <AUTHOR> Sai
     * @since 2015/xx/xx
     * @param   array  $params     条件パラメタ
     * @param   string $moushi_kbn_ctxt
     * @return  void
     */
    private function _saveLastCond($params) {
        $data = array();
        foreach ($params as $key => $val) {
            if (Msi_Sys_Utils::beginsWith($key, 's_')) { // s_ で始まる条件が対象
                $data[$key] = $val;
            }
        }

        $keySess = $this->_getSessCondKey();
        Msi_Sys_Utils::setSessionVar($keySess, $data);
    }

    /**
     * 検索結果表示 アクション
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     */
    public function listAction() {
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        $this->setParams($params);
        $data = $this->getData();
        $data['dataVac'] = $this->getVacData(); // 担当者休暇データを取得する
        $data['dataApp'] = $this->_params;
        $data['status'] = 'OK';
        // 検索条件の保存
//        $params['s_tgt_day'] = $this->_params['s_tgt_day'];
//        $params['s_bumon'] = $this->_params['s_bumon'];
        $this->_saveLastCond($this->_params);
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * データ取得処理
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     */
    public function getData() {
        return array();
    }

    /**
     * 表示期間取得処理
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     */
    public function getDisplayTerm() {
        return 1;
    }

    /**
     * データ取得
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     * @param array $params
     * @return array
     */
    protected function setParams($params) {
        // タブID 1:施行予定表、 2:担当者予定表 3:車輌予定表
        $this->s_display_term = $this->getDisplayTerm();
        $tgt_date = Msi_Sys_Utils::checkVarOrDefault($params['s_tgt_day'], 'DATE2', Msi_Sys_Utils::getDate());
        $s_display_term = Msi_Sys_Utils::checkVarOrDefault($this->s_display_term, 'DIGIT', 1);
        $bumon = Msi_Sys_Utils::easyGetVar($params, 's_bumon');
        $s_current_bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_current_bumon_cd');

        $off_from = '0 day';
        $epoch = Msi_Sys_Utils::getEpoch($tgt_date);
        $epoch_from = strtotime($off_from, $epoch);
        $epoch_to = strtotime($s_display_term - 1 . " day", $epoch);
        $date_from = Msi_Sys_Utils::getDate($epoch_from);
        $date_to = Msi_Sys_Utils::getDate($epoch_to);

        $this->_params['date_from'] = $date_from;
        $this->_params['date_to'] = $date_to;
        $this->_params['epoch_from'] = $epoch_from;
        $this->_params['epoch_to'] = $epoch_to;
        $this->_params['date_today'] = Msi_Sys_Utils::getDate();
        $this->_params['date_refresh'] = Msi_Sys_Utils::getDatetimeStd();
        if ($s_display_term == '1') {
            $this->_params['date_term'] = Msi_Sys_Utils::getDate($epoch_from, 'n/j');
        } else {
            $this->_params['date_term'] = Msi_Sys_Utils::getDate($epoch_from, 'n/j') . ' - ' . Msi_Sys_Utils::getDate($epoch_to, 'n/j');
        }
        $this->_params['s_tgt_day'] = $date_from;
        $this->_params['s_display_term'] = $s_display_term;
        $this->_params['s_bumon'] = $bumon;
        $this->_params['s_current_bumon_cd'] = $s_current_bumon_cd;
        $this->_params['logoin_tanto_cd'] = App_Utils::getTantoCd();
        $this->_params['logoin_tanto_nm'] = App_Utils::getTantoNm();
        return $this->_params;
    }

    /**
     * 施行予定データを取得する
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     * @return array
     */
    protected function getSekoYtData() {
        $date_from = Msi_Sys_Utils::easyGetVar($this->_params, 'date_from');
        $date_to = Msi_Sys_Utils::easyGetVar($this->_params, 'date_to');
        $dataTantoYt = Logic_Cale_ComLogic::get_assign($date_from, $date_to);
        return $dataTantoYt;
    }

    /**
     * 担当者休暇情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2017/xx/xx
     * @return array
     */
    protected function getVacData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $cond['__x1'] = array('x', "TO_CHAR(vacation_date, 'YYYY/MM/DD') BETWEEN :x1_1 AND :x1_2", array('x1_1' => $this->_params['date_from'], 'x1_2' => $this->_params['date_to']));
        $select = DataMapper_SekoSchedule::findVac($db, $cond);
        return $select;
    }

    /**
     * 施行件数取得アクション
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     */
    public function sekocountAction() {
        try {
            $db = Msi_Sys_DbManager::getMyDb();
            $params = Msi_Sys_Utils::webInputs();
            $target_ym = null;
            $dataSekoCount = null;
            if (isset($params['target_ym']) && strlen($params['target_ym']) > 0) {
                $target_ym = $params['target_ym'];
            }
            $ymd_list = App_DateCalc::getMonthDay($target_ym);
            if (isset($params['bumon_cd']) && strlen($params['bumon_cd']) > 0) {
                $bumon_cd = $params['bumon_cd'];
                $cond['__x_bumon'] = DataMapper_BumonEx::findDesCond($db, $bumon_cd);
            }
            if (isset($params['seko_basho']) && strlen($params['seko_basho']) > 0) {
                $cond['__x9'] = array('x', '(seko_basho = :x9_1 OR seko_shikijo_cd = :x9_1)'
                    , array('x9_1' => $params['seko_basho']
                ));
//                $cond['seko_basho'] = $params['seko_basho'];
            }
            $ymd_cnt = Msi_Sys_Utils::myCount($ymd_list);
            if ($ymd_cnt > 0) {
                $ymd_st = $ymd_list[0]['ymd'];
                $ymd_ed = $ymd_list[$ymd_cnt - 1]['ymd'];
                $cond['__x1'] = array('x', "cal_ymd BETWEEN :x1_1 AND :x1_2", array('x1_1' => $ymd_st, 'x1_2' => $ymd_ed));
//                $dataSekoCount = DataMapper_SekoNiteiEx::findnitei2($db, $cond);
                $dataSekoCount = DataMapper_SekoNiteiEx::findsekocnt($db, $cond);
            }
            $data['ymd_list'] = $ymd_list;
            $data['dataSekoCount'] = $dataSekoCount;
            $data['status'] = 'OK';
            Msi_Sys_Utils::outJson($data);
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }

}
