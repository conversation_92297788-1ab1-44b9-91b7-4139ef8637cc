var appsch=appsch||{},appschdlg=appschdlg||{};
$(function(){$("#calendar_dialog .close, #dialog_item_shade").click(function(){$("#calendar_dialog, #dialog_item_shade").fadeOut(200)});$(document).bind("keydown",function(a){27===a.keyCode&&$("#calendar_dialog .close").click()});appschdlg.showDialog=function(a,f){var b=a.model.get("seko_no"),e=a.model.get("assign_tanto_cd");appschdlg.niteiDtl=new d;appschdlg.assignDtl=new g;appschdlg.dlgView=new h;$.ajax({url:$.msiJqlib.baseUrl()+"/cale/sekocalendar/oneseko",data:{seko_no:b},type:"POST",success:function(a){if(0<
a.length){var c=new k,d=new l({model:c});c.set(a[0]);$("#calendar_dialog .content ul").remove();$("#calendar_dialog .content").append(d.render().el);$("#calendar_dialog .content .k_nm").attr("title",c.get("seko_no"));"2"===c.get("moushi_kbn")?$("#calendar_dialog .content .seko_hoyo_nm").show():$("#calendar_dialog .content .seko_hoyo_nm").hide();appschdlg.niteiDtl.reset(a);f&&(a=_.where(appsch.dataAll.dataTantoYt,{seko_no:b,assign_tanto_cd:e}),appschdlg.assignDtl.reset(a));$("#calendar_dialog, #dialog_item_shade").fadeIn(200)}}})};
var h=Backbone.View.extend({el:$("#calendar_dialog"),initialize:function(){this.listenTo(appschdlg.niteiDtl,"reset",this.addSekoNiteiMsiCol);this.listenTo(appschdlg.assignDtl,"reset",this.addSekoAssignMsiCol);this.render()},render:function(){},addNiteiMsiOne:function(a){a=new e({model:a});$("#calendar_dialog .content .seko_info").append(a.render().el)},addSekoNiteiMsiCol:function(a){a.each(this.addNiteiMsiOne,this)},addAssignMsiOne:function(a){a=new m({model:a});$("#calendar_dialog .content .assign_info").append(a.render().el)},
addSekoAssignMsiCol:function(a){a.each(this.addAssignMsiOne,this)}}),k=Backbone.Model.extend({defaults:function(){return{seko_no:null,k_nm:null,m_nm:null,seko_tanto_nm:null,mg_tel:null,mg_addr:null,mg_addr1:null,mg_addr2:null,biko:null,nitei_kbn:null,nitei_kbn_nm:null,nitei_ymd:null}}}),b=Backbone.Model.extend({defaults:function(){return{}}}),d=Backbone.Collection.extend({model:b}),g=Backbone.Collection.extend({model:b,comparator:"assign_date"}),l=Backbone.View.extend({tagName:"ul",className:"",tmpl1:_.template($("#tmpl-seko-info").html()),
events:{"click a.more":"showNew"},bindings:{".family .seko_no":"seko_no",".family .souke_nm":"souke_nm",".family .tanto_nm":"seko_tanto_nm",".family .uketuke_tanto_nm":"uketuke_tanto_nm",".family .kasoba_nm":"kasoba_nm",".family .choi_kouden":"choi_kouden_nm",".family .choi_kyoka":"choi_kyoka_nm",".family .basho_nm":"shiki_basho_nm",".family .k_nm":"k_nm",".family .m_nm":"m_nm",".family .relation":"m_zoku_kbn_nm",".family .tel":"mg_tel",".family .address":"mg_addr"},initialize:function(){},render:function(){this.$el.html(this.tmpl1(this.model.toJSON()));
this.stickit();return this},showNew:function(){var a=this.model.get("seko_no");if(!$.msiJqlib.isNullEx2(a)){var b=$.msiJqlib.baseUrl()+"/juchu/mitsu/input/sn/"+a;"2"===this.model.get("moushi_kbn")&&(b=$.msiJqlib.baseUrl()+"/juchu/houji/input/sn/"+a);msiLib2.openWinSub(function(){},b)}}}),e=Backbone.View.extend({tagName:"li",tmpl:_.template($("#tmpl-one-seko-nitei-msi").html()),bindings:{".time":{observe:"nitei_ymd_mdhm",onGet:function(a){return $.msiJqlib.isNullEx2(this.model.get("nitei_e_time"))?
a:a+" \uff5e "+this.model.get("nitei_e_time")}},".ceremony":"nitei_kbn_nm",".place":{observe:"basho_nm",onGet:function(a){return"0"===this.model.get("basho_kbn")?"\u81ea\u5b85":a}}},initialize:function(){},render:function(){this.$el.html(this.tmpl(this.model.toJSON()));this.stickit();return this}}),m=Backbone.View.extend({tagName:"li",tmpl:_.template($("#tmpl-one-seko-nitei-msi").html()),className:"assign_task",bindings:{".time":"assign_date",".ceremony":"assign_title"},initialize:function(){},render:function(){this.$el.html(this.tmpl(this.model.toJSON()));
this.stickit();return this}})});
