<?php

/**
 * PDF 見積書  軽減税率対応(keigen)
 * Pdf0113Controller.bellmony.php からコピーして作成
 *
 * @category   App
 * @package    controller
 * <AUTHOR> <PERSON>
 * @since      2014/02/19
 * @since      2015/01/26 佐野商店カスタマイズ
 * @since      2015/02/17 サンメンバーズカスタマイズ
 * @version    2017/08/02 Sai 二世会カスタマイズ
 * @version    2019/04/30 mihara 軽減税率対応
 * @version    2020/07/01 kobayashi 博全社カスタマイズ
 * @version    2022/07/12 kobayashi セレモアカスタマイズ
 * @filesource 
 */

/**
 * PDF 見積書
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Kobayashi
 * @since      2022/07/12
 */
class Juchu_Pdf0113Controller extends Zend_Controller_Action {

    private static $title = '見積書';
    private static $sourceFileName = array('pdf_tmpl/0113.pdf', 'pdf_tmpl/0113_00.pdf');
    private static $sourceFileKagami = 'pdf_tmpl/0113_K.pdf';

    /** 申込区分 1：葬儀 2：法事 5：生前依頼 6：その他依頼 */
    private static $_moushiKbn;
    private static $_kijun_ymd;
    private static $_p_seko_no;
    private static $_p_seko_no_sub;
    private static $_p_data_kbn;
    private static $_is_reduced_tax_rate_row_exists;
    private static $_p_denpyo_no;

    /** データ区分 1：葬儀 2：法事 */
    private static $dataSOUGI = 1;
    private static $dataHOUJI = 2;
    
    /**
     * アクション
     *
     * <AUTHOR> Kobayashi
     * @since   2020/07/01
     */
    public function indexAction($paramsData=array()) {
//        App_DevCoverage_Manager::easyStart();
        if (Msi_Sys_Utils::myCount($paramsData) > 0) {
            $params = $paramsData;
        } else {
            $params = Msi_Sys_Utils::webInputs();
        }
        if (isset($params['preview'])) {
            $preview = htmlspecialchars($params['preview']);         // プレビュー有無
        } else {
            $preview = 'off';
        }
        $seko_no = htmlspecialchars($params['seko_no']);            // 施行番号
        if (array_key_exists('seko_no_sub', $params)) {
            $seko_no_sub = htmlspecialchars($params['seko_no_sub']);
        } // 施行番号（枝番）
        else {
            $seko_no_sub = '00';
        }
        $data_kbn = htmlspecialchars($params['data_kbn']);        // データ区分　1：葬儀 2：法事 3：単品 4：別注品
        self::$_moushiKbn = DataMapper_PdfCommon::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
        if (isset($params['printKbn'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_kbn = $params['printKbn'];
        } else {
            $print_kbn = 0;
        }
        if (isset($params['printSbt'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_sbt = $params['printSbt'];
        } else {
            $print_sbt = 'normal';
        }
        // 軽減税率対応 mihara keigen   基準日の設定
        self::_prepKijunYmdKeigen($seko_no, $seko_no_sub, $data_kbn);

        $issue_date = date('Y/m/d');

        $db = Msi_Sys_DbManager::getMyDb();
        $pdfObj = new App_Pdf(self::getFileName($db, $seko_no, self::$title));

        if($print_sbt === 'normal'){ // 見積書
            // 履歴PDF表示
            if (isset($params['history_no']) && strlen($params['history_no']) > 0) {
                $history_states = $this->outDataHistory($db, $seko_no, $data_kbn, $seko_no_sub, $params['history_no']);
                if($history_states === FALSE){
                    $errMsg = '見積書データが存在しません';
                    $outData = array('status' => 'NG', 'msg' => $errMsg,);
                    Msi_Sys_Utils::outJson( $outData );
                }
                return;
            } else { // not履歴
                if (self::$_moushiKbn == 2){
                    //「法事」場合
                    if(isset($params['rireki_no'])){
                        self::outData_houji($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, false, $params['rireki_no']);
                    } else {
                        self::outData_houji($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);
                    }
                } else {
                    //「葬儀」の場合
                    if(isset($params['rireki_no'])){
                        self::outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, false, $params['rireki_no']);
                    } else {
                        self::outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);
                    }
                }
            }
        } else if($print_sbt === 'history'){ // 見積履歴(事前相談)
            if(isset($params['history_no']) && $params['history_no'] !== ''){
                $history_no = $params['history_no'];
            } else {
                $history_no = 0;
            }
            $rireki_flg = self::outDataMeisaiHistory($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $history_no);
            if($rireki_flg === FALSE){
                $errMsg = '見積履歴データが存在しません';
                $outData = array('status' => 'NG', 'msg' => $errMsg,);
                Msi_Sys_Utils::outJson( $outData );
                return;
            }
            
        }

        if ($preview == 'on') {
            $buf = $pdfObj->fileOutBuf();
            $file_name = $pdfObj->getFileName();
            $key = Msi_Sys_Utils::prepareMyTempFileEasy($buf, $file_name);
            $this->view->file = $file_name;
            $this->view->key = $key;
            $this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
            $this->_helper->viewRenderer->setScriptAction('pdf');
        } else if ($preview == 'save') {
            $buf = $pdfObj->fileOutBuf();
            return $buf;
        } else {
            $pdfObj->download();
        }
    }

    /**
     * 履歴PDF表示
     * <AUTHOR> Kobayashi
     * @since 2020/10/21
     */
	public function outDataHistory($db, $seko_no, $data_kbn, $seko_no_sub, $history_no)
    {
        $rec = DataMapper_Pdf0113::findHistoryDenpyo($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "history_no" => $history_no));
        
        if(Msi_Sys_Utils::myCount($rec) > 0){
            $recHistorty = $rec[0];
        } else {
            return FALSE;
        }
        //  PDFファイル
        if(isset($recHistorty['pdf_oid'])){
            $hachu_pdf = $recHistorty['pdf_oid'];
            $cont = $db->readBlobCont( $hachu_pdf );
        } else {
            return FALSE;
        }
        //  ファイル名
        if(isset($recHistorty['file_nm']) && $recHistorty['file_nm'] !== ''){
            $fname = $recHistorty['file_nm'].'.pdf';
        } else {
            $fname = $recHistorty['denpyo_no'].'-'.$recHistorty['history_no'].'.pdf';
        }
        
        Msi_Sys_Utils::out2wayPush($cont, $fname);
        // ファイルコンテンツを返す
        Msi_Sys_Utils::out2wayFlush();

        return TRUE;
	}

    /**
     * 見積書（葬儀）
     * <AUTHOR> Kobayashi
     * @since 2022/08/01
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei = false, $rireki_no = null) {

//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
            $recHead['rireki_no'] = $rireki_no;
        }
        
        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        $bumon_logo = $db->readBlobCont($recHead['bumon_logo']); // 部門ロゴ
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;

        // 鏡
        self::outDataKagami($pdfObj, $db, $seko_no, $recHead);

		// 明細書
		$sumArr = self::outDataDetail($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead);
        
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            
            // -- 鏡 --
            if($page == 1){
                // 御見積金額
                $pdfObj->write_string(array('x' => 210, 'y' => 392, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), '￥'.number_format($sumArr['total']).'－');
                // 内訳(大分類毎)
                $y1 = 448;
                $y2 = 668;
                $dai_total1 = 0;
                $dai_total2 = 0;
                foreach ($sumArr as $daibunrui_cd => $value) {
                    if(preg_match('/^([0-9]{4})$/', $daibunrui_cd)){ // キーが大分類(数字4桁)のみ対象
                        if($daibunrui_cd !== '0060' && $daibunrui_cd !== '0070'){ // 0060:立替費用, 0070:特別値引
                                $pdfObj->write_string(array('x' => 90, 'y' => $y1, 'width' => 85, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 大分類名
                                $pdfObj->write_num(array('x' => 185, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                                $y1 += 24.5;
                                $dai_total1 += $value['sum'];
                        } else {
                                $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 大分類名
                                $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                                $y2 += 24.5;
                                $dai_total2 += $value['sum'];
                        }
                    }
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 595, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $dai_total1); // 総計
                // 御見積金額
                $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '　　  小計'); // 小計
                $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $dai_total1+$dai_total2+$sumArr['tax']); // 小計
                if($sumArr['kaiin'] !== 0){ // 互助会利用合計
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2+24.5, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '互助会利用合計');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2+24.5, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['kaiin']);
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 766.5, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['total']); // 御見積金額
                // 部門情報(大)
                $pdfObj->write_image(array('x' => 348, 'y' => 708, 'width' => 220, 'height' => 65, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), $recHead['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>353, 'y' => 784, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $recHead['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $recHead['bumon_number']);
                }
                $pdfObj->write_string(array('x' =>331, 'y' => 798, 'width' => 500, 'height' => 11, 'font_size' => 8), '※御見積書の人名用漢字はシステムの関係上いくつかの旧字体が');
                $pdfObj->write_string(array('x' =>339, 'y' => 808, 'width' => 500, 'height' => 11, 'font_size' => 8), '常用漢字表記となります。ご容赦下さい。');
            }
            
            if($page !== 1){
                if(!isset($recHead['rireki_no'])){
                    $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no']); // 受付番号
                } else {
                    $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no'].'-'.sprintf('%02d', $recHead['rireki_no'])); // 受付番号
                }
                if($sumArr['nebiki_flg'] <> 0){
                    if($page == 2){
                        $pdfObj->write_string(array('x' =>309, 'y' => 119, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    } else {
                        $pdfObj->write_string(array('x' =>309, 'y' => 50, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    }
                }
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>390, 'y'=>742, 'width'=>400, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象、「内」は内税対象');
                }
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 420, 'y' => 760, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), $recHead['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>424, 'y' => 810, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
                }
                // ページ
                $disp_page = $page-1;
                $disp_numPages = $numPages-1;
                $pdfObj->write_string(array('x' => 45, 'y' => 800, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $disp_page . '/' . $disp_numPages);
            }
        }
    }
    
    /**
     * 御見積書鏡(葬儀)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagami($pdfObj, $db, $seko_no, $recHead) {
        
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileKagami);
        
        $date_format = "Y年n月j日";
        $date_format_time = "G時i分";
        
        if(!isset($recHead['rireki_no'])){
            $pdfObj->write_string(array('x'=>450, 'y'=>35, 'width'=>125, 'height'=>9, 'align' => 'R', 'font_size' => 9), '受付番号：'.$seko_no); // 受付番号
        } else {
            $pdfObj->write_string(array('x'=>450, 'y'=>35, 'width'=>125, 'height'=>9, 'align' => 'R', 'font_size' => 9), '受付番号：'.$seko_no.'-'.sprintf('%02d', $recHead['rireki_no'])); // 受付番号
        }
        if(isset($recHead['jichu_kakute_ymd'])){ // 見積書発行日(見積確定済の場合は見積確定日)
            $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['jichu_kakute_ymd'], $date_format);
        } else {
            $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['issue_date'], $date_format);
        }
        $pdfObj->write_string(array('x'=>325, 'y'=>110, 'width'=>125, 'height'=>9, 'font_size' => 11), '見積担当：'.$recHead['mitsu_tanto']); // 見積担当
        
        // 施主
        $pdfObj->write_string(array('x' => 52, 'y' => 108, 'width' => 125, 'height' => 9, 'font_size' => 10), '施主');
        $pdfObj->write_string(array('x' => 80, 'y' => 108, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['sekyu_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 119, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['s_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 130, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 141, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['s_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['s_mobile_tel']); // 携帯電話
        // 喪主
        $pdfObj->write_string(array('x' => 52, 'y' => 185, 'width' => 125, 'height' => 9, 'font_size' => 10), '喪主');
        $pdfObj->write_string(array('x' => 80, 'y' => 185, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['m_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 196, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['mg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 206, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['mg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 217, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['mg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 235, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['mg_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 235, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['mg_m_tel']); // 携帯電話
        // 故人
        $pdfObj->write_string(array('x' => 52, 'y' => 262, 'width' => 125, 'height' => 9, 'font_size' => 10), '故人');
        $pdfObj->write_string(array('x' => 80, 'y' => 262, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['k_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 273, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['kg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 284, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['kg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 295, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['kg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 306, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['kg_tel']); // TEL
        $pdfObj->write_string(array('x' => 80, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご年齢：' . $recHead['k_nenrei_man']. '歳'); // 満年齢
        $pdfObj->write_string(array('x' => 150, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), '喪主とのご関係：' . $recHead['m_zoku_nm']); // 続柄
        $pdfObj->write_string(array('x' => 80, 'y' => 328, 'width' => 125, 'height' => 9, 'font_size' =>10), 'ご逝去日：'); // 死亡日ラベル
        $pdfObj->write_date(array('x' => 130, 'y' => 328, 'width' => 125, 'height' => 9, 'type' => 'time', 'font_size' => 10), $recHead['sibou_ymd'], $date_format);  // 死亡日
        // 施行日程
        $recNitei = DataMapper_SekoNiteiEx::find($db, array('seko_no' => $seko_no));
        $y = 127;
        $sikijyo_flg = 0;
        $kasoba_flg = 0;

        foreach ($recNitei as $value) {
            if (isset($value['nitei_ymd_ymd'])) {
                switch ($value['nitei_kbn']) {
                    case 3: // 納棺
                    case 4: // 通夜
                    case 5: // 出棺
                    case 11: // 葬儀
                        // 日程区分名
                        if (isset($value['v_free4'])) {
                            $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['v_free4'] . '：');
                        } else {
                            $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['nitei_kbn_nm'] . '：');
                        }
                        $pdfObj->write_date(array('x' => 405, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_ymd_ymd'], $date_format);  // 日付
                        $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_s_time'], $date_format_time); // 開始時間
//                        if($value['nitei_kbn'] !== '5'){ // より（出棺以外）
//                            $pdfObj->write_string(array('x' => 540, 'y' => $y, 'width' => 100, 'height' => 9, 'font_size' => 11), 'より');
//                        }
//                        if (($value['nitei_kbn'] == '4' || $value['nitei_kbn'] == '11') && isset($value['nitei_e_time'])) { // 終了時間（通夜・葬儀のみ）
//                            $y += 12;
//                            $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 11), $value['nitei_e_time'], $date_format_time);
//                        }
                        if ($value['nitei_kbn'] == '3' && isset($value['basho_kbn'])) { // 場所（納棺のみ）
                            $y += 12;
                            if ($value['basho_kbn'] === '0') {
                                $basho_nm = '自宅';
                            } else {
                                $basho_nm = $value['basho_nm'];
                            }
                            $pdfObj->write_string(array('x' => 405, 'y' => $y, 'width' => 150, 'height' => 9, 'font_size' => 10), $basho_nm);
                        }
                        if ($sikijyo_flg == 0 && isset($value['shiki_basho_nm'])) { // 式場
                            $sikijyo_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 221, 'width' => 82, 'height' => 9, 'font_size' => 10), '式場：');
                            $pdfObj->write_string(array('x' => 405, 'y' => 221, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 233, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_zip']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 245, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_addr1']);
                            $pdfObj->write_string(array('x' => 405, 'y' => 257, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['shikijyo_addr2']);
                        }
                        if ($kasoba_flg == 0 && isset($value['kasoba_nm'])) { // 火葬場所
                            $kasoba_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 278, 'width' => 160, 'height' => 9, 'font_size' => 10), '火葬場所：');
                            $pdfObj->write_string(array('x' => 405, 'y' => 278, 'width' => 160, 'height' => 9, 'font_size' => 10), $value['kasoba_nm']);
                        }
                        break;
                    default:
                        break;
                }
            }
            $y += 12;
        }
        
        if(isset($recHead['todokede']) && $recHead['todokede'] !== ''){ // 届出役所
            $pdfObj->write_string(array('x'=>325, 'y'=>290, 'width'=>150, 'height'=>9, 'font_size' => 10), '届出役所：');
            $pdfObj->write_string(array('x'=>405, 'y'=>290, 'width'=>150, 'height'=>9, 'font_size' => 10), $recHead['todokede']);
        }

        if(isset($recHead['seko_plan_nm'])){ // 利用コース
            $pdfObj->write_string(array('x'=>325, 'y'=>312, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['seko_plan_nm'].'　'.number_format($recHead['seko_prc']).'円');
            $pdfObj->write_string(array('x'=>325, 'y'=>324, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['plan_summary_nm']);
        }
        
        // 見積金額文
        $word = '葬儀費用として、下記の通りお見積り申し上げます。';
        $pdfObj->write_string(array('x'=>52, 'y'=>369 ,'width'=>500, 'height'=>9, 'font_size' => 11), '故　'.$recHead['k_nm'].'　様　'.$word); // 故人名
        
        // 内金金額
        $uchikin = DataMapper_Pdf0113::getUchikin($db, $seko_no, self::$dataSOUGI);
        if($uchikin <> 0){
            $pdfObj->write_string(array('x'=> 450, 'y'=> 387 ,'width'=> 75, 'height'=>9, 'font_size' => 11), '内金金額');
            $pdfObj->write_num(array('x' => 450, 'y' => 401, 'width' => 75, 'height' => 15, 'align' => 'R', 'font_size' => 14), $uchikin);
        }
        
        // 備考欄文言
        $b1 = '(その他費用) 火葬料金　市民・市民外　　　　　　　円';
        $b2 = '(別途火葬場への支払い)';
        $b3 = 'ドリンク代 司祭者へのお礼';
        $b4 = '供花・供物代金';
        $pdfObj->write_string(array('x'=> 322, 'y'=> 515 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b1);
        $pdfObj->write_string(array('x'=> 322, 'y'=> 524 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b2);
        $pdfObj->write_string(array('x'=> 322, 'y'=> 533 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b3);
        $pdfObj->write_string(array('x'=> 322, 'y'=> 542 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b4);
        
        // 消費税
        $outzei = self::outZeiUchiwake1($pdfObj, 0, -58);
        $pdfObj->write_num(array('x' => 185, 'y' => 629, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $outzei); // 外税消費税
        // 互助会消費税
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no,"yoto_kbn" => '1', '__etc_orderby'=>array('yoto_kbn', 'kain_no',)));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            self::outGojoZeiUchiwake($pdfObj, $db, $kaiin_info);
        }
        
        // サイン欄
        $pdfObj->write_string(array('x'=> 144, 'y'=> 790.5 ,'width'=> 120, 'height'=>9, 'font_size' => 10), 'ご葬家様サイン欄');
    }

    /**
     * 明細を出力
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataDetail($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead) {
        
        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 146;
        static $meisai_row_height1 = 23.25;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 77;
        static $meisai_row_height2 = 23.25;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $dai_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        $sumArr['nebiki_flg'] = 0;
        
        $recMsi = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
        self::$_p_denpyo_no= $recMsi[0]['denpyo_no'];

        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if($total_tokuten == 0){
                        $total_tokuten = '';
                    } else {
                        $sumArr['nebiki_flg'] ++;
                    }
                    // 合計
                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['dai_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $nebiki_prc; // 請求金額
            $gojokai_nebiki_prc = '';
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0){
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc'];
            }

            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if(isset($value['plan_shohin_nm'])){
                $biko1 .= $value['plan_shohin_nm'].'から変更';
            }
            if($value['hoshi_disp'] !== ''){
                $biko2 .= $value['hoshi_disp'].'　';
            }
            $biko2 .= $value['shohin_tkiyo_nm'];

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['juchu_tnk'], $value['juchu_suryo'], $value['juchu_prc'], $gojokai_nebiki_prc, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp'], null);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['juchu_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['juchu_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['juchu_prc'];
                }
            }

            $total_ippan += $value['juchu_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if($total_tokuten == 0){
                $total_tokuten = '';
            } else {
                $sumArr['nebiki_flg'] ++;
            }
            // 合計
            $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
        $tax_info = self::outZeiUchiwake2($row_arr); // 内訳
        $sumArr['tax'] = $tax_info['tax_total'];
        $total_prc_all = $tax_info['total']; // UPD 2025/05/23 消費税内訳から合計金額上書きに変更
        $out_tax_total = 0;
        if (Msi_Sys_Utils::myCount($taxArr) > 0) { // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
        }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $total_gojokai_nebiki_prc = $total_prc_all-$zei_nebikimae_total;
        if($total_gojokai_nebiki_prc == 0){
            $total_gojokai_nebiki_prc = '';
        }
        $row_arr[] = array('【合　計】', null, null, null, null, $zei_nebikimae_total, $total_gojokai_nebiki_prc, null, null, null, null, null, $total_prc_all); // 合計
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no
                , '__etc_orderby_raw'=>"T.yoto_kbn, CASE WHEN ( T.point=0 and T.riyoken=0 ) THEN 1 WHEN ( T.point>0 ) THEN 2 WHEN ( T.riyoken>0 ) THEN 3 ELSE 4 END, T.kain_no"));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        $sumArr['kaiin'] = $kaiin_total;
        
        // 御見積金額
        $mitsumori_prc = $total_prc_all + $kaiin_total;
        $row_arr[] = array('【御見積金額】', null, null, null, null, null, null, $mitsumori_prc, null, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        
        // 内金
        $uchikin = DataMapper_Pdf0113::getUchikin($db, $seko_no, self::$dataSOUGI);
        if($uchikin <> 0){
            $row_arr[] = array();
            $row_arr[] = array('【内金金額】', null, null, null, null, null, null, $uchikin, null, null, null, null, null);
            $row_arr[] = array('【内金差引後金額】', null, null, null, null, null, null, $mitsumori_prc-$uchikin, null, null, null, null, null);
        }
        
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, '以下余白', null, null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName[0]);
        // ヘッダー
        $pdfObj->write_string(array('x' => 205, 'y' => 55, 'width' => 200, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 見 積 明 細 書');
        self::outHeader($pdfObj, $recHead);
//        self::outPlanSummaryNm($pdfObj, $recHead);
        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName[1]);
        
        return $sumArr;
    }

    /**
     * 見積書（法事）
     * <AUTHOR> Kobayashi
     * @since 2022/08/01
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outData_houji($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei = false, $rireki_no = null) {

//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
            $recHead['rireki_no'] = $rireki_no;
        }
        
        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        $bumon_logo = $db->readBlobCont($recHead['bumon_logo']); // 部門ロゴ
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;

        // 鏡
        self::outDataKagami_houji($pdfObj, $db, $seko_no, $recHead);

		// 明細書
		$sumArr = self::outDataDetail_houji($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead);

        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            
            // -- 鏡 --
            if($page == 1){
                // 御見積金額
                $pdfObj->write_string(array('x' => 210, 'y' => 392, 'width' => 200, 'height' => 15, 'align' => 'C', 'font_size' => 16), '￥'.number_format($sumArr['total']).'－');
                // 内訳(中分類毎)
                $y1 = 448;
                $y2 = 668;
                $chu_total1 = 0;
                $chu_total2 = 0;
                $chubun1 = array('法要費用'=>0, '法要返礼品'=>0, '法要会食'=>0);
                foreach ($sumArr as $chubunrui_cd => $value) {
                    if(preg_match('/^([0-9]{4})$/', $chubunrui_cd)){ // キーが中分類(数字4桁)のみ対象
                        if($chubunrui_cd !== 1130 && $chubunrui_cd !== 1150){ // 1130:法要立替金, 1150:法要値引き
                            if($chubunrui_cd == 1090){ // 1090:法要返礼品
                                $chubun1['法要返礼品'] += $value['sum'];
                            } else if($chubunrui_cd == 1120){ // 1120:法要会食
                                $chubun1['法要会食'] += $value['sum'];
                            } else {
                                $chubun1['法要費用'] += $value['sum'];
                            }
                        } else {
                            if($chubunrui_cd == 1130){
                                $value['name'] = '立替費用';
                            }
                            if($chubunrui_cd == 1150){
                                $value['name'] = '特別値引';
                            }
                            $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $value['name']); // 中分類名
                            $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $value['sum']); // 金額
                            $y2 += 24.5;
                            $chu_total2 += $value['sum'];
                        }
                    }
                }
                foreach ($chubun1 as $name => $prc) {
                    if($prc <> 0){
                        $pdfObj->write_string(array('x' => 90, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), $name); // 中分類名
                        $pdfObj->write_num(array('x' => 185, 'y' => $y1, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $prc); // 金額
                        $y1 += 24.5;
                        $chu_total1 += $prc;
                    }
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 595, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $chu_total1); // 総計
                // 御見積金額
                $pdfObj->write_string(array('x' => 65, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '　　  小計'); // 小計
                $pdfObj->write_num(array('x' => 185, 'y' => $y2, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $chu_total1+$chu_total2+$sumArr['tax']); // 小計
                if($sumArr['kaiin'] !== 0){ // 互助会利用合計
                    $pdfObj->write_string(array('x' => 65, 'y' => $y2+24.5, 'width' => 110, 'height' => 15, 'align' => 'L', 'font_size' => 12), '互助会利用合計');
                    $pdfObj->write_num(array('x' => 185, 'y' => $y2+24.5, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['kaiin']);
                }
                $pdfObj->write_num(array('x' => 185, 'y' => 766.5, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $sumArr['total']); // 御見積金額
                // 部門情報(大)
                $pdfObj->write_image(array('x' => 348, 'y' => 708, 'width' => 220, 'height' => 65, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), $recHead['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>353, 'y' => 784, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $recHead['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>353, 'y' => 773, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $recHead['bumon_number']);
                }
                $pdfObj->write_string(array('x' =>331, 'y' => 798, 'width' => 500, 'height' => 11, 'font_size' => 8), '※御見積書の人名用漢字はシステムの関係上いくつかの旧字体が');
                $pdfObj->write_string(array('x' =>339, 'y' => 808, 'width' => 500, 'height' => 11, 'font_size' => 8), '常用漢字表記となります。ご容赦下さい。');
            }
            
            if($page !== 1){
                if(!isset($recHead['rireki_no'])){
                    $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no']); // 受付番号
                } else {
                    $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no'].'-'.sprintf('%02d', $recHead['rireki_no'])); // 受付番号
                }
                if($sumArr['nebiki_flg'] <> 0){
                    if($page == 2){
                        $pdfObj->write_string(array('x' =>309, 'y' => 119, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    } else {
                        $pdfObj->write_string(array('x' =>309, 'y' => 50, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                    }
                }
                // 軽減税率対応 凡例
                if (self::_isKeigenAppliedCtxt() ) {
                    $pdfObj->write_string(array('x'=>390, 'y'=>742, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象、「内」は内税対象');
                }
                // 部門情報(小)
                $pdfObj->write_image(array('x' => 420, 'y' => 760, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
                if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), $recHead['bumon_kaisya_nm']);
                    $pdfObj->write_string(array('x' =>424, 'y' => 810, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
                } else {
                    $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
                }
                // ページ
                $disp_page = $page-1;
                $disp_numPages = $numPages-1;
                $pdfObj->write_string(array('x' => 45, 'y' => 800, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $disp_page . '/' . $disp_numPages);
            }
        }
    }
    
    /**
     * 御見積書鏡(法事)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataKagami_houji($pdfObj, $db, $seko_no, $recHead) {
        
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileKagami);
        
        $date_format = "Y年n月j日";
        $date_format_time = "G時i分";
        
        if(!isset($recHead['rireki_no'])){
            $pdfObj->write_string(array('x'=>450, 'y'=>35, 'width'=>125, 'height'=>9, 'align' => 'R', 'font_size' => 9), '受付番号：'.$seko_no); // 受付番号
        } else {
            $pdfObj->write_string(array('x'=>450, 'y'=>35, 'width'=>125, 'height'=>9, 'align' => 'R', 'font_size' => 9), '受付番号：'.$seko_no.'-'.sprintf('%02d', $recHead['rireki_no'])); // 受付番号
        }
        if(isset($recHead['jichu_kakute_ymd'])){ // 見積書発行日(見積確定済の場合は見積確定日)
            $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['jichu_kakute_ymd'], $date_format);
        } else {
            $pdfObj->write_date(array('x' => 450, 'y' => 45, 'width' => 125, 'height' => 9, 'type' => 'time', 'align' => 'R', 'font_size' => 9), $recHead['issue_date'], $date_format);
        }
        $pdfObj->write_string(array('x'=>325, 'y'=>110, 'width'=>125, 'height'=>9, 'font_size' =>11), '見積担当：'.$recHead['mitsu_tanto']); // 見積担当
        
        // 施主
        $pdfObj->write_string(array('x' => 52, 'y' => 108, 'width' => 125, 'height' => 9, 'font_size' => 10), '請求先');
        $pdfObj->write_string(array('x' => 80, 'y' => 108, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['sekyu_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 119, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['s_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 130, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 141, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['s_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['s_tel']); // TEL
        $pdfObj->write_string(array('x' => 193, 'y' => 158, 'width' => 125, 'height' => 9, 'font_size' => 10), '携帯：' . $recHead['s_mobile_tel']); // 携帯電話
        // 故人
        $pdfObj->write_string(array('x' => 52, 'y' => 262, 'width' => 125, 'height' => 9, 'font_size' => 10), '故人');
        $pdfObj->write_string(array('x' => 80, 'y' => 262, 'width' => 218, 'height' => 9, 'font_size' => 10), 'お名前：' . $recHead['hk_nm'] . ' 様'); // 名前
        $pdfObj->write_string(array('x' => 80, 'y' => 273, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご住所：〒' . $recHead['hkg_yubin_no']); // 郵便番号
        $pdfObj->write_string(array('x' => 122, 'y' => 284, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['hkg_addr1']); // 住所1
        $pdfObj->write_string(array('x' => 122, 'y' => 295, 'width' => 177, 'height' => 9, 'font_size' => 10), $recHead['hkg_addr2']); // 住所2
        $pdfObj->write_string(array('x' => 80, 'y' => 306, 'width' => 125, 'height' => 9, 'font_size' => 10), 'お電話：' . $recHead['hkg_tel']); // TEL
        $pdfObj->write_string(array('x' => 80, 'y' => 317, 'width' => 125, 'height' => 9, 'font_size' => 10), 'ご年齢：' . $recHead['hk_nenrei_man']. '歳'); // 満年齢
        $pdfObj->write_string(array('x' => 80, 'y' => 328, 'width' => 125, 'height' => 9, 'font_size' =>10), 'ご逝去日：'); // 死亡日ラベル
        $pdfObj->write_date(array('x' => 130, 'y' => 328, 'width' => 125, 'height' => 9, 'type' => 'time', 'font_size' => 10), $recHead['hk_death_ymd'], $date_format);  // 死亡日
        
        // 施行日程
        $recNitei = DataMapper_SekoNiteiHouji::find($db, array('seko_no' => $seko_no));
        $y = 127;
        $sikijyo_flg = 0;

        foreach ($recNitei as $value) {
            if (isset($value['nitei_ymd'])) {
                switch ($value['nitei_kbn']) {
                    case 1: // ご法要
                    case 3: // ご法宴
                    case 5: // 納骨
                        // 日程区分名
                        $pdfObj->write_string(array('x' => 325, 'y' => $y, 'width' => 82, 'height' => 9, 'font_size' => 10), $value['nitei_kbn_nm'] . '：');
                        $pdfObj->write_date(array('x' => 405, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_ymd'], $date_format);  // 日付
                        $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_time'], $date_format_time); // 開始時間
//                        $pdfObj->write_string(array('x' => 540, 'y' => $y, 'width' => 100, 'height' => 9, 'font_size' => 11), 'より');
//                        if (($value['nitei_kbn'] == '1' || $value['nitei_kbn'] == '3') && isset($value['nitei_ed_time'])) { // 終了時間（ご法要・ご法宴のみ）
//                            $y += 12;
//                            $pdfObj->write_date(array('x' => 492, 'y' => $y, 'width' => 100, 'height' => 9, 'type' => 'time', 'font_size' => 10), $value['nitei_ed_time'], $date_format_time);
//                        }
                        if ($value['nitei_kbn'] == '5') { // 場所（納棺のみ）
                            $y += 12;
                            $pdfObj->write_string(array('x' => 405, 'y' => $y, 'width' => 150, 'height' => 9, 'font_size' => 10), $value['basho_nm']);
                        }
                        if ($sikijyo_flg == 0 && isset($recHead['h_shikijyo'])) { // 式場
                            $sikijyo_flg ++;
                            $pdfObj->write_string(array('x' => 325, 'y' => 241, 'width' => 82, 'height' => 9, 'font_size' => 10), '式場：');
                            $pdfObj->write_string(array('x' => 400, 'y' => 241, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 253, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_zip']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 265, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_addr1']);
                            $pdfObj->write_string(array('x' => 400, 'y' => 277, 'width' => 160, 'height' => 9, 'font_size' => 10), $recHead['h_shikijyo_addr2']);
                        }
                        break;
                    default:
                        break;
                }
            }
            $y += 12;
        }

        if(isset($recHead['seko_plan_nm'])){ // 利用コース
            $pdfObj->write_string(array('x'=>325, 'y'=>312, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['seko_plan_nm'].'　'.number_format($recHead['seko_prc']).'円');
            $pdfObj->write_string(array('x'=>325, 'y'=>324, 'width'=>240, 'height'=>9, 'font_size' => 10), $recHead['plan_summary_nm']);
        }

        // 見積金額文
        $word = '法要費用として、下記の通りお見積り申し上げます。';
        $pdfObj->write_string(array('x'=>52, 'y'=>369, 'width'=>500, 'height'=>9, 'font_size' => 11), '故　'.$recHead['hk_nm'].'　様　'.$word); // 死亡日
        
        // 内金金額
        $uchikin = DataMapper_Pdf0113::getUchikin($db, $seko_no, self::$dataHOUJI);
        if($uchikin <> 0){
            $pdfObj->write_string(array('x'=> 450, 'y'=> 387 ,'width'=> 75, 'height'=>9, 'font_size' => 11), '内金金額');
            $pdfObj->write_num(array('x' => 450, 'y' => 401, 'width' => 75, 'height' => 15, 'align' => 'R', 'font_size' => 14), $uchikin);
        }
        
        // 備考欄文言
        $b1 = '当日の流れ';
        $b2 = 'ご持参品';
        $pdfObj->write_string(array('x'=> 322, 'y'=> 445 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b1);
        $pdfObj->write_string(array('x'=> 322, 'y'=> 495 ,'width'=> 300, 'height'=>9, 'font_size' => 8), $b2);
        
        // 消費税
        $outzei = self::outZeiUchiwake1($pdfObj, 0, -58);
        $pdfObj->write_num(array('x' => 185, 'y' => 629, 'width' => 110, 'height' => 15, 'align' => 'R', 'font_size' => 12), $outzei); // 外税消費税
        // 互助会消費税
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no,"yoto_kbn" => '1', '__etc_orderby'=>array('yoto_kbn', 'kain_no',)));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            self::outGojoZeiUchiwake($pdfObj, $db, $kaiin_info);
        }
        
        // サイン欄
        $pdfObj->write_string(array('x'=> 144, 'y'=> 790.5 ,'width'=> 120, 'height'=>9, 'font_size' => 10), 'お客様サイン欄');
    }
    
    /**
     * 明細を出力（法事）
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataDetail_houji($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead) {
        
        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 146;
        static $meisai_row_height1 = 23.25;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 77;
        static $meisai_row_height2 = 23.25;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $chu_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        $sumArr['nebiki_flg'] = 0;
        
        $recMsi = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
        self::$_p_denpyo_no= $recMsi[0]['denpyo_no'];

        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['chu_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if($total_tokuten == 0){
                        $total_tokuten = '';
                    } else {
                        $sumArr['nebiki_flg'] ++;
                    }
                    // 合計
                    $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
                    $sumArr[$breakKey]['name'] = $chu_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['chu_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $nebiki_prc; // 請求金額
            $gojokai_nebiki_prc = '';
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0){
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc'];
            }

            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if(isset($value['plan_shohin_nm'])){
                $biko1 .= $value['plan_shohin_nm'].'から変更';
            }
            if($value['hoshi_disp'] !== ''){
                $biko2 .= $value['hoshi_disp'].'　';
            }
            $biko2 .= $value['shohin_tkiyo_nm'];

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['juchu_tnk'], $value['juchu_suryo'], $value['juchu_prc'], $gojokai_nebiki_prc, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp'], null);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['juchu_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['juchu_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['juchu_prc'];
                }
            }

            $total_ippan += $value['juchu_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if($total_tokuten == 0){
                $total_tokuten = '';
            } else {
                $sumArr['nebiki_flg'] ++;
            }
            // 合計
            $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['chu_bunrui_cd']]['name'] = $rec['chu_bunrui_nm'];
            $sumArr[$rec['chu_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['chu_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['chu_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['chu_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
        $tax_info = self::outZeiUchiwake2($row_arr); // 内訳
        $sumArr['tax'] = $tax_info['tax_total'];
        $total_prc_all = $tax_info['total']; // UPD 2025/05/23 消費税内訳から合計金額上書きに変更
        $out_tax_total = 0;
        if (Msi_Sys_Utils::myCount($taxArr) > 0) { // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
        }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $total_gojokai_nebiki_prc = $total_prc_all-$zei_nebikimae_total;
        if($total_gojokai_nebiki_prc == 0){
            $total_gojokai_nebiki_prc = '';
        }
        $row_arr[] = array('【合　計】', null, null, null, null, $zei_nebikimae_total, $total_gojokai_nebiki_prc, null, null, null, null, null, $total_prc_all); // 合計
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no
            , '__etc_orderby_raw'=>"T.yoto_kbn, CASE WHEN ( T.point=0 and T.riyoken=0 ) THEN 1 WHEN ( T.point>0 ) THEN 2 WHEN ( T.riyoken>0 ) THEN 3 ELSE 4 END, T.kain_no"));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        $sumArr['kaiin'] = $kaiin_total;
        
        // 御見積金額
        $mitsumori_prc = $total_prc_all + $kaiin_total;
        $row_arr[] = array('【御見積金額】', null, null, null, null, null, null, $mitsumori_prc, null, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        // 内金
        $uchikin = DataMapper_Pdf0113::getUchikin($db, $seko_no, self::$dataHOUJI);
        if($uchikin <> 0){
            $row_arr[] = array();
            $row_arr[] = array('【内金金額】', null, null, null, null, null, null, $uchikin, null, null, null, null, null);
            $row_arr[] = array('【内金差引後金額】', null, null, null, null, null, null, $mitsumori_prc-$uchikin, null, null, null, null, null);
        }
        
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, '以下余白', null, null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName[0]);
        // ヘッダー
        $pdfObj->write_string(array('x' => 205, 'y' => 55, 'width' => 200, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 見 積 明 細 書');
        self::outHeader_houji($pdfObj, $recHead);
//        self::outPlanSummaryNm($pdfObj, $recHead);
        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName[1]);
        
        return $sumArr;
    }
    
    /**
     * 見積履歴書(事前相談)
     * <AUTHOR> Kobayashi
     * @since 2022/08/04
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outDataMeisaiHistory($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $history_no, $isMituKakutei = false) {

//        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recHeadInfo = DataMapper_Pdf0113::findKagami($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recHeadInfo) == 0) {
            return;
        } else {
            $recHead = $recHeadInfo[0];
            $recHead['issue_date'] = $issue_date;
        }
        
        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
        $bumon_logo = $db->readBlobCont($recHead['bumon_logo']); // 部門ロゴ
        
        // 軽減税率対応のためデータ設定
        self::$_p_seko_no     = $seko_no;
        self::$_p_seko_no_sub = $seko_no_sub;
        self::$_p_data_kbn    = $data_kbn;

        if($history_no == 0){
            $rec = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
        } else {
            $rec = DataMapper_Pdf0113::findMsiHistory($db, array("seko_no" => $seko_no, "history_no" => $history_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__raw_1" => "T2.kashidashi_kbn <> 2"), $print_kbn);
        }
        self::$_p_denpyo_no   = $rec[0]['denpyo_no'];
        // 明細書
        $sumArr = self::outDataDetailHistory($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead, $rec, $history_no);
        
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            if($history_no === 0){
                $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no']); // 受付番号
            } else {
                $pdfObj->write_string(array('x' => 30, 'y' => 20, 'width' => 150, 'height' => 15, 'font_size' =>11), '受付番号：'.$recHead['seko_no'].'-'.$history_no); // 受付番号-履歴NO
            }
            if($sumArr['nebiki_flg'] <> 0){
                if($page == 1){
                    $pdfObj->write_string(array('x' =>309, 'y' => 119, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                } else {
                    $pdfObj->write_string(array('x' =>309, 'y' => 50, 'width' => 150, 'height' => 11, 'font_size' => 8), '割引額');
                }
            }
            // 軽減税率対応 凡例
            if (self::_isKeigenAppliedCtxt() ) {
                $pdfObj->write_string(array('x'=>390, 'y'=>742, 'width'=>180, 'height'=>9, 'align' => 'L', 'font_size' => 8),'税欄の「軽」は軽減税率対象、「内」は内税対象');
            }
            // 部門情報(小)
            $pdfObj->write_image(array('x' => 420, 'y' => 760, 'width' => 150, 'height' => 42, 'font_size' => 12), $bumon_logo);
            if($recHead['kaisya_disp']){ // コード名称9750：正式会社名表示
                $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), $recHead['bumon_kaisya_nm']);
                $pdfObj->write_string(array('x' =>424, 'y' => 810, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
            } else {
                $pdfObj->write_string(array('x' =>424, 'y' => 801, 'width' => 500, 'height' => 11, 'font_size' => 7), '登録番号　' . $recHead['bumon_number']);
            }
            // ページ
            $pdfObj->write_string(array('x' => 45, 'y' => 785, 'width' => 530, 'height' => 9, 'font_size' => 9, 'align' => 'C'), $page . '/' . $numPages);
        }
        return TRUE;
        
    }
    
    /**
     * 明細を出力(見積履歴書)
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @param array $recHead	ヘッダーデータ
     * @return array    明細金額を集計した配列
     */
    private function outDataDetailHistory($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei, $recHead, $recMsi, $history_no) {
        
        // -- レイアウト設定 --
        // 明細表１枚目
        static $meisai_top1 = 146;
        static $meisai_row_height1 = 23.25;
        static $meisai_row_count1 = 25;
        $set_arr1 = self::getSetArr($meisai_top1);

        // 明細表２枚目以降
        static $meisai_top2 = 77;
        static $meisai_row_height2 = 23.25;
        static $meisai_row_count2 = 28;
        $set_arr2 = self::getSetArr($meisai_top2);

        // -- 表示データ --
        $sumArr = array();
        $taxArr = array();
        $breakKey = '';
        $dai_bunrui_nm = '';
        $shohin_kbn = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_prc_all = 0;
        $all_total_ippan = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $beforeRow = null;
        $sumArr['nebiki_flg'] = 0;
        
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if($total_tokuten == 0){
                        $total_tokuten = '';
                    } else {
                        $sumArr['nebiki_flg'] ++;
                    }
                    // 合計
                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['dai_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $shohin_kbn = '';
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
            }

            if($shohin_kbn != $value['shohin_kbn']){
                $koumoku = $value['koumoku'];
            } else {
                $koumoku = '';
            }
            $shohin_kbn = $value['shohin_kbn'];

            $nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $nebiki_prc; // 請求金額
            $gojokai_nebiki_prc = '';
            if($value['gojokai_nebiki_prc'] <> 0 || $value['nebiki_prc'] <> 0){
                $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'] + $value['nebiki_prc'];
            }

            $shohin_nm = '　 ' . $value['shohin_nm'];
            $shohin_cd = '　 ' . $value['shohin_cd'];
            
            $biko1 = ''; // 備考（上）：アップグレード変更内容
            $biko2 = ''; // 備考（下）：サービス料対象、商品摘要
            if(isset($value['plan_shohin_nm'])){
                $biko1 .= $value['plan_shohin_nm'].'から変更';
            }
            if($value['hoshi_disp'] !== ''){
                $biko2 .= $value['hoshi_disp'].'　';
            }
            $biko2 .= $value['shohin_tkiyo_nm'];

            // 軽減税率対応  2019/04/30 mihara keigen 
            $value['keigen_disp'] = '';
            if (self::_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    self::$_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['keigen_disp'] .= '軽';
                }
            }
            
            $row_arr[] = array(null, $shohin_nm, $shohin_cd, $value['juchu_tnk'], $value['juchu_suryo'], $value['juchu_prc'], $gojokai_nebiki_prc, $seikyu_prc, $biko1, $biko2, $value['zei_disp'], $value['keigen_disp'], null);
            
            // 値引前消費税合計の計算用
            $all_total_ippan += $value['juchu_prc']; // 値引前合計(全体)
            if($value['zei_kbn'] == 2){ // 外税のみ
                if(isset($taxArr[$value['zei_cd']])){
                    $taxArr[$value['zei_cd']] += $value['juchu_prc'];
                } else {
                    $taxArr[$value['zei_cd']] = $value['juchu_prc'];
                }
            }

            $total_ippan += $value['juchu_prc']; // 値引前合計
            $total_tokuten += $nebiki_prc;       // 値引金額合計
            $total += $seikyu_prc;               // 値引後合計
            $total_prc_all += $seikyu_prc + $value['out_zei_prc'];  // 税込値引後合計
            $chuCount++;
            $beforeRow = $value;
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if($total_tokuten == 0){
                $total_tokuten = '';
            } else {
                $sumArr['nebiki_flg'] ++;
            }
            // 合計
            $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $total_ippan, $total_tokuten, null, null, null, null, null, $total);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $row_arr[] = array();

        // 消費税
        $tax_info = self::outZeiUchiwake2($row_arr); // 内訳
        $sumArr['tax'] = $tax_info['tax_total'];
        $total_prc_all = $tax_info['total']; // UPD 2025/05/23 消費税内訳から合計金額上書きに変更
        $out_tax_total = 0;
        if (Msi_Sys_Utils::myCount($taxArr) > 0) { // 値引前合計の計算
            foreach ($taxArr as $zei_cd => $zei) {
                $zei_info = App_ClsTaxLib::GetCalcTaxZeiCd($db, $zei_cd, $zei, 2);
                $out_tax_total += $zei_info['ZeiPrc'];
            }
        }
        $zei_nebikimae_total = $all_total_ippan + $out_tax_total;
        $total_gojokai_nebiki_prc = $total_prc_all-$zei_nebikimae_total;
        if($total_gojokai_nebiki_prc == 0){
            $total_gojokai_nebiki_prc = '';
        }
        $row_arr[] = array('【合　計】', null, null, null, null, $zei_nebikimae_total, $total_gojokai_nebiki_prc, null, null, null, null, null, null, $total_prc_all); // 合計
        
        // 会員情報
        $kaiin_total = 0;
        $kaiin_info = DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no
                , '__etc_orderby_raw'=>"T.yoto_kbn, CASE WHEN ( T.point=0 and T.riyoken=0 ) THEN 1 WHEN ( T.point>0 ) THEN 2 WHEN ( T.riyoken>0 ) THEN 3 ELSE 4 END, T.kain_no"));
        if(Msi_Sys_Utils::myCount($kaiin_info) > 0){
            $kaiin_total += self::outKaiinInfoMsi($db, $row_arr, $kaiin_info);
        }
        $sumArr['kaiin'] = $kaiin_total;
        
        // 御見積金額
        $mitsumori_prc = $total_prc_all + $kaiin_total;
        $row_arr[] = array('【御見積金額】', null, null, null, null, null, null, $mitsumori_prc, null, null, null, null, null, null);
        $sumArr['total'] = $mitsumori_prc;
        // 内金
        $uchikin = DataMapper_Pdf0113::getUchikin($db, $seko_no, self::$dataSOUGI);
        if($uchikin <> 0){
            $row_arr[] = array();
            $row_arr[] = array('【内金金額】', null, null, null, null, null, null, $uchikin, null, null, null, null, null);
            $row_arr[] = array('【内金差引後金額】', null, null, null, null, null, null, $mitsumori_prc-$uchikin, null, null, null, null, null);
        }
        
        $row_arr[] = array(null, null, null, null, null, null, null, null, null, '以下余白', null, null, null);
        
        // 明細データをページ毎に分ける
        $row_arr1 = array();
        $row_arr2 = array();
        $row_arr_other = array();
        $row_cnt = 0;
        $row2_cnt = 0;
        foreach ($row_arr AS $row_arr_val){
            $row_cnt ++;
            if($row_cnt <= $meisai_row_count1){
                $row_arr1[] = $row_arr_val;
            } else {
                $row_arr2[] = $row_arr_val;
            }
        }
        if(Msi_Sys_Utils::myCount($row_arr_other) > 0){
            $row_arr2[] = $row_arr_other;
        }
        
        // --- 明細表１枚目出力 ---
        $pdfObj->write_table($set_arr1, $meisai_row_height1, $row_arr1, $meisai_row_count1, __DIR__ . '/' . self::$sourceFileName[0]);
        // ヘッダー
        $pdfObj->write_string(array('x' => 205, 'y' => 55, 'width' => 200, 'height' => 15, 'font_size' => 21, 'align' => 'C'), '御 見 積 明 細 書');
        self::outHeaderHistory($pdfObj, $recHead, $history_no, $recMsi);
//        self::outPlanSummaryNm($pdfObj, $recHead);
        $pdfObj->write_num(array('x' => 70, 'y' => 85, 'width' => 100, 'height' => 15, 'font_size' => 12), $sumArr['total']);

        // --- 明細表２枚目以降出力 ---
        $pdfObj->write_table($set_arr2, $meisai_row_height2, $row_arr2, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileName[1]);
        
        return $sumArr;
    }
    
    
    /**
     * 明細表ヘッダー
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeader($pdfObj, $recHead)
    {
        $date_format = "Y年n月j日";
        
        // 施行情報
        $k_nm = '故　'.$recHead['k_nm'].'様　ご葬儀'; // 故人名
        $pdfObj->write_string(array('x' => 30, 'y' => 32, 'width' => 130, 'height' => 15, 'font_size' =>11), $k_nm); // 故人
        $pdfObj->write_string(array('x' => 30, 'y' => 44, 'width' => 130, 'height' => 15, 'font_size' =>11), '見積担当： '.$recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 30, 'y' => 56, 'width' => 130, 'height' => 15, 'font_size' =>11), '施行日');
        $pdfObj->write_date(array('type' => 'time', 'x' =>70, 'y' => 56, 'width' =>  130, 'height' =>  15, 'font_size' =>11), $recHead['kokubetsu_ymd'], $date_format); // 施行日
        
        // 見積書発行日(見積確定済の場合は見積確定日)
        if(isset($recHead['jichu_kakute_ymd'])){
            $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['jichu_kakute_ymd'], $date_format);
        } else {
            $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['issue_date'], $date_format);
        }
        // 契約団体
        $pdfObj->write_string(array('x'=>400, 'y'=>93, 'width'=>175, 'height'=>9, 'font_size' =>10), $recHead['keiyaku_nm']); // 契約団体1
    }
    
    /**
     * 明細表ヘッダー（法事）
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeader_houji($pdfObj, $recHead)
    {
        $date_format = "Y年n月j日";
        
        // 施行情報
        $pdfObj->write_string(array('x' => 30, 'y' => 32, 'width' => 130, 'height' => 15, 'font_size' =>11), $recHead['sekyu_nm'].'様'); // 施主
        $pdfObj->write_string(array('x' => 30, 'y' => 44, 'width' => 130, 'height' => 15, 'font_size' =>11), '見積担当： '.$recHead['mitsu_tanto']); // 見積担当
        $pdfObj->write_string(array('x' => 30, 'y' => 56, 'width' => 130, 'height' => 15, 'font_size' =>11), '施行日');
        $pdfObj->write_date(array('type' => 'time', 'x' =>70, 'y' => 56, 'width' =>  130, 'height' =>  15, 'font_size' =>11), $recHead['houyou_ymd'], $date_format); // 施行日
        
        // 見積書発行日(見積確定済の場合は見積確定日)
        if(isset($recHead['jichu_kakute_ymd'])){
            $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['jichu_kakute_ymd'], $date_format);
        } else {
            $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recHead['issue_date'], $date_format);
        }
        // 契約団体
        $pdfObj->write_string(array('x'=>400, 'y'=>93, 'width'=>175, 'height'=>9, 'font_size' =>10), $recHead['keiyaku_nm']); // 契約団体1
    }
    
    /**
     * 明細表ヘッダー(見積履歴)
     * <AUTHOR> kobayashi
     * @since 2022/08/01
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outHeaderHistory($pdfObj, $recHead, $history_no, $recMsi)
    {
        $date_format = "Y年n月j日";
        
        // 施行情報
        $pdfObj->write_string(array('x' => 30, 'y' => 32, 'width' => 130, 'height' => 15, 'font_size' =>11), $recHead['m_nm'].'　様'); // 相談者
        $pdfObj->write_string(array('x' => 30, 'y' => 44, 'width' => 130, 'height' => 15, 'font_size' =>11), '相談担当： '.$recHead['seko_tanto']); // 相談担当
        
        // 見積書発行日
        $pdfObj->write_date(array('x' => 500, 'y' => 20, 'width' => 145, 'height' => 15, 'type' => 'time', 'font_size' => 9), $recMsi[0]['juchu_ymd'], $date_format);
        
        $pdfObj->write_string(array('x' => 285, 'y' => 83, 'width' => 350, 'height' => 15, 'font_size' =>9), '※商品および販売価格は、諸般の事情により変動する場合がございます。');
        $pdfObj->write_string(array('x' => 285, 'y' => 94, 'width' => 350, 'height' => 15, 'font_size' =>9), '※有効期限：発行日より3ヶ月');
    }
    
    /**
     * 施行プラン摘要名表示
     *
     * <AUTHOR> kobayashi
     * @since 2020/02/18
     * @value $pdfObj
     * @value $recHead ヘッダーデータ
     */
    protected function outPlanSummaryNm($pdfObj, $recHead)
    {
        if(isset($recHead['plan_summary_nm'])){
            $plan_summary_strlen = mb_strlen($recHead['plan_summary_nm'], 'UTF-8');
            if($plan_summary_strlen > 34){ // 圧縮あり(1行目備考欄に2行で表示)
                $pdfObj->write_string(array('x'=>405, 'y'=>136, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 0, $plan_summary_strlen/2, 'UTF-8'));
                $pdfObj->write_string(array('x'=>405, 'y'=>144, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], $plan_summary_strlen/2, $plan_summary_strlen-1, 'UTF-8'));
            } else {
                $pdfObj->write_string(array('x'=>405, 'y'=>136, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 0, 17, 'UTF-8'));
                $pdfObj->write_string(array('x'=>405, 'y'=>144, 'width' => 152, 'height' => 25, 'font_size' => 9), mb_substr($recHead['plan_summary_nm'], 17, $plan_summary_strlen-1, 'UTF-8'));
            }
        }
    }
    
    /**
     * 明細表の表示位置取得
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/10
     * @value $meisai_top
     * @return $set_arr
     */
    protected function getSetArr($meisai_top)
    {
        $set_arr[] = array('x' => 23, 'y' => $meisai_top, 'width' => 148, 'height' => 15); // 商品名（１行）
        $set_arr[] = array('x' => 23, 'y' => $meisai_top - 5, 'width' => 148, 'height' => 15); // 商品名（２行上）
        $set_arr[] = array('x' => 23, 'y' => $meisai_top + 6, 'width' => 148, 'height' => 15, 'font_size' => 8); // 商品名（２行下）
        $set_arr[] = array('x' => 174, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 商品単価
        $set_arr[] = array('x' => 230, 'y' => $meisai_top, 'width' => 20, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 252, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 商品価格
        $set_arr[] = array('x' => 300, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 会員特典
        $set_arr[] = array('x' => 350, 'y' => $meisai_top, 'width' => 46, 'height' => 15, 'type' => 'num'); // 御利用価格
        $set_arr[] = array('x' => 401, 'y' => $meisai_top - 5, 'width' => 157, 'height' => 15); // 備考（上）
        $set_arr[] = array('x' => 401, 'y' => $meisai_top + 5, 'width' => 157, 'height' => 15); // 備考（下）
        $set_arr[] = array('x' => 558, 'y' => $meisai_top - 5, 'width' => 20, 'height' => 15, 'align' => 'C'); // 税（上）
        $set_arr[] = array('x' => 558, 'y' => $meisai_top + 5, 'width' => 20, 'height' => 15, 'align' => 'C'); // 税（下）
        $set_arr[] = array('x' => 349, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num', 'font_size' => 12); // 御利用価格(サイズ大)
        
        return $set_arr;
    }
    
    /**
     * PDFのファイル名を施行No + 葬家名 + タイトル 形式で取得する
     * 
     * <AUTHOR> Matsuyama
     * @since 2016/09/06
     * @return string   PDFのファイル名
     */
    public function getFileName($db, $seko_no, $title) {
        $file_name = $title;
        $rec = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$seko_no) );
        if (Msi_Sys_Utils::myCount($rec) > 0) {
			//葬家名の特殊文字対応
			$file_name = Msi_Sys_Utils::normFilename($seko_no.$rec['souke_nm'].$title);
        }
        return $file_name;
    }

    /**
     * 互助会消費税金額の内訳を出力する(鏡)
     *
     * <AUTHOR> kobayashi
     * @since 2022/09/12
     * @param $pdfObj
     * @param $db
     * @param $kaiin_info 会員情報
     * @param $x1 x座標調整
     * @param $y1 y座標調整
     */
    protected function outGojoZeiUchiwake($pdfObj, $db, $kaiin_info, $x1 = 0, $y1 = 0) {
        
        $pdfObj->write_string(array('x' => 323 + $x1, 'y' => 636 + $y1, 'width' => 150, 'height' => 9, 'font_size' => 8.5), '【互助会利用　消費税内訳】');
        
        $gojozei = array();
        foreach ($kaiin_info as $kaiin) {
            // 会費消費税
            if(isset($kaiin['zei_cd'])){
                $k_rtu = App_ClsTaxLib::GetTaxInfoZeiCd($db, $kaiin['zei_cd']);
                $k_prc = $kaiin['minou_gaku']; // 残金額
                $k_tax = $kaiin['kanyu_tax'] - $kaiin['wari_gaku_tax'];
                if(isset($gojozei[$k_rtu['zei_rtu']])){
                    $gojozei[$k_rtu['zei_rtu']]['prc'] += $k_prc;
                    $gojozei[$k_rtu['zei_rtu']]['tax'] += $k_tax;
                } else {
                    $gojozei[$k_rtu['zei_rtu']]['prc'] = $k_prc;
                    $gojozei[$k_rtu['zei_rtu']]['tax'] = $k_tax;
                }
            }
            // 早期利用費消費税
            if($kaiin['early_use_cost'] <> 0 || $kaiin['early_use_cost_zei'] <> 0){
                $s_rtu = App_ClsTaxLib::GetTaxInfoZeiCd($db, $kaiin['early_use_cost_zei_cd']);
                $s_prc = $kaiin['early_use_cost'];
                $s_tax = $kaiin['early_use_cost_zei'];
                if(isset($gojozei[$s_rtu['zei_rtu']])){
                    $gojozei[$s_rtu['zei_rtu']]['prc'] += $s_prc;
                    $gojozei[$s_rtu['zei_rtu']]['tax'] += $s_tax;
                } else {
                    $gojozei[$s_rtu['zei_rtu']]['prc'] = $s_prc;
                    $gojozei[$s_rtu['zei_rtu']]['tax'] = $s_tax;
                }
            }
        }
        
        $y = 645.5 + $y1;
        foreach ($gojozei as $zei_rtu => $value) {
            if($zei_rtu !== 0){
                $cap = $zei_rtu.'％税抜対象額';
                $pdfObj->write_string(array('x' => 322 + $x1, 'y' => $y, 'width' => 70, 'height' => 9, 'align' => 'R', 'font_size' => 9), $cap); // 文言
                $pdfObj->write_string(array('x' => 390 + $x1, 'y' => $y, 'width' => 65, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['prc']) . '－'); // 金額
                $pdfObj->write_string(array('x' => 456 + $x1, 'y' => $y, 'width' => 60, 'height' => 9, 'align' => 'L', 'font_size' => 9), '(会費消費税)'); // 文言
                $pdfObj->write_string(array('x' => 512 + $x1, 'y' => $y, 'width' => 55, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['tax']) . '－'); // 金額
            } else {
                $pdfObj->write_string(array('x' => 322 + $x1, 'y' => $y, 'width' => 70, 'height' => 9, 'align' => 'R', 'font_size' => 9), '非課税対象契約額'); // 文言
                $pdfObj->write_string(array('x' => 390 + $x1, 'y' => $y, 'width' => 65, 'height' => 9, 'align' => 'R', 'font_size' => 9), '￥' . number_format($value['prc']) . '－'); // 金額
            }
            $y += 10;
        }
    }
    
    /**
     * 消費税金額の内訳を出力する(鏡)
     *
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @param App_Pdf  $pdfObj
     */
    protected function outZeiUchiwake1($pdfObj, $x1 = 0, $y1 = 0)
    {
        $seko_no     = self::$_p_seko_no;
        $seko_no_sub = self::$_p_seko_no_sub;
        $data_kbn    = self::$_p_data_kbn;

        $db = Msi_Sys_DbManager::getMyDb();

        $shohizei = App_KeigenUtils::getMitsuShohizeiEasy5($db, $seko_no, $seko_no_sub, $data_kbn);

        $pdfObj->write_string(array('x'=>323 + $x1, 'y'=>636 + $y1, 'width'=>150, 'height'=>9, 'font_size' => 8.5), '【消費税内訳】');
        $y = 645.5 + $y1;
        $outzei = 0;
        foreach ($shohizei as $value) {
            $cap = $value[0];
            $prc = $value[1];
            $detail = $value[2];
            if($prc !== 0){
                if($detail['type'] === 'price'){ // 対象額
                    $pdfObj->write_string(array('x'=>322 + $x1, 'y'=>$y, 'width'=>70, 'height'=>9, 'align' => 'L', 'font_size' => 9), $cap); // 文言
                    $pdfObj->write_string(array('x'=>390 + $x1, 'y'=>$y, 'width'=>65, 'height'=>9, 'align' => 'R', 'font_size' => 9), '￥'.number_format($prc).'－'); // 金額
                    if($detail['zei_kbn'] == 0){ // 非課税
                        $y += 10;
                    }
                } else if($detail['type'] === 'tax'){ // 消費税
                    $pdfObj->write_string(array('x'=>456 + $x1, 'y'=>$y, 'width'=>60, 'height'=>9, 'align' => 'L', 'font_size' => 9), $cap); // 文言
                    $pdfObj->write_string(array('x'=>512 + $x1, 'y'=>$y, 'width'=>55, 'height'=>9, 'align' => 'R', 'font_size' => 9), '￥'.number_format($prc).'－'); // 金額
                    $y += 10;
                    if($detail['zei_kbn'] == 2){ // 外税
                        $outzei += $prc;
                    }
                }
            }
        }
        return $outzei;
    }
    
    /**
     * 消費税金額の内訳を出力する(明細)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param App_Pdf  $pdfObj
     */
    protected function outZeiUchiwake2(&$row_arr)
    {
        $denpyo_no = self::$_p_denpyo_no;

        $db = Msi_Sys_DbManager::getMyDb();

        $shohizei = App_KeigenUtils::getMitsuShohizeiEasy6($db, $denpyo_no);

        $tax_total = 0;
        $total = 0;
        foreach ($shohizei as $value) {
            if ($value[1] !== 0) {
                $row_arr[] = array(' '.$value[0], null, null, null, null, null, null, $value[1], null, null, null, null, null);
                if($value[2]['type'] === 'tax' && $value[2]['zei_kbn'] === '2'){ // 外税合計
                    $tax_total += $value[1];
                }
                if(!($value[2]['type'] === 'tax' && $value[2]['zei_kbn'] === '1')){ // 合計金額(内税消費税金額を除く)
                    $total += $value[1];
                }
            }
        }
        
        return array('tax_total' => $tax_total, 'total' => $total);
    }

    /**
     * 消費税基準日(sougi_ymd)を設定する
     * juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param $pdfObj
     * @return void
     */
    protected function _prepKijunYmdKeigen($seko_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );
        if($sougi_ymd === null){
            self::$_kijun_ymd = Msi_Sys_Utils::getDate();
        } else {
            self::$_kijun_ymd = $sougi_ymd;
        }
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @return boolean
     */
    protected function _isKeigenAppliedCtxt()
    {
        $kijun_ymd = self::$_kijun_ymd;
        if ( $kijun_ymd == null ) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ( $keigenBgnYmd <= $kijun_ymd ) {
            return true;
        }
        return false;
    }
    
    /**
     * 会員情報（明細）を出力する
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/05
     * @param array  $row_arr
     * @return $kaiin_total
     */
    protected function outKaiinInfoMsi($db, &$row_arr, $kaiin_info)
    {
        $kaiin_row = 0;
        $kaiin_total = 0;
        $break_yoto = '';
        $row_arr[] = array();
        
        // 消費税情報（葬儀日→現在日）
        foreach ($kaiin_info as $value) {
            if(isset($value['zei_cd'])){ // 消費税コード
                $tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['zei_cd']);
            }
            if(isset($value['early_use_cost_zei_cd'])){ // 早期利用費消費税コード
                $early_tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['early_use_cost_zei_cd']);
            }
            if(isset($value['meigi_chg_cost_zei_cd'])){ // 名義変更手数料消費税コード
                $meigi_tax = App_ClsTaxLib::GetTaxInfoZeiCd($db, $value['meigi_chg_cost_zei_cd']);
            }
//            if(($break_yoto !== $value['yoto_kbn']) && $value['yoto_kbn'] !== '4'){
            if($value['yoto_kbn'] !== '4'){
                $kaiin_row++;
                $row_arr[] = array('◆' . $value['yoto_nm'] . '◆', null, null, null, null, null, null, null, null, null, null, null, null);
            }
//            }
            if(($value['yoto_kbn'] === '1')){ // 1:コース施行
                $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, null, '契約金額：'.self::_dispPrice($value['keiyaku_gaku']), '　入金額：'.self::_dispPrice($value['harai_gaku']), null, null, null);
                if($value['wari_gaku'] <> 0){ // 割引額(前納割引額)
                    $ikkatu = '';
                    if($value['n_free3'] <> 0 && $value['n_free3'] !== null){ // 全額一括支払割引額
                        $ikkatu = '※内、全額一括支払割引額：'.self::_dispPrice($value['n_free3']);
                    }
                    $row_arr[] = array('　割引額', null, null, null, null, null, null, null, self::_dispPrice($value['wari_gaku']*-1), $ikkatu, null, null, null);
                }
                $row_arr[] = array('　残金額', null, null, null, null, null, null, $value['minou_gaku'], '残回数：'.$value['minou_no'], null, null, null, null);
                $kaiin_total += $value['minou_gaku'];
                if($value['kanyu_tax'] <> 0){
                    $row_arr[] = array('　会費消費税('.$tax['zei_rtu'].'％)', null, null, null, null, null, null, $value['kanyu_tax']-$value['wari_gaku_tax'], null, null, null, null, null);
                    $kaiin_total += $value['kanyu_tax']-$value['wari_gaku_tax'];
                }
                if($value['early_use_cost'] <> 0){ // 早期利用費
                    $row_arr[] = array('　早期利用費', null, null, null, null, null, null, $value['early_use_cost'], null, null, null, null, null);
                    $kaiin_total += $value['early_use_cost'];
                    if($value['early_use_cost_zei'] <> 0){
                        $row_arr[] = array('　早期利用費消費税('.$early_tax['zei_rtu'].'％)', null, null, null, null, null, null, $value['early_use_cost_zei'], null, null, null, null, null);
                        $kaiin_total += $value['early_use_cost_zei'];
                    }
                }
                if($value['warimashi_gaku'] <> 0){ //  割増金額
                    $row_arr[] = array('　割増金額', null, null, null, null, null, null, null, self::_dispPrice($value['warimashi_gaku']*-1), null, null, null, null);
                }
            } else if($value['yoto_kbn'] === '3'){ // 3:完納充当
                if($value['point']  == 0 && $value['riyoken']  == 0){ // ポイントが0かつ利用券が0
                    $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, $value['keiyaku_gaku']*-1, '契約金額：'.self::_dispPrice($value['keiyaku_gaku']), null, null, null, null);
                    $kaiin_total += $value['keiyaku_gaku']*-1;
                    if($value['wari_gaku'] <> 0){ // 割引額(前納割引額)
                        $row_arr[] = array('　割引額', null, null, null, null, null, null, null, self::_dispPrice($value['wari_gaku']*-1), null, null, null, null);
                    }
                    $row_arr[] = array('　残金額', null, null, null, null, null, null, $value['minou_gaku'], '残回数：'.$value['minou_no'], null, null, null, null);
                    $kaiin_total += $value['minou_gaku'];
                } else if($value['point'] > 0){ // ポイント
                    $row_arr[] = array(null, ' '.$value['kain_no'], ' '.str_replace('-', '/', $value['kanyu_dt']).' '.$value['kanyu_nm'].' 様', null, null, null, null, $value['point']*-1, '契約金額：'.self::_dispPrice($value['point']*-1), null, null, null, null);
                    $kaiin_total += $value['point']*-1;
                } else if($value['riyoken'] > 0){ // 利用券
                    $row_arr[] = array(null, null, null, null, null, null, null, $value['riyoken']*-1, '施設利用券', null, null, null, null);
                    $kaiin_total += $value['riyoken']*-1;
                } 
                
            }
            $break_yoto = $value['yoto_kbn'];
        }
        if($kaiin_row > 0){
            $row_arr[] = array('【互助会利用合計】', null, null, null, null, null, null, $kaiin_total, null, null, null, null, null);
        }
        $row_arr[] = array();
        
        return $kaiin_total;
    }
    
    /**
     * 備考に表示用の金額変換
     *
     * <AUTHOR> Kobayashi
     * @return $disp_prc 変換後金額
     */
    protected function _dispPrice($prc)
    {
        $disp_prc = str_replace('-', '△', number_format($prc));
        return $disp_prc;
    }

}
