var apps=apps||{};
$(function(){apps.pro={s_oya_bumon:"#s_oya_bumon",last_out_date:"#last_out_date",furikomi_date:"#furikomi_date"};var d=Backbone.Model.extend({defaults:function(){return{s_oya_bumon:null,last_out_date:null,furikomi_date:null}},validation:{s_oya_bumon:{required:!0},furikomi_date:{required:!0,customFun:function(a){return $.msiJqlib.chkYmd(a)}}},labels:{s_oya_bumon:"\u4f1a\u793e",furikomi_date:"\u632f\u8fbc\u6307\u5b9a\u65e5"}}),e=Backbone.View.extend({el:"#my-form-id",events:{"click #btn_save":"doOutPut"},bindings:{"#s_oya_bumon":$.msiJqlib.getSelect2Binding("s_oya_bumon"),
"#furikomi_date":"furikomi_date","#last_out_date":"last_out_date"},initialize:function(){Backbone.Validation.bind(this,Backbone.Validation.msi_v_iv_callback(apps.pro,"error1"));this.render()},setSelect2:function(){$.msiJqlib.setSelect2Com1(this.$("#s_oya_bumon"),$.extend({data:c.oya_bumon},$.msiJqlib.setSelect2Default2))},render:function(){this.setSelect2();this.stickit();return this},doOutPut:function(){$.msiJqlib.clearAlert();this.isInputOk()&&msiLib2.fileDlAjax({url:$.msiJqlib.baseUrl()+"/juchu/hikitorifbout/output",
data:{dataAppJson:JSON.stringify(apps.appModel.toJSON())}},function(a){if("OK"==a.status){if(a.opt&&a.opt.outMsg){var b=a.opt.outMsg;a.opt.isOutMsgErr?$.msiJqlib.showWarn2(b):$.msiJqlib.showInfo2(b);apps.appModel.set("last_out_date",a.opt.last_out_date)}}else $.msiJqlib.showErr2(a.msg)})},isInputOk:function(){var a=[],b=apps.appModel.validate();b&&_.each(b,function(b,c){a.push(b)});return 0<a.length?($.msiJqlib.showErr(a.join(", ")),!1):!0}}),c=msiLib2.getJsonFromHtml($("#my-data-init-id"));apps.appModel=
new d;apps.appView=new e({model:apps.appModel});apps.appModel.set(c.dataApp)});
