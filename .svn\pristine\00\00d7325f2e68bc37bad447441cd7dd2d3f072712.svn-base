// お礼状

var appthanks = appthanks || {};

$(function() {
    "use strict";

    // 画面クラスとモデルのプロパティのオブジェクト
    appthanks.pro = {
        // 写真加工
        ts_free1: '#contact_date, #contact_time',
        ts_free1_date: '#contact_date',
        ts_free1_time: '#contact_time',
//        teikei_cnt: '#count_1', // 定型文枚数
//        ori_cnt: '#count_2', // オリジナル
//        huto_cnt: '#count_3', // 封筒
        i_free1: '#count_4', // なまえ文枚数
        shio_ari_cnt: '#count_5', // お清め塩（あり）
        sz_ymd: '#thanks-org-tab #contact_date', // 電話取材の希望時間
        sz_tel: '#thanks-org-tab #contact_tel', // TEL
        sz_fax: '#thanks-org-tab #contact_fax' // FAX
    };
    // 礼状構成料select2内容
    var _cstr_fee_kbn = [
        {id: '1', text: 'あり'},
        {id: '0', text: 'なし'}
    ];
    // 封筒単価select2内容
    var _futo_tnk_kbn = [
        {id: '50', text: '50円'},
        {id: '60', text: '60円'}
    ];
    /**
     * 日付チェック処理
     * @param {string} value 日付
     */
    var _chkYmd = function(value) {
        if (!$.msiJqlib.isNullEx2(value) && !$.msiJqlib.chkDate(value)) {
            return Backbone.Validation.messages.ymd;
        }
    };

    /**
     * テキストエリアの行数制限
     * @param {string} value 備考
     */
    var _lineLimit = function(value) {
        if (value == null || value.length == 0) {
            return;
        }

        var line = value.split("\n");
        if (line.length > 4) {
            return '備考は4行以内として下さい。';
        }
    };
    
    /**
     * オリジナル礼状受注数判定
     * @param {string} value 礼状種別
     */
    var _Orignal = function(value) {
        switch(value){
            case 0:
				return '会葬礼状が受注されていません。';
            case 1:
            case 3:
				return null;
            case 4:
                return '※校正代の数量に誤りがあります。商品選択画面から確認ください。';
        }
    };

    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function(niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };

    // 区分値コード数値設定処理
    var _setKbnCdVal = function($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_cd_num);
        }
    };

    // 画像ヘルパー処理 
    var _gazoHelper = function(t, hookData, gazo) {
        var m = t.model;
        t.$el.msiPickHelper({
            action: 'gazo',
            onSelect: function(data) {
                if (data.code) {
                    m.set(gazo.oid, data.gazo_img);
                    m.set(gazo.code, data.code);
                    m.set(gazo.name, data.gazo_nm);
                } else {
                    m.set(gazo.oid, null);
                    m.set(gazo.code, null);
                    m.set(gazo.name, null);
                }
            },
            onClear: function() {
                m.set(gazo.oid, null);
                m.set(gazo.code, null);
                m.set(gazo.name, null);
            },
            hookSetData: function() {
                return hookData;
            }
        });
    };

    // 全体モデル
    var AppModel = Backbone.Model.extend({});
    var AppView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click .tab li a": "changeTab",
            "click #btn_save": "doSave",
            "click #btn_reload": "doReload",
            "click #btn_print": "doPrint",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "click .label.dlg_hall_name": "hallHelper"
        },
        // タブ切り替え処理
        changeTab: function(e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            this.setButtonsStatus();
        },
        isInputOk: function() {
            var aMsg = [];
            // モデルチェック
            var result = appthanks.oreiModel.validate();
            if (result) {
                _.each(result, function(v, k) {
                    aMsg.push(v);
                });
            }

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#thanks-std-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#thanks-org-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#thanks-env-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            // OK 
            $.msiJqlib.clearAlert();
            return true;
        },
        doSave: function(e, options) {
            $.msiJqlib.clearAlert();
            if (!this.isInputOk()) {
                return;
            }
            if (this.checkChange()) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            var that = this;
            var m = appthanks.oreiModel;
            var o = appthanks.orgDataOrei;
            var dpCreFlg = false;
            // 受注伝票自動作成判定 定型文枚数 オリジナル枚数　なまえ文礼状　サイズのいずれが変更されたらTRUE
            if (o.teikei_cnt !== m.get('teikei_cnt') || o.ori_cnt !== m.get('ori_cnt') || o.ori_cnt !== m.get('i_free1') || o.hagaki_size !== m.get('hagaki_size') || o.cstr_fee_kbn !== appinvthx.prevModel.get('cstr_fee_kbn')
                    || o.huto_cnt !== m.get('free_kbn1') || o.huto_cnt !== m.get('free_kbn1')) {
                dpCreFlg = true;
            }

            var dataOreiJson = JSON.stringify(m.toJSON());
            var dataOrei2Json = JSON.stringify(appinvthx.prevModel.toJSON());
            var dataTeikeiJson = JSON.stringify(appinvthx.teikeiCol.toJSON());

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/thankssave',
                data: {
                    dataOrei: dataOreiJson,
                    dataOrei2: dataOrei2Json,
                    dataTeikei: dataTeikeiJson,
                    dpCreFlg: dpCreFlg
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        appthanks.resetData(mydata);
                        appthanks.isResetNew = false;
                        if (!$.msiJqlib.isNullEx2(options) && _.has(options, "exePrint")) {
                            options.exePrint.apply(that);
                        } else {
                            $.msiJqlib.showInfo(mydata.msg);
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
		// 再読込
        doReload: function(e, options) {
            $.msiJqlib.clearAlert();
            var that = this;
            var m = appthanks.oreiModel;
            var o = appthanks.orgDataOrei;
            var dataOreiJson = JSON.stringify(m.toJSON());
            var dataOrei2Json = JSON.stringify(appinvthx.prevModel.toJSON());
            var dataTeikeiJson = JSON.stringify(appinvthx.teikeiCol.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/thanksreload',
                data: {
                    dataOrei: dataOreiJson,
                    dataOrei2: dataOrei2Json,
                    dataTeikei: dataTeikeiJson,
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        appthanks.resetData(mydata);
                        appthanks.isResetNew = true;
						m.set('update','update');
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doPrint: function(e) {
            this.exePrint();
        },
        exePrint: function() {
            if (!this.isInputOk()) {
                return;
            }
            var sekoNo = appthanks.data.kihon.seko_no;
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            var hachuCd = appthanks.oreiView.model.get('hd_biko2');
            var hagakiSize = appthanks.oreiView.model.get('hagaki_size');
            if ($.msiJqlib.isNullEx2(hagakiSize)) {
                return;
            }
            var printData = appinvthx.getPrintData();
            var tabIndex = this.getTabIndex();
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0131',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    hachusk_cd: hachuCd,
                    hagaki_size: hagakiSize,
                    tab_index: tabIndex,
                    previewInfo: printData,
                    other_nm: $("#other_nm")[0].value
                }
            });
        },
        doDelete: function() {
            var tabIndex = this.getTabIndex();
            var lblPrint = this.$('.tab li').eq(tabIndex).text();
            if (tabIndex !== 0 && $.msiJqlib.isNullEx2(lblPrint)) {
                return;
            }
            if (!confirm('「' + lblPrint + '」を削除します。よろしいですか？')) {
                return;
            }
            var dataOreiJson = JSON.stringify(appinvthx.prevModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/thanksdelete',
                data: {
                    dataOrei: dataOreiJson
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        // バックアップデータクリア
                        appinvthx.deleteBakData();
                        appthanks.resetData(mydata);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        // 変更されているかチェックする　TRUE:変更なし
        checkChange: function() {
            // お礼状データイコール
            var appEq = $.msiJqlib.isEqual(appthanks.oreiModel.toJSON(), appthanks.orgDataOrei);
            // 定型文イコール
            var teikeiEq = appinvthx.isEqual();
            return (appEq && teikeiEq && !appthanks.isResetNew);
        },
        doCancel: function() {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        getTabIndex: function() {
            var $tab = this.$('.tab-contents');
            var index = $tab.index($tab.filter(':visible'));
            return index;
        },
        // 保存ボタンと削除ボタンの活性・非活性を設定する
        setButtonsStatus: function() {
            var $btnDel = this.$('#btn_delete');
            var $btnReload = this.$('#btn_reload');
            var idx = this.getTabIndex();
            if (idx <= 0) {
                $btnDel.show();
                $btnReload.hide();
                if (appthanks.data.dataTeikeiTrn.length > 0) {
                    $.msiJqlib.setBtnEnable($btnDel);
                } else {
                    $.msiJqlib.setBtnDisable($btnDel);
                }
            } else {
                $btnDel.hide();
                $btnReload.show();
                if (appthanks.data.dataTeikeiTrn.length > 0) {
                    $.msiJqlib.setBtnEnable($btnReload);
                } else {
                    $.msiJqlib.setBtnDisable($btnReload);
                }
            }
        },
        seikyuHelper: function(event) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyo',
                onSelect: function(data) {
                    m.set("hall_name", data.name);
                },
                onClear: function() {
                    m.set("hall_name", null);
                }
            });
        },
        torikomiHelper: function(event) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyo',
                onSelect: function(data) {
                    m.set("hall_name", data.name);
                },
                onClear: function() {
                    m.set("hall_name", null);
                }
            });
        },
        shuturyokuHelper: function(event) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyo',
                onSelect: function(data) {
                    m.set("hall_name", data.name);
                },
                onClear: function() {
                    m.set("hall_name", null);
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function(e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
        hallHelper: function(e) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyo',
                onSelect: function(data) {
                    var $target = $(e.currentTarget).prev("input");
                    $target[0].value = data.name;
                    $("#" + $target[0].id).change();
                },
                onClear: function() {
                    m.set("hall_name", null);
                }
            });
        }
    });

    // お礼状モデル
    var OreiModel = Backbone.Model.extend({
        defaults: function() {
            return {
                // 施行お礼状情報
                seko_no: null, // 施行番号
                oshirase_cd: null, // お礼状文言コード db項目はorei_cdですが、お知らせ状、お礼状の共通ライブラリで使用するため、名前を変更している
                teikei_cnt: null, // 定型文枚数
                ori_cnt: null, // オリジナル枚数
                huto_cnt: null, // 封筒枚数
                i_free1: null, // なまえ文礼状
                free_kbn1: 60, // 封筒単価区分
                shio_ari_cnt: null, // お清め塩（あり）
                hagaki_size: '0', // はがき大きさ 0：シングル 1：ダブル
				teikei_cd: null,
                tk_add: null, // 定型追記文
                tk_date: null, // 追記日付
                tk_address: null, // 追記場所
                tk_address2: null, // 追記場所2
                tk_end: null, // 追記文末
				tk_biko: null, // 追記備考
                sz_kbn: null, // 喪主に同じ
                sz_nm: null, // 取材遺族名
                sz_zoku_code_kbn: null, // 続柄コード 区分
                sz_zoku_cd: null, // 続柄コード
                sz_zoku_kbn: null, // 続柄
                chk_bumon_cd: null, // 確認先部門コード
                sz_ymd: null, // 取材希望日
                sz_st_kbn1: null, // 取材希望時間区分１
                sz_st_kbn2: null, // 取材希望時間区分２
                sz_tel: null, // 取材電話番号
                sz_fax: null, // 取材FAX番号
                hd_biko1: null, // 備考１
                hd_biko2: null, //発注先コード
                hachu_lnm: null, //発注先名
                huto_kbn: null, // 封筒表書
                huto_kbn_nm: null, // 封筒表書名
                kamon_oid: null, // 家紋OID
                kamon_code: null, // 家紋コード
                kamon_nm: null, // 家紋名
                // オリジナル親族情報 属性省略

                cstr_fee_kbn: null, // 構成料の有無
                pattern_code: null, //絵柄コード
                pattern_nm: null, //絵柄名称
                pattern_oid: null, //絵柄OID
                // 参照専用
                syushi_nm: null, // 宗旨名
                syuha_nm: null, // 宗派名
                souke_nm: null, // 葬家

                // みづまオリジナル礼状用追加項目
                sz_knm: null, // 取材遺族名カナ
                sz_mail: null, // メールアドレス
                free_kbn2: null, // 申込商品
                free_kbn3: null, // 利用台紙
                free_kbn4: null, // ヒアリング
                free_kbn5: null, // 確認者
                v_free1: null, // サイズ
                v_free2: null, // 請求先
                v_free3: null, // 取込先
                v_free4: null, // 出力先
                v_free5: null, // 続柄
                f_name1: null, // 注意する漢字１
                f_name2: null, // 注意する漢字２
                f_name3: null, // 注意する漢字３
                ts_free1: null, // ヒアリング希望時間(日付のみ)
                ts_free1_date: null, // ヒアリング希望時間(日付のみ)
                ts_free1_time: null, // ヒアリング希望時間(時刻のみ)
				other_nm: null
            };
        },
        validation: {
            syubetu: {
                required: false,
                customFun: function(value) {
                    return _Orignal(value);
                }
            },
            ts_free1: {
                required: false,
                pattern: 'timestamp',
                msg: '日付と時刻の整合性がありません'
            },
            ts_free1_date: {
                required: false,
                customFun: function(value) {
                    return _chkYmd(value);
                }
            },
            ts_free1_time: {
                required: false,
                pattern: 'time'
            },
            teikei_cnt: {
                required: false,
                pattern: 'number'
            },
            ori_cnt: {
                required: false,
                pattern: 'number'
            },
            huto_cnt: {
                required: false,
                pattern: 'number'
            },
            i_free1: {
                required: false,
                pattern: 'number'
            },
            sz_nm: {
                required: false,
                maxLength: 20
            },
            sz_knm: {
                required: false,
                maxLength: 30
            },
            sz_ymd: function(value) {
                return _chkYmd(value);
            },
            sz_tel: {
                required: false,
                pattern: 'tel'
            },
            sz_fax: {
                required: false,
                pattern: 'tel'
            },
            hd_biko1: {
                required: false,
                customFun: function(value) {
                    return _lineLimit(value);
                }
            },
            sz_mail :{
                required: false,
                pattern: 'email',
                msg: 'メールアドレスの形式エラーです'
            }
        },
        labels: {
            ts_free1: 'ヒアリング希望日',
            ts_free1_date: 'ヒアリング希望日',
            ts_free1_time: 'ヒアリング希望時間',
            teikei_cnt: '定型文枚数',
            ori_cnt: 'オリジナル',
            huto_cnt: '封筒',
            i_free1: 'なまえ文礼状',
            sz_nm: 'お話を伺う方',
            sz_knm: 'お話を伺う方(フリガナ)',
            sz_ymd: '電話取材の希望時間',
            sz_tel: 'TEL',
            sz_fax: 'FAX'
        }
    });

    // お礼状ビュー
    var OreiView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click #lbl_dlg_crest": "kamonHelper",
            "click #lbl_dlg_pattern": "patternHelper",
            "click .hachu, .dlg_hachu" : "hachuSaki",
            "click #count_5": function() {
                // 塩 1：あり
                var checked = $("#count_5").attr("checked");
                if(!$.msiJqlib.isNullEx2(checked)){
                    // チェック有り→無し
                    this.model.set("shio_ari_cnt", null);
                    this.$("#count_5").removeAttr("checked").button("refresh");  
                }else{
                    // チェック無し→有り、変更
                    this.model.set("shio_ari_cnt", "1");
                    $("#count_5").attr("checked", "checked").button("refresh");
                    $("#count_6").removeAttr("checked").button("refresh");
                }
            },
            "click #count_6": function() {
                // 塩 2：なし
                var checked = $("#count_6").attr("checked");
                if(!$.msiJqlib.isNullEx2(checked)){
                    // チェック有り→無し
                    this.model.set("shio_ari_cnt", null);
                    $("#count_6").removeAttr("checked").button("refresh");
                }else{
                    // チェック無し→有り、変更
                    this.model.set("shio_ari_cnt", "2");
                    $("#count_6").attr("checked", "checked").button("refresh");
                    $("#count_5").removeAttr("checked").button("refresh");
                    
                }
            },
            "click #card_single": function() {
                // はがき大きさ 0：礼状カード
                this.model.set("hagaki_size", "0");
            },
            "click #card_double": function() {
                // はがき大きさ 1：2つ折りカード
                this.model.set("hagaki_size", "1");
            },
            "click #card_double2": function() {
                // はがき大きさ 1：2つ折り礼状
                this.model.set("hagaki_size", "2");
            },
            "click #hearing_info #as_chief": function(e) {
                // 取材するための連絡先喪主に同じクリック処理
                if ($(e.currentTarget).prop('checked')) {
                    this.model.set("sz_kbn", "1");
                    this.model.set("sz_nm", this.model.get("m_nm"));
                    this.model.set("sz_knm", this.model.get("m_knm"));
                    this.model.set("v_free5", this.model.get("m_zoku_nm"));
                    this.model.set("sz_tel", this.model.get("m_tel"));
                    this.model.set("sz_mail", this.model.get("m_mail"));
                    this.$("#thanks-org-tab #contact_name, #thanks-org-tab #contact_name_kana, #thanks-org-tab #contact_relationship_mn, #thanks-org-tab #contact_tel, #thanks-org-tab").attr("disabled", "disabled");
                } else {
                    this.model.set("sz_kbn", "0");
                    this.$("#thanks-org-tab #contact_name, #thanks-org-tab #contact_name_kana, #thanks-org-tab #contact_relationship_mn, #thanks-org-tab #contact_tel, #thanks-org-tab").removeAttr("disabled");
                }
            },
            "click #envelope_title_1": function() {
                // 封筒表書区分 0：御会葬御礼
                this.model.set("huto_kbn", "0");
                this.model.set("huto_kbn_nm", "御会葬御礼");
                appinvthx.prevModel.set("huto_kbn", "0");
                appinvthx.prevModel.set("huto_kbn_nm", "御会葬御礼");
            },
            "click #envelope_title_2": function() {
                // 封筒表書区分 1：御礼
                this.model.set("huto_kbn", "1");
                this.model.set("huto_kbn_nm", "御礼");
                appinvthx.prevModel.set("huto_kbn", "1");
                appinvthx.prevModel.set("huto_kbn_nm", "御礼");
            }
        },
        bindings: {
            '#hachu': 'hachu_lnm', //発注先名
            '#syubetu': { // 礼状種別
                observe: 'syubetu',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#reijyo_count': 'reijyo_count', // 礼状枚数
//            '#count_1': 'teikei_cnt', // 定型文枚数
//            '#count_2': 'ori_cnt', // オリジナル枚数
//            '#count_3': 'huto_cnt', // 封筒枚数
//            '#count_4': 'i_free1', // なまえ文枚数
            '#thanks #huto_tnk': {// 封筒単価区分
                observe: 'free_kbn1',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#count_5': $.msiJqlib.getCheckBinding('shio_ari_cnt'), // お清め塩（あり）
			'#teikei_cd': {
                observe: 'teikei_cd',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            //　取材をするための連絡先
            '#thanks-org-tab #hearing_info #contact_name': 'sz_nm', // 取材遺族名
            '#thanks-org-tab #contact #contact_relationship': {// 続柄コード
                observe: 'sz_zoku_cd',
                getVal: function($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'sz_zoku_kbn');
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #confirm_bumon_cd': {// 確認先
                observe: 'chk_bumon_cd',
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #contact #contact_date': 'sz_ymd', // 取材希望日
            '#thanks-org-tab #contact #contact_hour': 'sz_st_kbn1', // 取材希望時間区分１
            '#thanks-org-tab #contact #contact_minute': 'sz_st_kbn2', // 取材希望時間区分２
            '#thanks-org-tab #hearing_info #contact_tel': 'sz_tel', // 取材電話番号
            '#thanks-org-tab #misc #contact_fax': 'sz_fax', // 取材FAX番号
            // その他
            '#thanks-org-tab #misc #company_name': 'k_knm_nm', // その他企業・団体名
            '#thanks-org-tab #misc #religion_name': 'syushi_nm', // 宗旨名
            '#thanks-org-tab #misc #denomination_name': 'syuha_nm', // 宗派名
            '#thanks-org-tab #misc #crest_name': 'kamon_nm', // 家紋名
            // 備考
            '#thanks-org-tab #misc #memo': 'hd_biko1',
            // 封筒　
            '#thanks-env-tab #envelope_family_name': 'souke_nm', // 葬家
            '#thanks-env-tab #preview #env_family_name': 'souke_nm', // プレビュー葬家
            '#thanks-env-tab #preview #env_title': 'huto_kbn_nm', // 封筒区分名
            '#thanks-env-tab #cloth_crest_name': 'kamon_nm', // 家紋名
            '#thanks-env-tab #pattern_name': 'pattern_nm', // 絵柄名称

            // オリジナル礼状（みづま）
            '#thanks-org-tab #hearing_info #contact_name_kana': 'sz_knm', // 取材遺族名カナ
            '#thanks-org-tab #misc #contact_mail': 'sz_mail', // メールアドレス
            '#thanks-org-tab #basic_info #ap_shohin': {// 申込商品
                observe: 'free_kbn2',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #basic_info #ap_template': {// 利用台紙
                observe: 'free_kbn3',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #hearing_info #hearing_type': {// ヒアリング
                observe: 'free_kbn4',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #misc #contact_from': {// 確認者
                observe: 'free_kbn5',
                getVal: function($el, event, options) {
                    return $el.val();
                },
                afterUpdate: function($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#thanks-org-tab #basic_info #template_size': 'v_free1', // サイズ
            '#thanks-org-tab #basic_info #seikyu': 'v_free2', // 請求先
            '#thanks-org-tab #basic_info #torikomi': 'v_free3', // 取込先
            '#thanks-org-tab #basic_info #shuturyoku': 'v_free4', // 出力先
            '#thanks-org-tab #hearing_info #contact_relationship_mn': 'v_free5', // 続柄
            '#thanks-org-tab #char_notice #char_1': 'f_name1', // 注意する漢字１
            '#thanks-org-tab #char_notice #char_2': 'f_name2', // 注意する漢字２
            '#thanks-org-tab #char_notice #char_3': 'f_name3', // 注意する漢字３
            '#thanks-org-tab #hearing_info #contact_date': {
                observe: 'ts_free1_date',
                getVal: function($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free1_time'), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#thanks-org-tab #hearing_info #contact_time': {
                observe: 'ts_free1_time',
                getVal: function($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free1_date'), $el.val(), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#thanks-org-tab #misc #other_nm': 'other_nm' // 外
        },
        initialize: function() {
            this.listenTo(this.model, 'change:teikei_cd change:hagaki_size', this.changeTeikeiTxt);
            this.listenTo(this.model, 'change:kamon_oid', this.setGazo);
            this.listenTo(this.model, 'change:pattern_oid', this.setGazo2);
            this.$("#contact_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#contact_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appthanks.pro, "error1"));
            this.render();
        },
        changeTeikeiTxt: function() {
            this.$("#samples .sample").remove();
            appinvthx.resetData(appthanks.data, appthanks.oreiModel, 0, "1");
        },
        render: function() {
            this.stickit();
            return this;
        },
        // 家紋ヘルパー処理 
        kamonHelper: function() {
            _gazoHelper(this, {s_gazo_kbn: 1, limit: 20}, {oid: 'kamon_oid', code: 'kamon_code', name: 'kamon_nm'});
        },
        // 絵柄ヘルパー処理 
        patternHelper: function() {
            _gazoHelper(this, {s_gazo_kbn: 6, limit: 20}, {oid: 'pattern_oid', code: 'pattern_code', name: 'pattern_nm'});
        },
        // 発注先検索
        hachuSaki: function(e) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'siire',
                mydata : {init_search: 1},
                onSelect: function(data) {
                    m.set("hd_biko2", data.code);
                    m.set("hachu_lnm",data.name);
                },
                onClear: function() {
                    m.set("hd_biko2", "");
                    m.set("hachu_lnm", "");
                }
            });
        },
        setGazo: function(m, val) {
            var c = m.changed;
            var el;
            if (_.has(c, 'kamon_oid')) {
                el = "#crest_pic_cloth";
            } else {
                return;
            }
            this.addGazo(this.$(el), val);
        },
        setGazo2: function(m, val) {
            var c = m.changed;
            var el;
            if (_.has(c, 'pattern_oid')) {
                el = "#pic_pattern";
            } else {
                return;
            }
            this.addGazo(this.$(el), val);
        },
        addGazo: function($el, val) {
            $el.find('img').remove();
            if (!$.msiJqlib.isNullEx2(val)) {
                $('<img>').appendTo($el).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + val);
            }
        }
    });

    // オリジナルの親族ビュー
    var OriSinzokuView = Backbone.View.extend({
        el: $("#thanks-org-tab"),
        events: {
        },
        bindings: {
            '#mourner_name': 'm_nm', // 喪主名
            '#family #relationship_1': {
                observe: 'zoku_cd1',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                },
                getVal: function($el) {
                    // 区分値コード数値と区分名設定処理
                    appinvthx.setKbnNmVal($el, this.model, 'zoku_kbn1', 'zoku_nm1');
                    return $el.val();
                }
            }, //  親族続柄コード1
            '#family #family_name_1': 'kankei_nm1', // 親族名氏名1
            '#family #relationship_2': {
                observe: 'zoku_cd2',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                },
                getVal: function($el) {
                    // 区分値コード数値と区分名設定処理
                    appinvthx.setKbnNmVal($el, this.model, 'zoku_kbn2', 'zoku_nm2');
                    return $el.val();
                }
            }, //  親族続柄コード2
            '#family #family_name_2': 'kankei_nm2', // 親族名氏名2
            '#family #relationship_3': {
                observe: 'zoku_cd3',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                },
                getVal: function($el) {
                    // 区分値コード数値と区分名設定処理
                    appinvthx.setKbnNmVal($el, this.model, 'zoku_kbn3', 'zoku_nm3');
                    return $el.val();
                }
            }, //  親族続柄コード3
            '#family #family_name_3': 'kankei_nm3', // 親族名氏名3
            '#family #relationship_4': {
                observe: 'zoku_cd4',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                },
                getVal: function($el) {
                    // 区分値コード数値と区分名設定処理
                    appinvthx.setKbnNmVal($el, this.model, 'zoku_kbn4', 'zoku_nm4');
                    return $el.val();
                }
            }, //  親族続柄コード4
            '#family #family_name_4': 'kankei_nm4', // 親族名氏名5
            '#family #relationship_5': {
                observe: 'zoku_cd5',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                },
                getVal: function($el) {
                    // 区分値コード数値と区分名設定処理
                    appinvthx.setKbnNmVal($el, this.model, 'zoku_kbn5', 'zoku_nm5');
                    return $el.val();
                }
            }, //  親族続柄コード5
            '#family #family_name_5': 'kankei_nm5', // 親族名氏名5
            '#family #cstr_fee_kbn': {
                observe: 'cstr_fee_kbn',
                afterUpdate: function($el) {
                    appinvthx.setSelect2Val($el, $el.val());
                }
            }, //  礼状構成料
            '#misc #other_1': 'k_etc_nm', // 親族名関係者外名称
            '#misc #other_2': 'k_knm_nm' // 関係者会社名

        },
        initialize: function() {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appthanks.pro, "error1"));
            this.render();
        },
        render: function() {
            this.stickit();
            return this;
        }
    });

    // データ取得
    var data = msiLib2.getJsonFromHtml($('#data-thanks'));

    // APP初期化処理
    appthanks.oreiModel = new OreiModel();
    appthanks.oreiView = new OreiView({model: appthanks.oreiModel});
    appthanks.appModel = new AppModel();
    appthanks.appView = new AppView({model: appthanks.appModel});

    // サイドメニュー
    $.msiSideMenuLib.setSideMenu({showFooter: true});

    // データ設定処理
    appthanks.resetData = function(data) {
        // リセットされたらTRUE
        appthanks.isResetNew = true;
        appthanks.data = data;
        appthanks.appView.setButtonsStatus();
        if (data) {
            // お礼状ビューを設定する
            var orei = _.extend({}, data.kihon, data.oreiData, data.kamon);
            appthanks.oreiModel.set(orei);
            // 定型文ビューを設定する
            $("#samples .sample").remove();
            appinvthx.resetData(data, appthanks.oreiModel, 0, "1");
            // オリジナル親族ビュー
            appthanks.oriSinzokuView = new OriSinzokuView({model: appinvthx.prevModel});
            var m = appthanks.oreiModel;
            // はがき大きさを設定
            if (m.get('hagaki_size') === "0") {
                $('#card_single').click();
            } else if (m.get('hagaki_size') === "1") {
                $('#card_double').click();
            } else if (m.get('hagaki_size') === "2") {
                $('#card_double2').click();
            }
            // 喪主に同じを設定
            var $asChief = $('#hearing_info #as_chief');
            if (m.get('sz_kbn') === "1" && !$asChief.prop('checked')) {
                $asChief.click();
            } else {
				if (m.get('sz_kbn') === "0" && $asChief.prop('checked')) {
					$asChief.click();
				}
			}
            // 封筒表書を設定
            if (m.get('huto_kbn') === "1") {
                $('#thanks-env-tab #envelope_title_2').click();
            } else {
                $('#thanks-env-tab #envelope_title_1').click();
            }
            // お清め塩  1：あり 2：なし
            var val = m.get('shio_ari_cnt');
            if(val === '1'){
                $("#detail #counts_2 #count_5").attr("checked", "checked").button("refresh");
            } else if(val === '2'){
                $("#detail #counts_2 #count_6").attr("checked", "checked").button("refresh");
            }
            
            // 礼状区分
            $.msiJqlib.setSelect2Com1($("#counts_1 #syubetu"), ($.extend({data: $.msiJqlib.objToArray3(data.reijyoKbn)}, $.msiJqlib.setSelect2Default1)));
			// 礼状コード
			$.msiJqlib.setSelect2Com1($("#samples #teikei_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.OshiraseCd)}, $.msiJqlib.setSelect2Default1)));
            // 封筒単価select2
            $.msiJqlib.setSelect2Com1($("#thanks #huto_tnk"), ($.extend({data: _futo_tnk_kbn}, {})));
            // 申込商品
            $.msiJqlib.setSelect2Com1($("#thanks-org-tab #ap_shohin"), ($.extend({data: $.msiJqlib.objToArray3(data.apshohinkbn)}, $.msiJqlib.setSelect2Default1)));
            // 利用台紙
            $.msiJqlib.setSelect2Com1($("#thanks-org-tab #ap_template"), ($.extend({data: $.msiJqlib.objToArray3(data.templatekbn)}, $.msiJqlib.setSelect2Default1)));
            // ヒアリング
            $.msiJqlib.setSelect2Com1($("#thanks-org-tab #hearing_type"), ($.extend({data: $.msiJqlib.objToArray3(data.hearingkbn)}, $.msiJqlib.setSelect2Default1)));
            // 原稿確認
            $.msiJqlib.setSelect2Com1($("#thanks-org-tab #contact_from"), ($.extend({data: $.msiJqlib.objToArray3(data.confirmationkbn)}, $.msiJqlib.setSelect2Default1)));
            // オリジナル電話取材の希望時間
            $("#contact #contact_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
//            $.msiJqlib.setSelect2Com1($("#contact #contact_hour"), ($.extend({data: $.msiJqlib.objToArray3(data.hourKbn)}, $.msiJqlib.setSelect2Default1)));
//            $.msiJqlib.setSelect2Com1($("#contact #contact_minute"), ($.extend({data: $.msiJqlib.objToArray3(data.minuteKbn)}, $.msiJqlib.setSelect2Default1)));
			if ($.msiJqlib.isNullEx2(m.get("teikei_cd"))) {
				m.set('teikei_cd','0010');
			}            // 家紋画像
            var $cp = $('#misc #crest_pic');
            $cp.find('img').remove();
            if (!$.msiJqlib.isNullEx2(m.get('kamon_oid'))) {
                $('<img>').appendTo($cp).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + m.get('kamon_oid'));
            }
			var tgt = $('#invitation .tab li').attr('data-target')
			$('#invitation .tab li.on').removeClass('on');
			$('#invitation .tab li.samples').addClass('on');
       }
        // データを退避する
        appthanks.orgDataOrei = appthanks.oreiModel.toJSON();
    };
    appthanks.resetData(data);
    $("#thanks-div-wrapper").show();
    
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#thanks-org-tab #hearing_info #contact_name': ['#thanks-org-tab #hearing_info #contact_name_kana', 'sz_knm', appthanks.oreiModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);
});
