/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 *
 * @version 2016/08/09 Mihara 「その他場所」追加
 * @version 2016/12/28 Matsuyama 納品場所区分「7：寺院」を追加
 */
var apphh = apphh || {};
$(function () {
    "use strict";
    var pro = {
        ndate:         '.i_date',
        komokku:       '.komokku',
        hachu_komokku: '.hachu_komokku',
        yobi_komokku:  '.yobi_komokku'
    };

    //明細モデル（発注返礼）
    var HachuModel = Backbone.Model.extend({
        defaults: function () {
            return {
                //SIIRE_MST 仕入先マスタ
                seko_no: null, // 施行番号
                hachu_no: 0, // 発注管理番号
                crt_row_no: 0, // 行番号
                siire_cd: null, // 仕入先コード
                report_cd: null, // 帳票コード
                ha_rp_code_kbn: null, // 発注書区分コード区分
                ha_rp_cd: null, // 発注書区分コード
                ha_rp_kbn: 0, // 発注書区分
                ha_entry_kbn: 0, // 発注書入力区分
                ha_syori_kbn: 0, // 発注書処理区分
                ha_etc_kbn: 0, // 発注書その他区分
                hachu_den_kbn: 0, // 発注伝票区分
                dai_bunrui_cd: null, // 大分類コード
                chu_bunrui_cd: null, // 中分類コード
                shohin_kbn: null, // 商品区分
                shohin_cd: null, // 商品コード
                shohin_nm: null, // 商品名
                shohin_tkiyo_nm: null, // 商品摘要名
                hachu_suryo: 0, // 数量
                hachu_suryo_bk     : 0,     // 数量
                tani_cd: 0, // 単位コード
                hanbai_tnk: 0, // 販売単価
                hachu_tnk: 0, // 発注単価
                hachu_prc: 0, // 発注金額
                nebiki_prc: 0, // 値引額
                delivery_kbn: 0, // 納品場所区分
                nonyu_cd: null, // 納入先コード
                nonyu_nm: null, // 納品場所（納入先名）
                nonyu_ymd: $.msiJqlib.getStdDate(), // 納品（納入）日時
                jc_denpyo_no: 0, // 受注伝票NO
                jc_msi_no: 0, // 受注明細№
                hc_denpyo_no: 0, // 発注伝票NO
                order_flg: 0, // 発注済み
                order_ymd: $.msiJqlib.getStdDate(), // 発注日時
                order_tanto_cd: null, // 発注担当者コード
                chk_order_flg: 0, // 発注確認済み
                chk_order_ymd: $.msiJqlib.getStdDate(), // 発注確認日時
                chk_order_tanto_cd: null, // 発注確認担当者コード
                hacyu_cnt: 0, // 発注回数
                status_kbn: 0, // 発注ステータス
                hachu_no_moto: null, // コピー元発注管理番号
                hd_biko1: null, // 備考１
                hd_biko2: null, // 備考２
                delete_flg: 0, // 削除フラグ
                //SHOHIN_KBN_MST 商品区分マスタ
                shohin_kbn_nm: null, // 商品区分名
                //SIIRE_MST 仕入先マスタ
                siire_lnm: null, // 仕入先名
                siire_knm: null, // 仕入先カナ名
                siire_snm: null, // 仕入先簡略名    

                is_added: false, //コピー先行フラグ

                n_free1: null, // 予備・追加区分
                nonyu_knm: null,
                nonyu_yubin_no: null,
                nonyu_addr1: null,
                nonyu_addr2: null,
                nonyu_tel: null,
                nonyu_fax: null,
                nonyu_biko1: null,
                nonyu_biko2: null,
                data_kbn: 0,
                k_free3: null, //発注区分
                k_free4: null, //予備区分
                n_free5: 0     //予備希望数量
                
            };
        },
        validation: {
            komokku: {
                required: function () {
//                    return this.deliveryRequired();
                    return false;
                },
                //  msg: '納品場所は必須入力です。'
                msg: '納品場所もしくは納品場所の住所が未設定です。'
            },
            ndate: [//{                
                //     required: function() {
                //         return this.dateRequired();
                //     },
                //     msg: '納品日時[日付]は必須入力です。'
                // },
                {
                    required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
                    msg: '納品日時[日付]の入力に誤りがあります。'
                }],
            ntime: [//{
                //    required: function() {
                //        //return true;
                //        return this.timeRequired();
                //    },
                //    msg: '納品日時[時間]は必須入力です。'
                //},
                {
                    required: false,
                    pattern: 'time',
                    msg: '納品日時[時間]の入力に誤りがあります。'
                }
            ],
            ntime2: {
                required: false,
                pattern: 'time',
                msg: '納品日時[時間]の入力に誤りがあります。'
            },
            hachu_suryo: [{
                required: function () {
                    //return true;
                    return this.valRequired();
                },
                pattern: 'number',
                msg: '数量の入力に誤りがあります。'    
            },
                {
                required: function () {
                    //return true;
                    return this.valRequired();
                },
                maxLength: 8,
                msg: '数量は8桁までです。'
            }],
            n_free5:[{
                    required: function () {
                    //return true;
                    return this.valRequired();
                },
                pattern: 'number',
                msg: '予備の数量の入力に誤りがあります。'    
            },
                {
                required: function () {
                    //return true;
                    return this.valRequired();
                },
                maxLength: 8,
                msg: '予備の数量は8桁までです。'
            }]
        },
        deliveryRequired: function () {
            //別注品は必須チェックなし
            if (hachuCol.length > 0 && this.get("is_checked") && (this.get('data_kbn') == 4)) {
                return false;
            }
            // 行選択されていない場合は必須チェックをはずす
            else if (hachuCol.length > 0 && this.get("is_checked") && (this.get('delivery_kbn') === null || this.get('delivery_kbn') == '0')) {
                return true;
            }
            else if (hachuCol.length > 0 && this.get("is_checked") && (this.get('delivery_kbn') == '9' && (this.get('nonyu_addr1') === null || this.get('nonyu_addr1') == ''))) {
                return true;   //その他で住所1が未設定の場合はエラー
            } else {
                return false;
            }
        },
        dateRequired: function () {
            //別注品は必須チェックなし
            if (hachuCol.length > 0 && this.get("is_checked") && (this.get('data_kbn') == 4)) {
                return false;
            }
            // 行選択されていない場合は必須チェックをはずす
            else if (hachuCol.length > 0 && this.get("is_checked") && (this.get('ndate') === null || this.get('ndate') == '')) {
                return true;
            } else {
                return false;
            }
        },
        timeRequired: function () {
            //別注品は必須チェックなし
            if (hachuCol.length > 0 && this.get("is_checked") && (this.get('data_kbn') == 4)) {
                return false;
            }
            // 行選択されていない場合は必須チェックをはずす
            else if (hachuCol.length > 0 && this.get("is_checked") && (this.get('ntime') === null || this.get('ntime') == '')) {
                return true;
            } else {
                return false;
            }
        },
        valRequired: function () {
            // 行選択されていない場合は必須チェックをはずす
            if (hachuCol.length > 0 && this.get("is_checked") && (this.get('hachu_suryo') === null || this.get('hachu_suryo') == '')) {
                return true;
            } else {
                return false;
            }
        },
        labels: {
            hachu_suryo: '数量'
        }
    }); // HachuModel
//    var AppModel = new HachuModel();


    //明細コレクション（発注返礼）
    var HachuCollection = Backbone.Collection.extend({
        model: HachuModel,
        //  comparator : 'hachu_no',
        nextSeqNo: function () {
            var i = mydata.dataCol[0].next_hachu_no;

            mydata.dataCol[0].next_hachu_no = mydata.dataCol[0].next_hachu_no + 1;
            return i;
        }
    });
    var hachuCol = new HachuCollection();


    //明細ビュー（発注返礼）
    var HachuView = Backbone.View.extend({
        tagName: "fieldset",
        className: "member",
        template: _.template($('#item-template').html()),
        events: {
            "click .hachusk_button": "dlghachusk",
            "click .i_n_place, .dlg_place": "dlgplace",
            "click .ymdcalendar": "dlgdate",
            "click .dlg_time": "dlgtime",
            "change .komokku": "setNonyuName",
            "keypress .i_amount": "preventEimoji",
            "change .i_date, .i_time, .i_time2, .i_bikou, .i_amount ,hachu_komokku,yobi_komokku, .yobi_suryo": "titleSet",
            "change .yobi_komokku" :"setYobiKbn", 
            "change .hachu_komokku" :"setHachuKbn" 
        },
        bindings: {
            '.hinmk': 'hachu_no',
            '.hinmkLabel': 'siire_lnm',
            '.siire_cd': 'siire_cd',
            '.i_n_place': 'nonyu_nm',
            '.i_date': 'ndate',
            '.i_time': 'ntime',
            '.i_time2': 'ntime2',
            '.shohin_kbn': 'shohin_kbn_nm',
            '.i_hinpin': 'shohin_nm',
            '.i_tekiyo': 'shohin_tkiyo_nm',
            '.i_tanka': {
                observe: 'hanbai_tnk',
                nSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_amount': {
                observe: 'hachu_suryo',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_stats': {
                observe: 'order_flg',
                initialize: 'setStatus'
            },
            '.yobi_suryo':{
                observe: 'n_free5',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            'select.komokku': {
                observe: 'delivery_kbn',
                selectOptions: {
                    collection: function () {
                        if (typeof apphh.delivery_kbn_arr != "undefined") {
                            return apphh.delivery_kbn_arr;
                        }
                        else {
                            return false;
                        }
                    },
                }
            },
            'select.hachu_komokku':{
                observe:'k_free3',
                 selectOptions: {
                    collection: function () {
                        if (typeof apphh.hachu_kbn_arr != "undefined") {
                            return apphh.hachu_kbn_arr;
                        }
                        else {
                            return false;
                        }
                    },
                }
            },
            'select.yobi_komokku':{
                observe:'k_free4',
                 selectOptions: {
                    collection: function () {
                        if (typeof apphh.yobi_kbn_arr != "undefined") {
                            return apphh.yobi_kbn_arr;
                        }
                        else {
                            return false;
                        }
                    },
                }
            },
            
            'select.yobikomokku': {
                observe: 'n_free1',
                selectOptions: {
                    collection: function () {
                       return [{value:"0",label:""},{value:"1",label:"予備"},{value:"2",label:"追加"}]
                    },
                }
            },
            '.i_bikou': 'hd_biko1'
        },
        initialize: function () {
            this.listenTo(this.model, 'destroy', this.remove);
            this.render();
        },
        render: function (e) {
            var m = this.model;
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(pro, "error2"));
//            Backbone.Validation.bind(this, {
//                valid: function (view, attr, selector) {
//                    var $el = view.$('.' + attr);
//                    $el.removeClass('error1');
//                    $el.attr('title', '');
//                },
//                invalid: function (view, attr, error, selector) {
//                    var $el = view.$('.' + attr);
//                    if ($el.length) {
//                        $el.addClass('error1');
//                        $el.attr('title', error);
//                    }
//                }
//            });

            if (typeof this.$el !== "undefined") {
                this.$el.html(this.template(this.model.toJSON()));
            }

//            //納品場所
//            if(this.model.get('delivery_kbn') === '9') {
//                $(this.$el).find('.i_n_place').attr("readonly", false);
//            }
//            else{
//                $(this.$el).find('.i_n_place').attr("readonly", true);    
//            }           

            this.$(".i_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".i_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$(".i_time2").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            //buttonsetを正しく実行ためにattrは動的に代わる
            var currAttr = $(this.$el).find(".lbl_stamp_check").attr("for");
            var newAttr = currAttr + this.model.attributes.hachu_no;
            $(this.$el).find(".lbl_stamp_check").attr("for", newAttr);

            $(this.$el).find(".lbl_stamp_check").next().attr("id", newAttr);
            $(this.$el).find("#hachu").attr("id", 'hachu' + this.model.attributes.hachu_no);
            //End attrネーミング
            var hachu_no = this.model.attributes.hachu_no;
            var listModel = this.model;


            //納品区分コンボボックスで動的IDを追加
            $(this.$el).find('.komokku').attr("data-id", listModel.cid);
            $(this.$el).find('.lbl_stamp_check').on('click', function (e) {
                //console.log($(e.currentTarget).class("ui-state-active"));
                if (typeof $(e.currentTarget).attr("aria-pressed") == "undefined" || $(e.currentTarget).attr("aria-pressed") == "false") {
                    $(e.currentTarget).next().val("1");
                    listModel.set('is_checked', 1);
                    /*if($(e.currentTarget).area-pressed == true) {
                     alert("clicked");
                     }*/
                }
                else
                {
                    $(e.currentTarget).next().val("0");
                    listModel.set('is_checked', 0);
                }
            });

            //コピー先の場合、数量の項目は変更不可能にする
            if (this.model.get('hachu_no_moto') === null) {
                $(this.$el).find('.i_amount').attr("readonly", true);
            } else
            {
                $(this.$el).find('.i_amount').attr("readonly", false);
            }
            $(this.$el).find('.i_amount').attr("id", "hachu_suryo_" + hachu_no);
            this.model.validate();
            this.stickit();

            this.titleSet();
            return this;
        },
        titleSet: function () {
            var arrayItem = ['.hinmkLabel', '.i_n_place', '.i_date', '.i_time', '.i_time2', '.shohin_kbn', '.i_hinpin', '.i_tekiyo', '.i_bikou',
                '.i_tanka', '.i_amount', '.i_stats','.yobi_suryo','hachu_komokku','.yobi_komokku'];
            for (var $i = 0; $i < arrayItem.length; $i++) {
                $(this.$el).find(arrayItem[$i]).attr("title", $(this.$el).find(arrayItem[$i]).val());
            }

        //    var $select = $(this.$el).find('.komokku').val();
        //    if ($select == 9) {
        //        $select = apphh.delivery_kbn_arr.length - 1;
        //    }
        //    $(this.$el).find('.komokku').attr("title", apphh.delivery_kbn_arr[$select]["label"]);            
            //各コンボボックスのラベル表示
            //納品区分
            var $select = $(this.$el).find('.komokku').val();
            for(var $i=0; $i<apphh.delivery_kbn_arr.length; $i++){ 
                if (apphh.delivery_kbn_arr[$i]["value"] == $select){
                    $(this.$el).find('.komokku').attr("title", apphh.delivery_kbn_arr[$i]["label"]);      
                }
            }
            //予備区分
              var $selectYobi = $(this.$el).find('.yobi_komokku').val();
            for(var $i=0; $i<apphh.yobi_kbn_arr.length; $i++){ 
                if (apphh.yobi_kbn_arr[$i]["value"] == $selectYobi){
                    $(this.$el).find('.yobi_komokku').attr("title", apphh.yobi_kbn_arr[$i]["label"]);
                }
            }
            //発注区分
              var $selectHachu = $(this.$el).find('.hachu_komokku').val();
            for(var $i=0; $i<apphh.hachu_kbn_arr.length; $i++){ 
                if (apphh.hachu_kbn_arr[$i]["value"] == $selectHachu){
                    $(this.$el).find('.hachu_komokku').attr("title", apphh.hachu_kbn_arr[$i]["label"]);
                }
            }
            return true;
        },
        unbindFromAll: function () {
            _.each(this.events, function (ev) {
                ev = {};
            });
            this.bindings = [];
        },
        dlghachusk: function (event) {
            var bbm = this.model;
            var that = this;
            $(event.currentTarget).msiPickHelper({'action': 'siire', data: {}, onSelect: function (data) {
                    //$('textbox').removeAttr('readonly').val('Changed Value');
                    $(event.currentTarget).prev().val(data.siire_lnm);
                    $(event.currentTarget).next(".siire_cd").val(data.code);
                    $(event.currentTarget).next(".siire_cd").change();
                    bbm.set({siire_cd: data.code,
                        siire_lnm: data.siire_lnm});
                    that.titleSet();
                },
                onClose: function (data) { //console.log("onClose here");
                },
                hookSetData: function (data) { //console.log("hookSetData"); 
                    return {};
                }
            });
        },
        dlgplace: function () {
            var that = this;
            var bbm = this.model;

            //「9:その他」以外はDLGを呼ばない。
            if ($(this.$el).find('.komokku').val() !== "9") {
                return false;
            }

            msiLib2.setPickHelperForm('seko.nohinbasho', this.$('.i_n_place')
                    , function () { // hookSetData
                        var p = {init_search: 1,
                            no_cond: 1,
                            s_seko_no: mydata.dataCol[0].seko_no,
                            s_basho_kbn: '9', // 9,
                            form_data: {nonyu_cd: bbm.get('nonyu_cd'),
                                nonyu_nm: bbm.get('nonyu_nm'),
                                nonyu_knm: bbm.get('nonyu_knm'),
                                nonyu_yubin_no: bbm.get('nonyu_yubin_no'),
                                nonyu_addr1: bbm.get('nonyu_addr1'),
                                nonyu_addr2: bbm.get('nonyu_addr2'),
                                nonyu_tel: bbm.get('nonyu_tel'),
                                nonyu_fax: bbm.get('nonyu_fax'),
                                nonyu_biko1: bbm.get('nonyu_biko1'),
                                nonyu_biko2: bbm.get('nonyu_biko2'),
                                nonyu_delivery_kbn: 9,
                                seko_no: mydata.dataCol[0].seko_no},
                        };
                        return p;
                    }
            , function (data) { // onUpdate
                console.log(JSON.stringify(data));
                var formJson = {nonyu_cd: data.nonyu_cd,
                    nonyu_nm: data.nonyu_nm,
                    nonyu_knm: data.nonyu_knm,
                    nonyu_yubin_no: data.nonyu_yubin_no,
                    nonyu_addr1: data.nonyu_addr1,
                    nonyu_addr2: data.nonyu_addr2,
                    nonyu_tel: data.nonyu_tel,
                    nonyu_fax: data.nonyu_fax,
                    nonyu_biko1: data.nonyu_biko1,
                    nonyu_biko2: data.nonyu_biko2,
                    nonyu_delivery_kbn: 9,
                    seko_no: mydata.dataCol[0].seko_no};
                $.ajax({
                    url: $.msiJqlib.baseUrl() + '/juchu/etc/regnohin',
                    type: 'POST',
                    data: {'form_data': formJson},
                    dataType: 'json',
                    success: function (data) {
                        console.log('np2-nohin-basho-reg=> ' + JSON.stringify(data));
                        if (data.status == 'OK') {
                            that.data_set(bbm, data);
                            that.titleSet();
                        } else {
                            //    msiLib2.showErr( data.msg );
                        }
                    }
                });
            }
            );
        },
        data_set: function (curModel, param) {
            curModel.set({nonyu_cd: param.data.nonyu_cd,
                nonyu_nm: param.data.nonyu_nm,
                nonyu_knm: param.data.nonyu_knm,
                nonyu_yubin_no: param.data.yubin_no,
                nonyu_addr1: param.data.addr1,
                nonyu_addr2: param.data.addr2,
                nonyu_tel: param.data.tel,
                nonyu_fax: param.data.fax,
                nonyu_biko1: param.data.biko1,
                nonyu_biko2: param.data.biko2
            });
        },
        dlgdate: function (event) {
            $(event.currentTarget).prev().datetimepicker("show");
        },
        dlgtime: function (event) {
            $(event.currentTarget).prev().timepicker("show");
        },
        setStatus: function ($el, model, options) {
            if ($el.val() == "1") {
                $el.val("○");
            }
            else if ($el.val() == "0") {
                $el.val("×");
            }
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        preventEimoji: function (e) {
            $.msiJqlib.preventEimoji(e);
        },
        //予備区分が希望の場合、個数入力のテキストボックス生成
        setYobiKbn: function (event){
            //予備数量区分
		var m = this.model;
		var val =  $(event.currentTarget).val();
                m.set("k_free4", val);
                  switch (val) {                          
		case '3':		//希望
		$(event.currentTarget).next().addClass( 'yobi_suryo' );
                $(event.currentTarget).next().val("0");
                $(event.currentTarget).next().change();
		break;
                    default:                //それ以外
		$(event.currentTarget).next().removeClass( 'yobi_suryo' );
                $(event.currentTarget).next().val("");
                $(event.currentTarget).next().change();
                this.suryoclear(this.model);
		break;
				}
                this.titleSet();
			
        },
         setHachuKbn: function (event){
            //発注区分
		var m = this.model;
		var val =  $(event.currentTarget).val();
                m.set("k_free3", val);
                this.titleSet();
            },
        setNonyuName: function (event) {
            var selectedKbn = $(event.currentTarget).val();
            var id = $(event.currentTarget).data("id");
            //console.log(this.collection.get(id));
            //故人宅や喪主宅の場合納品場所は空欄にする
            $.msiJqlib.clearAlert();
            if (selectedKbn == "0" || selectedKbn == "1" || selectedKbn == "2") {
                $(event.currentTarget).next().val("");
                $(event.currentTarget).next().change();
                this.nonyuclear(this.model);
//               $(this.$el).find('.i_n_place').attr("readonly", true);
                this.titleSet();
                return false;
            }
            //葬儀式所の場合
            else if (selectedKbn == "3" || selectedKbn == "4" || selectedKbn == "5" || selectedKbn == "6" ||
                     selectedKbn == "7" || selectedKbn == "8" ) { // 8(その他場所) Mihara 2016/08/09
                this.nonyuclear(this.model);
                var params = JSON.stringify({seko_no: mydata.dataCol[0].seko_no, delivery_kbn: selectedKbn});
                var $result = this.sendRequest(params, selectedKbn, $(event.currentTarget), this.model);
                //エラー時は納品場所を空欄にする
                if ($result !== "0") {
                    $(event.currentTarget).next().val("");
                    $(event.currentTarget).next().change();
                    this.nonyuclear(this.model);
                    this.titleSet();
                }
//                $(this.$el).find('.i_n_place').attr("readonly", true);
            }
            else {
                $(this.$el).find('.i_n_place').val("");
                $(this.$el).find('.i_n_place').change();
                this.nonyuclear(this.model);
                this.titleSet();
//                $(this.$el).find('.i_n_place').attr("readonly", false);
            }
        },
        suryoclear: function(currentModel){
            currentModel.attributes.n_free5 = null;
        },
        nonyuclear: function (currentModel) {
            currentModel.attributes.nonyu_cd = 0;
            currentModel.attributes.nonyu_knm = "";
            currentModel.attributes.yubin_no = "";
            currentModel.attributes.nonyu_addr1 = "";
            currentModel.attributes.nonyu_addr2 = "";
            currentModel.attributes.nonyu_tel = "";
            currentModel.attributes.nonyu_fax = "";
            currentModel.attributes.nonyu_biko1 = "";
            currentModel.attributes.nonyu_biko2 = "";
        },        
        sendRequest: function (params, selectedKbn, currentElement, currentModel) {
            var errorMsg = this.getErrorText(selectedKbn);
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/searchnonyuinfo',
                data: {
                    dataAppJson: params
                },
                type: 'POST',
                success: function (mydata) {

                    if (mydata.status === 'OK') {
                        //取得された納入場所名所を表示
                        currentElement.next().val(mydata.nonyu_nm);
                        currentElement.next().change();
                        //選択された行のモデルデータ更新
                        currentModel.attributes.nonyu_cd = mydata.nonyu_cd;
                        currentModel.attributes.nonyu_knm = mydata.nonyu_knm;
                        currentModel.attributes.yubin_no = mydata.yubin_no;
                        currentModel.attributes.nonyu_addr1 = mydata.addr1;
                        currentModel.attributes.nonyu_addr2 = mydata.addr2;
                        currentModel.attributes.nonyu_tel = mydata.tel;
                        currentModel.attributes.nonyu_fax = mydata.fax;
                        currentModel.attributes.nonyu_biko1 = mydata.biko1;
                        currentModel.attributes.nonyu_biko2 = mydata.biko2;
                        that.titleSet();
                        return "0";
                    } else if (mydata.status === 'Error') {
                        //console.log(mydata);
                        $.msiJqlib.showWarn(errorMsg);
                        return "-1";
                    }
                    //console.log('ajax res msg==>' + data.msg);
                }
            });
        },
        getErrorText: function (selectedKbn) {
            var errorMsg = "";
            var basho_nm = "";
            for(var $i=0; $i<apphh.delivery_kbn_arr.length; $i++){ 
                if (apphh.delivery_kbn_arr[$i]["value"] == selectedKbn){
                    basho_nm = apphh.delivery_kbn_arr[$i]["label"];
                }
            }  
            
            switch (selectedKbn) {
                case "1" :
                    errorMsg = "1番納品場所区分情報エラー"; //デバッグ用設定
                    break;
                case "2" :
                    errorMsg = "2番納品場所区分情報エラー"; //デバッグ用設定
                    break;
//                case "3" :
//                    errorMsg = "葬儀式場の情報が登録されていない為、データの取得ができません。";
//                    break;
//                case "4":
//                    errorMsg = "通夜会場の情報が登録されていない為、データの取得ができません。";
//                    break;
//                case "5":
//                    errorMsg = "壇払いの情報が登録されていない為、データの取得ができません。";
//                    break;
                case "3" :
                case "4" :
                case "5" :
                case "6" :
                case "8" : // mihara add 2016/08/09
                    errorMsg = basho_nm + "の情報が登録されていない為、データの取得ができません。";
                    break;
                default :
                    errorMsg = "納品場所区分の区分値情報エラー";
                    break;
            }
            return errorMsg;
        } 
    });


    // appモデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                nosi_nm: null // 熨斗紙
            };
        },
        validation: {
        },
        labels: {
        }
    });

    // 共通部分
    var AppView = Backbone.View.extend({
        el: "#detail",
        events: {
            "click #btn_back": "doBack",
            "click #btn_save": "doSave",
            "click #btn_preview": "doPreview",
            "click #btn_del": "doDelete",
            "click #btn_add": "addRow",
            "click #btn_order": "doOrder",
            "click #btn_cancel": "doakaCancel",
            "click #btn_cancel2": "doakaCancel"
        },
        bindings: {
            '#nosi_nm': 'nosi_nm'
        },
        initialize: function () {
            this.listenTo(this.collection, 'reset', this.resetCol);
            this.stickit();
        },
        render: function () {
            $("#dataContent").find('.radio_set').buttonset();
        },
        doBack: function () {
            location.href = $.msiJqlib.baseUrl() + '/hachu/hachushori/hachusholist/sn' + mydata2.seko_no;
        },
        doSave: function (mode) {
            //apphh.params = JSON.stringify(this.collection.toJSON());

            if (!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
            if (hachuCol.length == 0){
                $.msiJqlib.showInfo('データがありません。');
                return false;
            } 
            
            //バリデーション
            if (!this.isInputOk()) {
                return false;
            }
            //apphh.params = this.getAllRows();
            apphh.params = this.getSelectedRows();
            if (mode != "3") {
                appModel.set('mode', 1);
            }
            var that = this;
            //console.log(apphh.params); return;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/henreisave',
                type: 'POST',
                dataType: "json",
                data: {dataAppJSON: apphh.params, appData: JSON.stringify(appModel.toJSON())},
                success: function (respData) {
                    console.log(respData);
                    //$('#item-template').replaceWith(html);
                    /* ラジオボタン・チェックボックスのボタン化 */
                    // $('.radio_set, .checkbox_set').buttonset();
                    if (respData.status == "OK") {
                        var sidedata = {};
                        sidedata = JSON.parse(respData.sidedata);
                        $.msiSideMenuLib.setSideMenu({data: sidedata});
                        that.colrefreshOK2();
                        if (mode == "1") {that.doPreview_Sub();}
                        else if (mode == "2") {that.doOrder_Sub();} 
                        else if (mode == "3") {that.doakaCancel_Sub();}
                        else {$.msiJqlib.showInfo(respData.msg)}                        
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                    }
                }
            });
        },
        colrefreshOK2: function () {
            _.each(this.collection.models, function (m) {
                if (m.attributes.is_checked == 1) {
                    m.set({is_added: false})
                }
            });
        },
        doPreview: function () {

            if (this.doSave(1) === false) {
                return false;
            }
            ;
        },
        doPreview_Sub: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }            
            //pdf出力
            var sekoNo = mydata.dataCol[0].seko_no;
            var hachu_no = new Array();
            var selectedRows = this.getSelectedRows(true, true);
//            var canOrder = true;
            _.each(selectedRows, function (row) {
//                if(row.get('order_flg') === "1") {
//                    canOrder = false;
//                }      
                hachu_no.push(row.get('hachu_no'));
            });
            //何もせず終了
//            if(!canOrder) {
//                $.msiJqlib.showInfo('発注済のデータが選択されています。');
//                return false;
//            }     
            var hachu_param = "";
            for (var i = 0; i < hachu_no.length; i++) {
                hachu_param = hachu_param + '&hachu_no[]=' + hachu_no[i];
                //    this._showOpen( '/hachu/pdf?preview=on&seko_no='+sekoNo+'&hachu_no='+hachu_no[i]);
            }

//            $.ajax({
//                url: $.msiJqlib.baseUrl() + '/hachu/pdf/dividePerHachusho',
//                type: 'POST'
//            });

//            this._showOpen( '/hachu/pdf?preview=on&seko_no='+sekoNo+hachu_param+'&hachu=off');  
            msiLib2.openWinPv(
                    $.msiJqlib.baseUrl() + '/hachu/pdf?preview=on&seko_no=' + sekoNo + hachu_param + '&hachu=auto',
                    {
                        p1: 'v1', // ...
                    }
            );
        },
        _showOpen: function (path) {
            var url = $.msiJqlib.baseUrl() + path;
            window.open(url, "_blank");
        },
        doDelete: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo("行選択されていません。");
                return false;
            }
            //コピーされたデータ以外の場合エラーメッセージを表示
            var selectedRows = this.getSelectedRows(true, true);
            var canDelete = true;
            _.each(selectedRows, function (row) {
                if (row.get('hachu_no_moto') === null) {
                    $.msiJqlib.showErr("受注画面から作成された明細行は、行削除はできません。受注画面から削除してください。");
                    canDelete = false;
                    return false;
                }
            });
            //何もせず終了
            if (!canDelete) {
                return false;
            }

            if (!confirm('発注返礼情報を削除します。よろしいですか？')) {
                return;
            }

            //配列でJSONに変換してモデルを取得             
            var paramData = this.getSelectedRows(true);
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/delete',
                data: {
                    dataAppJSON: JSON.stringify(paramData)
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.removeRows(paramData);
                        $.msiJqlib.showInfo(mydata.msg);
                        window.location.reload();
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        //一括削除
        removeRows: function (selectedRows) {
            _.each(selectedRows, function (row) {
                $("#i_chk" + row.hachu_no).parent().parent().parent().parent().remove();
            });
            //チェックの全クリア
            _.each(this.collection.models, function (m) {
                m.attributes.is_checked = 0;
            });
        },
        addRow: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo("行選択されていません。");
                return false;
            }
            var that = this;
            var selectedRows = that.getSelectedRows(true, true); //console.log(selectedRows);
            var tmpModel = {};
            var totalModels = this.collection.length;
            var new_hachu_no = this.collection.nextSeqNo();
            _.each(selectedRows, function (row) {
                tmpModel = $.extend(true, {}, row);
                if (row.get('hachu_no_moto') !== null) {
                    $.msiJqlib.showErr("行コピーで作成された明細行のため、行コピーはできません。\n受注画面から作成された元の明細行を、再度選択してください。");
                    return false;
                }
                //console.log(tmpModel);
                tmpModel.set('hachu_no', new_hachu_no);
                tmpModel.set('delivery_kbn', row.get('delivery_kbn'));
                tmpModel.set('ha_syori_kbn', row.get('ha_syori_kbn'));

                tmpModel.set('hachu_no_moto', row.get('hachu_no'));
                //行コピーマークを付く
                tmpModel.set('is_added', true);
                that.collection.add(tmpModel.attributes);
                tmpModel = that.collection.models[totalModels];

                that.addOne(tmpModel);
                tmpModel.set('hachu_suryo', "0");
                tmpModel.set('hachu_suryo_bk', "0");
                tmpModel.set('crt_row_no', totalModels + 1);
                tmpModel.set('order_flg', "×");
                tmpModel.set('is_checked', 0);
                
                if(tmpModel.attributes.k_free4 != 3){
                   $('.yobi_suryo:last').removeClass('yobi_suryo');
                }
                new_hachu_no++;
                mydata.dataCol[0].next_hachu_no = mydata.dataCol[0].next_hachu_no + 1;
                totalModels++;
            });

            $(this.el).find('.radio_set').buttonset();
        },
        //行コピー
        addOne: function (hachuModel_P) {
            var $div = this.$("#dataContent");
            //hachuModel_P.set('hachu_suryo', 0); 
            var v = new HachuView({model: hachuModel_P});

            //this.model = hachuModel;
            var domObj = v.render().el;

            //alert("data reading");
            $div.append(domObj);
            //var hachu_no = this.model.attributes.hachu_no;
            //       var hachu_no = hachuModel_P.get('hachu_no');
            //       $(this.$el).find('#hachu_suryo_' + hachu_no).attr("readonly", false);
            //this.render();
        },
        doOrder: function () {
            if (this.doSave(2) === false) {
                return false;
            }
            ;
        },
        doOrder_Sub: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
            //pdf出力
            var sekoNo = mydata.dataCol[0].seko_no;
            var hachu_no = new Array();
            var selectedRows = this.getSelectedRows(true, true);
            var that = this;
            var canOrder = true;
            _.each(selectedRows, function (row) {
                if (row.get('order_flg') === "1" || row.get('order_flg') === "○") {
                    canOrder = false;
                }
                hachu_no.push(row.get('hachu_no'));
            });
            //何もせず終了
            if (!canOrder) {
                $.msiJqlib.showInfo('発注済のデータが選択されています。');
                return false;
            }

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/hachu/pdf',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    hachu_no: hachu_no,
                }
            },
            function (data) {
                if (data.status === 'OK') {
                    that.ordersave();
                    that.colrefreshOK();

                }
            });
        },
        colrefreshOK: function () {
            _.each(this.collection.models, function (m) {
                if (m.attributes.is_checked == 1) {
                    m.set({order_flg: "○"});
                    $("#dataContent").find('#hachu' + m.attributes.hachu_no).attr("title", $("#dataContent").find('#hachu' + m.attributes.hachu_no).val());
                }
            });

        },
        ordersave: function () {
            apphh.params = this.getSelectedRows();

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuorder',
                type: 'POST',
                dataType: "json",
                data: {dataAppJSON: apphh.params},
                success: function (respData) {
                    console.log(respData);
                    if (respData.status == "OK") {
                        $.msiJqlib.showInfo(respData.msg);
                        //   window.location.reload();
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                    }
                }
            });

        },
        doCancel: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
            //バリデーション
            if (!this.isInputOk()) {
                return false;
            }
            var selectedRows = this.getSelectedRows(true, true);
            var canOrder = true;
            _.each(selectedRows, function (row) {
                if (row.get('order_flg') === "0") {
                    canOrder = false;
                }
            });
            //何もせず終了
            if (!canOrder) {
                $.msiJqlib.showInfo('未発注のデータが選択されています。');
                return false;
            }

            apphh.params = this.getSelectedRows();

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuCancel',
                type: 'POST',
                dataType: "json",
                data: {dataAppJSON: apphh.params},
                success: function (respData) {
                    console.log(respData);
                    if (respData.status == "OK") {
                        $.msiJqlib.showInfo(respData.msg);
                        //      window.location.reload();
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                        return false;
                    }
                }
            });
        },
        doakaCancel: function () {
            if (this.doSave(3) === false) {
                return false;
            }
            ;
        },
        doakaCancel_Sub: function () {
            if (!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
            var selectedRows = this.getSelectedRows(true, true);
            var canOrder = true;
            var that = this;
            _.each(selectedRows, function (row) {
                if (row.get('order_flg') === "0" || row.get('order_flg') === "×") {
                    canOrder = false;
                }
            });
            //何もせず終了
            if (!canOrder) {
                $.msiJqlib.showInfo('未発注のデータが選択されています。');
                return false;
            }
            var sekoNo = mydata.dataCol[0].seko_no;
            apphh.params = this.getSelectedRows();

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuakacancel',
                type: 'POST',
                dataType: "json",
                data: {dataAppJSON: apphh.params},
                success: function (respData) {
                    console.log(respData);
                    if (respData.status == "OK") {
                        if ((respData.akaden.length) != 0) {
                            msiLib2.fileDlAjax({
                                url: $.msiJqlib.baseUrl() + '/hachu/pdf',
                                data: {
                                    preview: 'off',
                                    send: 'off',
                                    seko_no: sekoNo,
                                    hachu_no: respData.akaden,
                                }
                            },
                            function (data) {
                            });
                        }
                        //that.colrefreshNO(respData.cancelno);
                        that.colrefreshNO();
                        $.msiJqlib.showInfo(respData.msg);
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                        return false;
                    }
                }
            });
        },
        //colrefreshNO : function(data) {     
        colrefreshNO: function () {
            _.each(this.collection.models, function (m) {
                /*     
                 if(m.attributes.is_checked == 1 ) {
                 for (var i=0 ; i< data.length;i++){
                 if (m.attributes.hachu_no === data[i]["old_hachuno"] ) {
                 m.set({order_flg : "×",
                 hachu_no  : data[i]["new_hachuno"]}); 
                 $("#dataContent").find('#hachu' + data[i]["old_hachuno"]).attr("title", $("#dataContent").find('#hachu' + data[i]["old_hachuno"]).val());  
                 $("#dataContent").find('#hachu' + data[i]["old_hachuno"]).attr("id", 'hachu'+data[i]["new_hachuno"]); 
                 }  
                 if (mydata.dataCol[0].next_hachu_no <(data[i]["new_hachuno"] +1)){
                 mydata.dataCol[0].next_hachu_no = data[i]["new_hachuno"] + 1;                                
                 }   
                 }                            
                 }
                 */
                if (m.attributes.is_checked == 1) {
                    m.set({order_flg: "×"});
                }
            });
        },
        //アプリのコレクションを設定
        resetCol: function (collection) {
            var $div = this.$("#dataContent");
            $div.find('fieldset').remove();
            var i = 1;
            _.each(collection.models, function (m) {
                var v = new HachuView({model: m, row: i});
                var domObj = v.render().el;
                $div.append(domObj);
                v = {};
                i++;
            });
            this.render();
        },
        //選択された行のデータをパラメタで渡す
        getSelectedRows: function (isArray, isModel) {
            var selectedRows = new Array();
            var i = 0;
            _.each(this.collection.models, function (m) {
                //モデル全体を渡す
                if (typeof isModel != "undefined" && m.attributes.is_checked == 1) {
                    selectedRows[i] = m;
                    i++;
                }
                //モデルの値だけ渡す
                if (m.attributes.is_checked == 1 && (typeof isModel == "undefined" || !isModel)) {
                    selectedRows[i] = m.attributes;
                    i++;
                }
            });
            if (typeof isArray == "undefined" || !isArray) {
                selectedRows = JSON.stringify(selectedRows);
            }
            return selectedRows;
        },
        getAllRows : function(isArray, isModel) {        
            var selectedRows = new Array();
            var i = 0;
            _.each( this.collection.models, function(m) {
                 //モデル全体を渡す
                 if(typeof isModel != "undefined") {
                     selectedRows[i]= m;
                     i++;
              	 }
             	 //モデルの値だけ渡す
             	 if(typeof isModel == "undefined" || !isModel) {
                    selectedRows[i]= m.attributes;
                    i++;
             	 }
            });
            if(typeof isArray == "undefined" || !isArray) {
                 selectedRows = JSON.stringify(selectedRows);
            }        
            return selectedRows;
        },        
        //行選択チェック
        checkSelected: function (checkMultiple) {
            var selectedCount = 0;
            _.each(this.collection.models, function (m) {
                if (m.attributes.is_checked == 1) {
                    selectedCount++;
                }
            });
            //複数行選択チェック
            if (typeof checkMultiple != "undefined" && selectedCount > 1) {
                $.msiJqlib.showWarn("複数行選択されています。");
                return false;
            }
            if (selectedCount) {
                return true;
            }
            else {
                return false;
            }
        },
        //バリデーションを実行
        isInputOk: function () {
            this.$el.addClass('my-ctxt-show-error');

            var aMsg = [], line;

            var line = 0;
            this.collection.each(function (m, i) {
                //選択中の行のみチェック
                if (m.attributes.is_checked == 1) {
                    var resLine = m.validate();
                    if (resLine) {
                        line = i + 1;
                        _.each(resLine, function (v, k) {
                            aMsg.push('明細' + line + '行目 ' + v);
                            console.log('*** err ' + '明細' + line + '行目 ' + k + ': ' + v);
                        });
                    }
                }
            });

            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        }
    });


    var appModel = new AppModel();
    //画面を実行
    var _resetData = function (dataApp, myCol, dataflg) {
        $("body").find(".lbl_stamp_check").unbind("click");
        $("body").find(".mycheckBox").unbind("click");
        var app = new AppView({model: appModel, collection: hachuCol});

        if (dataflg) {
            app.collection.reset(myCol);
            appModel.set(dataApp);
        }
    };

    var mydata = {};
    var mydata2 = {};
    try {
        mydata = JSON.parse(_.unescape($('#my-data-init-id').text()));
        mydata2 = JSON.parse(_.unescape($('#my-data-init-seko').text()));
        var dataflg = true;
        if (mydata.dataCol.length > 0) {
            apphh.delivery_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].kbn_value_lnm);
            apphh.hachu_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].hachu_kbn); //発注区分
            apphh.yobi_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].yobi_kbn);  //予備区分
            //console.log( apphh.delivery_kbn_arr);
        }
        if (typeof mydata.dataCol[0].seko_no == "undefined" || mydata.dataCol.length == 0) {
            $.msiJqlib.showInfo("データがありません。");
            dataflg = false;
        }
        _resetData(mydata.dataApp, mydata.dataCol, dataflg);
    } catch (e) {
        console.log('JSON error. ' + e);
        msiLib2.showErr('JSON error. ' + e);
    }
        //初期表示時の予備区分の状態チェック
       $(".yobi_suryo").each(function(i) {
          var val = mydata.dataCol[i].k_free4;
          if( val != 3){
               $(this).removeClass( 'yobi_suryo' );
          }
    });
    return Backbone.Router.extend({});
});

