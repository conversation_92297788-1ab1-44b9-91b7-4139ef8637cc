<?php
  /**
   * App_HachuLib
   *
   * 発注処理　ライブラリクラス
   *
   * @category   App
   * @package    controllers\Hachu
   * <AUTHOR> Sato
   * @since      2014/03/04
   * @version    2019/07/xx mihara 軽減税率対応 keigen なお gashoen では伝票集計でなく明細からの積上のみ(端数調整なし)
   * @filesource 
   */

  /**
   * 発注処理　ライブラリクラス
   * 
   * @category   App
   * @package    controllers\Hachu
   * <AUTHOR> Sato
   * @since      2014/03/04
   */
class App_HachuLib {

    const ERR_STATUS_OK = 0;
    const ERR_STATUS_UNJUST_PARAMETER = 1;
    const ERR_STATUS_NODATA = 2;

	/**
     * 帳票名を取得
     * 
     * <AUTHOR> Sato
     * @since 2014/03/14
     * @param type $db
     * @param type $report_cd
     * @return 帳票名
     */
    public static function getReportName($db, $report_cd) {
        $report = DataMapper_ReportKanriMst::find($db, array("report_cd" => $report_cd));
        if (count($report) > 0) {
            return $report[0]['report_lnm'].time();
        }
        return null;
    }

	/**
	 * 発注済みにする
     * 
     * <AUTHOR> Sato
     * @since 2014/05/29
	 * @param type $seko_no
	 * @param type $hachu_no_ary
	 */
	public static function setOrderEnd($seko_no, $hachu_no_ary) {
		$customerinfo = new Hachu_HachuCustomerinfo();
		$customerinfo->getInitData($seko_no, null);
		$login_cd = Msi_Sys_Utils::getAuthInfo('tanto_cd');
		foreach ($hachu_no_ary as $hachu_no) {
			$ary[] = array('hachu_no' => $hachu_no);
		}
		$customerinfo->hachuOrder($login_cd, $ary, $seko_no);
	}

	/**
	 * 発注完了フラグを更新する
     * 
     * <AUTHOR> Sato
     * @since 2014/05/30
	 * @param type $seko_no
	 */
	public static function updateOrderFinish($seko_no) {
		$db = Msi_Sys_DbManager::getMyDb();
	  try{
		$db->easyExecute( <<< END_OF_SQL
UPDATE seko_kihon_info T SET
    order_finish_flg =
		CASE WHEN
			(SELECT COUNT(hachu_no) 
        FROM seko_hachu_info 
        WHERE seko_no = T.seko_no
        AND order_flg = 0
        AND delete_flg = 0) = 0
		THEN 1
		ELSE 0 END
WHERE
    T.seko_no = :seko_no
END_OF_SQL
		  , array(':seko_no' => $seko_no));
	  } catch (Exception $e) {
		echo $e->getTraceAsString();
		return false;
	  }

        $db->commit();
		return true;
	}

	/**
     * 発注伝票作成
     * @version 2015/12/02 ソースが醜いので修正 kayo
     * @param type $seko_no 施行番号
     * @param type $hachu_no 発注番号（配列指定可）
     * @return type
     */
    public static function makeDenpyo($seko_no, $hachu_no, $db_ref = null) {
        if ($db_ref == null) {
            $db = Msi_Sys_DbManager::getMyDb();
        } else {
            $db = $db_ref;
        }
        $where = 's_hachu.seko_no = :seko_no';
        $param[':seko_no'] = $seko_no;
        if (isset($hachu_no)) {
            if (is_string($hachu_no)) {
                $where .= ' AND s_hachu.hachu_no = :hachu_no';
                $param['hachu_no'] = $hachu_no;
            } else if (is_array($hachu_no)) {
                $wk = null;
                for ($i = 0; $i < count($hachu_no); $i++) {
                    $wk[] = ':hachu_no'.$i;
                    $param[':hachu_no'.$i] = $hachu_no[$i];
                }
                if (isset($wk)) {
                    $where .= ' AND s_hachu.hachu_no IN ('.implode(', ', $wk).')';
                }
            }
        }

	  try{
        // ヘッダ情報を取得
        $recH1 = $db->easySelect( <<< END_OF_SQL
SELECT
     s_hachu.hc_denpyo_no
    ,s_hachu.order_ymd
    ,s_hachu.data_kbn
    ,s.bumon_cd
    ,s_hachu.order_tanto_cd
    ,s_hachu.siire_cd
    ,siire.siire_lnm
    ,s_hachu.delivery_kbn
    ,s_hachu.nonyu_cd
    ,s_hachu.nonyu_nm
    ,s_hachu.order_flg
    ,s_hachu.order_ymd
    ,s_hachu.order_tanto_cd
    ,nonyu.nonyu_knm
    ,CASE WHEN nonyu.addr1 IS NOT NULL THEN
            nonyu.yubin_no
        WHEN s_hachu.delivery_kbn = 1 THEN
            kihon.kg_yubin_no
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 0 THEN
            kihon.mg_yubin_no
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 1 THEN
            kihon.kg_yubin_no
        END                         AS  yubin_no
    ,CASE WHEN nonyu.addr1 IS NOT NULL THEN
            nonyu.addr1
        WHEN s_hachu.delivery_kbn = 1 THEN
            kihon.kg_addr1
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 0 THEN
            kihon.mg_addr1
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 1 THEN
            kihon.kg_addr1
        END                         AS  addr1
    ,CASE WHEN nonyu.addr1 IS NOT NULL THEN
            nonyu.addr2
        WHEN s_hachu.delivery_kbn = 1 THEN
            kihon.kg_addr2
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 0 THEN
            kihon.mg_addr2
        WHEN s_hachu.delivery_kbn = 2 AND kihon.mg_kbn = 1 THEN
            kihon.kg_addr2
        END                         AS  addr2
    ,nonyu.tel
    ,nonyu.fax
    ,s.moushi_cd
FROM seko_kihon_info AS s
INNER JOIN seko_hachu_info AS s_hachu 
    ON  s.seko_no           = s_hachu.seko_no 
    AND s_hachu.delete_flg  = 0
LEFT JOIN siire_mst siire 
    ON s_hachu.siire_cd = siire.siire_cd
    AND siire.delete_flg = 0
LEFT JOIN nonyu_info nonyu 
    ON  s_hachu.seko_no     = nonyu.seko_no
    AND s_hachu.nonyu_cd    = nonyu.nonyu_cd
    AND nonyu.delete_flg    = 0
LEFT JOIN seko_kihon_info kihon 
    ON s_hachu.seko_no = kihon.seko_no 
    AND kihon.delete_flg = 0
WHERE s.delete_flg = 0 AND $where LIMIT 1
END_OF_SQL
        , $param );
        if (count($recH1) == 0) {
            return;
        }

        // 合計金額を取得
        $recH2 = $db->easySelect( <<< END_OF_SQL
SELECT
    SUM(s_hachu.hachu_prc)  AS hachu_prc_sum,
    SUM(s_hachu.nebiki_prc) AS hachu_nebk_sum,
    SUM(CASE shohin.shiire_zei_kbn 
        WHEN 2 THEN 
            s_hachu.hachu_prc
        ELSE
           0
        END)                AS szei_katax_taisho_prc,
    SUM(CASE shohin.shiire_zei_kbn WHEN 1 THEN 
            s_hachu.hachu_prc
        ELSE
            0
        END)                AS uzei_katax_taisho_prc,
    SUM(CASE shohin.shiire_zei_kbn WHEN 0 THEN 
            s_hachu.hachu_prc
        ELSE
            0
        END)                AS hitax_katax_taisho_prc
FROM seko_hachu_info AS s_hachu
LEFT JOIN siire_mst siire 
    ON s_hachu.siire_cd = siire.siire_cd 
    AND siire.delete_flg = 0
LEFT JOIN nonyu_info nonyu 
    ON s_hachu.seko_no = nonyu.seko_no 
    AND s_hachu.nonyu_cd = nonyu.nonyu_cd 
    AND nonyu.delete_flg = 0
LEFT JOIN shohin_mst AS shohin 
    ON s_hachu.shohin_cd = shohin.shohin_cd 
    AND shohin.delete_flg = 0
    AND ( shohin.bumon_cd = s_hachu.shohin_bumon_cd OR shohin.bumon_cd = '00000')
WHERE s_hachu.delete_flg = 0 AND $where
END_OF_SQL
        , $param );
        
        $recH = $recH1[0] + $recH2[0];

        // 軽減税率対応 keigen 別途積み上げで計算
		/* // 消費税額の計算 */
		/* $tax_ary = App_ClsTaxLib::GetCalcTax($db, $recH['order_ymd'], $recH['szei_katax_taisho_prc'], 2); */
		/* $recH['out_zei_prc'] = $tax_ary['ZeiPrc']; */
		/* $tax_ary = App_ClsTaxLib::GetCalcTax($db, $recH['order_ymd'], $recH['uzei_katax_taisho_prc'], 1); */
		/* $recH['in_zei_prc'] = $tax_ary['ZeiPrc']; */
				
        // 明細情報を取得
        $recD = $db->easySelect( <<< END_OF_SQL
SELECT                
     s_hachu.dai_bunrui_cd
    ,s_hachu.chu_bunrui_cd
    ,s_hachu.shohin_kbn
    ,s_hachu.shohin_cd
    ,s_hachu.shohin_nm
    ,s_hachu.shohin_tkiyo_nm
    ,s_hachu.hachu_suryo
    ,s_hachu.tani_cd
    ,s_hachu.hanbai_tnk
    --2016/02/01 DEL Kayo   ,s_hachu.hachu_suryo * (select siire_tnk 
    --2016/02/01 DEL Kayo    from shohin_tanka_mst stm 
    --2016/02/01 DEL Kayo    where stm.shohin_cd =   s_hachu.shohin_cd
    --2016/02/01 DEL Kayo    AND stm.delete_flg = 0 limit 1) 
    ,s_hachu.hachu_prc       AS   hachu_prc            -- 2016/02/01 ADD Kayo
    ,s_hachu.nebiki_prc
    ,s_hachu.nonyu_ymd
    ,s_hachu.hd_biko1
    ,s_hachu.hd_biko2
    ,COALESCE(shohin.shiire_zei_kbn, 99) AS zei_kbn
    ,shohin.siire_kamoku_cd
    -- 2016/02/01 DEL Kayo ,(select siire_tnk 
    -- 2016/02/01 DEL Kayo     from shohin_tanka_mst stm 
    -- 2016/02/01 DEL Kayo     where stm.shohin_cd=s_hachu.shohin_cd 
    -- 2016/02/01 DEL Kayo     AND stm.delete_flg = 0 limit 1) 
    ,s_hachu.hachu_tnk    as siire_tnk  -- 2016/02/01 ADD Kayo
    ,shohin.maker_hinban
    ,s_hachu.hachu_no
    ,s_hachu.kaisya_cd
    ,s_hachu.shohin_bumon_cd
    ,s_hachu.acos_hachu_no              -- ACOS発注番号
    ,s_hachu.k_free4  AS  spe_shiire_zei_cd    -- keigen 軽減税率対応  仕入消費税コード手動設定
FROM seko_hachu_info AS s_hachu
LEFT JOIN shohin_mst AS shohin 
ON  s_hachu.shohin_cd = shohin.shohin_cd 
 AND shohin.delete_flg = 0
 AND ( shohin.bumon_cd = s_hachu.shohin_bumon_cd OR shohin.bumon_cd = '00000') 
WHERE s_hachu.delete_flg = 0 AND $where
ORDER BY s_hachu.hachu_no
END_OF_SQL
        , $param );            
        // 施行情報取得 葬儀日取得の為    軽減税率対応 keigen
        $sekoInfo = DataMapper_SekoKihon::find2($seko_no);
        foreach ($recD as $key => $value) {
            // 仕入消費税情報取得
            $shohinInfo = DataMapper_Shohin0::find($db, array('shohin_cd' => $value['shohin_cd'],
                                                              'bumon_cd' => $value['shohin_bumon_cd']),
                                                   $sekoInfo['sougi_ymd']);

            $shiire_zei_cd  = $shohinInfo[0]['shiire_zei_cd'];                       // 仕入消費税コード
            $shiire_zei_kbn = $shohinInfo[0]['shiire_zei_kbn'];                      // 仕入税区分
            if(!isset($shiire_zei_kbn) && strlen($shiire_zei_kbn) == 0){
                $shiire_zei_kbn = 99;
            }
            $shiire_reduced_tax_rate = $shohinInfo[0]['shiire_reduced_tax_rate'];    // 仕入軽減税率区分 keigen

            // 特別に zei_cd が指定されているならそれを使う. 発注登録(葬儀・寄贈品)
            if ( strlen($recD[$key]['spe_shiire_zei_cd']) > 0 ) {
                $_shiire_zei_cd_cand = $recD[$key]['spe_shiire_zei_cd'];
                $_sougi_ymd = $sekoInfo['sougi_ymd'];
                $_zeiCond = array( 'zei_cd' => $_shiire_zei_cd_cand,
                                   '__x1' => array('x', "tekiyo_st_date <= :x1", array('x1'=>$_sougi_ymd)),
                                   '__x2' => array('x', "tekiyo_ed_date >= :x2", array('x2'=>$_sougi_ymd)) );
                $_zeiMstRec = DataMapper_ZeiMst::findOne($db, $_zeiCond, false);
                if ( $_zeiMstRec ) {
                    $shiire_zei_cd = $_zeiMstRec['zei_cd'];
                    $shiire_reduced_tax_rate = $_zeiMstRec['reduced_tax_rate'];
                }
            }

            // 明細に仕入消費税情報を設定
            $recD[$key]['zei_cd']  = $shiire_zei_cd;                       // 仕入消費税コード
            $recD[$key]['zei_kbn'] = $shiire_zei_kbn;                      // 仕入税区分
            $recD[$key]['reduced_tax_rate'] = $shiire_reduced_tax_rate;    // 仕入軽減税率区分 keigen
        }
        
		// 消費税額の計算
		// keigen $pChoseiPrcOut = 0;
		// keigen $pChoseiPrcIn = 0;
		// keigen $pRuikeiBaikaPrc = 0;
		// keigen $pRuikeiShohiZeiPrc = 0;

        // 以下変数追加  軽減税率対応 keigen
        // $hachu_prc_sum  = 0;         // 発注金額合計
        // $hachu_nebk_sum = 0;         // 発注値引合計
        $szei_katax_taisho_prc  = 0; // 外税課税対象額
        $uzei_katax_taisho_prc  = 0; // 内税課税対象額
        $hitax_katax_taisho_prc = 0; // 非税課税対象額
        // $out_zei_prc = 0;            // 外税消費税額
        // $in_zei_prc  = 0;            // 内税消費税額        
        $pRuikeiShohiZeiPrcSoto = 0; // 累計外税消費税額
        $pRuikeiShohiZeiPrcUchi = 0; // 累計内税消費税額

		for ($i = 0; $i < count($recD); $i++) {
            $rec = $recD[$i];

            // 軽減税率対応 keigen
            list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate00) = DataMapper_ZeiMstEasy::getRtuAndEtc($rec['zei_cd']);
            $pBaika = $rec['hachu_prc'] + $rec['nebiki_prc'];
            $zei = App_ClsTaxLib::CalcTax($pBaika, $rec['zei_kbn'], $zei_rtu, $zei_hasu_kbn);
            if ($rec['zei_kbn'] == 1) {
                $recD[$i]["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
                $recD[$i]['out_zei_prc'] = 0;    // 行外税消費税額
                $szei_katax_taisho_prc += $pBaika;
                $pRuikeiShohiZeiPrcUchi += $zei["ZeiPrc"];
            } else if ($rec['zei_kbn'] == 2) {
                $recD[$i]['in_zei_prc'] = 0;     // 行内税消費税額
                $recD[$i]["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
                $uzei_katax_taisho_prc += $pBaika;
                $pRuikeiShohiZeiPrcSoto += $zei["ZeiPrc"];
            } else {
                $recD[$i]['in_zei_prc'] = 0;
                $recD[$i]["out_zei_prc"] = 0;
                $hitax_katax_taisho_prc += $pBaika;
            }

			/* switch ($rec['zei_kbn']) { */
			/* 	case 1:	// 内税 */
			/* 		$pZeiNo = null; */
			/* 		$pZeiRt = null; */
			/* 		$pShouhiZeiPrc = null; */
			/* 		App_ClsTaxLib::GetTaxMsiInfoUchiZei( $db, */
            /*         $recH['order_ymd'], */
            /*         //$rec['hanbai_tnk'] * $rec['hachu_suryo'], */
            /*         $rec['siire_tnk'] * $rec['hachu_suryo'], */
            /*         $rec['zei_kbn'], */
            /*         $pZeiNo, $pZeiRt, $pShouhiZeiPrc, */
            /*         $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrcIn ); */
			/* 		$recD[$i]['zei_cd'] = $pZeiNo; */
			/* 		$recD[$i]['out_zei_prc'] = 0; */
			/* 		$recD[$i]['in_zei_prc'] = $pShouhiZeiPrc; */
			/* 		break; */
			/* 	case 2:	// 外税 */
			/* 		$pZeiNo = null; */
			/* 		$pZeiRt = null; */
			/* 		$pShouhiZeiPrc = null; */
			/* 		App_ClsTaxLib::GetTaxMsiInfo( $db, */
            /*         $recH['order_ymd'], */
            /*         //$rec['hanbai_tnk'] * $rec['hachu_suryo'], */
            /*         $rec['siire_tnk'] * $rec['hachu_suryo'], */
            /*         $rec['zei_kbn'], */
            /*         $pZeiNo, $pZeiRt, $pShouhiZeiPrc, */
            /*         $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrcOut ); */
			/* 		$recD[$i]['zei_cd'] = $pZeiNo; */
			/* 		$recD[$i]['out_zei_prc'] = $pShouhiZeiPrc; */
			/* 		$recD[$i]['in_zei_prc'] = 0; */
			/* 		break; */
			/* 	default: */
			/* 		$recD[$i]['zei_cd'] = 0;            // 2016/06/03 UPD Kayo */
			/* 		$recD[$i]['out_zei_prc'] = 0; */
			/* 		$recD[$i]['in_zei_prc'] = 0; */
			/* 		break; */
			/* } */
		}

		// 消費税の端数を調整
//		$recD[0]['in_zei_prc'] = $pChoseiPrcIn;
//		$recD[0]['out_zei_prc'] = $pChoseiPrcOut;
// keigen	$recD[0]['in_zei_prc']  += $pChoseiPrcIn;
// keigen	$recD[0]['out_zei_prc'] += $pChoseiPrcOut;
                
        // 発注伝票を挿入
        $headerArr = array(
             ':denpyo_no'               => $recH['hc_denpyo_no']
            ,':order_ymd'               => $recH['order_ymd']
            ,':data_kbn'                => $recH['data_kbn']
            ,':bumon_cd'                => $recH['bumon_cd']
            ,':tanto_cd'                => $recH['order_tanto_cd']
            ,':siire_cd'                => $recH['siire_cd']
            ,':siire_nm'                => $recH['siire_lnm']
            ,':delivery_kbn'             => $recH['delivery_kbn']
            ,':nonyu_cd'                => $recH['nonyu_cd']
            ,':nonyu_nm'                => $recH['nonyu_nm']
            ,':nonyu_knm'               => $recH['nonyu_knm']
            ,':nonyu_yubin_no'          => $recH['yubin_no']
            ,':nonyu_addr1'             => $recH['addr1']
            ,':nonyu_addr2'             => $recH['addr2']
            ,':nonyu_tel'               => $recH['tel']
            ,':nonyu_fax'               => $recH['fax']
            ,':hachu_prc_sum'           => $recH['hachu_prc_sum']
            ,':hachu_nebk_sum'          => $recH['hachu_nebk_sum']
            ,':szei_katax_taisho_prc'   => $recH['szei_katax_taisho_prc']
            ,':uzei_katax_taisho_prc'   => $recH['uzei_katax_taisho_prc']
            ,':hitax_katax_taisho_prc'  => $recH['hitax_katax_taisho_prc']
            ,':out_zei_prc'             => $pRuikeiShohiZeiPrcSoto  // 軽減税率対応  keigen  $recH['out_zei_prc']
            ,':in_zei_prc'              => $pRuikeiShohiZeiPrcUchi  // 軽減税率対応  keigen  $recH['in_zei_prc']
            ,':sumi_order_flg'          => $recH['order_flg']
            ,':sumi_order_ymd'          => $recH['order_ymd']
            ,':sumi_order_tanto_cd'     => $recH['order_tanto_cd']
            ,':tax_code_kbn'            => '0150'
            ,':tax_cd'                  => $recD[0]['zei_kbn']
            ,':tax_kbn'                 => $recD[0]['zei_kbn']
            ,':zei_cd'                  => $recD[0]['zei_cd']
        );


        $db->easyExecute( <<< END_OF_SQL
INSERT INTO hachu_denpyo (
     denpyo_no
    ,order_ymd
    ,data_kbn
    ,bumon_cd
    ,tanto_cd
    ,siire_cd
    ,siire_nm
    ,delivery_kbn
    ,nonyu_cd
    ,nonyu_nm
    ,nonyu_knm
    ,nonyu_yubin_no
    ,nonyu_addr1
    ,nonyu_addr2
    ,nonyu_tel
    ,nonyu_fax
    ,hachu_prc_sum
    ,hachu_nebk_sum
    ,szei_katax_taisho_prc
    ,uzei_katax_taisho_prc
    ,hitax_katax_taisho_prc
    ,out_zei_prc
    ,in_zei_prc
    ,sumi_order_flg
    ,sumi_order_ymd
    ,sumi_order_tanto_cd
    ,tax_code_kbn
    ,tax_cd
    ,tax_kbn
    ,zei_cd
) VALUES (
    :denpyo_no
    ,:order_ymd
    ,:data_kbn
    ,:bumon_cd
    ,:tanto_cd
    ,:siire_cd
    ,:siire_nm
    ,:delivery_kbn
    ,:nonyu_cd
    ,:nonyu_nm
    ,:nonyu_knm
    ,:nonyu_yubin_no
    ,:nonyu_addr1
    ,:nonyu_addr2
    ,:nonyu_tel
    ,:nonyu_fax
    ,:hachu_prc_sum
    ,:hachu_nebk_sum
    ,:szei_katax_taisho_prc
    ,:uzei_katax_taisho_prc
    ,:hitax_katax_taisho_prc
    ,:out_zei_prc
    ,:in_zei_prc
    ,:sumi_order_flg
    ,:sumi_order_ymd
    ,:sumi_order_tanto_cd
    ,:tax_code_kbn
    ,:tax_cd
    ,:tax_kbn
    ,:zei_cd
)
END_OF_SQL
            , $headerArr);

		// 発注伝票明細を挿入
        for ($i = 0; $i < count($recD); $i++) {
            $rec = $recD[$i];
            App_Utils2::replaceShohinNm($rec);
            $tujo_hachu_flg = self::getTsujoHachuFlg($db, $rec['shohin_cd'], $rec['shohin_bumon_cd']);
//            $acos_hachu_no = null;
//            if($tujo_hachu_flg == 1){
//                // ACOS発注番号
//                $acos_hachu_no = App_ClsGetYoyakuNo::GetHachuNo($db);
//            }
            $acos_hachu_no = $rec['acos_hachu_no'];// ACOS発注番号
            
            $detailArr = array(
                 ':denpyo_no'        => $recH['hc_denpyo_no']
                ,':msi_no'           => $i + 1
                ,':disp_no'          => $i + 1
                ,':denpyo_kbn'       => 1
                ,':dai_bunrui_cd'    => $rec['dai_bunrui_cd']
                ,':chu_bunrui_cd'    => $rec['chu_bunrui_cd']
                ,':shohin_kbn'       => $rec['shohin_kbn']
                ,':shohin_cd'        => $rec['shohin_cd']
                ,':shohin_nm'        => $rec['shohin_nm']
                ,':shohin_tkiyo_nm'  => ''
                ,':hachu_suryo'      => $rec['hachu_suryo']
                ,':tani_cd'          => $rec['tani_cd']
                ,':hachu_tnk'        => $rec['siire_tnk']
                ,':hachu_prc'        => $rec['hachu_prc']
                ,':nebiki_prc'       => $rec['nebiki_prc']
                ,':nonyu_ymd'        => $rec['nonyu_ymd']
                ,':msi_biko1'        => ''
                ,':msi_biko2'        => ''
                ,':zei_kbn'          => $rec['zei_kbn']
                ,':zei_cd'           => $rec['zei_cd']
                ,':reduced_tax_rate' => $rec['reduced_tax_rate']   // 軽減税率対応 keigen
                ,':out_zei_prc'      => $rec['out_zei_prc']
                ,':in_zei_prc'       => $rec['in_zei_prc']
                ,':siire_kamoku_cd'  => $rec['siire_kamoku_cd']
                ,':jyodai_tnk'       => $rec['hanbai_tnk']
                ,':jyodai_prc'       => $rec['hanbai_tnk'] * $rec['hachu_suryo']
                ,':hachu_no'         => $rec['hachu_no']
                ,':shohin_bumon_cd'  => $rec['shohin_bumon_cd']
                ,':acos_hachu_no'    => $acos_hachu_no
            );

            $db->easyExecute( <<< END_OF_SQL
INSERT INTO hachu_denpyo_msi (
    denpyo_no
    , msi_no
    , disp_no
    , denpyo_kbn
    , dai_bunrui_cd
    , chu_bunrui_cd
    , shohin_kbn
    , shohin_cd
    , shohin_nm
    , shohin_tkiyo_nm
    , hachu_suryo
    , tani_cd
    , hachu_tnk
    , hachu_prc
    , nebiki_prc
    , nonyu_ymd
    , msi_biko1
    , msi_biko2
    , zei_kbn
    , zei_cd
    , reduced_tax_rate
    , out_zei_prc
    , in_zei_prc
    , siire_kamoku_cd
    , kenpin_flg
    , jyodai_tnk
    , jyodai_prc
    , hachu_no
    , shohin_bumon_cd
    , acos_hachu_no
) VALUES (
      :denpyo_no
    , :msi_no
    , :disp_no
    , :denpyo_kbn
    , :dai_bunrui_cd
    , :chu_bunrui_cd
    , :shohin_kbn
    , :shohin_cd
    , :shohin_nm
    , :shohin_tkiyo_nm
    , :hachu_suryo
    , :tani_cd
    , :hachu_tnk
    , :hachu_prc
    , :nebiki_prc
    , :nonyu_ymd
    , :msi_biko1
    , :msi_biko2
    , :zei_kbn
    , :zei_cd
    , :reduced_tax_rate
    , :out_zei_prc
    , :in_zei_prc
    , :siire_kamoku_cd
    , 0
    , :jyodai_tnk
    , :jyodai_prc
    , :hachu_no
    , :shohin_bumon_cd
    , :acos_hachu_no
)
END_OF_SQL
            , $detailArr);

        }
	  } catch (Exception $e) {
		echo $e->getTraceAsString();
		return false;
	  }
    if ($db_ref == null) {
            $db->commit();
    }
		return true;
    }

	public function getErrMsg($status)
	{
        switch ($status) {
            case self::ERR_STATUS_UNJUST_PARAMETER:
                $msg = 'パラメーターが不正です。';
                break;
            case self::ERR_STATUS_NODATA:
                $msg = 'データがありません。';
                break;
            default:
                $msg = 'エラー['.$status.']';
        }
		return $msg;
	}
    
    /**
     * 商品マスタから「通常発注フラグ」を取得する
     * 
     * <AUTHOR>
     * @since 2018/05/15
     * @param type $db
     * @param type $shohin_cd
     * @param type $shohin_bumon_cd
     */
    public function getTsujoHachuFlg($db,$shohin_cd,$shohin_bumon_cd){
        $tujo_hachu_flg = 0;
        $select = $db->easySelect(<<< END_OF_SQL
                SELECT
                    tsujo_hachu_flg 
                FROM
                    shohin_mst
                WHERE shohin_cd = :shohin_cd
                  AND bumon_cd  = :bumon_cd
END_OF_SQL
        , array('shohin_cd' => $shohin_cd
                ,'bumon_cd'  => $shohin_bumon_cd));
        if(count($select) > 0){
            $tujo_hachu_flg = $select[0]['tsujo_hachu_flg'];
        }
        return $tujo_hachu_flg;
    }
    
    /**
     *
     * 発注済みのチェック<br>
     * 発注済みの商品がある場合エラーメッセージを返す
     * 
     * <AUTHOR> Sugiyama
     * @since 2019/04/17
     * @param db $db データベース
     * @param string $seko_no 施行番号
     * @param array $dataCol 商品選択画面で変更があった商品データ
     * @return string エラーメッセージ
     */
    public function chkItemexHachuiOrder($db, $seko_no, $dataCol) {
        // エラーメッセージ変数
        $msg = "";
        // 商品選択画面で選択された商品を判定
        if(isset($dataCol['new']) && count($dataCol['new']) > 0) {
            foreach ($dataCol['new'] as $shohin) {
                // 商品コードが xxxxxxx--xxxx の形式になっているので分割
                // shohin_cd--shohin_kbn
                // $shohin_cd[0] = shohin_cd
                // $shohin_cd[1] = ""
                // $shohin_cd[2] = shohin_kbn
                $shohin_cd = explode('-',$shohin['shohincd']);
                if(isset($shohin_cd[0]) && strlen($shohin_cd[0]) > 0){
                    // 施行発注情報を取得
                    $select = $db->easySelect( <<< END_OF_SQL
                        SELECT  
                            SHI.shohin_cd,          -- 商品CD
                            SHI.shohin_nm,          -- 商品名
                            SHI.order_flg           -- 発注済みフラグ
                        FROM 
                            seko_hachu_info SHI     -- 施行発注情報
                        WHERE 
                            SHI.data_kbn IN (1,2)                   -- データ区分 1:葬儀, 2:法事
                        AND SHI.seko_no         = :seko_no          -- 施行番号
                        AND SHI.dai_bunrui_cd   = :dai_bunrui_cd    -- 大分類コード
                        AND SHI.chu_bunrui_cd   = :chu_bunrui_cd    -- 中分類コード
                        AND SHI.shohin_kbn      = :shohin_kbn       -- 商品区分
                        AND SHI.shohin_cd       = :shohin_cd        -- 商品コード
                        AND SHI.shohin_bumon_cd = :shohin_bumon_cd  -- 商品部門コード
                        AND SHI.delete_flg      = 0                 -- 削除フラグ
END_OF_SQL
                        , array(
                            'seko_no'           => $seko_no,
                            'dai_bunrui_cd'     => $shohin['daibunruicd'],
                            'chu_bunrui_cd'     => $shohin['chubunruicd'],
                            'shohin_kbn'        => $shohin['shohinkbn'],
                            'shohin_bumon_cd'   => $shohin['shohinbumoncd'],
                            'shohin_cd'         => $shohin_cd[0],
                        )
                    );
                    // 施行発注情報をループしてチェックする
                    // 同じ商品が複数ある場合、どれかが発注済みの場合エラーメッセージを出す
                    foreach ($select as $hachuInfo) {
                        if( isset($hachuInfo['order_flg']) && $hachuInfo['order_flg'] == 1 ){
                            $msg = '発注済みの商品が有る為、保存することができません。ページをもう一度読み込んでください。(' . $hachuInfo['shohin_nm'] .')';
                            return  $msg;
                        }
                    }
                }
            }
        }
        return  $msg;
    }
    /**
     *
     * 発注済みのチェック（発注済みがある場合は、エラーとする。画面をリフレッシュしないで変更できるため）
     *
     * <AUTHOR> Kayo
     * @since 2018/12/21
     * @param db $db データベース
     * @param string $seko_no 施行番号
     * @param array $dataCol グリッドデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @return string エラーメッセージ
     */
    public function chkHachuiOrder($db, $seko_no, $dataCol, $dataTrnDelCol) {
        $msg =  null;
        foreach ( $dataCol as $rec ) {
            if (!isset($rec['msi_no'])) {
                continue;
            }
            $select = $db->easySelect( <<< END_OF_SQL
            SELECT  shohin_nm,                                   -- 商品名
                    order_flg        
            FROM seko_hachu_info
            WHERE   delete_flg=0
            AND     seko_no         = :seko_no
			AND     data_kbn	IN (1,2)
            AND     jc_msi_no       = :msi_no
            AND     hachu_no_moto IS NULL
            AND     delete_flg      = 0
END_OF_SQL
                , array('seko_no'   =>$seko_no,
                        'msi_no'    =>$rec['msi_no'],
                        ));
            if (count($select) <= 0)    {
                continue;
            }
            if ($rec['order_flg'] == 0 || strlen($rec['order_flg']) <= 0) {
                if ($select[0]['order_flg'] == 1)  {
                    if (strlen($msg) <= 0)  {
                        $msg = '発注済みの商品が有る為、保存することができません。ページをもう一度読み込んでください。';
                    }
                    $msg .= '(' . $select[0]['shohin_nm'] .')';
                    return  $msg;
                }
            } 
        }
        foreach ( $dataTrnDelCol as $rec ) {
            if (!isset($rec['msi_no'])) {
                continue;
            }
            $select = $db->easySelect( <<< END_OF_SQL
            SELECT  shohin_nm,                                   -- 商品名
                    order_flg        
            FROM seko_hachu_info
            WHERE   delete_flg=0
            AND     seko_no         = :seko_no
			AND     data_kbn	IN (1,2)
            AND     jc_msi_no       = :msi_no
            AND     hachu_no_moto IS NULL
            AND     delete_flg      = 0
END_OF_SQL
                , array('seko_no'   =>$seko_no,
                        'msi_no'    =>$rec['msi_no'],
                        ));
            if (count($select) <= 0)    {
                continue;
            }
            if ($rec['order_flg'] == 0 || strlen($rec['order_flg']) <= 0) {
                if ($select[0]['order_flg'] == 1)  {
                    if (strlen($msg) <= 0)  {
                        $msg = '発注済みの商品が有る為、保存することができません。ページをもう一度読み込んでください。';
                    }
                    $msg .= '(' . $select[0]['shohin_nm'] .')';
                    return  $msg;
                }
            } 
        }
        return  $msg;
    }    
}
