<?php
  /**
   * DataMapper_Pdf0701
   *
   * PDF出力 商品区分別売上一覧 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/12
   * @filesource 
   */

  /**
   * PDF出力 商品区分別売上一覧 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/12
   */
class DataMapper_Pdf0701 extends DataMapper_Abstract
{
	// 出力順ID
	const ORDERKBN_SEKONO = 1;	// 施行番号
	const ORDERKBN_SEKYUNO = 2;	// 請求先コード


	/**
     * PDF出力 商品区分別売上一覧 取得(商品区分別)
     * <AUTHOR> Sato
     * @since      2014/05/12
     * @param Msi_Sys_Db $db
     * @param type $ymd_st
     * @param type $ymd_ed
     * @param type $bum_ary 部門配列
     * @param type $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function find($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
	,T1.syukei_kbn		
    ,T1.tanto_cd
    ,T1.seko_tanto_cd
    ,T1.bumon_cd
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,CASE WHEN  T1.juchu_suryo = 0 THEN 
            0
        ELSE    
            round(T1.uri_prc / T1.juchu_suryo)
        END        AS  uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
	,T1.nebiki_prcx		
	,T1.gojokai_nebiki_prc	-- 互助会値引額	2016/12/22 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,tan.tanto_nm
    ,tan2.tanto_nm	AS	seko_tanto_nm
    ,sho_kbn.shohin_kbn_nm
  FROM (
    SELECT
         0                  AS  record_kbn
		,0					AS	syukei_kbn		
        ,h.tanto_cd         AS  tanto_cd        -- 2015/12/23 UPD Kayo
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
				k.seko_tanto_cd
			ELSE
				NULL
			END				AS seko_tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,d.shohin_cd
        ,d.shohin_nm    
        ,SUM(d.juchu_suryo)       AS  juchu_suryo
        ,0                        AS  uri_tnk         
        ,SUM(d.uri_prc)           AS  uri_prc
        ,SUM(d.nebiki_prc)        AS  nebiki_prc   
        ,SUM(d.nebiki_prc)        AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)         AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY          
         h.tanto_cd          -- 2015/12/23 UPD Kayo
		,k.seko_tanto_cd
		,h.data_kbn		
		,d.bumon_cd
		,d.shohin_kbn
		,d.shohin_cd
		,d.shohin_nm    
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
   UNION ALL
        -- 合計
    SELECT
         0                   AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,'99999999'          AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY   d.bumon_cd
                ,d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,d.bumon_cd
        ,d.shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd, d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,d.bumon_cd
        ,NULL				 AS	 shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,'999999'            AS  bumon_cd
        ,d.shohin_kbn        AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,'999999'            AS  bumon_cd
        ,NULL				 AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN tanto_mst tan2 
      ON T1.seko_tanto_cd = tan2.tanto_cd 
      AND tan2.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
ORDER BY
		  T.bumon_cd
        , T.record_kbn
        , T.shohin_kbn
  		, T.syukei_kbn		
		, T.uri_prc DESC
        , T.tanto_cd
END_OF_SQL
            );

        return $select;
    }
	/**
     * PDF出力 商品区分別売上一覧 取得(商品区分別)CSV出力用
     * <AUTHOR> Kayo
     * @since      2021/07/25
     * @param Msi_Sys_Db $db
     * @param type $ymd_st
     * @param type $ymd_ed
     * @param type $bum_ary 部門配列
     * @param type $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function findCsv($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
	,T1.syukei_kbn		
    ,T1.tanto_cd
    ,T1.seko_tanto_cd
    ,T1.bumon_cd
	,T1.uri_den_no
	,T1.seko_no
	,T1.sekyu_nm
	,T1.souke_nm
	,T1.k_nm        -- 故人名
	,T1.m_nm		-- 喪主名
	,T1.m_zoku_nm	-- 続柄名
    ,T1.basho_nm    -- 場所名            
	,T1.m_zoku_nm			-- 続柄名
    ,T1.juchu_ymd
	,T1.data_kbn_nml			
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,CASE WHEN  T1.juchu_suryo = 0 THEN 
            0
        ELSE    
            round(T1.uri_prc / T1.juchu_suryo)
        END        AS  uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
	,T1.nebiki_prcx		
	,T1.gojokai_nebiki_prc	-- 互助会値引額	2016/12/22 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,tan.tanto_nm
    ,tan2.tanto_nm	AS	seko_tanto_nm
    ,sho_kbn.shohin_kbn_nm
  FROM (
    SELECT
         0                  AS  record_kbn
		,0					AS	syukei_kbn		
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
                k.uketuke_tanto_cd         
            ELSE
                h.tanto_cd         
            END AS  tanto_cd        -- 2015/12/23 UPD Kayo
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
				k.seko_tanto_cd
			ELSE
				NULL
			END				AS seko_tanto_cd
        ,d.bumon_cd
		,h.uri_den_no
		,CASE	WHEN h.data_kbn = 3	THEN
				h.uri_den_no
			ELSE 	
				h.seko_no
			END				AS	seko_no
		,h.sekyu_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.souke_nm
			END 	AS	souke_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.k_nm
			END 	AS	k_nm        -- 故人名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.m_nm
			END 	AS	m_nm		-- 喪主名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				zoku.kbn_value_lnm                                  
			END 	AS	m_zoku_nm	-- 続柄名
        ,sn.basho_nm                -- 場所名        
		,TO_CHAR(CASE WHEN h.data_kbn IN (1,2,4,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') AS	juchu_ymd			
		,CASE	WHEN h.data_kbn = 1	THEN
				'1：葬儀'
			WHEN h.data_kbn		= 2	THEN
				'2：法事'
			WHEN h.data_kbn		= 3	THEN
				'3：単品'
			WHEN h.data_kbn		= 4	THEN
				 '4：別注品'
			ELSE '5：生前依頼'	END	AS	data_kbn_nml
        ,d.shohin_kbn
        ,d.shohin_cd
        ,d.shohin_nm    
        ,SUM(d.juchu_suryo)       AS  juchu_suryo
        ,0                        AS  uri_tnk         
        ,SUM(d.uri_prc)           AS  uri_prc
        ,SUM(d.nebiki_prc)        AS  nebiki_prc   
        ,SUM(d.nebiki_prc)        AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)         AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
    FROM uriage_denpyo h
    LEFT JOIN uriage_denpyo_msi d 
		ON    h.uri_den_no = d.uri_den_no 
		AND   0            = d.delete_flg
	LEFT JOIN seko_kihon_info k
		ON    h.seko_no    = k.seko_no
		AND   0            = k.delete_flg      
	LEFT JOIN code_nm_mst zoku 
		ON zoku.code_kbn = '0190' 
		AND k.m_zoku_kbn = zoku.kbn_value_cd_num 
		AND zoku.delete_flg = 0
    LEFT JOIN seko_nitei sn
        ON k.seko_no = sn.seko_no
        AND 7        = sn.nitei_kbn
        AND 0        = sn.delete_flg    
	WHERE $where
      GROUP BY          
         h.tanto_cd          -- 2015/12/23 UPD Kayo
        ,k.uketuke_tanto_cd 
		,k.seko_tanto_cd
		,h.data_kbn		
		,d.bumon_cd
		,h.uri_den_no
		,h.seko_no
		,h.sekyu_nm
		,k.souke_nm
		,k.k_nm
		,k.m_nm
		,zoku.kbn_value_lnm
        ,sn.basho_nm          -- 場所名    
		,k.sougi_ymd
		,h.keijo_ymd
		,h.juchu_ymd
		,d.juchu_ymd
		,d.shohin_kbn
		,d.shohin_cd
		,d.shohin_nm    
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
   UNION ALL
        -- 合計
    SELECT
         0                   AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,NULL				 AS	 bumon_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
		,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm        -- 故人名
		,NULL				 AS	 m_nm		-- 喪主名
		,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS  data_kbn_nml			
        ,d.shohin_kbn
        ,'99999999'          AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY   d.bumon_cd
                ,d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,d.bumon_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
		,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm        -- 故人名
		,NULL				 AS	 m_nm		-- 喪主名
		,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS  data_kbn_nml			
        ,d.shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd, d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,d.bumon_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
		,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm        -- 故人名
		,NULL				 AS	 m_nm		-- 喪主名
		,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS  data_kbn_nml			
        ,NULL				 AS	 shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.bumon_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,0					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,'999999'            AS  bumon_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
		,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm        -- 故人名
		,NULL				 AS  m_nm		-- 喪主名
		,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS  data_kbn_nml			
        ,d.shohin_kbn        AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      GROUP BY d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,1					 AS	 syukei_kbn		
        ,NULL                AS  tanto_cd
        ,NULL				 AS  seko_tanto_cd
        ,'999999'            AS  bumon_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
		,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
		,NULL				 AS	 m_nm		-- 喪主名
		,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS  data_kbn_nml			
        ,NULL				 AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)	 AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
      LEFT JOIN seko_kihon_info k
          ON    h.seko_no    = k.seko_no
          AND   0            = k.delete_flg      
      WHERE $where
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN tanto_mst tan2 
      ON T1.seko_tanto_cd = tan2.tanto_cd 
      AND tan2.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
ORDER BY
		  T.bumon_cd
        , T.record_kbn
		, T.seko_no
		, T.data_kbn_nml		
		, T.uri_den_no		
        , T.shohin_kbn
  		, T.syukei_kbn		
		, T.uri_prc DESC
        , T.tanto_cd
END_OF_SQL
            );

        return $select;
    }
	/**
     * PDF出力 商品区分別売上一覧 取得(商品区分別)CSV出力用
     * <AUTHOR> Kayo
     * @since      2021/07/25
     * @param Msi_Sys_Db $db
     * @param type $ymd_st
     * @param type $ymd_ed
     * @param type $bum_ary 部門配列
     * @param type $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function findCsvSalesforce($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary,true);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
	,T1.syukei_kbn		
    ,T1.tanto_cd
    ,T1.seko_tanto_cd
    ,T1.bumon_cd
	,T1.uri_den_no
	,T1.seko_no
	,T1.sekyu_nm
	,T1.souke_nm
	,T1.k_nm        -- 故人名
	,T1.m_nm		-- 喪主名
	,T1.m_zoku_nm	-- 続柄名
    ,T1.basho_nm    -- 場所名            
	,T1.m_zoku_nm	-- 続柄名
    ,T1.juchu_ymd
    ,T1.uriage_msi_id
    ,T1.uriage_id
    ,T1.update_ymd
	,T1.data_kbn_nml			
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,CASE WHEN  T1.juchu_suryo = 0 THEN 
            0
        ELSE    
            round(T1.uri_prc / T1.juchu_suryo)
        END        AS  uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
	,T1.nebiki_prcx		
	,T1.gojokai_nebiki_prc	-- 互助会値引額	2016/12/22 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,tan.tanto_nm
    ,tan2.tanto_nm	AS	seko_tanto_nm
    ,sho_kbn.shohin_kbn_nm
    ,T1.delete_flg      -- 削除フラグ        
  FROM (
    SELECT
         0                  AS  record_kbn
		,0					AS	syukei_kbn		
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
                k.uketuke_tanto_cd         
            ELSE
                h.tanto_cd
            END         AS  tanto_cd        -- 2015/12/23 UPD Kayo
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
				k.seko_tanto_cd
			ELSE
				NULL
			END				AS seko_tanto_cd
        ,d.bumon_cd
		,h.uri_den_no
		,CASE	WHEN h.data_kbn = 3	THEN
				h.uri_den_no
			ELSE 	
				h.seko_no
			END				AS	seko_no
		,h.sekyu_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.souke_nm
			END 	AS	souke_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.k_nm
			END 	AS	k_nm        -- 故人名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.m_nm
			END 	AS	m_nm		-- 喪主名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				zoku.kbn_value_lnm                                  
			END 	AS	m_zoku_nm	-- 続柄名
        ,sn.basho_nm                -- 場所名        
		,TO_CHAR(CASE WHEN h.data_kbn IN (1,2,4,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') AS	juchu_ymd
        ,TO_CHAR(h._mod_ts,'YYYY/MM/DD HH24:MI:SS')	|| h.uri_den_no || TO_CHAR(d.msi_no,'00000') AS uriage_msi_id
		,TO_CHAR(h._mod_ts,'YYYY/MM/DD HH24:MI:SS')	|| h.uri_den_no   AS uriage_id
		,TO_CHAR(h._mod_ts,'YYYY/MM/DD HH24:MI:SS')					  AS update_ymd
		,CASE	WHEN h.data_kbn = 1	THEN
				'1：葬儀'
			WHEN h.data_kbn		= 2	THEN
				CASE WHEN k.sk_houyo_kbn = 3 THEN
					'3:初盆'
				ELSE
					'2：法事'
				END
			WHEN h.data_kbn		= 3	THEN
				'4：単品'
			WHEN h.data_kbn		= 4	THEN
				'5：別注品'
			ELSE 
				'6：生前依頼'
			END					AS	data_kbn_nml
        ,d.shohin_kbn
        ,d.shohin_cd
        ,d.shohin_nm    
        ,SUM(d.juchu_suryo)       AS  juchu_suryo
        ,0                        AS  uri_tnk         
        ,SUM(d.uri_prc)           AS  uri_prc
        ,SUM(d.nebiki_prc)        AS  nebiki_prc   
        ,SUM(d.nebiki_prc)        AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)         AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
        ,CASE WHEN h.delete_flg = 1 THEN
                1
            ELSE
                d.delete_flg
            END         AS delete_flg       -- 削除フラグ        
    FROM uriage_denpyo h
    LEFT JOIN uriage_denpyo_msi d 
		ON    h.uri_den_no = d.uri_den_no 
	LEFT JOIN seko_kihon_info k
		ON    h.seko_no    = k.seko_no
		AND   0            = k.delete_flg      
	LEFT JOIN code_nm_mst zoku 
		ON zoku.code_kbn = '0190' 
		AND k.m_zoku_kbn = zoku.kbn_value_cd_num 
		AND zoku.delete_flg = 0
    LEFT JOIN seko_nitei sn
        ON k.seko_no = sn.seko_no
        AND 7        = sn.nitei_kbn
        AND 0        = sn.delete_flg    
	WHERE $where
      GROUP BY          
         h.tanto_cd          -- 2015/12/23 UPD Kayo
        ,k.uketuke_tanto_cd
		,k.seko_tanto_cd
		,h.data_kbn
		,d.bumon_cd
		,h.uri_den_no
		,h.seko_no
		,h.sekyu_nm
		,k.souke_nm
		,k.k_nm
		,k.m_nm
		,k.sk_houyo_kbn		
		,zoku.kbn_value_lnm
        ,sn.basho_nm          -- 場所名    
		,k.sougi_ymd
		,h.keijo_ymd
		,h.juchu_ymd
        ,h._mod_ts 
        ,d.msi_no    
		,d.juchu_ymd
		,d.shohin_kbn
		,d.shohin_cd
		,d.shohin_nm
        ,h.delete_flg        
        ,d.delete_flg        
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN tanto_mst tan2 
      ON T1.seko_tanto_cd = tan2.tanto_cd 
      AND tan2.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
ORDER BY
		  T.bumon_cd
        , T.record_kbn
		, T.seko_no
		, T.data_kbn_nml		
		, T.uri_den_no		
        , T.shohin_kbn
  		, T.syukei_kbn		
		, T.uri_prc DESC
        , T.tanto_cd
END_OF_SQL
            );

        return $select;
    }
    
    /**
     * PDF出力 商品区分別売上一覧 取得(施工先別)
     * <AUTHOR> Kayo
     * @since      2014/08/28
     * @param Msi_Sys_Db $db
     * @param string $ymd_st
     * @param string $ymd_ed
     * @param array $bum_ary 部門配列
     * @param array $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @param string $order_kbn		出力順
     * @return array 該当データがない場合はarray()を返す
     */
    public static function findSeko($db, $ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $order_kbn)
    {
        $where = self::getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary);
        $order = self::getOrder($order_kbn);
                 
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
     T1.record_kbn
	,T1.syukei_kbn		
	,T1.data_kbn
    ,T1.bumon_cd
	,T1.tanto_cd
	,T1.seko_tanto_cd				
	,T1.uri_den_no
	,T1.seko_no
	,T1.sekyu_nm
    ,T1.souke_nm
	,T1.k_nm                -- 故人名
	,T1.m_nm				-- 喪主名
	,T1.m_zoku_nm			-- 続柄名
    ,T1.basho_nm            -- 場所名        
	,T1.juchu_ymd			
	,T1.data_kbn_nm
	,T1.data_kbn_nml
	,T1.data_sbt
	,T1.msi_no
    ,T1.shohin_kbn
    ,T1.shohin_cd
    ,T1.shohin_nm    
    ,T1.juchu_suryo
    ,T1.uri_tnk         
    ,T1.uri_prc
    ,T1.nebiki_prc
	,T1.nebiki_prcx		
	,T1.gojokai_nebiki_prc -- 互助会値引額	2016/12/22 ADD Kayo
    ,T1.hoshi_prc
    ,T1.gokei_uri_prc
    ,bum.bumon_lnm
    ,tan.tanto_nm
	,tan2.tanto_nm	AS	seko_tanto_nm
    ,sho_kbn.shohin_kbn_nm
  FROM (
    SELECT
         0                  AS  record_kbn
		,0					AS	syukei_kbn		
		,h.data_kbn
		,h.bumon_cd
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
                k.uketuke_tanto_cd         
            ELSE
                h.tanto_cd
            END        AS  tanto_cd        -- 2015/12/23 UPD Kayo
        ,CASE	WHEN h.data_kbn IN (1,2)	THEN
				k.seko_tanto_cd
			ELSE
				NULL
			END				AS seko_tanto_cd
		,h.uri_den_no
		,CASE	WHEN h.data_kbn = 3	THEN
				h.uri_den_no
			ELSE 	
				h.seko_no
			END				AS	seko_no
		,h.sekyu_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.souke_nm
			END 	AS	souke_nm
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.k_nm
			END 	AS	k_nm        -- 故人名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				k.m_nm
			END 	AS	m_nm		-- 喪主名
        ,CASE	WHEN h.data_kbn = 3	THEN
				null
			ELSE 	
				zoku.kbn_value_lnm                                  
			END 	AS	m_zoku_nm	-- 続柄名
        ,sn.basho_nm                -- 場所名        
		,TO_CHAR(CASE WHEN h.data_kbn IN (1,2,4,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') AS	juchu_ymd			
		,CASE	WHEN h.data_kbn = 1	THEN
				'(葬)'
			WHEN h.data_kbn = 2	THEN
				'(法)'
			WHEN h.data_kbn = 3	THEN
				'(単)'
			WHEN h.data_kbn = 4	THEN
				 '(別)'
			ELSE '(生)'	END	AS	data_kbn_nm
		,CASE	WHEN h.data_kbn = 1	THEN
				'1：葬儀'
			WHEN h.data_kbn		= 2	THEN
				'2：法事'
			WHEN h.data_kbn		= 3	THEN
				'3：単品'
			WHEN h.data_kbn		= 4	THEN
				 '4：別注品'
			ELSE '5：生前依頼'	END	AS	data_kbn_nml
		,d.data_sbt
		,d.msi_no
		,d.shohin_kbn
		,d.shohin_cd
		,d.shohin_nm    
		,d.juchu_suryo            AS  juchu_suryo
		,d.uri_tnk                AS  uri_tnk         
		,d.uri_prc                AS  uri_prc
		,d.nebiki_prc             AS  nebiki_prc   
        ,d.nebiki_prc			  AS  nebiki_prcx
		,d.gojokai_nebiki_prc	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
		,d.hoshi_prc              AS  hoshi_prc
		,d.uri_prc + d.nebiki_prc + d.hoshi_prc  AS  gokei_uri_prc
      FROM uriage_denpyo h
        LEFT JOIN uriage_denpyo_msi d 
            ON    h.uri_den_no = d.uri_den_no 
            AND   0            = d.delete_flg
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
		LEFT JOIN code_nm_mst zoku 
			  ON zoku.code_kbn = '0190' 
			  AND k.m_zoku_kbn = zoku.kbn_value_cd_num 
			  AND zoku.delete_flg = 0
        LEFT JOIN seko_nitei sn
            ON k.seko_no = sn.seko_no
            AND 7        = sn.nitei_kbn
            AND 0        = sn.delete_flg    
      WHERE $where
      AND (d.juchu_suryo <> 0
      OR   d.uri_prc     <> 0           
      OR   d.nebiki_prc  <> 0           
      OR   d.hoshi_prc   <> 0)           
   UNION ALL
    -- 施工合計
    SELECT
         0                   AS  record_kbn
		,1					 AS	 syukei_kbn		
		,h.data_kbn			 AS	 data_kbn
        ,d.bumon_cd
        ,h.tanto_cd          AS  tanto_cd        -- 2015/12/23 UPD Kayo
        ,CASE	WHEN h.data_kbn = 1	THEN
				k.seko_tanto_cd
			ELSE
				NULL
			END				AS seko_tanto_cd
		,h.uri_den_no
		,CASE	WHEN h.data_kbn = 3	THEN
				h.uri_den_no
			ELSE 	
				h.seko_no
			END				 AS	 seko_no
		,NULL				 AS	 sekyu_nm
        ,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
        ,NULL				 AS	 m_nm		-- 喪主名
        ,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS	 data_kbn_nm
		,NULL				 AS	 data_kbn_nml
		,9					 AS  data_sbt
		,0					 AS	 msi_no
        ,NULL				 AS  shohin_kbn
        ,'99999999'          AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON    h.uri_den_no = d.uri_den_no 
          AND   0            = d.delete_flg
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
      WHERE $where
      GROUP BY   d.bumon_cd
				,h.uri_den_no
				,h.seko_no
                ,h.tanto_cd             -- 2015/12/23 UPD Kayo
				,k.seko_tanto_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,0					 AS	 syukei_kbn		
		,0					 AS	 data_kbn
        ,d.bumon_cd
		,NULL				 AS	 tanto_cd
        ,NULL				 AS  seko_tanto_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS  sekyu_nm
        ,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
        ,NULL				 AS	 m_nm		-- 喪主名
        ,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS	 data_kbn_nm
		,NULL				 AS	 data_kbn_nml
		,9					 AS  data_sbt
		,0					 AS	 msi_no
        ,d.shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
      WHERE $where
      GROUP BY d.bumon_cd, d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 部門合計
    SELECT
         31                  AS  record_kbn
		,1					 AS	 syukei_kbn		
		,0					 AS	 data_kbn
        ,d.bumon_cd
		,NULL				 AS	 tanto_cd
        ,NULL				 AS  seko_tanto_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS  sekyu_nm
        ,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
        ,NULL				 AS	 m_nm		-- 喪主名
        ,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS	 data_kbn_nm
		,NULL				 AS	 data_kbn_nml
		,9					 AS  data_sbt
		,0					 AS	 msi_no
        ,NULL				 AS	 shohin_kbn
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
      WHERE $where
      GROUP BY d.bumon_cd
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,0					 AS	 syukei_kbn		
		,0					 AS	 data_kbn
        ,'999999'            AS  bumon_cd
		,NULL				 AS	 tanto_cd
        ,NULL				 AS  seko_tanto_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS  sekyu_nm
        ,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
        ,NULL				 AS	 m_nm		-- 喪主名
        ,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS	 data_kbn_nm
		,NULL				 AS	 data_kbn_nml
		,9					 AS  data_sbt
		,0					 AS	 msi_no
        ,d.shohin_kbn        AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
      WHERE $where
      GROUP BY d.shohin_kbn
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
    UNION ALL
    -- 総合計 合計
    SELECT
         32                  AS  record_kbn
		,1					 AS	 syukei_kbn		
		,0					 AS	 data_kbn
        ,'999999'            AS  bumon_cd
		,NULL				 AS	 tanto_cd
        ,NULL				 AS  seko_tanto_cd
		,NULL				 AS	 uri_den_no
		,NULL				 AS	 seko_no
		,NULL				 AS  sekyu_nm
        ,NULL				 AS	 souke_nm
		,NULL				 AS	 k_nm       -- 故人名
        ,NULL				 AS	 m_nm		-- 喪主名
        ,NULL				 AS	 m_zoku_nm	-- 続柄名
        ,NULL                AS  basho_nm   -- 場所名        
		,NULL				 AS	 juchu_ymd			
		,NULL				 AS	 data_kbn_nm
		,NULL				 AS	 data_kbn_nml
		,9					 AS  data_sbt
		,0					 AS	 msi_no
        ,NULL				 AS  shohin_kbn   
        ,NULL                AS  shohin_cd
        ,NULL                AS  shohin_nm    
        ,SUM(d.juchu_suryo)  AS  juchu_suryo
        ,NULL                AS  uri_tnk         
        ,SUM(d.uri_prc)      AS  uri_prc   
        ,SUM(d.nebiki_prc)   AS  nebiki_prc
        ,SUM(d.nebiki_prc)   AS  nebiki_prcx
		,SUM(d.gojokai_nebiki_prc)	  AS gojokai_nebiki_prc -- 互助会値引額	2016/03/20 ADD Kayo
        ,SUM(d.hoshi_prc)    AS  hoshi_prc
        ,SUM(d.uri_prc + d.nebiki_prc + d.hoshi_prc)  AS  gokei_uri_prc
      FROM uriage_denpyo h
      LEFT JOIN uriage_denpyo_msi d 
          ON h.uri_den_no = d.uri_den_no 
          AND d.delete_flg = 0
        LEFT JOIN seko_kihon_info k 
            ON h.seko_no	=	k.seko_no 
            AND 0			=	k.delete_flg
      WHERE $where
      HAVING SUM(d.juchu_suryo) <> 0
      OR    SUM(d.uri_prc)     <> 0           
      OR    SUM(d.nebiki_prc)  <> 0           
      OR    SUM(d.hoshi_prc)   <> 0           
  ) T1
  LEFT JOIN bumon_mst bum 
      ON T1.bumon_cd = bum.bumon_cd 
      AND bum.delete_flg = 0
  LEFT JOIN tanto_mst tan 
      ON T1.tanto_cd = tan.tanto_cd 
      AND tan.delete_flg = 0
  LEFT JOIN tanto_mst tan2 
      ON T1.seko_tanto_cd = tan2.tanto_cd 
      AND tan2.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn 
      ON T1.shohin_kbn = sho_kbn.shohin_kbn 
      AND sho_kbn.delete_flg = 0
) T
$order
END_OF_SQL
            );

        return $select;
    }

    /**
     * WHERE句を取得
	 * 
     * <AUTHOR> Sato
     * @since      2014/05/14
     * @version 2015/11/13 コンバート区分の参照を追加、別注品が抽出されない件を修正 Kayo
     * @param string $ymd_st
     * @param string $ymd_ed
     * @param string $bum_ary 部門配列
     * @param string $sho_kbn_ary
     * @param array $daiBunrui_ary	大分類
     * @return string WHERE句
     */
    private static function getWhere($ymd_st, $ymd_ed, $bum_ary, $sho_kbn_ary, $daiBunrui_ary, $csv = false)
    {
        $where = null;

        // 期間
        if (isset($ymd_st) && isset($ymd_ed)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
                COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') BETWEEN '".$ymd_st."' AND '".$ymd_ed."'";
        } else if (isset($ymd_st)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') >= '".$ymd_st."'";
        } else if (isset($ymd_ed)) {
            $where_ymd = "TO_CHAR(CASE WHEN h.data_kbn IN (1,2,5) THEN -- 1：葬儀 2：法事 3：単品 4：別注品 5：生前依頼
            COALESCE(k.sougi_ymd,COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd)))
		 WHEN h.data_kbn = 4 THEN
			COALESCE(h.keijo_ymd, COALESCE(h.juchu_ymd, d.juchu_ymd))	
         ELSE
            COALESCE(h.keijo_ymd, h.juchu_ymd)
         END,'YYYY/MM/DD') <= '".$ymd_ed."'";
        } else {
            $where_ymd = null;
        }
        if (isset($where_ymd)) {
            if (isset($where)) { $where .= ' AND '; }
            $where .= $where_ymd;
        }

        // 部門
        if (isset($bum_ary) && count($bum_ary) > 0) {
            $ary = array();
            foreach ($bum_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.bumon_cd IN (".implode(',', $ary).")";
        }

        // 商品区分
        if (isset($sho_kbn_ary) && count($sho_kbn_ary) > 0) {
            $ary = array();
            foreach ($sho_kbn_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.shohin_kbn IN (".implode(',', $ary).")";
        }
        
        // 大分類
        if (isset($daiBunrui_ary) && count($daiBunrui_ary) > 0) {
            $ary = array();
            foreach ($daiBunrui_ary as $value) {
                $ary[] = "'".$value."'";
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "d.dai_bunrui_cd IN (".implode(',', $ary).")";
        }

        if (!$csv)  {   
            if (isset($where)) { $where .= ' AND '; }
            $where .= 'h.delete_flg = 0
                    AND h.convert_kbn = 0
            ';
        }    
        return $where;
    }

	/**
     * ORDER句を取得
	 * 
     * <AUTHOR> Kayo
     * @since      2014/08/17
     * @param string $order_kbn
     * @return string WORDER句
     */
    private static function getOrder($order_kbn) {
        $order = 'ORDER BY
					  T.bumon_cd
					, T.record_kbn
					, T.data_kbn
			';		
        switch ($order_kbn) {
		case self::ORDERKBN_SEKONO:
			$order .= '
				, T.seko_no
				, T.uri_den_no
			';	
			break;
		case self::ORDERKBN_SEKYUNO:
			$order .= '
				, T.uri_den_no
				, T.seko_no
			';	
			break;
        }
		$order .= '
			, T.data_sbt
			, T.syukei_kbn		
			, T.shohin_kbn
			, T.msi_no
		';	
        return $order;
    }
}
