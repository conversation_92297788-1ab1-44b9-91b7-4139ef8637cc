<?php

//require_once __DIR__. '/JuchuhenkoSubAbstract.php';

/**
 * <PERSON>chu_JuchuhenkoController
 *
 * 受注変更 コントローラクラス
 *
 * @category   App
 * @package    controllers\Juchuhenko
 * <AUTHOR> Sai
 * @since      2014/01/22
 * @version    2016/06/10  mihara sosogirei1Action,sosogirei2Action,sosogirei3Action 追加
 * @version    2019/05/21  mihara 軽減税率対応
 * @filesource 
 */

/**
 * 受注変更 コントローラクラス
 *
 * @category   App
 * @package    controllers\Juchuhenko
 * <AUTHOR>
 * @since      2014/01/17
 */
class Juchu_JuchuhenkoController extends Zend_Controller_Action {

    /**
     * お客様情報初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/12
     */
    public function customerinfoAction() {
        App_Utils::setCustomerinfo($this);
    }

    /**
     * お客様情報初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/12
     */
    public function customerinfo2Action() {
        App_Utils::setCustomerinfo($this, 'add.juchu');
    }

    /**
     * お客様情報新規作成アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/17
     */
    public function newAction() {
        // 施行番号クリア
        App_Utils::setSessionData('seko_no', null);

        $req = $this->getRequest();
        $module = $req->getModuleName();
        $controller = $req->getControllerName();
        $this->_redirect($module . '/' . $controller . '/' . 'customerinfo');
        //$this->customerinfoAction();
    }

    /**
     * お客様情報初期情報取得(料理のみ)アクション 
     *
     * <AUTHOR> Sai
     * @since 2017/02/20
     */
    public function customerinforrAction() {
        App_Utils::setCustomerinfo($this, 'henko.juchuryori');
        App_Smarty::pushJsFile(['app/juchu.customerinfo.adj.js']);
    }

    /**
     * 壇払い 初期情報取得(料理のみ)アクション 
     *
     * <AUTHOR> Sai
     * @since 2017/02/20
     */
    public function danbarairrAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbarai();
        $this->setCommonInfoS($juchuhenko, 'henko.juchuryori');
        App_Smarty::pushJsFile(['app/juchu.juchuhenko.adj.js']);
    }

    /**
     * 壇払い(法事) 初期情報取得(料理のみ)アクション 
     *
     * <AUTHOR> Sai
     * @since 2017/02/20
     */
    public function danbaraihrrAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbaraiH();
        $this->setCommonInfoS($juchuhenko, 'henko.houjiryori');
        App_Smarty::pushJsFile(['app/juchu.juchuhenko.adj.js']);
    }

    /**
     * お客様情報保存アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/17
     */
    public function customerinfosaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->save($req);
        }
    }

    /**
     * お客様情報保存アクション(事前相談) 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/01
     */
    public function preconsultsaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->preconsultSave($req);
        }
    }

    /**
     * お客様情報保存アクション(オーダーメイド) 
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     */
    public function ordermadesaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->ordermadeSave($req);
        }
    }

    /**
     * お客様情報削除アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/02/27
     */
    public function customerinfodeleteAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->delete($req);
        }
    }

    /**
     * img取得 アクション
     *
     * <AUTHOR> Sai
     * @since 2014/02/24
     */
    public function imgAction() {

        $params = Msi_Sys_Utils::webInputs();
        $oid = $params['imgid'];
        $db = Msi_Sys_DbManager::getMyDb();
        $cont = $db->readBlobCont($oid);
        Msi_Sys_Utils::outBinary($cont); // , 'image/jpeg' );
    }

    /**
     * 故人名添付ファイル名登録処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/24
     */
    public function writeblobAction() {
        $req = $this->getRequest();
        if ($req->isPost() && isset($_FILES["file"])) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->writeBlob($_FILES["file"]["tmp_name"]);
        }
    }

    /**
     * タイムスケジュール初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/01/14
     */
    public function timescheduleAction() {
        App_Utils::setTimeschedule($this);
    }

    /**
     * タイムスケジュール初期情報取得アクション(PDF表示版) 
     *
     * <AUTHOR> Kayo
     * @since 2016/12/01
     */
    public function timeschedulepdfdispAction() {
        App_Utils::setTimeschedulePdfDisp($this);
    }

    /**
     * タイムスケジュール形式変更アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/01/14
     */
    public function tschangeAction() {
        $req = $this->getRequest();
        //$params = Msi_Sys_Utils::webInputs();
        //Msi_Sys_Utils::debug('params==>' . Msi_Sys_Utils::dump($params));

        if ($req->isPost()) {
            $timeschedule = new Juchu_JuchuTimeschedule();
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $timeschedule->change($dataApp);
        }
    }

    /**
     * タイムスケジュール保存アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/01/14
     */
    public function tssaveAction() {
        $req = $this->getRequest();
        $params = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug('params==>' . Msi_Sys_Utils::dump($params));

        if ($req->isPost()) {
            $timeschedule = new Juchu_JuchuTimeschedule();
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $dataDtlCol = Msi_Sys_Utils::json_decode($req->getPost('dataDtlColJson'));
            $timeschedule->save($dataApp, $dataDtlCol);
        }
    }

    /**
     * タイムスケジュール時間再計算アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/14
     */
    public function tsrecalctimeAction() {
        $req = $this->getRequest();
        $params = Msi_Sys_Utils::webInputs();
        if ($req->isPost()) {
            $timeschedule = new Juchu_JuchuTimeschedule();
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $timeschedule->recalctime($dataApp);
        }
    }

    /**
     * 葬送儀礼 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/01/20
     */
    public function sosogireiAction() {
        $juchuhenko = new Juchu_JuchuhenkoSosogirei();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 葬送儀礼 初期情報取得アクション 
     *
     * <AUTHOR> Mihara
     * @since 2016/06/10
     */
    public function sosogirei1Action() {
        $juchuhenko = new Juchu_JuchuhenkoSosogirei1();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 葬送儀礼 初期情報取得アクション 
     *
     * <AUTHOR> Mihara
     * @since 2016/06/10
     */
    public function sosogirei2Action() {
        $juchuhenko = new Juchu_JuchuhenkoSosogirei2();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 葬送儀礼 初期情報取得アクション 
     *
     * <AUTHOR> Mihara
     * @since 2016/06/10
     */
    public function sosogirei3Action() {
        $juchuhenko = new Juchu_JuchuhenkoSosogirei3();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 返礼品 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function henreiAction() {
        $juchuhenko = new Juchu_JuchuhenkoHenrei();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 生花返礼品 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/07/03
     */
    public function sekahenreiAction() {
        $juchuhenko = new Juchu_JuchuhenkoSekaHenrei();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 生花返礼品 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function sekahenreiaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSekaHenrei();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 宗教者・受付者返礼品 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/11
     */
    public function syukyohenreiAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyukyohenrei();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 宗教者・受付者返礼品 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function syukyohenreiaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyukyohenrei();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 料理 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function ryoriAction() {
        $juchuhenko = new Juchu_JuchuhenkoRyori();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 壇払い 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function danbaraiAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbarai();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 壇払い(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function danbaraihAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbaraiH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 壇払い 初期情報取得アクション 
     *
     * <AUTHOR> Kayoi
     * @since 2017/10/02
     */
    public function danbaraihrAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbaraiH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * ハート綜合石材 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function hartsekiziAction() {
        $juchuhenko = new Juchu_JuchuhenkoHartsekizi();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * ハート綜合石材 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function hartsekiziaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoHartsekizi();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 別途費用 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function bettoAction() {
        $juchuhenko = new Juchu_JuchuhenkoBetto();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 別途費用 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2017/09/27
     */
    public function bettorAction() {
        $juchuhenko = new Juchu_JuchuhenkoBettor();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 別途費用 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function bettoraddAction() {
        $juchuhenko = new Juchu_JuchuhenkoBettor();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 立替費用 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function tatekaeAction() {
        $juchuhenko = new Juchu_JuchuhenkoTatekae();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 立替費用 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/27
     */
    public function tatekaerAction() {
        $juchuhenko = new Juchu_JuchuhenkoTatekaer();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 立替費用 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function tatekaeraddAction() {
        $juchuhenko = new Juchu_JuchuhenkoTatekaer();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 値引き 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/03/17
     */
    public function nebikiAction() {
        $juchuhenko = new Juchu_JuchuhenkoNebiki();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 値引き 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/27
     */
    public function nebikirAction() {
        $juchuhenko = new Juchu_JuchuhenkoNebikir();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 値引き 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function nebikiraddAction() {
        $juchuhenko = new Juchu_JuchuhenkoNebikir();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 供花 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2017/09/26
     */
    public function kyokakumotsuAction() {
        $juchuhenko = new Juchu_JuchuhenkoKyoka();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * オプション 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2015/05/29
     */
    public function optionAction() {
        $juchuhenko = new Juchu_JuchuhenkoOption();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * オプション(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/10
     */
    public function optionhAction() {
        $juchuhenko = new Juchu_JuchuhenkoOptionH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 法要・墓参り(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/17
     */
    public function houyoAction() {
        $juchuhenko = new Juchu_JuchuhenkoHouyo();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 法要・墓参り(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/10/02
     */
    public function houyorAction() {
        $juchuhenko = new Juchu_JuchuhenkoHouyo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 返礼品(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function henreihAction() {
        $juchuhenko = new Juchu_JuchuhenkoHenreiH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 返礼品(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/10/02
     */
    public function henreihrAction() {
        $juchuhenko = new Juchu_JuchuhenkoHenreiH();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 壇払い(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function danbarairAction() {
        $juchuhenko = new Juchu_JuchuhenkoDanbaraiH();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 別途費用(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function bettohAction() {
        $juchuhenko = new Juchu_JuchuhenkoBettoH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 別途費用(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/10/02
     */
    public function bettohrAction() {
        $juchuhenko = new Juchu_JuchuhenkoBettoH();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 立替費用(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function tatekaehAction() {
        $juchuhenko = new Juchu_JuchuhenkoTatekaeH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 立替費用(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/10/02
     */
    public function tatekaehrAction() {
        $juchuhenko = new Juchu_JuchuhenkoTatekaeH();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 値引き(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function nebikihAction() {
        $juchuhenko = new Juchu_JuchuhenkoNebikiH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 値引き(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2014/07/11
     */
    public function nebikihrAction() {
        $juchuhenko = new Juchu_JuchuhenkoNebikiH();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 初期情報設定共通処理(葬送儀礼～値引き)
     *
     * <AUTHOR> Sai
     * @since 2014/02/12
     * @version 2017/02/xx Sai 料理のみメニュー対応のため $sidemenukey　を追加
     */
    private function setCommonInfo($juchuhenko, $sidemenukey = '') {
        // 初期データを取得する
        $jsonData = $juchuhenko->getInitData();
        $this->view->juchuhenko_json = $jsonData;
        $this->view->sidemenu_key = $sidemenukey;
        $this->_helper->viewRenderer->setScriptAction('juchuhenko');
        // 葬儀基本情報(ヘッダー)を設定する
        //$this->setHeader($sekoNo);
        // サイドメニューデータを設定する
        $this->setSidemenu($juchuhenko, $sidemenukey);
        // 利用 CSS 設定
        App_Smarty::pushCssFile(['app/sais.css', 'app/juchu.juchuhenko.css']);
        if (true) { // App_KeigenUtils::isKeigenSupport() ) { // mihara  軽減税率対応なら keigen
            App_Smarty::pushCssFile('app/juchu.juchuhenko.keigen.css'); // mihara  軽減税率対応
            App_Smarty::pushJsFile('app/lib.keigen_f02.js'); // これは app/juchu.estimate.js 等より前に呼ぶ
            App_Smarty::pushJsFile('app/juchu/juchu.juchuhenko.keigen.js');
        }
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile(['app/juchu.juchuhenko.js', 'app/sidemenu.js', 'app/juchu.juchuhenko.side.js']);
        App_Smarty::pushCssFile(['app/juchu.hansoprc.css']);
        App_Smarty::pushJsFile(['app/juchu.hansoprc.js']);
    }

    /**
     * サイドメニュー設定処理
     *
     * <AUTHOR> Sai
     * @since 2014/02/12
     * @param Juchu_JuchuhenkoAbstract $juchuhenko 受注変更 抽象クラス
     * @version 2017/02/xx Sai 料理のみメニュー対応のため $sidemenukey　を追加
     */
    private function setSidemenu($juchuhenko, $sidemenukey = '') {
        $sideMenuData = $juchuhenko->getSideMenuData(array(), $sidemenukey);
        $jsonSideMenuData = Msi_Sys_Utils::json_encode($sideMenuData);
        $this->view->juchuhenko_side_json = $jsonSideMenuData;
    }

    /**
     * コース商品名変更アクション (葬送儀礼～値引き)
     *
     * <AUTHOR> Sai
     * @since 2014/02/06
     */
    public function planchangeAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->change($dataApp);
        }
    }

    /**
     * 保存アクション  (葬送儀礼～値引き)
     *
     * <AUTHOR> Sai
     * @since 2014/01/28
     */
    public function denpyosaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            // グリッドデータ
            $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            // グリッドトラン削除データ
            $dataTrnDelCol = Msi_Sys_Utils::json_decode($req->getPost('datatrnDelColJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->save($dataApp, $dataCol, $dataTrnDelCol);
        }
    }

    /**
     * 保存アクション  (葬送儀礼～値引き)※顧客管理画面用(セレモ用)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/14
     */
    public function denpyosaveaddAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            // グリッドデータ
            $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            // グリッドトラン削除データ
            $dataTrnDelCol = Msi_Sys_Utils::json_decode($req->getPost('datatrnDelColJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->save($dataApp, $dataCol, $dataTrnDelCol);
        }
    }

    /**
     * 保存アクション  (セレモ入金処理)
     *
     * <AUTHOR> Kayo
     * @since 2017/10/16
     */
    public function nyukinsaveAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            // グリッドデータ
            $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            // グリッドトラン削除データ
            $dataTrnDelCol = Msi_Sys_Utils::json_decode($req->getPost('datatrnDelColJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->nyukinsave($dataApp, $dataCol, $dataTrnDelCol);
        }
    }

    /**
     * 保存アクション  (セレモ入金取消)
     *
     * <AUTHOR> Kayo
     * @since 2017/10/21
     */
    public function nyukincancelAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            // グリッドデータ
            $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            // グリッドトラン削除データ
            $dataTrnDelCol = Msi_Sys_Utils::json_decode($req->getPost('datatrnDelColJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->nyukincancel($dataApp, $dataCol, $dataTrnDelCol);
        }
    }

    /**
     * 料理確定アクション  (ceremo)
     *
     * <AUTHOR> Sai
     * @since 2017/03/09
     */
    public function ryorifixAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->ryorifix($dataApp);
        }
    }

    /**
     * 料理確定取消アクション  (ceremo)
     *
     * <AUTHOR> Sai
     * @since 2017/03/09
     */
    public function ryoriunfixAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $className = $dataApp["clsass_name"];
            $juchuhenko = new $className();
            $juchuhenko->ryoriunfix($dataApp);
        }
    }

    /**
     * セッションクリアアクション 
     *
     * <AUTHOR> Sai
     * @since 2014/04/25
     */
    public function clearsessionAction() {
        Msi_Sys_Utils::outJson(['status' => 'OK']);
    }

    /**
     * 月次締めチェック処理
     *
     * <AUTHOR> Sai
     * @since 2014/10/27
     */
    public function checkgetujifixAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $den_Ymd = $dataApp['keijo_ymd'];
            $mode = 1;
            $data_kbn = 1;
            $denpyo_no = null;
            // モードを取得
            if (array_key_exists('mode', $dataApp)) {
                $mode = $dataApp['mode'];
            }
            // データ区分を取得
            if (array_key_exists('moushi_kbn', $dataApp)) {
                $data_kbn = $dataApp['moushi_kbn'];
            }
            // 伝票番号を取得
            if (array_key_exists('denpyo_no', $dataApp)) {
                $denpyo_no = $dataApp['denpyo_no'];
            }
            $data = App_ClsCheckGetujiFix::CheckGetujiFix(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $data_kbn, $denpyo_no, $mode);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理(仕入用)
     *
     * <AUTHOR> Sai
     * @since 2014/10/27
     */
    public function checkgetujifixsiireAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $den_Ymd = $dataApp['siire_ymd'];
            $mode = 1;
            $data_kbn = 1;
            $denpyo_no = null;
            // モードを取得
            if (array_key_exists('mode', $dataApp)) {
                $mode = $dataApp['mode'];
            }
            // 伝票番号を取得
            if (array_key_exists('denpyo_no', $dataApp)) {
                $denpyo_no = $dataApp['denpyo_no'];
            }
            $data = App_ClsCheckGetujiFix::CheckGetujiFixSiire(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $denpyo_no, $mode);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理(支払用)
     *
     * <AUTHOR> Kayo
     * @since 2016/02/14
     */
    public function checkgetujifixsiharaiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $den_Ymd = $dataApp['siharai_ymd'];
            $mode = 1;
            $data_kbn = 1;
            $denpyo_no = null;
            // モードを取得
            if (array_key_exists('mode', $dataApp)) {
                $mode = $dataApp['mode'];
            }
            // 伝票番号を取得
            if (array_key_exists('siharai_den_no', $dataApp)) {
                $denpyo_no = $dataApp['siharai_den_no'];
            }
            $data = App_ClsCheckGetujiFix::CheckGetujiFixSiire(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $denpyo_no, $mode);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理(検品・仕入確定)
     *
     * <AUTHOR> Sai
     * @since 2014/10/27
     */
    public function checkgetujifixkenpinAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            $mode = Msi_Sys_Utils::json_decode($req->getPost('kenpin_kbn'));
            $denpyo_no = null;
            // 未確定なので修正可能
            $data = array('status' => 'OK',
                'msg' => ''
            );
            foreach ($dataApp as $rec) {
                $den_Ymd = $rec['kenpin_dt'];
                if ($mode == 1) {
                    if (strlen($den_Ymd) <= 0) {
                        continue;
                    }
                } else {
                    if (strlen($den_Ymd) > 0) {
                        continue;
                    }
                    $den_Ymd = $rec['kenpin_dt2'];
                }
                $data = App_ClsCheckGetujiFix::CheckGetujiFixKenpin(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $denpyo_no, $mode);
                if ($data['status'] != 'OK') {
                    break;
                }
            }
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理(入金伝票)
     *
     * <AUTHOR> Sai
     * @since 2018/05/31
     */
    public function checkgetujifixnyukinAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $den_Ymd = $dataApp['nyukin_ymd'];
            $mode = 1;
            $denpyo_no = null;
            // モードを取得
            if (array_key_exists('mode', $dataApp)) {
                $mode = $dataApp['mode'];
            }
            // 伝票番号を取得
            if (array_key_exists('denpyo_no', $dataApp)) {
                $denpyo_no = $dataApp['denpyo_no'];
            }
            $data = App_ClsCheckGetujiFix::CheckGetujiFixNyukin(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $denpyo_no, $mode);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理
     *
     * <AUTHOR> Sai
     * @since 2014/10/31
     */
    public function checkgetujifixmitsuAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $seko_no = $dataApp['seko_no'];
            $data = App_ClsCheckGetujiFix::CheckGetujiFixMitsu(Msi_Sys_DbManager::getMyDb(), $seko_no);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 月次締めチェック処理
     *
     * <AUTHOR> Sai
     * @since 2014/11/20
     */
    public function checkgetujifixinputAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $seko_no = $dataApp['seko_no'];
            $data = App_ClsCheckGetujiFix::CheckGetujiFixInput(Msi_Sys_DbManager::getMyDb(), $seko_no);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 初期情報設定共通処理(佐野バージョン)
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    private function setCommonInfoS($juchuhenko, $sidemenukey = '') {
        // 初期データを取得する
        $jsonData = $juchuhenko->getInitData();
        $this->view->juchuhenko_json = $jsonData;
        $this->_helper->viewRenderer->setScriptAction('juchuhenkos');
        $this->view->sidemenu_key = $sidemenukey;
        // 葬儀基本情報(ヘッダー)を設定する
        //$this->setHeader($sekoNo);
        // サイドメニューデータを設定する
        $this->setSidemenu($juchuhenko, $sidemenukey);
        // 利用 CSS 設定
        App_Smarty::pushCssFile(['app/sais.css', 'app/juchu.juchuhenko.css', 'app/juchu.juchuhenkosn.css']);
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile(['app/juchu.juchuhenkos.js', 'app/sidemenu.js', 'app/juchu.juchuhenko.side.js']);
    }

    /**
     * 最終確認処理
     * 
     * <AUTHOR> Kayo
     * @param action $action
     * @since 2017/09/11
     */
    private function setConfirmation($juchuhenko) {
        // 初期データを取得する
        $jsonData = $juchuhenko->getInitData();
        App_ClsAuthority::CheckManager($this);
        $this->view->juchuhenko_json = $jsonData;
        $this->_helper->viewRenderer->setScriptAction('confirmation');
        // サイドメニューデータを設定する
        $this->setSidemenu($juchuhenko);
        // 利用 CSS 設定
        App_Smarty::pushCssFile(['app/juchu.juchuhenko.confirmation.css']);
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile(['app/juchu.juchuhenko.confirmation.js', 'app/sidemenu.js', 'app/juchu.juchuhenko.side.js']);
    }

    /**
     * 祭壇安置（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function saidanAction() {
        $juchuhenko = new Juchu_JuchuhenkoSaidan();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 祭壇安置 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/17
     */
    public function saidanaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSaidan();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 礼状 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function reijyoAction() {
        $juchuhenko = new Juchu_JuchuhenkoReijyo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 礼状 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/17
     */
    public function reijyoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoReijyo();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 納骨具 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function noukotuAction() {
        $juchuhenko = new Juchu_JuchuhenkoNoukotu();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 納骨具 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function noukotuaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoNoukotu();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 喪服 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function mofukuAction() {
        $juchuhenko = new Juchu_JuchuhenkoMofuku();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 喪服 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function mofukuaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoMofuku();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 棺 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function hitugiAction() {
        $juchuhenko = new Juchu_JuchuhenkoHitugi();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 棺 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function hitugiaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoHitugi();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 装飾花 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function soushokuAction() {
        $juchuhenko = new Juchu_JuchuhenkoSoushoku();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 装飾花 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function soushokuaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSoushoku();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 写真 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function syashinAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyashin();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 写真 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function syashinaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyashin();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 仏教・本位牌 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function bukyoAction() {
        $juchuhenko = new Juchu_JuchuhenkoBukyo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 仏教・本位牌 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function bukyoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoBukyo();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 神道 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function shindoAction() {
        $juchuhenko = new Juchu_JuchuhenkoShindo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 神道 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function shindoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoShindo();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * キリスト・無宗教 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function kirisutoAction() {
        $juchuhenko = new Juchu_JuchuhenkoKirisuto();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * キリスト・無宗教 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function kirisutoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoKirisuto();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 美粧・保全 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function yukanAction() {
        $juchuhenko = new Juchu_JuchuhenkoYukan();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 美粧・保全 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function yukanaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoYukan();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 車両 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function syaryoAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyaryo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 車両 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function syaryoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSyaryo();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 火葬車両（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function kasoAction() {
        $juchuhenko = new Juchu_JuchuhenkoKaso();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * その他(花瓶・ストキング・念珠等) 初期情報取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/24
     */
    public function sonotaAction() {
        $juchuhenko = new Juchu_JuchuhenkoSonota();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * その他(花瓶・ストキング・念珠等) 初期情報取得アクション (セレモ)
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function sonotaaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoSonota();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * その他(法事) 初期情報取得アクション 
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/10
     */
    public function sonotahAction() {
        $juchuhenko = new Juchu_JuchuhenkoSonotaH();
        $this->setCommonInfo($juchuhenko);
    }

    /**
     * 礼状返礼品（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function rhenreiAction() {
        $juchuhenko = new Juchu_JuchuhenkoRhenrei();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 礼状返礼品（セレモ） 初期情報取得アクション 
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function rhenreiaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoRhenrei();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 納棺通夜準備（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function nokanAction() {
        $juchuhenko = new Juchu_JuchuhenkoNokan();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 受付準備衣裳（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function uketsukeAction() {
        $juchuhenko = new Juchu_JuchuhenkoUketsuke();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 料理（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function inshokuAction() {
        $juchuhenko = new Juchu_JuchuhenkoInshoku();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 料理（セレモ） 初期情報取得アクション 
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function inshokuaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoInshoku();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 飲料（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function inryoAction() {
        $juchuhenko = new Juchu_JuchuhenkoInryo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 式場設営（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function shikijoAction() {
        $juchuhenko = new Juchu_JuchuhenkoShikijo();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 式場設営（セレモ） 初期情報取得アクション 
     *
     * <AUTHOR> Tosaka
     * @since 2018/12/15
     */
    public function shikijoaddAction() {
        $juchuhenko = new Juchu_JuchuhenkoShikijo();
        $this->setCommonInfoS($juchuhenko, 'add.juchu');
    }

    /**
     * 供花（佐野） 初期情報取得アクション 
     *
     * <AUTHOR> Sai
     * @since 2016/03/30
     */
    public function kyokaAction() {
        $juchuhenko = new Juchu_JuchuhenkoKyoka();
        $this->setCommonInfoS($juchuhenko);
    }

    /**
     * 最終確認取得アクション 
     *
     * <AUTHOR> Kayo
     * @since 2017/09/11
     */
    public function confirmationAction() {
        $juchuhenko = new Juchu_JuchuhenkoConfirmation();
        $this->setConfirmation($juchuhenko);
    }

    /**
     * 最終確認確定アクション  (セレモ)
     *
     * <AUTHOR> Kayo
     * @since 2017/10/21
     */
    public function finalfixAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $juchuhenko = new Juchu_JuchuhenkoConfirmation();
            $juchuhenko->finalfix($dataApp);
        }
    }

    /**
     * 月次締めチェック処理
     * 月次確定されたら一切修正不可
     * <AUTHOR> Sai
     * @since 2016/10/19
     */
    public function checkgetujifix2Action() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $den_Ymd = $dataApp['keijo_ymd'];
            $mode = 1;
            $data_kbn = 1;
            $denpyo_no = null;
            // モードを取得
            if (array_key_exists('mode', $dataApp)) {
                $mode = $dataApp['mode'];
            }
            // データ区分を取得
            if (array_key_exists('moushi_kbn', $dataApp)) {
                $data_kbn = $dataApp['moushi_kbn'];
            }
            // 伝票番号を取得
            if (array_key_exists('denpyo_no', $dataApp)) {
                $denpyo_no = $dataApp['denpyo_no'];
            }
            $data = App_ClsCheckGetujiFix::CheckGetujiFix2(Msi_Sys_DbManager::getMyDb(), $den_Ymd, $data_kbn, $denpyo_no, $mode);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 承認状況確認アクション 
     *
     * <AUTHOR> Sugiyama
     * @since 2019/12/03
     */
    public function shohincheckAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $customerinfo = new Juchu_JuchuCustomerinfo();
            $customerinfo->shohincheck($req);
        }
    }

    /**
     * 互助会値引設定処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     */
    public function setgojokainebikiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $params = Msi_Sys_Utils::webInputs();
            $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
            App_Utils2::setGojokaiNebikiOne($dataCol, $params['sekoNo']);
            $data = array('status' => 'OK', 'gojokai_nebiki_prc' => $dataCol['gojokai_nebiki_prc'], 'n_free2' => $dataCol['n_free2']);
            Msi_Sys_Utils::outJson($data);
        }
    }

    /**
     * 引取返金初期情報
     *
     * <AUTHOR> Sugiyama
     * @since 2021/01/xx
     */
    public function hikitoriAction() {
        $juchuhenko = new Juchu_JuchuHikitori();
        // 初期データを取得する
        $jsonData = $juchuhenko->getInitData();
        $this->view->mydata_json = $jsonData;
        $this->_helper->viewRenderer->setScriptAction('hikitori');
        $sidemenukey = '';
        $this->view->sidemenu_key = $sidemenukey;
        // サイドメニューデータを設定する
        $this->setSidemenu($juchuhenko, $sidemenukey);
        // 利用 CSS 設定
        App_Smarty::pushCssFile(['app/juchu.hikitori.css', 'app/sais.css']);
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile(['app/sidemenu.js', 'app/juchu.juchuhenko.side.js', 'app/juchu.hikitori.js']);
    }

    /**
     * 引取返金保存
     *
     * <AUTHOR> Sugiyama
     * @since 2021/01/xx
     */
    public function hikitorisaveAction() {
        $juchuhenko = new Juchu_JuchuHikitori();
        // 保存
        $jsonData = $juchuhenko->save();
    }
    
    /**
     * 顧客情報変更チェック処理
     *
     * <AUTHOR> Tosaka
     * @since 2022/xx/xx
     */
    public function checkkokyakuAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $data = array('status' => 'OK', 'msg' => null);
            // 画面Appデータ
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $seko_no = $dataApp['seko_no'];
            $db = Msi_Sys_DbManager::getMyDb();
            if (isset($seko_no)) {
                $sekoData = DataMapper_SekoKihon::find2($seko_no);
                $status_kbn = $sekoData['status_kbn'];
                // 喪主顧客番号が変更されているか
                if (isset($sekoData['m_cif_no']) && $sekoData['m_cif_no'] != $dataApp['m_cif_no']) {
                    $msg .= '喪主顧客番号,';
                }
                // 請求先顧客番号が変更されているか
                if (isset($sekoData['s_cif_no']) && $sekoData['s_cif_no'] != $dataApp['s_cif_no']) {
                    $msg .= '請求先顧客番号,';
                }
                // 故人情報が変更されているか
                if (isset($sekoData['k_first_nm']) && $sekoData['k_first_nm'] != $dataApp['k_first_nm']) {
                    $msg .= '故人名,';
                } else if (isset($sekoData['k_last_nm']) && $sekoData['k_last_nm'] != $dataApp['k_last_nm']) {
                    $msg .= '故人名,';
                }
                if (isset($sekoData['k_first_knm']) && $sekoData['k_first_knm'] != $dataApp['k_first_knm']) {
                    $msg .= '故人名カナ,';
                } else if (isset($sekoData['k_last_knm']) && $sekoData['k_last_knm'] != $dataApp['k_last_knm']) {
                    $msg .= '故人名カナ,';
                }
                if (isset($sekoData['k_seinengappi_ymd']) && $sekoData['k_seinengappi_ymd'] != $dataApp['k_seinengappi_ymd']) {
                    $msg .= '故人生年月日,';
                } else if (isset($sekoData['k_birth_year']) && $sekoData['k_birth_year'] != $dataApp['k_birth_year']) {
                    $msg .= '故人生年月日,';
                } else if (isset($sekoData['k_birth_month']) && $sekoData['k_birth_month'] != $dataApp['k_birth_month']) {
                    $msg .= '故人生年月日,';
                } else if (isset($sekoData['k_birth_day']) && $sekoData['k_birth_day'] != $dataApp['k_birth_day']) {
                    $msg .= '故人生年月日,';
                }
                if (isset($sekoData['kg_yubin_no']) && $sekoData['kg_yubin_no'] != $dataApp['kg_yubin_no']) {
                    $msg .= '故人郵便番号,';
                }
                if (isset($sekoData['kg_addr1']) && $sekoData['kg_addr1'] != $dataApp['kg_addr1']) {
                    $msg .= '故人住所1,';
                }
                if (isset($sekoData['kg_addr2']) && $sekoData['kg_addr2'] != $dataApp['kg_addr2']) {
                    $msg .= '故人住所2,';
                }
                // ステータスが入金済の場合はエラーそれ以外は確認ダイアログ
                if (isset($msg) && $status_kbn == '5') {
                    $data['status'] = 'NG';
                    $data['msg'] = trim($msg,',').'が変更されています。';
                } else if (isset($msg) && $status_kbn != '5') {
                    $data['status'] = 'INFO';
                    $data['msg'] = trim($msg,',').'が変更されています。よろしいですか？';
                }
            }
            Msi_Sys_Utils::outJson($data);
        }
    }

}
