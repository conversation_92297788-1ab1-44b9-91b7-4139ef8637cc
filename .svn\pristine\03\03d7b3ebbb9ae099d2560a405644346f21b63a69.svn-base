<?php
  /**
   * DataMapper_ScheduleInfoResult
   *
   * 各種スケジュール情報実績 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Otake
   * @since      2017/10/25
   * @filesource
   */

  /**
   * 各種スケジュール情報実績 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Otake
   * @since      2017/10/25
   */
class DataMapper_ScheduleInfoResult extends DataMapper_Abstract
{
	CONST TABLE = 'schedule_info_result';
	CONST TABLE_S = 'sir';
	CONST PRIMARY_KEY = ['seko_no','msi_no'];
	CONST COLUMNS = ['seko_no','msi_no','disp_no','sagyo_cd','sagyo_nm','tanto_cd'
                        ,'tanto_nm','tanto_jikyu','start_ymd','end_ymd','sagyo_jikan','biko','delete_flg'];

	/**
     * 各種スケジュール情報実績 取得
     *
     * <AUTHOR> yanagiso
     * @since      2014/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=[]) {
        list( $whereStr, $param ) = static::setWhere($keyHash, [], 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no, T.disp_no, T.sagyo_cd ';
        }
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT seko_no
       ,msi_no
       ,disp_no
       ,sagyo_cd
       ,sagyo_nm
       ,tanto_cd
       ,tanto_nm
       ,tanto_jikyu
       ,start_ymd
       ,end_ymd
--       ,to_char(start_ymd, 'YYYY/MM/DD HH24:MI') AS start_ymd
--       ,to_char(end_ymd, 'YYYY/MM/DD HH24:MI') AS end_ymd
       ,sagyo_jikan
       ,biko
       ,delete_flg
  FROM schedule_info_result sir
 WHERE sir.delete_flg=0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );
        return $select;
    }

    /**
     * 各種スケジュール情報実績データ 取得
     *
     * <AUTHOR> Otake
     * @since      2017/10/25
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
	public static function findPure($db, $keyHash=[]) {
		$table = self::TABLE;
		$short = self::TABLE_S;
		$columns = implode(', ', self::COLUMNS);
        list( $whereStr, $param) = static::setWhere($keyHash, [], $short);
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, $short);
        if (strlen($tailClause) <= 0) {$tailClause = '';}
		if (strlen($orderBy) <= 0) {$orderBy = '';}
		$sql = <<< END_OF_SQL
SELECT $columns
       , to_char(start_ymd, 'YYYY/MM/DD HH24:MI') AS start_datetime
       , to_char(end_ymd, 'YYYY/MM/DD HH24:MI') AS end_datetime
  FROM $table $short
 WHERE $whereStr
 ORDER BY disp_no, sagyo_cd
 $orderBy
 $tailClause
END_OF_SQL;
        return $db->easySelect($sql, $param);
	}

	/**
	 * データが存在していれば更新、そうでなければ追加する
	 * -既にデータが存在している場合、デフォルトではnullを無視せずに上書きする
	 * @param type $db
	 * @param type $data
	 * @param bool $ignoreNullValue 『valueがNULLであるkey』の更新が不要であればtrue
	 * @return int
	 */
	public static function upsert($db, $data, $ignoreNullValue = false) {
		if (static::isExist($db, $data)) {
			return static::update($db, $data, $ignoreNullValue);
		} else {
			return static::insert($db, $data);
		}
	}

	/**
	 * 既に存在するデータかどうか主キーで判定する
	 * @param type $db
	 * @param array $data
	 * @return bool 存在していればtrue
	 */
	protected static function isExist($db, $data) {
		$primary_key = static::extractPrimaryKey($data);
		if (count(self::PRIMARY_KEY) > count($primary_key)) {return false;}
		return static::findPure($db, $primary_key) ? true : false;
	}

	/**
	 * 更新
	 * デフォルトではnullを無視せずに上書きする
	 * @param type $db
	 * @param array $data
	 * @param bool $ignoreNullValue 『valueがNULLであるkey』の更新が不要であればtrue
	 * @return int
	 */
	public static function update($db, $data, $ignoreNullValue = false) {
		$columns = static::extractColumns($data, $ignoreNullValue);
		list($sql,$param) = DataMapper_Utils::makeUpdateSQL2(self::TABLE, $columns, self::PRIMARY_KEY);
		return $db->easyExecute($sql,$param);
	}

	/**
	 * 追加
	 * @param type $db
	 * @param array $data
	 * @return int
	 */
	public static function insert($db, $data) {
		$columns = static::extractColumns($data);
		static::adjustForInsert($db, $columns);
		list($sql,$param) = DataMapper_Utils::makeInsertSQL(self::TABLE, $columns);
		return $db->easyExecute($sql,$param);
	}

	/**
	 * insert前のデータチェック
	 * 調整が必要な場合ここで処理
	 * @param type $columns
	 * @throws Exception
	 */
	protected static function adjustForInsert($db, &$columns) {
		if (empty($columns['seko_no'])) {throw new Exception('施行番号が不正です。');}
		isset($columns['msi_no']) || $columns['msi_no'] = static::getNextMsiNo($db, $columns['seko_no']);
	}

	/**
	 * 明細No.最大値+1を取得
	 * @param type $db
	 * @param type $seko_no
	 * @return int
	 */
	protected function getNextMsiNo($db, $seko_no) {
		$table = self::TABLE;
		$sql = <<<END_OF_SQL
SELECT COUNT(1) as msi_no
  FROM $table
 WHERE seko_no = :seko_no
 GROUP BY seko_no
END_OF_SQL;
		$selOne = $db->easySelOne($sql,['seko_no'=>$seko_no]);
		return $selOne['msi_no'] ?: 0;
   }

	/**
	 * 論理削除
	 * @param type $db
	 * @param str $seko_no
	 * @param int $msi_no
	 * @return int
	 */
	public static function deleteLogicalByPrimaryKey($db, $data) {
		$table = self::TABLE;
		$short = self::TABLE_S;
		$primary_key = static::extractPrimaryKey($data);
		if (count(self::PRIMARY_KEY) > count($primary_key)) {throw new Exception('削除に必要な情報が不足しています。');}
		list($whereStr, $param) = static::setWhere($primary_key, [], $short);
		$sql = <<<END_OF_SQL
UPDATE $table $short
   SET delete_flg = 1
 WHERE $whereStr
END_OF_SQL;
		return $db->easyExecute($sql,$param);
	}

	/**
	 * 物理削除
	 * @param type $db
	 * @param str $seko_no
	 * @param int $msi_no
	 * @return int
	 * @throws Exception
	 */
	public static function deletePhysicalByPrimaryKey($db, $data) {
		$table = self::TABLE;
		$short = self::TABLE_S;
		$primary_key = static::extractPrimaryKey($data);
		if (count(self::PRIMARY_KEY) > count($primary_key)) {throw new Exception('削除に必要な情報が不足しています。');}
		list($whereStr, $param) = static::setWhere($primary_key, [], $short);
		$sql = <<<END_OF_SQL
DELETE FROM $table $short
 WHERE $whereStr
END_OF_SQL;
		return $db->easyExecute($sql, $param);
	}

	/**
	 * schedule_info_resultテーブルで利用できるカラムだけを抽出する
	 * @param array $data 抽出元のデータ
	 * @param bool $ignoreNullValue 『valueがNULLであるkey』の抽出が不要であればtrue
	 * @return array 抽出したデータ
	 */
	protected static function extractColumns($data, $ignoreNullValue = true) {
		return static::extractData($data, self::COLUMNS, $ignoreNullValue);
	}
	/**
	 * schedule_info_resultテーブルの主キーだけを抽出する
	 * @param array $data 抽出元のデータ
	 * @param bool $ignoreNullValue 『valueがNULLであるkey』の抽出が不要であればtrue
	 * @return array 抽出したデータ
	 */
	protected static function extractPrimaryKey($data, $ignoreNullValue = true) {
		return static::extractData($data, self::PRIMARY_KEY, $ignoreNullValue);
	}
	/**
	 * 抽出元のデータから任意のkeyのみのデータを抽出する
	 * @param array $data 抽出元のデータ
	 * @param array $extractKeys 抽出するkey
	 * @param bool $ignoreNullValue 『valueがNULLであるkey』の抽出が不要であればtrue
	 * @return array 抽出したデータ
	 */
	protected static function extractData($data, $extractKeys, $ignoreNullValue = true) {
		$extractData = [];
		foreach ($extractKeys as $key) {
			if (!array_key_exists($key, $data)) {continue;}
			if ($ignoreNullValue && $data[$key] === NULL) {continue;}
			$extractData[$key] = $data[$key];
		}
		return $extractData;
	}
}
