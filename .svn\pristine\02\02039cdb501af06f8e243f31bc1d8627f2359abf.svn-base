var msiMstr = msiMstr || {};
window.my = window.my || {};
$(function() {
  "use strict";
  // ==========================================================================
  // * build configure to Backbone.js
  // --------------------------------------------------------------------------
  // ----------------------------------
  // * Web表示商品一覧（区分）
  window.my.plan = window.my.plan || {};
  window.my.plan.conf = {
    searchElements : '.plan-area .search',
    modelInfo : planConf,  // model構造設定 (write from server)
    urls: {
       fetch: './restgroupfetch'  // グループ一覧取得API
      ,put  : './restgroupput'    // グループ一覧更新API
    },
    listView: {
       el: '#plan-table'
      ,templateNone: _.template($('#plan-table-none').html()) // 該当なしテンプレート
      ,templateView: _.template($('#plan-table-view').html()) // 参照モードテンプレート
      ,templateEdit: _.template($('#plan-table-edit').html()) // 編集モードテンプレート
      ,appendEvent : function(view) {   // template適用後の追加イベント
      }
    },
    funcView: {
       el: '.plan-area .function'
    }
  };
  // ----------------------------------
  // * Web表示商品一覧（詳細）
  window.my.smsi = window.my.smsi || {};
  window.my.smsi.conf = {
    modelInfo : planConf,  // model構造設定 (write from server)
    urls: {
       fetch: './restdetailfetch'  // 詳細一覧取得API
      ,put  : './restdetailput'    // 詳細一覧更新API
    },
    listView: {
       el: '#smsi-table'
      ,templateNone: _.template($('#smsi-table-none').html()) // 該当なしテンプレート
      ,templateView: _.template($('#smsi-table-view').html()) // 参照モードテンプレート
      ,templateEdit: _.template($('#smsi-table-edit').html()) // 編集モードテンプレート
      ,appendEvent : function(view) {   // template適用後の追加イベント
        // * render select2 CUSTOM
        // override
        var _sel   = ':input[name=bunrui_cd_all]';
        var $el    = view.$el.find(_sel);
        var _option = {
          query : function (query) {
            $.ajax({
               url: $el.data('url')
              ,type: 'GET'
              ,dataType: 'json'
              ,cache: true
              ,data: $el.parents('tr').find($el.data('search')).serialize()
              ,success: function(data) {
                var _join = [];
                for(var i=0;i<data.length;i++) {
                  _join.push({
                     id  : data[i].dai_bunrui_cd+':'+data[i].chu_bunrui_cd+':'+data[i].shohin_kbn
                    ,text: data[i].dai_bunrui_cd__alt+' / '+data[i].chu_bunrui_cd__alt+' / '+data[i].shohin_kbn__alt
                  });
                }
                query.callback({results: _join});
              }
            });
          }
        };
        msiMstr.renderSelect2(view.$el, _option, _sel);
        // add event
        $el.on('select2-close', function(e){
          var $target = $(e.currentTarget);
          var _vlist = $target.select2('data').id.split(':');
          $target.siblings('input[name=dai_bunrui_cd]:first').val(_vlist[0]);
          $target.siblings('input[name=chu_bunrui_cd]:first').val(_vlist[1]);
          $target.siblings('input[name=shohin_kbn]:first'   ).val(_vlist[2]);
          
          var _tlist = $target.select2('data').text.split(' / ');
          $target.siblings('input[name=dai_bunrui_cd__alt]:first').val(_tlist[0]);
          $target.siblings('input[name=chu_bunrui_cd__alt]:first').val(_tlist[1]);
          $target.siblings('input[name=shohin_kbn__alt]:first'   ).val(_tlist[2]);
        });
      }
    },
    funcView: {
       el: '.smsi-area .function'
    }
  };
  // ==========================================================================
  // * set global events
  // --------------------------------------------------------------------------
  // ----------------------------------
  // * 更新フラグ（区分）
  window.my.plan.isEdit = undefined;
  // * 更新フラグ操作＋トリガー
  var setEditedPlan = function(on) {
    var _on = (on) ? true : false;
    if(window.my.plan.isEdit === _on) return;
    
    window.my.plan.isEdit = _on;
    if(window.my.plan.isEdit) $(document).trigger('plan-edit'); // 編集ONトリガー発火
    else $(document).trigger('plan-unedit'); // 編集OFFトリガー発火
  };
  // * 更新フラグがONならば確認ダイアログ実行する関数 (区分)
  var editedConfirmPlan = function($msg){
    if(window.my.plan.isEdit) {
      return window.confirm($msg);
    } else return true;
  };
  // * 更新プラグイベント
  $(document).on('change', '.plan-area :input', function(e){
    setEditedPlan(true);
  });
  $(document).on('plan-edit', function(){
    // console.log('[throw]:plan-edit');
    msiMstr.renderEnable ($('.plan-area .gotoPost'));
    msiMstr.renderEnable ($('.plan-area .gotoPut'));
    msiMstr.renderEnable ($('.plan-area .gotoSave'));
    msiMstr.renderEnable ($('.plan-area .gotoGet'));
    msiMstr.renderEnable ($('.plan-area .gotoReset'));
    msiMstr.renderEnable ($('.plan-area .gotoHide'));
    msiMstr.renderEnable ($('.plan-area .gotoDestroy'));
  });
  $(document).on('plan-unedit', function(){
    // console.log('[throw]:plan-unedit');
    msiMstr.renderDisable($('.plan-area .gotoPost'));
    msiMstr.renderDisable($('.plan-area .gotoPut'));
    msiMstr.renderDisable($('.plan-area .gotoSave'));
    msiMstr.renderDisable($('.plan-area .gotoReset'));
    msiMstr.renderEnable ($('.plan-area .gotoGet'));
    msiMstr.renderEnable ($('.plan-area .gotoHide'));
    msiMstr.renderEnable ($('.plan-area .gotoDestroy'));
  });
  // ----------------------------------
  // * 更新フラグ (明細)
  window.my.smsi.isEdit = undefined;
  // * 更新フラグ操作＋トリガー
  var setEditedSmsi = function(on) {
    var _on = (on) ? true : false;
    if(window.my.smsi.isEdit === _on) return;
    
    window.my.smsi.isEdit = _on;
    if(window.my.smsi.isEdit) $(document).trigger('smsi-edit'); // 編集ONトリガー発火
    else $(document).trigger('smsi-unedit'); // 編集OFFトリガー発火
  };
  // * 更新フラグがONならば確認ダイアログ実行する関数 (区分)
  var editedConfirmSmsi = function($msg){
    if(window.my.smsi.isEdit) {
      return window.confirm($msg);
    } else return true;
  };
  // * 更新プラグイベント
  $(document).on('change', '.smsi-area :input', function(e){
    setEditedSmsi(true);
  });
  $(document).on('smsi-edit', function(){
    // console.log('[throw]:smsi-edit');
    msiMstr.renderEnable ($('.smsi-area .gotoPost'));
    msiMstr.renderEnable ($('.smsi-area .gotoPut'));
    msiMstr.renderEnable ($('.smsi-area .gotoSave'));
    msiMstr.renderEnable ($('.smsi-area .gotoGet'));
    msiMstr.renderEnable ($('.smsi-area .gotoReset'));
    msiMstr.renderEnable ($('.smsi-area .gotoHide'));
    msiMstr.renderEnable ($('.smsi-area .gotoDestroy'));
  });
  $(document).on('smsi-unedit', function(){
    // console.log('[throw]:smsi-unedit');
    msiMstr.renderDisable($('.smsi-area .gotoPost'));
    msiMstr.renderDisable($('.smsi-area .gotoPut'));
    msiMstr.renderDisable($('.smsi-area .gotoSave'));
    msiMstr.renderDisable($('.smsi-area .gotoReset'));
    msiMstr.renderEnable ($('.smsi-area .gotoGet'));
    msiMstr.renderEnable ($('.smsi-area .gotoHide'));
    msiMstr.renderEnable ($('.smsi-area .gotoDestroy'));
  });
  // ----------------------------------
  // * ページ遷移前の確認イベント
  $(window).on('beforeunload', function() {
    if(window.my.plan.isEdit || window.my.smsi.isEdit) return "保存されていないデータがあります.";
  });
  // ----------------------------------
  // * render ヘッダー固定テーブルの適用
  $('.grid-table').flexTable();
  // ----------------------------------
  // * function 行No発行
  var appendAutoNo = function(selecter) {
    var _cnt = 1;
    $(selecter).each(function(){
      $(this).html(_cnt++);
    });
  };
  // ----------------------------------
  // * function 区分一覧 <-> 詳細一覧 切り替え
  var switchEditMode = function(onoff){
    if(msiMstr.isset(onoff)) window.my.detailOn = !onoff;
    if(window.my.detailOn) {
      if(editedConfirmSmsi('編集したデータは保存されていません。破棄しますがよろしいですか？')) {
        window.my.detailOn = false;
        $('#mydetail').hide();
        $('#mylist').fadeIn('show');
        setEditedSmsi(false);
        window.my.smsi.collection.reset();
        $('#selected-item').text("");
      }
    } else {
      window.my.detailOn = true;
      $('#mylist').hide();
      $('#mydetail').fadeIn('show');
    }
  };
  $(document).on('click', '.changeView', function(e){
    switchEditMode(false);
  });
  // ----------------------------------
  // * render pickhelper 商品(& other)
  $(document).on('focus', 'input[name=shohin_cd__alt]', function(e){
    var $row  = $(e.currentTarget).parents('tbody:first');
    var $elem = $(e.currentTarget);
    msiLib2.setPickHelper('shohin', $elem,
      function(data) {
        $row.find('input[name=shohin_cd]'         ).val(data.shohin_cd);
        $row.find('input[name=shohin_cd__alt]'    ).val(data.shohin_nm);
        $row.find('input[name=dai_bunrui_cd]'     ).val(data.dai_bunrui_cd);
        $row.find('input[name=dai_bunrui_cd__alt]').val(data.dai_bunrui_nm);
        $row.find('input[name=chu_bunrui_cd]'     ).val(data.chu_bunrui_cd);
        $row.find('input[name=chu_bunrui_cd__alt]').val(data.chu_bunrui_nm);
        $row.find('input[name=shohin_kbn]'        ).val(data.shohin_kbn);
        $row.find('input[name=shohin_kbn__alt]'   ).val(data.shohin_kbn_nm);
        // select2
        $row.find('input[name=bunrui_cd_all]').select2('data', {
           id  : data.dai_bunrui_cd+':'+data.chu_bunrui_cd+':'+data.shohin_kbn
          ,text: data.dai_bunrui_nm+' / '+data.chu_bunrui_nm+' / '+data.shohin_kbn_nm
        });
      },
      function() {
        $row.find('input[name=shohin_cd]'         ).val('');
        $row.find('input[name=shohin_cd__alt]'    ).val('');
        $row.find('input[name=dai_bunrui_cd]'     ).val('');
        $row.find('input[name=chu_bunrui_cd]'     ).val('');
        $row.find('input[name=shohin_kbn]'        ).val('');
        $row.find('input[name=dai_bunrui_cd__alt]').val('');
        $row.find('input[name=chu_bunrui_cd__alt]').val('');
        $row.find('input[name=shohin_kbn__alt]'   ).val('');
        // select2
        $row.find('input[name=bunrui_cd_all]').select2('data', {id: '::', text: ' /  / '});
      },
      function() {
        var _val = $row.find('input[name=shohin_cd]').val();
        return {
           s_code : _val
          ,init_search: (_val) ? 1 : 0
          ,one_exit: 0
          ,no_date_check: 1
        };
      }
    );
  });
  // ----------------------------------
  // * デフォルト上書き値
  var mstrSmisModelOverride = function(append) {
    if(append) window.my.smsi.overrideModels = $.extend({}, append); // clone
    return window.my.smsi.overrideModels || {};
  };
  /**
   * グループ収集処理
   */
  var bindCollectionPlan = function(param) {
    var _PLAN   = window.my.plan;
    var _isTrue = true;
    var _planList = [];
    // * グループ情報
    _PLAN.collection.each(function(item){
      if(!item.view.isEdit) return true;  // 編集モードのみ実施
      _PLAN.bb.bindView2model(item, item.view.$el, {silent:true});
      // 収集
      var _item = {
         moushikomi_kbn : item.get('moushikomi_kbn')
        ,category_kbn   : item.get('category_kbn')
        ,tab_no         : item.get('tab_no')
        ,tab_nm         : item.get('tab_nm')
        ,disp_kbn       : item.get('disp_kbn')
      };
      _planList.push(_item);
      _item = null;
    });
    param.planJson = JSON.stringify(_planList);
    return _isTrue;
  };
  /**
   * 詳細収集処理
   */
  var bindCollectionSmsi = function(param) {
    var _SMSI   = window.my.smsi;
    var _isTrue = true;
    var _smsiList = [];
    var _errCnt = 0;
    $.extend(param, mstrSmisModelOverride());
    // * 明細情報
    _SMSI.collection.each(function(item){
      _SMSI.bb.bindView2model(item, item.view.$el, {silent:true});
      if(!item.isValid()) {
        msiMstr.renderErr(item.validationError, item.view.$el, !(_errCnt++));
        _isTrue = false;
        return false;
      }
      var _item = item.clone();
      _SMSI.bb.cutoffNotUploads(_item);
      _smsiList.push(_item.toJSON());
      _item = null;
    });
    param.smsiJson = JSON.stringify(_smsiList);
    return _isTrue;
  };
  // ==========================================================================
  // * build Backbone
  // --------------------------------------------------------------------------
  // ----------------------------------
  // * 施行プラン（一覧 & 詳細）
  window.my.plan.bb = new msiMstr.Bb(window.my.plan.conf.modelInfo);
  var _BB_P = window.my.plan.bb;
  var _CONFIGS_P = window.my.plan.conf;
  var _GLOBALS_P = window.my.plan;
  // ---------------------
  // * bb model
  var PlanModel = Backbone.Model.extend({
    idAttribute: '_rowid',
    defaults: function(){ return _BB_P.getDefaults(); },
    validate: function(attr){ return _BB_P.checkInputFrom(attr); }
  });
  // ---------------------
  // * bb Collection
  var PlanCollection = Backbone.Collection.extend({
     model: PlanModel
    ,url  : _CONFIGS_P.urls.fetch
  });
  // ---------------------
  // * bb ListRowView
  var PlanListRowView = Backbone.View.extend({
    template     : _CONFIGS_P.listView.templateView || _CONFIGS_P.listView.templateEdit,
    templateView : _CONFIGS_P.listView.templateView,
    templateEdit : _CONFIGS_P.listView.templateEdit,
    initialize: function () {
      this.model.view = this;
      this.replaceView();
      this.listenTo(this.model, 'change', this.render);
    },
    render: function () {
      _BB_P.bindModel2view(this.model, this.$el);
      return this;
    },
    appendEvent: function() {
      if(typeof _CONFIGS_P.listView.appendEvent == 'function') {
        _CONFIGS_P.listView.appendEvent(this);
      }
    },
    replaceView: function() {
      if(this.templateView) {
        var _elem = $(this.templateView(this.model.toJSON()));
        if(this.$el.html()) this.$el.html(_elem.html());
        else this.setElement(_elem);
        msiMstr.renderFormEvents(this.$el);
        this.appendEvent();
        
      }
      this.render();
      this.$el.removeClass('edited');
      this.isEdit = false;
    },
    replaceEdit: function() {
      if(this.isEdit) return;
      
      if(this.templateEdit) {
        var _elem = $(this.templateEdit(this.model.toJSON()));
        if(this.$el.html()) this.$el.html(_elem.html());
        else this.setElement(_elem);
        msiMstr.renderFormEvents(this.$el);
        this.appendEvent();
      }
      this.render();
      this.$el.addClass('edited');
      this.isEdit = true;
    },
    events: {
       'click'                  : 'stateEdit'      // 編集モード
      ,'click .changeDrilldown' : 'stateDrilldown' // 明細編集モード
    },
    stateEdit: function(e) {
      this.replaceEdit();
      appendAutoNo($('.col-seq', _GLOBALS_P.listView.$el));
      setEditedPlan(true);
      msiMstr.renderScrollTo(this.$el);
      if(e) e.stopPropagation();  // バブリングストップ
    },
    stateDrilldown: function (e) {
      // 選択モデルに登録
      if(_GLOBALS_P.selectedModel) _GLOBALS_P.selectedModel.view.$el.removeClass('selected');
      _GLOBALS_P.selectedModel = this.model;
      // 明細モデル上書きプロパティに登録
      var _param = {
         moushikomi_kbn: this.model.get('moushikomi_kbn')
        ,category_kbn  : this.model.get('category_kbn')
        ,tab_no        : this.model.get('tab_no')
        ,tab_nm        : this.model.get('tab_nm')
        ,disp_kbn      : this.model.get('disp_kbn')
      };
      mstrSmisModelOverride(_param);
      // タイトル更新
      $('#selected-item').text((this.model.get('moushikomi_kbn__alt') || '')
                     + ' : ' + (this.model.get('category_kbn__alt') || '')
                     + ' : ' + (this.model.get('tab_nm') || '')
      );
      // 問合せ
      _param._rowid = this.model.get('_rowid');
      _GLOBALS_S.listView.doFetch();
      switchEditMode(true);
      if(e) e.stopPropagation();  // バブリングストップ
    }
  });
  // ---------------------
  // * bb ListView */
  var PlanListView = Backbone.View.extend({
    el: _CONFIGS_P.listView.el,
    initialize: function () {
      this.collection.view = this;
      this.listenTo(this.collection, 'reset' , this.render);
      this.listenTo(this.collection, 'add'   , this.add);
    },
    render: function () {
      $('tbody', this.$el).remove();
      this.collection.each(function(item) {
        item.set(msiMstr.toHashMap(item.toJSON()));
        this.add(item);
      }, this);
      appendAutoNo($('.col-seq', this.$el));
      return this;
    },
    add: function (item) {
      this.$el.append(new PlanListRowView({model: item}).render().el);
    },
    events: {
       'click .gotoFetch'     : 'doFetch'  // 一覧取得
      ,'click .gotoPut'       : 'doPut'    // 一括保存
      ,'click .gotoReset'     : 'doReset'  // 再取得
      ,'click .gotoCsvout'    : 'doCsvout' // CSV出力with ajax
    },
    doFetch: function() {
      console.info('doFetch');
      this.collection.fetch({reset:true});
      setEditedPlan(false);
      // console.debug(this.collection.toJSON());
    },
    _doInput: function(requestMethod, targetUrl){
      if(!editedConfirmPlan('変更を保存してよろしいですか？')) return;
      
      msiMstr.renderMessageClear(); // エラー表示のクリア
      var deferred = $.Deferred();  // 非同期チェーン
      deferred.then(function(self) {
        var _param = {};
        if(bindCollectionPlan(_param)) {
          // console.debug(self.collection.models);
          msiMstr.requestAjax({
             type: 'POST'
            ,url: _CONFIGS_P.urls.put
            ,file: $('input[type=file]', self.$el)
            ,context: self
            ,data: _param
            ,success: function(data) {
              setEditedPlan(false);
              this.render();
            }
          });
        }
        return self;
      }).then(function(self) {
        appendAutoNo($('.col-seq', self.$el));
        return self;
      });
      deferred.resolve(this);
    },
    doPut: function() {
      // console.info('doPut');
      this._doInput(msiMstr.requestPut, _CONFIGS_P.urls.put);
    },
    doReset: function() {
      if(!editedConfirmPlan('変更は保存されません。よろしいですか？')) return;
      this.doFetch();
    },
    // CSVボタン押下時
    doCsvout: function() {
        this.exePrint(true);
    },
    exePrint: function(csv) {
		var model   = new PlanModel();
        var appObj = model.toJSON();
        appObj["csv"] = csv;                // 0:新規 1:更新 2:確定
        var url = $.msiJqlib.baseUrl() + '/mstr/pdf1405/index/';
        var dataAppJson = JSON.stringify(appObj);
        msiLib2.fileDlAjax({
            url: url,
            data: {
                dataAppJson: dataAppJson
            }
        });             
    },
  });
  // ---------------------
  // * bb FuncView
  var PlanFuncView = Backbone.View.extend({
    el: _CONFIGS_P.funcView.el,
    initialize: function () {
    },
    events: {
       'click .gotoFetch'     : 'doFetch'  // 一覧取得 with ajax
      ,'click .gotoPut'       : 'doPut'    // 保存（更新） with ajax
      ,'click .gotoReset'     : 'doReset'  // 再取得
      ,'click .gotoCsvout'    : 'doCsvout' // CSV出力with ajax
    },
    doFetch : function() { _GLOBALS_P.listView.doFetch(); },
    doPut   : function() { _GLOBALS_P.listView.doPut();   },
    doReset : function() { _GLOBALS_P.listView.doReset(); },
    doCsvout : function(){ _GLOBALS_P.listView.doCsvout(); },
  });
  _GLOBALS_P.selectedModel = null;
  _GLOBALS_P.isEdit = undefined;
  // ---------------------
  // * Creates
  var model      = new PlanModel();
  var collection = new PlanCollection();
  _GLOBALS_P.collection = collection;
  _GLOBALS_P.listView   = new PlanListView({collection: collection});
  _GLOBALS_P.funcView   = new PlanFuncView({collection: collection});
  
  // ----------------------------------
  // * 施行プラン商品明細
  window.my.smsi.bb = new msiMstr.Bb(window.my.smsi.conf.modelInfo);
  var _BB_S = window.my.smsi.bb;
  var _CONFIGS_S = window.my.smsi.conf;
  var _GLOBALS_S = window.my.smsi;
  // ---------------------
  // * bb Model
  var SmsiModel = Backbone.Model.extend({
     idAttribute : '_rowid'
    ,defaults: function(){ return _BB_S.getDefaults(); }
    ,validate: function(attr){ return _BB_S.checkInputFrom(attr); }
  });
  // ---------------------
  // * bb Collection
  var SmsiCollection = Backbone.Collection.extend({
     model: SmsiModel
    ,url  : _CONFIGS_S.urls.fetch
  });
  // ---------------------
  // * bb ListRowView
  var SmsiListRowView = Backbone.View.extend({
    template: _CONFIGS_S.listView.templateView || _CONFIGS_S.listView.templateEdit,
    templateView : _CONFIGS_S.listView.templateView,
    templateEdit : _CONFIGS_S.listView.templateEdit,
    initialize: function () {
      this.model.view = this;
      this.replaceView();
      this.listenTo(this.model, 'change', this.render);
      // this.listenTo(this.model, 'invalid', this.renderMessages);
    },
    render: function () {
      _BB_S.bindModel2view(this.model, this.$el);
      return this;
    },
    appendEvent: function() {
      if(typeof _CONFIGS_S.listView.appendEvent == 'function') {
        _CONFIGS_S.listView.appendEvent(this);
      }
    },
    replaceView: function() {
      if(this.templateView) {
        var _elem = $(this.templateView(this.model.toJSON()));
        if(this.$el.html()) this.$el.html(_elem.html());
        else this.setElement(_elem);
        msiMstr.renderFormEvents(this.$el);
        this.appendEvent();
      }
      this.render();
      this.$el.removeClass('edited');
      appendAutoNo($('.col-seq', _GLOBALS_S.listView.$el));
      this.isEdit = false;
    },
    replaceEdit: function() {
      if(this.isEdit) return;
      
      if(this.templateEdit) {
        var _elem = $(this.templateEdit(this.model.toJSON()));
        if(this.$el.html()) this.$el.html(_elem.html());
        else this.setElement(_elem);
        msiMstr.renderFormEvents(this.$el);
        this.appendEvent();
      }
      this.render();
      this.$el.addClass('edited');
      appendAutoNo($('.col-seq', _GLOBALS_S.listView.$el));
      this.isEdit = true;
    },
    renderMessages: function(model, errors) {
      msiMstr.renderMessage(errors, model.view.$el);
    },
    events: {
       'click'                : 'stateEdit'   // 編集モード
      ,'click .gotoHide'      : 'doHide'      // 論理削除 with ajax
      ,'click .changeCreate'  : 'stateCreate' // 新規作成
    },
    doHide    : function(e){
      // * 残件数確認
      var _enableCnt = 0;
      this.collection.each(function(item, index){
        if(item.get('delete_flg') != 1) _enableCnt++;
      });
//      if(_enableCnt <= 1) {
//        $.msiJqlib.showErr('1件以上登録されている必要があります。削除できません。');
//        return;
//      }
      // * 処理
      if(this.model.get('_rowid')) {
        this.model.set({delete_flg:'1'});
        this.$el.empty().remove();
      } else {
        this.model.collection.remove(this.model.cid);
      }
      _GLOBALS_S.listView.checkNonData();
      appendAutoNo($('.col-seq', _GLOBALS_S.listView.$el));
      setEditedSmsi(true);
      if(e) e.stopPropagation();  // バブリングストップ
    },
    stateCreate : function(e){
      var _idx   = this.collection.indexOf(this.model);
      var _model = new SmsiModel(mstrSmisModelOverride(), {collection: this.collection});
      _model.id = this.collection.length+1;
      this.collection.add(_model, {at: _idx+1}).view.stateEdit();
      _GLOBALS_S.listView.checkNonData();
      appendAutoNo($('.col-seq', _GLOBALS_S.listView.$el));
      if(e) e.stopPropagation();  // バブリングストップ
    },
    stateView : function(e){
      this.replaceView();
    },
    stateEdit : function(e){
      this.replaceEdit();
      setEditedSmsi(true);
      msiMstr.renderScrollTo(this.$el);
    }
  });
  // ---------------------
  // * bb ListView
  var SmsiListView = Backbone.View.extend({
    el: _CONFIGS_S.listView.el,
    initialize: function() {
      this.checkNonData();
      this.listenTo(this.collection, 'reset' , this.render);
      this.listenTo(this.collection, 'add', this.add);
      this.listenTo(this.collection, 'remove', this.remove);
    },
    render: function() {
      $('tbody', this.$el).remove();
      // console.debug(this.$el);
      this.collection.each(function (item) {
        item.set(msiMstr.toHashMap(item.toJSON()));
        if(item.get('delete_flg') != 1) this.add(item);
      }, this);
      appendAutoNo($('.col-seq', this.$el));
      return this;
    },
    add: function(item, collection, option) {
      var $view = new SmsiListRowView({model: item, collection: this.collection}).render();
      var $bview;
      if(option && option.at) $bview = this.collection.at(option.at-1);
      if($bview instanceof Backbone.Model) {
        $bview.view.$el.after($view.$el);
      } else {
        this.$el.append($view.$el);
      }
    },
    remove: function(item, collection, option) {
      item.view.$el.remove();
    },
    doFetch: function() {
      // console.log('doFetch');
      this.collection.fetch({
        data: $.param(mstrSmisModelOverride()),
        reset:true
      });
      setEditedSmsi(false);
    },
    doPut: function() {
      // console.log('doPut');
      if(!editedConfirmSmsi('変更を保存してよろしいですか？')) return;
      
      msiMstr.renderMessageClear(); // エラー表示のクリア
      var deferred = $.Deferred();  // 非同期チェーン
      deferred.then(function(self) {
        var _param = {};
        if(bindCollectionSmsi(_param)) {
          // console.debug(self.collection.models);
          msiMstr.requestAjax({
             type: 'POST'
            ,url: _CONFIGS_S.urls.put
            ,file: $('input[type=file]', self.$el)
            ,context: self
            ,data: _param
            ,success: function(data) {
              setEditedSmsi(false);
              this.render();
            }
          });
        }
        return self;
      }).then(function(self) {
        appendAutoNo($('.col-seq', self.$el));
        return self;
      });
      deferred.resolve(this);
    },
    doReset: function() {
      if(!editedConfirmSmsi('変更は保存されません。よろしいですか？')) return;
      // console.log('doReset');
      this.doFetch();
    },
    stateCreate: function(e) {
      var _model = new SmsiModel(mstrSmisModelOverride(), {collection: this.collection});
      this.collection.add(_model).view.stateEdit();
      this.checkNonData();
      appendAutoNo($('.col-seq', this.$el));
    },
    checkNonData: function() {
      if(this.collection.length <= 0) {
        this.$el.append($(_CONFIGS_S.listView.templateNone()));
      } else {
        $('.nodata', this.$el).remove();
      }
    }
  });
  // ---------------------
  // * bb FuncView
  var SmsiFuncView = Backbone.View.extend({
    el: _CONFIGS_S.funcView.el,
    events: {
       'click .gotoFetch'        : 'doFetch'       // 一覧取得 with ajax
      ,'click .gotoPut'          : 'doPut'         // 保存（更新） with ajax
      ,'click .gotoReset'        : 'doReset'       // 再取得
      ,'click .changeCreate'     : 'stateCreate'   // 新規作成状態へ移行[gotoPost]
      ,'click .gotoCsvout'    : 'doCsvout' // CSV出力with ajax
    }
    ,doFetch    : function() { _GLOBALS_S.listView.doFetch();     }
    ,doPut      : function() { _GLOBALS_S.listView.doPut();       }
    ,doReset    : function() { _GLOBALS_S.listView.doReset();     }
    ,stateCreate: function() { _GLOBALS_S.listView.stateCreate(); }
    ,doCsvout	 : function() { _GLOBALS_S.listView.doCsvout(); }
});
  // ---------------------
  // * Creates
  var collection = new SmsiCollection();
  _GLOBALS_S.collection = collection;
  _GLOBALS_S.listView   = new SmsiListView({collection: collection});
  _GLOBALS_S.funcView   = new SmsiFuncView({collection: collection});
 
  // ==========================================================================
  // * execute
  // --------------------------------------------------------------------------
  window.my.plan.funcView.doFetch();
});