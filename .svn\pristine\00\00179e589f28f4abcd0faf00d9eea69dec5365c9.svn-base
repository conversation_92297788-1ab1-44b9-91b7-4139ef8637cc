<?php

/**
 * Juchu_JuchuPhoto
 *
 * 写真加工クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuPhoto
 * <AUTHOR> Sai
 * @since      2014/05/02
 * @filesource 
 */

/**
 * 写真加工クラス
 *
 * @category   App
 * @package    controllers\Juchu\Juchu_JuchuPhoto
 * <AUTHOR>
 * @since      2014/05/02
 */
class Juchu_JuchuPhoto extends Juchu_JuchuMitsuAbstract {

    /** カテゴリ区分: 3=>写真加工 */
    const CATEGORY_KBN3 = 3;

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/05/02
     * @return array jsonData
     */
    public function getInitData() {
        $data = $this->getData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 写真加工データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/02
     * @return array 写真加工データ
     */
    private function getData() {
        // APPデータを取得する
        $dataApp = $this->getAppData();
        // 写真加工情報を取得する
        $dataPhotoCol = $this->getPhotoData();
        // 部門を取得する
        $bumonkbn = $this->getBumonMst();
//        // 仕入れ先を取得する
//        $siirekbn = $this->getSiireMst();
        // 画面データを設定する
	$kaisya_cd = Msi_Sys_Utils::getKaisyaCd();

        $data = array(
            'dataApp' => $dataApp,
            'dataPhotoCol' => $dataPhotoCol,
//            'dataMonitor' => $dataMonitor,
            'bumonkbn' => $bumonkbn,
//            'siirekbn' => $siirekbn,
            'kaisya_cd' => $kaisya_cd
        );
        return $data;
    }

    /**
     *
     * APPデータを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/02
     * @return array APPデータ
     */
    private function getAppData() {
        $sougi = $this->getSougiInfo();
        if (count($sougi) > 0) {
            $appData['sougi_ymd'] = $sougi['nitei_ymd'];
        }
        $appData['k_sex_kbn'] = $this->_selectSekoKihon['k_sex_kbn'];
        $appData['seko_no'] = $this->_sekoNo;
        $appData['juchu_kakute_ymd'] = $this->_juchuKakuteiYMD;
        $panelData = $this->getPanelInfo();
        $appData = array_merge($appData, $panelData);
        return $appData;
    }

    /**
     *
     * 施行写真加工情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/02
     * @return array 施行写真加工情報
     */
    private function getPhotoData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_SekoShasinInfo::find($db, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 保存処理
     *
     * <AUTHOR> Sai
     * @since 2014/05/02
     */
    public function save() {
        $cnt = 0;
        $req = Msi_Sys_Utils::getRequestObject();
        // 受注伝票作成フラグ 
        //$dpCreFlg = Msi_Sys_Utils::json_decode($req->getPost('dpCreFlg'));
        //if ($dpCreFlg && $this->isKakuteiOutNg()) {
        //    return;
        //}
        // チェンジフラグ
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        // 写真加工データ
        $dataPhoto = Msi_Sys_Utils::json_decode($req->getPost('dataPhoto'));

        $db = Msi_Sys_DbManager::getMyDb();
        $yakimashiKbn = $dataPhoto['yakimashi_kbn'];
        // 写真加工存在チェック
        $selectPhoto = DataMapper_SekoShasinInfo::find($db, array('seko_no' => $this->_sekoNo, 'yakimashi_kbn' => $yakimashiKbn));
        if (count($selectPhoto) > 0 && isset($selectPhoto[0]['i_free3'])) {
            $this->outNgJson('発注済みのため、変更することができません');
            return;
        }

        if ($changeFlg['photoChangeFlg']) {
            // 写真加工データを保存する
            $cnt += $this->savePhoto($db, $dataPhoto, $selectPhoto);
            // 受注伝票と施行発注管理自動作成する
            if ($dpCreFlg) {
                $cnt += $this->saveJuchuHachu($db);
            }
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, 1, 5);

        $db->commit();

        $data = $this->getData();
        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
        // 画面データを設定する
        $data['dataSideMenu'] = Juchu_Utils::getSideMenuData('photo');
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '更新しました';
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 写真加工削除処理
     *
     * <AUTHOR> Sai
     * @since 2014/05/09
     */
    public function delete() {
        $cnt = 0;
        if ($this->_juchuKakuteiYMD) {
            $this->outNgJson('見積が確定されたため、削除することができません。');
            return;
        }
        $req = Msi_Sys_Utils::getRequestObject();
        // 写真加工データ
        $dataPhoto = Msi_Sys_Utils::json_decode($req->getPost('dataPhoto'));

        $db = Msi_Sys_DbManager::getMyDb();
        $yakimashiKbn = $dataPhoto['yakimashi_kbn'];
        // 写真加工存在チェック
        $selectPhoto = DataMapper_SekoShasinInfo::find($db, array('seko_no' => $this->_sekoNo, 'yakimashi_kbn' => $yakimashiKbn));
        if (count($selectPhoto) > 0 && isset($selectPhoto[0]['i_free3'])) {
            $this->outNgJson('発注済みのため、削除することができません');
            return;
        }

        // 写真加工データを削除する
        $cnt += $this->deletePhoto($db, $dataPhoto);
        // 受注伝票と施行発注管理自動作成する
        $cnt += $this->saveJuchuHachu($db);
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, 1, 5);

        $db->commit();

        $data = $this->getData();
        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
        // 画面データを設定する
        $data['dataSideMenu'] = Juchu_Utils::getSideMenuData('photo');
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '削除しました';
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 写真加工保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/05/08
     * @param Msi_Sys_Db $db db
     * @param array $dataPhoto 写真加工データ
     * @return int 更新件数
     */
    private function savePhoto($db, $dataPhoto, $selectPhoto) {
        $yakimashiKbn = $dataPhoto['yakimashi_kbn'];
        // 写真加工存在チェック
//        $selectPhoto = DataMapper_SekoShasinInfo::find($db, array('seko_no' => $this->_sekoNo, 'yakimashi_kbn' => $yakimashiKbn));
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'gazo_oid');
        array_push($except, 's_huku_oid');
        array_push($except, 's_haikei_oid');
        array_push($except, 's_huku_nm');
        array_push($except, 's_haikei_nm');
        array_push($except, 's_output_date');
        array_push($except, 's_output_time');
        array_push($except, 'p_limit_date');
        array_push($except, 'p_limit_time');
        array_push($except, 'sougi_ymd');
        array_push($except, 'k_sex_kbn');
        array_push($except, 'panel_size_info_1');
        array_push($except, 'panel_size_info_2');
        array_push($except, 'panel_size_info_3');
        array_push($except, 'panel_size_info_4');
        array_push($except, 'panel_size_info_5');
        array_push($except, 'panel_price_1');
        array_push($except, 'panel_price_2');
        array_push($except, 'panel_price_3');
        array_push($except, 'panel_price_4');
        array_push($except, 'panel_price_5');
        array_push($except, 'juchu_kakute_ymd');
        array_push($except, 'tanto_nm');
        array_push($except, 'm_kbn');
        array_push($except, 'm_haikei_kbn');
        array_push($except, 'gazo_cd2');
        array_push($except, 'i_free3');
        if ($dataPhoto['i_free1'] === "") {
            $dataPhoto['i_free1'] = null;
        }
        if ($dataPhoto['i_free2'] === "") {
            $dataPhoto['i_free2'] = null;
        }
        

        if (count($selectPhoto) === 0) {
            $dataPhoto['seko_no'] = $this->_sekoNo;
            // 写真加工登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seko_shasin_info', $dataPhoto, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号 
            $where['yakimashi_kbn'] = $yakimashiKbn;  // 焼増区分
            $where['delete_flg'] = 0;  // 削除フラグ
            // 写真加工更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_shasin_info', $dataPhoto, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 写真加工削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/05/09
     * @param Msi_Sys_Db $db db
     * @param array $dataPhoto 写真加工データ
     * @return int 削除件数
     */
    private function deletePhoto($db, $dataPhoto) {
        $yakimashiKbn = $dataPhoto['yakimashi_kbn'];
        $sql = "
        DELETE
            FROM
                 seko_shasin_info
        WHERE
            seko_no = :seko_no
        AND yakimashi_kbn = :yakimashi_kbn
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'yakimashi_kbn' => $yakimashiKbn));
        return $cnt;
    }

    /**
     * 受注伝票と施行発注管理自動作成処理
     * <AUTHOR> Sai
     * @since 2014/06/02
     * @return $cnt 更新データ
     */
    private function saveJuchuHachu($db) {
        $cnt = 0;
        $dataApp = array();
        // 対象データを取得する
        list($dataCol, $dataDelCol) = $this->getJuchuPhotoWebData();
        // 表示順を再設定する
        App_Utils::resetDispNo($dataCol, $this->_sekoNo, $this->_sekoNoSub);
        // 受注伝票を保存する
        $cnt += $this->saveJuchu($db, $dataApp, $dataCol, $dataDelCol);
        // 施行発注管理情報を保存する
        $cnt +=$this->saveHachuInfo($db, $dataApp, $dataCol, $dataDelCol);
        // 見積確定かつコンバートデータの場合は集計処理を行わない
        $denpyoNo = $this->getJuchudenpyoNo();
        if ($this->isMitsuKakutei()) {
            $uridenNo = $this->getUriagedenpyoNo();
            $uriData = DataMapper_UriageDenpyo::findDenpyo2($db, array('uri_den_no' => $uridenNo));
            if ($uriData[0]['convert_kbn'] == '0') {
                $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
            }
        } else {
            $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
        }
        return $cnt;
    }

    /**
     * 受注伝票と施行発注管理自動作成に必要なデータの取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/02
     * @return array $dataCol→更新データ, $dataDelCol→削除データ
     */
    private function getJuchuPhotoWebData() {
        $dataCol = array();
        $dataDelCol = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $trnData = DataMapper_SekoShasinInfo::find($db, array('seko_no' => $this->_sekoNo));
        // マスタより取得する
        $webMstData = $this->getJuchuDataFromWebMst(self::CATEGORY_KBN3);
        // 伝票明細がすでに存在する場合、削除対象にする
        foreach ($webMstData as &$mstOne) {
            if (isset($mstOne['msi_no'])) {
                if (!isset($mstOne['m_shohin_cd']) || ($mstOne['add_kbn'] === self::ADD_KBN_AUTO)) { // 施行プラン明細に存在する場合は削除対象外
                    $dataDelCol[] = $mstOne;
                }
                $mstOne['msi_no'] = null; // 明細番号クリア
            }
        }
        // 登録データを取得する
        foreach ($trnData as $trnOne) {
            // 焼増区分 0：写真1 1：写真2 2：写真3 3：写真4 4：写真5
            $addKbn = $trnOne['yakimashi_kbn'];
            // 写真半切区分 0：新規 1：焼増
            $h_kbn = $trnOne['s_han_kbn'];
            // 写真半切枚数
            $h_cnt = $trnOne['s_han_cnt'];
            // 写真加工区分 0：カラー 1：白黒
            $s_kako_kbn = $trnOne['s_kako_kbn'];
            $monokuro_flg = false;
            if (empty($this->_gojokaiCoseCd) && $s_kako_kbn === '1') {
                $monokuro_flg = true;
            }

            if ($h_cnt > 0) {
                if ($monokuro_flg) {
                    $mstData = $this->getWebMstData($webMstData, "9", "9", $h_cnt, $addKbn, $s_kako_kbn);
                } else {
                    $mstData = $this->getWebMstData($webMstData, "0", $h_kbn, $h_cnt, $addKbn, $s_kako_kbn);
                }
                if (count($mstData) > 0) {
                    $dataCol = array_merge($dataCol, $mstData);
                }
            }
            // 写真キャビネ区分 0：新規 1：焼増
            $c_kbn = $trnOne['s_cab_kbn'];
            // 写真キャビネ枚数
            $c_cnt = $trnOne['s_cab_cnt'];
            if ($c_cnt > 0) {
                if ($monokuro_flg) {
                    $mstData = $this->getWebMstData($webMstData, "9", "9", $h_cnt, $addKbn, $s_kako_kbn);
                } else {
                    $mstData = $this->getWebMstData($webMstData, "1", $c_kbn, $c_cnt, $addKbn, $s_kako_kbn);
                }
                if (count($mstData) > 0) {
                    $dataCol = array_merge($dataCol, $mstData);
                }
            }
            // パネル写真
            $p_size_kbn = $trnOne['p_size_kbn'];
            // 1：半マットパネル 2：全紙マットパネル 3：全倍 4：大全倍 5：超全倍
            if ($p_size_kbn > 0) {
                $mstData = $this->getWebMstData($webMstData, "2", $p_size_kbn, 1, $addKbn, $s_kako_kbn);
                if (count($mstData) > 0) {
                    $dataCol = array_merge($dataCol, $mstData);
                }
            }
        }
        return array($dataCol, $dataDelCol);
    }

    /**
     * Webマスタレコード取得処理
     *
     * <AUTHOR> Sai
     * @param array $webMstData Webマスタデータ
     * @param string $kbn1  0:半切 1：キャビネ 2:パネル写真
     * @param string $kbn2  半切とキャビネ　0:新規 1：焼増し　パネル写真 1：半マットパネル 2：全紙マットパネル 3：全倍 4：大全倍 5：超全倍
     * @param string $cnt  枚数
     * @param string $addKbn 焼増区分 0：写真1 1：写真2 2：写真3 3：写真4 4：写真5
     * @param string $s_kako_kbn  写真加工区分 0：カラー 1：白黒
     * @since 2014/06/02
     * @return array $mstData 受注データ
     */
    private function getWebMstData($webMstData, $kbn1, $kbn2, $cnt, $addKbn, $s_kako_kbn) {
        $mstData = array();
        foreach ($webMstData as $mstOne) {
            $mstOne['k_free1'] = $addKbn;
            // 区分1 0:半切 1：キャビネ
            $m_kbn1 = $mstOne['kbn1'];
            // 区分2 0:新規 1：焼増し 2:パネル写真
            $m_kbn2 = $mstOne['kbn2'];
            if ($kbn1 === $m_kbn1 && $kbn2 === $m_kbn2) {
                if ($kbn1 === '2') { // パネル写真
                    $mstOne['juchu_suryo'] = $cnt; // 数量
                    $mstOne['add_kbn'] = self::ADD_KBN_AUTO; // 追加区分　9:自動作成
                    $mstData[] = $mstOne;
                } else if ($kbn1 === '9') { // 白黒
                    $mstOne['juchu_suryo'] = $cnt; // 数量
                    $mstOne['add_kbn'] = self::ADD_KBN_AUTO; // 追加区分　9:自動作成
                    $mstData[] = $mstOne;
                } else if ($kbn2 === '0') { // 半切とキャビネ　0:新規
                    $new_cnt = (int) $mstOne['m_suryo'];
                    // 焼増区分 0：写真1に限り、施行プラン明細の枚数分作成しない
                    if ($addKbn !== "0") {
                        $new_cnt = 0;
                    }
                    // 新規の場合、施行プラン明細に存在しない場合は1枚目が新規、存在する場合は、その枚数分は作成しない、それ以降は焼増し
                    if ($new_cnt === 0) {
                        // 葬送儀礼使用で白黒の場合、半切の一枚目のみはカラーで作成する
                        if ($kbn1 === '0' || ($kbn1 === '1' && $s_kako_kbn === '0')) {
                            $new_cnt = 1;
                            $mstOne['juchu_suryo'] = $new_cnt; // 数量
                            $mstOne['add_kbn'] = self::ADD_KBN_AUTO; // 追加区分　9:自動作成
                            $mstData[] = $mstOne;
                        }
                    }
                    // 白黒の場合、白黒の写真を作成する
                    if ($s_kako_kbn === '1') {
                        $mstSecond = $this->getWebMstDataOne($webMstData, '9', '9');
                    } else {
                        $mstSecond = $this->getWebMstDataOne($webMstData, $kbn1, "1");
                    }
                    if ($cnt > $new_cnt && count($mstSecond) > 0) {
                        $mstSecond['k_free1'] = $addKbn;
                        $mstSecond['juchu_suryo'] = $cnt - $new_cnt; // 数量
                        $mstSecond['add_kbn'] = self::ADD_KBN_AUTO; // 追加区分　9:自動作成
                        $mstData[] = $mstSecond;
                    }
                } else if ($kbn2 === '1') { // 半切とキャビネ　1:焼増し
                    // 白黒の場合、白黒の写真を作成する
                    if ($s_kako_kbn === '1') {
                        $mstOne = $this->getWebMstDataOne($webMstData, '9', '9');
                    }
                    if (count($mstOne) > 0) {
                        $mstOne['juchu_suryo'] = $cnt; // 数量
                        $mstOne['add_kbn'] = self::ADD_KBN_AUTO; // 追加区分　9:自動作成
                        $mstData[] = $mstOne;
                    }
                }
                break;
            }
        }
        return $mstData;
    }

    /**
     * Webマスタワンレコード取得処理
     *
     * <AUTHOR> Sai
     * @param array $webMstData Webマスタデータ
     * @param string $kbn1  0:半切 1：キャビネ
     * @param string $kbn2  0:新規 1：焼増し
     * @since 2014/06/02
     * @return array $mstDataOne Webマスタワンレコード
     */
    private function getWebMstDataOne($webMstData, $kbn1, $kbn2) {
        $mstDataOne = array();
        foreach ($webMstData as $mstOne) {
            // 区分1 0:半切 1：キャビネ
            $m_kbn1 = $mstOne['kbn1'];
            // 区分2 0:新規 1：焼増し
            $m_kbn2 = $mstOne['kbn2'];
            if ($kbn1 === $m_kbn1 && $kbn2 === $m_kbn2) {
                $mstDataOne = $mstOne;
                break;
            }
        }
        return $mstDataOne;
    }

    /**
     *
     * パネル写真情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/06/25
     * @return array パネル写真情報
     */
    private function getPanelInfo() {
        $panelData = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sm.shohin_cd
            ,wsm.kbn2
            ,sm.shohin_naiyo
            ,stm.hanbai_tnk
        FROM
            web_disp_shohin_mst wsm
            INNER JOIN
                shohin_mst sm
            ON  (
                    wsm.shohin_cd = sm.shohin_cd
                AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                AND sm.delete_flg = 0
                )
            LEFT OUTER JOIN
                shohin_tanka_mst stm
            ON  (
                    wsm.shohin_cd = stm.shohin_cd
                AND CURRENT_DATE BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                AND stm.delete_flg = 0
                )
        WHERE
            wsm.delete_flg = 0
        AND wsm.moushikomi_kbn = 1
        AND wsm.kbn1 = 2
        AND wsm.category_kbn = 3
        AND CURRENT_DATE BETWEEN wsm.tekiyo_st_date AND wsm.tekiyo_ed_date
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            foreach ($select as $value) {
                $kbn2 = $value['kbn2'];
                switch ($kbn2) {
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                        $panelData['panel_size_info_' . $kbn2] = $value['shohin_naiyo'];
                        $panelData['panel_price_' . $kbn2] = $value['hanbai_tnk'];
                        break;
                    default:
                        break;
                }
            }
        }
        return $panelData;
    }

    /**
     *
     * モニタ遺影の発注情報を作成する
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @param Msi_Sys_Db $db db
     * @return $cnt 処理件数
     */
    private function saveMonitorHachuInfo($db) {
        $ha_rp_kbn = '5'; // ビューアショット依頼書
        $m_kbn = '0'; // モニター遺影区分 0：なし 1：あり
        $selMonitor = $this->getMonitorData();
        if (count($selMonitor) > 0) {
            $m_kbn = $selMonitor['m_kbn'];
        }
        // 一旦ビューアショット依頼書の発注管理情報を削除する
        $cnt = $this->deleteHachuInfoByHarpKbn($db, $ha_rp_kbn);
        //ビューアショット依頼書の発注管理情報を作成する
        if ($m_kbn === '1') {
            $cnt = $this->createHachuInfo($db, $ha_rp_kbn);
        }
        return $cnt;
    }

    /**
     *
     * 部門マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return array 部門名配列
     */
    private function getBumonMst() {
        $arrBumonNames = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            bm.bumon_cd
            ,bm.bumon_lnm
        FROM
            BUMON_MST bm
        WHERE
            CURRENT_DATE BETWEEN bm.tekiyo_st_date AND bm.tekiyo_ed_date
        AND bm.bumon_cd in('01700', '01900', '02000')
        AND bm.delete_flg = 0
        ORDER BY
            bm.bumon_cd
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrBumonNames[$select[$i]['bumon_cd']] = $select[$i]['bumon_lnm'];
            }
        }
        return $arrBumonNames;
    }

    /**
     *
     * 仕入れ先マスタを取得する
     *
     * <AUTHOR> Sai Taikichi
     * @since 2021/1/24
     * @return array 仕入れ先配列
     */
    private function getSiireMst() {
        $arrSiireNames = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sm.siire_cd
            ,sm.siire_lnm
        FROM
            SIIRE_MST sm
        WHERE
            CURRENT_DATE BETWEEN sm.tekiyo_st_date AND sm.tekiyo_ed_date
        AND sm.siire_kbn = 4
        AND sm.delete_flg = 0
        ORDER BY
            sm.siire_cd
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrSiireNames[$select[$i]['siire_cd']] = $select[$i]['siire_lnm'];
            }
        }
        return $arrSiireNames;
    }

}
