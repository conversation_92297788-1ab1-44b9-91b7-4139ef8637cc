/** 
 * @fileoverview 運送実績 登録処理
 * @version      2020/04/xx mihara
 */
msiGlobalObj.myApp = msiGlobalObj.myApp || { appView:null, mstrData:null };// グローバルな受け渡し用

$( function() {
    "use strict";

    var gMyApp = msiGlobalObj.myApp;
    var getAppView = function() { return msiGlobalObj.myApp.appView; }
    var getMstr    = function() { return msiGlobalObj.myApp.mstrData; }
    var gMySubViewApp = null;

    var utils = window.msiBbUtils;
    var viewUtils = window.msiBbViewUtils;
    var mixin = window.msiBbMixin;


    var gStepMinute = 1;
    var myMstrData = [];
    var mergedMstrData = [];

    var _itemMapper =
{
// item                  label       initVal   validation     selector         bindings
// uketsuke_no:     '受付番号          ""        _na              _ex          _na',
// uketuke_no_sub:  '受付番号枝番      ""        _na              _ex          _na',
// msi_no:          '明細番号          ""        _na              _ex          _na',
//
depature_kbn:    '出発地区分        ""        req              .depature_kbn      sel2RtCheck2.depature_kbn',
depature_nm:     '出発地名          ""        max(60)          .depature_nm       rtCheck',
depature_zip_cd: '出発地郵便番号    ""        zip              .depature_zip_cd   rtCheck',
depature_addr1:  '出発地住所1       ""        max(60)          .depature_addr1    rtCheck',
depature_addr2:  '出発地住所2       ""        max(60)          .depature_addr2    rtCheck',
depature_tel:    '出発地電話番号    ""        tel              .depature_tel      rtCheck',
depature_meter:  '出発時メーター    ""        req,number,nmax(999999)    .depature_meter    commaRtCheck',
dpt_ymd:         '日付(出発時)      ""        req,ymd          .dpt_ymd           rtCheck',
dpt_time:        '時間(出発時)      ""        req,time         .dpt_time          rtCheck',
depature_syako_opt: '出発地営業所(車庫)  ""   null             .depature_syako_opt    _ex',
depature_sonota: '出発地その他      ""        max(30)          .depature_sonota   rtCheck',
//
arrival_kbn:     '到着地区分        ""        null             .arrival_kbn      sel2RtCheck2.arrival_kbn',
arrival_nm:      'お迎え先名        ""        max(60)          .arrival_nm       rtCheck',
arrival_zip_cd:  'お迎え先郵便番号  ""        zip              .arrival_zip_cd   rtCheck',
arrival_addr1:   'お迎え先住所1     ""        max(60)          .arrival_addr1    rtCheck',
arrival_addr2:   'お迎え先住所2     ""        max(60)          .arrival_addr2    rtCheck',
arrival_tel:     'お迎え先電話番号  ""        tel              .arrival_tel      rtCheck',
arrival_meter:   '到着時メーター    ""        number,nmax(999999)        .arrival_meter    commaRtCheck',
arr_ymd:         '日付(到着時)      ""        ymd              .arr_ymd            rtCheck',
arr_time:        '時間(到着時)      ""        time             .arr_time           rtCheck',
arrival_syako_opt: '出発地営業所(車庫)  ""   null             .arrival_syako_opt    _ex',
arrival_sonota: '出発地その他      ""        max(30)          .arrival_sonota   rtCheck',
//
hanso_result:    '搬送結果          ""        null             .hanso_result       sel2RtCheck2.hanso_result',
//
pass_kaisya_nm:  '引渡会社名        ""        null             .pass_kaisya_nm      rtCheck',
pass_kaiin_kbn:  '会員区分          ""        null             .pass_kaiin_kbn      sel2RtCheck2.pass_kaiin_kbn',
pass_biko:       '引渡備考          ""        null,max(120)    .pass_biko           rtCheck',
//
ka_k_cif_no:     'CIFNo.（故人）    ""        null             .ka_k_cif_no         rtCheck',      
ka_k_l_knm:      '故人名(姓)カナ    ""        max(30)          .ka_k_l_knm          rtCheck',
ka_k_f_knm:      '故人名(名)カナ    ""        max(30)          .ka_k_f_knm          rtCheck',
ka_k_l_nm:       '故人名(姓)        ""        max(30)          .ka_k_l_nm           rtCheck',
ka_k_f_nm:       '故人名(名)        ""        max(30)          .ka_k_f_nm           rtCheck',
ka_k_sex_kbn:    '故人性別          ""        null             _input[name=ka_k_sex_kbn]   ',
ka_k_yubin_no:   '故人郵便番号      ""        zip              .ka_k_yubin_no       rtCheck',
ka_k_addr1:      '故人住所１        ""        max(30)          .ka_k_addr1          rtCheck',
ka_k_addr2:      '故人住所２        ""        max(30)          .ka_k_addr2          rtCheck',
ka_k_gengo:      '故人生年月日和暦  ""        null             _ex                         ',
ka_k_w_birth_date:   '故人生年月日      ""        null         .ka_k_w_birth_date   rtCheck',
ka_m_zoku_kbn2:     '故人喪主との続柄  ""        null             .ka_m_zoku_kbn2      sel2RtCheck2.ka_m_zoku_kbn2',
ka_k_tel:        '故人TEL           ""        tel              .ka_k_tel         rtCheck',
ka_k_fax:        '故人FAX           ""        tel              .ka_k_fax         rtCheck',
//
ka_iso_saki_kbn:     '安置先移送先区分  ""        null             .ka_iso_saki_kbn        sel2RtCheck2.ka_iso_saki_kbn',
ka_anti_sisetu_nm:   '安置先施設名      ""        max(30)          .ka_anti_sisetu_nm      rtCheck',
ka_anti_basho_nm:    '安置先場所名      ""        max(30)          .ka_anti_basho_nm       rtCheck',
ka_anti_saki_nm:     '安置先名称        ""        max(60)          .ka_anti_saki_nm        rtCheck',
ka_anti_yubin_no:    '安置先郵便番号    ""        zip              .ka_anti_yubin_no       rtCheck',
ka_anti_addr1:       '安置先住所1       ""        max(30)          .ka_anti_addr1          rtCheck',
ka_anti_addr2:       '安置先住所2       ""        max(30)          .ka_anti_addr2          rtCheck',
ka_anti_tel:         '安置先TEL         ""        tel              .ka_anti_tel            rtCheck',
// ka_k_birth_gengoyear(故人生年月日元号+年), ka_k_birth_month(故人生年月日:月), ka_k_birth_day(故人生年月日:月)
//
ha_k_cif_no:     'CIFNo.（故人）    ""        null             .ha_k_cif_no         rtCheck',      
ha_k_l_knm:      '故人名(姓)カナ    ""        max(30)          .ha_k_l_knm          rtCheck',
ha_k_f_knm:      '故人名(名)カナ    ""        max(30)          .ha_k_f_knm          rtCheck',
ha_k_l_nm:       '故人名(姓)        ""        max(30)          .ha_k_l_nm           rtCheck',
ha_k_f_nm:       '故人名(名)        ""        max(30)          .ha_k_f_nm           rtCheck',
ha_k_sex_kbn:    '故人性別          ""        null             _input[name=ha_k_sex_kbn]   ',
ha_k_yubin_no:   '故人郵便番号      ""        zip              .ha_k_yubin_no       rtCheck',
ha_k_addr1:      '故人住所１        ""        max(30)          .ha_k_addr1          rtCheck',
ha_k_addr2:      '故人住所２        ""        max(30)          .ha_k_addr2          rtCheck',
ha_k_gengo:      '故人生年月日和暦  ""        null             _ex                         ',
ha_k_w_birth_date:   '故人生年月日      ""        null         .ha_k_w_birth_date   rtCheck',
// ha_k_birth_gengoyear(故人生年月日元号+年), ha_k_birth_month(故人生年月日:月), ha_k_birth_day(故人生年月日:月)
ha_m_zoku_kbn2:     '故人喪主との続柄  ""        null             .ha_m_zoku_kbn2      sel2RtCheck2.ha_m_zoku_kbn2',
ha_k_tel:        '故人TEL           ""        tel              .ha_k_tel         rtCheck',
ha_k_fax:        '故人FAX           ""        tel              .ha_k_fax         rtCheck',
//
ha_iso_saki_kbn:     '安置先移送先区分  ""        null             .ha_iso_saki_kbn        sel2RtCheck2.ha_iso_saki_kbn',
ha_anti_sisetu_nm:   '安置先施設名      ""        max(30)          .ha_anti_sisetu_nm      rtCheck',
ha_anti_basho_nm:    '安置先場所名      ""        max(30)          .ha_anti_basho_nm       rtCheck',
ha_anti_saki_nm:     '安置先名称        ""        max(60)          .ha_anti_saki_nm        rtCheck',
ha_anti_yubin_no:    '安置先郵便番号    ""        zip              .ha_anti_yubin_no       rtCheck',
ha_anti_addr1:       '安置先住所1       ""        max(30)          .ha_anti_addr1          rtCheck',
ha_anti_addr2:       '安置先住所2       ""        max(30)          .ha_anti_addr2          rtCheck',
ha_anti_tel:         '安置先TEL         ""        tel              .ha_anti_tel            rtCheck',
    };

    var _JiSubModelBase = utils.genModelBase( _itemMapper,
    {
        setInitData: function( mydata ) {
            // console.log( 'hanso.jisekiSubView.js setInitData mydata =>', mydata );

            this.resetData( mydata.dataApp );

            var myMstrData = mydata.mstrData;
            mergedMstrData = _.extend( {}, getMstr(), myMstrData);

            // console.log( 'mergedMstrData=>', mergedMstrData );

            // setTimeout( function() { // app.$('.jiseki-main-view-jiseki-cont').fadeIn();  // ちらつきのごまかし
            // app.trigger('renderInit'); }, 0 );

            this.trigger('setInitData');
        },

        resetData: function( myApp ) {
            // console.log( 'hanso.jisekiSubView.js resetData myApp =>', myApp );

            this.set( myApp );
            this.markAsOrg();
        },

        restoreData: function() {
            var _orgDataApp = this.getOrgData();
            this.resetData( _orgDataApp );
        },

        genSendData: function() {
            /*
            var _keys = _.keys(_itemMapper);
            _keys.push( 'hanso_kbn', 'unkou_kbn', 'syaryo_cd', 'tanto_cd', 'uketsuke_no', 'uketuke_no_sub' );
            var rtnData = this.pick(_keys);
             */
            var rtnData = this.toJSON();
            rtnData = _.reduce( rtnData, function(ac, v, k) { if(!k.match(/^_/)) ac[ k ] = v; return ac; }, {} );

            var _appModel = getAppView().model;
            rtnData['patch_k_sex_kbn'] = _appModel.get('k_sex_kbn');
            rtnData['tanto_cd'] = _appModel.get('tanto_cd');
            rtnData['syaryo_cd'] = _appModel.get('syaryo_cd');

            return rtnData;
        },

        isDataChanged: function() { 
            if ( this.isChangedFromOrg('_,depature_syako_opt,depature_sonota,arrival_syako_opt,arrival_sonota') ) {
                return true;
            }
            // return false;
        },

        initialize: function() {
            this.on('change:ha_k_birth_gengoyear change:ha_k_birth_month change:ha_k_birth_day', this.chg_ha_k_birth);
            this.on('change:ka_k_birth_gengoyear change:ka_k_birth_month change:ka_k_birth_day', this.chg_ka_k_birth);
        },

        // 故人生年月日(仮安置)
        chg_ka_k_birth: function() {
            var _k_birth_gengoyear = this.get('ka_k_birth_gengoyear');
            var _k_birth_month     = this.get('ka_k_birth_month');
            var _k_birth_day       = this.get('ka_k_birth_day');
            var _k_gengo = null;
            var _k_birth_year = null;
            var _k_w_birth_date = null;

            if ( !$.msiJqlib.isNullEx2(_k_birth_gengoyear) ) {
                _k_gengo = _k_birth_gengoyear.slice(0, 1);
                _k_birth_year = _k_birth_gengoyear.slice(1);
            }
            if ( !$.msiJqlib.isNullEx2(_k_birth_year) &&
                 !$.msiJqlib.isNullEx2(_k_birth_month) && !$.msiJqlib.isNullEx2(_k_birth_day) ) {
                _k_w_birth_date = _k_birth_year + '/' + _k_birth_month + '/' + _k_birth_day;
            }

            if ( (!$.msiJqlib.isNullEx2(_k_birth_gengoyear) || !$.msiJqlib.isNullEx2(_k_birth_month) || !$.msiJqlib.isNullEx2(_k_birth_day))
               && ($.msiJqlib.isNullEx2(_k_gengo) || $.msiJqlib.isNullEx2(_k_w_birth_date)) ) {
                // 何かが設定されていて形式エラーなら、エラー値を設定する
                _k_w_birth_date = 'ERROR';
            }

            this.set('ka_k_gengo', _k_gengo);
            this.set('ka_k_w_birth_date', _k_w_birth_date);
        },

        // 故人生年月日(安置)
        chg_ha_k_birth: function() {
            var _k_birth_gengoyear = this.get('ha_k_birth_gengoyear');
            var _k_birth_month     = this.get('ha_k_birth_month');
            var _k_birth_day       = this.get('ha_k_birth_day');
            var _k_gengo = null;
            var _k_birth_year = null;
            var _k_w_birth_date = null;

            if ( !$.msiJqlib.isNullEx2(_k_birth_gengoyear) ) {
                _k_gengo = _k_birth_gengoyear.slice(0, 1);
                _k_birth_year = _k_birth_gengoyear.slice(1);
            }
            if ( !$.msiJqlib.isNullEx2(_k_birth_year) &&
                 !$.msiJqlib.isNullEx2(_k_birth_month) && !$.msiJqlib.isNullEx2(_k_birth_day) ) {
                _k_w_birth_date = _k_birth_year + '/' + _k_birth_month + '/' + _k_birth_day;
            }

            if ( (!$.msiJqlib.isNullEx2(_k_birth_gengoyear) || !$.msiJqlib.isNullEx2(_k_birth_month) || !$.msiJqlib.isNullEx2(_k_birth_day))
               && ($.msiJqlib.isNullEx2(_k_gengo) || $.msiJqlib.isNullEx2(_k_w_birth_date)) ) {
                // 何かが設定されていて形式エラーなら、エラー値を設定する
                _k_w_birth_date = 'ERROR';
            }

            this.set('ha_k_gengo', _k_gengo);
            this.set('ha_k_w_birth_date', _k_w_birth_date);
        },

        // 故人生年月日(仮安置)
        set_ka_k_birth: function(_k_gengo, _k_w_birth_date) {
            var _k_birth_gengoyear = this._getBirthGengoYear( _k_gengo, _k_w_birth_date ); // ex. 元号+和暦年を返す ex.S50
            var _k_birth_month = this._getBirthMonth(_k_w_birth_date);
            var _k_birth_day   = this._getBirthDay(_k_w_birth_date);
            this.set('ka_k_birth_gengoyear', _k_birth_gengoyear);
            this.set('ka_k_birth_month', _k_birth_month);
            this.set('ka_k_birth_day', _k_birth_day);
        },

        // 故人生年月日(安置)
        set_ha_k_birth: function(_k_gengo, _k_w_birth_date) {
            var _k_birth_gengoyear = this._getBirthGengoYear( _k_gengo, _k_w_birth_date ); // ex. 元号+和暦年を返す ex.S50
            var _k_birth_month = this._getBirthMonth(_k_w_birth_date);
            var _k_birth_day   = this._getBirthDay(_k_w_birth_date);
            this.set('ha_k_birth_gengoyear', _k_birth_gengoyear);
            this.set('ha_k_birth_month', _k_birth_month);
            this.set('ha_k_birth_day', _k_birth_day);
        },

        // gengo+wareki年から 年ドロップダウン(sel2)の id (ex. S50)を返す
        // gengo(M|T|S|H|R), wareki(yy/mm/dd)
        _getBirthGengoYear: function(gengo, wareki) {
            if ( !_.isString(gengo) || !_.isString(wareki) ) {
                return null;
            }
            var p = wareki.split(/[/-]/);
            if ( $.msiJqlib.isNullEx2(p[0]) ) {
                return null;
            }
            var val = gengo + ('00'+p[0]).slice(-2);
            return val;
        },

        // wareki(yy/mm/dd)から月(mm)を返す
        _getBirthMonth: function(wareki) {
            if ( !_.isString(wareki) ) {
                return null;
            }
            var p = wareki.split(/[/-]/);
            if ( $.msiJqlib.isNullEx2(p[1]) ) {
                return null;
            }
            var val = ('00'+p[1]).slice(-2);
            return val;
        },

        // wareki(yy/mm/dd)から日(dd)を返す
        _getBirthDay: function(wareki) {
            if ( !_.isString(wareki) ) {
                return null;
            }
            var p = wareki.split(/[/-]/);
            if ( $.msiJqlib.isNullEx2(p[2]) ) {
                return null;
            }
            var val = ('00'+p[2]).slice(-2);
            return val;
        },

        validation: {
            // ka_k_w_birth_date: {
            //     required: false,
            //     fn: Backbone.Validation.msi_v_fn.clWarekiYmd('ka_k_gengo'),
            // },
            // ha_k_w_birth_date: {
            //     required: false,
            //     fn: Backbone.Validation.msi_v_fn.clWarekiYmd('ha_k_gengo'),
            // },
            ka_k_w_birth_date: {
                required: false,
                fn: function(value, attr, computedState) {
                    var _f = Backbone.Validation.msi_v_fn.clWarekiYmd('ka_k_gengo');
                    var eMsg = _f.apply(this, [value, attr, computedState]);
                    if ( eMsg ) {
                        this.trigger('decoErrInfo', ['.ka_k_birth_gengoyear', '.ka_k_birth_month', '.ka_k_birth_day'].join(','), eMsg);
                        return eMsg;
                    }
                }
            },
            ha_k_w_birth_date: {
                required: false,
                fn: function(value, attr, computedState) {
                    var _f = Backbone.Validation.msi_v_fn.clWarekiYmd('ha_k_gengo');
                    var eMsg = _f.apply(this, [value, attr, computedState]);
                    if ( eMsg ) {
                        this.trigger('decoErrInfo', ['.ha_k_birth_gengoyear', '.ha_k_birth_month', '.ha_k_birth_day'].join(','), eMsg);
                        return eMsg;
                    }
                }
            },
            depature_kbn: {
                required: true,
                msg: '出発地は必須です',
            },
            depature_syako_opt: {
                required: function() { return this.get('depature_kbn') == 1; }, // 1:営業所(車庫)
                msg: '出発地は必須です',
            },
            depature_sonota: {
                required: function() { return this.get('depature_kbn') == 2; }, // 2:その他
                msg: '出発地は必須です',
            },
            arrival_kbn: {
                required: function() { return !$.msiJqlib.isNullEx2(this.get('hanso_result')); },
                msg: '到着地は必須です',
            },
            arrival_syako_opt: {
                required: function() { return !$.msiJqlib.isNullEx2(this.get('hanso_result')) &&
                                       this.get('arrival_kbn') == 1; }, // 1:営業所(車庫)
                msg: '到着地は必須です',
            },
            arrival_sonota: {
                required: function() { return !$.msiJqlib.isNullEx2(this.get('hanso_result')) &&
                                       ( gMySubViewApp && gMySubViewApp.$('.arrival_sonota').is(':visible') ) &&
                                       this.get('arrival_kbn') == 2; }, // 2:その他
                msg: '到着地は必須です',
            },
            _all: function(val, attr, computed) {
                var hanso_result   = this.get('hanso_result'),
                    depature_meter = this.get('depature_meter'),
                    dpt_ymd        = this.get('dpt_ymd'),
                    dpt_time       = this.get('dpt_time'),
                    arrival_nm     = this.get('arrival_nm'),
                    arrival_meter  = this.get('arrival_meter'),
                    arr_ymd        = this.get('arr_ymd'),
                    arr_time       = this.get('arr_time'),
                    aTgt = [],
                    aMsg = [],
                    msg,
                    _date1, _date2,
                    that = this;

                if ( !$.msiJqlib.isNullEx2(hanso_result) ) {
                    if ( $.msiJqlib.isNullEx2(arrival_nm) ) {
                        aTgt.push( '.arrival_nm' );
                    }
                    if ( $.msiJqlib.isNullEx2(arrival_meter) ) {
                        aTgt.push( '.arrival_meter' );
                    }
                    if ( $.msiJqlib.isNullEx2(arr_ymd) ) {
                        aTgt.push( '.arr_ymd' );
                    }
                    if ( $.msiJqlib.isNullEx2(arr_time) ) {
                        aTgt.push( '.arr_time' );
                    }
                    if ( aTgt.length > 0 ) {
                        msg = '「搬送結果」入力時は到着時情報も必須です';
                        _.defer( function() { that.trigger('decoErrInfo', aTgt.join(','), msg); } ); // 個別チェックと競合するので
                        return msg;
                    }
                }

                if ( !$.msiJqlib.isNullEx2(dpt_ymd) ) {
                    _date1 = $.msiJqlib.normYmd(dpt_ymd);
                    _date2 = $.msiJqlib.dateIntervalYmd(1); // 明日
                    if ( _date1 > _date2 ) {
                        aMsg.push('「出発日時」が未来日です');
                        aTgt.push( '.dpt_ymd' );
                    }
                }
                if ( !$.msiJqlib.isNullEx2(arr_ymd) ) {
                    _date1 = $.msiJqlib.normYmd(arr_ymd);
                    _date2 = $.msiJqlib.dateIntervalYmd(1); // 明日
                    if ( _date1 > _date2 ) {
                        aMsg.push('「到着日時」が未来日です');
                        aTgt.push( '.arr_ymd' );
                    }
                }

                if ( !$.msiJqlib.isNullEx2(depature_meter) && !$.msiJqlib.isNullEx2(arrival_meter) ) {
                    if ( +depature_meter > +arrival_meter ) {
                        aMsg.push('「出発時メーター」値が「到着時メーター」より大きいです');
                        aTgt.push(['.depature_meter', '.arrival_meter']);
                    }
                }

                if ( !$.msiJqlib.isNullEx2(dpt_ymd) && !$.msiJqlib.isNullEx2(dpt_time) 
                     && !$.msiJqlib.isNullEx2(arr_ymd) && !$.msiJqlib.isNullEx2(arr_time) ) {
                    dpt_time = ('0'+dpt_time).slice(-5); // hh:mm
                    arr_time = ('0'+arr_time).slice(-5); // hh:mm
                    var _dtp = $.msiJqlib.normYmd(dpt_ymd) + dpt_time,
                        _arr = $.msiJqlib.normYmd(arr_ymd) + arr_time;
                    if ( _dtp > _arr ) {
                        aMsg.push('「出発日時」が「到着日時」より大きいです');
                        aTgt.push(['.dpt_ymd', '.dpt_time', '.arr_ymd', '.arr_time']);
                    }
                }

                if ( aMsg.length ) {
                    msg = aMsg.join(', ');
                    _.defer( function() { that.trigger('decoErrInfo', aTgt.join(','), msg); } );
                    return msg;
                }
            },
            // _all: function(val, attr, computed) {},
        },
    } ); // _JiSubModelBase
    // console.log( '_JiSubModelBase=>', _JiSubModelBase );
    var JiSubModel = gMyApp.JiSubModel = Backbone.Model.extend( _JiSubModelBase );
    _.defaults( JiSubModel.prototype, mixin.ModelDataChangedMixin ); // データ変更確認機能付与
    
    var _JiSubViewBase = utils.genViewBase( _itemMapper,
    {
        tagName: 'div',
        className: 'jiseki-main-view-div',
        events: {
            "click .dlg_date00, .dlg_time00": "setDatePickerFocus",
            "click .dlg_syutsudo_saki": "syutsudoSakiHelper",
            "click .dlg_depature_zip_cd": "zip1Helper",
            "click .dlg_arrival_zip_cd": "zip2Helper",
            "click .dlg_zip_ka_k": "zip3Helper",
            "click .dlg_zip_ka_anti": "zip4Helper",
            "click .dlg_zip_ha_k": "zip5Helper",
            "click .dlg_zip_ha_anti": "zip6Helper",
            // "click .dlg_depature": "depatureHelper",
            // "click .dlg_arrival": "arrivalHelper",
            "select2-selecting .hanso_result": "hansoResultSelecting",
            "click .btn-dpt": "clickBtnDpt",
            "click .btn-arr": "clickBtnArr",
            "click .jiseki_result_tab li": "clickHansoResultTab",
            "click .dlg_pass_kaisya": "passKaisyaHelper",
            "click .dlg_ka_anti_saki": "kaIsoSakiShisetsuHelper",
            "click .dlg_ha_anti_saki": "haIsoSakiShisetsuHelper",
            "click .dlg_ka_kojin_cif_no": "kaCifHelper",
            "click .dlg_ha_kojin_cif_no": "haCifHelper",
            "select2-selecting .ka_iso_saki_kbn": "sel2KaIsoSakiKbnChg",
            "select2-clearing  .ka_iso_saki_kbn": "sel2KaIsoSakiKbnChg",
            "select2-selecting .ha_iso_saki_kbn": "sel2HaIsoSakiKbnChg",
            "select2-clearing  .ha_iso_saki_kbn": "sel2HaIsoSakiKbnChg",
            "select2-selecting .depature_kbn": "sel2DepatureKbn",
            "select2-clearing  .depature_kbn": "sel2DepatureKbn",
            "select2-selecting .arrival_kbn": "sel2ArrivalKbn",
            "select2-clearing  .arrival_kbn": "sel2ArrivalKbn",
        },
        initialize: function() {
            var that = this;
            gMySubViewApp = this;

            Backbone.Validation.bind( this, Backbone.Validation.msi_v_iv_callback2({
                // birthday_date: '.birthday_date,.birthday_era',
            }, undefined, '.') );

            this.listenTo(this.model, 'decoErrInfo', this.decoErrInfo);

            this.listenTo(this.model, 'change', this.render);
            this.listenTo(this.model, 'remove', this.remove);

            this.listenTo(this.model, 'change:hanso_result', this.hansoResultChange);

            this.listenTo(this.model, 'change:depature_sonota', this.chgDepatureSonota);
            this.listenTo(this.model, 'change:arrival_sonota', this.chgArrivalSonota);

            var _appView = getAppView();
            this.listenTo(_appView, 'msiEvent:btn_save', this.savePrc);
            this.listenTo(_appView, 'msiEvent:btn_del', this.delPrc);
            this.listenTo(_appView, 'msiEvent:btn_seisan', this.seisanPrc);
            this.listenTo(_appView, 'msiEvent:btn_sijisho', this.sijishoPrc);

            this.listenTo(this.model, 'setInitData', this.renderInit);
        },

        renderInit: function() {
            var that = this;

            var _tmpl = gMyApp.svTmpl;
            var html = _.template( _tmpl, this.model.toJSON() );
            this.$el.html( html );

            this.stickit();
            
            var _mstr = mergedMstrData; // getMstr();

            utils.setSelect2CdNm( this.$el, _mstr, [ '.depature_kbn',
                                                     '.arrival_kbn',
                                                     ['.hanso_result', 'my_hanso_result'],
                                                     ['.pass_kaiin_kbn', 'arunasi_kbn'],
                                                     // ['.ka_k_gengo', 'gengo'],
                                                     ['.ka_m_zoku_kbn2', 'zoku_kbn'],
                                                     '.ka_iso_saki_kbn',
                                                     // ['.ha_k_gengo', 'gengo'],
                                                     ['.ha_m_zoku_kbn2', 'zoku_kbn'],
                                                     '.ha_iso_saki_kbn',
                                                   ],
                                  {placeholder: ' ', dropdownAutoWidth:true} );

            // 故人生年月日(仮安置)
            $.msiJqlib.setSelect2Com1( this.$(".ka_k_birth_gengoyear"),
                                       ($.extend({data: _mstr.gengo_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            // minimumResultsForSearch: -1:表示しない, 0:常に表示
            $.msiJqlib.setSelect2Com1( this.$(".ka_k_birth_month"),
                                       ($.extend({data: _mstr.month_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            $.msiJqlib.setSelect2Com1( this.$(".ka_k_birth_day"),
                                       ($.extend({data: _mstr.day_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            var ka_k_gengo = this.model.get('ka_k_gengo');
            var ka_k_w_birth_date = this.model.get('ka_k_w_birth_date');
            this.model.set_ka_k_birth(ka_k_gengo, ka_k_w_birth_date);

            // 故人生年月日(安置)
            $.msiJqlib.setSelect2Com1( this.$(".ha_k_birth_gengoyear"),
                                       ($.extend({data: _mstr.gengo_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            // minimumResultsForSearch: -1:表示しない, 0:常に表示
            $.msiJqlib.setSelect2Com1( this.$(".ha_k_birth_month"),
                                       ($.extend({data: _mstr.month_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            $.msiJqlib.setSelect2Com1( this.$(".ha_k_birth_day"),
                                       ($.extend({data: _mstr.day_sel2}, $.msiJqlib.setSelect2Default1,
                                                 {placeholder: ' ', minimumResultsForSearch:0, dropdownAutoWidth:true})) );
            var ha_k_gengo = this.model.get('ha_k_gengo');
            var ha_k_w_birth_date = this.model.get('ha_k_w_birth_date');
            this.model.set_ha_k_birth(ha_k_gengo, ha_k_w_birth_date);

            // 営業所(車庫)
            $.msiJqlib.setSelect2Com1( this.$(".depature_syako_opt"),
                                       ($.extend({data: _mstr.syako_opt},
                                                 $.msiJqlib.setSelect2Default1, {placeholder: ' ', dropdownAutoWidth:true})) );

            $.msiJqlib.setSelect2Com1( this.$(".arrival_syako_opt"),
                                       ($.extend({data: _mstr.syako_opt},
                                                 $.msiJqlib.setSelect2Default1, {placeholder: ' ', dropdownAutoWidth:true})) );


            // 郵便番号による住所1自動入力設定
            $.msiJqlib.setAutoZipToAddrModel( { '.depature_zip_cd':  ['depature_zip_cd',  'depature_addr1', this.model],
                                                '.arrival_zip_cd':   ['arrival_zip_cd',   'arrival_addr1',  this.model],
                                                '.ka_k_yubin_no':    ['ka_k_yubin_no',    'ka_k_addr1',     this.model],
                                                '.ka_anti_yubin_no': ['ka_anti_yubin_no', 'ka_anti_addr1',  this.model],
                                                '.ha_k_yubin_no':    ['ha_k_yubin_no',    'ha_k_addr1',     this.model],
                                                '.ha_anti_yubin_no': ['ha_anti_yubin_no', 'ha_anti_addr1',  this.model] }, this );

            // 出発地の調整
            var depature_kbn = this.model.get('depature_kbn');
            var depature_nm = this.model.get('depature_nm');
            if ( depature_kbn == 1 ) { // 1:営業所(車庫)
                if ( !$.msiJqlib.isNullEx2(depature_nm) ) {
                    var _data_syako_opt = _.find( _mstr.syako_opt, function(d) { return d.text == depature_nm; } );
                    if ( _data_syako_opt ) {
                        this.model.set('depature_syako_opt', _data_syako_opt.id);
                        // this.$('.depature_syako_opt').select2('val', _data_syako_opt.id);
                    }
                } else {
                }
            } else if ( depature_kbn == 2 ) { // 2:その他
                if ( !$.msiJqlib.isNullEx2(depature_nm) ) {
                    this.model.set('depature_sonota', depature_nm);
                } else {
                }
            }

            // 到着地の調整
            var arrival_kbn = this.model.get('arrival_kbn');
            var arrival_nm = this.model.get('arrival_nm');
            if ( arrival_kbn == 1 ) { // 1:営業所(車庫)
                if ( !$.msiJqlib.isNullEx2(arrival_nm) ) {
                    var _data_syako_opt = _.find( _mstr.syako_opt, function(d) { return d.text == arrival_nm; } );
                    if ( _data_syako_opt ) {
                        this.model.set('arrival_syako_opt', _data_syako_opt.id);
                        // this.$('.arrival_syako_opt').select2('val', _data_syako_opt.id);
                    }
                } else {
                }
            } else if ( arrival_kbn == 2 ) { // 2:その他
                if ( !$.msiJqlib.isNullEx2(arrival_nm) ) {
                    this.model.set('arrival_sonota', arrival_nm);
                } else {
                }
            }

            this.model.markAsOrg(); // arrival_addr1,arrival_zip_cd などが変更されたとみなされるのでここでマーク

            this.$(".dpt_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".dpt_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault, {stepMinute: gStepMinute}));
            this.$(".arr_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".arr_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault, {stepMinute: gStepMinute}));

            this.hansoResultChange();

            this.$('.ka_k_sex_kbn_radio_set').buttonset(); // patch
            this.$('.ha_k_sex_kbn_radio_set').buttonset(); // patch

            $.msiJqlib.setAutoKanaModelCol( {
                '.ka_k_l_nm' : [ '.ka_k_l_knm', 'ka_k_l_knm', this.model ],
                '.ka_k_f_nm' : [ '.ka_k_f_knm', 'ka_k_f_knm', this.model ],
                '.ha_k_l_nm' : [ '.ha_k_l_knm', 'ha_k_l_knm', this.model ],
                '.ha_k_f_nm' : [ '.ha_k_f_knm', 'ha_k_f_knm', this.model ],
            }, this );
 
            this.render();
        },

        decoErrInfo: function(pos, msg) {
            // console.log( 'decoErrInfo=>'+msg + ' pos=>' + pos, this);
            this.$(pos).msiErrDeco(msg);
        },

        // 搬送結果を固定化する場合その理由を示す文字列を返す
        _isHansoResultFixed: function() {
            var _last_hanso_result = this.model.get('_last_hanso_result');
            if ( _last_hanso_result == 2 ) { // 2(仮安置)
                return '「仮安置」で登録されています';
            }
            var unkou_kbn = this.model.get('unkou_kbn');
            var unkou_kbn_1_hanso_result = this.model.get('unkou_kbn_1_hanso_result');
            var unkou_kbn_2_hanso_result = this.model.get('unkou_kbn_2_hanso_result');
            var unkou_kbn_3_hanso_result = this.model.get('unkou_kbn_3_hanso_result');
            if ( (unkou_kbn == 1 && (!$.msiJqlib.isNullEx2(unkou_kbn_2_hanso_result) || !$.msiJqlib.isNullEx2(unkou_kbn_3_hanso_result))) ||
                 (unkou_kbn == 2 && (!$.msiJqlib.isNullEx2(unkou_kbn_3_hanso_result))) ) {
                return '後続の運行区分に搬送結果が登録されています';
            }

            return null;
        },

        render: function () {
            // console.log( '@@@ render() called');

            // this.trigger( 'syncYmd', 'wareki' );

            if ( this._isReadOnly() ) {
                this._readOnlySetting();
            }

            this.fAdj_buttons();
            this.fAdj_ka_iso_saki_etc();
            this.fAdj_ha_iso_saki_etc();
            this.fAdj_unkou_kbn_03();
            this.fAdj_depature_kbn();
            this.fAdj_arrival_kbn();

            var msgRsn = this._isHansoResultFixed();
            if ( msgRsn ) {
                this.$('.hanso_result').attr('readonly', 'readonly');
                // this.$('.hanso_result').attr('title', msgRsn);
            }

            return this;
        },

        fAdj_buttons: function() {
            var msi_no,
                msg,
                _app = getAppView();

            if ( this._isReadOnly() ) {
                _app.btnHide('#btn_save');
                _app.btnHide('#btn_del');
            } else {
                _app.btnShow('#btn_save');
                _app.btnShow('#btn_del');
            }
            
            msi_no = this.model.get('msi_no');
            if ( $.msiJqlib.isNullEx2(msi_no) ) {
                _app.btnHide('#btn_del');
            }

            var hanso_kbn = this.model.get('hanso_kbn');
            if ( hanso_kbn != 5 ) {
                _app.btnShow('#btn_sijisho');
            } else {
                _app.btnHide('#btn_sijisho');
            }

            var _last_hanso_result = this.model.get('_last_hanso_result');
            if ( _last_hanso_result == 5 || _last_hanso_result == 6 ) { // && !this._isReadOnly() ) { // 5:搬送のみ,6:安置
                _app.btnShow('#btn_seisan');
            } else {
                _app.btnHide('#btn_seisan');
            }
        },

        // 運行区分 帰庫(3) の特別処理
        fAdj_unkou_kbn_03: function() {
            var unkou_kbn = this.model.get('unkou_kbn'),
                hanso_result = this.model.get('hanso_result'),
                $tgt;
            if ( unkou_kbn == 3 ) { // 運行区分 帰庫(3)
                $tgt = this.$('.jiseki_result_tab_cont_div');

                if ( true ) { // hanso_result == 8 ) { // 搬送結果 帰庫(8)
                    this._readOnlySetting($tgt);

                    this.$('.ka_k_sex_kbn_radio_set').buttonset(); // patch
                    this.$('.ha_k_sex_kbn_radio_set').buttonset(); // patch

                    // タブをオレンジでなくグレーに
                    var $tabSel = this.$('.jiseki_result_tab_container .jiseki_result_tab li.selected');
                    $tabSel.addClass('color_unchange');

                    // 安置か仮安置でなければ 表示しない  // cf. hansoResultChange()
                    var unkou_kbn_2_hanso_result = this.model.get('unkou_kbn_2_hanso_result'); // 運行区分搬送(2)の搬送結果
                    // console.log('**=>', this.model.toJSON() );
                    // console.log('unkou_kbn_2_hanso_result=>' + unkou_kbn_2_hanso_result );
                    if ( unkou_kbn_2_hanso_result != 2 && unkou_kbn_2_hanso_result != 6 ) { // 2(仮安置), 6(安置)
                        this.$('.jiseki_result_tab_container').hide();
                        this.$('.jiseki_result_tab_cont_div').hide();
                    }
                } else {
                    this._readOnlySettingClear($tgt);
                }
            }
        },

        fAdj_ka_iso_saki_etc: function() {
            var ka_iso_saki_kbn = this.model.get('ka_iso_saki_kbn');
            // var $tgt = this.$('.iso_saki_shisetsu_nm, .iso_saki_yubin_no, .iso_saki_addr1, .iso_saki_addr2, .iso_saki_tel');
            var $tgt = this.$('.ka_anti_sisetu_nm');
            var $par = this.$('.ka_iso_saki_div');
            // var $tgt = this.$('.iso_saki_shisetsu_nm');
            if ( +ka_iso_saki_kbn === 1 ) { // 移送先区分: 1:当社施設
                $tgt.attr('readonly', 'readonly');
                $par.addClass('readonly_iso_saki');
            } else {
                $tgt.removeAttr('readonly');
                $par.removeClass('readonly_iso_saki');
            }
        },

        fAdj_ha_iso_saki_etc: function() {
            var ha_iso_saki_kbn = this.model.get('ha_iso_saki_kbn');
            // var $tgt = this.$('.iso_saki_shisetsu_nm, .iso_saki_yubin_no, .iso_saki_addr1, .iso_saki_addr2, .iso_saki_tel');
            var $tgt = this.$('.ha_anti_sisetu_nm');
            var $par = this.$('.ha_iso_saki_div');
            // var $tgt = this.$('.iso_saki_shisetsu_nm');
            if ( +ha_iso_saki_kbn === 1 ) { // 移送先区分: 1:当社施設
                $tgt.attr('readonly', 'readonly');
                $par.addClass('readonly_iso_saki');
            } else {
                $tgt.removeAttr('readonly');
                $par.removeClass('readonly_iso_saki');
            }
        },

        // 出発地区分
        fAdj_depature_kbn: function() {
            if ( this._isReadOnly() ) { return; }
            var depature_kbn = this.model.get('depature_kbn');
            if ( depature_kbn == 1 ) { // 営業所(車庫)(1)
                this.$('.depature_syako_opt').removeAttr('readonly');
                this.$('.depature_sonota').attr('readonly', 'readonly');
            } else if ( depature_kbn == 2 ) { // その他(2)
                this.$('.depature_syako_opt').attr('readonly', 'readonly');
                this.$('.depature_sonota').removeAttr('readonly');
            } else { // 未指定
                this.$('.depature_syako_opt').attr('readonly', 'readonly');
                this.$('.depature_sonota').attr('readonly', 'readonly');
            }
        },

        // 到着地区分
        fAdj_arrival_kbn: function() {
            if ( this._isReadOnly() ) { return; }
            var arrival_kbn = this.model.get('arrival_kbn');
            if ( arrival_kbn == 1 ) { // 営業所(車庫)(1)
                this.$('.arrival_syako_opt').removeAttr('readonly');
                this.$('.arrival_sonota').attr('readonly', 'readonly');
            } else if ( arrival_kbn == 2 ) { // その他(2)
                this.$('.arrival_syako_opt').attr('readonly', 'readonly');
                this.$('.arrival_sonota').removeAttr('readonly');
            } else { // 未指定
                this.$('.arrival_syako_opt').attr('readonly', 'readonly');
                this.$('.arrival_sonota').attr('readonly', 'readonly');
            }
        },

        // 仮安置先移送先区分
        sel2KaIsoSakiKbnChg: function(e) {
            var cur_ka_iso_saki_kbn = this.model.get('ka_iso_saki_kbn');
            var new_ka_iso_saki_kbn = e.val;
            if ( cur_ka_iso_saki_kbn != new_ka_iso_saki_kbn ) {
                var m = this.model;
                var newVal = { ka_anti_sisetu_nm:   null,
                               ka_anti_basho_nm:    null,
                               ka_anti_saki_nm:     null,
                               ka_anti_yubin_no:    null,
                               ka_anti_addr1:       null,
                               ka_anti_addr2:       null,
                               ka_anti_tel:         null };
                m.set( newVal );
            }
        },

        // 安置先移送先区分
        sel2HaIsoSakiKbnChg: function(e) {
            var cur_ha_iso_saki_kbn = this.model.get('ha_iso_saki_kbn');
            var new_ha_iso_saki_kbn = e.val;
            if ( cur_ha_iso_saki_kbn != new_ha_iso_saki_kbn ) {
                var m = this.model;
                var newVal = { ha_anti_sisetu_nm:   null,
                               ha_anti_basho_nm:    null,
                               ha_anti_saki_nm:     null,
                               ha_anti_yubin_no:    null,
                               ha_anti_addr1:       null,
                               ha_anti_addr2:       null,
                               ha_anti_tel:         null };
                m.set( newVal );
            }
        },

        hansoResultChange: function() {
            var hanso_result = this.model.get('hanso_result');

            // console.log('hansoResultChange =>' + hanso_result);

            // 全タブコンテンツを一旦非表示
            this.$('.jiseki_result_tab_cont_div .jiseki_result_tab_ele').hide();

            // 全タブを一旦クリア
            var $tabAll = this.$('.jiseki_result_tab_container .jiseki_result_tab li');
            $tabAll.removeClass('selected');

            // タブのうち hanso_result の選択肢にないものは要素削除する
            var opts = _.pluck( mergedMstrData['my_hanso_result'], 'kbn_value_cd_num' );
            $tabAll.each( function() {
                var $liEle = $(this);
                var hanso_result = $liEle.attr('data-hanso-result');
                if ( opts.indexOf(hanso_result) < 0 ) {
                    $liEle.remove();
                }
            } );

            // タブコンテンツを選択
            this.$('.jiseki_result_tab_cont_div .jiseki_result_tab_ele[data-hanso-result="'
                   + hanso_result + '"]').show();

            // タブの表示
            if ( true ) {
                // 選択された jiseki_result 以外のタブは非表示
                $tabAll.hide();
                var $tabSel = this.$('.jiseki_result_tab_container .jiseki_result_tab li[data-hanso-result="' + hanso_result + '"]');
                if ( $tabSel.length ) {
                    $tabSel.show();
                    $tabSel.addClass('selected');
                }
            } else {
                var $tabSel = this.$('.jiseki_result_tab_container .jiseki_result_tab li[data-hanso-result="' + hanso_result + '"]');
                if ( $tabSel.length ) {
                    // タブを選択
                    $tabAll.show();
                    $tabSel.addClass('selected');
                } else {
                    if ( $.msiJqlib.isNullEx2(hanso_result) ) {
                        $tabAll.hide(); // 選択されていないときはタブ表示しない場合
                        // $tabAll.show(); // 選択されていないときはタブ表示する場合
                    } else {
                        $tabAll.hide();
                    }
                }
            }
        },

        chgDepatureSonota: function() {
            // console.log('chgDepatureSonota');
            var m = this.model;
            var depature_kbn = m.get('depature_kbn');
            var depature_sonota = m.get('depature_sonota');
            if ( depature_kbn == 2 ) { // 2(その他)
                m.set('depature_nm', depature_sonota);
            }
        },

        chgArrivalSonota: function() {
            // console.log('chgDepatureSonota');
            var m = this.model;
            var arrival_kbn = m.get('arrival_kbn');
            var arrival_sonota = m.get('arrival_sonota');
            if ( arrival_kbn == 2 ) { // 2(その他)
                m.set('arrival_nm', arrival_sonota);
            }
        },

        clickHansoResultTab: function(e) {
            var hanso_result = $(e.currentTarget).attr('data-hanso-result');
            if ( hanso_result ) {
                this.model.set('hanso_result', hanso_result);
            }
        },

        setDatePickerFocus: function(e) {
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない

            // 横アイコンクリック時に真上のinput要素を探す
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            $tgt.datepicker("show");
        },

        // 郵便番号 picker
        zip1Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip1Helper.apply(this);
        },
        zip2Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip2Helper.apply(this);
        },
        zip3Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip3Helper.apply(this);
        },
        zip4Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip4Helper.apply(this);
        },
        zip5Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip5Helper.apply(this);
        },
        zip6Helper: function(e) {
            var $tgt = $(e.currentTarget).prev("input");
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._zip6Helper.apply(this);
        },
        _zip1Helper: viewUtils.zipHelper2( 'depature_zip_cd', 'depature_addr1' ),
        _zip2Helper: viewUtils.zipHelper2( 'arrival_zip_cd', 'arrival_addr1' ),
        _zip3Helper: viewUtils.zipHelper2( 'ka_k_yubin_no', 'ka_k_addr1' ),
        _zip4Helper: viewUtils.zipHelper2( 'ka_anti_yubin_no', 'ka_anti_addr1' ),
        _zip5Helper: viewUtils.zipHelper2( 'ha_k_yubin_no', 'ha_k_addr1' ),
        _zip6Helper: viewUtils.zipHelper2( 'ha_anti_yubin_no', 'ha_anti_addr1' ),

        // // nm_jyusyo_mst.jyusho_kbn に対応する 移送先区分(code_nm_mst: 7738) を返す
        // _jyusho_kbn_to_cd7738: function(jyusho_kbn) {
        //     jyusho_kbn = +jyusho_kbn;
        //     var cd7738;
        //     switch ( jyusho_kbn ) {
        //     case 2: cd7738 = 1; break; // 当社施設
        //     default: cd7738 = null; }  // 搬送先
        //     return cd7738;
        // },

        // // 移送先区分(code_nm_mst: 7738)に対応する nm_jyusyo_mst.jyusho_kbn を返す
        // _cd7738_to_jyusho_kbn: function(cd7738) {
        //     cd7738 = +cd7738;
        //     var jyusho_kbn;
        //     switch ( cd7738 ) {
        //     case 1: jyusho_kbn = 2; break; // 当社施設
        //     default: jyusho_kbn = null; }  // 搬送先 // 9999
        //     return jyusho_kbn;
        // },

        // 移送先施設名 pickup
        // cf. hanso.iraiMsi.js isoSakiShisetsuHelper()
        kaIsoSakiShisetsuHelper: function(e) {
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない
            if ( this.$('.ka_iso_saki_div').hasClass('disable') ) return;

            var m = this.model;
            var that = this;

            var iso_saki_kbn = m.get('ka_iso_saki_kbn');
            // if ( iso_saki_kbn != 1 ) return; // 1:当社施設 以外不可
            // var type = this._cd7738_to_jyusho_kbn(iso_saki_kbn); // 1:当社施設

            var seko_kaisya_cd = getAppView().model.get('seko_kaisya_cd');

            this.$el.msiPickHelper({
                action: '/hanso/dlganchi', 
                onSelect: function(data) {
                    // console.log( 'kaIsoSakiShisetsuHelper', data );
                    var newVal = { ka_anti_sisetu_nm: data.name,
                                   ka_anti_basho_nm: null, // data.name,
                                   // 移送先区分「当社施設」の時、施設予約[安置先]の予約した場所を表示
                                   ka_anti_saki_nm: data.name,
                                   ka_anti_yubin_no:    data.zip_no,
                                   ka_anti_addr1:       data.addr1_nm,
                                   ka_anti_addr2:       data.addr2_nm,
                                   ka_anti_tel:         data.tel };
                    newVal.ka_iso_saki_kbn = 1; // 当社施設
                    // var cd7738 = that._jyusho_kbn_to_cd7738(data.jyusho_kbn);
                    // if ( cd7738 != null ) {
                    //     newVal.ka_iso_saki_kbn = cd7738;
                    // }
                    m.set( newVal );
                },
                onClear: function(rtnData) {
                    // console.log( 'syutsudoSakiHelper onClear' );
                    var newVal = { ka_anti_sisetu_nm:   null,
                                   ka_anti_basho_nm:    null,
                                   ka_anti_saki_nm:     null,
                                   ka_anti_yubin_no:    null,
                                   ka_anti_addr1:       null,
                                   ka_anti_addr2:       null,
                                   ka_anti_tel:         null };
                    m.set( newVal );
                },
                hookSetData: function() {
                    return {
                        no_cond_exp: true,
                        init_search: 0,
                        limit: 50,
                        s_seko_kaisya: seko_kaisya_cd,
                        // s_order_by_1: 'kaijyo_cd:asc',
                    }
                },
            });
            return;
        },

        // 移送先施設名 pickup
        // cf. hanso.iraiMsi.js isoSakiShisetsuHelper()
        haIsoSakiShisetsuHelper: function(e) {
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない
            if ( this.$('.ha_iso_saki_div').hasClass('disable') ) return;

            var m = this.model;
            var that = this;

            var iso_saki_kbn = m.get('ha_iso_saki_kbn');
            // if ( iso_saki_kbn != 1 ) return; // 1:当社施設 以外不可
            // var type = this._cd7738_to_jyusho_kbn(iso_saki_kbn); // 1:当社施設

            var seko_kaisya_cd = getAppView().model.get('seko_kaisya_cd');

            this.$el.msiPickHelper({
                action: '/hanso/dlganchi', 
                onSelect: function(data) {
                    // console.log( 'haIsoSakiShisetsuHelper', data );
                    var newVal = { ha_anti_sisetu_nm: data.name,
                                   ha_anti_basho_nm: null, // data.name,
                                   // 移送先区分「当社施設」の時、施設予約[安置先]の予約した場所を表示
                                   ha_anti_saki_nm: data.name,
                                   ha_anti_yubin_no:    data.zip_no,
                                   ha_anti_addr1:       data.addr1_nm,
                                   ha_anti_addr2:       data.addr2_nm,
                                   ha_anti_tel:         data.tel };
                    newVal.ha_iso_saki_kbn = 1; // 当社施設
                    // var cd7738 = that._jyusho_kbn_to_cd7738(data.jyusho_kbn);
                    // if ( cd7738 != null ) {
                    //     newVal.ha_iso_saki_kbn = cd7738;
                    // }
                    m.set( newVal );
                },
                onClear: function(rtnData) {
                    // console.log( 'syutsudoSakiHelper onClear' );
                    var newVal = { ha_anti_sisetu_nm:   null,
                                   ha_anti_basho_nm:    null,
                                   ha_anti_saki_nm:     null,
                                   ha_anti_yubin_no:    null,
                                   ha_anti_addr1:       null,
                                   ha_anti_addr2:       null,
                                   ha_anti_tel:         null };
                    m.set( newVal );
                },
                hookSetData: function() {
                    return {
                        no_cond_exp: true,
                        init_search: 0,
                        limit: 50,
                        s_seko_kaisya: seko_kaisya_cd,
                        // s_order_by_1: 'kaijyo_cd:asc',
                    }
                },
            });
            return;
        },

        // 引渡し会社
        passKaisyaHelper: function(e) {
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない

            // ひとまず nmjyusyo から
            var m = this.model;
            var that = this;

            var type = 2;
            var $ele = this.$el;
            msiLib2.setPickHelper2( type, 'nmjyusho', $ele,
                                    function(data) { // onSelect
                                        // console.log( 'passKaisyaHelper onSelect data=>', data );
                                        var newVal = { pass_kaisya_nm:     data.name };
                                        m.set( newVal );
                                    },
                                    function() { // onClear
                                        var newVal = { pass_kaisya_nm:     null };
                                        m.set( newVal );
                                    },
                                    function() { // hookSetData   関数とする
                                        var hookSetData = {
                                            init_search: 1,
                                            limit: 50,
                                        };
                                        return hookSetData;
                                    } );
        },

        // 仮安置先 CIF 検索
        kaCifHelper: function(e) {
            // console.log('CIF 検索');
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない

            var that = this;

            // Backbone.trigger('msi.hakuexif.cifdlg.open', this.$el);
            msiGlobalObj.hakuexifCifdlgOpen( this, 
                                             ['故人'],
                                             { cif_no_p1: this.model.get('ka_k_cif_no'),
                                              },
                                             this._kaCifHelperOnClose );
        },
        // cifdlgData: cifdlg のモデルデータ
        _kaCifHelperOnClose: function( cifdlgData ) {
            // console.log( '_kaCifHelperOnClose cifdlgData=>', cifdlgData );

            // if ( cifdlgData.cif_no_p1_org != cifdlgData.cif_no_p1 ) { // 変更されていれば
            if ( cifdlgData.cif_upd_p1 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(cifdlgData.cif_no_p1) ) {
                    this.model.set('ka_k_cif_no', cifdlgData.cif_no_p1);
                    // this.model.set('kojin_cif_no_result', 2); // 該当あり(2)
                    if ( cifdlgData.cif_data_p1 ) {
                        this.model.set('ka_k_l_nm', cifdlgData.cif_data_p1.fam_cust_nm);
                        this.model.set('ka_k_l_knm', cifdlgData.cif_data_p1.fam_cust_knm);
                        this.model.set('ka_k_f_nm', cifdlgData.cif_data_p1.giv_cust_nm);
                        this.model.set('ka_k_f_knm', cifdlgData.cif_data_p1.giv_cust_knm);
                        this.model.set('ka_k_sex_kbn', cifdlgData.cif_data_p1.sex_kbn);
                        this.model.set('ka_k_yubin_no', cifdlgData.cif_data_p1.yubin_no);
                        this.model.set('ka_k_addr1', cifdlgData.cif_data_p1.addr1);
                        this.model.set('ka_k_addr2', cifdlgData.cif_data_p1.addr2);
                        this.model.set('ka_k_tel', cifdlgData.cif_data_p1.tel_no);
                        var ka_k_gengo = cifdlgData.cif_data_p1.birth_wareki_gengo;
                        var ka_k_w_birth_date = cifdlgData.cif_data_p1.birth_wareki_ymd;
                        this.model.set_ka_k_birth(ka_k_gengo, ka_k_w_birth_date);
                        // this.model.set('ka_k_gengo', cifdlgData.cif_data_p1.birth_wareki_gengo);
                        // this.model.set('ka_k_w_birth_date', cifdlgData.cif_data_p1.birth_wareki_ymd);
                    }
                } else {
                    this.model.set('ka_k_cif_no', cifdlgData.cif_no_p1);
                    // this.model.set('kojin_cif_no_result', cifdlgData.cif_result_p1);

                    this.model.set('ka_k_l_nm', null);
                    this.model.set('ka_k_l_knm', null);
                    this.model.set('ka_k_f_nm', null);
                    this.model.set('ka_k_f_knm', null);
                    this.model.set('ka_k_sex_kbn', 0); // null);
                    this.model.set('ka_k_yubin_no', null);
                    this.model.set('ka_k_addr1', null);
                    this.model.set('ka_k_addr2', null);
                    this.model.set('ka_k_tel', null);
                    this.model.set('ka_k_birth_gengoyear', null);
                    this.model.set('ka_k_birth_month', null);
                    this.model.set('ka_k_birth_day', null);
                    // this.model.set('ka_k_gengo', null);
                    // this.model.set('ka_k_w_birth_date', null);
                }
            }
        },

        // 安置先 CIF 検索
        haCifHelper: function(e) {
            // console.log('CIF 検索');
            if ( this._isReadOnly() ) return; // 参照専用の場合は何もしない

            var that = this;

            // Backbone.trigger('msi.hakuexif.cifdlg.open', this.$el);
            msiGlobalObj.hakuexifCifdlgOpen( this, 
                                             ['故人'],
                                             { cif_no_p1: this.model.get('ha_k_cif_no'),
                                              },
                                             this._haCifHelperOnClose );
        },
        // cifdlgData: cifdlg のモデルデータ
        _haCifHelperOnClose: function( cifdlgData ) {
            // console.log( '_haCifHelperOnClose cifdlgData=>', cifdlgData );

            // if ( cifdlgData.cif_no_p1_org != cifdlgData.cif_no_p1 ) { // 変更されていれば
            if ( cifdlgData.cif_upd_p1 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(cifdlgData.cif_no_p1) ) {
                    this.model.set('ha_k_cif_no', cifdlgData.cif_no_p1);
                    // this.model.set('kojin_cif_no_result', 2); // 該当あり(2)
                    if ( cifdlgData.cif_data_p1 ) {
                        this.model.set('ha_k_l_nm', cifdlgData.cif_data_p1.fam_cust_nm);
                        this.model.set('ha_k_l_knm', cifdlgData.cif_data_p1.fam_cust_knm);
                        this.model.set('ha_k_f_nm', cifdlgData.cif_data_p1.giv_cust_nm);
                        this.model.set('ha_k_f_knm', cifdlgData.cif_data_p1.giv_cust_knm);
                        this.model.set('ha_k_sex_kbn', cifdlgData.cif_data_p1.sex_kbn);
                        this.model.set('ha_k_yubin_no', cifdlgData.cif_data_p1.yubin_no);
                        this.model.set('ha_k_addr1', cifdlgData.cif_data_p1.addr1);
                        this.model.set('ha_k_addr2', cifdlgData.cif_data_p1.addr2);
                        this.model.set('ha_k_tel', cifdlgData.cif_data_p1.tel_no);
                        var ha_k_gengo = cifdlgData.cif_data_p1.birth_wareki_gengo;
                        var ha_k_w_birth_date = cifdlgData.cif_data_p1.birth_wareki_ymd;
                        this.model.set_ha_k_birth(ha_k_gengo, ha_k_w_birth_date);
                        // this.model.set('ha_k_gengo', cifdlgData.cif_data_p1.birth_wareki_gengo);
                        // this.model.set('ha_k_w_birth_date', cifdlgData.cif_data_p1.birth_wareki_ymd);
                    }
                } else {
                    this.model.set('ha_k_cif_no', cifdlgData.cif_no_p1);
                    // this.model.set('kojin_cif_no_result', cifdlgData.cif_result_p1);

                    this.model.set('ha_k_l_nm', null);
                    this.model.set('ha_k_l_knm', null);
                    this.model.set('ha_k_f_nm', null);
                    this.model.set('ha_k_f_knm', null);
                    this.model.set('ha_k_sex_kbn', 0); // null);
                    this.model.set('ha_k_yubin_no', null);
                    this.model.set('ha_k_addr1', null);
                    this.model.set('ha_k_addr2', null);
                    this.model.set('ha_k_tel', null);
                    this.model.set('ha_k_birth_gengoyear', null);
                    this.model.set('ha_k_birth_month', null);
                    this.model.set('ha_k_birth_day', null);
                    // this.model.set('ha_k_gengo', null);
                    // this.model.set('ha_k_w_birth_date', null);
                }
            }
        },

        // 出発ボタン
        clickBtnDpt: function(e) {
            var $tgt = $(e.currentTarget);
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._setCurDatetime('dpt_ymd', 'dpt_time');
        },

        // 到着ボタン
        clickBtnArr: function(e) {
            var $tgt = $(e.currentTarget);
            if ( $tgt.is('[readonly]') || $tgt.prop('disabled') ) return; // 要素が readonly のときも何もしない
            this._setCurDatetime('arr_ymd', 'arr_time');
        },

        // 現在日時を設定
        _setCurDatetime: function(key_ymd, key_time) {
            var ts = $.msiJqlib.getStdDate(null, 'yyyy/MM/dd HH:mm').split(/( |:)/);
            var ymd  = ts[0];
            var hh   = ts[2];
            var min  = +ts[4];
            min = min - (min % gStepMinute);
            min = ('00'+min).slice(-2);
            var time = hh + ':' + min;
            this.model.set(key_ymd, ymd);
            this.model.set(key_time, time);
        },

        // 搬送結果選択
        hansoResultSelecting: function(e) {
            msiLib2.clearAlert();
            var iraisya_kbn = +getAppView().model.get('iraisya_kbn');
            var hanso_result = +e.val;
            if ( iraisya_kbn === 1 ) { // 依頼者区分   1(病院)
                if ( hanso_result === 6 ) { // 6(安置)
                    msiLib2.showWarn( '依頼者が病院の場合、搬送結果「安置」は設定できません' );
                    e.preventDefault();
                    $(e.currentTarget).select2('close');
                    return;
                }
            }

            var msgRsn = this._isHansoResultFixed();
            if ( msgRsn ) {
                msiLib2.showWarn( msgRsn );
                e.preventDefault();
                $(e.currentTarget).select2('close');
                return;
            }

            if ( hanso_result === 2 ) { // 2(仮安置)
                this._setDfltAntiInfoKa(); // 設定されていなければ初期設定
            } else if ( hanso_result === 6 ) { // 6(安置)
                this._setDfltAntiInfoHa(); // 設定されていなければ初期設定
            }
        },

        // 仮安置情報について設定されていなければ移送先からコピーして設定する
        _setDfltAntiInfoKa: function() {
            var m = this.model;
            var _appModel = getAppView().model;
            var iso_saki_kbn      = _appModel.get('iso_saki_kbn'); // 搬送依頼の移送先
            var ka_iso_saki_kbn   = m.get('ka_iso_saki_kbn');
            var ka_anti_sisetu_nm = m.get('ka_anti_sisetu_nm');
            if ( (ka_iso_saki_kbn && ka_anti_sisetu_nm) || !iso_saki_kbn ) {
                return;
            }
            m.set( { ka_iso_saki_kbn:   _appModel.get('iso_saki_kbn'),
                     ka_anti_sisetu_nm: _appModel.get('iso_saki_shisetsu_nm'),
                     ka_anti_basho_nm:  null,
                     ka_anti_saki_nm:   _appModel.get('iso_saki_nm'),
                     ka_anti_yubin_no:  _appModel.get('iso_saki_yubin_no'),
                     ka_anti_addr1:     _appModel.get('iso_saki_addr1'),
                     ka_anti_addr2:     _appModel.get('iso_saki_addr2'),
                     ka_anti_tel:       _appModel.get('iso_saki_tel'),
                   } );
        },

        // 安置情報について設定されていなければ移送先からコピーして設定する
        _setDfltAntiInfoHa: function() {
            var m = this.model;
            var _appModel = getAppView().model;
            var iso_saki_kbn      = _appModel.get('iso_saki_kbn'); // 搬送依頼の移送先
            var ha_iso_saki_kbn   = m.get('ha_iso_saki_kbn');
            var ha_anti_sisetu_nm = m.get('ha_anti_sisetu_nm');
            if ( (ha_iso_saki_kbn && ha_anti_sisetu_nm) || !iso_saki_kbn ) {
                return;
            }
            m.set( { ha_iso_saki_kbn:   _appModel.get('iso_saki_kbn'),
                     ha_anti_sisetu_nm: _appModel.get('iso_saki_shisetsu_nm'),
                     ha_anti_basho_nm:  null,
                     ha_anti_saki_nm:   _appModel.get('iso_saki_nm'),
                     ha_anti_yubin_no:  _appModel.get('iso_saki_yubin_no'),
                     ha_anti_addr1:     _appModel.get('iso_saki_addr1'),
                     ha_anti_addr2:     _appModel.get('iso_saki_addr2'),
                     ha_anti_tel:       _appModel.get('iso_saki_tel'),
                   } );
        },

        // 出発地
        sel2DepatureKbn: function(e) {
            var m = this.model;
            var cur_depature_kbn = m.get('depature_kbn');
            var new_depature_kbn = e.val;
            // console.log( '[cur_depature_kbn,new_depature_kbn]=>', [cur_depature_kbn,new_depature_kbn] );
            if ( cur_depature_kbn == new_depature_kbn ) {
                // console.log( '** not changed ');
                return; // 変更なし
            }
            if ( new_depature_kbn == 1 ) { // 1:営業所(車庫)
                m.set('depature_sonota', null);
            } else if ( new_depature_kbn == 2 ) { // 2:その他
                m.set('depature_syako_opt', null);
                this.$('.depature_syako_opt').select2('val', null);
            } else {
                m.set('depature_sonota', null);
                m.set('depature_syako_opt', null);
                this.$('.depature_syako_opt').select2('val', null);
            }
            m.set( { depature_nm:     null,
                     depature_zip_cd: null,
                     depature_addr1:  null,
                     // depature_addr2:  null,
                     // depature_tel:    null,
                   } );

            this.$('.depature_syako_opt, .depature_sonota').msiErrClear();
        },

        // 到着地
        sel2ArrivalKbn: function(e) {
            var m = this.model;
            var cur_arrival_kbn = m.get('arrival_kbn');
            var new_arrival_kbn = e.val;
            // console.log( '[cur_arrival_kbn,new_arrival_kbn]=>', [cur_arrival_kbn,new_arrival_kbn] );
            if ( cur_arrival_kbn == new_arrival_kbn ) {
                // console.log( '** not changed ');
                return; // 変更なし
            }
            if ( new_arrival_kbn == 1 ) { // 1:営業所(車庫)
                m.set('arrival_sonota', null);
            } else if ( new_arrival_kbn == 2 ) { // 2:その他
                m.set('arrival_syako_opt', null);
                this.$('.arrival_syako_opt').select2('val', null);
            } else {
                m.set('arrival_sonota', null);
                m.set('arrival_syako_opt', null);
                this.$('.arrival_syako_opt').select2('val', null);
            }
            m.set( { arrival_nm:     null,
                     arrival_zip_cd: null,
                     arrival_addr1:  null,
                     // arrival_addr2:  null,
                     // arrival_tel:    null,
                   } );

            this.$('.arrival_syako_opt, .arrival_sonota').msiErrClear();
        },

        _isReadOnly: function() {
            if ( utils.isReadOnlyCtxt() ) {
                return true;
            }

            // NG.  仮安置で次タブ(次の出動指示)が作成されてしまう場合がある.
            // var isLast = getAppView().isShowLastTab();
            // if ( ! isLast ) {
            //     return true;
            // }

            if ( this.model.get('_readonly_ctxt') ) {
                return true;
            }

            return false;
        },

        _readOnlySetting: function($tgt) {
            if ( $tgt === undefined ) $tgt = this.$el;
            $tgt.msiInputReadonly()
                .msiCalReadonly()
                .find('.my-readonly-hidden').hide();

            $tgt.find('.my-readonly-visible').show();

            $tgt.find('.dlg_pick_std:not(.no-bgc)').addClass('disable_etc');
            
            $tgt.find('.dlg_zip:not(.no-bgc)').addClass('disable_etc');

            $tgt.find('.lbl_zip').addClass('disable_etc');

            // $tgt.find('.syutsudo_item_ele').addClass('readonly-cls');

            this.$('.ka_k_sex_kbn_radio_set').buttonset(); // patch
            this.$('.ha_k_sex_kbn_radio_set').buttonset(); // patch
        },

        _readOnlySettingClear: function($tgt) {
            if ( $tgt === undefined ) $tgt = this.$el;
            $tgt.msiInputReadonlyUnset()
                .msiCalReadonlyUnset()
                .find('.my-readonly-hidden').show();

            $tgt.find('.my-readonly-visible').show();

            $tgt.find('.dlg_pick_std:not(.no-bgc)').removeClass('disable_etc');

            // $tgt.find('.syutsudo_item_ele').removeClass('readonly-cls');

            this.$('.ka_k_sex_kbn_radio_set').buttonset(); // patch
            this.$('.ha_k_sex_kbn_radio_set').buttonset(); // patch
        },

        // エラー表示をクリア
        clearErr: function() {
            this.$el.msiErrClearAll();
        },

        // _isErrInTab: function( tabIndex ) {
        _isErrInTab: function( hanso_result ) {
            if ( hanso_result === undefined ) {
                hanso_result = this.model.get('hanso_result');
            }
            var $tgt = this.$('.jiseki_result_tab_cont_div .jiseki_result_tab_ele[data-hanso-result="'
                              + hanso_result + '"]'),
                $errs = $tgt.find( '.my-error' );
            if ( $errs.length ) {
                return true;
            }
            return false;
        },

        // 入力チェック. エラー時にエラーが存在するタブにとぶ
        isInputOkWithGoErrTab: function() {
            var isOk = this.isInputOk(),
                tabIndex,
                that = this;
            if ( isOk ) {
                return true;
            }

            // 現在表示タブでエラーならそのまま
            if ( this._isErrInTab() ) {
                return;
            }

            // 現在表示タブでエラーがないなら、エラーが存在するタブを探し、表示する
            var $errEle = this.$('.jiseki_result_tab_cont_div .jiseki_result_tab_ele .my-error');
            if ( $errEle.length ) {
                var $tgt = $errEle.closest('.jiseki_result_tab_ele');
                if ( $tgt.length ) {
                    var err_hanso_result = $tgt.attr('data-hanso-result');
                    this.model.set('hanso_result', err_hanso_result);
                }
            }
        },

        // 入力チェック
        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line, that=this;
            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push( v );
                    }
                    // console.log( '*** err ' + k + ': ' + v );
                } );
            }

//            aMsg = aMsg.concat( this.model.meisaiCol.validateEx() );

            // NG
            if ( aMsg.length > 0 ) {
                // no alert component        msiLib2.showErr( aMsg.join(', ') );
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },

        // 初期状態から変更されているか
        isChanged: function() {
            return this.model.isDataChanged();
        },

        // doSave()
        savePrc: function(ev) {
            var bbv = this;

            // console.log( 'doSave called' );
            // console.log( JSON.stringify(this.model) );

            if ( ! this.isInputOkWithGoErrTab() ) {
                return;
            }

            var hanso_result = +this.model.get('hanso_result');

            if ( hanso_result === 2 ) { // 2:仮安置
                if ( ! confirm('搬送結果「仮安置」では出動依頼（再出動）が作成されます. よろしいですか？') ) {
                    return;
                }
            }

            // var forceFlg = false;
            // if ( !this.isChanged() ) { // doSave() で変更なし
            //     msiLib2.showInfo( 'データが変更されていません' );
            //     return;

            //     // or
            //     if ( ! confirm('データが変更されていませんが、更新しますか？') ) {
            //         return;
            //     } else {
            //         forceFlg = true;
            //     }
            // }

            var rtnData = {
                dataApp: this.model.genSendData(),
                // forceFlg:  forceFlg, 
            };

            // console.log( 'savePrc rtnData =>', rtnData );

            var dataJson = JSON.stringify( rtnData );

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hanso/jiseki/regjiseki',
                data: dataJson,
                type: 'POST',
                contentType: 'application/json',
                dataType: 'json',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        // console.log( 'regjiseki  done   mydata.data', mydata.data );

                        bbv.model.resetData( mydata.data.dataApp );

                        if ( mydata.data.newUnkouKbn ) { // 運行区分を refresh
                            setTimeout( function() { 
                                bbv.model._myPar.transNewUnkouKbn( mydata.data.newUnkouKbn );
                            }, 500 );
                        }

                        // 運行区分(出動=1)の搬送結果 更新
                        if ( mydata.data.dataApp && _.has(mydata.data.dataApp, 'unkou_kbn_1_hanso_result') ) {
                            bbv.model._myPar.set_unkou_kbn_1_hanso_result( mydata.data.dataApp.unkou_kbn_1_hanso_result );
                        }

                        getAppView().model.setJisekiExists(true);
                        msiLib2.showInfo( mydata.msg );
                    } else {
                        msiLib2.showErr2( mydata.msg );
                    }
                }
            });
        },

        // doDel()
        delPrc: function(ev) {
            var bbv = this;
            
            if ( ! confirm('削除してもよろしいですか？') ) {
                return;
            }

            this.clearErr();

            // console.log( 'doSave called' );
            // console.log( JSON.stringify(this.model) );

            // if ( ! this.isInputOkWithGoErrTab() ) {
            //     return;
            // }

            // var forceFlg = false;
            // if ( !this.isChanged() ) { // doSave() で変更なし
            //     msiLib2.showInfo( 'データが変更されていません' );
            //     return;

            //     // or
            //     if ( ! confirm('データが変更されていませんが、更新しますか？') ) {
            //         return;
            //     } else {
            //         forceFlg = true;
            //     }
            // }

            var rtnData = {
                dataApp: this.model.genSendData(),
                // forceFlg:  forceFlg, 
            };

            // console.log( 'delPrc =>', rtnData );

            var dataJson = JSON.stringify( rtnData );

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hanso/jiseki/deljiseki',
                data: dataJson,
                type: 'POST',
                contentType: 'application/json',
                dataType: 'json',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        // console.log( 'deljiseki  done   mydata.data', mydata.data );

                        bbv.model.resetData( mydata.data.dataApp );

                        if ( mydata.data.newUnkouKbn ) { // 運行区分を refresh
                            setTimeout( function() { 
                                bbv.model._myPar.transNewUnkouKbn( mydata.data.newUnkouKbn );
                            }, 500 );
                        }

                        getAppView().model.setJisekiExists(false);
                        msiLib2.showInfo( mydata.msg );
                    } else {
                        msiLib2.showErr2( mydata.msg );
                    }
                }
            });
        },

        // doSeisan()
        seisanPrc: function(ev) {
            var bbv = this;

            // console.log( 'seisanPrc() called' );

            var url = $.msiJqlib.baseUrl() + '/hanso/denpyo/index';
            var un = this.model.get('uketsuke_no');
            var uns = this.model.get('uketuke_no_sub');
            url = url + '/un/' + un + '.' + uns;

            var isNewWin = (ev.shiftKey || ev.ctrlKey || ev.metaKey) ? true : false;
            if ( isNewWin ) {
                msiLib2.openNewWinAbs( url );
            } else {
                getAppView().isNoChangeCheck_true();
                location.href = url;
            }
        },

        // doSijisho()
        sijishoPrc: function(ev) {
            var bbv = this;

            var uketsuke_no = this.model.get('uketsuke_no');
            var uketuke_no_sub = this.model.get('uketuke_no_sub');

            if ( $.msiJqlib.isNullEx2(uketsuke_no) || $.msiJqlib.isNullEx2(uketuke_no_sub) ) {
                msiLib2.showErr2( '受付番号が指定されていません' );
                return;
            }

            var _appModel = getAppView().model;
            var syaryo_disp = _appModel.get('syaryo_disp');
            var syaryo_cd = _appModel.get('syaryo_cd');
            if ( $.msiJqlib.isNullEx2(syaryo_disp) || $.msiJqlib.isNullEx2(syaryo_cd) ) {
                msiLib2.showErr2( '車両が指定されていません' );
                return;
            }

            var msg = '「出動指示書」を出力します. よろしいですか？';
            if ( ! confirm(msg) ) {
                return;
            }

            this.clearErr();

            var reqData = {
                dataApp: { uketsuke_no: uketsuke_no,
                           uketuke_no_sub: uketuke_no_sub,
                           syaryo_cd: syaryo_cd,
                           syaryo_disp: syaryo_disp },
            };

            // console.log( 'sijishoPrc =>', reqData );

            var dataJson = JSON.stringify( reqData );

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/hanso/sijisho/print',
                data: dataJson,
                type: 'POST',
                contentType: 'application/json',
                dataType: 'json',
            });
        },

        bindings: {
            'input[name="ka_k_sex_kbn"]': {
                observe: 'ka_k_sex_kbn',
                afterUpdate: function($el, val, options) {
                    // console.log( '**** @@@@@ input.ka_k_sex_kbn=>' + val + ' $el=>'+$el.attr('class') + ' id=>'+$el.attr('id'));
                    this.$('.ka_k_sex_kbn_radio_set').buttonset(); // patch for init
                },
            },
            'input[name="ha_k_sex_kbn"]': {
                observe: 'ha_k_sex_kbn',
                afterUpdate: function($el, val, options) {
                    // console.log( '**** @@@@@ input.ha_k_sex_kbn=>' + val + ' $el=>'+$el.attr('class') + ' id=>'+$el.attr('id'));
                    this.$('.ha_k_sex_kbn_radio_set').buttonset(); // patch for init
                },
            },
            // '.ka_k_gengo': {
            //     observe: 'ka_k_gengo',
            //     afterUpdate: function ($el, event, options) {
            //         var val = $el.val();
            //         $el.select2("val", val);
            //     },
            // },
            // '.ha_k_gengo': {
            //     observe: 'ha_k_gengo',
            //     afterUpdate: function ($el, event, options) {
            //         var val = $el.val();
            //         $el.select2("val", val);
            //     },
            // },
            '.depature_syako_opt': {
                observe: 'depature_syako_opt',
                onGet: utils.onGetWithRtCheck,
                getVal: function($el, event, options) {
                    utils.getValWithRtCheck($el, event, options);
                    return utils.clGetValKbnCdNm2( 'depature_syako_opt' ).apply( this, [$el, event, options] );
                },
                afterUpdate: function ($el, event, options) {
                    // console.log('.depature_syako_opt afterUpdate');
                    var val = $el.val();
                    $el.select2("val", val);
                    var data = $el.select2("data");
                    if ( data ) {
                        this.model.set( { depature_nm:     data.text,
                                          depature_zip_cd: data.zip_no,
                                          depature_addr1:  data.addr1_nm,
                                          // depature_addr2:  data.addr2_nm,
                                          // depature_tel:    data.tel,
                                        } );
                    } else { // clear
                        this.model.set( { depature_nm:     null,
                                          depature_zip_cd: null,
                                          depature_addr1:  null,
                                          // depature_addr2:  null,
                                          // depature_tel:    null,
                                        } );
                    }
                },
            },
            '.arrival_syako_opt': {
                observe: 'arrival_syako_opt',
                onGet: utils.onGetWithRtCheck,
                getVal: function($el, event, options) {
                    utils.getValWithRtCheck($el, event, options);
                    return utils.clGetValKbnCdNm2( 'arrival_syako_opt' ).apply( this, [$el, event, options] );
                },
                afterUpdate: function ($el, event, options) {
                    // console.log('.arrival_syako_opt afterUpdate');
                    var val = $el.val();
                    $el.select2("val", val);
                    var data = $el.select2("data");
                    if ( data ) {
                        this.model.set( { arrival_nm:     data.text,
                                          arrival_zip_cd: data.zip_no,
                                          arrival_addr1:  data.addr1_nm,
                                          // arrival_addr2:  data.addr2_nm,
                                          // arrival_tel:    data.tel,
                                        } );
                    } else { // clear
                        this.model.set( { arrival_nm:     null,
                                          arrival_zip_cd: null,
                                          arrival_addr1:  null,
                                          // arrival_addr2:  null,
                                          // arrival_tel:    null,
                                        } );
                    }
                },
            },
            '.ka_k_birth_gengoyear': {
                observe: 'ka_k_birth_gengoyear',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
            '.ka_k_birth_month': {
                observe: 'ka_k_birth_month',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
            '.ka_k_birth_day': {
                observe: 'ka_k_birth_day',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
            '.ha_k_birth_gengoyear': {
                observe: 'ha_k_birth_gengoyear',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
            '.ha_k_birth_month': {
                observe: 'ha_k_birth_month',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
            '.ha_k_birth_day': {
                observe: 'ha_k_birth_day',
                afterUpdate: function ($el, event, options) {
                    var val = $el.val();
                    $el.select2("val", val);
                },
            },
        },
    } ); // _JiSubViewBase

    // console.log( '_JiSubViewBase=>',  _JiSubViewBase );
    gMyApp.JiSubView = Backbone.View.extend( _JiSubViewBase );

} );
