<?php

/**
 * Juchu_JuchuCustomerinfo
 *
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfo
 * <AUTHOR> Sai
 * @version セレモニアカスタムファイル
 * @since      2014/12/04
 * @version    2016/04/07 会員区分は自動判別でなく入力されたものとする. 
 *                        ただし後日、最新 sano 版を反映させること
 * @version    2016/06/09 mihara date_kbn:12:出棺経,13:納骨,14:会食 追加
 * @version    2016/06/21 mihara 配偶者情報登録
 * @version    2017/01/06 mihara 顧客登録関連
 * @version    2017/01/23 mihara ゴミの customer_base_info データを削除
 * @version    2019/05/07 mihara 軽減税率対応
 * @filesource 
 */

/**
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfo
 * <AUTHOR>
 * @since      2014/02/12
 */
class Juchu_JuchuCustomerinfo extends Juchu_JuchuCustomerinfoCom {

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/01/14
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
    public function getInitData($controllerName) {
        
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();
        $this->_customerCd = $this->getCustomerCd();

        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        // 売上部門
        if(strlen($this->_sekoNo) == 0){
            $dataSekoKihon['bumon_cd_hdr']  = App_Utils::getTantoBumonCd();
            $dataSekoKihon['bumon_lnm_hdr'] = App_Utils::getTantoBumonNm();
        }        

        // 配偶者情報 2016/06/21 mihara
        $dataSekoKihon = array_merge($dataSekoKihon, $this->uiSpouseInfo());

        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        if (!isset($dataKihonFree['tanto_cd1'])) {
            // 新規登録時の受付担当者にログイン者の情報を設定する
            $dataKihonFree['tanto_cd1'] = App_Utils::getTantoCd();
            $dataKihonFree['tanto_cd1_nm'] = App_Utils::getTantoNm();
        }
        if (!isset($dataKihonFree['ts_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['ts_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
            $dataKihonFree['ts_free1_date'] = Msi_Sys_Utils::getDate();
            $dataKihonFree['ts_free1_time'] = Msi_Sys_Utils::getDate(null, 'H:i');
        }
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember(10);
        // 施行日程を取得する
        $dataNiteiCol = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfo = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 貸出備品情報を取得する
        $dataKashidasiCol = $this->getKashidasiBihin();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // 互助会金額マスタを取得する
        $gojokaiPrcMst = $this->getGojokaiPrcMst2();
        // 施行報告書を取得する
        $dataReport = $this->getReport();
        // 引継ぎ書を取得する
        $dataHikitsugi = $this->getHikitsugi();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        // 地区を取得する
        $dataChiku = $this->getChiku();
        // 顧客施行履歴を取得する
        $dataCstSeko = $this->getCstSekoHst();
        // アフターフォロー項目確認明細を取得する
        $a_f_c1 = $this->getCstAfterMsi1(self::CODE_KBN_CONTENT1);
        $a_f_c2 = $this->getCstAfterMsi2(self::CODE_KBN_CONTENT2);
        $db = Msi_Sys_DbManager::getMyDb();
        $gojokai_cose = $this->GetCodeNameMst2($db, self::CODE_GOJOKAI_COSE); // ソート順のため、個別取得する
        // 組を取得する
        $dataKumi = $this->getKumi();
        $dataKbns = array(
            // 基本タブ
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN_S), // 申込区分
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'haigu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAIGU_KBN), // 配偶者区分
            'gengo' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENGO), // 元号
            'gengo2' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENGO2), // 元号
            'setai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETAI_KBN), // 世帯主
            'hito_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HITO_KBN), // 筆頭者
            'keishiki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEISHIKI_KBN), // 葬儀形式
            'sk_addr' => $this->filter($dataCodeNameMst, self::CODE_SK_ADDR), // 葬家住所
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            'p_info' => $this->filter($dataCodeNameMst, self::CODE_P_INFO), // 個人情報保護
            // 日程タブ
            'nyukan_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_NYUKAN_KYO), // 入棺経
            'syukan_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUKAN_KYO), // 出棺経
            'kaso_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_KASO_KYO), // 火葬経
            'sogi_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_SOGI_BASHO_KBN), // 葬儀場所
            'dan_kaijo_kbn' => $this->filter($dataCodeNameMst, self::CODE_DAN_KAIJO_KBN), // 壇払会場
            'kai_kaijo_kbn' => $this->filter($dataCodeNameMst, self::CODE_KAI_KAIJO_KBN), // 会食会場
            'nokotu_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_NOKOTU_BASHO_KBN), // 納骨場所
            'kigae' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIGAE), // 着替え
            'nanoka' => $this->filter($dataCodeNameMst, self::CODE_KBN_NANOKA), // 初七日
            'tera_shokai' => $this->filter($dataCodeNameMst, self::CODE_KBN_TERA_SHOKAI), // 菩提寺紹介者
            'kaijo_use' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIJO_USE), // 会場利用
            // 喪主タブ
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            'keisyo_kbn' => $this->filter($dataCodeNameMst, self::CODE_KEISYO_KBN), // 敬称区分
            // 互助会タブ
            'kanyu_dantai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KANYU_DANTAI), // 加入団体
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN), // 用途
            'gojokai_cose' => $gojokai_cose, // 互助会コース
            'kanyu_toku' => $this->filter($dataCodeNameMst, self::CODE_KANYU_TOKU), // 加入団体（特約）
            // その他タブ
            'hs_gyomu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HS_GYOMU_KBN), // 用途
            'hs_anchi_kbn' => $this->filter($dataCodeNameMst, self::HS_ANCHI_KBN), // 搬送区分
            'delivery_kbn' => $this->filter($dataCodeNameMst, self::CODE_DELIVERY_KBN), // 搬入場所区分
            'mt_area' => $this->filter($dataCodeNameMst, self::CODE_MT_AREA_KBN), // 目録手配対象エリア
            'mt_item' => $this->filter($dataCodeNameMst, self::CODE_MT_ITEM_KBN), // 目録手配品目
            'mt_biko' => $this->filter($dataCodeNameMst, self::CODE_MT_BIKO), // 目録備考
            'iso_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ISO_KBN), // 移送者区分
//            'area' => $this->getArea(), // エリア
            'chiku' => $dataChiku, // 地区データ
            'kumi' => $dataKumi, // 組データ
            // 引継ぎ書
            'hiki_1' => $this->filter($dataCodeNameMst, self::CODE_HIKI_1),
            'hiki_2' => $this->filter($dataCodeNameMst, self::CODE_HIKI_2),
            'hiki_3' => $this->filter($dataCodeNameMst, self::CODE_HIKI_3),
            'hiki_4' => $this->filter($dataCodeNameMst, self::CODE_HIKI_4),
            'hiki_5' => $this->filter($dataCodeNameMst, self::CODE_HIKI_5),
            'hiki_6' => $this->filter($dataCodeNameMst, self::CODE_HIKI_6),
            'hiki_7' => $this->filter($dataCodeNameMst, self::CODE_HIKI_7),
            'hiki_8' => $this->filter($dataCodeNameMst, self::CODE_HIKI_8),
            'hiki_9' => $this->filter($dataCodeNameMst, self::CODE_HIKI_9),
            'hiki_10' => $this->filter($dataCodeNameMst, self::CODE_HIKI_10),
            'hiki_11' => $this->filter($dataCodeNameMst, self::CODE_HIKI_11),
            'hiki_12' => $this->filter($dataCodeNameMst, self::CODE_HIKI_12),
            'hiki_13' => $this->filter($dataCodeNameMst, self::CODE_HIKI_13),
            'hiki_14' => $this->filter($dataCodeNameMst, self::CODE_HIKI_14),
            'hiki_15' => $this->filter($dataCodeNameMst, self::CODE_HIKI_15),
            'hiki_16' => $this->filter($dataCodeNameMst, self::CODE_HIKI_16),
            'hiki_17' => $this->filter($dataCodeNameMst, self::CODE_HIKI_17),
            'hiki_18' => $this->filter($dataCodeNameMst, self::CODE_HIKI_18),
            'hiki_19' => $this->filter($dataCodeNameMst, self::CODE_HIKI_19),
            'hiki_20' => $this->filter($dataCodeNameMst, self::CODE_HIKI_20),
            'hiki_21' => $this->filter($dataCodeNameMst, self::CODE_HIKI_21),
            'hiki_22' => $this->filter($dataCodeNameMst, self::CODE_HIKI_22),
            'hiki_23' => $this->filter($dataCodeNameMst, self::CODE_HIKI_23),
            'hiki_24' => $this->filter($dataCodeNameMst, self::CODE_HIKI_24),
            'hiki_25' => $this->filter($dataCodeNameMst, self::CODE_HIKI_25),
            'hiki_26' => $this->filter($dataCodeNameMst, self::CODE_HIKI_26),
            'hiki_27' => $this->filter($dataCodeNameMst, self::CODE_HIKI_27),
            'hiki_28' => $this->filter($dataCodeNameMst, self::CODE_KBN_SEKO_BASHO),
            'hiki_hour' => $this->filter($dataCodeNameMst, self::CODE_HIKI_HOUR),
            'hiki_min' => $this->filter($dataCodeNameMst, self::CODE_HIKI_MIN),
            'hiki_rin' => $this->filter($dataCodeNameMst, self::CODE_HIKI_RIN),
            'hiki_dango' => $this->filter($dataCodeNameMst, self::CODE_HIKI_DANGO),
            'hiki_kityu' => $this->filter($dataCodeNameMst, self::CODE_HIKI_KITYU),
            'hiki_yoyaku' => $this->filter($dataCodeNameMst, self::CODE_HIKI_YOYAKU),
            // アフターフォロー
            'a_f_1' => $this->filter($dataCodeNameMst, self::CODE_KBN_RENRAKU),
            'a_f_2' => $this->filter($dataCodeNameMst, self::CODE_KBN_IF_NASHA),
        );
        $dataKbns['setai_kbn2'] = $this->replaceString($dataKbns['setai_kbn'], array("故人" => "対象者", "喪主" => "相談者")); // 世帯主2(生前依頼)
        $dataKbns['hito_kbn2'] = $this->replaceString($dataKbns['hito_kbn'], array("故人" => "対象者", "喪主" => "相談者")); // 筆頭者2(生前依頼)
        // 会社情報取得
        $kaisya_info = $this->getKaisyaInfo();
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataNiteiCol' => $dataNiteiCol,
            'dataSekyuInfo' => $dataSekyuInfo,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'dataKashidasiCol' => $dataKashidasiCol,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'gojokaiPrcMst' => $gojokaiPrcMst,
            'dataKbns' => $dataKbns,
            'dataReport' => $dataReport,
            'dataHikitsugi' => $dataHikitsugi,
            'a_f_c1' => $a_f_c1,
            'a_f_c2' => $a_f_c2,
            'dataCstSeko' => $dataCstSeko,
            'kaisya_info' => $kaisya_info,
            'taxInfo' => $taxInfo,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 施行日程情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/18
     * @return array 施行日程情報
     */
    private function getNitei() {

        $dataNitei = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分
                ,tsi.ts_based_nm    -- 日程基本名 
                ,tsi.ts_based_nm2   -- 日程基本名2 
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ymd-- 日程タイムスタンプ
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,CASE 
                    WHEN sn.free3_kbn = 1 AND sn.nitei_kbn = 1
                    THEN NULL
                    ELSE TO_CHAR(sn.nitei_ymd ,'HH24:MI')
                END nitei_time -- 日程時刻のみ
                ,TO_CHAR(sn.nitei_ed_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ed_ymd-- 日程終了タイムスタンプ
                ,TO_CHAR(sn.nitei_ed_ymd ,'HH24:MI') AS nitei_ed_time -- 日程終了時刻のみ
                ,sn.spot_cd       -- 場所区分コード
                ,sn.basho_kbn       -- 場所区分
                ,sn.basho_cd        -- 場所コード
                ,sn.basho_nm        -- 場所名
                ,sn.nyukan_kyo      -- 入棺経
                ,sn.syukan_kyo      -- 出棺経
                ,sn.kaso_kyo        -- 火葬経    
                ,sn.shishikisha_ninzu-- 司式者人数    
                ,sn.free1_kbn        -- 納棺着替え    
                ,sn.free2_kbn        -- 初七日    
                ,sn.v_free3          -- 初七日    
            FROM
                seko_nitei sn
                INNER JOIN 
                    tm_schedule_info tsi
                    ON
                    (
                         sn.nitei_kbn = tsi.date_kbn
                    )
            WHERE
                    sn.seko_no = :seko_no
                AND sn.delete_flg = 0
            ORDER BY
                tsi.disp_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));

        // タイムスケジュール情報マスタ取得SQL
        $sql2 = "
            SELECT
                tsi.ts_based_nm     -- 日程基本名
                ,tsi.ts_based_nm2   -- 日程基本名2 
                ,tsi.date_kbn       -- 日付区分 
            FROM
                tm_schedule_info tsi
            WHERE
                   tsi.delete_flg = 0
            ORDER BY
                tsi.disp_no
                ";
        $select2 = $db->easySelect($sql2);
        foreach ($select2 as &$value) {
            $value['nitei_kbn'] = (int) $value['date_kbn'];
        }
        // 施行日程に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $niteiOneRowData = array();
                $niteiOneRowData['seko_no'] = $select[$i]['seko_no'];
                $niteiOneRowData['nitei_kbn'] = (int) $select[$i]['nitei_kbn'];
                $niteiOneRowData['ts_based_nm'] = $select[$i]['ts_based_nm'];
                $niteiOneRowData['ts_based_nm2'] = $select[$i]['ts_based_nm2'];
                $niteiOneRowData['nitei_ymd'] = $select[$i]['nitei_ymd'];
                $niteiOneRowData['nitei_date'] = $select[$i]['nitei_date'];
                $niteiOneRowData['nitei_time'] = $select[$i]['nitei_time'];
                $niteiOneRowData['nitei_ed_ymd'] = $select[$i]['nitei_ed_ymd'];
                $niteiOneRowData['nitei_ed_time'] = $select[$i]['nitei_ed_time'];
                $niteiOneRowData['spot_cd'] = $select[$i]['spot_cd'];
                $niteiOneRowData['basho_kbn'] = $select[$i]['basho_kbn'];
                $niteiOneRowData['basho_cd'] = $select[$i]['basho_cd'];
                $niteiOneRowData['basho_nm'] = $select[$i]['basho_nm'];
                $niteiOneRowData['nyukan_kyo'] = $select[$i]['nyukan_kyo'];
                $niteiOneRowData['syukan_kyo'] = $select[$i]['syukan_kyo'];
                $niteiOneRowData['kaso_kyo'] = $select[$i]['kaso_kyo'];
                $niteiOneRowData['shishikisha_ninzu'] = $select[$i]['shishikisha_ninzu'];
                $niteiOneRowData['free1_kbn'] = $select[$i]['free1_kbn'];
                $niteiOneRowData['free2_kbn'] = $select[$i]['free2_kbn'];
                $niteiOneRowData['v_free3'] = $select[$i]['v_free3'];
                $dataNitei[$i] = $niteiOneRowData;
            }
        } else {
            $dataNitei = $select2;
        }
        return $dataNitei;
    }

    /**
     *
     * 貸出備品情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/28
     * @return array 貸出備品情報
     */
    private function getKashidasiBihin() {
        $bihin = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sqlTrn = "
            SELECT
                sk.seko_no              -- 施行番号
                ,sk.disp_no             -- 表示順序
                ,sk.shohin_cd           -- 商品コード
                ,CASE 
                    WHEN sk.shohin_nm IS NULL 
                    THEN sm.shohin_nm 
                    ELSE sk.shohin_nm 
                 END shohin_nm -- 商品名
                ,sk.kashidashi_kbn      -- 貸出区分
                ,sk.suryo               -- 貸出数量
                ,sm.nm_input_kbn        -- 名称入力区分
                ,TO_CHAR(sk.kaisyu_yotei_ymd,'YYYY/MM/DD') AS kaisyu_yotei_ymd-- 回収予定日
                ,0 AS up_flg            -- 更新対象フラグ 0:更新対象外 1:更新対象
                ,2 AS data_status       -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            FROM
                seko_kashidasi_bihn sk
            INNER JOIN
                shohin_mst sm
            ON  (
                    sk.shohin_cd = sm.shohin_cd
                AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                )
            WHERE
                    sk.seko_no = :seko_no
                AND sk.delete_flg = 0
                AND sm.delete_flg = 0
            ORDER BY
                sm.shohin_cd
                ";

        $sqlMst = "
            SELECT
                row_number () OVER () AS disp_no -- 表示順序
                ,sm.shohin_cd               -- 商品コード
                ,sm.shohin_nm               -- 商品名
                ,sm.kashidashi_kbn          -- 貸出区分
                ,0 AS suryo                 -- 貸出数量
                ,sm.nm_input_kbn            -- 名称入力区分
                ,NULL AS kaisyu_yotei_ymd   -- 回収予定日
                ,1 AS up_flg                -- 更新対象フラグ 0:更新対象外 1:更新対象
                ,0 AS data_status           -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            FROM
                shohin_mst sm
            WHERE
                    CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                AND sm.kashidashi_kbn IN (1)
                AND sm.delete_flg = 0
            ORDER BY
                sm.shohin_cd
                ";
        $select = $db->easySelect('SELECT seko_no FROM seko_kashidasi_bihn WHERE seko_no = :seko_no', array('seko_no' => $this->_sekoNo));
        // 施行貸出備品に存在するとき
        if (count($select) > 0) {
            $selectTrn = $db->easySelect($sqlTrn, array('seko_no' => $this->_sekoNo));
            $bihin = $selectTrn;
        } else {
            $selectMst = $db->easySelect($sqlMst);
            $bihin = $selectMst;
        }
        foreach ($bihin as &$value) {
            $value['disp_no'] = (int) $value['disp_no'];
        }
        return $bihin;
    }

    /**
     * 保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/17
     * @param array $req リクエスト
     */
    public function save($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();

        // $params = Msi_Sys_Utils::webInputs();// XXX
        // Msi_Sys_Utils::debug( 'req=>' .  Msi_Sys_Utils::dump($params) ); // XXX

        $controllerName = $req->getPost('controllerName');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataNiteiCol = Msi_Sys_Utils::json_decode($req->getPost('dataNiteiColJson'));
        $dataSekyuInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekyuInfoJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $dataKashidasiCol = Msi_Sys_Utils::json_decode($req->getPost('dataKashidasiColJson'));
        $dataKashidasiDelCol = Msi_Sys_Utils::json_decode($req->getPost('dataKashidasiDelColJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        // 部門コードを設定
        $dataSekoKihon['bumon_cd'] = $dataSekoKihon['bumon_cd_hdr'];
        unset($dataSekoKihon['bumon_cd_hdr']);
        unset($dataSekoKihon['bumon_lnm_hdr']);
        // 加入員番号重複チェック
        if (!$this->checkGojokaiMember($dataGojokaiMemberCol)) {
            return;
        }
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        if ( isset($dataSekoKihon['mg_customer_cd']) ) { // 2017/01/06
            $this->_mg_customer_cd = $dataSekoKihon['mg_customer_cd'];
        } else {
            $this->_mg_customer_cd = null;
        }
        unset($dataSekoKihon['mg_customer_cd']); // 2017/01/06
        $this->_customerCd = $this->getCustomerCdEx(); // 2017/01/06
//        Msi_Sys_Utils::debug( '1=>' . $this->_sekoNo );
        if (empty($this->_sekoNo)) {
            $this->_sekoNo = $this->getAutoSekoNo($db);
//            Msi_Sys_Utils::debug( '2=>' . $this->_sekoNo );
            if (is_null($this->_sekoNo)) {
                return;
            }
        }
        else { // 2019/05/07 mihara keigen
            App_KeigenUtils::sougiYmdChgPre( $this->_sekoNo ); // sougi_ymd 変更の準備
        }

        if (empty($this->_customerCd)) {
            $this->_customerCd = $this->getAutoCustomerCd($db);
        }
        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }

        // 互助会コース、基本パターン、会員区分を求める。
        $sekoInfo = $this->calcSekoPlanPtn($dataSekoKihon, $dataGojokaiInfo, $dataGojokaiMemberCol, $dataNiteiCol);
        if (count($sekoInfo) > 0) {
            $dataSekoKihon['gojokai_cose_cd'] = '0000'; // 互助会コースコード
            $dataSekoKihon['gojokai_kbn'] = $sekoInfo['gojokai_kbn']; // 互助会区分
            $dataSekoKihon['main_pt_cd'] = $sekoInfo['main_pt_cd']; // 基本パターン区分コード
            $dataSekoKihon['main_pt_kbn'] = $sekoInfo['main_pt_kbn']; // 基本パターン区分
            $changeFlg['kihonChangeFlg'] = true;
        }
        $this->_nebikiGojokaiKbn = null;
        $this->setNebikiGojokaiInfo($dataGojokaiInfo, $dataGojokaiMemberCol);
        if (!is_null($this->_nebikiGojokaiKbn)) {
            $dataSekoKihon['gojokai_kbn'] = $this->_nebikiGojokaiKbn; // 互助会区分
            $changeFlg['kihonChangeFlg'] = true;
        }
        // 施行プランコードが存在し、かつ基本パターンコードまたは互助会区分がが変わっていたら警告メッセージを出す
        if (!empty($this->_sekoPlanCd) && ($this->_mainPtCd !== $dataSekoKihon['main_pt_cd'] || $this->_gojokaiKbn !== $dataSekoKihon['gojokai_kbn'] )) {
            $data['status'] = 'NG';
            $data['msg'] = "基本パターンが変更されるため、更新することができません。処理を続行するには基本プランを一度削除する必要があります。";
            Msi_Sys_Utils::outJson($data);
            return;
        }
        if (!empty($this->_sekoPlanCd) && $this->isChangedPlanNebiki($dataGojokaiMemberCol)) {
            $data['status'] = 'NG';
            $data['msg'] = "プラン値引きが変更されるているため、更新することができません。処理を続行するには基本プランを一度削除する必要があります。";
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $gojokai_kbn_change = false; // 互助会区分変更フラグ
        if ($this->_gojokaiKbn !== "" && $this->_gojokaiKbn !== $dataSekoKihon['gojokai_kbn']) {
            $gojokai_kbn_change = true;
        }
//        if ($dataSekoKihon['gojokai_kbn'] == 6 && $dataSekoKihon['hs_gyomu_kbn2'] != 1) {
//            $data['status'] = 'NG';
//            $data['msg'] = "安置先が通夜会場ではないため、このプラン金額を利用することができません。";
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        $denpyoNo = $this->getJuchudenpyoNo();
        // 互助会完納を求める
        $flg = $this->checkHenreiDanbariKannou($dataGojokaiMemberCol);
        if ($flg !== '1') {
            $this->gojokaiKannoNgOutJson($flg);
            return;
        }

        // 駐車場警備依頼書を作成するか
        // $flg_irai 0:なし 1:削除のみ 2 作成と削除
        $flg_irai = $this->checkIfCreateIrai($db, $dataNiteiCol);
        if ($flg_irai > 0) {
            $this->saveSpecialHachuInfo($db, '36', $flg_irai);
        }
        // 駐車場警備依頼書を作成する
        // 施行日程保存処理 
        if ($changeFlg['niteiChangeFlg']) {
            $cnt += $this->saveSekoNitei($db, $dataNiteiCol);
        }
        // 請求先情報保存処理 
//        if ($changeFlg['sekyuInfoChangeFlg'] || $dataSekoKihon['sekyu_kbn'] === "1") {
        $cnt += $this->saveSekyuInfo($db, $dataSekyuInfo);
        // 基本情報の請求コード更新のため
        $changeFlg['kihonChangeFlg'] = true;
        $dataSekoKihon['sekyu_cd'] = $this->_sekyuCd;
//        }
        // 施行互助会情報保存処理 
        if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $cnt = $this->saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol);
        }
        // 施行互助会加入者保存処理 (葬儀日変更時も対象にするため、niteiChangeFlg追加)
        if ($changeFlg['gojokaiMemberChangeFlg'] || $changeFlg['niteiChangeFlg']) {
            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 貸出備品処理
        if ($changeFlg['kashidasiChangeFlg']) {
            // 貸出備品削除処理
            $cnt += $this->deleteKashidasi($db, $dataKashidasiDelCol);
            // 貸出備品保存処理 
            $cnt += $this->saveKashidasi($db, $dataKashidasiCol);
        }
        // 会員区分を求めるを求める
        //  $kaiin = $this->calcKaiinKbn($dataGojokaiInfo, $dataGojokaiMemberCol);
        //  $dataSekoKihon['kaiin_cd'] = $kaiin['kaiin_cd']; // 会員区分コード
        //  $dataSekoKihon['kaiin_kbn'] = $kaiin['kaiin_kbn']; // 会員区分
        // 施行基本保存処理 
        if ($changeFlg['kihonChangeFlg']) {
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $cnt = $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg);
            if ($bumonCdChangeFlg) {
                // 伝票の部門コードを更新する。
                $cnt += $this->updateDenpyoBumoncd($db, $denpyoNo, $dataSekoKihon['bumon_cd']);
            }
            // 伝票の担当コードを更新する
            $cnt += $this->updateDenpyoDantocd($db, $dataSekoKihon['seko_tanto_cd']);
            // 別途費用の式場商品・通夜会場商品受注伝票作成処理
            if ($changeFlg['bettoCreFlg'] || ($changeFlg['uneiCreFlg'])) {
                // 施行基本情報を再設定する
                $this->setInitParam();
                $dataApp = array();
                $isKakutei = $this->isMitsuKakutei();
                if ($changeFlg['bettoCreFlg']) {
                    $juchuSpecial = new Juchu_JuchuSpecial($this->_sekoNo, $this->_sekoNoSub, $this->_selectSekoKihon);
                    list($bettoUpData, $bettoDelData) = $juchuSpecial->getJuchuDetailSpecialData($isKakutei);
                    // 表示順を再設定する
                    if ($isKakutei) {
                        App_Utils::resetDispNoUri($bettoUpData, $this->_sekoNo, $this->_sekoNoSub);
                        $cnt +=$this->saveUriage($db, $dataApp, $bettoUpData, $bettoDelData);
                    } else {
                        App_Utils::resetDispNo($bettoUpData, $this->_sekoNo, $this->_sekoNoSub);
                        $cnt +=$this->saveJuchu($db, $dataApp, $bettoUpData, $bettoDelData);
                    }
                }
                if ($changeFlg['uneiCreFlg']) {
                    $juchuUnei = new Juchu_JuchuSpecial($this->_sekoNo, $this->_sekoNoSub, $this->_selectSekoKihon);
                    list($uneiUpData, $uneiDelData) = $juchuUnei->getJuchuDetailUneiData($isKakutei);
                    // 表示順を再設定する
                    if ($isKakutei) {
                        App_Utils::resetDispNoUri($uneiUpData, $this->_sekoNo, $this->_sekoNoSub);
                        $cnt +=$this->saveUriage($db, $dataApp, $uneiUpData, $uneiDelData);
                    } else {
                        App_Utils::resetDispNo($uneiUpData, $this->_sekoNo, $this->_sekoNoSub);
                        $cnt +=$this->saveJuchu($db, $dataApp, $uneiUpData, $uneiDelData);
                    }
                }
            }
        }
        //if (!$this->isMitsuKakutei()) { // 見積未確定
        //    // 受注明細の付帯値引きの再設定を行う
        //    $dataApp = array();
        //    $msiData = $this->getJuchuMsiData();
        //    App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
        //    $cnt +=$this->saveJuchu($db, $dataApp, $msiData);
        //}
        // 施行基本フリー保存処理 
        if ($changeFlg['kihonFreeChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
            $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon);
        }
        // 施行報告書保存処理 
        if ($changeFlg['houkokuChangeFlg']) {
            $dataReport = Msi_Sys_Utils::json_decode($req->getPost('dataHoukokuJson'));
            $cnt += $this->saveReport($db, $dataReport);
        }
        // 引継ぎ書保存処理 
        if ($changeFlg['hikitsugiChangeFlg']) {
            $dataHikitsugi = Msi_Sys_Utils::json_decode($req->getPost('dataHikitsugiJson'));
            $cnt += $this->saveHikitsugi($db, $dataHikitsugi);
        }

        // 顧客施行履歴保存処理（アフターフォロー）
        $dataCstSeko = Msi_Sys_Utils::json_decode($req->getPost('dataCstSekoHstJson'));
        if ($changeFlg['afterFollowEqChangeFlg']) {
            $this->saveCstSekoHst($db, $dataCstSeko);
        }
        // アフターフォロー項目確認明細保存処理
        if ($changeFlg['afterFollowEqChangeFlg2']) {
            $cstAfterMsi1 = Msi_Sys_Utils::json_decode($req->getPost('dataCstAfterMsiJson1'));
            $cstAfterMsi2 = Msi_Sys_Utils::json_decode($req->getPost('dataCstAfterMsiJson2'));
            $this->saveCstAfterMsi($db, $cstAfterMsi1, $cstAfterMsi2);
        }
        // 顧客基本情報保存処理
        if ($changeFlg['kihonChangeFlg']) {
            $this->saveCstBaseInfo($db, $dataSekoKihon);
            $this->saveSpouseInfo($db, $dataSekoKihon); // 配偶者情報 2016/06/21 mihara
            $this->saveSekyuSakiToCstBase($db, $dataSekoKihon, $dataSekyuInfo); // 請求先を顧客基本情報テーブルへ保存 2017/01/06
        }
        // アフターフォロー対象情報作成処理
        if ($changeFlg['afterFollowEqChangeFlg'] || $changeFlg['afterFollowEqChangeFlg2'] ||
                (isset($this->_selectSekoKihon['sougi_ymd']) && $dataCstSeko['funeral_date'] != $this->_selectSekoKihon['sougi_ymd'])) {
            $dataCstSeko['customer_cd'] = $this->_customerCd;
            DataMapper_CustShoudan::saveAfTargetInfo($db, $dataCstSeko);
        }
        // 受注業務の新規作成時は施行管理情報を更新しない
        if (!App_Utils::isCustomerNewinJuchu($controllerName)) {
            $dataSbt = 2;
            if ($controllerName === self::MITSU) {
                $dataSbt = 1; // 見積書の場合
                App_Utils::saveTantoSekoInfo($this->_sekoNo);
            }
            // 施行管理情報を更新する
            App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $dataSbt, 1);
        }

        // 2019/05/07 mihara keigen
        try {
            App_KeigenUtils::sougiYmdChgEnd( $db, $this->_sekoNo, $denpyoNo ); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array( 'status'=>'NG', 'msg' => $e->getMessage() );
            Msi_Sys_Utils::outJson($data);
            return;
        }

        $cnt +=Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 施行日程情報を取得する
        $dataNiteiColNew = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfoNew = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember(10);
        // 貸出備品を取得する
        $dataKashidasiColNew = $this->getKashidasiBihin();

        // サイドメニューデータを取得する
        if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataNiteiCol' => $dataNiteiColNew,
            'dataSekyuInfo' => $dataSekyuInfoNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataKashidasiCol' => $dataKashidasiColNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 施行基本保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/18
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 画面施行基本データ
     * @param boolean &$bumonCdChangeFlg 部門コード変更フラグ
     * @return int 更新件数
     */
    private function saveSekoKihon($db, $dataSekoKihon, &$bumonCdChangeFlg) {
        // 施行基本存在チェック
        $selectSekoKihon = $this->selectSekoKihon();
        $tableSekokihon = "seko_kihon_info";
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'k_file');
        array_push($except, 'uketuke_tanto_nm');
        array_push($except, 'seko_tanto_nm');
        array_push($except, 'sd_yotei_date');
        array_push($except, 'sd_yotei_time');
        array_push($except, 'm_file');
        array_push($except, 'jichu_kakute_ymd');
        array_push($except, 'mt_hannyu_ymd');
        array_push($except, 'mt_hannyu_start_time');
        array_push($except, 'mt_hannyu_end_time');
        array_push($except, 'tuya_shikijo_prc1');
        array_push($except, 'tuya_shikijo_prc2');
        array_push($except, 'chiku_lnm');
        array_push($except, 'kumi_lnm');

        array_push($except, 'iso_tanto_nm1');
        array_push($except, 'iso_tanto_nm2');
        array_push($except, 'iso_tanto_cd1_2');
        array_push($except, 'iso_tanto_cd2_2');
        array_push($except, 'iso_tanto_nm1_2');
        array_push($except, 'iso_tanto_nm2_2');

        // 2016/06/21 mihara
        array_push($except, 'spouse_name'); 
        array_push($except, 'spouse_kana'); 
        array_push($except, 'sp_gengo'); 
        array_push($except, 'sp_seinengappi_ymd'); 
        array_push($except, 'sp_nenrei_man'); 

        array_push($except, 'keisyo_kbn'); 
        
        // emptyToNull
        $dataSekoKihon['k_nenrei_kyounen'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['k_nenrei_kyounen']);
        $dataSekoKihon['shikijo_shiyou_prc'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['shikijo_shiyou_prc']);
        $dataSekoKihon['tuya_paku_su'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tuya_paku_su']);
        $dataSekoKihon['tuya_shikijo_tanka'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tuya_shikijo_tanka']);
        $dataSekoKihon['tuya_paku_su2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tuya_paku_su2']);
        $dataSekoKihon['tuya_shikijo_tanka2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tuya_shikijo_tanka2']);
        $dataSekoKihon['tk_house_cnt'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tk_house_cnt']);
        $dataSekoKihon['tk_person_cnt'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['tk_person_cnt']);
        $dataSekoKihon['kk_kinmusaki_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kk_kinmusaki_kbn']);
        $dataSekoKihon['mk_kinmusaki_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['mk_kinmusaki_kbn']);
        $dataSekoKihon['sd_copy_cnt'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sd_copy_cnt']);
        $dataSekoKihon['az_death_cnt'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['az_death_cnt']);
        $dataSekoKihon['az_photo_cnt'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['az_photo_cnt']);
        if (isset($dataSekoKihon['sougi_ymd'])) {
            $dataSekoKihon['sougi_ymd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sougi_ymd']);
        }
        $dataSekoKihon['n_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['n_free1']);
        $dataSekoKihon['sd_step_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sd_step_kbn']);
        $dataSekoKihon['n_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['keisyo_kbn']);

        // 移送者区分が他者依頼の場合、仕入先コードをを設定し、移送者コードをクリアする
        if ($dataSekoKihon['iso_accept_cd_1'] == 2) {
            $dataSekoKihon['iso_siire_cd1'] = $dataSekoKihon['iso_tanto_cd1'];
            $dataSekoKihon['iso_tanto_cd1'] = null;
        }
        if ($dataSekoKihon['iso_accept_cd_2'] == 2) {
            $dataSekoKihon['iso_siire_cd2'] = $dataSekoKihon['iso_tanto_cd2'];
            $dataSekoKihon['iso_tanto_cd2'] = null;
        }
        // 故人名添付ファイル存在するときに登録する
        if ($dataSekoKihon['k_file']) {
            $dataSekoKihon['k_file_nm'] = $dataSekoKihon['k_file'];
        }
        // 喪主名添付ファイル存在するときに登録する
        if ($dataSekoKihon['m_file']) {
            $dataSekoKihon['m_file_nm'] = $dataSekoKihon['m_file'];
        }
        // 通夜式場使用料を求める
        $shikijo_prc1 = $dataSekoKihon['tuya_shikijo_prc1'];
        $shikijo_prc2 = $dataSekoKihon['tuya_shikijo_prc2'];
        if (isset($shikijo_prc1) && isset($shikijo_prc2)) {
            $dataSekoKihon['tuya_shikijo_prc'] = $shikijo_prc1 * 1 + $shikijo_prc2;
        } else if (isset($shikijo_prc1)) {
            $dataSekoKihon['tuya_shikijo_prc'] = $shikijo_prc1;
        } else if (isset($shikijo_prc2)) {
            $dataSekoKihon['tuya_shikijo_prc'] = $shikijo_prc2;
        } else {
            $dataSekoKihon['tuya_shikijo_prc'] = null;
        }
        // 葬儀日が入力された場合
        if (isset($dataSekoKihon['sougi_ymd'])) {
            if (empty($dataSekoKihon['daicho_no_eria']) && empty($dataSekoKihon['daicho_no_eria']) && empty($dataSekoKihon['daicho_no_seq'])) {
                // 台帳番号を自動採番する
                $daichoNo = $this->getAutoDaichoNo($dataSekoKihon['sougi_ymd']);
                // 台帳番号を設定する
//                if (empty($dataSekoKihon['daicho_no_eria'])) {
                $dataSekoKihon['daicho_no_eria'] = $daichoNo['daicho_no_eria'];
//                }
//                if (empty($dataSekoKihon['daicho_no_mm'])) {
                $dataSekoKihon['daicho_no_mm'] = $daichoNo['daicho_no_mm'];
//                }
//                if (empty($dataSekoKihon['daicho_no_seq'])) {
                $dataSekoKihon['daicho_no_seq'] = $daichoNo['daicho_no_seq'];
//                }
            }
        }
        if (Msi_Sys_Utils::myCount($selectSekoKihon) === 0) {
            $dataSekoKihon['seko_no'] = $this->_sekoNo;
            // 施行基本登録SQL
            //$sqlSekoKihon = $this->makeInsertSQL($tableSekokihon, $dataSekoKihon, $except);
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeInsertSQL($tableSekokihon, $dataSekoKihon, $except);
        } else {
            if ($selectSekoKihon['bumon_cd'] !== $dataSekoKihon['bumon_cd']) {
                $bumonCdChangeFlg = true;
            }
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $dataSekoKihon['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行基本更新SQL
            //$sqlSekoKihon = $this->makeUpdateSQL($tableSekokihon, $dataSekoKihon, $where, $except);
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeUpdateSQL($tableSekokihon, $dataSekoKihon, $where, $except);
        }
        $cnt = $db->easyExecute($sqlSekoKihon, $param);
        return $cnt;
    }

    /**
     * 施行日程保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/18
     * @param Msi_Sys_Db $db db
     * @param array $dataNiteiCol 画面日程タブデータ
     * @return int 更新件数
     */
    private function saveSekoNitei($db, $dataNiteiCol) {
        $cnt = 0;
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'nitei_date');
        array_push($except, 'nitei_time');
        array_push($except, 'ts_based_nm');
        array_push($except, 'ts_based_nm2');
        array_push($except, 'date_kbn');
        array_push($except, 'nitei_ed_time');

//        Msi_Sys_Utils::debug( '$dataNiteiCol=>' .  Msi_Sys_Utils::dump($dataNiteiCol));
//        Msi_Sys_Utils::debug( '3=>' . $this->_sekoNo );
        // 葬儀場所情報を取得
        $bashoInfo = $this->getSougiNiteiBasho($dataNiteiCol);
        // 施行日程データがない場合、登録する
        // 施行日程がある場合、更新する
        foreach ($dataNiteiCol as $niteiRow) {
            // 日程区分 1:亡日 2:湯灌 3:入棺 4:通夜 5:出棺 6:火葬 7:葬儀 8:壇払 10:自宅出棺,12:出棺経,13:納骨,14:会食
            $nitei_kbn = $niteiRow["nitei_kbn"];
            $niteiRow['free1_kbn'] = Msi_Sys_Utils::emptyToNull($niteiRow['free1_kbn']);
            $niteiRow['free2_kbn'] = Msi_Sys_Utils::emptyToNull($niteiRow['free2_kbn']);
            if ($nitei_kbn === 1) { // 亡日
                if (empty($niteiRow['nitei_time'])) {
                    $niteiRow['free3_kbn'] = 1;
                } else {
                    $niteiRow['free3_kbn'] = null;
                }
            }
            if (empty($niteiRow['nitei_ed_time'])) {
                $niteiRow['nitei_ed_ymd'] = null;
            }
            // 受注伝票明細存在チェック
            $sqlSelectNitei = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分 
            FROM
                seko_nitei sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY
                sn.nitei_kbn
                ";
            $selectNitei = $db->easySelect($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
//            Msi_Sys_Utils::debug( '$selectNitei=>' .  Msi_Sys_Utils::dump($selectNitei));
            if (count($selectNitei) === 0) {
                if ($nitei_kbn === 1) {
                    $niteiRow['spot_code_kbn'] = '0220'; // 搬送業務区分
                } else if ($nitei_kbn === 3 || $nitei_kbn === 5) {
                    $niteiRow['spot_code_kbn'] = self::HS_ANCHI_KBN; // 安置先
                } else if ($nitei_kbn === 7) {
                    $niteiRow['spot_code_kbn'] = self::CODE_SOGI_BASHO_KBN; // 葬儀場所
                } else if ($nitei_kbn === 8) { // 壇払会場
                    $niteiRow['spot_code_kbn'] = self::CODE_DAN_KAIJO_KBN;
                } else if ($nitei_kbn === 13) { // 13:納骨
                    $niteiRow['spot_code_kbn'] = self::CODE_NOKOTU_BASHO_KBN;
                } else if ($nitei_kbn === 14) { // 14:会食
                    $niteiRow['spot_code_kbn'] = self::CODE_KAI_KAIJO_KBN;
                }
                $niteiRow['seko_no'] = $this->_sekoNo;
//                Msi_Sys_Utils::debug( $nitei_kbn. '=>' . $this->_sekoNo );
                // 施行日程登録SQL
                //$sql = $this->makeInsertSQL("seko_nitei", $niteiRow, $except);
//                array_pop($except, 'seko_no');
//                Msi_Sys_Utils::debug( '$except=>' .  Msi_Sys_Utils::dump($except));
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_nitei", $niteiRow, $except);
//                Msi_Sys_Utils::debug( 'param=>' .  Msi_Sys_Utils::dump($param));
            } else {
//                Msi_Sys_Utils::debug( $nitei_kbn. '=>' . $this->_sekoNo );
                array_push($except, 'seko_no');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['nitei_kbn'] = $niteiRow['nitei_kbn'];  // 日程区分
                $where['delete_flg'] = 0;  // 削除フラグ
                $niteiRow = $this->setSyukanNiteiBasho($bashoInfo, $niteiRow);
                // 施行基本更新SQL
//                $sql = $this->makeUpdateSQL("seko_nitei", $niteiRow, $where, $except);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_nitei", $niteiRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }
    /**
     * 葬儀場所情報を取得
     * 
     * @param type $dataNiteiCol
     * @return type $bashoInfo
     */
    private function getSougiNiteiBasho($dataNiteiCol){
        $bashoInfo = array();
        foreach ($dataNiteiCol as $niteiRow) {
            // 日程区分 7:葬儀
            $nitei_kbn = $niteiRow["nitei_kbn"];
            if($nitei_kbn === 7) {
                $bashoInfo['basho_cd'] = $niteiRow['basho_cd'];
                $bashoInfo['basho_nm'] = $niteiRow['basho_nm'];
            }
        }
        return $bashoInfo;
    }
    /**
     * 出棺場所に葬儀場所を設定
     * 
     * @param type $bashoInfo
     * @param type $niteiRow
     * @return type $niteiRow
     */
    private function setSyukanNiteiBasho($bashoInfo, $niteiRow){
        if (Msi_Sys_Utils::myCount($bashoInfo) > 0) {
            $nitei_kbn = $niteiRow["nitei_kbn"];
            // 日程区分 5:出棺
            if($nitei_kbn === 5) {
                $niteiRow['basho_cd'] = $bashoInfo['basho_cd'];
                $niteiRow['basho_nm'] = $bashoInfo['basho_nm'];
            }
        }
        return $niteiRow;
    }
    /**
     * 施行貸出備品論理削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/28
     * @param Msi_Sys_Db $db db
     * @param array $dataKashidasiDelCol 施行貸出備品削除データ
     * @return int 更新件数
     */
    private function deleteKashidasi($db, $dataKashidasiDelCol) {
        $cntAll = 0;
        foreach ($dataKashidasiDelCol as $oneRow) {
            $sql = "
            UPDATE 
                seko_kashidasi_bihn
            SET
                delete_flg = 1
            WHERE
                    seko_no = :seko_no 
                AND disp_no = :disp_no
                AND delete_flg = 0
                ";
            $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'disp_no' => $oneRow["disp_no"]));
            // トランに登録する前に画面より削除された場合は、削除データとして登録を行う
            //  この処理をしないとトランに登録する前に画面より全削除で保存した場合に再度表示されてしまう。
            if ($cnt === 0 && $oneRow["data_status"] === 0 && !empty($oneRow["disp_no"])) {
                $oneRow = Msi_Sys_Utils::remapArrayFlat($oneRow, 'disp_no shohin_cd shohin_nm kashidashi_kbn suryo kaisyu_yotei_ymd');
                $oneRow['kaisyu_yotei_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['kaisyu_yotei_ymd']);
                $oneRow['seko_no'] = $this->_sekoNo;
                $oneRow['delete_flg'] = 1;
                // 施行貸出備品登録SQL
                //$sql = $this->makeInsertSQL("seko_kashidasi_bihn", $oneRow, $except);
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kashidasi_bihn", $oneRow);
                $cnt = $db->easyExecute($sql, $param);
            }
            $cntAll = $cntAll + $cnt;
        }
        return $cntAll;
    }

    /**
     * 施行貸出備品保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/28
     * @param Msi_Sys_Db $db db
     * @param array $dataKashidasiCol 施行貸出備品データ
     * @return int 更新件数
     */
    private function saveKashidasi($db, $dataKashidasiCol) {
        $cnt = 0;
        $maxDispNo = $this->getMaxDispNo();
        // 最大表示順を求める
        foreach ($dataKashidasiCol as $oneRow) {
            $maxDispNo = max($maxDispNo, $oneRow['disp_no']);
        }
        // 施行貸出備品データがない場合、登録する
        // 施行貸出備品データがある場合、更新する
        foreach ($dataKashidasiCol as $oneRow) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($oneRow, 'disp_no shohin_cd shohin_nm kashidashi_kbn suryo kaisyu_yotei_ymd');
            $oneRow['kaisyu_yotei_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['kaisyu_yotei_ymd']);
            // 施行貸出備品存在チェック
            $sqlSelect = "
            SELECT
                sk.seko_no          -- 施行番号
                ,sk.disp_no         -- 表示順序
            FROM
                seko_kashidasi_bihn sk
            WHERE
                    sk.seko_no = :seko_no
                AND sk.disp_no = :disp_no
                AND sk.delete_flg = 0
                ";
            $select = $db->easySelect($sqlSelect, array('seko_no' => $this->_sekoNo, 'disp_no' => $oneRow["disp_no"]));
            if (count($select) === 0) {
                $oneRow['seko_no'] = $this->_sekoNo;
                if ($oneRow['disp_no'] === null) {
                    $oneRow['disp_no'] = ++$maxDispNo;
                }
                // 施行貸出備品登録SQL
                //$sql = $this->makeInsertSQL("seko_kashidasi_bihn", $oneRow, $except);
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kashidasi_bihn", $oneRow);
            } else {
                // 更新対象外項目設定
                $except = array();
                array_push($except, 'disp_no');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;     // 施行番号
                $where['disp_no'] = $oneRow["disp_no"];  // 表示順序
                $where['delete_flg'] = 0;  // 削除フラグ
                // 施行貸出備品更新SQL
                //$sql = $this->makeUpdateSQL("seko_kashidasi_bihn", $oneRow, $where, $except);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kashidasi_bihn", $oneRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 施行貸出備品最大表示順を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/28
     * @return int 施行貸出備品最大表示順
     */
    private function getMaxDispNo() {
        // 施行貸出備品最大表示順
        $maxDispNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            COALESCE(MAX(disp_no),0) AS disp_no
        FROM
            seko_kashidasi_bihn
        WHERE
            seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if (count($select) > 0) {
            $maxDispNo = $select['disp_no'];
        }
        return $maxDispNo;
    }

    /**
     *
     * currentのcss名を取得する
     * 'customer' => 'お客様情報'
     * 'schedule' => 'タイムスケジュール'
     * 'sosogirei' => '葬送儀礼'
     * 'gift' => '返礼品'
     * 'cook' => '料理'
     * 'danbarai' => '壇払'
     * 'betto' => '別途費用'
     * 'betto' => '立替費用'
     * 'nebiki' => '値引'
     *
     * <AUTHOR> Sai
     * @since 2014/2/4
     * @return schedule
     */
    public function getCssClassName() {
        return 'customer';
    }

    /**
     *
     * 文字列を置換する
     *
     * <AUTHOR> Sai
     * @since 2014/3/19
     * @param array $dataKbns 置換元配列
     * @param array $replace 置換情報
     * @return array 置換後配列
     */
    private function replaceString($dataKbns, $replace) {

        foreach ($dataKbns as &$val) {
            foreach ($replace as $substring => $newsubstring) {
                $lnm = preg_replace("/$substring/", $newsubstring, $val['kbn_value_lnm']);
                $val['kbn_value_lnm'] = $lnm;
            }
        }
        return $dataKbns;
    }

    /**
     *
     * 互助会コース、基本パターン、会員区分を求める。
     *
     * <AUTHOR> Sai
     * @since 2014/05/13
     * @param array $dataSekoKihon 施行基本情報
     * @param array $dataGojokaiInfo 施行互助会情報
     * @param array $dataGojokaiMemberCol 施行互助会加入情報
     * @param array $dataNiteiCol 施行日程情報
     * @return array[gojokai_kbn]     => 互助会区分
     *          array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     *          array[kaiin_cd]        => 会員区分コード
     *          array[kaiin_kbn]       => 会員区分

     */
    private function calcSekoPlanPtn($dataSekoKihon, $dataGojokaiInfo, $dataGojokaiMemberCol, $dataNiteiCol) {
        $sekoPP = array();
        // 申込区分 1:葬儀 2:法事 3:単品 4:生前依頼
        $moushi = $dataSekoKihon['moushi_cd'];
        if ($moushi === self::MOUSHI_KBN_SOUGI || $moushi === self::MOUSHI_KBN_SEIZEN || $moushi === self::MOUSHI_KBN_OTHER) {
            // 基本プランの互助会区分を求める
            $sekoPlan = $this->calcSekoPlan($dataGojokaiInfo, $dataGojokaiMemberCol);
            $sekoPP['gojokai_kbn'] = $sekoPlan['gojokai_kbn']; // 互助会区分
            // 基本プランのパターンを求める
            $sekoPtn = $this->calcSekoPtn($dataSekoKihon, $dataNiteiCol);
            $sekoPP['main_pt_cd'] = $sekoPtn['main_pt_cd']; // 基本パターン区分コード
            $sekoPP['main_pt_kbn'] = $sekoPtn['main_pt_kbn']; // 基本パターン区分
        }

        return $sekoPP;
    }

    /**
     *
     * 基本プランの互助会区分を求める。
     *
     * <AUTHOR> Sai
     * @since 2014/05/15
     * @param array $dataGojokaiInfo 施行互助会情報
     * @param array $dataGojokaiMemberCol 施行互助会加入情報
     * @return array[gojokai_kbn] => 互助会区分
     */
    private function calcSekoPlan($dataGojokaiInfo, $dataGojokaiMemberCol) {
        $sekoPlan = array();
        // 互助会区分 デフォルト 0:一般
        $gojokai_kbn = '0';

        // 加入団体（一般） 
        $kanyu_cd = $dataGojokaiInfo['kanyu_dantai_kbn'];
        if (!empty($kanyu_cd)) {
            $kanyu = $this->getGojokaiCouseMst2(self::CODE_KBN_KANYU_DANTAI, $kanyu_cd);
            if (Msi_Sys_Utils::myCount($kanyu) > 0) {
                $gojokai_kbn = $kanyu['gojokai_kbn'];
            }
        }
        // 加入団体（特約） 
        $spec_agent_cd = $dataGojokaiInfo['spec_agent_cd'];
        if (!empty($spec_agent_cd)) {
            $spec_agent = $this->getGojokaiCouseMst2(self::CODE_KANYU_TOKU, $spec_agent_cd);
            if (Msi_Sys_Utils::myCount($spec_agent) > 0) {
                $gojokai_kbn = $spec_agent['gojokai_kbn'];
            }
        }
        // 画面で求めた互助会区分を設定する
        if (isset($dataGojokaiInfo['gojokai_kbn'])) {
            $gojokai_kbn = $dataGojokaiInfo['gojokai_kbn'];
        }
//        $gojokaiCouseMst = $this->getGojokaiCouseMst();
//        $sosogireiCol = null;
//        // 葬送儀礼を使用した場合の互助会区分を求める
//        foreach ($dataGojokaiMemberCol as $oneRow) {
//            if ($oneRow['yoto_kbn'] === '1') { // 1:葬送儀礼
//                $sosogireiCol[] = $oneRow;
//                $course_snm_cd = $oneRow['course_snm_cd'];
//                foreach ($gojokaiCouseMst as $coseOneRow) {
//                    if ($course_snm_cd == $coseOneRow['gojokai_cose_iw']) {
//                        $gojokai_kbn = $coseOneRow['gojokai_kbn'];
//                        break;
//                    }
//                }
//            }
//        }
//        $plan_use_prc = $dataGojokaiInfo['plan_use_prc'];  // プラン利用金額
//        $plan_change_prc = $dataGojokaiInfo['plan_change_prc'];  // プラン変更差額
//        // 葬送儀礼が複数、またはプラン変更差額が入力された場合
//        if (count($sosogireiCol) > 1 || $plan_change_prc != 0) {
//            $gojokai_kbn = '0'; // デフォルト 0:一般に一旦もどす
//            $gojokaiPrcMst = $this->getGojokaiPrcMst2(); // 互助会金額マスタ
//            foreach ($gojokaiPrcMst as $oneRow) {
//                if ($oneRow['course_price'] == $plan_use_prc) {
//                    $gojokai_kbn = $oneRow['gojokai_kbn'];
//                    break;
//                }
//            }
//        }
        $sekoPlan['gojokai_kbn'] = $gojokai_kbn;
        return $sekoPlan;
    }

    /**
     *
     * 基本プランのパターンを求める。
     *
     * <AUTHOR> Sai
     * @since 2014/05/15
     * @param array $dataSekoKihon 施行基本情報
     * @param array $dataNiteiCol 施行日程情報
     * @return array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     */
    private function calcSekoPtn($dataSekoKihon, $dataNiteiCol) {
        $sekoPtn = array();

        // 葬儀区分 1:個人 2:社葬 3:寺院葬
        $sogi = $dataSekoKihon['sougi_cd'];
        // 形式 1:個人葬 2:団体葬（合同葬）3:家族葬 4:火葬式 5:お別れ会
        $keishiki = $dataSekoKihon['keishiki_cd'];
        // 安置先 00:自宅 01:通夜会場 99:その他
        $anchi = $dataSekoKihon['hs_gyomu_cd2'];
        // 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
        $sikijyo = null;
        // 施行日程情報
        foreach ($dataNiteiCol as $oneRow) {
            if ($oneRow['nitei_kbn'] === 7) { // 7:葬儀
                $sikijyo = $oneRow['spot_cd'];
                break;
            }
        }
        // 形式で団体葬（合同葬）、家族葬、お別れ会が選択されている場合、個人葬と同じプランを表示する。
        if ($keishiki === '2' || $keishiki === '3' || $keishiki === '5') {
            $keishiki = '1';
        }
        list($main_pt_cd, $main_pt_kbn) = $this->calcKihonPtn($sogi, $keishiki, $anchi, $sikijyo);
        $sekoPtn['main_pt_cd'] = $main_pt_cd; // 基本パターン区分コード
        $sekoPtn['main_pt_kbn'] = $main_pt_kbn; // 基本パターン区分
        return $sekoPtn;
    }

    /**
     *
     * エリアを取得する（山形県限定）
     *
     * <AUTHOR> Sai
     * @since 2014/06/16
     * @return array 地区
     */
    private function getArea() {
        $dataArea = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                kokyo_dantai_cd
                ,addr2_nm
            FROM
                zip_no_mst
            WHERE
                addr1_nm = '山形県'
            GROUP BY
                kokyo_dantai_cd
                ,addr2_nm
            ORDER BY
                kokyo_dantai_cd
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            $dataArea['000000'] = 'すべて';
            for ($i = 0; $i < count($select); $i++) {
                $dataArea[$select[$i]['kokyo_dantai_cd']] = $select[$i]['addr2_nm'];
            }
        }
        return $dataArea;
    }

    /**
     *
     * 地区を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/14
     * @version 2014/06/16 Juchu_JuchuAbstractから移動
     * @return array 地区
     */
    private function getChiku() {
        $dataChiku = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "SELECT 
                    chiku_cd
                    ,chiku_lnm
                FROM
                    chiku_mst
                WHERE
                    delete_flg = 0
                ORDER BY
                    chiku_cd
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataChiku[$select[$i]['chiku_cd']] = $select[$i]['chiku_lnm'];
            }
        }
        return $dataChiku;
    }

    /**
     *
     * 組を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/14
     * @version 2014/06/16 Juchu_JuchuTimescheduleから移動
     * @return array 組
     */
    private function getKumi() {
        //$dataKumi = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "SELECT 
                    kumi_cd
                    ,kumi_lnm
                    ,chiku_cd
                FROM
                    kumi_mst
                WHERE
                    delete_flg = 0
                ORDER BY
                    chiku_cd
                    ,kumi_cd
                ";
        $select = $db->easySelect($sql);
//        if (count($select) > 0) {
//            for ($i = 0; $i < count($select); $i++) {
//                $dataKumi[$select[$i]['kumi_cd']] = $select[$i]['kumi_lnm'];
//            }
//        }
        return $select;
    }

    /**
     *
     * 施行報告書を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/13
     * @return array 施行報告書データ
     */
    private function getReport() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sr.seko_no         -- 施行番号
            ,TO_CHAR(sr.report_ymd ,'YYYY/MM/DD') AS report_ymd -- 報告日
            ,sr.kaiso_ninzu    -- 会葬人数
            ,sr.t_seika_cnt    -- 当社生花数
            ,sr.seika_cnt      -- 生花数
            ,sr.jt_saidan      -- 自宅祭壇
            ,sr.sk_saidan      -- 式場祭壇
            ,sr.h1_saidan      -- 祭壇に関して
            ,sr.h1_saidan_nm   -- 祭壇に関してイメージ名
            ,sr.h1_saidan_img  -- 祭壇に関してイメージ
            ,sr.h2_ihin        -- 遺品装飾
            ,sr.h2_ihin_nm     -- 遺品装飾イメージ名
            ,sr.h2_ihin_img    -- 遺品装飾イメージ
            ,sr.m1_souke       -- 葬家情報
            ,sr.m2_care_point  -- 気を遣った点
            ,sr.m3_person      -- 故人らしさ
            ,sr.m4_thanks      -- お客様からの言葉
            ,sr.m5_impression  -- お手伝い等の方々の印象
            ,sr.m6_claim       -- ミス・クレーム
            ,sr.m7_urikake     -- 売掛
            ,sr.m8_etc         -- イレギューラー・その他
            ,sr.f1_reason      -- 訃報（理由）
            ,sr.f2_reply       -- 訃報（対応）
        FROM
            seko_result_report sr
        WHERE
            sr.seko_no = :seko_no
        AND sr.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 施行報告書保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/08/13
     * @param Msi_Sys_Db $db db
     * @param array $dataReport 施行報告書データ
     * @return int 更新件数
     */
    private function saveReport($db, $dataReport) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataReport, 'report_ymd kaiso_ninzu t_seika_cnt seika_cnt jt_saidan sk_saidan h1_saidan h2_ihin m1_souke m2_care_point 
                                                              m3_person m4_thanks m5_impression m6_claim m7_urikake m8_etc f1_reason f2_reply 
                                                              h1_saidan_nm h1_saidan_img h2_ihin_nm h2_ihin_img');
        $oneRow['report_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['report_ymd']);
        $oneRow['kaiso_ninzu'] = Msi_Sys_Utils::emptyToNull($oneRow['kaiso_ninzu']);
        $oneRow['seika_cnt'] = Msi_Sys_Utils::emptyToNull($oneRow['seika_cnt']);
        $oneRow['t_seika_cnt'] = Msi_Sys_Utils::emptyToNull($oneRow['t_seika_cnt']);
        $except = array();
        // 存在チェック
        $select = $this->getReport();
        if (count($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_result_report", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_result_report", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * 施行引継ぎ書を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/18
     * @return array 施行引継ぎ書データ
     */
    private function getHikitsugi() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sh.seko_no          -- 施行番号
            ,TO_CHAR(sh.visit_ymd ,'YYYY/MM/DD HH24:MI') AS visit_ymd-- お伺い時間タイムスタンプ
            ,TO_CHAR(sh.visit_ymd ,'YYYY/MM/DD') AS visit_date-- お伺い時間日付のみ
            ,TO_CHAR(sh.visit_ymd ,'HH24:MI') AS visit_time-- お伺い時間時刻のみ
            ,sh.jt_kbn          -- 自宅祭壇区分
            ,sh.jt_setup_cd     -- 設置コード
            ,sh.jt_setup_kbn    -- 設置区分
            ,sh.jt_geta_cd      -- 下駄コード
            ,sh.jt_geta_kbn     -- 下駄区分
            ,sh.jt_sode_cd      -- 袖コード
            ,sh.jt_sode_kbn     -- 袖区分
            ,sh.jt_reason_cd    -- 無理由コード
            ,sh.jt_reason_kbn   -- 無理由区分
            ,sh.jt_reason_etc   -- 無理由その他
            ,sh.ty_saidan_cd    -- 通夜祭壇確認コード
            ,sh.ty_saidan_kbn   -- 通夜祭壇確認区分
            ,sh.ty_kumotu_cd    -- 通夜供物コード
            ,sh.ty_kumotu_kbn   -- 通夜供物区分
            ,sh.ty_butubana_cd  -- 通夜仏花コード
            ,sh.ty_butubana_kbn -- 通夜仏花区分
            ,sh.jt_maku_cd      -- 自宅幕張コード
            ,sh.jt_maku_kbn     -- 自宅幕張区分
            ,sh.jt_ten_syaku    -- 天井（尺）
            ,sh.jt_ten_cnt      -- 天井（枚）
            ,sh.jt_waki_syaku   -- 脇幕（尺）
            ,sh.jt_waki_cnt     -- 脇幕（枚）
            ,sh.jt_gabyo_cd     -- 画鋲コード
            ,sh.jt_gabyo_kbn    -- 画鋲区分
            ,sh.jt_mae_cd       -- 前飾りコード
            ,sh.jt_mae_kbn      -- 前飾り区分
            ,sh.jt_mae_memo     -- 前飾りメモ
            ,sh.hg_size_cd      -- 柩サイズコード
            ,sh.hg_size_kbn     -- 柩サイズ区分
            ,sh.hg_height       -- 身長
            ,sh.hg_width_cd     -- 柩幅コード
            ,sh.hg_width_kbn    -- 柩幅区分
            ,sh.hg_memo         -- 柩メモ
            ,TO_CHAR(sh.kaso_ymd ,'YYYY/MM/DD HH24:MI') AS kaso_ymd-- 火葬日時タイムスタンプ
            ,TO_CHAR(sh.kaso_ymd ,'YYYY/MM/DD') AS kaso_date-- 火葬日時日付のみ
            ,TO_CHAR(sh.kaso_ymd ,'HH24:MI') AS kaso_time--火葬日時時刻のみ
            ,TO_CHAR(sh.sougi_ymd ,'YYYY/MM/DD HH24:MI') AS sougi_ymd-- 葬儀日時タイムスタンプ
            ,TO_CHAR(sh.sougi_ymd ,'YYYY/MM/DD') AS sougi_date-- 葬儀日時日付のみ
            ,TO_CHAR(sh.sougi_ymd ,'HH24:MI') AS sougi_time--葬儀日時時刻のみ
            ,sh.spot_cd         -- 場所区分コード
            ,sh.basho_kbn       -- 場所区分
            ,sh.basho_cd        -- 場所コード
            ,sh.basho_nm        -- 場所名
            ,sh.kibo_reason     -- 希望理由
            ,sh.tr_saidan_cd    -- 寺祭壇コード
            ,sh.tr_saidan_kbn   -- 寺祭壇区分
            ,TO_CHAR(sh.setup_ymd ,'YYYY/MM/DD HH24:MI') AS setup_ymd-- 設営日時タイムスタンプ
            ,TO_CHAR(sh.setup_ymd ,'YYYY/MM/DD') AS setup_date-- 設営日時日付のみ
            ,TO_CHAR(sh.setup_ymd ,'HH24:MI') AS setup_time--設営日時時刻のみ
            ,sh.yukan_cd        -- 湯灌確認コード
            ,sh.yukan_kbn       -- 湯灌確認区分
            ,sh.eisei1_cd       -- 遺体衛生保全1コード
            ,sh.eisei1_kbn      -- 遺体衛生保全1区分
            ,sh.eisei2_cd       -- 遺体衛生保全2コード
            ,sh.eisei2_kbn      -- 遺体衛生保全2区分
            ,sh.eisei_memo      -- 遺体衛生保全メモ
            ,sh.nenkin_cd       -- 年金確認コード
            ,sh.nenkin_kbn      -- 年金確認区分
            ,sh.nenkin_memo      -- 年金メモ
            ,sh.agree_cd        -- 同意書コード
            ,sh.agree_kbn       -- 同意書区分
            ,sh.agree_chk_cd    -- 同意確認コード
            ,sh.agree_chk_kbn   -- 同意確認区分
            ,sh.agree_memo      -- 同意確認メモ
            ,sh.photo_cd        -- 写真預かりコード
            ,sh.photo_kbn       -- 写真預かり区分
            ,sh.photo_memo      -- 写真預かりメモ
            ,sh.sutra_cd        -- 枕経コード
            ,sh.sutra_kbn       -- 枕経区分
            ,TO_CHAR(sh.sutra_ymd ,'YYYY/MM/DD HH24:MI') AS sutra_ymd-- 枕経日時タイムスタンプ
            ,TO_CHAR(sh.sutra_ymd ,'YYYY/MM/DD') AS sutra_date-- 枕経日時日付のみ
            ,TO_CHAR(sh.sutra_ymd ,'HH24:MI') AS sutra_time--枕経日時時刻のみ
            ,TO_CHAR(sh.jyucyu_ymd ,'YYYY/MM/DD HH24:MI') AS jyucyu_ymd-- 受注日時タイムスタンプ
            ,TO_CHAR(sh.jyucyu_ymd ,'YYYY/MM/DD') AS jyucyu_date-- 受注日時日付のみ
            ,TO_CHAR(sh.jyucyu_ymd ,'HH24:MI') AS jyucyu_time--受注日時時刻のみ
            ,sh.v_free1         -- 持参品の備考
            ,sh.v_free2         -- 受注時間・場所の備考
            ,sh.temple_cd       -- 寺院連絡コード
            ,sh.temple_kbn      -- 寺院連絡区分
            ,sh.temple_hour     -- 寺院連絡時間
            ,sh.tk_meet_cd      -- 隣組集合コード
            ,sh.tk_meet_kbn     -- 隣組集合区分
            ,TO_CHAR(sh.tk_meet_ymd ,'YYYY/MM/DD HH24:MI') AS tk_meet_ymd-- 隣組集合日時タイムスタンプ
            ,TO_CHAR(sh.tk_meet_ymd ,'YYYY/MM/DD') AS tk_meet_date-- 隣組集合日時日付のみ
            ,TO_CHAR(sh.tk_meet_ymd ,'HH24:MI') AS tk_meet_time--隣組集合日時時刻のみ
            ,sh.tk_house_cnt    -- 軒数
            ,sh.tk_person_cnt   -- 人数
            ,sh.mk_hana_yen     -- 枕花金額
            ,sh.mk_hana_cnt     -- 枕花本数
            ,sh.mk_tehai_cd     -- 手配済コード
            ,sh.mk_tehai_kbn    -- 手配済区分
            ,sh.mk_umu_cd       -- 枕花有無コード
            ,sh.mk_umu_kbn      -- 枕花有無区分
            ,sh.book_cd         -- 書物コード
            ,sh.book_kbn        -- 書物区分
            ,sh.kakezu_cnt      -- 掛図枚数
            ,sh.kakezu_cd       -- 掛図コード
            ,sh.kakezu_kbn      -- 掛図区分
            ,sh.oshirase_cnt    -- お知らせ状枚数
            ,sh.souke_info_memo -- 葬家情報
            ,sh.memo            -- 備考
            ,sh.rin_cd          -- リン鐘コード
            ,sh.rin_kbn         -- リン鐘コード区分
            ,sh.dango_cd        -- 団子セットコード
            ,sh.dango_kbn       -- 団子セットコード区分
            ,sh.kityushi_cd     -- 忌中紙・訃報紙コード
            ,sh.kityushi_kbn    -- 忌中紙・訃報紙コード区分
            ,sh.kityushi_memo   -- 忌中紙・訃報紙コードメモ
            ,sh.kasoba_cd       -- 火葬場
            ,sh.kasoba_nm       -- 火葬場名
            ,sh.yoyaku_umu_cd   -- 予約の有無コード
            ,sh.yoyaku_umu_kbn  -- 予約の有無コード区分
            ,sh.yoyaku_no       -- 予約番号
            ,sh.dry_cd          -- ドライ時間コード
            ,sh.dry_kbn         -- ドライ時間コード区分
            ,sh.free1_code_cd   -- 施行場所コード
            ,sh.free1_kbn       -- 施行場所区分
        FROM
            seko_hiki_info sh
        WHERE
            sh.seko_no = :seko_no
        AND sh.delete_flg = 0
                ";

        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 施行引継ぎ書保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/08/18
     * @param Msi_Sys_Db $db db
     * @param array $dataHiki 施行引継ぎ書データ
     * @return int 更新件数
     */
    private function saveHikitsugi($db, $dataHiki) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataHiki, 'visit_ymd kaso_ymd sougi_ymd spot_cd basho_kbn
                                                              basho_cd basho_nm kibo_reason yukan_cd yukan_kbn 
                                                              agree_cd agree_kbn agree_chk_cd agree_chk_kbn agree_memo photo_cd photo_kbn photo_memo sutra_cd sutra_kbn
                                                              sutra_ymd temple_cd temple_kbn temple_hour mk_hana_yen mk_hana_cnt
                                                              mk_tehai_cd mk_tehai_kbn souke_info_memo memo 
                                                              eisei1_cd eisei1_kbn eisei2_cd eisei2_kbn eisei_memo mk_umu_cd mk_umu_kbn
                                                              rin_cd rin_kbn dango_cd dango_kbn kityushi_cd kityushi_kbn kityushi_memo 
                                                              kasoba_cd kasoba_nm yoyaku_umu_cd yoyaku_umu_kbn yoyaku_no dry_cd dry_kbn 
                                                              jyucyu_ymd v_free1 v_free2 free1_code_cd free1_kbn');
        $oneRow['visit_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['visit_ymd']);
        $oneRow['kaso_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['kaso_ymd']);
        $oneRow['sougi_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['sougi_ymd']);
        $oneRow['sutra_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['sutra_ymd']);
        $oneRow['temple_hour'] = Msi_Sys_Utils::emptyToNull($oneRow['temple_hour']);
        $oneRow['mk_hana_yen'] = Msi_Sys_Utils::emptyToNull($oneRow['mk_hana_yen']);
        $oneRow['mk_hana_cnt'] = Msi_Sys_Utils::emptyToNull($oneRow['mk_hana_cnt']);
        $oneRow['jyucyu_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['jyucyu_ymd']);
        $except = array();
        // 存在チェック
        $select = $this->getHikitsugi();
        if (count($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hiki_info", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hiki_info", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * 互助会コースマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2015/1/16
     * @param string $group_code_kbn 
     * @param string $group_cd 
     * @return array 互助会コースマスタ
     */
    private function getGojokaiCouseMst2($group_code_kbn, $group_cd) {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                gcm.gojokai_cose_cd     -- 互助会コースコード
                ,gcm.gojokai_kbn        -- 互助会コースコード頭文字
            FROM
                gojokai_couse_mst gcm
            WHERE
                    CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
                AND gcm.delete_flg = 0
                AND gcm.group_code_kbn = :group_code_kbn
                AND gcm.group_kbn = :group_cd
                ";
        $select = $db->easySelOne($sql, array('group_code_kbn' => $group_code_kbn, 'group_cd' => $group_cd));
        return $select;
    }

    /**
     * 顧客基本情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2015/01/22
     * @version 2017/01/06   mihara  喪主(customer_kbn*=2)の顧客登録
     * @param Msi_Sys_Db $db db
     * @param array $dataSk 施行基本情報データ
     * @return int 更新件数
     */
    private function saveCstBaseInfo($db, $dataSk) {

        $oneRow['customer_nm'] = $dataSk['m_nm']; // 顧客名
        $oneRow['customer_knm'] = $dataSk['m_knm']; // 顧客名カナ
        $oneRow['zoku_cd'] = $dataSk['m_zoku_cd'];  // 続柄コード
        $oneRow['zoku_kbn'] = $dataSk['m_zoku_kbn'];  // 続柄
        $oneRow['birth_era'] = $dataSk['m_gengo'];  // 生年月日元号
        $oneRow['birth'] = $dataSk['m_seinengappi_ymd'];  // 生年月日	
        $oneRow['age'] = $dataSk['m_nenrei_man'];  // 年齢
        if ($dataSk['mg_kbn'] == 1) { // 故人に同じ
            $oneRow['zip_no'] = $dataSk['kg_yubin_no']; // 現住所郵便番号
            $oneRow['addr1'] = $dataSk['kg_addr1']; // 現住所1
            $oneRow['addr2'] = $dataSk['kg_addr2']; // 現住所2
            $oneRow['tel_no'] = $dataSk['kg_tel']; // 現住所電話番号
        } else {
            $oneRow['zip_no'] = $dataSk['mg_yubin_no']; // 現住所郵便番号
            $oneRow['addr1'] = $dataSk['mg_addr1']; // 現住所1
            $oneRow['addr2'] = $dataSk['mg_addr2']; // 現住所2
            $oneRow['tel_no'] = $dataSk['mg_tel']; // 現住所電話番号
        }
        $oneRow['mobile_tel'] = $dataSk['mg_m_tel']; // 携帯番号
//        $oneRow['emergency_nm'] = null; // 緊急先名
//        $oneRow['emergency_knm'] = null; // 緊急先名カナ
        $oneRow['em_zoku_cd2'] = $dataSk['m_zoku_cd2']; // 顧客からみた続柄コード
        $oneRow['em_zoku_kbn2'] = $dataSk['m_zoku_kbn2']; // 顧客からみた続柄
//        $oneRow['em_zip_no'] = null; // 緊急先郵便番号
//        $oneRow['em_addr1'] = null; // 緊急先住所1
//        $oneRow['em_addr2'] = null; // 緊急先住所2
//        $oneRow['em_tel'] = null; // 緊急先電話番号
//        $oneRow['em_mobile_tel'] = null; // 緊急先携帯番号
        $oneRow['resident_kbn'] = null; // 現住所に同じ
        // if ($dataSk['mg_kbn'] == 1) { // 故人に同じ
        if ($dataSk['mj_kbn'] == 1) { // 故人に同じ   // 2017/01/07 mg_kbn=>mj_kbn
            $oneRow['resident_zip_no'] = $dataSk['kg_yubin_no']; // 現住所郵便番号
            $oneRow['resident_addr1'] = $dataSk['kg_addr1']; // 現住所1
            $oneRow['resident_addr2'] = $dataSk['kg_addr2']; // 現住所2
            $oneRow['resident_tel'] = $dataSk['kg_tel']; // 現住所電話番号
        } else {
            $oneRow['resident_zip_no'] = $dataSk['mj_yubin_no']; // 住民登録郵便番号
            $oneRow['resident_addr1'] = $dataSk['mj_addr1']; // 住民登録住所1
            $oneRow['resident_addr2'] = $dataSk['mj_addr2']; // 住民登録住所2
            $oneRow['resident_tel'] = $dataSk['mj_tel']; // 住民登録電話番号
        }
        $oneRow['family_kbn'] = null; // 現住所に同じ
//        $oneRow['family_zip_no'] = $dataSk['mh_yubin_no']; // 住民登録郵便番号
//        $oneRow['family_addr1'] = $dataSk['mh_addr1']; // 住民登録住所1
//        $oneRow['family_addr2'] = $dataSk['mh_addr2']; // 住民登録住所2
        $oneRow['office_kbn'] = Msi_Sys_Utils::emptyToNull($dataSk['mk_kinmusaki_kbn']); // 勤務先区分
        $oneRow['office_nm'] = $dataSk['mk_kinmusaki_nm']; // 勤務先名
        $oneRow['office_post_nm'] = $dataSk['mk_yakusyoku_nm']; // 役職／職種
        $oneRow['office_tel'] = $dataSk['mk_tel']; // 勤務先TEL
        $oneRow['office_fax'] = $dataSk['mk_fax']; // 勤務先FAX
        // 2017/01/07 mihara
        $oneRow['customer_kbn_cd'] = '2'; // code_nm_mst.code_kbn(1850) 2(喪主)
        $oneRow['customer_kbn'] = 2;
        $oneRow['sodan_kbn'] = null; // 事前相談有無
        $oneRow['rireki_kbn'] = null; // 施行履歴有無
        if ( $dataSk['moushi_kbn'] == 1 ) { // 申込区分（=1：葬儀）
            $oneRow['rireki_kbn'] = 1; // 施行履歴有無
        } else if ( $dataSk['moushi_kbn'] == 5 ) { // 申込区分（=5：事前相談）
            $oneRow['sodan_kbn'] = 1; // 事前相談有無
        }
        $oneRow['syushi_cd']  = $dataSk['syushi_cd'];   // 宗旨コード
        $oneRow['syushi_kbn'] = $dataSk['syushi_kbn'];  // 宗旨区分
        $oneRow['syuha_cd']   = $dataSk['syuha_cd'];    // 宗派コード
        $oneRow['syuha_kbn']  = $dataSk['syuha_kbn'];   // 宗派区分
        $oneRow['syuha_nm']   = $dataSk['syuha_nm'];    // 宗派名
        $oneRow['syuha_knm']  = $dataSk['syuha_knm'];   // 宗派名カナ
        //$oneRow['jyusho_kbn'] = $dataSk['jyusho_kbn'];  // 寺院区分
        $oneRow['jyusho_cd']  = $dataSk['jyusho_cd'];   // 寺院コード
        $oneRow['jyusho_nm']  = $dataSk['jyusho_nm'];   // 寺院名
        $oneRow['jyusho_knm'] = $dataSk['jyusho_knm'];  // 寺院名カナ
        // 家紋情報取得
        $orei = DataMapper_SekoOreiInfo::findOne($db, array('seko_no' => $this->_sekoNo));
        if(Msi_Sys_Utils::myCount($orei) > 0){
            $oneRow['kamon_code'] = $orei['kamon_code']; // 家紋コード
            $oneRow['kamon_nm']   = $orei['kamon_nm'];   // 家紋名称
        }
        // 存在チェック
        // $select = $this->getCustomerBaseInfo();
        $select = $this->getCustomerBaseInfoEx($this->_mg_customer_cd); // 2017/01/06 
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['customer_cd'] = $this->_customerCd;
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_base_info", $oneRow);
        } else {
            // 新規時に登録していないデータが存在するので、
            // ここでも、施行情報履歴テーブル（CS_SEKO_INFO_HISTORY）へ記録している。本来不要。2017/01/06
            $cur_sekoNo = $select['seko_no'];
            if ( $cur_sekoNo !== $this->_sekoNo ) {
                $this->saveCsSekoInfoHistory($db, $this->_mg_customer_cd, $cur_sekoNo);
            }

            // 条件部
            $where['customer_cd'] = $this->_customerCd;  // 顧客コード
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_base_info", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);

        $this->saveCsSekoInfoHistory($db, $this->_customerCd, $this->_sekoNo); // 新規時もここで登録する  2017/01/06

        // ゴミの customer_base_info データを削除する 2017/01/23 mihara
        $cnt = $db->easyExecute( <<< END_OF_SQL
DELETE FROM customer_base_info cbi
 WHERE seko_no=:seko_no
   AND customer_kbn=2 -- 喪主
   AND customer_cd <> :customer_cd
   AND (customer_nm = '' OR customer_nm IS NULL)
   AND EXISTS (SELECT * FROM customer_base_info cbi2 
                       WHERE cbi2.customer_cd <> cbi.customer_cd
                         AND cbi2.customer_kbn=2
                         AND cbi2.delete_flg=0) -- 他のデータが存在する
END_OF_SQL
                                 , array('seko_no'=>$this->_sekoNo, 'customer_cd'=>$this->_customerCd) );

        return $cnt;
    }

    /**
     *
     * 顧客施行履歴を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/01/22
     * @param string $seko_history_no 施行履歴
     * @return array 顧客施行履歴データ
     */
    private function getCstSekoHst($seko_history_no = '1') {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
        SELECT
            csh.customer_cd         -- 顧客コード
            ,csh.seko_history_no    -- 施行履歴№
            ,csh.seko_no            -- 施行番号
            ,TO_CHAR(csh.die_date ,'YYYY/MM/DD') AS die_date        -- 死亡日
            ,TO_CHAR(csh.funeral_date ,'YYYY/MM/DD') AS funeral_date-- 葬儀日
            ,csh.customer_kbn       -- 顧客区分
            ,csh.af_houji_kbn       -- 法事案内区分
            ,csh.af_altar_cd        -- 仏壇有無コード
            ,csh.af_altar_kbn       -- 仏壇有無区分
            ,csh.af_altar_state_cd  -- 仏壇無状況コード
            ,csh.af_altar_state_kbn -- 仏壇無状況区分
            ,csh.af_altar_note      -- 仏壇関係特記事項
            ,csh.af_grave_cd        -- お墓有無コード
            ,csh.af_grave_kbn       -- お墓有無区分
            ,csh.af_grave_state_cd  -- お墓無状況コード
            ,csh.af_grave_state_kbn -- お墓無状況区分
            ,csh.af_grave_note      -- お墓関係特記事項
            ,csh.af_etc_cd          -- その他有無コード
            ,csh.af_etc_kbn         -- その他有無区分
            ,csh.af_etc_state_cd    -- その他無状況コード
            ,csh.af_etc_state_kbn   -- その他無状況区分
            ,csh.af_etc_note        -- その他特記事項
            ,csh.af_advise_kbn      -- 相談窓口紹介区分
            ,csh.af_contact_cd      -- 連絡方法コード
            ,csh.af_contact_kbn     -- 連絡方法区分
            ,csh.af_contact_note    -- 連絡方法特記事項
        FROM
            customer_seko_history csh
        WHERE
            csh.customer_cd = :customer_cd
        AND csh.seko_history_no = :seko_history_no
        AND csh.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('customer_cd' => $this->_customerCd, 'seko_history_no' => $seko_history_no));
        return $select;
    }

    /**
     * 顧客施行履歴保存処理 
     *
     * <AUTHOR> Sai
     * @since 2015/01/22
     * @param Msi_Sys_Db $db db
     * @param array $dataCstSeko 顧客施行履歴データ
     * @return int 更新件数
     */
    private function saveCstSekoHst($db, $dataCstSeko) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataCstSeko, 'customer_cd seko_history_no die_date funeral_date customer_kbn af_houji_kbn
                                                              af_altar_cd af_altar_kbn af_altar_state_cd af_altar_state_kbn af_altar_note
                                                              af_grave_cd af_grave_kbn af_grave_state_cd af_grave_state_kbn af_grave_note
                                                              af_etc_cd af_etc_state_cd af_etc_state_kbn af_etc_note af_advise_kbn
                                                              mk_tehai_cd mk_tehai_kbn souke_info_memo memo 
                                                              af_contact_cd af_contact_kbn af_contact_note');
        $oneRow['af_altar_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_altar_cd']);
        $oneRow['af_grave_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_grave_cd']);
        $oneRow['af_altar_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_altar_kbn']);
        $oneRow['af_grave_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_grave_kbn']);
        $oneRow['die_date'] = Msi_Sys_Utils::emptyToNull($oneRow['die_date']);
        $oneRow['funeral_date'] = Msi_Sys_Utils::emptyToNull($oneRow['funeral_date']);

        $except = array();
        // 存在チェック
        $select = $this->getCstSekoHst($oneRow['seko_history_no']);
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['customer_cd'] = $this->_customerCd;
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_seko_history", $oneRow);
        } else {
            array_push($except, 'customer_cd');
            array_push($except, 'seko_history_no');
            // 条件部
            $where['customer_cd'] = $this->_customerCd;  // 顧客コード
            $where['seko_history_no'] = $oneRow['seko_history_no'];  // 施行履歴№
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_seko_history", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * アフターフォロー項目確認明細を取得する
     *
     * <AUTHOR> Sai
     * @param string $code_kbn コード区分
     * @param string $seko_history_no 施行履歴
     * @since 2015/01/22
     * @return array アフターフォロー項目確認明細データ
     */
    private function getCstAfterMsi1($code_kbn, $seko_history_no = '1') {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "

        SELECT
            cm.code_kbn            -- コード区分
            ,cm.code_kbn_nm        -- コード区分名
            ,cm.kbn_value_cd       -- 区分値コード
            ,cm.kbn_value_cd_num   -- 区分値コード数値
            ,cm.kbn_value_lnm      -- 区分値正式名
            ,cam.select_kbn        -- 選択区分
            ,cam.customer_cd       -- 顧客コード
            ,cam.seko_history_no   -- 施行履歴№
        FROM
            code_nm_mst cm 
            LEFT OUTER JOIN 
                customer_afchk_msi cam 
                ON
                (
                        cm.code_kbn = cam.trf_code_kbn
                    AND cm.kbn_value_cd = cam.trf_cd
                    AND cm.kbn_value_cd_num = cam.trf_kbn
                    AND :customer_cd = cam.customer_cd
                    AND :seko_history_no = cam.seko_history_no
                    AND cam.delete_flg = 0
                )
        WHERE
            cm.delete_flg = 0
        AND cm.code_kbn = :code_kbn
        ORDER BY
            cm.code_kbn
            ,cm.kbn_value_cd_num
                ";
        $select = $db->easySelect($sql, array('code_kbn' => $code_kbn, 'customer_cd' => $this->_customerCd, 'seko_history_no' => $seko_history_no));
        return $select;
    }

    /**
     *
     * アフターフォロー項目確認明細を取得する
     *
     * <AUTHOR> Sai
     * @param string $code_kbn コード区分
     * @param string $seko_history_no 施行履歴
     * @since 2015/01/22
     * @return array アフターフォロー項目確認明細データ
     */
    private function getCstAfterMsi2($code_kbn, $seko_history_no = '1') {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "

        SELECT
            cm.code_kbn            -- コード区分
            ,cm.code_kbn_nm        -- コード区分名
            ,cm.kbn_value_cd       -- 区分値コード
            ,cm.kbn_value_cd_num   -- 区分値コード数値
            ,cm.kbn_value_lnm      -- 区分値正式名
            ,cam.select_kbn        -- 選択区分
            ,cam.customer_cd       -- 顧客コード
            ,cam.seko_history_no   -- 施行履歴№
        FROM
            code_nm_mst cm 
            LEFT OUTER JOIN 
                customer_afchk_msi cam 
                ON
                (
                        cm.code_kbn = cam.env_code_kbn
                    AND cm.kbn_value_cd = cam.env_cd
                    AND cm.kbn_value_cd_num = cam.env_kbn
                    AND :customer_cd = cam.customer_cd
                    AND :seko_history_no = cam.seko_history_no
                    AND cam.delete_flg = 0
                )
        WHERE
            cm.delete_flg = 0
        AND cm.code_kbn = :code_kbn
        ORDER BY
            cm.code_kbn
            ,cm.kbn_value_cd_num
                ";
        $select = $db->easySelect($sql, array('code_kbn' => $code_kbn, 'customer_cd' => $this->_customerCd, 'seko_history_no' => $seko_history_no));
        return $select;
    }

    /**
     * アフターフォロー項目確認明細保存処理 
     *
     * <AUTHOR> Sai
     * @since 2015/01/22
     * @param Msi_Sys_Db $db db
     * @param array $dataCstAfterCol1 アフターフォロー項目確認明細データ1
     * @param array $dataCstAfterCol2 アフターフォロー項目確認明細データ2
     * @return int 更新件数
     */
    private function saveCstAfterMsi($db, $dataCstAfterCol1, $dataCstAfterCol2) {
        $seko_history_no = '1';
        if (count($dataCstAfterCol1) > 0) {
            if (!empty($dataCstAfterCol1[0]['seko_history_no'])) {
                $seko_history_no = $dataCstAfterCol1[0]['seko_history_no'];
            }
        }
        // 削除→登録する
        $sql = "
            DELETE
                FROM
                    customer_afchk_msi
            WHERE
                    customer_cd = :customer_cd 
                    AND seko_history_no = :seko_history_no
                ";
        $cnt = $db->easyExecute($sql, array('customer_cd' => $this->_customerCd, 'seko_history_no' => $seko_history_no));
        $msi_no = 0;
        $disp_no1 = 0;
        $disp_no2 = 0;
        foreach ($dataCstAfterCol1 as $oneRec) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($oneRec, 'customer_cd seko_history_no select_kbn code_kbn kbn_value_cd kbn_value_cd_num
                                                    ', array('code_kbn' => 'trf_code_kbn', 'kbn_value_cd' => 'trf_cd', 'kbn_value_cd_num' => 'trf_kbn'));
            $oneRow['af_msi_no'] = ++$msi_no; // アフターフォロー明細№
            $oneRow['disp_no'] = ++$disp_no1; // 表示順 
            $oneRow['customer_cd'] = $this->_customerCd; // 顧客コード
            $oneRow['seko_history_no'] = $seko_history_no; // 施行履歴№
//            $oneRow['own_kbn'] = 1; // 自社他社区分 
//            $oneRow['af_kbn'] = 1; // アフターフォロー区分 
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_afchk_msi", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($dataCstAfterCol2 as $oneRec) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($oneRec, 'customer_cd seko_history_no select_kbn code_kbn kbn_value_cd kbn_value_cd_num
                                                    ', array('code_kbn' => 'env_code_kbn', 'kbn_value_cd' => 'env_cd', 'kbn_value_cd_num' => 'env_kbn'));
            $oneRow['af_msi_no'] = ++$msi_no; // アフターフォロー明細№
            $oneRow['disp_no'] = ++$disp_no2; // 表示順 
            $oneRow['customer_cd'] = $this->_customerCd; // 顧客コード
            $oneRow['seko_history_no'] = $seko_history_no; // 施行履歴№
//            $oneRow['own_kbn'] = 1; // 自社他社区分 
//            $oneRow['af_kbn'] = 1; // アフターフォロー区分 
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_afchk_msi", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 顧客コード採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/22
     * @param Msi_Sys_Db $db db
     * @return string 顧客コード
     */
    private function getAutocustomerCd($db) {
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $customerCd = App_ClsGetCodeNo::GetCodeNo($db, 'customer_base_info', 'customer_cd', $kijyunYmd);
        return $customerCd;
    }

    /**
     *
     * 依頼書を作成フラグ判定処理
     *
     * <AUTHOR> Sai
     * @since 2015/04/07
     * @param Msi_Sys_Db $db db
     * @param array $dataNiteiCol 
     * @return $flg  0:なし 1:削除のみ 2 作成と削除
     */
    private function checkIfCreateIrai($db, $dataNiteiCol) {
        $flg = 0;
        $nitei_kbn = '7';
        // 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
        $sikijyo = null;
        // 施行日程情報
        foreach ($dataNiteiCol as $oneRow) {
            if ($oneRow['nitei_kbn'] == $nitei_kbn) { // 7:葬儀
                $sikijyo = $oneRow['spot_cd'];
                break;
            }
        }

        $old_sikijyo = null;
        $sqlSelectNitei = "
            SELECT
                sn.spot_cd         
            FROM
                seko_nitei sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY
                sn.nitei_kbn
                ";
        $selectNitei = $db->easySelOne($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
        if (Msi_Sys_Utils::myCount($selectNitei) > 0) {
            $old_sikijyo = $selectNitei['spot_cd'];
        }
        if ($sikijyo != $old_sikijyo) {
            $flg = 1;
            if ($sikijyo == '02') {
                $flg = 2;
            }
        }
        return $flg;
    }

    /**
     *
     * 各種依頼書を作成する
     *
     * <AUTHOR> Sai
     * @since 2015/04/07
     * @param Msi_Sys_Db $db db
     * @param string $ha_rp_kbn 発注書区分
     * @param boolean $createFlg 作成フラグ
     * @return $cnt 処理件数
     */
    private function saveSpecialHachuInfo($db, $ha_rp_kbn, $createFlg) {
        $cnt = 0;
        // 一旦依頼書の発注管理情報を削除する
        if ($createFlg > 0) {
            $cnt = $this->deleteHachuInfoByHarpKbn($db, $ha_rp_kbn);
        }
        //依頼書の発注管理情報を作成する
        if ($createFlg == 2) {
            $cnt = $this->createHachuInfo($db, $ha_rp_kbn);
        }
        return $cnt;
    }

    /**
     *
     * 施行基本フリーを取得する
     *
     * <AUTHOR> Sai
     * @since 2015/08/03
     * @return array 施行基本フリーデータ
     */
    private function getKihonFree() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            skf.seko_no                     -- 施行番号
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD HH24:MI') AS ts_free1     -- 受付日
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD') AS ts_free1_date        -- 受付日日付のみ
            ,TO_CHAR(skf.ts_free1 ,'HH24:MI') AS ts_free1_time           -- 受付日時刻のみ
            ,skf.tanto_cd1                  -- 受付者コード
            ,t1.tanto_nm AS tanto_cd1_nm    -- 受付担当者名
            ,skf.v_free1                    -- 故人様のお名前
            ,skf.v_free2                    -- 故人様のお名前カナ
            ,skf.zip_no1                    -- 現住所郵便番号
            ,skf.addr1_1                    -- 現住所1
            ,skf.addr1_2                    -- 現住所2
            ,skf.tel_no1                    -- 現住所電話番号
            ,skf.free8_code_cd              -- 現住所世帯主コード
            ,skf.free8_kbn                  -- 現住所世帯主区分
            ,TO_CHAR(skf.ts_free5 ,'YYYY/MM/DD HH24:MI') AS ts_free5     -- 死亡日
            ,TO_CHAR(skf.ts_free5 ,'YYYY/MM/DD') AS ts_free5_date        -- 死亡日日付のみ
            ,CASE 
                WHEN skf.free_kbn4 = 1 
                THEN NULL
                ELSE TO_CHAR(skf.ts_free5 ,'HH24:MI')
            END ts_free5_time -- 死亡日時刻のみ
            ,skf.v_free3                    -- 病棟
            ,skf.v_free6                    -- 階
            ,TO_CHAR(skf.ts_free2 ,'YYYY/MM/DD HH24:MI') AS ts_free2     -- お迎え予定時間
            ,TO_CHAR(skf.ts_free2 ,'YYYY/MM/DD') AS ts_free2_date        -- お迎え予定時間日付のみ
            ,TO_CHAR(skf.ts_free2 ,'HH24:MI') AS ts_free2_time           -- お迎え予定時間時刻のみ
            ,TO_CHAR(skf.ts_free3 ,'YYYY/MM/DD HH24:MI') AS ts_free3     -- お迎え予定時間
            ,TO_CHAR(skf.ts_free3 ,'YYYY/MM/DD') AS ts_free3_date        -- お迎え予定時間日付のみ
            ,TO_CHAR(skf.ts_free3 ,'HH24:MI') AS ts_free3_time           -- お迎え予定時間時刻のみ
            ,TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD HH24:MI') AS ts_free4     -- お迎え予定時間
            ,TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD') AS ts_free4_date        -- お迎え予定時間日付のみ
            ,TO_CHAR(skf.ts_free4 ,'HH24:MI') AS ts_free4_time           -- お迎え予定時間時刻のみ
            ,skf.free1_code_cd              -- 亡くなられた場所コード
            ,skf.free1_kbn                  -- 亡くなられた場所区分
            ,skf.free1_place_kbn            -- 亡くなられた場所場所区分
            ,skf.free1_place_nm             -- 亡くなられた場所場所
            ,skf.v_free4                    -- 連絡者お名前
            ,skf.free2_code_cd              -- 連絡者続柄
            ,skf.v_free5                    -- 連絡者携帯番号
        FROM
            seko_kihon_all_free skf
            LEFT OUTER JOIN tanto_mst t1
            ON  (
                    skf.tanto_cd1 = t1.tanto_cd
                AND t1.delete_flg = 0
                )
        WHERE
            skf.seko_no = :seko_no
        AND skf.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 施行基本フリー保存処理
     *
     * <AUTHOR> Sai
     * @since 2015/08/03
     * @param Msi_Sys_Db $db db
     * @param array $dataKihonFree 施行基本フリーデータ
     * @param array $dataSekoKihon 施行基本
     * @return int 更新件数
     */
    private function saveKihonFree($db, $dataKihonFree, $dataSekoKihon) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataKihonFree, 'ts_free1 tanto_cd1 v_free1 v_free2 zip_no1 addr1_1 addr1_2 tel_no1 free8_code_cd free8_kbn    
                                                              ts_free5 v_free3 v_free6 ts_free2 ts_free3 ts_free4 free1_code_cd free1_place_kbn free1_place_nm free1_kbn  
                                                               v_free4 free2_code_cd v_free5');
        $oneRow['ts_free1'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free1']);
        $oneRow['ts_free2'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free2']);
        $oneRow['ts_free3'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free3']);
        $oneRow['ts_free4'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free4']);
        $oneRow['ts_free5'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free5']);
        $oneRow['free1_place_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['free1_place_kbn']);
        $oneRow['free1_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['free1_kbn']);
        $oneRow['tanto_cd2'] = $dataSekoKihon['iso_tanto_cd1_2'];
        $oneRow['tanto_cd3'] = $dataSekoKihon['iso_tanto_cd2_2'];
        //  死亡日が日付のみは1
        if (empty($dataKihonFree['ts_free5_time'])) {
            $oneRow['free_kbn4'] = 1;
        } else {
            $oneRow['free_kbn4'] = null;
        }
        $except = array();
        // 存在チェック
        $select = $this->getKihonFree();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 受注明細取得処理
     *
     * <AUTHOR> Sai
     * @since 2015/11/30
     * @return array 受注明細
     */
    private function getJuchuMsiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $denpyo_no = $this->getJuchudenpyoNo();
        $sql = "
        SELECT
            *
        FROM
            juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
        AND delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('denpyo_no' => $denpyo_no));
        return $select;
    }

    /**
     * 会員情報のプラン値引きが変更されているかチェック処理
     *
     * <AUTHOR> Sai
     * @since 2016/01/15
     * @return boolean
     */
    private function isChangedPlanNebiki($dataGojokaiMemberCol) {
        $old_cnt = $this->getPlanNebikiCount();
        $new_cnt = 0;
        foreach ($dataGojokaiMemberCol as $value) {
            if ($value['yoto_kbn'] == '11') {
                $new_cnt+=1;
            }
        }
        return $old_cnt != $new_cnt;
    }

    /**
     * 互助会の用途がプラン値引きのカウントを取得する
     * <AUTHOR> Sai
     * @since 2016/01/14
     * @return $cnt 
     */
    private function getPlanNebikiCount() {
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = 0;
        $sql = "
        SELECT
            COUNT(sgm.seko_no) AS cnt
        FROM
            seko_gojokai_member sgm
        WHERE
            sgm.seko_no = :seko_no
        AND sgm.yoto_kbn IN (11)
        AND sgm.delete_flg = 0 
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if (count($select) > 0) {
            $cnt = $select['cnt'];
        }
        return $cnt;
    }

    /**
     * 配偶者用顧客基本情報 保存処理 
     *   cf. JuchuCustomerinfo.php saveCstBaseInfo()
     *
     * <AUTHOR> Mihara
     * @since 2016/06/21
     * @param Msi_Sys_Db $db db
     * @param array $dataSk 施行基本情報データ
     * @return int 更新件数
     */
    protected function saveSpouseInfo($db, $dataSk)
    {
        if ( ! Msi_Sys_Utils::arrIsSet($dataSk, 'spouse_name') &&
             ! Msi_Sys_Utils::arrIsSet($dataSk, 'spouse_kana') ) { // 入力データがない
            // 既存データがあれば削除
            $this->deleteSpouseInfo($db, $this->_sekoNo);
            return;
        }

        $oneRow = array();
        $oneRow['customer_nm'] = $dataSk['spouse_name']; // 配偶者名
        $oneRow['customer_knm'] = $dataSk['spouse_kana']; // 配偶者カナ

        $manNeirei = Msi_Sys_Utils::manNenrei($dataSk['sp_gengo'] . $dataSk['sp_seinengappi_ymd']);
        if ( $manNeirei !== null ) { 
            $oneRow['birth_era'] = $dataSk['sp_gengo'];  // 生年月日元号
            $oneRow['birth'] = $dataSk['sp_seinengappi_ymd'];  // 生年月日	
            $oneRow['age'] = $manNeirei; // $dataSk['sp_nenrei_man'];  // 年齢
        } else {
            $oneRow['birth_era'] = null;
            $oneRow['birth'] = null;
            $oneRow['age'] = null;
        }

        $oneRow['customer_kbn_cd'] = '5'; // code_nm_mst.code_kbn(1850) 5(配偶者)
        $oneRow['customer_kbn'] = 5;
        if ( $dataSk['k_sex_cd'] == 1 ) { // 0080(性別) 1(男性)
            $oneRow['zoku_cd'] = '3'; // 続柄名(0190) 3(妻), 4(夫)
            $oneRow['zoku_kbn'] = 3;  // 続柄
        } else if ( $dataSk['k_sex_cd'] == 2 ) { // 0080(性別) 2(女性)
            $oneRow['zoku_cd'] = '4'; // 続柄名(0190) 3(妻), 4(夫)
            $oneRow['zoku_kbn'] = 4;  // 続柄
        }

        $oneRow['zip_no'] = $dataSk['kg_yubin_no']; // 現住所郵便番号
        $oneRow['addr1'] = $dataSk['kg_addr1']; // 現住所1
        $oneRow['addr2'] = $dataSk['kg_addr2']; // 現住所2
        $oneRow['tel_no'] = $dataSk['kg_tel']; // 現住所電話番号

        /* * *
        if ($dataSk['mg_kbn'] == 1) { // 故人に同じ
            $oneRow['zip_no'] = $dataSk['kg_yubin_no']; // 現住所郵便番号
            $oneRow['addr1'] = $dataSk['kg_addr1']; // 現住所1
            $oneRow['addr2'] = $dataSk['kg_addr2']; // 現住所2
            $oneRow['tel_no'] = $dataSk['kg_tel']; // 現住所電話番号
        } else {
            $oneRow['zip_no'] = $dataSk['mg_yubin_no']; // 現住所郵便番号
            $oneRow['addr1'] = $dataSk['mg_addr1']; // 現住所1
            $oneRow['addr2'] = $dataSk['mg_addr2']; // 現住所2
            $oneRow['tel_no'] = $dataSk['mg_tel']; // 現住所電話番号
        }
        $oneRow['mobile_tel'] = $dataSk['mg_m_tel']; // 携帯番号
//        $oneRow['emergency_nm'] = null; // 緊急先名
//        $oneRow['emergency_knm'] = null; // 緊急先名カナ
        $oneRow['em_zoku_cd2'] = $dataSk['m_zoku_cd2']; // 顧客からみた続柄コード
        $oneRow['em_zoku_kbn2'] = $dataSk['m_zoku_kbn2']; // 顧客からみた続柄
//        $oneRow['em_zip_no'] = null; // 緊急先郵便番号
//        $oneRow['em_addr1'] = null; // 緊急先住所1
//        $oneRow['em_addr2'] = null; // 緊急先住所2
//        $oneRow['em_tel'] = null; // 緊急先電話番号
//        $oneRow['em_mobile_tel'] = null; // 緊急先携帯番号
        $oneRow['resident_kbn'] = null; // 現住所に同じ
        if ($dataSk['mg_kbn'] == 1) { // 故人に同じ
            $oneRow['resident_zip_no'] = $dataSk['kg_yubin_no']; // 現住所郵便番号
            $oneRow['resident_addr1'] = $dataSk['kg_addr1']; // 現住所1
            $oneRow['resident_addr2'] = $dataSk['kg_addr2']; // 現住所2
            $oneRow['resident_tel'] = $dataSk['kg_tel']; // 現住所電話番号
        } else {
            $oneRow['resident_zip_no'] = $dataSk['mj_yubin_no']; // 住民登録郵便番号
            $oneRow['resident_addr1'] = $dataSk['mj_addr1']; // 住民登録住所1
            $oneRow['resident_addr2'] = $dataSk['mj_addr2']; // 住民登録住所2
            $oneRow['resident_tel'] = $dataSk['mj_tel']; // 住民登録電話番号
        }
        $oneRow['family_kbn'] = null; // 現住所に同じ
//        $oneRow['family_zip_no'] = $dataSk['mh_yubin_no']; // 住民登録郵便番号
//        $oneRow['family_addr1'] = $dataSk['mh_addr1']; // 住民登録住所1
//        $oneRow['family_addr2'] = $dataSk['mh_addr2']; // 住民登録住所2
        $oneRow['office_kbn'] = Msi_Sys_Utils::emptyToNull($dataSk['mk_kinmusaki_kbn']); // 勤務先区分
        $oneRow['office_nm'] = $dataSk['mk_kinmusaki_nm']; // 勤務先名
        $oneRow['office_post_nm'] = $dataSk['mk_yakusyoku_nm']; // 役職／職種
        $oneRow['office_tel'] = $dataSk['mk_tel']; // 勤務先TEL
        $oneRow['office_fax'] = $dataSk['mk_fax']; // 勤務先FAX
        * * */
        // 存在チェック
        $rec = $this->getSpouseInfo();
        if ($rec === null) {
            $oneRow['customer_cd'] =  $this->getAutoCustomerCd($db);
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_base_info", $oneRow);
        } else {
            // 条件部
            $where['customer_cd'] = $rec['customer_cd']; // $this->_customerCd;  // 顧客コード
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_base_info", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 請求先用顧客基本情報 保存処理 
     *
     * <AUTHOR> Mihara
     * @since 2017/01/06
     * @param Msi_Sys_Db $db db
     * @param array $dataSk 施行基本情報データ
     * @param array $dataSekyuInfo 請求先データ
     * @return int 更新件数
     */
    protected function saveSekyuSakiToCstBase($db, $dataSk, $dataSekyuInfo)
    {
        // 請求先の喪主に同じチェックボックス
        $sekyu_kbn = $dataSk['sekyu_kbn'];
        if ( $sekyu_kbn == 1 || 
             ( strlen($dataSekyuInfo['sekyu_nm']) <= 0 && strlen($dataSekyuInfo['sekyu_knm']) <= 0 ) ) {
            // 既存データがあれば削除
            $this->deleteSekyuSakiFromCstBase($db, $this->_sekoNo);
            return;
        }

        $oneRow = array();
        $oneRow['customer_nm']  = $dataSekyuInfo['sekyu_nm'];
        $oneRow['customer_knm'] = $dataSekyuInfo['sekyu_knm'];

        $oneRow['customer_kbn_cd'] = '3'; // code_nm_mst.code_kbn(1850) 3(施主)
        $oneRow['customer_kbn'] = 3;
        /* if ( $dataSekyuInfo['k_sex_cd'] == 1 ) { // 0080(性別) 1(男性) */
        /*     $oneRow['zoku_cd'] = '3'; // 続柄名(0190) 3(妻), 4(夫) */
        /*     $oneRow['zoku_kbn'] = 3;  // 続柄 */
        /* } else if ( $dataSekyuInfo['k_sex_cd'] == 2 ) { // 0080(性別) 2(女性) */
        /*     $oneRow['zoku_cd'] = '4'; // 続柄名(0190) 3(妻), 4(夫) */
        /*     $oneRow['zoku_kbn'] = 4;  // 続柄 */
        /* } */

        $oneRow['zip_no']     = $dataSekyuInfo['sekyu_yubin_no'];
        $oneRow['addr1']      = $dataSekyuInfo['sekyu_addr1'];
        $oneRow['addr2']      = $dataSekyuInfo['sekyu_addr2'];
        $oneRow['tel_no']     = $dataSekyuInfo['sekyu_tel'];
        $oneRow['mobile_tel'] = $dataSekyuInfo['mobile_tel'];

        $oneRow['sodan_kbn'] = null; // 事前相談有無
        $oneRow['rireki_kbn'] = null; // 施行履歴有無
        if ( $dataSk['moushi_kbn'] == 1 ) { // 申込区分（=1：葬儀）
            $oneRow['rireki_kbn'] = 1; // 施行履歴有無
        } else if ( $dataSk['moushi_kbn'] == 5 ) { // 申込区分（=5：事前相談）
            $oneRow['sodan_kbn'] = 1; // 事前相談有無
        }

        // 存在チェック
        $rec = $this->getSekyuSakiFromCstBase();
        if ($rec === null) {
            $oneRow['customer_cd'] =  $this->getAutoCustomerCd($db);
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_base_info", $oneRow);
        } else {
            // 条件部
            $where['customer_cd'] = $rec['customer_cd']; // $this->_customerCd;  // 顧客コード
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_base_info", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 施行情報履歴テーブル(cs_seko_info_history) 保存処理
     * (customer_cd, seko_no)で特定される cs_seko_info_history を登録・更新する
     *
     * <AUTHOR> Mihara
     * @since 2017/01/06
     * @param Msi_Sys_Db $db db
     * @param string     $customer_cd
     * @param string     $seko_no
     * @return int 更新件数
     */
    protected function saveCsSekoInfoHistory($db, $customer_cd, $seko_no)
    {
        $rec00 = DataMapper_SekoKihonInfo::findOne($db, array('seko_no'=>$seko_no));

        if ( $rec00 === null ) {
            throw new Exception("saveCsSekoInfoHistory: no seko data");
        }

        // Msi_Sys_Utils::debug( 'saveCsSekoInfoHistory=>' .  Msi_Sys_Utils::dump($rec00) );

        $recCsih = Msi_Sys_Utils::remapArrayFlat( $rec00, 'seko_no  moushi_kbn  sougi_ymd  nakijitu',
                                                  array( 'sougi_ymd' => 'funeral_date',
                                                         'nakijitu'  => 'die_date') );
        $recCsih['customer_cd'] = $customer_cd;
        $recCsih['moushi_cd']   = (string)$recCsih['moushi_kbn'];
        $recCsih = Msi_Sys_Utils::emptyToNullArr($recCsih);

        // Msi_Sys_Utils::debug( 'saveCsSekoInfoHistory=>' .  Msi_Sys_Utils::dump($recCsih) );

        DataMapper_Utils::upsertEasy($db, 'cs_seko_info_history', $recCsih);
    }

}
