<?php
  /**
   * PDF 御芳名印刷
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Kayo
   * @since      2016/03/14
   * @filesource 
   */

  /**
   * PDF 御芳名印刷
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Kayo
   * @since      2016/03/14
   */
class Kanri_Pdf0603Controller extends Zend_Controller_Action
{
    private static $title = '生花供物帳';
    private static $sourceFileName1 = 'pdf_tmpl/0603_Hed.pdf';
    private static $sourceFileName2 = 'pdf_tmpl/0603_Msi.pdf';
    
    /** 支払方法区分: 持込 */
    const PAY_KBN_MOTIKOMI = '5';
    /** 支払方法区分: キャンセル */
    const PAY_KBN_CANCEL = '6';
    /** 印刷対象外リスト */
    private static $jogai_list = array(
         self::PAY_KBN_MOTIKOMI
       , self::PAY_KBN_CANCEL
    );

    private static $row_height = 40.6;
    private static $row_count = 10;

	/**
     * アクション
     *
     * <AUTHOR> Kayoo
     * @since 2014/07/09
     */
    public function indexAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

        $prn_ymd = date('Y/m/d');
        $seko_no = null;

	// 印刷年月日
        if (array_key_exists('print_ymd', $dataAppAry) && $dataAppAry['print_ymd'] != '') {
            $prn_ymd = htmlspecialchars($dataAppAry['print_ymd']);
	}
	// 施行番号
        if (array_key_exists('seko_no', $dataAppAry) && $dataAppAry['seko_no'] != '') {
            $seko_no = htmlspecialchars($dataAppAry['seko_no']);
	}

	$db = Msi_Sys_DbManager::getMyDb();

	$pdfObj = new App_Pdf(self::$title);
	$ret = $this->outData($pdfObj, $db, $prn_ymd, $seko_no);
	if ($ret != App_PdfKanriLib::STATUS_OK) {
            App_PdfKanriLib::err($ret);
            return;
	}
	$pdfObj->download();
    }

    /**
     * 御芳名の印刷処理
     * ※請求先名、請求担当者名、名札は★→＿ 
     * 
     * <AUTHOR> Kayo
     * @since 2016/03/14
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $prn_ymd	印刷日付
	 * @param string $seko_no_st 施行番号
     * @return void
     */
    private function outData($pdfObj, $db, $prn_ymd, $seko_no)
    {
        $pdfObj->set_default_font_size(8);
        
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName1);
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        $rec = DataMapper_SekoKihon::find($db,array('seko_no' => $seko_no));   
        if(count($rec) > 0){
            $recKihon = $rec[0];
        }
        
        $headRec = DataMapper_Pdf0601::findHead($db, null, null, $seko_no, $seko_no);
        if (count($headRec) == 0) {
            return App_PdfKanriLib::STATUS_NODATA;
	}
        
        // ロゴ出力
        $rec_kaisya = DataMapper_KaisyaInfo::findOne($db);
        if (isset($rec_kaisya['syamei_logo_img'])) {
            $img = $db->readBlobCont($rec_kaisya['syamei_logo_img']);
            $pdfObj->write_image(array('x' => 300, 'y' => 650, 'width' => 220, 'height' => 50, 'font_size' => 12), $img);
        }

	foreach ($headRec as $head) {
            $seko_no = $head['seko_no'];
            $pdfObj->write_string(array('x' =>  10, 'y' => 500, 'width' => 600, 'height' => 20, 'font_size' => 20, 'align' => 'C'), '故　' . $head['k_nm'] . '　様'); // 故人名

            // 支払方法が「持込」のデータは一番最後に表示させるようにする
            $recMsi = DataMapper_Pdf0601::find($db, array('seko_no' => $seko_no, "__etc_orderby" => array('m_order_no ASC', 'kaishu_kbn ASC', 'k_order_no ASC', 'uri_den_no ASC', 'msi_no ASC')));
            if (count($recMsi) == 0) {
                continue;
            }

            $numPagesCur = $pdfObj->getNumPages();
            // 明細書
            $this->outDataDetail($pdfObj, $db, $recMsi, $recKihon, $numPagesCur);        
        }
    }
    
    /**
     * 明細を出力
     * ※請求先名、請求担当者名、名札は★→＿ 
     * 
     * <AUTHOR> Kayo
     * @since 2015/05/20
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $rec  施行番号
     * @return void
     * @version 2017/06/30 MSI Oka 葬家名追加
     */
    private function outDataDetail($pdfObj, $db, $recMsi, $recKihon, $numPagesCur) {
        $meisai_top          = 44;
        $meisai_row_height   = 108.5;
        $meisai_row_count    = 7;

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);
       
        // 明細金額を集計した配列
        $sumArr = array();
        $set_arr[] = array('x' =>  63, 'y' => $meisai_top,     'width' =>  35, 'height' => 16, 'font_size' => 14, 'type' => 'num');     // №
        $set_arr[] = array('x' => 105, 'y' => $meisai_top,     'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // 御芳名
        $set_arr[] = array('x' =>  32, 'y' => $meisai_top+34,  'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // 品名
        $set_arr[] = array('x' => 430, 'y' => $meisai_top+34,  'width' => 100, 'height' => 16, 'font_size' => 14, 'type' => 'num');     // 金額
        $set_arr[] = array('x' => 445, 'y' => $meisai_top+52,  'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // TEL
        $set_arr[] = array('x' => 125, 'y' => $meisai_top+85,  'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // 請求先名
        $set_arr[] = array('x' => 125, 'y' => $meisai_top+52,  'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // 住所1
        $set_arr[] = array('x' => 125, 'y' => $meisai_top+67,  'width' => 375, 'height' => 16, 'font_size' => 14, 'align' => 'L');      // 住所2
        //$set_arr[] = array('x' => 490, 'y' => $meisai_top+13, 'width' =>  70, 'height' => 16, 'font_size' => 14, 'type' => 'num');   // 数量
        $count = 0;
        
        foreach ($recMsi as $value) {
            ++$count;
            if (strlen(trim($value['nafuda_nm'])) > 0) {
                $nafuda_nm = str_replace('★', '＿', trim($value['nafuda_nm']));
            } else {
                $nafuda_nm = str_replace('★', '＿', trim($value['sekyu_nm'].' '.$value['v_free3']));
            }
            // 割引適応が同業者の場合は住所表記をしない
            if (isset($value['k_free5']) && $value['k_free5'] == '1') {
                $addr1 = null;
                $addr2 = null;
                $tel = null;
                $nm = null;
            } else {
                $addr1 = $value['sekyu_addr1'];
                $addr2 = $value['sekyu_addr2'];
                $tel = $value['sekyu_tel'];
                $nm = str_replace('★', '＿', $value['sekyu_nm'].' '.$value['v_free3'])."　様";
            }
			$Baika_prc =$value['uri_tnk']*$value['juchu_suryo'];
            $zei = App_ClsTaxLib::GetCalcTaxZeiCd($db, $value['zei_cd'], $Baika_prc, $value['zei_kbn']);
            if ($value['zei_kbn'] == 2) {
                $Baika_prc = $zei["KomiPrc"];  // 行外税消費税額
            }

            $row_arr[] = array(
                    $count                  // №
                   ,$nafuda_nm              // 御芳名
                   ,$value['shohin_nm']     // 品名
                   //,$value['juchu_suryo'] // 数量
                   //,$value['uri_tnk']*$value['juchu_suryo'] // 金額
				   ,$Baika_prc	
                   ,$tel // TEL
                   ,$nm // 請求先名
                   ,$addr1 // 住所1
                   ,$addr2 // 住所2
                   );
        }
        $row_count = 1;
        $row_arr_wk = array();
        foreach ($row_arr as $row) {
            if ($row_count % $meisai_row_count == 1) {
                if (count($row_arr_wk) > 0) {
                    $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName2, 1);
                    // 表形式で書き込む
                    $pdfObj->write_table_simple($set_arr, $meisai_row_height, $row_arr_wk);
                }
                $row_arr_wk = array();
            }
            // 値の設定
            $row_arr_wk[] = $row;
            $row_count++;
        }

        //全ページ共通
        if (count($row_arr_wk) > 0) {
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName2, 1);
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            if($i > 0){
                $page = $i + 1;
                $pdfObj->setPage($page);
                $pdfObj->write_string(array('x' => 500, 'y' => 22, 'width' => 60, 'height' =>  15, 'font_size' => 14, 'align' => 'R'), ($page -1)." 頁");
                $pdfObj->write_string(array('x' =>15, 'y' => 22, 'width' => 80, 'height' => 15, 'font_size' => 14, 'align' => 'R'), $recKihon['souke_nm']."　家");
            }
        }
            $pdfObj->write_table_simple($set_arr, $meisai_row_height, $row_arr_wk);
        }       

        return $sumArr;
    }


}
