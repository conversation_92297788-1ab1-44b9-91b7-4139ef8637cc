/** 
 * @fileoverview 施行一覧
 */
$( function() {

    "use strict";

    var utils = window.msiBbUtils;

    var AppModel = Backbone.Model.extend({

        defaults: function() {
            return {
                s_sougi_from: null, // 葬儀日from
                s_sougi_to: null, // 葬儀日to
                s_code_1: null, // 台帳番号1
                s_code_2: null, // 台帳番号2
                s_code_3: null, // 台帳番号3
                s_name_1: null, // お客様名(葬家)
                s_name_2: null, // 故人名
                s_name_3: null, // 喪主名
                s_apply: null, // 申込区分
                s_status_kbn: null, // ステータス
                s_order_flg: null, // 発注完了フラグ
                s_kaijyosiki: null, // 式場(表示用)
                s_staff_2: null, // 施行担当(表示用)
                s_staff_1: null, // 受付担当(表示用)
                s_bumon: null, // 部門(表示用)
                s_kaijyosiki_cd: null, // 式場
                s_staff_2_cd: null, // 施行担当
                s_staff_1_cd: null, // 受付担当
                s_bumon_cd: null, // 部門
                s_number: null, // 施行No
                seko_no_selected: null, // 選択された施行No
            };
        },

        validation: {
            s_sougi_from: {
                required: false,
                pattern: 'ymd',
            },
            s_sougi_to: {
                required: false,
                pattern: 'ymd',
            },
        },

        labels: {
            s_sougi_from: '葬儀日from',
            s_sougi_to: '葬儀日to'
        },

    }); // AppModel

    var vOpt = {

        el: '#my-form-id',

        events: {
            "click #btn_search": "doSearch",
            "click #btn_clear": "doClear",
            "click #s_kaijyosiki,.s_kaijyosiki-ref": "kaijyoHelper",
            "click #s_bumon,.s_bumon-ref": "bumonHelper",
            "click #s_staff_2,.s_staff_2-ref": "staff2Helper",
            "click #s_staff_1,.s_staff_1-ref": "staff1Helper",
            "click .s_number-ref": "sekoNoHelper",
            "click #btn_hachu": "doShowHachu",

            // "click #btn_save": "doSave",
            // "click #btn_copy": "doCopy", // XXX
            // "click #btn_delete": "doDelete",
            // "click #btn_new": "doNew",
            // "click #btn_cancel": "doCancel",
            // "click #btn_check": "doCheck",
            // "click #btn_clear_err": "doClearErr",
            // "click #btn_print": "doPrint",
        },

        initialize: function() {

            Backbone.Validation.bind(this);

            this.listenTo(this.model, 'change', this.render);
            this.listenTo(this.model, 'change:seko_no_selected', this.renderBtn);

            this.render();
            this.renderBtn();
        },

        render: function() {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind( this, Backbone.Validation.msi_err_setting_std() );

            this.stickit();
            
            // スクロール調整
            this.scrollAdj();

            // select2 調整。XXX 
            this.select2Adj(); // patch

            return this;
        },

        renderBtn: function() {
            var $seko_no_selected = this.model.get('seko_no_selected');
            if ( $seko_no_selected ) {
                this.btnEnabled('.sekolist-cmd-buttons input[type="button"]');
            } else {
                this.btnDisabled('.sekolist-cmd-buttons input[type="button"]');
            }
        },

        // スクロールバー表示調整
        scrollAdj: function() {
            var $list = this.$('.result-list .list'),
                $header = this.$('.result-list .header'),
                sc_of,
                sc_w,
                hh,
                cont_h = $('#order').height(),
                src_h = this.$('.search').height(),
                adj_h = 220, // button, etc.
                my_h;
            my_h = cont_h - src_h - adj_h;
            // console.log( 'height=>' + [cont_h,src_h,adj_h].join(', ') + ' my_h=>' + my_h );

            $list.height( my_h );

            // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
            if ( $list[0].scrollHeight === $list[0].clientHeight ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
            } else {
                sc_of = 'scroll';
                hh = $header.height();
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome. XXX
            }
        },

        // select2 調整
        select2Adj: function() {
            var bbv = this;
            _.each( 's_apply s_status_kbn s_order_flg'.split(/\s+/),
                    function(k) {
                        var $el = bbv.$('#' + k);
                        var $s2el = bbv.$('#s2id_' + k);
                        if ( $s2el.length ) {
                            $el.select2("val", bbv.model.get(k));
                        }
                    } );
        },

        // エラー表示をクリア
        clearErr: function() {
            this.$el.msiErrClearAll();
        },

        // ボタン非活性化
        btnDisabled: function(elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },

        // ボタン活性化
        btnEnabled: function(elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },

        // 入力チェック
        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                    // console.log( '*** err ' + k + ': ' + v );
                } );
            }

            // NG
            if ( aMsg.length > 0 ) {
                // no alert component        msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },

        // 変更可否
        isChanged: function() {
            return false;
            // if ( !orgDataApp || $.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) ) {
            //     return false;
            // }
            // return true;
        },

        // 検索処理
        // isPreserveSekoNo(施行番号チェックを保存(再現)する)機能は抑制しています
        //
        searchFunc: function(isPreserveSekoNo) {
            // jQuery.Event が isPreserveSekoNo に設定されている場合がある
            if ( isPreserveSekoNo === undefined || _.isObject(isPreserveSekoNo) ) isPreserveSekoNo = false;
            isPreserveSekoNo = false; // isPreserveSekoNo 機能は抑制しています
            if ( ! this.isInputOk() ) {
                return;
            }

            // 選択済み施行Noをクリア
            if ( ! isPreserveSekoNo ) {
                this.selectFunc(null);
            }

            // var dataAppJson = JSON.stringify(this.model.toJSON());
            var bbm = this.model;
            $.ajax( {
                url: $.msiJqlib.baseUrl() + '/hachu/hachulist12/search',
                type: 'GET',
                data: {
                    s_code_1:  bbm.get('s_code_1'),
                    s_code_2:  bbm.get('s_code_2'),
                    s_code_3:  bbm.get('s_code_3'),
                    s_apply:   bbm.get('s_apply'),
                    s_status_kbn:   bbm.get('s_status_kbn'),
                    s_order_flg:   bbm.get('s_order_flg'),
                    // s_ontime:  bbm.get('s_ontime:checked').val(),
                    s_name_1:  bbm.get('s_name_1'),
                    s_name_2:  bbm.get('s_name_2'),
                    s_name_3:  bbm.get('s_name_3'),
                    s_bumon:  bbm.get('s_bumon_cd'),
                    s_kaijyosiki: bbm.get('s_kaijyosiki_cd'),
                    s_staff_2: bbm.get('s_staff_2_cd'),
                    s_staff_1: bbm.get('s_staff_1_cd'),
                    // s_staff_1: bbm.get('s_staff_1').val(), // 受付担当
                    s_sougi_from: bbm.get('s_sougi_from'), // 葬儀日from
                    s_sougi_to: bbm.get('s_sougi_to'), // 葬儀日to
                    s_number:   bbm.get('s_number'),
                },
                dataType: 'html',
                success: function(html) {
                    // console.log( html );
                    $('#order .result-list').replaceWith(html);
                    // console.log( '*** seko_no_selected=>' + app.model.get('seko_no_selected') );
                    app.selectFunc( app.model.get('seko_no_selected'), false );
                    app.model.trigger('change'); // render
                }
                // error処理は共通設定を使う
            } );
        },

        // 検索実行
        doSearch: function(isPreserveSekoNo) {
            this.searchFunc(isPreserveSekoNo);
        },

        // クリア
        doClear: function() {

            _resetData( orgDataApp );

            // 明細行をクリア
            $('#order .result-list .list').empty();

            app.render();
        },

        // 施行選択
        selectFunc: function(seko_no, isToggle) {
            if ( isToggle === undefined ) isToggle =true;

            var old_seko_no = this.model.get('seko_no_selected'),
                $tr = this.$('.result-list .list table').find( 'tr[data-seko_no="' + seko_no + '"]' );

            this.$('.result-list .list table').find( 'tr' ).removeClass('row-selected');

            // console.log( '**** selectFunc seko_no=>' + seko_no + ' old=>' + old_seko_no + ' $tr.length=>' + $tr.length );

            if ( $tr.length < 1 || seko_no == null ||
                 (isToggle && old_seko_no == seko_no)  ) { // 選択済みを未選択に
                this.model.set('seko_no_selected', null);
            } else {
                this.model.set('seko_no_selected', seko_no);
                $tr.addClass('row-selected');
                // $tr.focus();  // ??? scroll?
            }
            return;
        },

        // 他画面参照
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { //console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },

        // 発注内容確認
        doShowHachu: function() {
            var seko_no = this.model.get('seko_no_selected');
            if ( ! seko_no ) return;
            // console.log( '**** 発注内容確認 seko_no=>' + seko_no );

            this._showNew( '/hachu/hachushori/hachusholist/sn/' + seko_no );
        },

        // dbl click 起動画面
        doShowDefault: function() {
            this.doShowHachu();
        },

        // 会場 pickup
        kaijyoHelper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyosiki',
                onSelect: function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_kaijyosiki_cd', data.code);
                    bbm.set('s_kaijyosiki', data.name);
                },
                onClear: function() {
                    bbm.set('s_kaijyosiki_cd', null);
                    bbm.set('s_kaijyosiki', null);
                },
            });
        },

        // 部門 pickup
        bumonHelper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'bumon',
                onSelect: function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_bumon_cd', data.code);
                    bbm.set('s_bumon', data.name.trim());
                },
                onClear: function() {
                    bbm.set('s_bumon_cd', null);
                    bbm.set('s_bumon', null);
                },
                hookSetData: function() {
                    return {
                        s_bumon_kbn: '1,2',
                    }
                },
            });
        },

        // 施行担当 pickup
        staff2Helper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_staff_2_cd', data.code);
                    bbm.set('s_staff_2', data.name);
                },
                onClear: function() {
                    bbm.set('s_staff_2_cd', null);
                    bbm.set('s_staff_2', null);
                },
                hookSetData: function() {
                    // return {};
                    return { s_bumon:'' }; // 部門を指定しない場合
                },
            });
        },

        // 受付担当 pickup
        staff1Helper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_staff_1_cd', data.code);
                    bbm.set('s_staff_1', data.name);
                },
                onClear: function() {
                    bbm.set('s_staff_1_cd', null);
                    bbm.set('s_staff_1', null);
                },
                hookSetData: function() {
                    // return {};
                    return { s_bumon:'' }; // 部門を指定しない場合
                },
            });
        },

        // 施行No pickup
        sekoNoHelper: function() {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_number', data.seko_no);
                } );
        },

        bindings: {
            '#s_apply': {
                observe: 's_apply',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '#s_status_kbn': {
                observe: 's_status_kbn',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '#s_order_flg': {
                observe: 's_order_flg',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '#s_sougi_from': 's_sougi_from',
            '#s_sougi_to': 's_sougi_to',
            '#s_code_1': 's_code_1',
            '#s_code_2': 's_code_2',
            '#s_code_3': 's_code_3',
            '#s_name_1': {
                observe: 's_name_1',
                events: ['change'],
            },                
            '#s_name_2': {
                observe: 's_name_2',
                events: ['change'],
            },                
            '#s_name_3': {
                observe: 's_name_3',
                events: ['change'],
            },                
            '#s_kaijyosiki': 's_kaijyosiki',
            '#s_kaijyosiki_cd': 's_kaijyosiki_cd',
            '#s_bumon': 's_bumon',
            '#s_bumon_cd': 's_bumon_cd',
            '#s_staff_2': 's_staff_2',
            '#s_staff_2_cd': 's_staff_2_cd',
            '#s_staff_1': 's_staff_1',
            '#s_staff_1_cd': 's_staff_1_cd',
            '#s_number': 's_number',
        },
    };

    // 追加設定があれば設定. (ex. seikyu.sekolist.ex.js)
    if ( msiGlobalObj.myAppViewOptExFunc ) {
        msiGlobalObj.myAppViewOptExFunc( vOpt );
    }

    var AppView = Backbone.View.extend( vOpt );

    var app,
        orgDataApp = {},
        _resetData,
        _setInitData;

    app = new AppView( { model: new AppModel } );

    _resetData = function( myApp ) {
        app.model.set( myApp );
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();
        app.model.trigger('change');
    };

    _setInitData = function() {
        var mydata = msiLib2.getJsonFromHtml( $('#my-data-init-id') );

        if ( mydata && mydata.dataApp ) {
            _resetData( mydata.dataApp );
        }
    };

    // msiパーツの有効化 
    msiLib2.msiPrepareParts('#order');

    // 各種イベント設定
    $('#order').on( 'mouseover',
                    '.result-list-sel',
                    function() {
                        var seko_no = $(this).attr('data-seko_no');
                        $('.result-list-sel').find('td').removeClass('my-hover');
                        $(".seko_no_" + seko_no).find('td').addClass('my-hover')
                            .end().find('a').focus();
                    } )
        .on( 'mouseout', '.result-list-sel',
             function() {
                 var seko_no = $(this).attr('data-seko_no');
                 $(".seko_no_" + seko_no).find('td').removeClass('my-hover');
             } )
        // .on( 'click', '#btn_search', searchFunc )
        .on( 'keydown', '.search',
             function(e) {
                 // console.log( 'keydown e.keyCode=>' + e.keyCode );
                 if (e.keyCode != 13) return; // enter
                 // e.stopPropagation();
                 setTimeout( function() { app.searchFunc(); }, 0 );
             } )
        .on( 'focus', '.result-list-sel',
             function(e) {
                 var focus_id = $(e.target).attr('id'), seko_no;
                 // console.log( '**** focus id=>' + focus_id );
                 $('.result-list-sel').find('td').removeClass('my-hover');
                 if ( focus_id && focus_id.indexOf('a_seko_no_') === 0 ) {
                     seko_no = focus_id.substr(10);
                     $(".seko_no_" + seko_no).find('td').addClass('my-hover');
                 }
             } )
        .on( 'focusout', '.result-list-sel',
             function(e) {
                 var focus_id = $(e.target).attr('id'), seko_no;
                 // console.log( '**** focusout id=>' + focus_id );
                 if ( focus_id && focus_id.indexOf('a_seko_no_') === 0 ) {
                     seko_no = focus_id.substr(10);
                     $(".seko_no_" + seko_no).find('td').removeClass('my-hover');
                 }
             } )
        .on( 'click', '.result-list-sel',
             function(e) {
                 var focus_id = $(':focus').attr('id'), seko_no;
                 if ( focus_id && focus_id.indexOf('a_seko_no_') === 0 ) {
                     seko_no = focus_id.substr(10);
                     setTimeout( function() { app.selectFunc(seko_no); }, 0 );
                     return;
                 }
                 seko_no = $(this).attr('data-seko_no');
                 setTimeout( function() { app.selectFunc(seko_no); }, 0 );
             } )
        .on( 'dblclick', '.result-list-sel',
             function(e) {
                 var focus_id = $(':focus').attr('id'), seko_no;
                 if ( focus_id && focus_id.indexOf('a_seko_no_') === 0 ) {
                     seko_no = focus_id.substr(10);
                     setTimeout( function() { app.selectFunc(seko_no); app.doShowDefault(); }, 0 );
                     return;
                 }
                 seko_no = $(this).attr('data-seko_no');
                 setTimeout( function() { app.selectFunc(seko_no); app.doShowDefault(); }, 0 );
             } );


    // 上下矢印 移動処理
    var upDownArrowClick = function (e) {
        if ( msiLib2.isPickerOpen() ) return; // helper が表示されている場合は処理しない

        var $tgt, seko_no, isUp, $els, i, $e, e_seko_no, bgn, end, step;
        // 38(up arrow) 40(down arrow)
        if      (e.keyCode == 38) { isUp = true; }
        else if (e.keyCode == 40) { isUp = false;}
        else                      { return; }

        $tgt = $('tr.result-list-sel :focus');
        if ( $tgt.length ) {
            seko_no = $tgt.attr('id').substr(10); // a_seko_no_*
            // console.log( 'upDownArrowClick **** seko_no=>' + seko_no );
            if ( seko_no ) {
                $els = isUp ? $tgt.closest('tr').prevAll() : $tgt.closest('tr').nextAll();
                $els.each( function() {
                    $e = $(this), e_seko_no = $e.attr('data-seko_no');
                    if ( e_seko_no != seko_no ) {
                        if ( $e.find('a') ) {
                            // console.log( 'upDownArrowClick e_seko_no=>' + e_seko_no );
                            e.stopImmediatePropagation();
                            $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                            // $e.trigger('mouseover');
                            return false;
                        }
                    }
                } );
            }
        } else { // focus がないとき
            $els = $('tr.result-list-sel');
            if ( $els.length ) {
                if ( $els.length === 1 ) {
                    $i = 0;
                    $e = $els.eq(i);
                    e_seko_no = $e.attr('data-seko_no');
                    if ( e_seko_no && $e.find('a') ) {
                        e.stopImmediatePropagation();
                        $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                        return false;
                    }
                    return;
                }
                if ( isUp ) {
                    bgn = $els.length-1, end = 0, step = -1;
                } else {
                    bgn = 0, end = $els.length-1, step = 1;
                }
                for ( i = bgn ; i != end ; i = i+step )  {
                    $e = $els.eq(i);
                    e_seko_no = $e.attr('data-seko_no');
                    if ( e_seko_no && $e.find('a') ) {
                        e.stopImmediatePropagation();
                        $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                        return false;
                    }
                }
            }
        }
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function() {
        if ( app.isChanged() ) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on( 'resize', function() { app.render(); } );

    // 広域イベント捕捉
    $(document).on( 'keydown', upDownArrowClick );

    // 初期フォーカス設定
    // setTimeout( function() {$('#s_number').focus();}, 0 );


    $.msiJqlib.initDone( function() { _setInitData(); } ); // 処理完了

    // $('#order').fadeIn('fast'); // ちらつきのごまかし

    // 大域変数に設定. 他の共用プログラムで使う(eg. saiken/seikyusyo/seikyulist)
    msiGlobalObj.myAppView = app;

} );
