<?php

/**
 * Hachu_HenreidataController
 *
 * 返礼品データ抽出ダウンロード　コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Kayo
 * @since      2017/05/31
 * @filesource 
 */

/**
 * 返礼品データ抽出ダウンロード コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Kayo
 * @since      2017/05/31
 */
class Hachu_HenreidataController extends Msi_Zend_Controller_Action {
    private static $title = '返礼品データ';

	/**
     * 返礼品データ抽出ダウンロード コントローラクラス(ハートフーズ)
     *
     * @category   App
     * @package    controller\kanri
     * <AUTHOR> Kayo
     * @since      2017/05/31
     */
    public function downloadfAction() {
        $params  = Msi_Sys_Utils::webInputs();
        //Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        $db = Msi_Sys_DbManager::getMyDb();

		$_dataApp = $this->_defaultDataApp($db);
		$_dataApp['download_mode'] = 1;	// ハートフーズ
		$data = array( 
					  'dataApp' => $_dataApp,
					  'status' => 'OK',
					  'msg' => ''
					   );

        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        App_Smarty::pushCssFile( ['app/hachu.henreidata.css'] );
        App_Smarty::pushJsFile( ['app/hachu.henreidata.js'] );
	}	
	/**
     * 返礼品データ抽出ダウンロード コントローラクラス
     *
     * @category   App
     * @package    controller\kanri
     * <AUTHOR> Kayo
     * @since      2017/05/31
     */
    public function downloadAction() {
        $params  = Msi_Sys_Utils::webInputs();
        //Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        $db = Msi_Sys_DbManager::getMyDb();

		$_dataApp = $this->_defaultDataApp($db);
		$_dataApp['download_mode'] = 2;	// 森エンター

		$data = array( 
					  'dataApp' => $_dataApp,
					  'status' => 'OK',
					  'msg' => ''
					   );

        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        App_Smarty::pushCssFile( ['app/hachu.henreidata.css'] );
        App_Smarty::pushJsFile( ['app/hachu.henreidata.js'] );
    }

     /**
     * 初期値を返す
     *
     * <AUTHOR> Kayo
     * @since  2017/05/31
     * @param      Msi_Sys_Db $db	データベース
     */
	protected function _defaultDataApp($db)
    {
		
		$dataApp = array(
                         'taisho_st_ymd'    => Msi_Sys_Utils::getDate(strtotime("-1 day")),		// 対象年月日（自）
                         'taisho_ed_ymd'    => Msi_Sys_Utils::getDate(strtotime("-1 day")),        // 対象年月日（至）
            );
        return $dataApp;
    }
    
    /**
     * 返礼品データCSV作成   アクション
     *
     * <AUTHOR> Kayo
     * @since 2017/05/31
     */
    public function makeAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
		$dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $ymd_st = null;
        $ymd_ed = null;

        // 対象期間（自）
        $ymd_st = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dataAppAry['taisho_st_ymd']), 'DATE2' );
        if ( strlen($ymd_st) <= 0 ) {
            throw new Msi_Sys_Exception_InputException( "対象期間（自）は必須です。");
        }
        // 対象期間（至）
        $ymd_ed = Msi_Sys_Utils::checkVar( Msi_Sys_Utils::emptyToNull($dataAppAry['taisho_ed_ymd']), 'DATE2' );
        if ( strlen($ymd_ed) <= 0 ) {
            throw new Msi_Sys_Exception_InputException( "対象期間（至）は必須です。");
        }
		$download_mode = $dataAppAry['download_mode'];
        
        $db = Msi_Sys_DbManager::getMyDb();
        $csvData = $this->getHenrei_Data_Csv($db, $ymd_st, $ymd_ed, $download_mode);
        if (!isset($csvData)) {
            App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
            return;
        }
        // 会社コードを取得する
        if ( !App_Utils::isFukusuKaisyaKanri() ) {
			$kaisya = DataMapper_PdfCommon::getKaisya($db, array('kaisya_cd', 'kaisya_snm'));
		} else {
            $kaisyacd = App_Utils::getCtxtKaisyaEasy();
			$kaisya = DataMapper_PdfCommon::getKaisyaEx($db, array('kaisya_cd', 'kaisya_snm'), $kaisyacd);
		}	
		if (!isset($kaisya)) {
			return;
		}
        $file_nm = $kaisya['kaisya_snm'] . str_replace('/', '', $ymd_ed);
        $buf = Msi_Sys_Utils::csvOutString($csvData);
        Msi_Sys_Utils::out2way1( $buf, self::$title. $file_nm . '.csv', 'text/csv' );
    }

    /**
     * 返礼品データCSV編集
     *
     * <AUTHOR> Kayo
     * @since      2017/05/31
     * @param      db   $db
     * @param      string   $ymd_st 集計開始日
     * @param      string   $ymd_ed 集計終了日
     * @param      string   $download_mode 1:ハートフーズ 2:森エンター
     * @return     array   データ一式
     */
    private function getHenrei_Data_Csv($db, $ymd_st, $ymd_ed, $download_mode)
    {
       $kaisyacd = App_Utils::getCtxtKaisyaEasy();
	   if ($download_mode == 1)	{	// ハートフーズ
		   $where1 = "AND hd.siire_cd IN ('0000060','0000070','0000075')"; 
		   $where2 = "AND hm.siire_cd IN ('0000060','0000070','0000075')"; 
	   } else {		// 株式会社モリ・エンタープライズ
		   $where1 = "AND hd.siire_cd IN ('0001170','0001175')"; 
		   $where2 = "AND hm.siire_cd IN ('0001170','0001175')"; 
	   }
       $select = $db->easySelect( <<< END_OF_SQL
SELECT DISTINCT * FROM (               
SELECT
     sk.moushi_kbn                          AS  申込区分           
    ,sk.daicho_no_eria                      AS  受付No
    ,sk_c.daicho_no_eria                    AS  引継受付番号           
    ,TO_CHAR(snit.nitei_ymd,'YYYYMMDD')     AS  通夜日
    ,CASE WHEN sk.moushi_kbn = 2 THEN
        TO_CHAR(snih.nitei_ymd,'YYYYMMDD')
     ELSE
        TO_CHAR(snis.nitei_ymd,'YYYYMMDD')
     END                                    AS  葬儀日
    ,sk.souke_nm || ' 家'                   AS  葬家
    ,sk.m_nm                                AS  喪主名
    ,sk.m_knm                               AS  喪主名カナ
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_yubin_no
        ELSE
            sk.mg_yubin_no
        END                                 AS  郵便番号
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr1,'')            
        ELSE
            COALESCE(sk.mg_addr1,'')
        END                                 AS  住所1
    ,CAST(CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr2,'')
        ELSE
            COALESCE(sk.mg_addr2,'')
        END AS TEXT)                       AS  住所2
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_tel
        ELSE
            sk.mg_tel
        END                                 AS  電話番号
    ,sk.k_nm                                AS  故人名
    ,sk.k_knm                               AS  故人名カナ
    ,sk.m_zoku_nm                           AS  続柄
    ,bm.bumon_lnm                           AS  依頼元担当部門           
    ,stm.tanto_nm                           AS  依頼元打合せ担当者名
    ,CASE WHEN sk.moushi_kbn = 2 THEN 
        CASE WHEN snih.basho_kbn = 0 THEN 
            '自宅'
        ELSE snih.basho_nm  END
     ELSE    
        CASE WHEN snis.basho_kbn = 0 THEN 
            '自宅'
        ELSE snis.basho_nm  END
     END                                    AS  葬儀場所
   ,COALESCE(hm_j.reijo_suryo,0)            AS  礼状枚数
   ,hm.msi_no                               AS  商品行番号
   ,hm.shohin_cd                            AS  商品コード            
   ,hm.shohin_nm                            AS  商品名
   ,hm.jyodai_tnk                           AS  単価                         
   ,hm.hachu_suryo                          AS  数量
   ,hm.dai_bunrui_cd || ':' || stbm.dai_bunrui_nm    AS 大分類名
   ,NULL AS 送付先名
   ,NULL AS 送付先郵便番号
   ,NULL AS 送付先住所1
   ,NULL AS 送付先住所2
   ,NULL AS 送付先電話番号
FROM    hachu_denpyo	hd
LEFT JOIN hachu_denpyo_msi hm
    ON hd.denpyo_no = hm.denpyo_no
    AND 0           = hm.delete_flg
LEFT JOIN 
    (SELECT hm.denpyo_no, sum(hm.hachu_suryo * COALESCE(sm.irisu,1)) AS    reijo_suryo
        FROM hachu_denpyo_msi hm
		LEFT JOIN shohin_mst sm		-- 商品マスタ	-- 2020/03/29 ADD Kayo
			  ON hm.kaisya_cd			= sm.kaisya_cd 
			  AND hm.shohin_bumon_cd	= sm.bumon_cd 
			  AND hm.shohin_cd			= sm.shohin_cd
			  AND 0						= sm.delete_flg	
        WHERE hm.delete_flg = 0
        AND  hm.chu_bunrui_cd = '0015'
        AND  hm.shohin_kbn    = '3005'    
        GROUP BY hm.denpyo_no) hm_j
    ON hd.denpyo_no = hm_j.denpyo_no
LEFT JOIN seko_hachu_info shi
    ON  hm.denpyo_no = shi.hc_denpyo_no
    AND hm.hachu_no  = shi.hachu_no
    AND 0            = shi.delete_flg
LEFT JOIN seko_kihon_info sk
    ON shi.seko_no   = sk.seko_no
    AND 0            = sk.delete_flg
LEFT JOIN seko_kihon_info sk_c              -- キャンセル時の施行番号
    ON sk.v_free16  = sk_c.seko_no
    AND 0           = sk_c.delete_flg
LEFT	JOIN	seko_nitei  snis       -- 葬儀会場名
    ON  sk.seko_no          =   snis.seko_no
    AND 11                  =   snis.nitei_kbn
    AND 0                   =   snis.delete_flg
LEFT	JOIN	seko_nitei_houji  snih -- 葬儀会場名（法事）
    ON  sk.seko_no          =   snih.seko_no
    AND 1                   =   snih.nitei_kbn
    AND 0                   =   snih.delete_flg
LEFT	JOIN	seko_nitei  snit       -- 通夜
    ON  sk.seko_no          =   snit.seko_no
    AND 4                   =   snit.nitei_kbn
    AND 0                   =   snit.delete_flg
LEFT	JOIN	bumon_mst	bm
    ON	sk.bumon_cd 	=	bm.bumon_cd
    AND 0				=	bm.delete_flg
LEFT	JOIN	tanto_mst stm
    ON	sk.uketuke_tanto_cd	=	stm.tanto_cd
    AND 0					=	stm.delete_flg
LEFT	JOIN	shohin_dai_bunrui_mst stbm
    ON	stbm.dai_bunrui_cd =hm.dai_bunrui_cd
    AND 0 = stbm.delete_flg
WHERE   TO_CHAR(hd.order_ymd, 'YYYY/MM/DD') >= :st_ymd
AND     TO_CHAR(hd.order_ymd, 'YYYY/MM/DD') <= :ed_ymd
AND     hd. kaisya_cd = :kaisya_cd              
AND     hd.delete_flg = 0
AND     hm.dai_bunrui_cd IN ('0020','0025')
AND     sk.daicho_no_eria IS NOT NULL
$where1			   
UNION ALL  -- 生花返礼品              
SELECT 
     sk.moushi_kbn                          AS  申込区分           
    ,sk.daicho_no_eria                      AS  受付No
    ,sk_c.daicho_no_eria                    AS  引継受付番号           
    ,TO_CHAR(snit.nitei_ymd,'YYYYMMDD')     AS  通夜日
    ,CASE WHEN sk.moushi_kbn = 2 THEN
        TO_CHAR(snih.nitei_ymd,'YYYYMMDD')
     ELSE
        TO_CHAR(snis.nitei_ymd,'YYYYMMDD')
     END                                    AS  葬儀日
    ,sk.souke_nm || ' 家'                   AS  葬家
    ,sk.m_nm                                AS  喪主名
    ,sk.m_knm                               AS  喪主名カナ
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_yubin_no
        ELSE
            sk.mg_yubin_no
        END                                 AS  郵便番号
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr1,'')            
        ELSE
            COALESCE(sk.mg_addr1,'')
        END                                 AS  住所1
    ,CAST(CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr2,'')
        ELSE
            COALESCE(sk.mg_addr2,'')
        END AS TEXT)                       AS  住所2
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_tel
        ELSE
            sk.mg_tel
        END                                 AS  電話番号
    ,sk.k_nm                                AS  故人名
    ,sk.k_knm                               AS  故人名カナ
    ,sk.m_zoku_nm                           AS  続柄
    ,bm.bumon_lnm                           AS  依頼元担当部門           
    ,stm.tanto_nm                           AS  依頼元打合せ担当者名
    ,CASE WHEN sk.moushi_kbn = 2 THEN 
        CASE WHEN snih.basho_kbn = 0 THEN 
            '自宅'
        ELSE snih.basho_nm  END
     ELSE    
        CASE WHEN snis.basho_kbn = 0 THEN 
            '自宅'
        ELSE snis.basho_nm  END
     END                                    AS  葬儀場所
   ,COALESCE(hm_j.reijo_suryo,0)            AS  礼状枚数
   ,hm.msi_no                               AS  商品行番号
   ,hm.shohin_cd                            AS  商品コード            
   ,hm.shohin_nm                            AS  商品名
   ,hm.uri_tnk                              AS  単価                         
   ,hm.juchu_suryo                          AS  数量
   ,hm.dai_bunrui_cd || ':' || stbm.dai_bunrui_nm    AS 大分類名
   ,COALESCE(hm.v_free2 || ' ', '') || COALESCE(hm.v_free3, '')    AS 送付先名
   ,hm.nonyu_yubin_no AS 送付先郵便番号
   ,hm.nonyu_addr1 AS 送付先住所1
   ,hm.nonyu_addr2 AS 送付先住所2
   ,hm.nonyu_tel AS 送付先電話番号
FROM    uriage_denpyo	hd
LEFT JOIN uriage_denpyo_msi hm
    ON hd.uri_den_no= hm.uri_den_no
    AND 0           = hm.delete_flg
LEFT JOIN 
    (SELECT uri_den_no, sum(um.juchu_suryo * COALESCE(sm.irisu,1)) AS    reijo_suryo
        FROM uriage_denpyo_msi um
		LEFT JOIN shohin_mst sm		-- 商品マスタ	-- 2020/03/29 ADD Kayo
			  ON um.kaisya_cd			= sm.kaisya_cd 
			  AND um.shohin_bumon_cd	= sm.bumon_cd 
			  AND um.shohin_cd			= sm.shohin_cd
			  AND 0						= sm.delete_flg	
        WHERE um.delete_flg = 0
        AND  um.chu_bunrui_cd = '0015'
        AND  um.shohin_kbn    = '3005'    
        GROUP BY um.uri_den_no) hm_j
    ON hd.uri_den_no = hm_j.uri_den_no
LEFT JOIN seko_kihon_info sk
    ON hd.seko_no    = sk.seko_no
    AND 0            = sk.delete_flg
LEFT JOIN seko_kihon_info sk_c              -- キャンセル時の施行番号
    ON sk.v_free16  = sk_c.seko_no
    AND 0           = sk_c.delete_flg
LEFT	JOIN	seko_nitei  snis       -- 葬儀会場名
    ON  sk.seko_no          =   snis.seko_no
    AND 11                  =   snis.nitei_kbn
    AND 0                   =   snis.delete_flg
LEFT	JOIN	seko_nitei_houji  snih -- 葬儀会場名（法事）
    ON  sk.seko_no          =   snih.seko_no
    AND 1                   =   snih.nitei_kbn
    AND 0                   =   snih.delete_flg
LEFT	JOIN	seko_nitei  snit       -- 通夜
    ON  sk.seko_no          =   snit.seko_no
    AND 4                   =   snit.nitei_kbn
    AND 0                   =   snit.delete_flg
LEFT	JOIN	bumon_mst	bm
    ON	sk.bumon_cd 	=	bm.bumon_cd
    AND 0				=	bm.delete_flg
LEFT	JOIN	tanto_mst stm
    ON	sk.uketuke_tanto_cd	=	stm.tanto_cd
    AND 0					=	stm.delete_flg
LEFT	JOIN	shohin_dai_bunrui_mst stbm
    ON	stbm.dai_bunrui_cd =hm.dai_bunrui_cd
    AND 0 = stbm.delete_flg
WHERE   TO_CHAR(hm.ts_free1, 'YYYY/MM/DD') >= :st_ymd
AND     TO_CHAR(hm.ts_free1, 'YYYY/MM/DD') <= :ed_ymd
AND     hd. kaisya_cd   = :kaisya_cd              
AND     hd.delete_flg   = 0
AND     hd.data_kbn     IN (1,2)
AND     hm.dai_bunrui_cd IN ('0025')
AND     sk.daicho_no_eria IS NOT NULL
$where2			   
-- 礼状枚数のみ
UNION ALL
SELECT
     sk.moushi_kbn                          AS  申込区分           
    ,sk.daicho_no_eria                      AS  受付No
    ,sk_c.daicho_no_eria                    AS  引継受付番号           
    ,TO_CHAR(snit.nitei_ymd,'YYYYMMDD')     AS  通夜日
    ,CASE WHEN sk.moushi_kbn = 2 THEN
        TO_CHAR(snih.nitei_ymd,'YYYYMMDD')
     ELSE
        TO_CHAR(snis.nitei_ymd,'YYYYMMDD')
     END                                    AS  葬儀日
    ,sk.souke_nm || ' 家'                   AS  葬家
    ,sk.m_nm                                AS  喪主名
    ,sk.m_knm                               AS  喪主名カナ
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_yubin_no
        ELSE
            sk.mg_yubin_no
        END                                 AS  郵便番号
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr1,'')            
        ELSE
            COALESCE(sk.mg_addr1,'')
        END                                 AS  住所1
    ,CAST(CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            COALESCE(sk.kg_addr2,'')
        ELSE
            COALESCE(sk.mg_addr2,'')
        END AS TEXT)                       AS  住所2
    ,CASE WHEN sk.mg_kbn =  1   THEN       -- 故人に同じ
            sk.kg_tel
        ELSE
            sk.mg_tel
        END                                 AS  電話番号
    ,sk.k_nm                                AS  故人名
    ,sk.k_knm                               AS  故人名カナ
    ,sk.m_zoku_nm                           AS  続柄
    ,bm.bumon_lnm                           AS  依頼元担当部門           
    ,stm.tanto_nm                           AS  依頼元打合せ担当者名
    ,CASE WHEN sk.moushi_kbn = 2 THEN 
        CASE WHEN snih.basho_kbn = 0 THEN 
            '自宅'
        ELSE snih.basho_nm  END
     ELSE    
        CASE WHEN snis.basho_kbn = 0 THEN 
            '自宅'
        ELSE snis.basho_nm  END
     END                                    AS  葬儀場所
   ,COALESCE(hm_j.reijo_suryo,0)            AS  礼状枚数
   ,1                                       AS  商品行番号
   ,NULL                                    AS  商品コード            
   ,NULL                                    AS  商品名
   ,0                                       AS  単価                         
   ,0                                       AS  数量
   ,NULL    AS 大分類名
   ,NULL AS 送付先名
   ,NULL AS 送付先郵便番号
   ,NULL AS 送付先住所1
   ,NULL AS 送付先住所2
   ,NULL AS 送付先電話番号
FROM    hachu_denpyo	hd
LEFT JOIN (SELECT denpyo_no, hachu_no, 
        COALESCE(SUM(case WHEN dai_bunrui_cd IN ('0020','0025') THEN
                hachu_suryo
			ELSE
                0	
			END),0) AS hachu_suryo  
    FROM hachu_denpyo_msi
    WHERE delete_flg = 0
    AND   (dai_bunrui_cd IN ('0020','0025')
	OR (chu_bunrui_cd = '0015' AND shohin_kbn    = '3005'))
    GROUP BY denpyo_no, hachu_no) hm            
    ON hd.denpyo_no = hm.denpyo_no
LEFT JOIN
    (SELECT denpyo_no, sum(hm.hachu_suryo * COALESCE(sm.irisu,1)) AS    reijo_suryo
        FROM hachu_denpyo_msi	hm
		LEFT JOIN shohin_mst sm		-- 商品マスタ	-- 2020/03/29 ADD Kayo
			  ON hm.kaisya_cd			= sm.kaisya_cd 
			  AND hm.shohin_bumon_cd	= sm.bumon_cd 
			  AND hm.shohin_cd			= sm.shohin_cd
			  AND 0						= sm.delete_flg	
        WHERE hm.delete_flg = 0
        AND  hm.chu_bunrui_cd = '0015'
        AND  hm.shohin_kbn    = '3005'    
        GROUP BY hm.denpyo_no) hm_j
    ON hd.denpyo_no = hm_j.denpyo_no
LEFT JOIN seko_hachu_info shi
    ON  hm.denpyo_no = shi.hc_denpyo_no
    AND hm.hachu_no  = shi.hachu_no
    AND 0            = shi.delete_flg
LEFT JOIN seko_kihon_info sk
    ON shi.seko_no   = sk.seko_no
    AND 0            = sk.delete_flg
LEFT JOIN seko_kihon_info sk_c              -- キャンセル時の施行番号
    ON sk.v_free16  = sk_c.seko_no
    AND 0           = sk_c.delete_flg
LEFT	JOIN	seko_nitei  snis       -- 葬儀会場名
    ON  sk.seko_no          =   snis.seko_no
    AND 11                  =   snis.nitei_kbn
    AND 0                   =   snis.delete_flg
LEFT	JOIN	seko_nitei_houji  snih -- 葬儀会場名（法事）
    ON  sk.seko_no          =   snih.seko_no
    AND 1                   =   snih.nitei_kbn
    AND 0                   =   snih.delete_flg
LEFT	JOIN	seko_nitei  snit       -- 通夜
    ON  sk.seko_no          =   snit.seko_no
    AND 4                   =   snit.nitei_kbn
    AND 0                   =   snit.delete_flg
LEFT	JOIN	bumon_mst	bm
    ON	sk.bumon_cd 	=	bm.bumon_cd
    AND 0				=	bm.delete_flg
LEFT	JOIN	tanto_mst stm
    ON	sk.uketuke_tanto_cd =	stm.tanto_cd
    AND 0					=	stm.delete_flg
WHERE   TO_CHAR(hd.order_ymd, 'YYYY/MM/DD') >= :st_ymd
AND     TO_CHAR(hd.order_ymd, 'YYYY/MM/DD') <= :ed_ymd
AND     hd. kaisya_cd = :kaisya_cd              
AND     hd.delete_flg = 0
AND     hm.hachu_suryo= 0
AND     sk.daicho_no_eria IS NOT NULL
$where1			   
   ) T    
ORDER BY        
         T.受付No
        ,T.商品行番号
END_OF_SQL
            , array(
                 'st_ymd'   =>$ymd_st
                ,'ed_ymd'   =>$ymd_ed
                ,'kaisya_cd'=>$kaisyacd
            ));

        if ( count($select) === 0 ) {
            return null;
        }
        // 重複を削除
    Msi_Sys_Utils::debug( '* select=>' . Msi_Sys_Utils::dump($select) );
        $select2 = array();
        foreach ( $select as $rec1 ) {
            // 改行コードを削除する
             $soufu_nm = str_replace(array("\r\n", "\r", "\n"), '', $rec1['送付先名']);
             $rec1['送付先名'] = $soufu_nm;
            if (strlen($rec1['商品コード']) <= 0) {
                // 返礼品データが存在するかチェック
                $sel_flg = 0; 
                foreach ( $select as $rec2 ) {
                    if (strlen($rec2['商品コード']) <= 0) {
                        continue;
                    }    
                    if ($rec1['受付no'] == $rec2['受付no']) {
                        $sel_flg = 1;
                        break;
                    } 
                }
                if ($sel_flg == 0) {
                    $select2[] = $rec1;
                }
            } else {
                $select2[] = $rec1;
            }
        }
        // CSVのヘッダー情報を設定
		$csvData = array();
		$csvline = array();
 		foreach ($select[0]	as $column=>$value) {
 			$csvline[] = $column;
		}
		$csvData[] =  $csvline;
        // 明細を編集
        $new_key    = null; 
        $old_key    = null;
        $count      = 0;
        foreach ( $select2 as $rec ) {
            $new_key = $rec['受付no'];
            if (strlen($old_key) <= 0)   {
               $old_key =  $new_key;
            }
            if ($new_key != $old_key)   {
                $old_key =  $new_key;
                $count	 = 0;
            }
            ++$count;
            $rec['商品行番号'] = $count;
			$csvline = array();
            
			foreach ($rec as $value) {
				$csvline[] = $value;
			}
			$csvData[] =  $csvline;
        }
        return $csvData;
    }
}
