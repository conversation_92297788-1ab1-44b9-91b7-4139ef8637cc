<?php

/**
 * Juchu_JuchuhenkoSosogirei
 *
 * 受注変更 葬送儀礼クラス
 *
 * @category   App
 * @package    controllers\Juchu\Juchu_JuchuhenkoSosogirei
 * <AUTHOR> Sai
 * @since      2014/01/22
 * @filesource 
 */

/**
 * 葬送儀礼クラス
 *
 * @category   App
 * @package    controllers\Juchu\Juchu_JuchuhenkoSosogirei
 * <AUTHOR>
 * @since      2014/01/17
 */
class Juchu_JuchuhenkoSosogirei extends Juchu_JuchuhenkoSubAbstract {

    /**
     *
     * 商品区分大分類コードを取得
     * 0010 => '葬送儀礼'
     * 0020 => '返礼品'
     * 0030 => '料理'
     * 0040 => '壇払'
     * 0050 => '別途費用'
     * 0060 => '立替費用'
     * 0070 => '値引'
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return string 0010:葬送儀礼
     * 
     */
    public function getDaibunruiCd() {
        return "0010";
    }

    /**
     *
     * 目的区分を取得する
     * 1 => '会葬', 2 => 'あつらい', 3 => '忌中', 4 => '遺物', 10 => '通夜用', 20 => '当日昼食', 30 => '壇払い'
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @return string 1:葬送儀礼
     */
    public function getMokutekiKbn() {
        return "1";
    }

    /**
     *
     * データ種別を取得する
     * 1 => '葬送儀礼', 2 => '返礼品', 3 => '料理', 4 => '壇払い', 5 => '別途費用', 6 => '立替金', 7 => '値引き'
     *
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @return string 1:葬送儀礼
     */
    public function getDataSbt() {
        return "1";
    }

    /**
     *
     * currentのcss名を取得する
     * 'customer' => 'お客様情報'
     * 'schedule' => 'タイムスケジュール'
     * 'sosogirei' => '葬送儀礼'
     * 'shikijo' => '式場関係費'
     * 'gift' => '返礼品'
     * 'cook' => '料理'
     * 'danbarai' => '壇払'
     * 'betto' => '別途費用'
     * 'tatekae' => '立替費用'
     * 'nebiki' => '値引'
     *
     * <AUTHOR> Sai
     * @since 2014/2/4
     * @return sosogirei
     */
    public function getCssClassName() {
        return 'sosogirei';
    }


    /**
     *
     * 商品区分中分類条件を取得
     *
     * <AUTHOR> Sai
     * @since 2014/3/27
     * @return string 中分類条件
     */
     public function getChubunruiWhere() {
         return "AND sbm.chu_bunrui_cd NOT IN ('2000')";
     }
     
    /**
     *
     * 印刷グループ条件を取得（葬送儀礼は式場費とその他附帯品を除く）
     *
     * <AUTHOR> Tosaka
     * @since 2018/6/4
     * @return string 印刷グループ条件
     */
     public function getSyoukeiGroupWhere() {
         return "AND syoukei_group_cd NOT IN ('20', '70')";
     }

    /**
     *
     * 施行管理情報の項目番号を取得する
     * 3:葬送儀礼　4:返礼品、5:料理　6:壇払い 7:別途費用　8：立替費用 9：値引き 
     * <AUTHOR> Sai
     * @since 2014/05/09
     * @return string 項目番号
     */
    public function getItemno() {
        return 3;
    }
}
