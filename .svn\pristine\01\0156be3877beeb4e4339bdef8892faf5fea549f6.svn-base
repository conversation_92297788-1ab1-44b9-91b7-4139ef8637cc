<?php
  /**
   * Juchu_BechudenpyoController
   *
   * 別注品受注入力 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   * @version    2019/05/xx sugiyama 軽減税率対応
   * @filesource 
   */

  /**
   * 別注品受注入力 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   */
class Juchu_BechudenpyoController extends Msi_Zend_Controller_Action
{
    /** 施行番号 */
    protected $_sekoNo = '';

    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     */
	public function indexAction()
    {
        $this->_forward( 'order' );
	}

    /**
     * orderアクション
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
	 * @version 2015/01/18 請求先ダイアログの対応 Kayo
     */
    public function orderAction()
    {
        $req = $this->getRequest();

        $sekoNo = App_Utils::getSekoNoEasy();
        if ( strlen($sekoNo) <= 0 ) {
            throw new Exception( "施行No が指定されていません" );
        }
        $this->_sekoNo = $sekoNo;

        App_Utils::setSekoInfo(); // 常に施行情報を表示する

        $params  = Msi_Sys_Utils::webInputs();
        Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        @ $readonly = !!$params['readonly'];

        $db = Msi_Sys_DbManager::getMyDb();

        // Ajax
        if ( $req->isPost() ) {
            try {
                $action = $req->getPost('action');
                // Msi_Sys_Utils::debug( 'action==>' . $action );

                $dataApp = Msi_Sys_Utils::json_decode( $req->getPost('dataAppJson') );
                $dataCol = Msi_Sys_Utils::json_decode( $req->getPost('dataColJson') );

                // Msi_Sys_Utils::debug( 'dataApp==>' . Msi_Sys_Utils::dump($dataApp) );
                // Msi_Sys_Utils::debug( 'dataCol==>' . Msi_Sys_Utils::dump($dataCol) );

                if ( $action == '保存' ) {

                    $data = $dataApp;
                    $data['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy(); // 2016/12/25 ADD Kayo
                    $data['_dtl_'] = $dataCol;

                    // $dataNew = Logic_JuchuDenpyoTanpin::upsert( $data );
                    $dataNew = Logic_JuchuBechuDenpyo::upsert( $data );

                    // Msi_Sys_Utils::debug( 'NEW dataNew ==>' . Msi_Sys_Utils::dump($dataNew) );

                    $newDataApp = $this->_pickDataApp( $dataNew );
                    $newDataCol = $this->_pickDataCol( $dataNew['_dtl_'] );

                    // Msi_Sys_Utils::debug( 'NEW dataApp==>' . Msi_Sys_Utils::dump($newDataApp) );
                    // Msi_Sys_Utils::debug( 'NEW dataCol==>' . Msi_Sys_Utils::dump($newDataCol) );

                    $isNew = $dataNew['_isNew'];
                    $data = array( 
                                  'dataApp' => $newDataApp,
                                  'dataCol' => $newDataCol,
                                  'status' => 'OK',
                                  'msg' => ($isNew ? '新規登録しました' : '更新しました'),
                                   );

                    Msi_Sys_Utils::outJson( $data );
                }
                else if ( $action == '表示' ) {
                    /*
                    $denpyo_no = $req->getPost('denpyo_no');

                    Msi_Sys_Utils::debug( '****** denpyo_no ==>' . $denpyo_no );
                    $_data = DataMapper_JuchuDenpyo::findOne( $db, array('denpyo_no'=>$denpyo_no) );
                    */
                    // $sekyu_cd = $req->getPost('sekyu_cd');
                    // $seko_no = $req->getPost('sn');
                    $sekyu_cd = $params['sekyu_cd'];
                    $seko_no = $params['sn'];
                    $mode = 0;      // 0:受注番号 1:請求番号 2016/12/28 ADD Kayo
                    if (isset($params['mode'])) {
                        $mode = $params['mode'];
                    }    
                    
                    // Msi_Sys_Utils::debug( '** 222 **** seko_no ==>' . $seko_no . ' sekyu_cd ==>' . $sekyu_cd );
                    if ($mode == 0) {
                        $_data = DataMapper_JuchuDenpyo::findOne( $db, array('denpyo_no'=>$sekyu_cd, 'seko_no'=>$seko_no) );
                    } else {
                        $_data = DataMapper_JuchuDenpyo::findOne( $db, array('uri_den_no'=>$sekyu_cd, 'seko_no'=>$seko_no) );
                    }    
                    // $_data = DataMapper_JuchuDenpyoBechu::findOne( $db, array('sekyu_cd'=>$sekyu_cd) ); // , 'seko_no'=>$seko_no) );

                    // Msi_Sys_Utils::debug( '_data==>' . Msi_Sys_Utils::dump($_data) );

                    if ( $_data === null ) {
                        throw new Exception( sprintf("伝票No(%s)のデータがありません", $sekyu_cd) );
                    }

                    $_dataApp = $this->_pickDataApp( $_data );
                    $_dataCol = $this->_pickDataCol( $_data['_dtl_'] );

                        $data = array( 
                                      'dataApp' => $_dataApp,
                                      'dataCol' => $_dataCol,
                                      'status' => 'OK',
                                      'msg' => ''
                                       );

                    // Msi_Sys_Utils::debug( 'data==>' . Msi_Sys_Utils::dump($data) );

                    Msi_Sys_Utils::outJson( $data );
                }
                else if ( $action == '削除' ) {
                    $data = $dataApp;
                    $data['_dtl_'] = $dataCol;

                    $_data = Logic_JuchuBechuDenpyo::delete( $data );
                    // $_data = Logic_JuchuDenpyoTanpin::delete( $data );

                    $_dataApp = $this->_defaultDataApp($db);
                    $_dataCol = $this->_defaultDataCol();
                    $data = array(
                                  'dataApp' => $_dataApp,
                                  'dataCol' => $_dataCol,
                                  'status' => 'OK',
                                  'msg' => '削除しました',
                                  );
                    Msi_Sys_Utils::outJson( $data );
                }
                else if ( $action == '初期化' || $action == '新規' ) {

                    $_dataApp = $this->_defaultDataApp($db);
                    $_dataCol = $this->_defaultDataCol();

                    $data = array( 
                                  'dataApp' => $_dataApp,
                                  'dataCol' => $_dataCol,
                                  'status' => 'OK',
                                  'msg' => ''
                                   );

                    Msi_Sys_Utils::outJson( $data );
                    }
                }
            catch ( Exception $e ) {
                $err = $e->getMessage();
                $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
                $errData = array(
                                 'status' => 'NG',
                                 'msg' => $userMsg,
                                 );
                Msi_Sys_Utils::outJson( $errData );
            }

            return;

        } // isPost()

        // URI query_string で sekyu_cd が指定されている場合
        if ( isset($params['sekyu_cd']) && strlen($params['sekyu_cd']) > 0 ) {
            $sekyu_cd = $params['sekyu_cd'];
            // Msi_Sys_Utils::debug( '****** sekyu_cd ==>' . $sekyu_cd );
            /*
            $_data = DataMapper_SekyuSakiBechu::findOne( $db, array('sekyu_cd'=>$sekyu_cd) );
            if ( $_data ) {
                $dataApp = $this->_pickDataApp( $_data );
                $dataCol = $this->_pickDataCol( $_data['_dtl_'] );
            } else {
                $msg = sprintf( "伝票No(%s)のデータは存在しません", $denpyo_no );
                Msi_Sys_Utils::pushMsg( $msg, 'warn' );
            }
            */
            $dataApp = $this->_defaultDataApp($db);
            $dataCol = $this->_defaultDataCol();
            $dataApp['_reload_denpyo_no'] = $sekyu_cd; // denpyo_no;
        }

        // 初期値
        if ( ! isset($dataApp) ) {
            $dataApp = $this->_defaultDataApp($db);
            $dataCol = $this->_defaultDataCol();
        }

        // 受注日
        $this->view->juchu_ymd = $dataApp['juchu_ymd'];

        // 売上計上日
        $this->view->keijo_ymd = $dataApp['keijo_ymd'];

        // 回収区分
        $kaishu_kbn = $dataApp['kaishu_kbn'];
        $this->view->kaishu_kbn = $kaishu_kbn;

        // 受注先区分
        $juchusaki_kbn = $dataApp['juchusaki_kbn'];
        $this->view->juchusaki_kbn = $juchusaki_kbn;

        // 支払方法
        $pay_method_cd = $dataApp['pay_method_cd'];
        $this->view->pay_method_cd = $pay_method_cd;

        // 税区分
        $tax_kbn = $dataApp['tax_kbn'];
        $this->view->tax_kbn = $tax_kbn;

        // 税率
        $zei_cd = $dataApp['zei_cd'];
        $this->view->zei_cd = $zei_cd;

        // 担当者
        $this->view->tanto_cd = $dataApp['tanto_cd'];
        $this->view->tanto_nm = $dataApp['tanto_nm'];

        // 伝票区分 0420
        $denku = $dataCol['denku'];
        $this->view->denku = $denku;

        // 単位
        $tani_cd = $dataCol['tani_cd'];
        $this->view->tani_cd = $tani_cd;

        // 参照専用
        if ( $readonly ) {
            $this->view->ctxt_readonly = 'my-ctxt-readonly';
        }
        // Zeiマスタ
        $zeiData = array();
        foreach ( DataMapper_ZeiMst::find($db, array(), false) as $rec ) {
            // zei_cd  tekiyo_st_date  tekiyo_ed_date  zei_rtu  zei_hasu_kbn  hasu_chg_date
            $rec11 = Msi_Sys_Utils::remapArrayFlat( $rec, <<< END_OF_TXT
zei_cd zei_rtu zei_hasu_kbn
END_OF_TXT
                                                     );
            $zeiData[] = array_merge( $rec11,
                                      array(
                                            'id' => $rec['zei_cd'],
                                            'text' => $rec['zei_rtu'] . '%',
                                            ) );
        }
        
        // 初期設定データ
        $data = array(
                      'dataApp' => $dataApp,
                      'dataCol' => $dataCol,
                      'mst'     => array('zei' => $zeiData),
                      );

        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        App_Smarty::pushCssFile( ['app/juchu.bechudenpyo.order.css'] );

        if ( true ) { // App_KeigenUtils::isKeigenSupport() ) { // sugiyama  軽減税率対応  keigen
            App_Smarty::pushCssFile('app/juchu/juchu.denpyo.keigen.css');
            App_Smarty::pushJsFile('app/lib.keigen_f04.js'); // これは app/juchu.*.js 等より前に呼ぶ
            App_Smarty::pushJsFile('app/juchu/juchu.bechudenpyo.keigen.js');
        }

        App_Smarty::pushJsFile( ['app/models/model.sekyu.js',      // 2015/01/18 ADD Kayo
			                     'app/juchu.bechudenpyo.order.js'] );
        // 'app/models/model.sekyu.js',
        //                      'app/models/model.nonyu.js',
        //                                 'app/juchu.denpyo.order.js'] );
    }

    /**
     * 伝票データの初期値を返す
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     * @param  Msi_Sys_Db $db	データベース
     */
	protected function _defaultDataApp($db)
    {
		// 回収区分
        $kaishu_kbn = $db->getOneVal( <<< END_OF_SQL
        SELECT   MIN(kbn_value_cd_num)	AS	kbn_value_cd_num
        FROM code_nm_mst
        WHERE code_kbn    = '0430'
        AND	  delete_flg  = 0
END_OF_SQL
        );
		// 受注先区分
        $juchusaki_kbn = $db->getOneVal( <<< END_OF_SQL
        SELECT   MIN(kbn_value_cd_num)	AS	kbn_value_cd_num
        FROM code_nm_mst
        WHERE code_kbn    = '1140'
        AND	  delete_flg  = 0
END_OF_SQL
        );
		// 支払方法
        $pay_method_cd = $db->getOneVal( <<< END_OF_SQL
        SELECT   MIN(kbn_value_cd_num)	AS	kbn_value_cd_num
        FROM code_nm_mst
        WHERE code_kbn    = '1130'
        AND	  delete_flg  = 0
END_OF_SQL
        );
        // 会社コードを取得する 2015/06/10 ADD Kayo
        $kaisya = DataMapper_KaisyaInfo::find($db);
        if (count($kaisya)<= 0) {
            $addDay				= 0;    // 基準日からXX日後
            $bankHolidayKbn		= 0;	// 銀行休日区分 0:前日 1:翌日
        } else {
            $addDay				= $kaisya[0]['maturity_day'];			// 基準日からXX日後
            $bankHolidayKbn		= $kaisya[0]['bank_holiday_kbn'];		// 銀行休日区分 0:前日 1:翌日
        }
         // 葬儀日を取得 
        $seko_kihon = $db->easySelect( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd,'YYYY/MM/DD') AS   sougi_ymd		-- 葬儀日
FROM seko_kihon_info
WHERE delete_flg	=	0
AND seko_no    =   :seko_no
END_OF_SQL
    , array( 'seko_no' => $this->_sekoNo));

        // 支払期日を算出する(銀行休日を判断して支払期日を算出する)
        if (count($seko_kihon) > 0) {       // 2015/08/24 ADD kayo
            if (strlen($seko_kihon[0]['sougi_ymd']) > 0) {
                 $kaishu_ymd = App_DateCalc::GetMaturity($seko_kihon[0]['sougi_ymd'], $addDay, $bankHolidayKbn);
            }   else {
                // 回収予定日を取得  
                 $kaishu_ymd = App_DateCalc::GetMaturity(Msi_Sys_Utils::getDate(), $addDay, $bankHolidayKbn);
            }
        } else {    
            // 回収予定日を取得  
             $kaishu_ymd = App_DateCalc::GetMaturity(Msi_Sys_Utils::getDate(), $addDay, $bankHolidayKbn);
        }    
        
        $dataApp = array(
                         'seko_no'      => $this->_sekoNo,	// 施行No
                         'denpyo_no'    => null,			// 伝票No
                         'kaishu_kbn'   => $kaishu_kbn,		// 回収区分 0430: 売掛(0),現売(1),クレジット(2)
                         'juchusaki_kbn' => $juchusaki_kbn, // 受注先区分 1140: 葬家(1), 直接(2), ネット(3), その他(9)
                         'pay_method_cd' => $pay_method_cd, // 支払方法 1130: 葬家(10),当日(20),振込(30),クレジット(40),
															// グループ(50),社内(60),その他(99)
                         'tax_kbn'      => 2,				// 税区分 0150: 非課税(0),内税(1),外税(2)
                         'zei_cd'       => App_Utils::getZeiCd(), // 税率
                         'juchu_ymd'    => Msi_Sys_Utils::getDate(), // 受注日
                         'keijo_ymd'   => Msi_Sys_Utils::getDate(), // 売上計上日
                         'sekyu_cd'     => null, // 請求先CD
                         'sekyu_nm'     => null, // 請求先名
                         'sekyu_nm2'    => null, // 送付先名
                         'delivery_kbn' => 9,       // 納品場所区分  その他(9)
                         'nonyu_delivery_kbn' => 9, // 納品場所区分  その他(9)
                         'nonyu_cd'     => null, // 納品先CD
                         'nonyu_nm'     => null, // 納品先名
                         'nonyu_dt'     => null, // 納入予定日
                         'kaishu_ymd'   => $kaishu_ymd, // 回収予定日
                         'tanto_cd'     => App_Utils::getTantoCd(), // 担当者CD
                         'tanto_nm'     => App_Utils::getTantoNm(), // 担当者名
                         'bumon_cd'     => App_Utils::getSessionData('ctxt_hall_cd'), // 部門CD
                         'denpyo_biko1' => null, // 伝票備考１
                         'denpyo_biko2' => null, // 伝票備考２
                         'msi_no'       => null, // 受注明細No
                         'cre_user' => null, // 登録    2016/06/16 ADD Kayo
                         'mod_user' => null, // 更新    2016/06/16 ADD Kayo
                         'seikyu_user' => null, // 請求    2016/06/16 ADD Kayo
                         );
        return $dataApp;
    }

    /**
     * 伝票明細データの初期値を返す
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     */
	protected function _defaultDataCol()
    {
        $dataCol = array(
                         'denku'        => 1,  // 伝区 0420: 受注(1),返品(2)
                         'tani_cd'      => 99, // 単位コード(tani_mst): 99(未指定)
                         'suryo'        => 1,  // 数量
                         'nm_input_kbn' => 1,  // 名称入力区分
                         'tnk_chg_kbn'  => 0,  // 売上単価変更区分
                         'tani_ovr_kbn' => 0,  // 単位変更不可区分
                         'order_flg'    => 0   // 発注済み 2015/12/15 ADD Kayo    
                         // 'shohin_nm' => 'aaa'
                         );
        return $dataCol;
    }

    /**
     * 伝票データから項目を抜き出して返す
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     * @param  array  $hdrData
     * @return array
     */
	protected function _pickDataApp($hdrData)
    {
        $dataApp = Msi_Sys_Utils::remapArrayFlat( $hdrData, <<< END_OF_TXT
denpyo_no     kaishu_kbn   tax_kbn    zei_cd     juchu_ymd  keijo_ymd
pay_method_cd  juchusaki_kbn
sekyu_cd  sekyu_nm  sekyu_knm  sekyu_yubin_no sekyu_addr1
sekyu_addr2  sekyu_tel  sekyu_fax  sekyu_biko1  sekyu_biko2
nonyu_cd  nonyu_nm  nonyu_knm  nonyu_yubin_no nonyu_addr1
nonyu_addr2  nonyu_tel  nonyu_fax  nonyu_biko1  nonyu_biko2
delivery_kbn
nonyu_dt  kaishu_ymd
tanto_cd     tanto_nm   bumon_cd 
denpyo_biko1  denpyo_biko2
sekyu_soufu_nm
kaiin_kbn_nm
kaiin_sbt_nm    
seko_no
cre_user
mod_user
seikyu_user    
END_OF_TXT
                                                  );

        $dataApp['nonyu_delivery_kbn'] = $dataApp['delivery_kbn']; // 重複して設定
        $dataApp['pay_method_cd'] = trim($dataApp['pay_method_cd']); // char(5) のため
        return $dataApp;
    }

    /**
     * 伝票明細データから項目を抜き出して返す
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     * @param  array  $hdrData
     * @return array
     */
	protected function _pickDataCol($dtlData)
    {
        $dataCol = array();
        foreach ( $dtlData as $rec ) {
            $newRec = Msi_Sys_Utils::remapArrayFlat( $rec,
                                                     <<< END_OF_TXT
denpyo_kbn  shohin_cd   shohin_nm   juchu_suryo  tani_cd
juchu_tnk   gen_tnk     msi_biko1   msi_biko2    shohin_tkiyo_nm
nebiki_prc
juchu_prc   gen_gaku    arari_gaku  msi_no
disp_no
nm_input_kbn  tnk_chg_kbn  tani_ovr_kbn
juchu_ymd
nafuda_nm
soko_cd soko_nm zaiko_knri_kbn
dai_bunrui_cd chu_bunrui_cd shohin_kbn
order_flg    
k_free1 v_free2 v_free4
zei_kbn zei_cd reduced_tax_rate
END_OF_TXT
                                                     ,
                                                     array( 'denpyo_kbn'      => 'denku',
                                                            'juchu_suryo'     => 'suryo',
                                                            'juchu_tnk'       => 'juchu_tanka',
                                                            'gen_tnk'         => 'genka_tanka',
                                                            'shohin_tkiyo_nm' => 'shohin_tekiyo',
                                                            'juchu_prc'       => 'juchu_gaku',
                                                            'nebiki_prc'      => 'nebiki_gaku',
                                                            'gen_gaku'        => 'genka_gaku',
                                                            'arari_gaku'      => 'arari',
                                                            'disp_no'         => 'line_no' ) );
            /*
            if ( isset($rec['juchu_ymd']) && strlen($rec['juchu_ymd']) > 0 ) {
                $newRec['juchu_bi_v'] = $newRec['juchu_ymd'] = $rec['juchu_ymd'];
            } else if ( isset($rec['juchu_bi_v']) && strlen($rec['juchu_bi_v']) > 0 ) {
                $newRec['juchu_bi_v'] = $newRec['juchu_ymd'] = $rec['juchu_bi_v'];
            }
            */

            $dataCol[] = $newRec;
        }

        return $dataCol;
    }

    /**
     * 一括削除 アクション (現状、使いません)
     *
     * <AUTHOR> Mihara
     * @since  2014/xx/xx
     */
    public function delmultiAction()
    {
        $req = $this->getRequest();

        $params  = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        $db = Msi_Sys_DbManager::getMyDb();

        try {
            $bechu_nos = Msi_Sys_Utils::json_decode( $req->getPost('bechu_nos') );

            // Msi_Sys_Utils::debug( 'bechu_nos==>' . Msi_Sys_Utils::dump($bechu_nos) );

            $dataMulti = array();
            foreach ( $bechu_nos as $line ) {
                list($seko_no, $sekyu_cd) = preg_split('/-/', $line);
                $dataMulti[] = array( 'seko_no' => $seko_no,
                                      'sekyu_cd' => $sekyu_cd );
            }
            
            // Msi_Sys_Utils::debug( 'dataMulti==>' . Msi_Sys_Utils::dump($dataMulti) );

            Logic_JuchuBechuDenpyo::deleteMulti( $dataMulti );

            $data = array( 
                          'status' => 'OK',
                          'msg' => '削除しました'
                           );

            Msi_Sys_Utils::outJson( $data );
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                             'status' => 'NG',
                             'msg' => $userMsg,
                             );
            Msi_Sys_Utils::outJson( $errData );
        }
    }

}
