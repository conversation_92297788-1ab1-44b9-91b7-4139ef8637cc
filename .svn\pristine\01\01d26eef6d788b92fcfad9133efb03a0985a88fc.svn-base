<?php

/**
 * PDF 部門登録リスト
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Kayo
 * @since 	 2015/10/11
 * @filesource 
 */

/**
 * PDF 部門登録リスト
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Kayo
 * @since 	 2015/10/11
 */
class Mstr_Pdf1411Controller extends Zend_Controller_Action {

    private static $title = '部門登録リスト';

    /**
     * アクション
     *
     * <AUTHOR> Kayo
     * @since 2015/10/11
     */
    public function indexAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

        $csv = false;
        // CSV出力
        if (array_key_exists('csv', $dataAppAry)) {
            $csv = $dataAppAry['csv'];
        }

        $db = Msi_Sys_DbManager::getMyDb();
        $title = self::$title;
        if ($csv) {
            $csvData = $this->getCsv($db);
            if (!isset($csvData)) {
                App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
                return;
            }
            $buf = Msi_Sys_Utils::csvOutString($csvData);
            Msi_Sys_Utils::out2way1($buf, $title . '.csv', 'text/csv');
        }
    }

    /**
     * 部門マスタSELECT
     *
     * <AUTHOR> Kayo
     * @since 2015/10/11
     * @param Msi_Sys_Db $db	データベース
     * @return	array	$set_ary	設定配列
     */
    private function bumon_mst_select($db) {
        // 複数会社対応    2016/10/25 ADD Kayo
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $addSql = null;
        $addLeftJoin = null;
        if (App_Utils::isFukusuKaisyaKanri()) {
            $addSql = '
                       bm.kaisya_cd     AS  会社コード,
                       km.kaisya_lnm    AS  会社名,';
            $addLeftJoin = '
                    LEFT JOIN kaisya_info km
                    ON  bm.kaisya_cd = km.kaisya_cd
                    AND 0            = km.delete_flg   
            ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT
    $addSql            
	bm.bumon_cd			AS	部門コード,
    TO_CHAR(bm.tekiyo_st_date,'YYYY/MM/DD')		AS	適用開始日
   ,TO_CHAR(bm.tekiyo_ed_date,'YYYY/MM/DD')		AS	適用終了日
   ,bm.bumon_lnm			AS	正式部門名
   ,bm.bumon_snm			AS	簡略部門名
   ,bm.bumon_lknm			AS	正式カナ名
   ,bm.bumon_sknm			AS	簡略カナ名
   ,bm.zip_no				AS	郵便番号
   ,bm.addr1_nm				AS	住所１
   ,bm.addr2_nm				AS	住所２
   ,bm.addr1_knm			AS	住所１カナ
   ,bm.addr2_knm			AS	住所２カナ
   ,bm.tel					AS	電話番号
   ,bm.tel2					AS	電話番号2
   ,bm.fax					AS	FAX番号
   ,bm.bumon_kbn  || ':' || cd_bumon_kbn.kbn_value_lnm	AS	部門区分
   ,bm.area_cd	  || ':' || cd_area_cd.kbn_value_lnm	AS	エリアコード
   ,bm.kaikei_bumon_cd		AS	会計部門コード
   ,bm.uri_kamoku_cd		AS	売掛金科目コード
   ,CASE WHEN bm.seko_kbn = 0 THEN
		'0:なし'
	ELSE
		'1:あり'
	END						AS	施行区分
   ,CASE WHEN bm.bumon_shohin_sel_kbn = 0 THEN
		'0:部門別商品なし'
	ELSE
		'1:部門別商品あり'
	END						AS	部門別商品選択区分
   --,bm.rendo_seko_kbn || ':' || cd_rendo_seko_kbn.kbn_value_lnm AS 連動用施行区分
   ,bm.char_free2           AS 正式会社名
   ,bm.char_free1           AS ProActive部門コード
   ,CASE WHEN bm.joui_bumon_shohin_sel_kbn = 0 THEN
		'0:選択不可'
	ELSE
		'1:選択可能'
	END						AS	上位部門商品選択区分
   ,bm.soko_cd  || ':'  || sm.soko_lnm                  AS 倉庫
   ,bm.register_no          AS レジ番号
   ,bm.char_free3           AS レジ名
   ,bm.siire_cd  || ':'  || sim.siire_lnm                  AS 仕入先コード
   ,bm.genkin_kamoku_cd           AS 現金科目コード
   ,bm.genkin_hojo_cd           AS 現金補助科目コード
FROM bumon_mst bm
LEFT JOIN code_nm_mst cd_bumon_kbn
    ON  '0100'              =   cd_bumon_kbn.code_kbn
    AND bm.bumon_kbn		=   cd_bumon_kbn.kbn_value_cd_num
    AND 0                   =   cd_bumon_kbn.delete_flg
LEFT JOIN code_nm_mst cd_area_cd
    ON  '1560'              =   cd_area_cd.code_kbn
    AND bm.area_cd			=   cd_area_cd.kbn_value_cd
    AND 0                   =   cd_area_cd.delete_flg
/*LEFT JOIN code_nm_mst cd_rendo_seko_kbn
    ON  '2270'              =   cd_rendo_seko_kbn.code_kbn
    AND bm.rendo_seko_kbn	=   cd_rendo_seko_kbn.kbn_value_cd_num
    AND 0                   =   cd_rendo_seko_kbn.delete_flg*/
LEFT JOIN soko_mst sm
    ON sm.soko_cd = bm.soko_cd
    AND CURRENT_DATE BETWEEN sm.tekiyo_st_date AND sm.tekiyo_ed_date
    AND sm.delete_flg = 0
LEFT JOIN siire_mst sim
    ON sim.siire_cd = bm.siire_cd
    AND CURRENT_DATE BETWEEN sim.tekiyo_st_date AND sim.tekiyo_ed_date
    AND sim.delete_flg = 0
$addLeftJoin                
WHERE bm.delete_flg = 0
AND bm.kaisya_cd  =   :kaisya_cd                            
ORDER BY  bm.bumon_cd
END_OF_SQL
                , array('kaisya_cd' => $curKaisyaCd));
        return $select;
    }

    /**
     * CSV出力メイン
     *
     * <AUTHOR> Kayo
     * @since 2015/10/11
     * @param Msi_Sys_Db $db	データベース
     * @return	   viod
     */
    private function getCsv($db) {
        $select = $this->bumon_mst_select($db);
        if (count($select) == 0) {
            return null;
        }
        // CSVを編集
        $csvData = App_ClsMsterCsvEdit::EditCsv($select);

        return $csvData;
    }

}
