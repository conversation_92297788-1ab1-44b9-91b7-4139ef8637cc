<?php

/**
 * <PERSON>aiko_TanadlgController
 *
 * 棚卸伝票 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Zaiko
 * <AUTHOR> Sai
 * @since      2014/09/09
 * @filesource 
 */

/**
 * 棚卸伝票 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Zaiko
 * <AUTHOR> Sai
 * @since      2014/09/09
 */
class Zaiko_TanadlgController extends Mref_AbstractdlgController {

    /**
     * init ファンクション
     *
     * @override
     * <AUTHOR> Sai
     * @since 2014/09/09
     */
    public function init() {
        $this->_helper->viewRenderer->setViewScriptPathSpec(':module/tanadlg/:action.:suffix');
    }

    /**
     * 検索処理
     *
     * @return array($data, $hash)
     * <AUTHOR> Sai
     * @since 2014/09/09
     */
    protected function _doSearch() {
        $params = $this->_params;
        $data = array();
        $hash = array();

        $db = Msi_Sys_DbManager::getMyDb();
        $cond = array();

        $code           = Msi_Sys_Utils::easyGetVar($params, 's_code');
        $tanto          = Msi_Sys_Utils::easyGetVar($params, 's_tanto');
        $bumon          = Msi_Sys_Utils::easyGetVar($params, 's_bumon');
        $bumon_cd       = Msi_Sys_Utils::easyGetVar($params, 's_bumon_cd');
        $tanto_cd       = Msi_Sys_Utils::easyGetVar($params, 's_tanto_cd');
        $tana_ymd_from  = Msi_Sys_Utils::checkVarOrDefault($params['s_tana_ymd_from'], 'DATE2', null);
        $tana_ymd_to    = Msi_Sys_Utils::checkVarOrDefault($params['s_tana_ymd_to'], 'DATE2', null);
        $soko_cd        = Msi_Sys_Utils::easyGetVar($params, 's_soko_cd'); // 2016/03/06 ADD Kayo 

        // 複数会社対応 2019/02/26 ADD Kobayashi
        if ( App_Utils::isFukusuKaisyaKanri() ) {
            $cond['kaisya_cd'] =  App_Utils::getCtxtKaisyaEasy();
        }

        if (strlen($code) > 0) {
            $cond['denpyo_no'] = array('~', $code);
            $this->view->s_code = $code;
        }
        if (strlen($tanto) > 0) {
            $cond['__x2'] = array('x', "(tanto_cd ~ :x2_1 OR tanto_nm ~ :x2_2)",
                array('x2_1' => $tanto, 'x2_2' => $tanto));
            $this->view->s_tanto = $tanto;
        }
        if (strlen($bumon) > 0) {
            $cond['__x3'] = array('x', "(bumon_cd ~ :x3_1 OR bumon_lnm ~ :x3_2 OR bumon_snm ~ :x3_3)",
                array('x3_1' => $bumon, 'x3_2' => $bumon, 'x3_3' => $bumon));
            $this->view->s_bumon = $bumon;
        }
        if (strlen($bumon_cd) > 0) {
            $cond['bumon_cd'] = DataMapper_BumonEx::findDesCond($db, $bumon_cd);
        }
        if (strlen($tanto_cd) > 0) {
            $cond['tanto_cd'] = $tanto_cd;
        }
        if (strlen($tana_ymd_from) > 0) {
            $cond['tana_dt__no__1'] = array('>=', $tana_ymd_from);
            $this->view->s_tana_ymd_from = $tana_ymd_from;  // 2016/03/06 ADD Kayo
        }
        if (strlen($tana_ymd_to) > 0) {
            $cond['tana_dt__no__2'] = array('<=', $tana_ymd_to);
            $this->view->s_tana_ymd_to = $tana_ymd_to;  // 2016/03/06 ADD Kayo
        }
        if (strlen($soko_cd) > 0) {             // 2016/03/06 ADD Kayo
            $cond['soko_cd'] = $soko_cd;        // 2016/03/06 ADD Kayo
            $this->view->s_soko_cd = $soko_cd;  // 2016/03/06 ADD Kayo
        }                                       // 2016/03/06 ADD Kayo
        $cond['__etc_orderby'] = array(array('tana_dt', 'desc'),array('soko_cd', 'ASC'),array('denpyo_no', 'ASC')); // 2016/03/06 ADD Kayo

        $den = DataMapper_ZaikoInventory::findDen($db, $cond);
        foreach ($den as $rec) {
            $myid = $rec['myid'] = $rec['denpyo_no'];
            $rec['code']         = $rec['denpyo_no'];
            $rec['tana_dt']      = $rec['tana_dt'];
            $rec['soko']         = $rec['soko_nm'];
            $rec['tanto']        = $rec['tanto_nm']; 
            $rec['bumon']        = $rec['bumon_nm']; 
            $data[]              = $rec;
            $hash[$myid]         = $rec;
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );

        return array($data, $hash);
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Sai
     * @since 2014/09/09
     * @return void
     */
    protected function _setScriptVar() {
        $this->view->dlg_title = '棚卸伝票検索';
        $this->view->dlg_tpl_search = 'search-denpyo.tpl';
        $this->view->dlg_tpl_head = 'head-denpyo.tpl';
        $this->view->dlg_tpl_list = 'list-denpyo.tpl';

        foreach (Msi_Sys_Utils::strArrayify_qw('s_code s_tanto s_bumon s_bumon_cd s_tanto_cd' .
                ' s_tana_ymd_from  s_tana_ymd_to') as $k) {
            @ $this->view->$k = $this->_params[$k];
        }
		$wkDate = Msi_Sys_Utils::getDate();;
		$year	= substr($wkDate,0,4);
		$month	= substr($wkDate,5,2);
		$day	= substr($wkDate,8,2);
		$wkDate = date('Y/m/d', strtotime($year .'-' . $month .'-' . $day ." -45 day"));

        $this->view->s_tana_ymd_from = $wkDate;                 // 2016/03/06 ADD Kayo
        $this->view->s_tana_ymd_to = Msi_Sys_Utils::getDate();  // 2016/03/06 ADD Kayo

        App_Smarty::pushCssFile(['app/mref.dialog2.css']);
    }

}
