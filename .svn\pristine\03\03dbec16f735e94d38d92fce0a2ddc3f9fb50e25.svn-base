{include file="fdn_head_std.tpl"}
{include file="fdn_header_4.tpl"}  
<form id="mst-form-id" method="post">
    <div id="main">
        <div id="wideBasic">
            <div id="searchbtnarea">
                <div id="floatl">
                    <span id="shori-title">{$kain_name}金額管理登録</span>
                </div>
            </div>
            <fieldset>
                <label for="lbl_code_kbn" id="lbl_select_code_kbn" class="lbl_normal_cmn require done">{$kain_name}名</label>
                <input  type="hidden" id="code_kbn" name="code_kbn" class="s_code_kbn selectUnder"/>
                <input  type="hidden" id="code_name" name="code_name" class="s_code_name" val="{$kain_name}"/>
            </fieldset> 
            <div class="items">      
                <div class="header">
                    <table id="t_head">
                        <tr>
                            <td class="w3 tbhd">#</td>
                            <td class="w3 tbhd"><i class="glyphicon glyphicon-cog"></i></td>
                            <td class="w5 tbhd">適用開始日</td>                           
			    <td class="w5 tbhd">適用終了日</td>                            
                            <td class="w5 tbhd">コース金額</td>   
                            <td class="w5 tbhd">コース上限金額</td>  
                            <td class="w5 tbhd">口数制限</td> 
                            <td class="w5 tbhd">コース利用金額</td>  
                            <td class="w5 tbhd">{$kain_name}<br>コース優位性</td> 
                            <td class="w5 tbhd">{$kain_name}<br>グループ番号</td>  
                            <td class="w5 tbhd">{$kain_name}特典用<br>コースコード</td> 
                        </tr>
                    </table>
                </div>
                <div class="list">
                    <table id="t_dtl">
                    </table>
                </div>
            </div>
            <div id="buttonsArea">
                <input type="button" name="btn_save" id="btn_save" value="保存" />
                <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
            </div>
        </div>
    </div>
</form>
<!-- テンプレート -->
<script type="text/template" id="tmpl">
	<tr>
	<td class="w3 no tbdt">1</td>
	<td class="control no-border-bottom w3 tbdt">
	<a href="javascript:void(0)" class="destroy my-readonly-hidden iconcolor"><i title="この行を削除します" class="glyphicon glyphicon-trash"></i></a>&nbsp;&nbsp;
	<a href="javascript:void(0)"class="add my-readonly-hidden iconcolor"><i title="行を追加します" class="glyphicon glyphicon-plus-sign"></i></a></td>            
	<td class="w5 tbdt"><input type="text" class="tekiyo_st_date"    value="" maxlength = "10" style="ime-mode:disabled"/></td>    
	<td class="w5 tbdt"><input type="text" class="tekiyo_ed_date"    value="" maxlength = "10" style="ime-mode:active"/></td>            
	<td class="w5 tbdt"><input type="text" class="course_price"      value="" maxlength = "10" style="ime-mode:active" onblur="$.msiJqlib.commaFilterTemp($(this))"/></td>                  
	<td class="w5 tbdt"><input type="text" class="course_price_max"  value="" maxlength = "10" style="ime-mode:active" onblur="$.msiJqlib.commaFilterTemp($(this))"/></td>                  
	<td class="w5 tbdt"><input type="text" class="multiple_max"      value="" maxlength = "2"  style="ime-mode:active"/></td>                  
	<td class="w5 tbdt"><input type="text" class="course_price_used" value="" maxlength = "10" style="ime-mode:active" onblur="$.msiJqlib.commaFilterTemp($(this))"/></td>                  
	<td class="w5 tbdt"><input type="text" class="course_priority"   value="" maxlength = "5"  style="ime-mode:active"/></td>                  
	<td class="w5 tbdt"><input type="text" class="gojokai_group_no"  value="" maxlength = "5"  style="ime-mode:active"/></td>                  
	<td class="w5 tbdt"><input type="text" class="gojokai_cose_cd"   value="" maxlength = "4"  style="ime-mode:active"/></td>                  
	</tr>
</script>

<script id="data-json" type="application/json">
    {$data_json|smarty:nodefaults}
</script>
<script id="data-kain" type="application/json">
    {$data_kain|smarty:nodefaults}
</script>
{include file="fdn_footer_std.tpl"}
