-- fdndt46460000 だけに index を作成します
-- bellmony #9734 [ユーザ問合せ]顧客取引履歴の供花供物検索に時間がかかる
-- lifeland #12254, #12255 顧客取引履歴照会でタイムアウト
CREATE OR REPLACE FUNCTION f202505230001temp() RETURNS VOID AS $$
DECLARE
   v_my_database TEXT;
BEGIN
    SELECT INTO v_my_database current_database();
    IF v_my_database = 'fdndt46460000' THEN
-- 202211250001/fdndt36360000_idx_cre.sql
CREATE INDEX IF NOT EXISTS customer_base_info_seko_no_delete_flg_customer_cd_idx ON customer_base_info (seko_no, delete_flg, customer_cd);
CREATE INDEX IF NOT EXISTS seko_kihon_info_free3_cd_idx ON seko_kihon_info (free3_cd);
CREATE INDEX IF NOT EXISTS seko_kihon_all_free_ts_free1_idx ON seko_kihon_all_free (ts_free1);
CREATE INDEX IF NOT EXISTS juchu_denpyo_nonyu_dt_idx ON juchu_denpyo (nonyu_dt);
-- ./202212190001/fdndt36360000_idx_cre.sql
CREATE INDEX IF NOT EXISTS seko_kihon_info_free2_cd_idx ON seko_kihon_info (free2_cd);
-- ./202301110001/fdndt36360000_idx_cre.sql
CREATE INDEX IF NOT EXISTS seikyu_denpyo_bun_gas_seikyu_den_no_idx ON seikyu_denpyo (bun_gas_seikyu_den_no);
-- ./202301230002/fdndt36360000_idx_cre.sql
CREATE INDEX IF NOT EXISTS seikyu_denpyo_text_free5_idx ON seikyu_denpyo (text_free5);
-- ./202307270001/fdndt46460000_idx_cre_202307270001.sql
CREATE INDEX IF NOT EXISTS seko_kihon_info_sougi_ymd_idx ON seko_kihon_info (sougi_ymd);
    END IF;
END;
$$ LANGUAGE 'plpgsql';

SELECT f202505230001temp();
DROP FUNCTION f202505230001temp();
