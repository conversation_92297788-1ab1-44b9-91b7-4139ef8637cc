<?php

/**
 * Juchu_JuchuinfoSubAbstractHj.php
 * cf. Juchu_JuchuinfoSubAbstract.php からコピー
 *   修正する場合は、Juchu_JuchuinfoSubAbstract.php も併せて検討ください.
 *
 * 法事用 返礼品，商品選択，料理のためのサブ抽象クラス
 *
 * @category   App
 * @package    controller\Juchu\JuchuinfoSubAbstract
 * <AUTHOR> Shimomura
 * @since      2014/03/19
 * @version    2015/01/27 Mihara 新商品選択コンテキスト(_isJuchuItemex_ctxt)の場合、dai_bunrui_cd 等をそのまま使う
 * @version    2015/01/29 Mihara 新商品選択コンテキスト(_juchuItemex_getItem_no_kbn1)の場合、kbn1 条件を使わない
 * @version    2015/05/08 Mihara 「発注済み」項目を追加設定する
 * @version    2015/05/22 Mihara 更新項目に shohin_tkiyo_nm(商品摘要名) を追加
 * @version    2015/06/02 Mihara サイドメニュー更新日時を設定
 * @version    2015/06/30 Mihara 商品名(shohin_nm)をedit可にする対応
 * @version    2020/01/04 tosaka 軽減税率対応
 */
abstract class Juchu_JuchuinfoSubAbstractHj extends Juchu_JuchuAbstract {

    //登録時に使用する目的区分を返す
    protected abstract function getMokutekiKbn();

    /**
     * 商品選択用共通ロジック
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param string $category_kbn カテゴリ区分
     * @param string $kbn1 区分１
     * @param string $kbn2 区分２
     * @return  array $item_list 商品情報
     */
    protected function getItem($category_kbn, $kbn1, $kbn2) {
        $db = Msi_Sys_DbManager::getMyDb();
        $moushikomi_kbn = $this->_moushiKbn;  // 申込区分
        if ($moushikomi_kbn == 5 || $moushikomi_kbn == 6) {
            $moushikomi_kbn = 1;
        }
        // 2017/06/01 複数会社対応
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); // 2017/06/01 ADD Otake
        $denpyoMsi = $this->getJuchuDenpyoMsi($db);
        $item_list = array();
        // 霊柩車の場合は、KBN1を判定しない
        $where1 = '';
        $where2 = '';
        $where3 = '';
        if ($category_kbn !== 12 && $category_kbn !== 13 && @!$this->_juchuItemex_getItem_no_kbn1                 // mihara 2015/01/29
        ) {
            $where1 = 'AND w1.kbn1				= ' . $kbn1;
            $where2 = 'AND w2.kbn1				= ' . $kbn1;
            $where3 = 'AND kbn1					= ' . $kbn1;
        }
        $where4 = '';
        if ($kbn2 == 0) {
            $where4 = 'AND	web_disp_kbn	=	1';
        }
        if ($category_kbn !== 12) {
            // 霊柩車以外の場合	
            $ordr = "
ORDER BY 	case when gazo_file_nm is null then 1 else 0 end asc
		,disp_no  asc
		,T.size_nm
		,T.shohin_cd
		,tnk_chg_kbn asc				-- 売上単価変更区分のあるものは後ろへ表示
		,hanbai_tnk	asc
		,shohin_cd	asc
	";
        } else {
            // 霊柩車の場合
            $ordr = 'ORDER BY T.disp_no,T.shohin_kbn, T.shohin_cd';
        }
        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (				

	SELECT 
		 m.shohin_cd
		,m.shohin_nm
		,m.kani_shohin_nm
		,m.shohin_knm
		,m.shohin_tkiyo_nm
		,m.tani_cd
		,m.setshohin_kbn
		,m.size_nm
		,m.maker_hinban
		,m.tnk_chg_kbn
		,m.shohin_naiyo
		,m.hoshi_umu_kbn
		,m.gazo_file_nm
		,m.gazo_img
		,m._mod_cnt AS shohin_mst_mod_cnt
		,m.gazo_tn_data
		,m.web_disp_kbn
		,m.sagaku_keisan_grp	
		,tk.hanbai_tnk
		,tk.siire_tnk
		,br.dai_bunrui_cd		AS dai_bunrui_cd
		,br.chu_bunrui_cd		AS chu_bunrui_cd
		,br.shohin_kbn			AS shohin_kbn
		,br.shohin_kbn			AS shohin_kbn2
		,skm.shohin_kbn_nm		AS shohin_kbn_nm
		,sdbm.dai_bunrui_nm	AS dai_bunrui_nm
		,scbm.chu_bunrui_nm	AS chu_bunrui_nm
		,case when w1.disp_no is null then	w2.moushikomi_kbn
									  else	w1.moushikomi_kbn end	AS 	moushikomi_kbn	
		,case when w1.disp_no is null then	w2.category_kbn
									  else	w1.category_kbn end		AS 	category_kbn	
		,case when w1.disp_no is null then	w2.kbn1
									  else	w1.kbn1 end				AS 	kbn1	
		,case when w1.disp_no is null then	w2.kbn2
									  else	w1.kbn2 end				AS 	kbn2	
		,case when w1.disp_no is null then	w2.disp_no
									  else	w1.disp_no end  AS 	disp_no	
		,case when w1.tab_no is null then	w2.tab_no
									  else	w1.tab_no end  AS 	tab_no	
		,m.delete_flg
		,0							AS	add_kbn		
		,m.uri_zei_kbn			-- 2015/05/01 ADD Kayo
        -- 販売単価（税込）		-- 2015/05/01 ADD Kayo
        ,CASE WHEN m.uri_zei_kbn = 2 THEN	-- 外税
            tk.hanbai_tnk + (CASE WHEN z.zei_hasu_kbn = 0 THEN		-- 切捨て
                TRUNC(tk.hanbai_tnk * COALESCE(z.zei_rtu, 0) / 100)
            WHEN z.zei_hasu_kbn = 1 THEN		-- 四捨五入　
                round(tk.hanbai_tnk * COALESCE(z.zei_rtu, 0) / 100)
            ELSE								 -- 2:切上げ
                trunc((tk.hanbai_tnk * COALESCE(z.zei_rtu, 0) / 100)+ 0.9)
            END)
        ELSE	-- 非課税、内税
            0
        END						AS	hanbai_tnk_zei	-- 販売単価（税込）
        ,z.zei_rtu              -- 2015/05/01 ADD Kayo
        ,z.zei_hasu_kbn         -- 2015/05/01 ADD Kayo
	FROM shohin_mst m
		LEFT JOIN shohin_tanka_mst tk
			ON m.shohin_cd = tk.shohin_cd
			AND tk.delete_flg=0
			AND ( tk.tekiyo_st_date <= CURRENT_DATE AND tk.tekiyo_ed_date >= CURRENT_DATE )
		LEFT JOIN shohin_bunrui_mst br
			ON m.shohin_cd		=	br.shohin_cd
			AND br.delete_flg	=	0
		LEFT JOIN shohin_kbn_mst skm
			ON br.shohin_kbn	=	skm.shohin_kbn
			AND skm.delete_flg	=	0
		LEFT JOIN shohin_dai_bunrui_mst sdbm
			ON br.dai_bunrui_cd	=	sdbm.dai_bunrui_cd
			AND sdbm.delete_flg	=	0
		LEFT JOIN shohin_chu_bunrui_mst scbm
			ON br.chu_bunrui_cd	=	scbm.chu_bunrui_cd
			AND scbm.delete_flg	=	0
		LEFT JOIN web_disp_shohin_mst w1
			ON  m.shohin_cd	= w1.shohin_cd			
			AND br.dai_bunrui_cd	= w1.dai_bunrui_cd			
			AND br.chu_bunrui_cd	= w1.chu_bunrui_cd			
			AND br.shohin_kbn		= w1.shohin_kbn			
			AND CURRENT_DATE	BETWEEN w1.tekiyo_st_date AND w1.tekiyo_ed_date
			AND	w1.moushikomi_kbn	= :moushikomi_kbn
			AND w1.category_kbn		= :category_kbn
			$where1
			AND	w1.kbn2				= :kbn2
			AND w1.delete_flg		= 0
		LEFT JOIN web_disp_shohin_mst w2
			ON  br.dai_bunrui_cd	= w2.dai_bunrui_cd			
			AND br.chu_bunrui_cd	= w2.chu_bunrui_cd			
			AND br.shohin_kbn		= w2.shohin_kbn			
			AND	W2.shohin_cd		is	null		
			AND CURRENT_DATE	BETWEEN w2.tekiyo_st_date AND w2.tekiyo_ed_date
			AND	w2.moushikomi_kbn	= :moushikomi_kbn
			AND w2.category_kbn		= :category_kbn
			$where2
			AND	w2.kbn2				= :kbn2
			AND w2.delete_flg		= 0
            LEFT	JOIN zei_mst	z		-- 2015/04/21 ADD Kayo
                ON CURRENT_DATE	BETWEEN z.tekiyo_st_date AND z.tekiyo_ed_date
                AND	z.delete_flg	= 0
				AND z.kaisya_cd		= :kaisya_cd -- 2017/06/01 ADD Otake
	WHERE m.delete_flg=0
	AND m.hanbai_st_ymd <= CURRENT_DATE AND m.hanbai_end_ymd >= CURRENT_DATE
) T
WHERE delete_flg=0
	AND	moushikomi_kbn	= :moushikomi_kbn
	AND category_kbn	= :category_kbn
	$where3
	AND kbn2			= :kbn2
	$where4
	$ordr
END_OF_SQL
                , array(
            'moushikomi_kbn' => $moushikomi_kbn,
            'category_kbn' => $category_kbn,
            'kbn2' => $kbn2,
            'kaisya_cd' => $kaisya_cd // 2017/06/01 ADD Otake
        ));

        foreach ($select as $item) {
            $flg = 0;
            foreach ($denpyoMsi as $msi) {
                if ($item['shohin_cd'] == $msi['shohin_cd'] // 商品コードが一致しているか？
                        && $item['dai_bunrui_cd'] == $msi['dai_bunrui_cd'] && $item['chu_bunrui_cd'] == $msi['chu_bunrui_cd'] && $item['shohin_kbn'] == $msi['shohin_kbn']) {
                    $item['msi_no'] = $msi['msi_no'];   // 明細番号
                    $item['quantity'] = $msi['juchu_suryo'];  // 受注数量
                    $item['hanbai_tnk'] = $msi['juchu_tnk'];  // 受注単価
                    $item['uri_zei_kbn'] = $msi['uri_zei_kbn'];  // 売上課税区分      2015/05/01 ADD Kayo
                    $item['hanbai_tnk_zei'] = $msi['hanbai_tnk_zei']; // 受注単価（税込）　2015/05/01 ADD Kayo
                    $item['chargekbn'] = $msi['hoshi_umu_kbn']; // 奉仕料有無区分
                    $item['hoshi_prc'] = $msi['hoshi_prc'];  // 奉仕料
                    $item['refer_uchiwk_no'] = $msi['refer_uchiwk_no']; // 参照先明細№
                    $item['select_shohin_cd'] = $msi['select_shohin_cd']; // 選択商品コード
                    $item['dai_bunrui_cd'] = $msi['dai_bunrui_cd']; // 大分類コード
                    $item['chu_bunrui_cd'] = $msi['chu_bunrui_cd']; // 中分類コード
                    $item['add_kbn'] = $msi['add_kbn'];   // 追加区分 0：通常 1：受注の見積書で追加 8：互助会特典 9：自動作成
                    $flg = 1;
                    break;
                }
            }
            if ($flg == 0) {
                $item['quantity'] = 0;       // 受注数量
                $item['chargekbn'] = $item['hoshi_umu_kbn'];  // 奉仕料有無区分
            }
            $item_list[] = $item;
        }

        // 「その他必要なもの」の場合は、選択された商品を作成するため受注伝票の明細かた特別にデータを作成する。
        if ($category_kbn == 15) {
            foreach ($select as $item) {
                foreach ($denpyoMsi as $msi) {
                    if ($item['shohin_cd'] == $msi['shohin_cd']) { // 商品コードが一致しているか？
                        $item['shohin_kbn'] = '0000';     // 商品区分
                        $item['msi_no'] = $msi['msi_no'];   // 明細番号
                        $item['quantity'] = $msi['juchu_suryo'];  // 受注数量
                        $item['hanbai_tnk'] = $msi['juchu_tnk'];  // 受注単価
                        $item['uri_zei_kbn'] = $msi['uri_zei_kbn'];  // 売上課税区分      2015/05/01 ADD Kayo
                        $item['hanbai_tnk_zei'] = $msi['hanbai_tnk_zei']; // 受注単価（税込）　2015/05/01 ADD Kayo
                        $item['chargekbn'] = $msi['hoshi_umu_kbn']; // 奉仕料有無区分
                        $item['hoshi_prc'] = $msi['hoshi_prc'];  // 奉仕料
                        $item['refer_uchiwk_no'] = $msi['refer_uchiwk_no']; // 参照先明細№
                        $item['select_shohin_cd'] = $msi['select_shohin_cd']; // 選択商品コード
                        $item['dai_bunrui_cd'] = $msi['dai_bunrui_cd']; // 大分類コード
                        $item['chu_bunrui_cd'] = $msi['chu_bunrui_cd']; // 中分類コード
                        $item['add_kbn'] = $msi['add_kbn'];   // 追加区分 0：通常 1：受注の見積書で追加 8：互助会特典 9：自動作成
                        $item_list[] = $item;
                    }
                }
            }
        }

        //選択商品コードがある場合の処理
        foreach ($denpyoMsi as $msi) {
            if (strlen($msi['refer_uchiwk_no']) <= 0) {
                continue;
            }
            //追加料の商品を検索する
            $shohin_cd1 = $msi['shohin_cd'];  // 商品コード
            $uchiwk_no = $msi['refer_uchiwk_no']; // 参照先明細№	
            $shohin_cd2 = null;                     // 商品コード
            foreach ($denpyoMsi as $msi2) {
                $msi_no = $msi2['msi_no'];    // 表示順
                if ($msi_no !== $uchiwk_no) {    // 表示順が異なっている商品は対象外
                    continue;
                }
                // 一致した場合
                if (strlen($msi2['select_shohin_cd']) <= 0) {
                    //NULLの場合は、自分自身の商品コードを設定
                    $shohin_cd2 = $msi2['shohin_cd'];    // 選択商品コード
                } else {
                    $shohin_cd2 = $msi2['select_shohin_cd'];  // 選択商品コード
                }
                break;
            }
            //互助会の商品は数量をゼロに変更
            if (strlen($shohin_cd2) > 0) { // 追加料の商品が存在する場合
                $i = -1;
                foreach ($item_list as $item) {
                    $i++;
                    if ($item['shohin_cd'] !== $shohin_cd1) {
                        continue;
                    }
                    $item['quantity'] = 0;  // 受注数量
                    $item_list[$i] = $item;
                    break;
                }
                //追加料にある商品は数量１とする。
                $i = -1;
                foreach ($item_list as $item) {
                    $i++;
                    if ($item['shohin_cd'] !== $shohin_cd2) {
                        continue;
                    }
                    $item['quantity'] = 1;  // 受注数量
                    $item_list[$i] = $item;
                    break;
                }
            }
        }
        // 互助会商品の場合、コースを追加する。
        $i = -1;
        foreach ($item_list as $item) {
            $i++;
            $item['course_str'] = null; // Web表示コース
            $item['is_course'] = 0;

            // 施行プラン商品明細マスタを取得する
            $sekoplan = $this->getSekoPlanSmsiMst($db
                    , $item['shohin_cd']  // 商品コード
                    , null      // 内訳№
                    , $item['dai_bunrui_cd'] // 大分類コード
                    , $item['chu_bunrui_cd'] // 中分類コード
                    , $item['shohin_kbn2']); // 商品区分
            if (count($sekoplan) > 0) {
                if ($sekoplan[0]['service_kbn'] !== '0') {
                    //コース名を設定
                    $item['course_str'] = $sekoplan[0]['web_disp_crs']; // Web表示コース
                    $item['is_course'] = 1;
                } else {
                    //プラン名を設定
                    $item['web_disp_plan'] = $sekoplan[0]['web_disp_plan']; // Web表示コース
                    $item['web_disp_color'] = $sekoplan[0]['web_disp_color']; // Web表示色
                    $item['is_course'] = 2;
                }
            }
            $item_list[$i] = $item;
        }

        // 互助会特典マスタに商品が存在するの場合、特典マークを追加する。
        $i = -1;
        foreach ($item_list as $item) {
            $i++;
            if ($item['is_course'] !== 0) {
                continue;
            }
            // 互助会特典で作成されたものを対象（追加区分 0：通常 1：受注の見積書で追加 8：互助会特典 9：自動作成）

            if ($item['add_kbn'] !== '8') {
                continue;
            }
            $item['course_str'] = null; // Web表示コース
            $item['is_course'] = 0;
            // 互助会コース特典マスタを取得する
            $tkuten = $this->getGojokaiTknMst($db
                    , $item['shohin_cd']  // 商品コード
                    , $item['dai_bunrui_cd'] // 大分類コード
                    , $item['chu_bunrui_cd'] // 中分類コード
                    , $item['shohin_kbn2']); // 商品区分
            if (count($tkuten) > 0) {
                //特典名を設定
                $item['course_str'] = '特典'; // Web表示コース
                $item['is_course'] = 3;
                $item_list[$i] = $item;
            }
        }

        // 「発注済み」項目を追加設定する    2015/05/08 mihara added
        $this->decoOrderFlg($item_list);

        return $item_list;
    }

    /**
     * 受注済み返礼品を取得
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @return  array $select 受注伝票明細
     */
    public function getJuchuDenpyoMsi($db) {
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); // 2017/06/01 ADD Otake
        $select = $db->easySelect(<<< END_OF_SQL
	SELECT	j.*
			,s.sagaku_keisan_grp 
			,s.uri_zei_kbn			-- 2015/04/19 ADD Kayo
			-- 販売単価（税込）		-- 2015/04/19 ADD Kayo
			,CASE WHEN s.uri_zei_kbn = 2 THEN	-- 外税
				j.juchu_tnk + (CASE WHEN z.zei_hasu_kbn = 0 THEN		-- 切捨て
					TRUNC(j.juchu_tnk * COALESCE(z.zei_rtu, 0) / 100)
				WHEN z.zei_hasu_kbn = 1 THEN		-- 四捨五入　
					round(j.juchu_tnk * COALESCE(z.zei_rtu, 0) / 100)
				ELSE								 -- 2:切上げ
					trunc((j.juchu_tnk * COALESCE(z.zei_rtu, 0) / 100)+ 0.9)
				END)
			ELSE	-- 非課税、内税
				0
			END						AS	hanbai_tnk_zei	-- 販売単価（税込）
	FROM	juchu_denpyo_msi	j
	LEFT JOIN	juchu_denpyo		jh
	ON		j.denpyo_no			=	jh.denpyo_no
	AND		0					=	jh.delete_flg		
	LEFT JOIN  shohin_mst		s
	ON		j.shohin_cd		=	s.shohin_cd
	AND		0				=	s.delete_flg			
	LEFT	JOIN zei_mst	z		-- 2015/04/19 ADD Kayo
		ON j.zei_cd			=	z.zei_cd
		AND	z.delete_flg	=	0
		AND z.kaisya_cd		=	:kaisya_cd -- 2017/06/01 ADD Otake
	WHERE	j.seko_no		=	:seko_no 
        AND     jh.seko_no		=	:seko_no 
	AND		j.seko_no_sub	=	:seko_no_sub 
	--AND		j.data_sbt		=	:data_sbt
	AND		jh.data_kbn		=	2			
	--AND		j.mokuteki_kbn	=	:mokuteki_kbn
	AND		j.delete_flg	=	0
END_OF_SQL
                , array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
            //'data_sbt'		=> $this->getDataSbt(),
            //'mokuteki_kbn'	=> $this->getMokutekiKbn(),
            'kaisya_cd' => $kaisya_cd // 2017/06/01 ADD Otake
        ));

        return $select;
    }

    /**
     * 受注伝票明細 保存処理
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param array $dataApp
     * @return  outJson $data ステータス
     */
    public function save($dataApp) {
        // 確定済み？
        if ($this->isKakuteiOutNg()) {
            return false;
        }

        $db = Msi_Sys_DbManager::getMyDb();
        //保存済みの受注伝票明細データを取得
        $juchu_list = $this->getJuchuDenpyoMsi($db);

        $shohin_cds = array();
        foreach ($dataApp as $tmp) {
            $shohin_cds[] = $tmp['item'];
        }

        if (($dataApp) == null || count($dataApp) == 0) {
            $ret = '更新データなし';

            //全ての登録済みデータを消す
            $shohin_cds = array();
            $i = 0;
            foreach ($juchu_list as $item) {
                // 施行プラン商品明細マスタの施行プランに含まれる商品は削除しない
                $sekoplan = $this->getSekoPlanSmsiMst($db
                        , $item['item']    // 商品コード
                        , null      // 内訳№
                        , $item['dai_bunrui_cd'] // 大分類コード
                        , $item['chu_bunrui_cd'] // 中分類コード
                        , $item['shohin_kbn']  // 商品区分
                );
                if (count($sekoplan) > 0) {
                    if ($sekoplan[0]['service_kbn'] !== '0') {
                        continue;
                    }
                }
                $delete_cds[$i]['shohin_cd'] = $item['shohin_cd'];  // 商品コード
                $delete_cds[$i]['denpyo_no'] = $item['denpyo_no'];  // 伝票№
                $delete_cds[$i]['msi_no'] = $item['msi_no'];   // 明細№
                $i++;
            }
            $this->deleteJuchudenpyoMsiData($db, $shohin_cds);
        } else {
            $delete_cds = array();
            $i = 0;
            foreach ($juchu_list as $item) {
                foreach ($dataApp as $delitem) {
                    if ($delitem['delItem'] == '1') {
                        continue;
                    }
                    if ($item['shohin_cd'] == $delitem['item'] || $item['select_shohin_cd'] == $delitem['item']) {
                        // 施行プラン商品明細マスタの施行プランに含まれる商品は削除しない
                        $sekoplan = $this->getSekoPlanSmsiMst($db, $delitem['item'], $item['msi_no']);
                        if (count($sekoplan) > 0) {
                            if ($sekoplan[0]['service_kbn'] !== '0') {
                                continue;
                            }
                        }
                        $delete_cds[$i]['shohin_cd'] = $delitem['item'];  // 商品コード
                        $delete_cds[$i]['denpyo_no'] = $item['denpyo_no'];  // 伝票№
                        $delete_cds[$i]['msi_no'] = $item['msi_no'];   // 明細№
                        $delete_cds[$i]['dai_bunrui_cd'] = $item['dai_bunrui_cd'];  // 大分類コード
                        $delete_cds[$i]['chu_bunrui_cd'] = $item['chu_bunrui_cd'];  // 中分類コード
                        $delete_cds[$i]['shohin_kbn'] = $item['shohin_kbn'];     // 商品区分
                        $delete_cds[$i]['juchu_tnk'] = $item['juchu_tnk'];      // 商品単価
                        $delete_cds[$i]['shohin_nm'] = $item['shohin_nm'];      // 商品名
                        $i++;
                    }
                }
            }
            $this->deleteJuchudenpyoMsiData($db, $delete_cds);

            //保存済みの受注伝票明細データを取得
            $juchu_list = $this->getJuchuDenpyoMsi($db);

            // 受注伝票、受注伝票明細をを保存
            $ret = $this->saveJuchudenpyoMsi($db, $dataApp, $juchu_list);
        }
        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
        // サイドメニューデータを取得する
        $catKbn = $this->getCategoryKbn();
        $sideMenuData = App_Utils2::getSideMenuDatahoj($catKbn, $this->_sekoNo);    // 2016/07/28 ADD Kayo
//        if ( $catKbn == 101) { // 法要・墓参り
//			$sideMenuData = Juchu_Utils::getSideMenuData( 'memorial', null, null, '2' );
//        } else if ( $catKbn == 102 ) { // 料理
//			$sideMenuData = Juchu_Utils::getSideMenuData( 'food', null, null, '2' );
//        } else if ( $catKbn == 103 ) { // 返礼品
//			$sideMenuData = Juchu_Utils::getSideMenuData( 'gift', null, null, '2' );
//        } else if ( $catKbn == 104 ) { // 宿泊
//			$sideMenuData = Juchu_Utils::getSideMenuData( 'accommodation', null, null, '2' );
//        } else {
//            throw new Exception( "JuchuinfoSubAbstractHj::save(): getSideMenuData( $catKbn ?" );
//        }
        // commit
        $db->commit();

        $data = array(
            'status' => 'OK',
            'dataSideMenu' => $sideMenuData,
            'msg' => '更新しました',
            'ret' => $ret,
        );
        return $data;
    }

    /**
     * 受注伝票明細 削除処理
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param array $dataApp
     * @return  outJson $data ステータス
     */
    public function delete($dataApp) {

        $db = Msi_Sys_DbManager::getMyDb();

        $shohin_cds = array();
        foreach ($dataApp as $shohin_cd => $quantity) {
            $shohin_cds[] = $shohin_cd;
        }
        $this->deleteJuchudenpyoMsiData($db, $shohin_cds);

        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
        // サイドメニューデータを取得する
        if ($this->getCategoryKbn() == 6) {
            //返礼品を更新
            $sideMenuData = Juchu_Utils::getSideMenuData('gift');
        } elseif ($this->getCategoryKbn() == 7) {
            //料理を更新
            $sideMenuData = Juchu_Utils::getSideMenuData('food');
        } else {
            //選択商品を更新
            $sideMenuData = Juchu_Utils::getSideMenuData('item');
        }
        // commit
        $db->commit();

        $data = array(
            'status' => 'OK',
            'dataSideMenu' => $sideMenuData,
            'msg' => '選択項目を削除しました',
        );
        return $data;
    }

    /**
     * 受注伝票明細 保存処理
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param array $dataApp
     * @param array $juchu_list 受注伝票明細
     * @return  void
     */
    public function saveJuchudenpyoMsi($db, $dataApp, $juchu_list) {

        $disp_no = 1;
        $dataHed = array();
        $dataDtl = array();
        $dataTrnDelCol = array();

        $i = 0;
        // 表示順番の最大値を取得する
        $select = $db->easySelect(<<< END_OF_SQL
	SELECT coalesce(max(j.disp_no),0)		AS	disp_no
	FROM	juchu_denpyo_msi	j
		LEFT JOIN	juchu_denpyo		jh
		ON		j.denpyo_no			=	jh.denpyo_no
		AND		0					=	jh.delete_flg		
	WHERE	j.seko_no		=	:seko_no 
	AND		jh.seko_no	=	:seko_no
	AND		j.seko_no_sub	=	:seko_no_sub
	AND		j.delete_flg	=	0			
	AND		jh.data_kbn		=	2			
END_OF_SQL
                , array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
        ));
        if (count($select) > 0) {
            $disp_no = $select[0]['disp_no'];
        } else {
            $disp_no = 0;
        }
        foreach ($dataApp as $tmp) {
            if ($tmp['delItem'] == '1') {
                $retArr = $this->makeJuchudenpyoMsiData($db, $tmp, $disp_no, $juchu_list);
                if ($retArr !== null) {
                    $disp_no++;
                    $dataDtl[$i] = $retArr;
                    $i++;
                }
            }
        }

        // 受注データの登録
        $cnt = $this->saveJuchu($db, $dataHed, $dataDtl, $dataTrnDelCol);
        // 表示順番を更新する
        $select = $db->easySelect(<<< END_OF_SQL
	SELECT
		 j.msi_no
		,j.disp_no
		,j.data_sbt
		,j.mokuteki_kbn
		,j.denpyo_no		
	FROM
		juchu_denpyo_msi	j
		LEFT JOIN	juchu_denpyo		jh
		ON		j.denpyo_no			=	jh.denpyo_no
		AND		0					=	jh.delete_flg		
	WHERE	j.seko_no		=	:seko_no 
        AND             jh.seko_no		=	:seko_no 
	AND		j.seko_no_sub	=	:seko_no_sub 
	AND		j.delete_flg	=	0
	AND		jh.data_kbn		=	2			
	ORDER BY j.data_sbt,j.mokuteki_kbn,j.data_sbt,j.disp_no			
END_OF_SQL
                , array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
        ));
        foreach ($select as $tmp) {
            $disp_no++;
            //1：葬送儀礼 2：返礼品　3：料理 4：壇払い 5：別途費用 6：立替金 7：値引き 8：別注品
            if ($tmp['data_sbt'] == '2' || $tmp['data_sbt'] == '3' || $tmp['data_sbt'] == '4') {
                $db->easyExecute(<<< END_OF_SQL
UPDATE	juchu_denpyo_msi
SET		disp_no		= :disp_no
WHERE   delete_flg  = 0
	AND	seko_no		= :seko_no
	AND seko_no_sub = :seko_no_sub
	AND	denpyo_no	= :denpyo_no					
	AND msi_no		= :msi_no
END_OF_SQL
                        , array('seko_no' => $this->_sekoNo
                    , 'seko_no_sub' => $this->_sekoNoSub
                    , 'msi_no' => $tmp['msi_no']
                    , 'denpyo_no' => $tmp['denpyo_no']
                    , 'disp_no' => $disp_no
                ));
            }
        }
        // 施行発注管理情報を保存する
        $cnt = $this->saveHachuInfo($db, $dataHed, $dataDtl, $dataTrnDelCol);
        // 2015/06/02 Mihara add    cf. save()
        $catKbn = $this->getCategoryKbn();
        $itemno = App_Utils2::getItemnohoj($catKbn);
//        if ( $catKbn == 101) { // 法要・墓参り
//            $itemno = 4;
//        } else if ( $catKbn == 102 ) { // 料理
//            $itemno = 5;
//        } else if ( $catKbn == 103 ) { // 返礼品
//            $itemno = 6;
//        } else if ( $catKbn == 104 ) { // 宿泊
//            $itemno = 8;
//        } else         // 2015/06/02 Mihara add end
//        if ($this->getCategoryKbn() == 6)
//        {
//            $itemno = 9;        //返礼品を更新
//        }
//        elseif ($this->getCategoryKbn() == 7)
//        {
//            $itemno = 10;       //料理を更新
//        }
//        else
//        {
//            $itemno = 11;       //選択商品を更新
//        }
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, 4, $itemno); // 2015/06/02 Mihara 4:法事
    }

    /**
     * 受注伝票から渡された商品コードに相当するデータを削除する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param $db db
     * @param $shohin_cds 消したい商品コードの配列
     */
    protected function deleteJuchudenpyoMsiData($db, $shohin_cds) {
        if (count($shohin_cds) <= 0) {
            return;
        }
        foreach ($shohin_cds as $tmp) {
            // 受注伝票明細を削除
            $db->easyExecute(<<< END_OF_SQL
			UPDATE
				juchu_denpyo_msi
			SET	delete_flg = 1
			WHERE   delete_flg		= 0
				AND	seko_no			=	:seko_no
				AND	seko_no_sub		=	:seko_no_sub		
				AND	denpyo_no		=	:denpyo_no
				AND msi_no			=	:msi_no
END_OF_SQL
                    , array('seko_no' => $this->_sekoNo
                , 'seko_no_sub' => $this->_sekoNoSub
                , 'denpyo_no' => $tmp['denpyo_no']
                , 'msi_no' => $tmp['msi_no']
            ));
            // 施行発注管理情報を削除
            $db->easyExecute(<<< END_OF_SQL
				UPDATE
					seko_hachu_info
				SET	delete_flg = 1
				WHERE   delete_flg  = 0
					AND	seko_no			=	:seko_no
					AND	jc_denpyo_no	=	:jc_denpyo_no
					AND jc_msi_no		=	:jc_msi_no
END_OF_SQL
                    , array('seko_no' => $this->_sekoNo
                , 'jc_denpyo_no' => $tmp['denpyo_no']
                , 'jc_msi_no' => $tmp['msi_no']
            ));
        }
    }

    /**
     * 受注明細に登録するためのデータを作成する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @version 2014/12/17 施行プラン商品明細マスタから単価を取得する 2014/12/17 ADD Kayo
     * @param $db DB
     * @param $tmp	画面から取得したデータ
     * @param at $rray $juchu_list 受注伝票明細
     * @return  array $dataDtl		受注伝票明細
     */
    protected function makeJuchudenpyoMsiData($db, $tmp, $disp_no, $juchu_list) {
        $category_kbn = $tmp['category_kbn'];
        $kbn1 = $tmp['kbn1'];
        $kbn2 = $tmp['kbn2'];
        $shohin_cd = $tmp['item'];
        $quantity = $tmp['quantity'];
        $price = (isset($tmp['price']) ? $tmp['price'] : null);
        $shohin_nm = (isset($tmp['shohinnm']) ? $tmp['shohinnm'] : null); // 2015/06/30 mihara
        $hoshi_umu_kbn = $tmp['charge'];

        // 選択可能な返礼品の商品情報を表示する
        $moushikomi_kbn = $this->_moushiKbn;  // 申込区分
        if ($moushikomi_kbn == 5 || $moushikomi_kbn == 6) {
            $moushikomi_kbn = 1;
        }
        $webDisp = $this->getWebDispShohinMst($db, $moushikomi_kbn, $category_kbn, $kbn1, $kbn2);
        $where = "";
        $i = 0;
        foreach ($webDisp as $tmp2) {
            //グループ設定の場合
            $dai_bunrui_cd = $tmp2['dai_bunrui_cd'];
            $chu_bunrui_cd = $tmp2['chu_bunrui_cd'];
            $shohin_kbn = $tmp2['shohin_kbn'];
            if ($i == 0) {
                $where = $where . "     ((T.dai_bunrui_cd = '" . $dai_bunrui_cd . "' ";
                $where = $where . " AND   T.chu_bunrui_cd = '" . $chu_bunrui_cd . "' ";
                $where = $where . " AND   T.shohin_kbn    = '" . $shohin_kbn . "') ";
            } else {
                $where = $where . " OR  (T.dai_bunrui_cd = '" . $dai_bunrui_cd . "' ";
                $where = $where . " AND  T.chu_bunrui_cd = '" . $chu_bunrui_cd . "' ";
                $where = $where . " AND  T.shohin_kbn    = '" . $shohin_kbn . "') ";
            }
            $i++;
        }

        if ($i > 0) {
            $where = $where . ')';
        }
        // mihara 2015/01/27
        // 新商品選択コンテキストの場合、getWebDispShohinMst()を使わず、dai_bunrui_cd 等をそのまま使う
        if (@ $this->_isJuchuItemex_ctxt) { // 新商品選択コンテキスト
            $dai_bunrui_cd = $tmp['daibunruicd'];
            $chu_bunrui_cd = $tmp['chubunruicd'];
            $shohin_kbn = $tmp['shohinkbn'];
            if (isset($tmp['mokuteki_kbn']) && strlen($tmp['mokuteki_kbn']) > 0) {
                $mokuteki_kbn_itemex = $tmp['mokuteki_kbn'];
            }
            $where = '';
            $where = $where . "     ((T.dai_bunrui_cd = '" . $dai_bunrui_cd . "' ";
            $where = $where . " AND   T.chu_bunrui_cd = '" . $chu_bunrui_cd . "' ";
            $where = $where . " AND   T.shohin_kbn    = '" . $shohin_kbn . "')) ";
        } // mihara 2015/01/27
        if ($where == '')
            $where = ' 1=1 '; // mihara 2014/12/25

            
// 0：商品区分単位
        $shohin = DataMapper_Shohin::findOne($db, array(
                    'delete_flg' => 0,
                    'shohin_cd' => $shohin_cd,
                    '__raw_1' => $where
        ));

        if ($shohin === null) {
            return null;
        }

        if ($price === null) {
            // 施行プラン商品明細マスタを取得する 2014/12/17 ADD Kayo
            $sekoplan = $this->getSekoPlanSmsiMst($db
                    , $shohin_cd            // 商品コード
                    , null     // 内訳№
                    , $tmp['daibunruicd'] // 大分類コード
                    , $tmp['chubunruicd'] // 中分類コード
                    , $tmp['shohinkbn']); // 商品区分
            if (count($sekoplan) > 0) {
                $price = $sekoplan[0]['hanbai_tnk'];
            } else {
                $price = $shohin['hanbai_tnk'];
            }
        }

        $shohintkiyonm = $tmp['shohintkiyonm'];
        $daibunruicd = $tmp['daibunruicd'];  // 大分類コード
        $chubunruicd = $tmp['chubunruicd'];  // 中分類コード
        $shohinkbn = $tmp['shohinkbn'];  // 商品区分
        $sagakukeisangrp = $tmp['sagakukeisangrp']; // 差額計算グループ
        $juchumsi = array();
        if (strlen($sagakukeisangrp) > 0) {
            if ($sagakukeisangrp !== '00000') {  // 差額計算グループ
                if (strlen($daibunruicd) > 0 && strlen($chubunruicd) > 0 && strlen($shohinkbn) > 0) {
                    $flg = 0;    // 検索フラグ
                    // ①受注明細から基本商品を検索する
                    foreach ($juchu_list as $juchu1) {
                        if ($juchu1['sagaku_keisan_grp'] == $sagakukeisangrp) {
                            if (strlen($juchu1['refer_uchiwk_no']) > 0) {
                                $juchumsi[] = $juchu1;  // 受注明細をSAVE
                                $flg = 1;
                                break;
                            }
                        }
                    }
                    // ②見つからない場合は、互助会で設定されている数量のあるものを設定する
                    if ($flg == 0) {
                        foreach ($juchu_list as $juchu1) {
                            if ($juchu1['sagaku_keisan_grp'] == $sagakukeisangrp) {
                                if (strlen($juchu1['juchu_suryo']) !== 0) {
                                    $juchumsi[] = $juchu1;  // 受注明細をSAVE
                                    break;
                                }
                            }
                        }
                    }

                    // ③受注明細から追加料の商品を検索する
                    foreach ($juchu_list as $juchu2) {
                        if (count($juchumsi) > 0) {
                            if ($juchu2['msi_no'] !== $juchumsi[0]['refer_uchiwk_no']) {
                                continue;
                            }
                            // 商品コードが一致しているか確認
                            if ($juchu2['shohin_cd'] == $tmp['item'] || $juchu2['select_shohin_cd'] == $tmp['item']) {
                                continue;
                            }
                            $juchumsi[] = $juchu2;  // 受注明細をSAVE
                            break;
                        } else {
                            break;
                        }
                    }
                }

                // 受注明細の配列がある場合、差額を算出する
                if (count($juchumsi) > 1) {
                    if ($juchumsi[1]['select_shohin_cd'] == $shohin_cd) {
                        // 商品コードが変更されていない場合、何もしない
                        return null;
                    }
                    // 商品差額計算マスタを取得
                    $sagakuData = $this->getShohinCalcMst($db, $juchumsi[0]['sagaku_keisan_grp']);
                    //差額計算区分 0:計算なし(追加のみ) 1:基本商品から減算
                    if ($sagakuData['sagaku_keisan_kbn'] == '1') {
                        $juchu_prc = $juchumsi[0]['juchu_prc'];      // 受注金額
                        $sagaku = $price - $juchu_prc;        // 変更された商品の差額を求める
                        $juchumsi[1]['shohin_tkiyo_nm'] = '変更料金';    // 商品摘要名
                    } else {
                        $sagaku = $price;           // 変更された商品の差額は求めない
                        $juchumsi[1]['shohin_tkiyo_nm'] = '';      // 商品摘要名
                    }
                    $juchumsi[1]['juchu_tnk'] = $sagaku;                      // 受注単価
                    $juchumsi[1]['juchu_prc'] = $sagaku;                      // 受注金額
                    $juchumsi[1]['select_shohin_cd'] = $shohin_cd;                   // 選択商品コード
                    $juchumsi[1]['shohin_nm'] = strlen($shohin_nm) > 0 ? $shohin_nm : $shohin['shohin_nm']; // 商品名 2015/06/30 mihara
                    $juchumsi[1]['hachu_kbn'] = $shohin['hachu_kbn'];         // 発注区分
                    $juchumsi[1]['zei_kbn'] = $shohin['uri_zei_kbn'];       // 消費税区分
                    //$juchumsi[1]['dai_bunrui_cd']       = $shohin['dai_bunrui_cd'];		// 大分類コード
                    //$juchumsi[1]['chu_bunrui_cd']       = $shohin['chu_bunrui_cd'];		// 中分類コード
                    //$juchumsi[1]['shohin_kbn']          = $shohin['shohin_kbn'];		// 商品区分
                    $juchumsi[1]['siire_cd'] = $shohin['siire_cd'];   // 仕入先コード
                    $juchumsi[1]['siire_lnm'] = $shohin['siire_lnm'];   // 仕入先名
                    $juchumsi[1]['uri_kamoku_cd'] = $shohin['uri_kamoku_cd'];  // 科目コード
                    $juchumsi[1]['hachu_kbn'] = $shohin['hachu_kbn'];   // 発注区分
                    return $juchumsi[1];
                }
            }
        }

        $gen_gaku = $shohin['siire_tnk'] * $quantity;
        $select_shohin_cd = '';
        // 受注明細の差額レコードがない場合
        if (count($juchumsi) > 0) {
            //互助会の商品コードと追加商品コードが同じ場合、差額は出力しない
            if ($juchumsi[0]['shohin_cd'] == $shohin_cd) {
                return null;
            }
            // 明細№の最大値を取得
            $denno = $this->getJuchudenpyoNo();
            $max = $this->getMaxdenpyoMsiNo($denno);
            $max++;
            //参照先明細№を更新
            $db->easyExecute(<<< END_OF_SQL
UPDATE	juchu_denpyo_msi
SET		refer_uchiwk_no		= :refer_uchiwk_no
WHERE   delete_flg  = 0
	AND	seko_no		= :seko_no
	AND seko_no_sub = :seko_no_sub
	AND	denpyo_no	= :denpyo_no				
	AND msi_no		= :msi_no
END_OF_SQL
                    , array('seko_no' => $this->_sekoNo
                , 'seko_no_sub' => $this->_sekoNoSub
                , 'denpyo_no' => $juchumsi[0]['denpyo_no']
                , 'msi_no' => $juchumsi[0]['msi_no']
                , 'refer_uchiwk_no' => $max
            ));
            // 商品差額計算マスタを取得
            $sagakuData = $this->getShohinCalcMst($db, $juchumsi[0]['sagaku_keisan_grp']);
            //差額計算区分 0:計算なし(追加のみ) 1:基本商品から減算
            if ($sagakuData['sagaku_keisan_kbn'] == '1') {
                // 1:基本商品から減算
                $juchu_prc = $juchumsi[0]['juchu_prc']; // 受注金額
                $price = $price - $juchu_prc;    // 変更された商品の差額を求める
                $shohintkiyonm = '変更料金';      // 商品摘要名
                $shohin['dai_bunrui_cd'] = $sagakuData['dai_bunrui_cd'];  // 大分類コード
                $shohin['chu_bunrui_cd'] = $sagakuData['chu_bunrui_cd'];  // 中分類コード
                $shohin['shohin_kbn'] = $sagakuData['shohin_kbn'];  // 商品区分
                $select_shohin_cd = $shohin_cd;      // 商品コード
                $shohin_cd = $sagakuData['sagaku_shohin_cd']; // 差額商品コード
            } else {
                // 0:計算なし(追加のみ)
                $shohintkiyonm = '';       // 商品摘要名
            }
        }

        // 受注伝票が存在する場合、明細を取得してを更新する
        $select = $db->easySelect(<<< END_OF_SQL
	SELECT j.* 
            ,COALESCE(shi.siire_cd, j.siire_cd) AS siire_cd_2       -- 仕入コード 
            ,COALESCE(siirem.siire_lnm, j.siire_lnm) AS siire_lnm_2 -- 仕入名     
	FROM	juchu_denpyo_msi	j
		LEFT JOIN	juchu_denpyo		jh
		ON		j.denpyo_no			=	jh.denpyo_no
		AND		0					=	jh.delete_flg	
                LEFT OUTER JOIN
                    seko_hachu_info shi
                    ON  (
                        j.seko_no = shi.seko_no
                        AND j.msi_no = shi.jc_msi_no
                        AND j.shohin_cd = shi.shohin_cd
                        AND shi.hachu_no_moto IS NULL
                        AND shi.delete_flg = 0
                        )
                            -- 仕入先マスタ
                LEFT OUTER JOIN siire_mst siirem			
                    ON  shi.siire_cd = siirem.siire_cd		
                    AND 0 = siirem.delete_flg
        WHERE           j.seko_no               =       :seko_no
        AND		j.seko_no_sub		=       :seko_no_sub
        AND             j.dai_bunrui_cd         =       :dai_bunrui_cd	
        AND             j.chu_bunrui_cd         =       :chu_bunrui_cd	
        AND             j.shohin_kbn            =       :shohin_kbn	
        AND             j.shohin_cd             =       :shohin_cd	
        --AND           j.data_sbt              =       :data_sbt
        --AND           j.mokuteki_kbn		=       :mokuteki_kbn			
        AND             j.delete_flg            =       0
        AND             jh.data_kbn             =       2			
END_OF_SQL
                , array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
            'dai_bunrui_cd' => $dai_bunrui_cd,
            'chu_bunrui_cd' => $chu_bunrui_cd,
            'shohin_kbn' => $shohin_kbn,
            'shohin_cd' => $shohin_cd,
                //'data_sbt'        => $this->getDataSbt(),
                //'mokuteki_kbn'    => $this->getMokutekiKbn()
        ));
        if (count($select) > 0) {
            // すでに存在する場合は更新にて終了
            $select[0]['juchu_suryo'] = $quantity;     // 数量
            $select[0]['juchu_tnk'] = $price;      // 単価
            $select[0]['juchu_prc'] = $quantity * $price;   // 受注金額
            $select[0]['select_shohin_cd'] = $select_shohin_cd;   // 選択商品コード
            $select[0]['hoshi_umu_kbn'] = $hoshi_umu_kbn;    // 奉仕料有無区分
            $select[0]['gen_tnk'] = $shohin['siire_tnk'];   // 原価単価
            $select[0]['gen_gaku'] = $gen_gaku;     // 原価金額
            $select[0]['hachu_kbn'] = $shohin['hachu_kbn'];   // 発注区分
            $select[0]['zei_kbn'] = $shohin['uri_zei_kbn'];  // 消費税区分
            $select[0]['shohin_nm'] = strlen($shohin_nm) > 0 ? $shohin_nm : $shohin['shohin_nm']; // 商品名 2015/06/30 mihara
            //$select[0]['dai_bunrui_cd']     = $shohin['dai_bunrui_cd'];		// 大分類コード
            //$select[0]['chu_bunrui_cd']     = $shohin['chu_bunrui_cd'];		// 中分類コード
            //$select[0]['shohin_kbn']		= $shohin['shohin_kbn'];		// 商品区分
            $select[0]['siire_cd'] = $select[0]['siire_cd_2'];   // 仕入先コード
            $select[0]['siire_lnm'] = $select[0]['siire_lnm_2'];   // 仕入先名
            $select[0]['uri_kamoku_cd'] = $shohin['uri_kamoku_cd'];  // 科目コード
            $select[0]['hachu_kbn'] = $shohin['hachu_kbn'];   // 発注区分
            $select[0]['select_shohin_cd'] = $select_shohin_cd;   // 選択商品コード
            $select[0]['shohin_tkiyo_nm'] = $shohintkiyonm;               // 商品摘要名  mihara 2015/05/22
            return $select[0];
        }

        $dataDtl = array();
        $Datasbt = $this->getDataSbt();
        $mokutekiKbn = $this->getMokutekiKbn();

        $dataDtl['denpyo_no'] = null;       // 受注伝票№
        $dataDtl['msi_no'] = null;       // 受注明細№
        $dataDtl['disp_no'] = $disp_no;      // 表示順
        if ($Datasbt == 2 || $Datasbt == 3 || $Datasbt == 4) {
            //2：返礼品　3：料理 4：壇払い 
            $dataDtl['add_kbn'] = 1;      // 追加区分
        } else {
            $dataDtl['add_kbn'] = 1;      // 追加区分 とりあえず修正、削除可能にする。
        }
        $dataDtl['data_sbt'] = $Datasbt;      // データ種別
        $dataDtl['bumon_cd'] = $this->_bumonCd;    // 売上部門コード
        $dataDtl['seko_no'] = $this->_sekoNo;    // 施行番号
        $dataDtl['seko_no_sub'] = $this->_sekoNoSub;   // 施行番号（枝番）
        $dataDtl['juchu_ymd'] = null;       // 受注日
        $dataDtl['denpyo_kbn'] = 1;       // 伝票区分
        $dataDtl['mokuteki_kbn'] = isset($mokuteki_kbn_itemex) ? $mokuteki_kbn_itemex : $mokutekiKbn; // 使用目的区分. mihara 2015/3/24
        $dataDtl['dai_bunrui_cd'] = $shohin['dai_bunrui_cd'];  // 大分類コード
        $dataDtl['chu_bunrui_cd'] = $shohin['chu_bunrui_cd'];  // 中分類コード
        $dataDtl['shohin_kbn'] = $shohin['shohin_kbn'];  // 商品区分
        $dataDtl['shohin_cd'] = $shohin_cd;     // 商品コード
        $dataDtl['shohin_nm'] = strlen($shohin_nm) > 0 ? $shohin_nm : $shohin['shohin_nm']; // 商品名 2015/06/30 mihara
        $dataDtl['shohin_tkiyo_nm'] = $shohintkiyonm;    // 商品摘要名
        $dataDtl['juchu_suryo'] = $quantity;     // 数量
        $dataDtl['tani_cd'] = $shohin['tani_cd'];   // 単位コード
        $dataDtl['juchu_tnk'] = $price;      // 単価
        $dataDtl['juchu_prc'] = $quantity * $price;   // 受注金額
        if (isset($tmp['nebiki_prc'])) {
            $dataDtl['nebiki_prc'] = $tmp['nebiki_prc'];  // 値引額 彩苑      2015/05/28　Kayo ADD
        } else {
            $dataDtl['nebiki_prc'] = 0;      // 値引額　ナウエル 2015/05/28　Kayo ADD
        }
        if (isset($tmp['gojokai_nebiki_prc'])) {
            $dataDtl['gojokai_nebiki_prc'] = $tmp['gojokai_nebiki_prc'];  // 互助会値引額 佐野      2015/11/26　sai ADD
        } else {
            $dataDtl['gojokai_nebiki_prc'] = 0;
        }
        $dataDtl['gen_tnk'] = $shohin['siire_tnk'];   // 原価単価
        $dataDtl['gen_gaku'] = $gen_gaku;     // 原価金額
        $dataDtl['arari_gaku'] = 0;       // 粗利益額
        $dataDtl['zei_kbn'] = $shohin['uri_zei_kbn'];  // 消費税区分
        if (isset($tmp['zei_cd']) && strlen($tmp['zei_cd']) > 0) { // 軽減税率対応  keigen tosaka
            $dataDtl['zei_cd'] = $tmp['zei_cd'];
            $dataDtl['out_zei_prc'] = null;       // 行外税消費税額  あとで設定される
            $dataDtl['in_zei_prc'] = null;       // 行内税消費税額  あとで設定される
        } else {
            $dataDtl['zei_cd'] = 0;       // 消費税コード
            $dataDtl['out_zei_prc'] = 0;       // 行外税消費税額
            $dataDtl['in_zei_prc'] = 0;       // 行内税消費税額
        }
        $dataDtl['nafuda_nm'] = null;       // 名札
        $dataDtl['hoshi_umu_kbn'] = $hoshi_umu_kbn;    // 奉仕料有無区分
        $dataDtl['hoshi_ritu_cd'] = 0;       // 奉仕料率コード
        $dataDtl['hoshi_prc'] = 0;       // 奉仕料金額
        $dataDtl['delivery_kbn'] = null;       // 納品場所区分
        $dataDtl['nonyu_cd'] = null;       // 納入先コード
        $dataDtl['nonyu_nm'] = null;       // 納入先名
        $dataDtl['nonyu_knm'] = null;       // 納入先名カナ
        $dataDtl['nonyu_yubin_no'] = null;       // 納入先郵便番号
        $dataDtl['nonyu_addr1'] = null;       // 納入先住所1
        $dataDtl['nonyu_addr2'] = null;       // 納入先住所2
        $dataDtl['nonyu_tel'] = null;       // 納入先電話番号
        $dataDtl['nonyu_fax'] = null;       // 納入先FAX
        $dataDtl['nonyu_dt'] = null;       // 納入予定日
        $dataDtl['msi_biko1'] = null;
        ;      // 明細備考１
        $dataDtl['msi_biko2'] = null;       // 明細備考２
        $dataDtl['siire_cd'] = $shohin['siire_cd'];   // 仕入先コード
        $dataDtl['siire_lnm'] = $shohin['siire_lnm'];   // 仕入先名
        $dataDtl['uri_kamoku_cd'] = $shohin['uri_kamoku_cd'];  // 科目コード
        $dataDtl['delete_flg'] = 0;       // 削除フラグ
        $dataDtl['hachu_kbn'] = $shohin['hachu_kbn'];   // 発注区分
        $dataDtl['select_shohin_cd'] = $select_shohin_cd;   // 選択商品コード

        return $dataDtl;
    }

    /**
     * 商品差額計算マスタを取得を取得する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param Msi_Sys_DbManager $db
     * @param string $sagaku_keisan_grp 差額計算グループ
     * @return  array $select	 商品差額計算マスタ
     */
    public function getShohinCalcMst($db, $sagaku_keisan_grp) {
        // 商品差額計算マスタを取得
        $select = $db->easySelect(<<< END_OF_SQL
				SELECT * 
				FROM	SHOHIN_CALC_MST
				WHERE	sagaku_keisan_grp		=	:sagaku_keisan_grp 
				AND		delete_flg	=	0
END_OF_SQL
                , array(
            'sagaku_keisan_grp' => $sagaku_keisan_grp
        ));
        $sagaku_keisan_kbn = 0;  // 差額計算区分
        $sagaku_shohin_cd = '';  // 差額商品コード
        $dai_bunrui_cd = '';  // 大分類コード
        $chu_bunrui_cd = '';  // 中分類コード
        $shohin_kbn = '';  // 商品区分

        if (count($select) > 0) {
            $sagaku_keisan_kbn = $select[0]['sagaku_keisan_kbn']; // 差額計算区分
            $sagaku_shohin_cd = $select[0]['sagaku_shohin_cd'];  // 差額商品コード
            $dai_bunrui_cd = $select[0]['dai_bunrui_cd'];  // 大分類コード
            $chu_bunrui_cd = $select[0]['chu_bunrui_cd'];  // 中分類コード
            $shohin_kbn = $select[0]['shohin_kbn'];   // 商品区分
        }
        return array('sagaku_keisan_kbn' => $sagaku_keisan_kbn  // 差額計算区分
            , 'sagaku_shohin_cd' => $sagaku_shohin_cd  // 差額商品コード	
            , 'dai_bunrui_cd' => $dai_bunrui_cd   // 大分類コード
            , 'chu_bunrui_cd' => $chu_bunrui_cd   // 中分類コード
            , 'shohin_kbn' => $shohin_kbn    // 商品区分
        );
    }

    /**
     * 施行プラン商品明細マスタを取得する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param Msi_Sys_DbManager $db
     * @param string $shohin_cd 商品コード
     * @param string $dai_bunrui_cd 大分類コード
     * @param string $chu_bunrui_cd 中分類コード
     * @param string $shohin_kbn 商品区分
     * @return  array $select	 施行プラン商品明細マスタ
     */
    public function getSekoPlanSmsiMst($db, $shohin_cd, $uchiwk_no = null, $dai_bunrui_cd = null, $chu_bunrui_cd = null, $shohin_kbn = null) {
        if ($uchiwk_no === null) {
            // 商品コードでの検索
            $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	 a.seko_plan_cd			-- 施行プランコード
	,a.seko_plan_uchiwk_no	-- 施行プラン明細№
	,a.gojokai_cose_cd		-- 互助会コースコード
	,a.gojokai_kbn			-- 互助会区分
	,a.service_kbn			-- サービス区分
	,a.shohin_cd			-- 商品コード
	,a.dai_bunrui_cd		-- 大分類コード
	,a.chu_bunrui_cd		-- 中分類コード
	,a.shohin_kbn			-- 商品区分
	,a.suryo				-- 数量
	,a.hanbai_tnk			-- 販売単価
	,a.refer_uchiwk_no		-- 参照先明細№
	,a.select_shohin_cd		-- 選択商品コード
	,b.web_disp_crs			-- Web表示コース
	,b.web_disp_plan		-- Web表示プラン
	,b.web_disp_color		-- Web表示色				
FROM	seko_plan_smsi_mst	a
	LEFT	JOIN seko_plan_mst	b
	ON		a.seko_plan_cd	=	b.seko_plan_cd
	AND		a.gojokai_kbn	=	b.gojokai_kbn				
	AND		0				=	b.delete_flg				
   WHERE	a.delete_flg		= 0
		AND	a.seko_plan_cd		= :seko_plan_cd
		AND (a.shohin_cd		= :shohin_cd
		OR	 a.select_shohin_cd	= :shohin_cd)
        AND CURRENT_DATE BETWEEN a.tekiyo_st_date AND a.tekiyo_ed_date -- 2017/03/01 ADD Kayo
		AND	dai_bunrui_cd		= :dai_bunrui_cd					
		AND	chu_bunrui_cd		= :chu_bunrui_cd					
		AND	shohin_kbn			= :shohin_kbn					
		AND a.gojokai_kbn		= :gojokai_kbn
END_OF_SQL
                    , array(
                'seko_plan_cd' => $this->_sekoPlanCd,
                'shohin_cd' => $shohin_cd,
                'dai_bunrui_cd' => $dai_bunrui_cd,
                'chu_bunrui_cd' => $chu_bunrui_cd,
                'shohin_kbn' => $shohin_kbn,
                'gojokai_kbn' => $this->_gojokaiKbn
            ));
        } else {
            // 内訳番号での検索
            $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	 a.seko_plan_cd			-- 施行プランコード
	,a.seko_plan_uchiwk_no	-- 施行プラン明細№
	,a.gojokai_cose_cd		-- 互助会コースコード
	,a.gojokai_kbn			-- 互助会区分
	,a.service_kbn			-- サービス区分
	,a.shohin_cd			-- 商品コード
	,a.dai_bunrui_cd		-- 大分類コード
	,a.chu_bunrui_cd		-- 中分類コード
	,a.shohin_kbn			-- 商品区分
	,a.suryo				-- 数量
	,a.hanbai_tnk			-- 販売単価
	,a.refer_uchiwk_no		-- 参照先明細№
	,a.select_shohin_cd		-- 選択商品コード			
	,b.web_disp_crs			-- Web表示コース
	,b.web_disp_plan		-- Web表示プラン
	,b.web_disp_color		-- Web表示色				
FROM	seko_plan_smsi_mst a
	LEFT	JOIN seko_plan_mst	b
	ON		a.seko_plan_cd	=	b.seko_plan_cd
	AND		a.gojokai_kbn	=	b.gojokai_kbn				
	AND		0				=	b.delete_flg				
WHERE	a.delete_flg				= 0
        AND CURRENT_DATE BETWEEN a.tekiyo_st_date AND a.tekiyo_ed_date -- 2017/03/01 ADD Kayo
		AND	a.seko_plan_cd			= :seko_plan_cd
		AND a.seko_plan_uchiwk_no	= :seko_plan_uchiwk_no
		AND a.gojokai_kbn			= :gojokai_kbn
END_OF_SQL
                    , array(
                'seko_plan_cd' => $this->_sekoPlanCd,
                'seko_plan_uchiwk_no' => $uchiwk_no,
                'gojokai_kbn' => $this->_gojokaiKbn
            ));
        }

        return $select;
    }

    /**
     * 互助会コース特典マスタを取得する
     *
     * <AUTHOR> Kayo
     * @since 2014/06/26
     * @param Msi_Sys_DbManager $db
     * @param string $shohin_cd 商品コード
     * @param string $dai_bunrui_cd 大分類コード
     * @param string $chu_bunrui_cd 中分類コード
     * @param string $shohin_kbn 商品区分
     * @return  array $select	 互助会コース特典マスタ
     */
    public function getGojokaiTknMst($db, $shohin_cd, $dai_bunrui_cd, $chu_bunrui_cd, $shohin_kbn) {
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	gojokai_cose_cd		-- 互助会コースコード
   ,gojokai_tkn_msi_no	-- 互助会特典明細№
   ,gojokai_cose_iw		-- 互助会コースコード頭文字
   ,gojokai_kbn			-- 互助会区分
   ,cose_tkn_kbn		-- コース特典区分
   ,juto_kbn			-- 充当区分
   ,shohin_cd			-- 商品コード
   ,dai_bunrui_cd		-- 大分類コード
   ,chu_bunrui_cd		-- 中分類コード
   ,shohin_kbn			-- 商品区分
   ,juto_suryo			-- 充当数量
   ,juto_prc			-- 充当金額
FROM	gojokai_tkn_mst
WHERE	delete_flg		= 0
AND	gojokai_cose_cd		= :gojokai_cose_cd
AND shohin_cd			= :shohin_cd
AND	dai_bunrui_cd		= :dai_bunrui_cd					
AND	chu_bunrui_cd		= :chu_bunrui_cd					
AND	shohin_kbn			= :shohin_kbn					
AND gojokai_kbn			= :gojokai_kbn
END_OF_SQL
                , array(
            'gojokai_cose_cd' => $this->_gojokaiCoseCd,
            'shohin_cd' => $shohin_cd,
            'dai_bunrui_cd' => $dai_bunrui_cd,
            'chu_bunrui_cd' => $chu_bunrui_cd,
            'shohin_kbn' => $shohin_kbn,
            'gojokai_kbn' => $this->_gojokaiKbn
        ));

        return $select;
    }

    /**
     * 選択可能な返礼品の商品情報を表示する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param Msi_Sys_DbManager	$db
     * @param string $kbn2 表示順
     * @param string $moushikomi_kbn 申込区分
     * @param string $category_kbn カテゴリ区分
     * @param string $kbn1 区分１
     * @param string $kbn2 区分２
     * @return  array $select	WEB表示商品マスタ
     */
    public function getWebDispShohinMst($db, $moushikomi_kbn, $category_kbn, $kbn1 = null, $kbn2 = null) {
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	disp_no,
	disp_kbn,
	dai_bunrui_cd,
	chu_bunrui_cd,
	kbn1,
	kbn2,
	shohin_kbn,
	suryo_inp_kbn,
	prc_inp_kbn,
	fuksu_sel_shohin
FROM
	web_disp_shohin_mst
WHERE
		moushikomi_kbn		= :moushikomi_kbn
		AND category_kbn	= :category_kbn
		AND kbn1			= :kbn1
		AND	kbn2			= :kbn2
		AND delete_flg		= 0
		AND CURRENT_DATE	BETWEEN tekiyo_st_date AND tekiyo_ed_date
ORDER BY disp_no
END_OF_SQL
                , array(
            'moushikomi_kbn' => $moushikomi_kbn,
            'category_kbn' => $category_kbn,
            'kbn1' => $kbn1,
            'kbn2' => $kbn2
        ));


        return $select;
    }

    /**
     * 表示する商品区分の一覧を取得する
     *
     * <AUTHOR> Kayo
     * @since 2014/05/09
     * @param void
     * @return  array $ret	商品区分
     */
    public function getShohinKbnList($category_kbn) {
        $db = Msi_Sys_DbManager::getMyDb();
        $moushikomi_kbn = $this->_moushiKbn;
        if ($moushikomi_kbn == 5 || $moushikomi_kbn == 6) {
            $moushikomi_kbn = 1;
        }
        switch ($category_kbn) {
            case 15:
                // その他必要なものの場合	
                $select = $db->easySelect(<<< END_OF_SQL
SELECT * FROM	(					
	SELECT 
		m.disp_no,
		m.disp_kbn,
		m.dai_bunrui_cd,
		m.chu_bunrui_cd,
		m.kbn1,
		m.kbn2,
		m.shohin_kbn,
		m.shohin_kbn	AS	shohin_kbn2,
		m.suryo_inp_kbn,
		m.prc_inp_kbn,
		m.fuksu_sel_shohin,
		skm.shohin_kbn_nm as shohin_kbn_nm
	FROM	web_disp_shohin_mst m
		LEFT JOIN shohin_kbn_mst skm
		ON	m.shohin_kbn	=	skm.shohin_kbn
		AND	skm.delete_flg	=	0
	WHERE
		m.moushikomi_kbn	= :moushikomi_kbn
		AND m.category_kbn	= :category_kbn
		AND m.disp_kbn		= 0
		AND m.delete_flg	= 0
		AND CURRENT_DATE BETWEEN m.tekiyo_st_date AND m.tekiyo_ed_date
	UNION ALL					
	SELECT 
		0					AS	disp_no,	
		1					AS	disp_kbn,
		'0000'				AS	dai_bunrui_cd,
		'0000'				AS	chu_bunrui_cd,
		0					AS	kbn1,
		0					AS	kbn2,
		'0000'				AS	shohin_kbn,
		'0000'				AS	shohin_kbn2,
		0					AS	suryo_inp_kbn,
		0					AS	prc_inp_kbn,
		0					AS	fuksu_sel_shohin,
		'選択された商品'	AS	shohin_kbn_nm
) m
ORDER BY	m.kbn1, m.disp_no
END_OF_SQL
                        , array(
                    'moushikomi_kbn' => $moushikomi_kbn,
                    'category_kbn' => $category_kbn,
                ));
                $ret = array();
                foreach ($select as $tmp) {
                    $ret[$tmp['shohin_kbn']] = $tmp;
                }
                break;
            case 12:
                // 霊柩車の場合は商品区分ごとに１個にまとめる
                $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	m.kbn1,
	m.tab_no,
	m.suryo_inp_kbn,
	m.prc_inp_kbn,
	m.fuksu_sel_shohin,
	min(m.tab_nm)	as shohin_kbn_nm  -- タブ名称
FROM	web_disp_shohin_mst m
	LEFT JOIN shohin_kbn_mst skm
	ON	m.shohin_kbn	=	skm.shohin_kbn
	AND	skm.delete_flg	=	0
WHERE
	m.moushikomi_kbn	= :moushikomi_kbn
	AND m.category_kbn	= :category_kbn
	AND m.disp_kbn		= 0
	AND m.delete_flg	= 0
	AND CURRENT_DATE BETWEEN m.tekiyo_st_date AND m.tekiyo_ed_date
GROUP BY m.kbn1
	,m.tab_no
	,m.suryo_inp_kbn
	,m.prc_inp_kbn
	,m.fuksu_sel_shohin
ORDER BY m.kbn1,m.tab_no
END_OF_SQL
                        , array(
                    'moushikomi_kbn' => $moushikomi_kbn,
                    'category_kbn' => $category_kbn,
                ));
                $ret = array();
                foreach ($select as $tmp) {
                    $ret[$tmp['tab_no']] = $tmp;
                }

                break;
            default:
                // 上記以外の場合
                $select = $db->easySelect(<<< END_OF_SQL
SELECT 
	m.disp_no,
	m.disp_kbn,
	m.dai_bunrui_cd,
	m.chu_bunrui_cd,
	m.kbn1,
	m.kbn2,
	m.shohin_kbn,
	m.suryo_inp_kbn,
	m.prc_inp_kbn,
	m.fuksu_sel_shohin,
	skm.shohin_kbn_nm as shohin_kbn_nm
FROM	web_disp_shohin_mst m
	LEFT JOIN shohin_kbn_mst skm
	ON	m.shohin_kbn	=	skm.shohin_kbn
	AND	skm.delete_flg	=	0
WHERE
	m.moushikomi_kbn	= :moushikomi_kbn
	AND m.category_kbn	= :category_kbn
	AND m.disp_kbn		= 0
	AND m.delete_flg	= 0
	AND CURRENT_DATE BETWEEN m.tekiyo_st_date AND m.tekiyo_ed_date
ORDER BY	m.kbn1, m.disp_no
END_OF_SQL
                        , array(
                    'moushikomi_kbn' => $moushikomi_kbn,
                    'category_kbn' => $category_kbn,
                ));
                $ret = array();
                foreach ($select as $tmp) {
                    $ret[$tmp['shohin_kbn']] = $tmp;
                }
                break;
        }
        return $ret;
    }

    /**
     * 「発注済み」項目を追加設定する
     * ただし、見積確定済みの場合は設定しない
     *
     * <AUTHOR> Mihara
     * @since 2015/05/08
     * @param array  $rec     受注明細等(msi_no, 
     * @return void
     */
    public function decoOrderFlg(&$rec) {
        // Msi_Sys_Utils::debug( '*** decoOrderFlg =>' . Msi_Sys_Utils::dump($rec) );

        $sekoNo = $this->_sekoNo;
        $isKakutei = !!$this->_juchuKakuteiYMD;

        if ($isKakutei) {
            return;
        }

        $db = Msi_Sys_DbManager::getMyDb();

        $denpyoMsi = $this->getJuchuDenpyoMsi($db);
        if (count($denpyoMsi) <= 0) {
            return;
        }

        $denpyo_nos = array(); // should be one rec.
        foreach ($denpyoMsi as $msi) {
            $denpyoNo = $msi['denpyo_no'];
            if (in_array($denpyoNo, $denpyo_nos)) {
                continue;
            }
            $denpyo_nos[] = $denpyoNo;
        }

        $cond = array(
            '__etc_orderby' => array('jc_denpyo_no ASC', 'jc_msi_no ASC'),
        );
        $cond['seko_no'] = $sekoNo;
        // $cond['order_flg'] = 1;
        $cond['__x1'] = DataMapper_Utils::condOneOf('jc_denpyo_no', $denpyo_nos);

        $select = DataMapper_SekoHachuInfoEx::find($db, $cond);

        // Msi_Sys_Utils::debug( '*** decoOrderFlg select=>' . Msi_Sys_Utils::dump($select) );

        foreach ($rec as &$one) {
            if (!isset($one['msi_no'])) {
                continue;
            }
            $msi_no = $one['msi_no'];
            $shohin_cd = $one['shohin_cd'];
            $sehiRec = null;
            foreach ($select as $selOne) {
                if ((int) $selOne['jc_msi_no'] === (int) $msi_no &&
                        (string) $selOne['shohin_cd'] === (string) $shohin_cd) {
                    $sehiRec = $selOne;
                    break;
                }
            }
            if ($sehiRec) { // SEKO_HACHU_INFO にデータあり
                $one['order_flg'] = $sehiRec['order_flg'];
            } else {
                $one['order_flg'] = 0; // 未発注
            }
        }
    }

}
