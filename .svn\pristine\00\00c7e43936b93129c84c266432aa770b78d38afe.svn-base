<?php
/**
 * <PERSON><PERSON>_DlghandenshohinController
 *
 * 搬送伝票 商品 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2015/07/xx
 * @filesource 
 */

/**
 * 搬送伝票 商品 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2015/07/xx
 */
class Hanso_DlghandenshohinController extends Mref_ShohindlgAbstController
{
    /**
     * init ファンクション
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
    public function init()
    {
        // controller path を dlg に設定
        $this->_helper->viewRenderer->setViewScriptPathSpec(':module/:controller/:action.:suffix');
    }

    /**
     * 検索条件調整
     *
     * <AUTHOR> Mihara
     * @since 2015/05/xx
     * @param  array  $cond
     * @param  array  $addParam
     * @return void
     */
    protected function _searchPreFind(&$cond, $addParam)
    {
        $cond['other_bunrui_kbn'] = '9001'; // その他分類区分「9001」

        // 部門
        $bumonCd = App_HakuzenUtils::guessMyKaisyaBumonCd(); // 所属会社の部門 10000 など
        $aBumonCds = array('00001'); // 共通部門
        if ( $bumonCd ) {
            $aBumonCds[] = $bumonCd;
        }

        $cond['__bumon_cds'] = DataMapper_Utils::condOneOf( 'bumon_cd', $aBumonCds, 'bcds_' );

        $cond['__etc_orderby'] = array('shohin_cd ASC', 'bumon_cd ASC');
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @return void
     */
    protected function _setScriptVarHook()
    {
        $this->view->dlg_title = '商品検索（物品販売）';
    }

}
