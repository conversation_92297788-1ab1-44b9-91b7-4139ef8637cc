<?php

/**
 * Ka<PERSON><PERSON>_JuchuhachufileprnController
 *
 * 受注発注ファイル出力　コントローラクラス
 *
 * @category   App
 * @package    controller\Kanri
 * <AUTHOR> Mogi
 * @since      2021/03/xx
 * @filesource 
 */
class Kanri_JuchuhachufileprnController extends Msi_Zend_Controller_Action {

    /**
     * index アクション
     *
     * <AUTHOR> Mogi
     * @since      2021/03/xx
     */
    public function indexAction() {
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile([
            'app/kanri.juchuhachufileprn.js'
        ]);

        // 利用 CSS 設定
        App_Smarty::pushCssFile([
            'app/kanri.print.siji.css',
            'app/kanri.kaikeiprint.siji.css',
        ]);

        //画面初期表示時のデータ取得
        $data = $this->_getInitData();
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
    }

    /**
     * 初期表示データ 取得
     *
     * <AUTHOR> Mogi
     * @since      2021/03/xx
     */
    private function _getInitData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 会社取得
        $dataKaisya = $db->easySelect(<<< END_OF_SQL
SELECT
bumon_cd AS id
,bumon_lnm AS text
FROM bumon_mst
WHERE bumon_kbn = 0
    AND bumon_cd = '40000' -- SOUのみにする
ORDER BY bumon_cd          
END_OF_SQL
        );
        // 現在日前日の日付を取得
        $yesterday = Msi_Sys_Utils::getDateOff(-1);
        $dataApp = array(
            'kaisya_cd' => '40000',
            'print_kbn' => '0',
            'st_ymd' => date('Y/m/d', strtotime('first day of ' . $yesterday)), // 対象年月（自）現在日付前日の月の１日
            'ed_ymd' => $yesterday, // 対象年月（至）現在日付の前日
            'print_sbt' => null
        );
        $printKbn = array(
            array('id' => 0, 'text' => '新規のみ'),
            array('id' => 1, 'text' => '全て'),
        );
        $printSbt = array(
            array('id' => 0, 'text' => '受注'),
            array('id' => 1, 'text' => '発注'),
        );
        $dataCol = DataMapper_Kanri_Juchuhachufileprn::find($db);

        $data = array(
            'dataKaisya' => $dataKaisya,
            'dataApp' => $dataApp,
            'printKbn' => $printKbn,
            'printSbt' => $printSbt,
            'dataCol' => $dataCol
        );

        return $data;
    }

    /**
     * CSV出力 アクション
     *
     * <AUTHOR> Mogi
     * @since      2021/03/xx
     */
    public function csvAction() {
        $db = Msi_Sys_DbManager::getMyDb();
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

        // 条件
        $cond = array();
        $title = 'HANBAI_SOU_';
        // 出力種別
        switch ($dataAppAry['print_sbt']) {
            case '0': // 受注：部門の指定は無し、仕入先がSOU
                $cond['grp_kaisya_kbn'] = '1';
                $cond['zaimu_rendo_kbn'] = '1';
                $title .= 'JUCHU_';
                break;
            case '1': // 発注：部門・親部門はSOU、仕入先は指定なし
//                $cond['h_oya_bumon_cd'] = $dataAppAry['kaisya_cd'];
                //$cond['chukaimoto_zaimu_rendo_kbn'] = '1';
                $cond['__x4'] = array('x', "(T.chukaimoto_zaimu_rendo_kbn=:x4_1 OR T.h_data_kbn=:x4_2)",
                    array(
                        'x4_1' => 1,
                        'x4_2' => 99,
                    )
                );
                $cond['__x3'] = array('x', "(h_bumon_cd = :x3_1 OR h_oya_bumon_cd = :x3_2)",
                    array(
                        'x3_1' => $dataAppAry['kaisya_cd'],
                        'x3_2' => $dataAppAry['kaisya_cd']
                    )
                );
                $title .= 'HACHU_';
                break;
        }
        // 出力区分
        if ($dataAppAry['print_kbn'] == '0') { // 新規のみの場合
            $cond['m_hankan_rendo_kbn'] = '0';
                $title .= 'NEW';
        } else {
                $title .= 'ALL';
        }
        // 検品日：入力した出力範囲
        $cond['__x1'] = array('x', "(h_siire_ymd BETWEEN :x1_1 AND :x1_2)",
            array(
                'x1_1' => $dataAppAry['st_ymd'],
                'x1_2' => $dataAppAry['ed_ymd']
            )
        );
        $_st_ymd = explode('/', $dataAppAry['st_ymd']);
        $_ed_ymd = explode('/', $dataAppAry['ed_ymd']);
        $st_ymd = substr($_st_ymd[0], 2) . sprintf('%02d', $_st_ymd[1]) . sprintf('%02d', $_st_ymd[2]);
        $ed_ymd = substr($_ed_ymd[0], 2) . sprintf('%02d', $_ed_ymd[1]) . sprintf('%02d', $_ed_ymd[2]);
        $title .= $st_ymd . '_' . $ed_ymd;
        // CSV出力
        if (array_key_exists('csv', $dataAppAry)) {
            $csv = $dataAppAry['csv'];
        }
        // 削除区分：削除データは含まない
        $cond['m_delete_flg'] = 0;
        // 出力順：仕入先コード順
        $cond['__etc_orderby'] = array('h_bumon_cd', 'h_siire_cd', 'h_siire_ymd', 'h_data_kbn', 'h_siire_den_no', 'm_msi_no');

        $rec = DataMapper_Pdf1202::find($db, $cond);
        // 出力種別 0：受注
        if($dataAppAry['print_sbt'] == '0'){
            $cond2 = array();
            $cond2['grp_kaisya_kbn']  = '1';
            $cond2['zaimu_rendo_kbn'] = '1';
            if ($dataAppAry['print_kbn'] == '0') { // 新規のみの場合
                $cond2['m_hankan_rendo_kbn'] = '0';
            }
            // 売上計上日(振込日)
            $cond2['__x1'] = array('x', "(h_siire_ymd BETWEEN :x1_1 AND :x1_2)",
                array(
                    'x1_1' => $dataAppAry['st_ymd'],
                    'x1_2' => $dataAppAry['ed_ymd']
                )
            );
            // 削除区分：削除データは含まない
            $cond2['m_delete_flg'] = 0;
            // 出力順：仕入先コード順
            $cond2['__etc_orderby'] = array('h_bumon_cd', 'h_siire_cd', 'h_siire_ymd', 'h_data_kbn', 'h_siire_den_no', 'm_msi_no');
            $rec1 = DataMapper_Pdf1202::findHikitori($db, $cond2);
            $rec = array_merge($rec, $rec1);
        }
        if (count($rec) == 0) {
            App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
            return;
        }

        if ($csv) {
            $csvData = $this->getCsv($rec);
            if (!isset($csvData)) {
                App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
                return;
            }
            $buf = Msi_Sys_Utils::csvOutString($csvData);
            Msi_Sys_Utils::out2way1($buf, $title . '.csv', 'text/csv');
        }
        $dataAppAry['cnt'] = count($rec);
        try {
            $this->saveHistoryData($db, $dataAppAry);
            $this->updRendoKbn($db, $rec);
            $db->commit();
        } catch (Exception $e) {
            $errData = array(
                'status' => 'NG',
                'msg' => '保存処理が失敗しました',
                'e' => $e->getMessage()
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }

    /**
     * 出力履歴データ取得 アクション
     *
     * <AUTHOR> Mogi
     * @since      2021/03/xx
     */
    public function newdataAction() {
        $db = Msi_Sys_DbManager::getMyDb();
        $data = array(
            'dataCol' => DataMapper_Kanri_Juchuhachufileprn::find($db)
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 施行番号順CSV明細を取得（明細）
     * 
     * <AUTHOR> Mogi
     * @since       2021/03/xx
     * @param       array $rec
     * @return      array $csvData CSVデータ
     */
    private function getCsv($rec) {
        $head = '売掛部門CD 売掛部門名 担当者CD 担当者名 区分 葬家名 請求先名 仕入伝票№ 発注伝票NO 施行番号 連動伝票番号 検品日 仕入先CD 共通仕入先CD 仕入先名 締日 支払区分 支払予定日 ';
        $head .= '納入先CD 納入先名 納入日 税区分 伝票備考１ 伝票備考２ 明細№ 伝票区分 大分類CD 大分類名 中分類CD 中分類名 商品区分 商品区分名 商品CD 商品名 ';
        $head .= '商品摘要名 数量 単位 仕入単価 仕入金額値引前 値引額 仕入金額 消費税 消費税調整 税区分 仕入金額(税込) 上代単価 上代金額 倉庫CD 倉庫名 科目CD 科目名 連動 仲介部門 集計グループCD 集計グループ';
        $csvData = array(Msi_Sys_Utils::strArrayify_qw($head));

        foreach ($rec as $row) {
            $csvData[] = array(
                $row['h_bumon_cd'], // 部門CD
                $row['h_bumon_nm'], // 部門名
                $row['h_tanto_cd'], // 担当者CD
                $row['h_tanto_nm'], // 担当者名
                $row['data_kbn_nm'], // 区分
                $row['souke_nm'], // 葬家名
                $row['h_sekyu_nm'], // 請求先名
                $row['h_siire_den_no'], // 仕入伝票№
                $row['h_denpyo_no'], // 発注伝票NO
                $row['seko_no'], // 施行番号
                $row['dendo_denno'], // 連動伝票番号	2015/05/19 ADD Kayo
                $row['h_siire_ymd'], // 検品日
                $row['h_siire_cd'], // 仕入先CD
                $row['com_siire_cd'], // 共通仕入先CD
                $row['h_siire_nm'], // 仕入先名
                $row['h_sime_ymd'], // 締日
                $row['h_harai_kbn_nm'], // 支払区分
                $row['h_harai_yotei_ymd'], // 支払予定日
                $row['h_nonyu_cd'], // 納入先CD
                $row['h_nonyu_nm'], // 納入先名
                $row['h_nonyu_dt'], // 納入日
                $row['h_tax_kbn_nm'], // 税区分
                $row['h_denpyo_biko1'], // 伝票備考１
                $row['h_denpyo_biko2'], // 伝票備考２
                $row['m_msi_no'], // 明細№
                $row['m_denpyo_kbn_nm'], // 伝票区分
                $row['m_dai_bunrui_cd'], // 大分類CD
                $row['m_dai_bunrui_nm'], // 大分類名
                $row['m_chu_bunrui_cd'], // 中分類CD
                $row['m_chu_bunrui_nm'], // 中分類名
                $row['m_shohin_kbn'], // 商品区分
                $row['m_shohin_kbn_nm'], // 商品区分名
                $row['m_shohin_cd'], // 商品CD
                $row['m_shohin_nm'], // 商品名
                $row['m_shohin_tkiyo_nm'], // 摘要名
                $row['m_siire_suryo'], // 数量
                $row['m_tani_nm'], // 単位
                $row['m_siire_tnk'], // 仕入単価
                $row['m_siire_prc'], // 仕入金額(値引前)
                $row['m_nebiki_prc'], // 値引額
                $row['m_siire_nebiki_prc'], // 仕入金額(値引後)
                $row['m_out_zei_prc'], // 消費税
                $row['m_out_zei_hasu_prc'], // 消費税端数
                $row['m_zei_kbn_nm'], // 消費税区分 軽減税率対応
                $row['m_siire_zei_prc'], // 仕入金額(税込)
                $row['m_jyodai_tnk'], // 上代単価
                $row['m_jyodai_prc'], // 上代金額
                $row['m_soko_cd'], // 倉庫CD
                $row['m_soko_nm'], // 倉庫名
                $row['m_kamoku_cd'], // 科目CD
                $row['m_kamoku_nm'], // 科目名
                $row['rendo_mark'], // 連動
                $row['chukai_bumon_nm'], // 仲介部門
                $row['syukei_group_cd'], // 集計グループCD
                $row['syukei_group_nm'], // 集計グループ
            );
        }
        return $csvData;
    }

    /**
     * 仕入連動履歴 保存処理
     * 
     * <AUTHOR> Mogi
     * @since       2021/03/xx
     * @param       Msi_Sys_Db $db	データベース
     * @param       array $dataAppAry
     * @return      int $cnt
     */
    private function saveHistoryData($db, $dataAppAry) {
        $cnt = 0;
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $data = array();
        $data['kanri_no'] = App_ClsGetCodeNo::GetCodeNo($db, 'siire_rendo_history', 'kanri_no', $kijyunYmd);
        $data['select_st_date'] = $dataAppAry['st_ymd']; // 出力範囲（自）
        $data['select_ed_date'] = $dataAppAry['ed_ymd']; // 出力範囲（至）
        $data['select_kbn'] = $dataAppAry['print_kbn']; // 出力区分
        $data['add_select_date'] = date("Y-m-d H:i:s"); // 処理日時
        $data['add_select_kbn'] = $dataAppAry['print_sbt']; // 出力種別
        $data['add_select_cnt'] = $dataAppAry['cnt']; // 出力件数
        $data['bumon_cd'] = $dataAppAry['kaisya_cd']; // 部門コード

        list($sql, $param) = DataMapper_Utils::makeInsertSQL("siire_rendo_history", $data);
        $cnt += $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 仕入伝票明細 販売管理連動区分の更新処理
     * 
     * <AUTHOR> Mogi
     * @since       2021/03/xx
     * @param       Msi_Sys_Db $db	データベース
     * @param       array $rec
     * @return      int $cnt
     */
    private function updRendoKbn($db, $rec) {
        $cnt = 0;

        foreach ($rec as $onerow) {
            $data['hankan_rendo_kbn'] = 1; // 販売管理連動区分
            // 条件部
            $where['siire_den_no'] = $onerow["h_siire_den_no"];  // 仕入伝票番号
            $where['msi_no'] = $onerow["m_msi_no"];  // 明細番号
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("siire_denpyo_msi", $data, $where);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

}
