/** 
 * @fileoverview 退会タブ
 * @version      2022/08/xx mihara
 * 2023/11/xx mihara create(2023/11/07)  #9963 [2次]同居人住所同期でTELも同期(課題No.487)
 */
// var appcst = appcst || {};
msiGlobalObj.myApp = msiGlobalObj.myApp || { appView:null, mstrData:null };// グローバルな受け渡し用

$( function() {
    "use strict";

    var gMyApp = msiGlobalObj.myApp;
    var getAppView = function() { return msiGlobalObj.myApp.appView; }
    var getMstr    = function() { return msiGlobalObj.myApp.mstrData; }

    var utils = window.msiBbUtils;
    var viewUtils = window.msiBbViewUtils;
    var mixin = window.msiBbMixin;

    var _itemMapper =
{
// item                  label       initVal   validation     selector         bindings
msi_no:           '行No                 ""        null          .msi_no             rtCheck',
kokyaku_no:       '顧客No               ""        _ex           .kokyaku_no         rtCheck',
kokyaku_nm1:      '氏名_姓              ""        max(20)       .kokyaku_nm1        rtCheck',
kokyaku_nm2:      '氏名_名              ""        max(20)       .kokyaku_nm2        rtCheck',
kokyaku_kana1:    '氏名カナ_姓          ""        max(20)       .kokyaku_kana1      rtCheck',
kokyaku_kana2:    '氏名カナ_名          ""        max(20)       .kokyaku_kana2      rtCheck',
sex_kbn:          '性別                 ""        null          .sex_kbn            sel2RtCheck2.sex_kbn',
zoku_nm:          '続柄                 ""        max(10)       .zoku_nm            rtCheck',
jotai_kbn:        '状態                 ""        null          .jotai_kbn          sel2RtCheck2.jotai_kbn',
shibo_ymd:        '死亡日               ""        ymd           .shibo_ymd          rtCheck',
kaiin_tekiyo_kbn: '会員適用             0         _ex           .kaiin_tekiyo_kbn   sel2RtCheck2.kaiin_tekiyo_kbn',
seinengappi_ymd:  '生年月日             ""        ymd           .seinengappi_ymd    rtCheck',
tel1:             'TEL1                 ""        tel           .tel1               rtCheck',
tel2:             'TEL2                 ""        tel           .tel2               rtCheck',
yubin_no:         '郵便番号             ""        zip           .yubin_no           rtCheck',
addr1:            '住所1                ""        max(40)       .addr1              rtCheck',
addr2:            '住所2                ""        max(40)       .addr2              rtCheck',
credit_umu_kbn:   'クレジット有無       ""        null          .credit_umu_kbn     sel2RtCheck2.credit_umu_kbn',
taikai_ymd:       '退会日               ""        ymd           .taikai_ymd         rtCheck',
my_douki_flg:     '住所同期             ""        null          .my_douki_flg       rtCheck',
// my_douki_flg:     '住所同期             1        null          .my_douki_flg       rtCheck',
//
line_no:       '行番号               -1        null          .line_no           ',
    };

    var _TaikaiModelBase = utils.genModelBase( _itemMapper,
    {
        initialize: function() {
        },

        // 最終要素かどうかの真偽値を返す. null:判定不能
        isLastItem: function() {
            var appView = getAppView();
            if ( !appView ) return null;
            var col = appView.model.taikaiCol;
            var isLast = col.isLastTaikaiModel(this);
            return isLast;
        },

        validation: {
			kokyaku_no : [
                {
                    required: function(value, attr, computed) {
                        var appView = getAppView();
                        var len = appView.model.taikaiCol.length;
                        if ( len > 1 ) {
                            return true;
                        }
                        return false;
                    },
                    msg: '顧客Noが指定されていません',
                },
                {
                    fn: function(value, attr, computedState) {
                        var appView = getAppView();
                        var recsDoukyoCol = appView.model.filterKokyakuNoInDoukyoCol(value);
                        if ( recsDoukyoCol.length > 0 ) {
                            return '重複する「顧客No」が同居人データに存在します';
                        }
                        var recsTaikaiCol = appView.model.filterKokyakuNoInTaikaiCol(value);
                        if ( recsTaikaiCol.length > 1 ) {
                            return '同じ「顧客No」が退会情報データに存在します';
                        }
                        var recsHonninCol = appView.model.filterKokyakuNoInHonninCol(value);
                        if ( recsHonninCol.length > 0 ) {
                            return '本会員(筆頭者)の「顧客No」は指定できません';
                        }
                    },
                    // msg: '入金日の形式エラーです',
                },
            ],
			kaiin_tekiyo_kbn: [
                {
                    required: function(value, attr, computed) {
                        var appView = getAppView();
                        var len = appView.model.doukyoCol.length;
                        if ( len > 1 ) {
                            return true;
                        }
                        return false;
                    },
                    msg: '会員適用は必須です',
                },
            ],
            jotai_kbn: [
                {
                    fn: function(value, attr, computedState) {
                        var shibo_ymd = this.get('shibo_ymd');
                        if ( value == 0 && !$.msiJqlib.isNullEx2(shibo_ymd) ) { // 生存状態(9619) 0:生存,1:死亡
                            return '「死亡日」が指定されていますが「生存」状態になっています';
                        }
                    },
                },
            ],
            // 退会
            _taikai_etc: function(val, attr, computed) {
                var kaiin_tekiyo_kbn = this.get('kaiin_tekiyo_kbn'),
                    taikai_ymd =  this.get('taikai_ymd'),
                    aTgt = [],
                    aMsg = [],
                    msg,
                    that = this;
                if ( kaiin_tekiyo_kbn == 0 ) { // 会員適用(9642) 0:適用,1:退会,2:会員範囲外 #7425 2022/11/17
                    if ( !$.msiJqlib.isNullEx2(taikai_ymd) ) {
                        aMsg.push('会員適用「適用」ですが「退会日」が指定されています');
                        aTgt.push( '.taikai_etc_cls' );
                    }
                } else if ( kaiin_tekiyo_kbn != 0 ) { // 1:退会, 2:会員範囲外 #7425 2022/11/17
                    if ( $.msiJqlib.isNullEx2(taikai_ymd) ) {
                        aMsg.push('会員適用「退会/会員範囲外」ですが「退会日」が指定されていません');
                        aTgt.push( '.taikai_etc_cls' );
                    }
                }
                if ( aMsg.length ) {
                    msg = aMsg.join(', ');
                    _.defer( function() { that.trigger('decoErrInfo', aTgt.join(','), msg); } );
                    return msg;
                }
            },
            // 必須項目チェック
            _req_etc: function(val, attr, computed) {
                var kokyaku_no = this.get('kokyaku_no'),
                    kokyaku_nm1 = this.get('kokyaku_nm1'),
                    kokyaku_nm2 = this.get('kokyaku_nm2'),
                    kokyaku_kana1 = this.get('kokyaku_kana1'),
                    kokyaku_kana2 = this.get('kokyaku_kana2'),
                    sex_kbn = this.get('sex_kbn'),
                    seinengappi_ymd = this.get('seinengappi_ymd'),
                    jotai_kbn = this.get('jotai_kbn'),
                    aTgt = [],
                    aMsg = [],
                    msg,
                    that = this;
                if ( !$.msiJqlib.isNullEx2(kokyaku_no) ) {
                    if ( $.msiJqlib.isNullEx2(kokyaku_nm1) ) {
                        aMsg.push('氏名_姓が入力されていません');
                        aTgt.push( '.kokyaku_nm1' );
                    }
                }
                // if ( !$.msiJqlib.isNullEx2(kokyaku_no) ) {
                //     if ( $.msiJqlib.isNullEx2(seinengappi_ymd) ) {
                //         aMsg.push('生年月日が入力されていません');
                //         aTgt.push( '.seinengappi_ymd' );
                //     }
                // }

                if ( aMsg.length ) {
                    msg = aMsg.join(', ');
                    _.defer( function() { that.trigger('decoErrInfo', aTgt.join(','), msg); } );
                    return msg;
                }
            },
        },

    } ); // _TaikaiModelBase
    // console.log( '_TaikaiModelBase=>', _TaikaiModelBase );
    var TaikaiModel = gMyApp.TaikaiModel = Backbone.Model.extend( _TaikaiModelBase );
    
    var _TaikaiViewBase = utils.genViewBase( _itemMapper,
    {
        tagName: 'tbody',
        className: 'dtl-taikai-tbody',
        template: _.template( $('.dtl-taikai-item-template').html() ),
        events: {
            "click a.delete,.btn_dtl_del" : "delete",
            "click a.add"    : "add",
            "click .dlg_date": "setDatePickerFocus",
            "click .dlg_taikai_kokyaku_no": "taikaiKokyakuHelper",
            "click .dlg_taikai_yubin_no:not(.disable_etc)": "taikaiYubinNoHelper",
            "change .my_douki_flg": "chgDoukiFlg",
        },

        setCheckBox: function(e, pro, target) {
            var val = this.$(target + ':checked').val();
            this.model.set(pro, val);
        },

        initialize: function() {
            var that = this;

            Backbone.Validation.bind( this, Backbone.Validation.msi_v_iv_callback2({}, undefined, '.') );

            this.listenTo(this.model, 'change', this.render);
            this.listenTo(this.model, 'remove', this.remove);

            this.listenTo(this.model, 'decoErrInfo', this.decoErrInfo);
            this.listenTo(this.model, 'clearErrInfo', this.clearErrInfo);

            this.$el.html( this.template(this.model.toJSON()) );

            this.$(".my-type-date").datepicker($.extend({}, $.msiJqlib.datePickerDefault, {}));

            this.dropdownInit00();

            this.stickit();
        },

        // select2 dropdown 設定
        dropdownInit00: function() {
            var _mstr = getMstr();

            utils.setSelect2CdNm( this.$el, _mstr, [ '.sex_kbn',
                                                     '.jotai_kbn',
                                                     '.credit_umu_kbn',
                                                     '.kaiin_tekiyo_kbn',
                                                     ],
                                                     { placeholder: ' ',
                                                       dropdownAutoWidth:true,
                                                       minimumResultsForSearch: 6 },
                                                     true); // isSetterNum=true

            var that = this;
            _.each( 'sex_kbn  jotai_kbn  credit_umu_kbn  kaiin_tekiyo_kbn'.split(/\s+/),
                    function(key) {
                        var $sel = that.$el.find('.' + key);
                        $sel.select2('val', that.model.get(key));
                    } );

        },

        setDatePickerFocus: function(e) {
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない

            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },

        chgDoukiFlg: function(e) {
            var target = '.my_douki_flg';
            var pro = 'my_douki_flg';
            var val = this.$(target + ':checked').val();
            this.model.set(pro, val);

            var _appView = getAppView();
            if ( val == 1 ) { // selected
                if ( !this.model.has('_is_org_addr_back') ) { // 初回
                    this.model.set('_org_addr_back_yubin_no', this.model.get('yubin_no'));
                    this.model.set('_org_addr_back_addr1', this.model.get('addr1'));
                    this.model.set('_org_addr_back_addr2', this.model.get('addr2'));
                    this.model.set('_org_addr_back_tel1', this.model.get('tel1'));  // #9963 TEL1も同期
                    this.model.set('_is_org_addr_back', 1);
                }
                this.model.set('yubin_no', _appView.model.get('yubin_no'));
                this.model.set('addr1', _appView.model.get('addr1'));
                this.model.set('addr2', _appView.model.get('addr2'));
                this.model.set('tel1', _appView.model.get('tel1'));  // #9963 TEL1も同期
            } else {
                if ( this.model.has('_is_org_addr_back') ) {
                    this.model.set('yubin_no', this.model.get('_org_addr_back_yubin_no'));
                    this.model.set('addr1', this.model.get('_org_addr_back_addr1'));
                    this.model.set('addr2', this.model.get('_org_addr_back_addr2'));
                    this.model.set('tel1', this.model.get('_org_addr_back_tel1'));  // #9963 TEL1も同期
                }
            }
        },

        // 郵便番号 picker
        taikaiYubinNoHelper: viewUtils.zipHelper2( 'yubin_no', 'addr1' ),

        // 顧客 ピッカー
        taikaiKokyakuHelper: function() {
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない

            var bbv = this;

            var par = {
                // is_click_mode: true, // クリックで選択
                // init_search: 1,
                s_kokyaku_kbn: 0, // 0:個人
                is_easyreg: 1, // 登録用ボタン表示
            };

            msiGlobalObj.kaikokyakudlgOpen( this,
                                            par,
                                            function(data) { // select
                                                // console.log( '@@@ selectKaiKokyaku =>', data );
                                                if ( !data ) { return; }

                                                bbv._setTaikaiKokyakuInfo(data);
                                            },
                                            function(data) { // clear
                                                // console.log( '@@@ clearKaiKokyaku =>', data );
                                                bbv._clearTaikaiKokyakuInfo();
                                            } );
        },

        _setTaikaiKokyakuInfo: function(data) {
            var kokyaku_no = data.kokyaku_no;
            var appView = getAppView();
            var msg;

            // if ( appView.model.isKokyakuNoInSomeCols(kokyaku_no) ) {
            //     msg = '顧客番号:' + kokyaku_no + ' はすでに選択されています';
            //     setTimeout( function() { msiLib2.showErr2( msg ); }, 1000 );
            //     return;
            // }

            var bbv = this;
            bbv.model.set( {
                kokyaku_no: data.kokyaku_no,
                kokyaku_nm1: data.kokyaku_nm1,
                kokyaku_nm2: data.kokyaku_nm2,
                kokyaku_kana1: data.kokyaku_kana1,
                kokyaku_kana2: data.kokyaku_kana2,
                sex_kbn: data.sex_kbn,
                zoku_nm: null,
                jotai_kbn: data.jotai_kbn,
                shibo_ymd: data.shibo_ymd,
                kaiin_tekiyo_kbn: 1,
                seinengappi_ymd: data.seinengappi_ymd,
                tel1: data.tel1,
                tel2: data.tel2,
                yubin_no: data.yubin_no,
                addr1: data.addr1,
                addr2: data.addr2,
                credit_umu_kbn: null,
                taikai_ymd: null,
                my_douki_flg: null,
            } );
        },

        _clearTaikaiKokyakuInfo: function() {
            var bbv = this;
            bbv.model.set( {
                kokyaku_no: null,
                kokyaku_nm1: null,
                kokyaku_nm2: null,
                kokyaku_kana1: null,
                kokyaku_kana2: null,
                sex_kbn: null,
                zoku_nm: null,
                jotai_kbn: null,
                shibo_ymd: null,
                kaiin_tekiyo_kbn: null,
                seinengappi_ymd: null,
                tel1: null,
                tel2: null,
                yubin_no: null,
                addr1: null,
                addr2: null,
                credit_umu_kbn: null,
                taikai_ymd: null,
                my_douki_flg: null,
            } );
        },

        decoErrInfo: function(pos, msg) {
            // console.log( 'decoErrInfo=>'+msg + ' pos=>' + pos, this);
            this.$(pos).msiErrDeco(msg);
        },

        clearErrInfo: function(pos) {
            this.$(pos).msiErrClear();
        },

        render: function () {
            if ( this._isReadOnly() ) {
                this._readOnlySetting();
            }

            // 退会情報タブでは追加は不可
            this.$('td .glyphicon-plus-sign').hide();

            // utils.decoMinus( this, 'taikai_prc' );

            // // 既存なら削除は不可. 退会とする
            // if ( false ) {
            //     var msi_no = this.model.get('msi_no');
            //     var $ele = this.$('td .glyphicon-trash');
            //     if ( $.msiJqlib.isNullEx2(msi_no) ) { // 新規
            //         $ele.show();
            //     } else { // 既存
            //         $ele.hide();
            //     }
            // }

            return this;
        },

        _isReadOnly: function() {
            var appView = getAppView();
            if ( appView ) {
                if ( appView._isReadOnly() ) {
                    return true;
                }
            }
            return false;
        },

        _readOnlySetting: function() {
            this.$el.msiInputReadonly()
                .msiCalReadonly()
                .find('.my-readonly-hidden').hide();
            this.$el.find('.my-readonly-visible').show();
            this.$el.find('.dlg_pick_std:not(.no-bgc)').addClass('disable_etc');
        },

        _readOnlySettingClear: function() {
            this.$el.msiInputReadonlyUnset()
                .msiCalReadonlyUnset()
                .find('.my-readonly-hidden').show();
            this.$el.find('.my-readonly-visible').show();
            this.$el.find('.dlg_pick_std:not(.no-bgc)').removeClass('disable_etc');
        },

        delete: function() {
            this.model.destroy();
        },

        add: function(ev) {
            var _appView = getAppView();

            // shift を押した場合は上に追加
            var isUpside = ev.shiftKey ? true : false;
            // Ctrl を押した場合はコピー
            // var isCopy = (ev.ctrlKey || ev.metaKey) ? true : false;
            var isCopy = false; // コピー機能は抑制

            var msiCol = _appView.model.taikaiCol; // collection;
            var off = msiCol.indexOf(this.model); // this.model.get('line_no');
            var newModel, orgModel;

            if ( isCopy ) {
                orgModel = msiCol.get( this.model );
                newModel = orgModel.clone(); // shallow copy
                newModel.set( 'msi_no', null );
            } else {
                newModel = new gMyApp.TaikaiModel;
            }

            if ( !isUpside ) off = off +1;

            msiCol.add( newModel, {at: off} );
        },
    } ); // _TaikaiViewBase
    // console.log( '_TaikaiViewBase=>',  _TaikaiViewBase );
    gMyApp.TaikaiView = Backbone.View.extend( _TaikaiViewBase );

    gMyApp.TaikaiCollection =  Backbone.Collection.extend({
        model: TaikaiModel,

        initialize: function() {
            this.on( 'remove', this.adjItems );
            this.on( 'reset', this.adjItems );
            this.on( 'add', this.adjItems );
        },
        
        adjItems: function() {
            // 要素が１つもない場合は、１つだけ(入力用に)確保しておく
            if ( this.length <= 0 ) {
                var newModel = new TaikaiModel;
                this.add( newModel );
            }
            this.resetLineNo();
        },

        resetLineNo: function() {
            var i, max, m;
            for ( i = 0, max=this.length ; i < max ; i++ ) {
                m = this.at(i);
                m.set( 'line_no', i+1 );
            }
        },

        // model が最終要素なら真を返す
        isLastTaikaiModel: function(model) {
            var lastOff = this.length -1;
            var m = this.at(lastOff);
            var isLast = model === m;
            return isLast;
        },

        // 入力チェック
        validateEx: function () {
            var aMsg = [];
            var lastOff = this.length -1;
            this.each(function(m, i) {
                var msg, line, resLine;
                resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        msg = line + '行目:' + v;
                        aMsg.push( msg );
                    } );
                }
            });
            return aMsg;
        },
    }); // TaikaiCollection
    _.defaults( gMyApp.TaikaiCollection.prototype, mixin.ModelDataChangedMixin ); // データ変更確認機能付与

} );
