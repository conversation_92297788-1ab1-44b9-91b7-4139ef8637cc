function css_browser_selector(a){var b=a.toLowerCase();a=function(a){return-1<b.indexOf(a)};var d=document.documentElement;c=[!/opera|webtv/i.test(b)&&/msie\s(\d)/.test(b)?"ie ie"+RegExp.$1:a("firefox/2")?"gecko ff2":a("firefox/3.5")?"gecko ff3 ff3_5":a("firefox/3.6")?"gecko ff3 ff3_6":a("firefox/3")?"gecko ff3":a("gecko/")?"gecko":a("opera")?"opera"+(/version\/(\d+)/.test(b)?" opera"+RegExp.$1:/opera(\s|\/)(\d+)/.test(b)?" opera"+RegExp.$2:""):a("konqueror")?"konqueror":a("blackberry")?"mobile blackberry":
a("android")?"mobile android":a("chrome")?"webkit chrome":a("iron")?"webkit iron":a("applewebkit/")?"webkit safari"+(/version\/(\d+)/.test(b)?" safari"+RegExp.$1:""):a("mozilla/")?"gecko":"",a("j2me")?"mobile j2me":a("iphone")?"mobile iphone":a("ipod")?"mobile ipod":a("ipad")?"mobile ipad":a("mac")?"mac":a("darwin")?"mac":a("webtv")?"webtv":a("win")?"win"+(a("windows nt 6.0")?" vista":""):a("freebsd")?"freebsd":a("x11")||a("linux")?"linux":"","js"].join(" ");d.className+=" "+c;return c}css_browser_selector(navigator.userAgent);
