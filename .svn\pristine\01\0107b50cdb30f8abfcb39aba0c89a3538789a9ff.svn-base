{include file="fdn_head_std.tpl"}
{include file="fdn_header_12.tpl"}

<form action="#">

    {include file="header_info.tpl"}
    {include file="side_menu.tpl"}

    <form action="#">
        <div id="detail">
            <div id = "photo-div-wrapper" style="display: none">
                <ul class="tab">
                    <li id="first_li"><span>日立南{*ASKAネット*}</span></li>
                    <li id="second_li"><a href="javascript:void(0)">その他会館{*ASKAネット以外*}</a></li>
                </ul>
				<div id="photo-tab" class = "tab-contents">
					<fieldset class="print">
                        <span id="print_set" class="radio_set">
                            <label for="new_print" class="lbl_print" id="lbl_new_print">写真１</label><input name="print" id="new_print" type="radio" value="0"  />
                        </span>
                    </fieldset>
                </div>
                <div id="other-hall-tab" class = "tab-contents off">
                    <div id="ohter_hall" >
						<ul id="other_hall_list">
						{*その他会館(ASKAネット以外)*}
						<fieldset class="setting">
							<label for="hachusk_1" class="require hachusk done">発注先</label>
							<input name="hachusk_1" id="hachusk_1" type="text" class="txt borderDel width18" value="" />
							<div class="label dlg_staff dlg_hachusk"></div>
						</fieldset>
						<fieldset class="setting">
							<label for="s_todoke_date" class="lbl_s_todok my-require done" style="width:14%;">お届け日時</label>
							<input name="s_todoke_date" id="s_todoke_date" type="text" style="width:15%;" class="txt todoke_dt" value="" maxlength = "10" />
							<div class="label dlg_date"></div>
							<input  name="lbl_todoke_time" id="s_todoke_time" type="text" class="txt time" value="" maxlength = "5" />
							<div class="label dlg_time"></div>
						</fieldset>
						<fieldset class="setting grp_top">
							<label for="oh_kako_kbn" class="lbl_setting require done" id="lbl_oh_kako_kbn">加工方法</label>
							<span id="oh_kako_kbn_set" class="radio_set" data-ref_attr="s_kako_kbn">
							<label for="oh_kako_kbn_color" class="radio_label radio10">カラー</label><input name="oh_kako_kbn" id="oh_kako_kbn_color" type="radio" value="1" />
							<label for="oh_kako_kbn_mono"  class="radio_label radio10">白黒</label>  <input name="oh_kako_kbn" id="oh_kako_kbn_mono"  type="radio" value="0" />
							</span>
						</fieldset>
						<fieldset class="setting grp_middle">
							<label for="oh_kisekae_kbn" class="lbl_setting require done" id="lbl_oh_kisekae_kbn">着せ替え</label>
							<span id="oh_kisekae_kbn_set" class="radio_set" data-ref_attr="s_kisekae_kbn">
								<label for="oh_kisekae_kbn_wasou"    class="radio_label radio10">和装</label><input name="oh_kisekae_kbn" id="oh_kisekae_kbn_wasou"  type="radio" value="1" />
								<label for="oh_kisekae_kbn_yousou"   class="radio_label radio10">洋装</label><input name="oh_kisekae_kbn" id="oh_kisekae_kbn_yousou" type="radio" value="2" />
								<label for="oh_kisekae_kbn_nochange" class="radio_label radio10">衣装そのまま</label><input name="oh_kisekae_kbn" id="oh_kisekae_kbn_nochange" type="radio" value="0" />
							</span>
						</fieldset>
						<fieldset class="setting grp_middle">
							<label for="oh_haikei_kbn" class="lbl_setting option done" id="lbl_oh_haikei_kbn" style="width:14%;">背景</label>
							<span id="oh_haikei_kbn_set" class="radio_set" data-ref_attr="s_haikei_kbn">
								<label for="oh_haikei_kbn_keep"  class="radio_label radio10">背景そのまま</label><input name="oh_haikei_kbn" id="oh_haikei_kbn_keep" type="radio" value="1" />
								<label for="oh_haikei_kbn_erase" class="radio_label radio10">背景消し</label>    <input name="oh_haikei_kbn" id="oh_haikei_kbn_erase" type="radio" value="0" />
							</span>
						</fieldset>
						<fieldset class="setting grp_middle">
							<label for="kamon" class="lbl_kamon option done" style="width:14%;">家紋</label>
							<input name="oh_kamon" id="oh_kamon_nm" type="text" style="width:20%;" class="txt size_value" value="" />
						</fieldset>
						<fieldset class="setting grp_middle">
							<label  class="lbl_setting require done" id="lbl_tefuda">手札判</label>
							<span id="oh_tefuda_kbn_set" class="radio_set" data-ref_attr="s_tefuda_kbn">
								<label for="oh_tefuda_kbn_ari"   class="radio_label radio10">額有り</label><input name="oh_tefuda_kbn" id="oh_tefuda_kbn_ari"   type="radio" value="1" />
                                                                <label for="oh_tefuda_kbn_nashi" class="radio_label radio10">額無し</label><input name="oh_tefuda_kbn" id="oh_tefuda_kbn_nashi" type="radio" value="0" />
								<input name="oh_tefuda_cnt" id="oh_tefuda_cnt" type="text" class="txt size_value" value="" />
							</span>
						</fieldset>
						<fieldset class="setting grp_bottom">
							<label for="oh_output_kbn" class="lbl_setting require done" id="lbl_oh_output_kbn">出力</label>
							<span id="oh_output_kbn_set" class="radio_set" data-ref_attr="s_output_kbn">
								<label for="oh_output_kbn_a1" class="radio_label radio10">Ａ１</label><input name="oh_output_kbn" id="oh_output_kbn_a1" type="radio" value="1" />
								<label for="oh_output_kbn_no" class="radio_label radio10">無し</label><input name="oh_output_kbn" id="oh_output_kbn_no" type="radio" value="0" />
							</span>
						</fieldset>
						<fieldset class="memo">
							<label for="memo" class="lbl_memo option">備考</label>
							<textarea name="photo_memo" id="oh_memo" class="txt" cols="50" rows="10" maxlength="60"></textarea>
						</fieldset>
						</ul>
					</div><!--other_hall-->
				</div><!--other-hall-tab-->
                <div class="buttons">
                    <input type="button" name="btn_save" id="btn_save" value="保存" />
                    <input type="button" name="btn_delete" id="btn_delete" value="削除" />
                    <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
                </div>
            </div>
        </div>
    </form>

    {literal}
        <!-- 写真加工テンプレート -->
        <script type="text/template" id="tmpl-photo">
			<fieldset class="base_1">
			<label for="hachusk_2" class="require hachusk done">発注先</label>
			<input name="hachusk_2" id="hachusk_2" type="text" class="txt borderDel width18" value="" />
			<div class="label dlg_staff dlg_hachusk2"></div>
            </fieldset>
            <fieldset class="base_1">
            <label for="yakimashi" class="lbl_yakimashi option done" id="lbl_yakimashi">新規・焼増</label>
            <input type="hidden" name="photo_copy_1" id="photo_copy_1" class="cls_photo_copy"/>
            </fieldset>
            <fieldset class="base_1">
            <label for="input_bumon_cd" class="lbl_input_bumon_cd done">写真取込先</label>
            <input name="input_bumon_cd" id="input_bumon_cd" type="hidden"  value="" class="cls_input_bumon_cd"/>
            </fieldset>
            <fieldset class="base_1">
            <label for="output_bumon_cd" class="lbl_output_bumon_cd done">写真出力先</label>
            <input name="output_bumon_cd" id="output_bumon_cd" type="hidden" value="" class="cls_output_bumon_cd"/>
            </fieldset>

            <fieldset class="print_schedule">
            <label for="print_date" class="lbl_print_date require done" id="lbl_print_datetime">出力希望時間</label>
            <input name="print_date" id="print_date" type="text" class="txt photo_date" value="" />
            <div class="label dlg_date"></div>
            <input name="print_time" id="print_time" type="text" class="txt time" value="" />
            <div class="label dlg_time"></div>
            <label for="print_date_2" class="lbl_print_date require done" id="lbl_print_date_2">葬儀</label>
            <div class="label" id="print_date_2"></div>
            </fieldset>
            <fieldset class="setting" id="setting_1">
            <label for="color_type" class="lbl_setting require done" id="lbl_color_type">加工方法</label>
            <span id="color_type_set" class="radio_set">
            <label for="color" class="lbl_color" id="lbl_color_color">カラー</label><input name="color_type" id="color" type="radio" value="0" />
            <label for="mono" class="lbl_color" id="lbl_color_mono">白黒</label><input name="color_type" id="mono" type="radio" value="1" />
            </span>
            <label for="trim_type" class="lbl_setting require done" id="lbl_trim_size">切り抜きサイズ</label>
            <span id="trim_size_set" class="radio_set">
            <label for="trim_a" class="lbl_trim">A</label><input name="trim_type" id="trim_a" type="radio" value="0" />
            <label for="trim_b" class="lbl_trim">B</label><input name="trim_type" id="trim_b" type="radio" value="1" />
            <label for="trim_c" class="lbl_trim">C</label><input name="trim_type" id="trim_c" type="radio" value="2" />
            </span>
            <div id="photo_pic_back"></div>
            <div id="photo_pic"></div>
            </fieldset>
            <fieldset class="setting" id="setting_2">
            <label for="change_cloth" class="lbl_setting require done" id="lbl_change_cloth">着せ替えは？</label>
            <span id="change_cloth_set" class="radio_set">
            <label for="change" class="lbl_change_cloth" id="lbl_cloth_change">する</label><input name="change_cloth" id="change" type="radio" value="1" />
            <label for="nochange" class="lbl_change_cloth" id="lbl_cloth_no_change">しない</label><input name="change_cloth" id="nochange" type="radio" value="0" />
            </span>
            <div id="bg_setting_set" class="radio_set">
            <label for="bg_setting" class="lbl_setting option done" id="lbl_bg_setting">背景は？</label>
            <label for="bg_hide" class="lbl_bg_setting" id="lbl_bg_hide">消す</label><input name="bg_setting" id="bg_hide" type="radio" value="0" />
            <label for="bg_show" class="lbl_bg_setting" id="lbl_bg_show">そのまま</label><input name="bg_setting" id="bg_show" type="radio" value="1" />
            </div>
            </fieldset>
            <fieldset class="setting" id="setting_3">
            <div class="lbl_setting subtitle option done" id="lbl_cloth_type">服は？</div>
            <div id="cloth_type_set" class="radio_set cloth_set">
            <label for="japanese_cloth" class="lbl_cloth_type" id="lbl_japanese_cloth">和服</label><input name="cloth_type" id="japanese_cloth" type="radio" value="0" />
            <label for="foreign_cloth" class="lbl_cloth_type" id="lbl_foreign_cloth">洋服</label><input name="cloth_type" id="foreign_cloth" type="radio" value="1" />
            </div>
            <input name="cloth_name" id="cloth_name" type="text" class="txt" value="" readonly = "readonly"/>
            <div class="label dlg_cloth cursor-pointer" id="lbl_dlg_cloth"></div>
            <label for="bg_name" class="lbl_setting option done" id="lbl_bg_name">背景の種類は？</label>
            <input name="bg_name" id="bg_name" type="text" class="txt" value="" readonly = "readonly"/>
            <div class="label dlg_bg cursor-pointer" id="lbl_dlg_bg"></div>
            </fieldset>
            <fieldset class="size" id="size_4">
            <label for="photo_size_1" class="lbl_size option done" id="lbl_photo_size_1">四つ切</label>
            <input type="hidden" name="photo_copy_1" id="photo_copy_1" class="cls_photo_copy" style = "display:none;"/>
            <input type="hidden" name="photo_size_1" id="photo_size_1" class="cls_photo_size"/>
            <label for="photo_size_2" class="lbl_size option done" id="lbl_photo_size_2">手札</label>
            <input type="hidden" name="photo_copy_2" id="photo_copy_2" class="cls_photo_copy" style = "display:none;"/>
            <input type="hidden" name="photo_size_2" id="photo_size_2" class="cls_photo_size"/>
            </fieldset>
            <fieldset class="size" id="size_5">
            <label for="photo_size_3" class="lbl_size option done" id="lbl_photo_size_3">指定：</label>
            <input name="photo_size_3_width" id="photo_size_3_width" type="text" class="txt size_value" value="" />
            <div class="label size_x">×</div>
            <input name="photo_size_3_height" id="photo_size_3_height" type="text" class="txt size_value" value="" />
            <input type="hidden" name="photo_size_3" id="photo_size_3" class="cls_photo_size"/>
            <input name="photo_size_4_width" id="photo_size_4_width" type="text" class="txt size_value" value="" />
            <div class="label size_x">×</div>
            <input name="photo_size_4_height" id="photo_size_4_height" type="text" class="txt size_value" value="" />
            <input type="hidden" name="photo_size_4" id="photo_size_4" class="cls_photo_size"/>
            </fieldset>

            <fieldset class="panel" id="panel_1">
            <label for="panel_type" class="lbl_panel_type require done" id="lbl_panel_type">切り抜きサイズ</label>
            <span id="panel_type_set" class="radio_set">
            <label for="no_panel" class="lbl_panel" id="lbl_no_panel" >不要</label><input name="panel_type" id="no_panel" type="radio" value="0" />
            <label for="panel_size_1" class="lbl_panel" id="lbl_panel_size_1" style = "width:16%;">A</label><input name="panel_type" id="panel_size_1" type="radio" value="1" />
            <label for="panel_size_2" class="lbl_panel" id="lbl_panel_size_2" style = "width:16%;">B</label><input name="panel_type" id="panel_size_2" type="radio" value="2" />
            <label for="panel_size_3" class="lbl_panel" id="lbl_panel_size_3" style = "width:15%;">C</label><input name="panel_type" id="panel_size_3" type="radio" value="3" />
            </span>
            </fieldset>

            <fieldset class="panel" id="panel_3" style = "display:none;">
            <label class="lbl_panel_type require done" id="lbl_panel_price">電飾写真金額</label>
            <label class="label lbl_panel_3" id="lbl_panel_no_price"></label>
            <label class="label lbl_panel_3" id="lbl_panel_price_1" style = "width:16%;"></label>
            <label class="label lbl_panel_3" id="lbl_panel_price_2" style = "width:16%;"></label>
            <label class="label lbl_panel_3" id="lbl_panel_price_3" style = "width:15%;"></label>
            </fieldset>
            <fieldset class="panel" id="panel_4">
            <label for="panel_deliv_date" class="lbl_panel_deliv require done" id="lbl_panel_deliv">電飾写真到着期限</label>
            <input name="panel_deliv_date" id="panel_deliv_date" type="text" class="txt photo_date" value="" />
            <div class="label dlg_date"></div>
            <input name="panel_deliv_time" id="panel_deliv_time" type="text" class="txt time" value="" />
            <div class="label dlg_time"></div>
            </fieldset>
            <fieldset class="case">
            <label for="photo_condition" class="lbl_case require done" id="lbl_photo_condition">取り込んだお写真の状態が良くない場合は？</label>
            <span id="answer_set" class="radio_set">
            <label for="answer_1" class="lbl_answer" id="lbl_answer_1">電話で連絡がほしい</label><input name="answer" id="answer_1" type="radio" value="0" />
            <label for="answer_2" class="lbl_answer" id="lbl_answer_2">この写真しかないので連絡は不要</label><input name="answer" id="answer_2" type="radio" value="1" />
            </span>
            </fieldset>
            <fieldset class="memo">
            <label for="memo" class="lbl_memo option">備考</label>
            <textarea name="photo_memo" id="photo_memo" class="txt" cols="50" rows="10" maxlength="60"></textarea>
            </fieldset>
        </script>
    {/literal}
    <script id="data-photo" type="application/json">
        {$press_json|smarty:nodefaults}
    </script>

    {include file="fdn_footer_std.tpl"}