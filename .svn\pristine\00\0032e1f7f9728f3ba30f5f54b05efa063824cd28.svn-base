(function ($) {
    "use strict";

    // dialog 終了処理
    var endDialog;

    // 条件 clear 処理
    var clearFunc;

    // 通夜日の日付ダイアログ
    $("#s_sougi_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
    $(".dlg_datexx").on("click", function (e) {
        var $target = $(e.currentTarget).prev("input");
        $target.datepicker("show");
    });
    // 検索実行
    var searchFunc = function (e, offset) {
        if (offset === undefined)
            offset = 0;
        // 次ボタン等クリア
        $('#btn_search_next').removeAttr('data-offset').css('visibility', 'hidden'); // hide();
        $('#btn_search_prev').removeAttr('data-offset').css('visibility', 'hidden'); // hide();

        var mitsu_st = $('#celemony_dialog #s_mitsu_st').val();
        var mitsu_ed = $('#celemony_dialog #s_mitsu_ed').val();
        var shinzoku_st = $('#celemony_dialog #s_shinzoku_st').val();
        var shinzoku_ed = $('#celemony_dialog #s_shinzoku_ed').val();
        var kaiso_st = $('#celemony_dialog #s_kaiso_st').val();
        var kaiso_ed = $('#celemony_dialog #s_kaiso_ed').val();
        $('#celemony_dialog #s_mitsu_st, #s_mitsu_ed').removeClass("error1");
        if (!$.msiJqlib.isNullEx2(mitsu_st)) {
            mitsu_st = mitsu_st.replace(/,/g, '');
            if (!$.isNumeric(mitsu_st)) {
                $('#celemony_dialog #s_mitsu_st').addClass("error1");
                return;
            }
        }
        if (!$.msiJqlib.isNullEx2(mitsu_ed)) {
            mitsu_ed = mitsu_ed.replace(/,/g, '');
            if (!$.isNumeric(mitsu_ed)) {
                $('#celemony_dialog #s_mitsu_ed').addClass("error1");
                return;
            }
        }
        $('#celemony_dialog #s_shinzoku_st, #s_shinzoku_ed').removeClass("error1");
        if (!$.msiJqlib.isNullEx2(shinzoku_st)) {
            shinzoku_st = shinzoku_st.replace(/,/g, '');
            if (!$.isNumeric(shinzoku_st)) {
                $('#celemony_dialog #s_shinzoku_st').addClass("error1");
                return;
            }
        }
        if (!$.msiJqlib.isNullEx2(shinzoku_ed)) {
            shinzoku_ed = shinzoku_ed.replace(/,/g, '');
            if (!$.isNumeric(shinzoku_ed)) {
                $('#celemony_dialog #s_shinzoku_ed').addClass("error1");
                return;
            }
        }
        $('#celemony_dialog #s_kaiso_st, #s_kaiso_ed').removeClass("error1");
        if (!$.msiJqlib.isNullEx2(kaiso_st)) {
            kaiso_st = kaiso_st.replace(/,/g, '');
            if (!$.isNumeric(kaiso_st)) {
                $('#celemony_dialog #s_kaiso_st').addClass("error1");
                return;
            }
        }
        if (!$.msiJqlib.isNullEx2(kaiso_ed)) {
            kaiso_ed = kaiso_ed.replace(/,/g, '');
            if (!$.isNumeric(kaiso_ed)) {
                $('#celemony_dialog #s_kaiso_ed').addClass("error1");
                return;
            }
        }
        $.ajax({
            url: $.msiJqlib.baseUrl() + '/mref/sekodialog/search',
            type: 'GET',
            data: {
                s_number: $('#celemony_dialog #s_number').val(),
                s_code_1: $('#celemony_dialog #s_code_1').val(),
                s_code_2: $('#celemony_dialog #s_code_2').val(),
                s_code_3: $('#celemony_dialog #s_code_3').val(),
                s_tel: $('#celemony_dialog #s_tel').val(),
                s_staff_1: $('#celemony_dialog #s_staff_1').val(),
                s_apply: $('#celemony_dialog #s_apply').val(),
                s_ontime: $('#celemony_dialog #s_ontime:checked').val(),
                s_name_1: $('#celemony_dialog #s_name_1').val(),
                s_name_2: $('#celemony_dialog #s_name_2').val(),
                s_name_3: $('#celemony_dialog #s_name_3').val(),
                s_name_4: $('#celemony_dialog #s_name_4').val(),
                s_name_5: $('#celemony_dialog #s_name_5').val(),
                s_name_kana_1: $('#celemony_dialog #s_name_kana_1').val(),
                s_name_kana_2: $('#celemony_dialog #s_name_kana_2').val(),
                s_name_kana_3: $('#celemony_dialog #s_name_kana_3').val(),
                s_staff_2: $('#celemony_dialog #s_staff_2').val(),
                s_year: $('#celemony_dialog #s_year').val(),
                s_month: $('#celemony_dialog #s_month').val(),
                s_bumon: $('#celemony_dialog #s_bumon').val(),
                s_addr: $('#celemony_dialog #s_addr').val(), // 2016/06/13 ADD
                s_sougi_ymd: $('#celemony_dialog #s_sougi_ymd').val(), // 2016/06/14 ADD
                s_sort: $('#celemony_dialog #s_sort').val(),
                s_gojokai: $('#celemony_dialog #s_gojokai').val(),
                s_gojokai2: $('#celemony_dialog #s_gojokai2').val(),
                s_gojokai3: $('#celemony_dialog #s_gojokai3').val(),
                s_gojokai4: $('#celemony_dialog #s_gojokai4').val(),
                s_gojokai5: $('#celemony_dialog #s_gojokai5').val(),
                s_mitsu_st: $('#celemony_dialog #s_mitsu_st').val(),
                s_mitsu_ed: $('#celemony_dialog #s_mitsu_ed').val(),
                s_shinzoku_st: $('#celemony_dialog #s_shinzoku_st').val(),
                s_shinzoku_ed: $('#celemony_dialog #s_shinzoku_ed').val(),
                s_kaiso_st: $('#celemony_dialog #s_kaiso_st').val(),
                s_kaiso_ed: $('#celemony_dialog #s_kaiso_ed').val(),
                s_status_kbn: $('#celemony_dialog #s_status_kbn').val(),
                limit: 15,
                offset: offset,
            },
            dataType: 'html',
            success: function (html) {
                // console.log( html );
                $('#celemony_dialog .result').replaceWith(html);

                var _mydataCtl = msiLib2.getJsonFromHtml($('#msi-dialog-ctldata-json'));
                // console.log( 'msi-dialog-ctldata-json =>' + JSON.stringify(_mydataCtl) );
                if (_.has(_mydataCtl, 'next_offset') && _.isNumber(_mydataCtl.next_offset) && _mydataCtl.next_offset > 0) {
                    $('#btn_search_next').attr('data-offset', _mydataCtl.next_offset).css('visibility', 'visible'); // show();
                }
                if (_.has(_mydataCtl, 'prev_offset') && _.isNumber(_mydataCtl.prev_offset) && _mydataCtl.prev_offset >= 0) {
                    $('#btn_search_prev').attr('data-offset', _mydataCtl.prev_offset).css('visibility', 'visible'); // show();
                }

                setTimeout(function () {
                    $(window).trigger("resize");
                }, 0);
            }
            // error処理は共通設定を使う
        });
    };

    // 施行選択 実処理
    var _selectFunc = function (seko_no) {
        // console.log( '**** SELECT seko_no=>' + seko_no );
        $.ajax({
            url: msiLib2.baseUrl() + '/mref/sekodialog/setsekono',
            type: 'POST',
            dataType: 'json',
            data: {
                seko_no: seko_no,
                path: msiLib2.urlPath(location.href)
            },
            success: function (data) {
                // console.log( data );
                setTimeout(function () {
                    endDialog();
                }, 0);
                // $('#celemony_dialog').fadeOut(400, function() { $(this).remove(); } );
                if ($.msiJqlib.celemonyDialogOnSelect) { // コールバックが別途設定されている場合はこちらを使う
                    $.msiJqlib.celemonyDialogOnSelect.apply(this, [data]);
                    $.msiJqlib.celemonyDialogOnSelect = null;
                    return;
                }
                if (data.status == 'OK') {
                    if (data.area_cd) {
                        $('#area_cdnm').text(data.area_cd + ' ' + data.area_nm);
                    } else {
                        $('#area_cdnm').text('');
                    }
                    if (data.hall_cd) {
                        $('#hall_cd').select2('val', data.hall_cd);
                    } else {
                        $('#hall_cd').select2('val', '');
                    }
                    if (data.seko_no) {
                        $('#seko_no_id').text(data.seko_no);
                    } else {
                        $('#seko_no_id').text('');
                    }
                    if (true || location.href.match(/\bcustomerinfo\b/)) { // XXX
                        // location.reload();
                        var url = location.href;
                        url = url.replace(/\/\bsn[\/=](\d+)?\b/, ''); // seko_no パラメタ削除
                        url = url.replace(/\/\bsns[\/=](\d+)?\b/, ''); // seko_no_sub パラメタ削除
                        url = url.replace(/\/+$/, ''); // 末尾の / 削除
                        url = url + '/sn/' + seko_no;
                        location.href = url;
                    } else {
                        $.msiJqlib.showInfo(data.msg);
                    }
                } else {
                    $.msiJqlib.showErr(data.msg);
                }
            }
            // error処理は共通設定を使う
        });
    };

    $(function () {

        // msiパーツの有効化 
        msiLib2.msiPrepareParts('#celemony_dialog');

        // dialog 終了処理
        endDialog = function () {
            $(document).off('keydown', upDownArrowClick)
                    .off('click', checkOuterClick)
                    .off('keydown', checkEscKey);
            $('#celemony_dialog').fadeOut(400, function () {
                $(this).remove();
            });
        };

        // 条件 clear 処理
        clearFunc = function () {
            var init_search = 0;
            $.ajax({
                url: msiLib2.baseUrl() + '/mref/sekodialog/clearcond',
                type: 'GET',
                data: {
                    init_search: init_search,
                },
                dataType: 'html',
                success: function (html) { // cf. msiLib2.js
                    // console.log( html );
                    $('#is_init_search_flg').val(init_search);
                    $('#celemony_dialog').remove();
                    $(html).appendTo($('#wrapper')).fadeIn('slow');
                }
            });
        };

        // 各種イベント設定
        $('#celemony_dialog').on('mouseover',
                '.msi-dialog-selection',
                function () {
                    var seko_no = $(this).attr('data-seko_no');
                    $('.msi-dialog-selection').find('td').removeClass('my-hover');
                    $(".seko_no_" + seko_no).find('td').addClass('my-hover')
                            .end().find('a').focus();
                })
                .on('mouseout', '.msi-dialog-selection',
                        function () {
                            var seko_no = $(this).attr('data-seko_no');
                            $(".seko_no_" + seko_no).find('td').removeClass('my-hover');
                        })
                .on('click', '#close_celemony_search, #close_celemony_dialog', endDialog)
                .on('click', '#btn_search', searchFunc)
                .on('click', '#btn_clear', clearFunc)
                .on('click', '#btn_search_prev',
                        function (e) {
                            var $ele = $('#btn_search_prev'),
                                    offset = $ele.attr('data-offset');
                            searchFunc(e, offset);
                        })
                .on('click', '#btn_search_next',
                        function (e) {
                            var $ele = $('#btn_search_next'),
                                    offset = $ele.attr('data-offset');
                            searchFunc(e, offset);
                        })
                .on('keydown', '.search',
                        function (e) {
                            // console.log( 'keydown e.keyCode=>' + e.keyCode );
                            if (e.keyCode != 13)
                                return;
                            searchFunc(e);
                        })
                .on('focus', '.msi-dialog-selection',
                        function (e) {
                            var focus_id = $(e.target).attr('id'), seko_no;
                            // console.log( '**** focus id=>' + focus_id );
                            $('.msi-dialog-selection').find('td').removeClass('my-hover');
                            if (focus_id && focus_id.indexOf('a_seko_no_') === 0) {
                                seko_no = focus_id.substr(10);
                                $(".seko_no_" + seko_no).find('td').addClass('my-hover');
                            }
                        })
                .on('focusout', '.msi-dialog-selection',
                        function (e) {
                            var focus_id = $(e.target).attr('id'), seko_no;
                            // console.log( '**** focusout id=>' + focus_id );
                            if (focus_id && focus_id.indexOf('a_seko_no_') === 0) {
                                seko_no = focus_id.substr(10);
                                $(".seko_no_" + seko_no).find('td').removeClass('my-hover');
                            }
                        })
                .on('click', '.msi-dialog-selection',
                        function (e) {
                            var focus_id = $(':focus').attr('id'), seko_no;
                            if (focus_id && focus_id.indexOf('a_seko_no_') === 0) {
                                seko_no = focus_id.substr(10);
                                _selectFunc(seko_no);
                                return;
                            }
                            seko_no = $(this).attr('data-seko_no');
                            _selectFunc(seko_no);
                        });

        // 上下矢印 移動処理
        var upDownArrowClick = function (e) {
            var $tgt, seko_no, isUp, $els, i, $e, e_seko_no, bgn, end, step;
            // 38(up arrow) 40(down arrow)
            if (e.keyCode == 38) {
                isUp = true;
            } else if (e.keyCode == 40) {
                isUp = false;
            } else {
                return;
            }

            $tgt = $('tr.msi-dialog-selection :focus');
            if ($tgt.length) {
                seko_no = $tgt.attr('id').substr(10); // a_seko_no_*
                // console.log( 'upDownArrowClick **** seko_no=>' + seko_no );
                if (seko_no) {
                    $els = isUp ? $tgt.closest('tr').prevAll() : $tgt.closest('tr').nextAll();
                    $els.each(function () {
                        $e = $(this), e_seko_no = $e.attr('data-seko_no');
                        if (e_seko_no != seko_no) {
                            if ($e.find('a')) {
                                // console.log( 'upDownArrowClick e_seko_no=>' + e_seko_no );
                                e.stopImmediatePropagation();
                                $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                                // $e.trigger('mouseover');
                                return false;
                            }
                        }
                    });
                }
            } else { // focus がないとき
                $els = $('tr.msi-dialog-selection');
                if ($els.length) {
                    if ($els.length === 1) {
                        $i = 0;
                        $e = $els.eq(i);
                        e_seko_no = $e.attr('data-seko_no');
                        if (e_seko_no && $e.find('a')) {
                            e.stopImmediatePropagation();
                            $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                            return false;
                        }
                        return;
                    }
                    if (isUp) {
                        bgn = $els.length - 1, end = 0, step = -1;
                    } else {
                        bgn = 0, end = $els.length - 1, step = 1;
                    }
                    for (i = bgn; i != end; i = i + step) {
                        $e = $els.eq(i);
                        e_seko_no = $e.attr('data-seko_no');
                        if (e_seko_no && $e.find('a')) {
                            e.stopImmediatePropagation();
                            $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                            return false;
                        }
                    }
                }
            }
        };

        // 領域外クリック時に閉じる処理
        // cf. jquery.outerclick.js
        // NG.  $('#celemony_dialog .dialog_body').outerclick( function (e) {
        // patch 的対応 XXX
        var $elementTarget = $('#celemony_dialog');
        var checkOuterClick = function (e) {
            // 通常の Select だとこのイベントを拾ってしまう
            // select2 を使うこと XXX
            // console.log( 'target=>' + e.target + ' currentTarget=>' + e.currentTarget  + ' target.id=>' + $(e.target).attr('id') );
            if (e.target == '[object HTMLSelectElement]' || e.target == '[object HTMLOptionElement]'
                    || $(e.target).attr('id') === 's_ontime' // IE patch
                    ) {
                // console.log( 'select element' );
                return;
            }
            var
                    clickX = e.pageX,
                    clickY = e.pageY,
                    offsetElementTarget = $elementTarget.offset(),
                    offsetTopElementTarget = offsetElementTarget.top,
                    offsetBottomElementTarget = offsetTopElementTarget + $elementTarget.height(),
                    offsetLeftElementTarget = offsetElementTarget.left,
                    offsetRightElementTarget = offsetLeftElementTarget + $elementTarget.width();
            // cf. main.css
            // #celemony_dialog .dialog_body {
            //   top: 3%;
            //   left: 3%;
            var marginH = Math.floor(offsetBottomElementTarget * 0.03);
            var marginV = Math.floor(offsetRightElementTarget * 0.03);
            if (marginH < 10 || marginV < 10)
                return; // #celemony_dialog が非表示
            offsetTopElementTarget = marginH;
            offsetBottomElementTarget = offsetBottomElementTarget - marginH;
            offsetLeftElementTarget = marginV;
            offsetRightElementTarget = offsetRightElementTarget - marginV;
            // console.log( [clickX, clickY, offsetElementTarget, offsetTopElementTarget,
            //               offsetBottomElementTarget, offsetLeftElementTarget, offsetRightElementTarget,
            //              marginH, marginV ].join(', ') );
            if (!(
                    clickY > offsetTopElementTarget &&
                    clickY < offsetBottomElementTarget &&
                    clickX > offsetLeftElementTarget &&
                    clickX < offsetRightElementTarget
                    )) {
                endDialog();
            }
            ;
        };

        // ESC キーで閉じる処理
        var checkEscKey = function (e) {
            // console.log( 'keydown e.keyCode=>' + e.keyCode );
            if (e.keyCode == 27) {
                endDialog();
            }
        };

        // 広域イベント捕捉
        $(document).on('keydown', upDownArrowClick)
                .on('click', checkOuterClick)
                .on('keydown', checkEscKey);

        // スクロールバー調整
        // リサイズ処理
        $(window).on('resize',
                function () {
                    var _myId = '#celemony_dialog';
                    var $list = $(_myId + ' .result .list'),
                            $header = $(_myId + ' .result .head'),
                            sc_of,
                            hh;
                    // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
                    if ($list.length < 1)
                        return;
                    if ($list[0].scrollHeight === $list[0].clientHeight) {
                        sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                        $list.css("overflow-y", sc_of);
                        $header.css("overflow-y", sc_of);
                    } else {
                        sc_of = 'scroll';
                        hh = $header.height();
                        $list.css("overflow-y", sc_of);
                        $header.css("overflow-y", sc_of);
                        // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                        $header.height(hh); // for Chrome. XXX
                    }
                });

        // 初期フォーカス設定等
        setTimeout(function () {
            if (!msiLib2.iPadPatch()) {
                $('#s_number').focus();
            }
            $(window).trigger("resize");

            if ($('#is_init_search_flg').val() == 1) {
                searchFunc();
            }
        }, 0);

    });

})(jQuery);
