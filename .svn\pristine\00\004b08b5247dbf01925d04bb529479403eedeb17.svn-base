<table>
    {foreach from=$list_data|smarty:nodefaults item=myrec name=loop}
    <!-- tr class="{$myrec.oddEven} msi-dialog-selection myid_{$myrec.myid}" data-myid="{$myrec.myid}" -->
    <tr class="{cycle values='odd,even'} msi-dialog-selection myid_{$myrec.myid}" data-myid="{$myrec.myid}">
        <td class="seikyu_ymd text-center"      style="width:7%">{$myrec.seikyu_ymd}</td>
        <td class="daicho_no_eria text-center"  style="width:6%" >{$myrec.daicho_no_eria}</td>
        <td class="souke_nm text-center"        style="width:5%" >{$myrec.souke_nm}</td>
        <td class="seikyu_kbn_nm"               style="width:5%">{$myrec.seikyu_kbn_nm}</td>
        <td class="code text-center"            style="width:7%"><a href="javascript:void(0)" id="a_myid_{$myrec.myid}">{$myrec.code}</a></td>
        <td class="sekyu_nm"                    style="width:12%">{$myrec.sekyu_nm}</td>
        <td class="seikyu_zei_prc text-right"   style="width:7% {if $myrec.seikyu_zei_prc < 0};color:red{/if}">{$myrec.seikyu_zei_prc}</td>
        <td class="nyukin_prc text-right"       style="width:7% {if $myrec.nyukin_prc < 0};color:red{/if}">{$myrec.nyukin_prc}</td>
        <td class="seikyu_zan text-right"       style="width:7% {if $myrec.seikyu_zan < 0};color:red{/if}">{$myrec.seikyu_zan}</td>
        <td class="nafuda_nm"                   style="width:20%">{$myrec.nafuda_nm}</td>
        <td class="tanto_nm"                    style="width:8%">{$myrec.tanto_nm}</td>
        <td class="bumon_nm"                    style="width:10%">{$myrec.bumon_nm}</td>
    </tr>
    {/foreach}
</table>
