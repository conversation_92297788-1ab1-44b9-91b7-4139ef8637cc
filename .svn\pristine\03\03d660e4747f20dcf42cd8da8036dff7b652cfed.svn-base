<?php

/**
 * Kan<PERSON>_UrinyucsvController
 *
 * 売上・入金CSVデータダウンロード　コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Okuyama
 * @since      2017/05/11
 * @filesource 
 */

/**
 * 売上・入金CSVデータダウンロード コントローラクラス
 *
 * @category   App
 * @package    controller\kanri
 * <AUTHOR> Sato
 * @since      2017/05/11
 */
class Kanri_UrinyucsvController extends Msi_Zend_Controller_Action {

    private static $title_uri         = '売上データ';
    private static $title_uri_mesai   = '売上明細データ';
    private static $title_nyukin      = '入金データ';
    private static $title_kaihi_sougi = '会費利用_葬儀_データ';
    private static $title_kaihi_other = '会費利用_葬儀以外_データ';
    private static $title_tanto       = '担当者別売上集計データ';
    private static $title_bumon       = '部門別葬儀売上集計データ';
    private static $bindings = array(
        '#output_kbn' => 'output_kbn',  // 出力区分
        '.useform'    => 'useform',     // 利用形態
    );
    // 出力区分
    private static $output_kbn_ary = array(
        array('id' => 1, 'text' => '売上データ'),
        array('id' => 2, 'text' => '売上明細データ'),
//        array('id' => 3, 'text' => '入金データ'),
//        array('id' => 4, 'text' => '会費利用（葬儀）データ'),
//        array('id' => 5, 'text' => '会費利用（葬儀以外）データ'),
//        array('id' => 6, 'text' => '担当者別売上集計データ'),
        array('id' => 7, 'text' => '部門別葬儀売上集計データ'),
    );

    /**
     * 
     * 売上・入金CSVデータダウンロード コントローラクラス
     *
     * @category   App
     * @package    controller\kanri
     * <AUTHOR> Okuyama
     * @since      2017/05/11
     */
    public function mainAction() {
        $params = Msi_Sys_Utils::webInputs();
        $db = Msi_Sys_DbManager::getMyDb();
        $bumon = DataMapper_BumonMst::find($db,array('bumon_kbn' => 2));
        $bumon_list = $this->setBumonList($bumon);
        $_dataApp = $this->_defaultDataApp();
        $data = array(
            'dataApp' => $_dataApp,
            'bindings' => self::$bindings,
            'select2DataAry' => array(
                array('id' => '#output_kbn', 'data' => self::$output_kbn_ary),
                array('id' => '#bumon_cd'  , 'data' => $bumon_list)
            ),
            'status' => 'OK',
            'msg' => ''
        );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
        App_Smarty::pushCssFile(['app/kanri.urinyucsv.css']);
        App_Smarty::pushJsFile(['app/kanri.urinyucsv.js']);
    }
    /**
     * 部門情報
     * 
     * @param type $bumon
     * @return type
     */
    private function setBumonList($bumon){
        $dataCol = array();
        foreach ( $bumon as $rec ) {
            $newRec = Msi_Sys_Utils::remapArrayFlat( $rec,<<< END_OF_TXT
bumon_cd
bumon_lnm
END_OF_TXT
                ,array(
                    'bumon_cd'  => 'bumon_cd',
                    'bumon_lnm' => 'bumon_nm'
                ));
            $dataCol[] = $newRec;
        }
        $result = array();
        foreach ($dataCol as $key => $value) {
            $result[] = array('id' => $value['bumon_cd'], 'text' => $value['bumon_nm']);
        }
        return $result;
    }
    
    /**
     * 初期値を返す
     *
     * <AUTHOR> Okuyama
     * @since  2017/05/11
     */
    protected function _defaultDataApp() {
        //対象期間開始日（システム日付の前月の月初日）
        $data_st = date('Y/m/d', strtotime(date('Y-m-1') . ' -1 month'));
        //対象期間終了日（システム日付の前月の月末日）
        $data_ed = date('Y/m/t', strtotime(date('Y-m-1') . ' -1 month'));
        $dataApp = array(
            'taisho_st_ymd' => $data_st, // 対象年月日（自）
            'taisho_ed_ymd' => $data_ed, // 対象年月日（至）
            'output_kbn'    => 1,        // 出力区分（初期表示は売上データ）
            'useform'       => array('1', '2', '3', '4'),
        );
        return $dataApp;
    }

    /**
     * 売上・入金CSVデータ作成   アクション
     *
     * <AUTHOR> Okuyama
     * @since 2017/05/11
     */
    public function makeAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $ymd_st = null;
        $ymd_ed = null;

        $db = Msi_Sys_DbManager::getMyDb();
        
        // 対象期間（自）
        if (trim($dataAppAry['taisho_st_ymd']) == '') {
            Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => "対象期間（自）は必須です。"));
            return;
        }
        $ymd_st = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($dataAppAry['taisho_st_ymd']), 'DATE2');
        if (!$ymd_st) {
            Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => "対象期間（自）が正しくありません。"));
            return;
        }

        // 対象期間（至）
        if (trim($dataAppAry['taisho_ed_ymd']) == '') {
            Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => "対象期間（至）は必須です。"));
            return;
        }
        $ymd_ed = Msi_Sys_Utils::checkVar(Msi_Sys_Utils::emptyToNull($dataAppAry['taisho_ed_ymd']), 'DATE2');
        if (!$ymd_ed) {
            Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => "対象期間（至）が正しくありません。"));
            return;
        }

        // データ区分
        $data_kbn_ary = null;
        if (array_key_exists('useform', $dataAppAry)) {
            $data_kbn_ary = $dataAppAry['useform'];
        }    
        
        // 出力区分
        $output_kbn = null;
        if (array_key_exists('output_kbn', $dataAppAry)) {
            $output_kbn = $dataAppAry['output_kbn'];
        }

        // 部門コード
        $bumon_cd = null;
        if (array_key_exists('bumon_cd', $dataAppAry)) {
            $bumon_cd = $dataAppAry['bumon_cd'];
        }
        
        //データ抽出
        $csvData = $this->getData_Csv($db, $ymd_st, $ymd_ed, $output_kbn, $data_kbn_ary, $bumon_cd);
        if (!isset($csvData)) {
            App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
            return;
        }

        //ファイル名編集
        $file_name   = null;
        $ymd_st_date = new DateTime($ymd_st);
        $ymd_ed_date = new DateTime($ymd_ed);
        $ymd_edit    = '_' . $ymd_st_date->format('Ymd') . '-' . $ymd_ed_date->format('Ymd');

        switch ($output_kbn) {
            case 1:
                //売上データ
                $file_name = self::$title_uri . $ymd_edit . '.csv';
                break;
            case 2:
                //売上明細データ
                $file_name = self::$title_uri_mesai . $ymd_edit . '.csv';
                break;
//            case 3:     //一旦コメントアウト
//                //入金データ
//                $file_name = self::$title_nyukin . $ymd_edit . '.csv';
//                break;
//            case 4:
//                //会費利用（葬儀）データ
//                $file_name = self::$title_kaihi_sougi . $ymd_edit . '.csv';
//                break;
//            case 5:
//                //会費利用（葬儀以外）データ
//                $file_name = self::$title_kaihi_other . $ymd_edit . '.csv';
//                break;
//            case 6:
//                //担当者別売上集データ
//                $file_name = self::$title_tanto . $ymd_edit . '.csv';
//                break;
            case 7:
                // 部門情報取得
                $bumon = $this->getBumonInfo($db, $bumon_cd);
                $bumon_snm = "_" . $bumon['bumon_snm'];
                $file_name = self::$title_bumon . $bumon_snm . $ymd_edit . '.csv';
                break;
            default:
        }

        //CSVファイルに出力
        $buf = Msi_Sys_Utils::csvOutString($csvData);
        Msi_Sys_Utils::out2way1($buf, $file_name, 'text/csv');
    }

    /**
     * 売上・入金CSVデータ編集
     *
     * <AUTHOR> Okuyama
     * @since      2017/05/11
     * @param      db   $db
     * @param      string $ymd_st 集計開始日
     * @param      string $ymd_ed 集計終了日
     * @param      string $output_kbn 出力区分
     * @param      array  $data_kbn_ary
     * @param      string $bumon_cd 部門コード
     * @return     array   データ一式
     */
    private function getData_Csv($db, $ymd_st, $ymd_ed, $output_kbn, $data_kbn_ary, $bumon_cd) {
        $where = self::getWhere($data_kbn_ary);
        if ($output_kbn == 1) {
            //売上データ
            $select = $db->easySelect(<<< END_OF_SQL
            SELECT
                 TO_CHAR(TR_UR.keijo_ymd,'YYYYMM')                                                      AS 売上計上年月
                ,COALESCE(TR_UR.data_kbn,0) || '：' || COALESCE(MS_CN_data_kbn.kbn_value_lnm,'')        AS 売上区分
               -- ,''                                                                                     AS 売上種類
                ,CASE TR_UR.aka_kuro_kbn
                    WHEN 0 THEN '0:黒伝'
                    ELSE        '1:赤伝'
                 END                                                                                    AS 赤黒区分    
                ,TR_UR.uri_den_no                                                                       AS 売上伝票番号
                ,CASE 
                    WHEN SKI.moushi_kbn IS NULL THEN '－'
                    ELSE COALESCE(SKI.moushi_kbn,0) || '：' || COALESCE(SKI.moushi_kbn_lnm,'')
                 END                                                                                    AS 申込区分
                ,CASE TR_UR.data_kbn
                    WHEN 3 THEN ''      -- 一般受注（単品受注）の場合、施行番号はないためNULLとする
                    ELSE TR_UR.seko_no
                 END                                                                                    AS 施行番号
                ,CASE TR_UR.data_kbn
                    WHEN 1 THEN ''      -- 葬儀の場合
                    ELSE TR_UR.denpyo_no
                 END                                                                                    AS 受注伝票番号
                ,TR_UR.bumon_cd                                                                         AS 部門コード
                ,MS_BM.bumon_lnm                                                                        AS 部門名
                ,TR_UR.tanto_cd                                                                         AS 担当者コード
                ,MS_TA.tanto_nm                                                                         AS 担当者名
                ,CASE TR_UR.data_kbn
                    WHEN 1 THEN ''
                    WHEN 2 THEN ''
                    ELSE COALESCE(TR_UR.pay_method_cd,'') || '：' || COALESCE(MS_CN_pay_method_cd.kbn_value_lnm,'')
                 END                                                                                    AS 支払方法
                ,CASE TR_UR.data_kbn
                    WHEN 1 THEN ''
                    WHEN 2 THEN ''
                    ELSE COALESCE(TR_UR.kaishu_kbn,0) || '：' || COALESCE(MS_CN_kaishu_kbn.kbn_value_lnm,'')
                 END                                                                                    AS 回収区分
                ,TR_UR.keijo_ymd                                                                        AS 売上計上日
                ,TR_UR.kaishu_ymd                                                                       AS 回収予定日
                ,TR_UR.sekyu_cd                                                                         AS 請求先コード
                ,TR_UR.sekyu_nm                                                                         AS 請求先名
                ,TR_UR.sekyu_soufu_nm                                                                   AS 請求書送付先名
                ,TR_UR.sekyu_yubin_no                                                                   AS 請求先郵便番号
                ,COALESCE(TR_UR.sekyu_addr1,'') || COALESCE(TR_UR.sekyu_addr2,'')                       AS 請求先住所
                ,TR_UR.sekyu_tel                                                                        AS 請求先電話番号
                ,TR_UR.uri_prc_sum + TR_UR.uri_hepn_sum + TR_UR.uri_nebk_sum + TR_UR.hoshi_prc_sum - TR_UR.in_zei_prc   AS 売上金額
                ,TR_UR.out_zei_prc + TR_UR.in_zei_prc                                                   AS 消費税額
                ,TR_UR.uri_prc_sum + TR_UR.uri_hepn_sum + TR_UR.uri_nebk_sum + TR_UR.hoshi_prc_sum + TR_UR.out_zei_prc  AS 請求金額
                ,TR_UR.nyukin_prc                                                                       AS 入金済額
                ,TR_UR.seikyu_zan                                                                       AS 請求残高
                ,TR_UR.genka_prc_sum                                                                    AS 原価金額
                ,TR_UR.arari_prc                                                                        AS 粗利益額
                ,SKI.kaiin_kbn_lnm                                                                      AS 会員区分
                --,GOJO_TOKU.kbn_value_lnm                                                                AS 加入団体（特約）
                --,GOJO_IPAN.kbn_value_lnm                                                                AS 加入団体（一般）
                ,SKI.keishiki_kbn_lnm                                                                   AS 葬儀形式
                ,SKI.syushi_kbn_lnm                                                                     AS 宗旨
                ,SKI.syuha_nm                                                                           AS 宗派
                ,SKI.jyusho_nm                                                                          AS 寺院名
                ,SKI.sesyu_shokai_kbn_lnm                                                               AS 導師紹介
                --,SKI.sougi_scale_kbn_lnm                                                                AS 葬儀規模
                ,CASE
                    WHEN SKI.sougi_kibo_kbn IS NULL THEN ''
                    ELSE SKI.sougi_kibo_kbn || '：' || SKI.sougi_kibo_kbn_lnm
                 END                                                                                    AS 葬儀規模
                ,SKI.reihin_suryo                                                                       AS 礼品数
                ,SKI.web_disp_plan_nm                                                                   AS 施行プラン名
                ,(SELECT udm.shohin_nm FROM uriage_denpyo_msi udm 
                  LEFT JOIN shohin_bunrui_mst sbm ON sbm.shohin_cd = udm.shohin_cd AND sbm.delete_flg = 0
                  WHERE udm.uri_den_no = UR_HD2.uri_den_no AND udm.delete_flg = 0 AND sbm.other_bunrui_kbn = '100'
                  ORDER BY udm.uri_den_no, udm.msi_no
                  LIMIT 1
                 )                                                                                      AS パックプラン
                ,SKI.k_nm || ' 様'                                                                      AS 故人名
                ,SKI.k_knm                                                                              AS 故人名カナ
                ,CASE SKI.k_sex_kbn
                    WHEN 1 THEN '男性'
                    WHEN 2 THEN '女性'
                 END                                                                                    AS 性別
                ,SKI.k_nenrei_man                                                                       AS "年齢(満)"
                ,CASE
                    WHEN SKI.k_nenrei_man <= 19                           THEN '01: 10代'
                    WHEN SKI.k_nenrei_man > 19 AND SKI.k_nenrei_man <  30 THEN '02: 20代'
                    WHEN SKI.k_nenrei_man > 29 AND SKI.k_nenrei_man <  40 THEN '03: 30代'
                    WHEN SKI.k_nenrei_man > 39 AND SKI.k_nenrei_man <  50 THEN '04: 40代'
                    WHEN SKI.k_nenrei_man > 49 AND SKI.k_nenrei_man <  60 THEN '05: 50代'
                    WHEN SKI.k_nenrei_man > 59 AND SKI.k_nenrei_man <  70 THEN '06: 60代'
                    WHEN SKI.k_nenrei_man > 69 AND SKI.k_nenrei_man <  80 THEN '07: 70代'
                    WHEN SKI.k_nenrei_man > 79 AND SKI.k_nenrei_man <  90 THEN '08: 80代'
                    WHEN SKI.k_nenrei_man > 89 AND SKI.k_nenrei_man < 100 THEN '09: 90代'
                    WHEN SKI.k_nenrei_man > 99                            THEN '10: 100歳以上'
                 END                                                                                    AS 故人年齢層
                ,SKI.souke_nm || ' 家'                                                                  AS 葬家名
                ,CASE 
                    WHEN SKAF.free1_place_nm IS NULL THEN
                        CASE 
                            WHEN SKAF.free1_place_kbn IS NULL THEN ''
                            ELSE 'その他'
                        END
                    ELSE 
                        CASE 
                            WHEN SKAF.free1_place_kbn IS NULL THEN 'その他'
                            ELSE MS_CN_jyusho_kbn.kbn_value_lnm
                        END
                 END                                                                                    AS 住所区分
                ,SKAF.free1_place_nm                                                                    AS 亡くなられた場所
                ,CAST(SKAF.ts_free1 AS DATE)                                                            AS 受付日
                ,SKI.shibou_ymd                                                                         AS 亡日
                ,SKI.tsuya_ymd                                                                          AS 通夜
                ,CASE 
                    WHEN TR_UR.data_kbn = 2 THEN NULL
                    ELSE SKI.sougi_ymd
                 END                                                                                    AS 葬儀日
                ,CASE
                    WHEN SKI.sougi_basho_kbn IS NULL THEN ''
                    WHEN SKI.sougi_basho_kbn = 0     THEN '0:自宅'
                    ELSE SKI.sougi_basho_kbn || '：' || SKI.sougi_basho_kbn_lnm
                 END                                                                                    AS 葬儀場所区分
                ,CASE
                    WHEN SKI.sougi_basho_kbn = 0 THEN '自宅'
                    ELSE SKI.sougi_basho_nm
                 END                                                                                    AS 葬儀会場名
                ,CASE
                    WHEN SKI.sougi_kaijyo_kbn IS NOT NULL THEN SKI.sougi_kaijyo_kbn || '：' || SKI.sougi_kaijyo_kbn_lnm
                    ELSE ''
                 END                                                                                    AS 葬儀会場区分
                ,CASE
                    WHEN SKI.keishiki_kbn_lnm = '火葬式' THEN
                        CASE
                            WHEN SKI.sougi_basho_kbn = 0 THEN '火葬のみ'      -- 自宅
                            WHEN SKI.sougi_basho_kbn = 7 THEN '火葬のみ'      -- 斎場(火葬場)
                            WHEN SKI.sougi_basho_kbn = 9 THEN '火葬のみ'      -- その他
                        ELSE
                            CASE
                                WHEN SKI.sougi_kaijyo_kbn = 5 THEN '火葬のみ' -- 霊安室
                                ELSE ''
                            END
                        END
                    ELSE ''
                 END                                                                                    AS 火葬施行区分
                ,SKI.sougi_kaijyo_bumon_cd                                                              AS 葬儀会場部門コード
                ,SKI.sougi_kaijyo_bumon_lnm                                                             AS 葬儀会場部門名
                ,SKI.kasou_ymd                                                                          AS 火葬日
                ,SKI.kasou_basho_nm                                                                     AS 火葬場
                ,CASE 
                    WHEN TR_UR.data_kbn = 2 THEN SKI.sougi_ymd
                    ELSE NULL
                 END                                                                                    AS 法要日
                ,SKI.sougi_ymd - SKI.shibou_ymd                                                         AS 葬儀までの期間
                ,CAST(SKI.jichu_kakute_ymd AS DATE)                                                     AS 受注確定日
                ,CAST(SKI.jichu_kakute_ymd AS DATE) - CAST(SKAF.ts_free1 AS DATE)                       AS 受注確定までの期間
                ,CASE
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 300000   THEN '1: 30万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 300000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 500000   THEN '2: 50万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 800000   THEN '3: 80万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 800000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 1000000  THEN '4: 100万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 1000000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 1500000  THEN '5: 150万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 1500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 2000000  THEN '6: 200万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 2000000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 2500000  THEN '7: 250万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 2500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 3000000  THEN '8: 300万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 3000000 THEN '9: 300万以上'
                 END                                                                                    AS 価格帯
                ,CASE UR_HD.data_kbn
                    WHEN 1 THEN  -- データ区分(葬儀)
                        CASE SKI.moushi_kbn
                            WHEN 1 THEN 1
                            ELSE 0 
                        END
                    WHEN 2 THEN  -- データ区分(法事)
                        CASE SKI.moushi_kbn
                            WHEN 2 THEN 1
                            ELSE 0
                        END
                    ELSE 0
                 END                                                                                    AS "施行件数"
                ,CASE UR_HD.data_kbn
                    WHEN 3 THEN 1 -- 単品
                    ELSE 0
                 END                                                                                    AS "単品件数"
                ,CASE UR_HD.data_kbn
                    WHEN 4 THEN 1 -- 別注品
                    ELSE 0
                 END                                                                                    AS "別注品件数"
                ,COALESCE(SUBSTRING(SKI.kg_zip_no,1,3) || '-'|| SUBSTRING(SKI.kg_zip_no,4,4),'＊＊＊')  AS 故人郵便番号
                ,COALESCE(SKI.kg_addr1_nm,'＊＊＊')                                                     AS 故人住所1
                ,COALESCE(SKI.kg_addr2_nm,'＊＊＊')                                                     AS 故人住所2
                ,COALESCE(SKI.kg_addr3_nm,'＊＊＊')                                                     AS 故人住所3
               -- ,CASE TR_UR.history_no
                   -- WHEN 0 THEN
                     --   CASE TR_UR.data_kbn
                       --     WHEN 1 THEN COALESCE(SKI.gojokai_riyou_gaku, 0) -- 葬儀
                         --   WHEN 2 THEN COALESCE(SKI.seko_kaiyaku_gaku, 0)  -- 法事
                           -- ELSE 0
                      --  END
                   -- ELSE 0
                -- END                                                                                    AS 互助会利用額
               -- ,CASE TR_UR.history_no
                  --  WHEN 0 THEN
                    --    CASE TR_UR.data_kbn
                     --       WHEN 1 THEN COALESCE(SKI.seko_kaiyaku_gaku, 0)
                       --     ELSE 0
                       -- END
                   -- ELSE 0
                -- END                                                                                    AS 施行時解約金
            FROM
                uriage_denpyo_history       AS TR_UR
            LEFT OUTER JOIN juchu_denpyo    AS TR_JC 
            ON  TR_UR.denpyo_no  = TR_JC.denpyo_no
            AND TR_JC.delete_flg = 0
            LEFT OUTER JOIN uriage_denpyo   AS UR_HD2 
            ON  TR_UR.uri_den_no  = UR_HD2.uri_den_no
            AND UR_HD2.delete_flg = 0
            LEFT OUTER JOIN (
                SELECT   UDH_A.uri_den_no, UDH_A.history_no, UDH_A.data_kbn, UDH_A.seko_no
                        ,UDH_A.uri_prc_sum, UDH_A.genka_prc_sum, UDH_A.uri_hepn_sum
                        ,UDH_A.uri_nebk_sum, UDH_A.hoshi_prc_sum, UDH_A.out_zei_prc, UDH_A.in_zei_prc, UDH_A.genka_prc_sum
                        ,UDH_A.arari_prc, UDH_A.delete_flg
                FROM uriage_denpyo_history UDH_A
                INNER JOIN (
                    SELECT   uri_den_no
                            ,MIN(history_no)    AS min_history_no
                    FROM uriage_denpyo_history
                    WHERE delete_flg = 0
                    GROUP BY uri_den_no
                ) AS UDH_B
                ON  UDH_A.uri_den_no = UDH_B.uri_den_no
                AND UDH_A.history_no = UDH_B.min_history_no
                AND UDH_A.delete_flg = 0
            ) UR_HD 
            ON  TR_UR.uri_den_no = UR_HD.uri_den_no
            AND TR_UR.history_no = UR_HD.history_no
            AND UR_HD.delete_flg = 0
            LEFT OUTER JOIN seko_kihon_info AS SKI_CNT 
            ON  TR_UR.seko_no    = SKI_CNT.seko_no
            AND TR_UR.history_no = 0
            AND TR_UR.delete_flg = 0
            LEFT OUTER JOIN bumon_mst       AS MS_BM 
            ON  TR_UR.bumon_cd   = MS_BM.bumon_cd
            AND MS_BM.delete_flg = 0
            LEFT OUTER JOIN tanto_mst       AS MS_TA 
            ON  TR_UR.tanto_cd   = MS_TA.tanto_cd
            AND MS_TA.delete_flg = 0
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_data_kbn       -- 名称（データ区分）
            ON  MS_CN_data_kbn.code_kbn   = '0920'
            AND MS_CN_data_kbn.delete_flg = 0
            AND TR_UR.data_kbn            = MS_CN_data_kbn.kbn_value_cd_num
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_kaishu_kbn     -- 名称（回収区分）
            ON  MS_CN_kaishu_kbn.code_kbn = '0430'
            AND MS_CN_data_kbn.delete_flg = 0
            AND TR_UR.kaishu_kbn          = MS_CN_kaishu_kbn.kbn_value_cd_num
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_pay_method_cd  -- 名称（支払方法）
            ON  MS_CN_pay_method_cd.code_kbn   = '1130'
            AND MS_CN_pay_method_cd.delete_flg = 0
            AND TR_UR.pay_method_cd            = MS_CN_pay_method_cd.kbn_value_cd
            LEFT OUTER JOIN (
                SELECT   MS_CN_kaiin_kbn.kbn_value_lnm          AS kaiin_kbn_lnm
                        ,MS_CN_keishiki_kbn.kbn_value_lnm       AS keishiki_kbn_lnm
                        ,MS_CN_syushi_kbn.kbn_value_lnm         AS syushi_kbn_lnm
                        ,SPM.seko_plan_cd                       AS seko_plan_cd
                        ,SPM.web_disp_plan                      AS web_disp_plan_nm
                        ,SN.nitei_shibou_ymd                    AS shibou_ymd
                        ,SN.nitei_tsuya_ymd                     AS tsuya_ymd
                        ,SN.nitei_sougi_basho_kbn               AS sougi_basho_kbn
                        ,SN.nitei_sougi_basho_kbn_lnm           AS sougi_basho_kbn_lnm
                        ,SN.nitei_sougi_basho_cd                AS sougi_basho_cd
                        ,SN.nitei_sougi_basho_nm                AS sougi_basho_nm
                        ,SN.nitei_sougi_kaijyo_kbn              AS sougi_kaijyo_kbn
                        ,SN.nitei_sougi_kaijyo_kbn_lnm          AS sougi_kaijyo_kbn_lnm
                        ,SN.nitei_sougi_kaijyo_bumon_cd         AS sougi_kaijyo_bumon_cd
                        ,SN.nitei_sougi_basho_kbn_lnm           AS nitei_sougi_basho_kbn_lnm
                        ,SN.nitei_kasou_ymd                     AS kasou_ymd
                        ,SN.nitei_kasou_basho_cd                AS kasou_basho_cd
                        ,SN.nitei_kasou_basho_nm                AS kasou_basho_nm
                        ,SN.nitei_sougi_kaijyo_bumon_lm         AS sougi_kaijyo_bumon_lnm
                        ,MS_CN_moushi_kbn.kbn_value_lnm         AS moushi_kbn_lnm
                        ,MS_CN_sougi_scale_kbn.kbn_value_lnm    AS sougi_scale_kbn_lnm
                        ,MS_CN_sesyu_syokai_kbn.kbn_value_lnm   AS sesyu_shokai_kbn_lnm
                        ,znm.zip_no                             AS kg_zip_no
                        ,znm.addr1_nm                           AS kg_addr1_nm
                        ,znm.addr2_nm                           AS kg_addr2_nm
                        ,znm.addr3_nm                           AS kg_addr3_nm
                        ,SGM.plan_convert_gaku                  AS gojokai_riyou_gaku
                        ,SGM2.kaiyaku_gaku                      AS seko_kaiyaku_gaku
                        ,seko_kihon_info.delete_flg             AS ski_delete_flg
                        ,seko_kihon_info.n_free1                AS sougi_kibo_kbn
                        ,MS_CN_sougi_kibo_kbn.kbn_value_lnm     AS sougi_kibo_kbn_lnm
                        ,seko_kihon_info.n_free2                AS reihin_suryo
                        ,* 
                FROM seko_kihon_info
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_moushi_kbn
                ON  MS_CN_moushi_kbn.code_kbn               = '0010'  -- 名称(申込区分)
                AND MS_CN_moushi_kbn.delete_flg             = 0
                AND MS_CN_moushi_kbn.kbn_value_cd_num       = seko_kihon_info.moushi_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_kaiin_kbn
                ON  MS_CN_kaiin_kbn.code_kbn                = '0030'  -- 名称(会員区分)
                AND MS_CN_kaiin_kbn.delete_flg              = 0
                AND MS_CN_kaiin_kbn.kbn_value_cd            = seko_kihon_info.kaiin_cd
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_keishiki_kbn
                ON  MS_CN_keishiki_kbn.code_kbn             = '0040'  -- 名称(葬儀形式区分)
                AND MS_CN_keishiki_kbn.delete_flg           = 0
                AND MS_CN_keishiki_kbn.kbn_value_cd_num     = seko_kihon_info.keishiki_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_syushi_kbn
                ON  MS_CN_syushi_kbn.code_kbn               = '0240'  -- 名称(宗旨区分)
                AND MS_CN_syushi_kbn.delete_flg             = 0
                AND MS_CN_syushi_kbn.kbn_value_cd_num       = seko_kihon_info.syushi_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_sougi_scale_kbn
                ON  MS_CN_sougi_scale_kbn.code_kbn          = '0045'  -- 名称(葬儀規模)
                AND MS_CN_sougi_scale_kbn.delete_flg        = 0
                AND MS_CN_sougi_scale_kbn.kbn_value_cd_num  = seko_kihon_info.sougi_scale
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_sesyu_syokai_kbn
                ON  MS_CN_sesyu_syokai_kbn.code_kbn         = '1830'  -- 名称(施主紹介者)
                AND MS_CN_sesyu_syokai_kbn.delete_flg       = 0
                AND MS_CN_sesyu_syokai_kbn.kbn_value_cd_num = seko_kihon_info.free1_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_sougi_kibo_kbn
                ON  MS_CN_sougi_kibo_kbn.code_kbn           = '8614'  -- 名称(葬儀規模)
                AND MS_CN_sougi_kibo_kbn.delete_flg         = 0
                AND MS_CN_sougi_kibo_kbn.kbn_value_cd_num   = seko_kihon_info.n_free1
                LEFT OUTER JOIN (
                    SELECT   DISTINCT
                             seko_plan_cd
                            ,gojokai_kbn
                            ,main_pt_cd
                            ,web_disp_plan 
                    FROM seko_plan_mst 
                    WHERE delete_flg = 0
                ) SPM
                ON  seko_kihon_info.seko_plan_cd = SPM.seko_plan_cd
                AND seko_kihon_info.gojokai_kbn  = SPM.gojokai_kbn
                AND seko_kihon_info.main_pt_cd   = SPM.main_pt_cd   
                AND seko_kihon_info.delete_flg   = 0
                -- 互助会利用額
                LEFT OUTER JOIN (
                    SELECT   sgm.seko_no                AS gojo_mem_seko_no
                            --,sgm.course_snm_cd
                            --,sgm.kain_no
                            --,sgm.apply_no
                            --,sgm.kanyu_nm
                            --,sgm.yoto_kbn
                            ,SUM(sgm.plan_convert_gaku) AS plan_convert_gaku
                            ,SUM(sgm.harai_gaku)        AS harai_gaku
                            --,sgm.harai_no
                            ,SUM(sgm.early_use_cost)    AS early_use_cost
                    FROM seko_gojokai_member sgm
                    WHERE sgm.yoto_kbn = 1
                    GROUP BY sgm.seko_no
                ) SGM
                ON  seko_kihon_info.seko_no    = SGM.gojo_mem_seko_no
                AND seko_kihon_info.delete_flg = 0
                -- 施行時解約金
                LEFT OUTER JOIN (
                    SELECT   sgmk.seko_no                   AS kaiyaku_seko_no
                            ,SUM(sgmk.keiyaku_gaku)         AS kaiyaku_gaku 
                    FROM seko_gojokai_member sgmk
                    WHERE sgmk.yoto_kbn = 3
                    GROUP BY sgmk.seko_no
                ) SGM2
                ON  seko_kihon_info.seko_no    = SGM2.kaiyaku_seko_no
                AND seko_kihon_info.delete_flg = 0
                LEFT OUTER JOIN (
                    SELECT  DISTINCT 
                             zip_no
                            ,addr1_nm
                            ,addr2_nm
                            ,addr3_nm 
                    FROM zip_no_mst 
                    WHERE delete_flg = 0
                ) znm 
                ON substring(seko_kihon_info.kg_yubin_no, 1, 3) || substring(seko_kihon_info.kg_yubin_no, 5, 4) = znm.zip_no 
                LEFT OUTER JOIN (
                    SELECT   seko_no AS nitei_seko_no
                            ,CAST(max(CASE WHEN nitei_kbn = 1 THEN nitei_ymd END) AS DATE)              AS nitei_shibou_ymd
                            ,CAST(max(CASE WHEN nitei_kbn = 4 THEN nitei_ymd END) AS DATE)              AS nitei_tsuya_ymd
                            ,CAST(max(CASE WHEN nitei_kbn = 7 THEN nitei_ymd END) AS DATE)              AS nitei_sougi_ymd
                            ,CAST(max(CASE WHEN nitei_kbn = 6 THEN nitei_ymd END) AS DATE)              AS nitei_kasou_ymd
                            ,max(CASE WHEN nitei_kbn = 7 THEN basho_kbn END)                            AS nitei_sougi_basho_kbn
                            ,max(CASE WHEN nitei_kbn = 7 THEN MS_CN_sougi_basho_kbn.kbn_value_lnm END)  AS nitei_sougi_basho_kbn_lnm
                            ,max(CASE WHEN nitei_kbn = 7 THEN basho_cd END)                             AS nitei_sougi_basho_cd
                            ,max(CASE WHEN nitei_kbn = 7 THEN basho_nm END)                             AS nitei_sougi_basho_nm
                            ,max(CASE WHEN nitei_kbn = 6 THEN basho_cd END)                             AS nitei_kasou_basho_cd
                            ,max(CASE WHEN nitei_kbn = 6 THEN basho_nm END)                             AS nitei_kasou_basho_nm
                            ,max(CASE WHEN nitei_kbn = 7 THEN KM99.bumon_cd END)                        AS nitei_sougi_kaijyo_bumon_cd
                            ,max(CASE WHEN nitei_kbn = 7 THEN KM99.kaijyo_kbn END)                      AS nitei_sougi_kaijyo_kbn
                            ,max(CASE WHEN nitei_kbn = 7 THEN MS_CN_sougi_kaijyo_kbn.kbn_value_lnm END) AS nitei_sougi_kaijyo_kbn_lnm
                            ,max(CASE WHEN nitei_kbn = 7 THEN BM99.bumon_lnm END)                       AS nitei_sougi_kaijyo_bumon_lm
                    FROM
                        seko_nitei
                    LEFT OUTER JOIN code_nm_mst     AS MS_CN_sougi_basho_kbn
                    ON  MS_CN_sougi_basho_kbn.code_kbn   = '0670'
                    AND MS_CN_sougi_basho_kbn.delete_flg = 0
                    AND seko_nitei.nitei_kbn             = 7
                    AND seko_nitei.basho_kbn             = MS_CN_sougi_basho_kbn.kbn_value_cd_num
                    LEFT OUTER JOIN kaijyo_mst      AS KM99
                    ON  seko_nitei.basho_cd  = KM99.kaijyo_cd
                    AND KM99.delete_flg      = 0
                    AND seko_nitei.nitei_kbn = 7
                    LEFT OUTER JOIN bumon_mst       AS BM99
                    ON  KM99.bumon_cd   = BM99.bumon_cd
                    AND BM99.delete_flg = 0
                    LEFT OUTER JOIN code_nm_mst     AS MS_CN_sougi_kaijyo_kbn
                    ON  MS_CN_sougi_kaijyo_kbn.code_kbn   = '1550'
                    AND MS_CN_sougi_kaijyo_kbn.delete_flg = 0
                    AND KM99.kaijyo_kbn                   = MS_CN_sougi_kaijyo_kbn.kbn_value_cd_num
                    AND seko_nitei.nitei_kbn              = 7
                    AND seko_nitei.basho_kbn              = 2
                    WHERE
                        nitei_kbn IN (1, 4, 6, 7) AND seko_nitei.delete_flg = 0 
                    GROUP BY
                        seko_no
                ) SN
                ON  seko_kihon_info.seko_no     = SN.nitei_seko_no
                AND seko_kihon_info.moushi_kbn <> 5
                AND seko_kihon_info.delete_flg  = 0
            ) SKI
            ON  TR_UR.seko_no      = SKI.seko_no
            AND SKI.ski_delete_flg = 0
            AND TR_UR.delete_flg   = 0
            LEFT OUTER JOIN seko_kihon_all_free SKAF
            ON  TR_UR.seko_no    = SKAF.seko_no
            AND TR_UR.delete_flg = 0
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_jyusho_kbn     -- 名称（各種住所区分）
            ON  MS_CN_jyusho_kbn.code_kbn   = '2560'
            AND MS_CN_jyusho_kbn.delete_flg = 0
            AND SKAF.free1_kbn              = MS_CN_jyusho_kbn.kbn_value_cd_num
            LEFT JOIN seko_gojokai_info SKI_GOJO        -- 施行互助会情報
            ON  SKI_GOJO.seko_no    = SKI_CNT.seko_no
            AND SKI_GOJO.delete_flg = 0
            LEFT JOIN code_nm_mst GOJO_TOKU             -- 加入団体（特約）
            ON  GOJO_TOKU.code_kbn         = '1620'
            AND GOJO_TOKU.kbn_value_cd_num = SKI_GOJO.spec_agent_kbn
            AND GOJO_TOKU.delete_flg       = 0
            LEFT JOIN code_nm_mst GOJO_IPAN             -- 加入団体（一般）
            ON  GOJO_IPAN.code_kbn         = '0210'
            AND GOJO_IPAN.kbn_value_cd_num = SKI_GOJO.kanyu_dantai_kbn
            AND GOJO_IPAN.delete_flg       = 0
            WHERE 
                TR_UR.keijo_ymd BETWEEN :st_ymd AND :ed_ymd
            AND TR_UR.delete_flg = 0
                   -- $where
            ORDER BY
                 TR_UR.bumon_cd
                ,TR_UR.keijo_ymd
                ,TR_UR.uri_den_no
                ,TR_UR.aka_kuro_kbn 
END_OF_SQL
                    , array(
                  'st_ymd' => $ymd_st
                , 'ed_ymd' => $ymd_ed
            ));
        } else if ($output_kbn == 2) {
            //売上明細データ
            $select = $db->easySelect(<<< END_OF_SQL
            SELECT
                 TO_CHAR(TR_UR.keijo_ymd,'YYYYMM')                                                  AS 売上計上年月
                ,TR_UR.keijo_ymd                                                                    AS 売上計上日
                ,COALESCE(TR_UR.data_kbn,0) || '：' || COALESCE(MS_CN_data_kbn.kbn_value_lnm,'')    AS 売上区分
                --,''                                                                                 AS 売上種類
                ,CASE TR_UR.aka_kuro_kbn
                    WHEN 0 THEN '0:黒伝'
                    ELSE        '1:赤伝'
                 END                                                                                AS 赤黒区分  
                ,TR_UR.uri_den_no                                                                   AS 売上伝票番号
                ,COALESCE(SKI.moushi_kbn,0) || '：' || COALESCE(SKI.moushi_kbn_lnm,'')              AS 申込区分
                ,CASE TR_UR.data_kbn
                    WHEN 3 THEN ''          -- 一般受注（単品受注）の場合
                    ELSE TR_UR.seko_no
                 END                                                                                AS 施行番号
                ,CASE TR_UR.data_kbn
                    WHEN 1 THEN ''          --葬儀の場合
                    ELSE TR_UR.denpyo_no
                 END                                                                                AS 受注伝票番号
                ,TR_UM.msi_no                                                                       AS 売上明細番号
                ,TR_UM.denpyo_kbn || ':' || MS_CN_denpyo_kbn.kbn_value_lnm                          AS 伝票区分
                ,TR_UM.juchu_ymd                                                                    AS "追加・返品日"
                ,TR_UM.shohin_cd                                                                    AS 商品コード
                ,TR_UM.shohin_nm                                                                    AS 商品名
                ,ACCT.kamoku_cd                                                                     AS 科目コード
                ,ACCT.kamoku_nm                                                                     AS 科目名称
                ,CASE
                    WHEN ACCT.hojo_cd IS NULL THEN '-'
                    ELSE ACCT.hojo_cd
                 END                                                                                AS 補助科目コード
                ,CASE
                    WHEN ACCT.hojo_nm IS NULL THEN '-'
                    ELSE ACCT.hojo_nm
                 END                                                                                AS 補助科目名称
                ,TR_UR.bumon_cd                                                                     AS 部門コード
                ,BM2.bumon_lnm                                                                      AS 部門名称
                ,TR_UM.bumon_cd                                                                     AS "部門コード(明細)"
                ,BM.bumon_lnm                                                                       AS "部門名称(明細)"
                ,TR_UR.tanto_cd                                                                     AS 担当者コード
                ,MS_TA.tanto_nm                                                                     AS 担当者名
                ,TR_UM.dai_bunrui_cd || ':' || DBM.dai_bunrui_nm                                    AS 大分類名称
                ,TR_UM.chu_bunrui_cd || ':' || CBM.chu_bunrui_nm                                    AS 中分類名称
                ,TR_UM.shohin_kbn    || ':' || SKM.shohin_kbn_nm                                    AS 商品区分名称
                ,TR_UM.juchu_suryo                                                                  AS 数量
                ,TR_UM.uri_tnk                                                                      AS 単価
                ,TR_UM.uri_prc                                                                      AS 金額
                ,TR_UM.gojokai_nebiki_prc                                                           AS 付帯・特典
                ,TR_UM.nebiki_prc                                                                   AS 割引額
                ,TR_UM.gen_tnk                                                                      AS 原価単価
                ,TR_UM.gen_gaku                                                                     AS 原価金額
                ,TR_UM.arari_gaku                                                                   AS 粗利益額
                ,TR_UM.uri_prc + TR_UM.gojokai_nebiki_prc + TR_UM.nebiki_prc + TR_UM.hoshi_prc  - TR_UM.in_zei_prc AS 売上金額
                ,CASE TR_UM.zei_kbn
                    WHEN 0 THEN '非課税'
                    WHEN 1 THEN '内税'
                    WHEN 2 THEN '外税'
                 END                                                                                AS 税区分
                ,TR_UM.out_zei_prc + TR_UM.in_zei_prc                                               AS 消費税額
                ,TR_UM.uri_prc + TR_UM.gojokai_nebiki_prc + TR_UM.nebiki_prc + TR_UM.hoshi_prc  - TR_UM.in_zei_prc + TR_UM.out_zei_prc + TR_UM.in_zei_prc AS 税込み金額
                ,SKI.kaiin_kbn_lnm                                                                  AS 会員区分
                ,SKI.keishiki_kbn_lnm                                                               AS 葬儀形式
                ,SKI.syushi_kbn_lnm                                                                 AS 宗旨
                ,SKI.syuha_nm                                                                       AS 宗派
                ,SKI.jyusho_nm                                                                      AS 寺院名
                ,SKI.web_disp_plan_nm                                                               AS 施行プラン名
                ,SKI.k_nm || ' 様'                                                                  AS 故人名
                ,SKI.k_knm                                                                          AS 故人名カナ
                ,CASE SKI.k_sex_kbn
                    WHEN 1 THEN '男性'
                    WHEN 2 THEN '女性'
                 END                                                                                AS 性別
                ,SKI.k_nenrei_man                                                                   AS "年齢(満)"
                ,CASE
                    WHEN SKI.k_nenrei_man <= 19                            THEN '01: 10代'
                    WHEN SKI.k_nenrei_man >  19 AND SKI.k_nenrei_man <  30 THEN '02: 20代'
                    WHEN SKI.k_nenrei_man >  29 AND SKI.k_nenrei_man <  40 THEN '03: 30代'
                    WHEN SKI.k_nenrei_man >  39 AND SKI.k_nenrei_man <  50 THEN '04: 40代'
                    WHEN SKI.k_nenrei_man >  49 AND SKI.k_nenrei_man <  60 THEN '05: 50代'
                    WHEN SKI.k_nenrei_man >  59 AND SKI.k_nenrei_man <  70 THEN '06: 60代'
                    WHEN SKI.k_nenrei_man >  69 AND SKI.k_nenrei_man <  80 THEN '07: 70代'
                    WHEN SKI.k_nenrei_man >  79 AND SKI.k_nenrei_man <  90 THEN '08: 80代'
                    WHEN SKI.k_nenrei_man >  89 AND SKI.k_nenrei_man < 100 THEN '09: 90代'
                    WHEN SKI.k_nenrei_man >  99                            THEN '10: 100歳以上'
                 END                                                                                AS 故人年齢層
                ,SKI.souke_nm || ' 家'                                                              AS 葬家名
                ,SKI.shibou_ymd                                                                     AS 亡日
                ,SKI.tsuya_ymd                                                                      AS 通夜
                ,CASE
                    WHEN TR_UR.data_kbn = 2 THEN NULL
                    ELSE SKI.sougi_ymd
                 END                                                                                AS 葬儀日
                ,CASE
                    WHEN TR_UR.data_kbn = 2 THEN SKI.sougi_ymd
                    ELSE NULL
                 END                                                                                AS 法要日
                ,SKI.sougi_ymd - SKI.shibou_ymd                                                     AS 葬儀までの期間
                ,TR_UR.sekyu_nm                                                                     AS 請求先名
                ,TR_UM.nafuda_nm                                                                    AS 名札
                ,TR_UM.msi_biko1                                                                    AS 備考
                ,CASE
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 300000   THEN '1: 30万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 300000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 500000   THEN '2: 50万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 800000   THEN '3: 80万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 800000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 1000000  THEN '4: 100万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 1000000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 1500000  THEN '5: 150万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 1500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 2000000  THEN '6: 200万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 2000000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 2500000  THEN '7: 250万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 2500000
                     AND UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc < 3000000  THEN '8: 300万未満'
                    WHEN UR_HD2.uri_prc_sum + UR_HD2.uri_nebk_sum + UR_HD2.hoshi_prc_sum - UR_HD2.in_zei_prc >= 3000000 THEN '9: 300万以上'
                 END                                                                                AS 価格帯
                ,CASE
                    WHEN TR_UR.uri_den_no = TR_UM2.uri_den_no THEN
                        CASE
                            WHEN TR_UR.history_no = TR_UM2.history_no THEN
                                CASE
                                    WHEN TR_UM.msi_no = TR_UM2.msi_no THEN
                                        CASE UR_HD.data_kbn
                                            WHEN 1 THEN --データ区分(葬儀)
                                                CASE SKI.moushi_kbn
                                                    WHEN 1 THEN 1
                                                    ELSE 0
                                                END
                                            WHEN 2 THEN --データ区分(法事)
                                                CASE SKI.moushi_kbn
                                                    WHEN 2 THEN 1
                                                    ELSE 0
                                                END
                                            ELSE 0
                                        END
                                    ELSE 0
                                END
                            ELSE 0
                        END
                    ELSE 0
                 END                                                                                AS "施行件数"
                ,CASE
                    WHEN TR_UR.uri_den_no = TR_UM2.uri_den_no THEN
                        CASE
                            WHEN TR_UR.history_no = TR_UM2.history_no THEN
                                CASE
                                    WHEN TR_UM.msi_no = TR_UM2.msi_no THEN
                                        CASE UR_HD.data_kbn
                                            WHEN 3 THEN 1 --データ区分(単品)
                                            ELSE 0
                                        END
                                    ELSE 0
                                END
                            ELSE 0
                        END
                    ELSE 0
                 END                                                                                AS "単品件数"
                ,CASE
                    WHEN TR_UR.uri_den_no = TR_UM2.uri_den_no THEN
                        CASE
                            WHEN TR_UR.history_no = TR_UM2.history_no THEN
                                CASE
                                    WHEN TR_UM.msi_no = TR_UM2.msi_no THEN
                                        CASE UR_HD.data_kbn
                                            WHEN 4 THEN 1 --データ区分(別注品)
                                            ELSE 0
                                        END
                                    ELSE 0
                                END
                            ELSE 0
                        END
                    ELSE 0
                 END                                                                                AS "別注品件数"
                ,CASE
                    WHEN TR_UM.dai_bunrui_cd NOT IN ('0110','0080') THEN COALESCE(MS_CN_syoukei_group_cd.kbn_value_cd,'') || '：' || COALESCE(MS_CN_syoukei_group_cd.kbn_value_lnm,'')
                    ELSE ''
                 END                                                                                AS "小計グループ"
            FROM
                uriage_denpyo_history       AS TR_UR
            INNER JOIN uriage_denpyo_msi_history AS TR_UM
            ON  TR_UR.uri_den_no = TR_UM.uri_den_no
            AND TR_UR.history_no = TR_UM.history_no
            AND TR_UM.delete_flg = 0
            LEFT OUTER JOIN juchu_denpyo    AS TR_JC 
            ON  TR_UR.denpyo_no  = TR_JC.denpyo_no
            AND TR_JC.delete_flg = 0
            LEFT OUTER JOIN uriage_denpyo AS UR_HD2 
            ON  TR_UR.uri_den_no  = UR_HD2.uri_den_no
            AND UR_HD2.delete_flg = 0
            LEFT OUTER JOIN (
                SELECT
                     uri_den_no
                    ,history_no
                    ,min(msi_no) AS msi_no
                    ,delete_flg
                FROM
                    uriage_denpyo_msi_history
                WHERE
                    (uri_den_no, history_no) 
                IN (
                    SELECT
                         uri_den_no
                        ,min(history_no) AS history_no
                    FROM
                        uriage_denpyo_msi_history
                    WHERE
                        delete_flg = 0
                    GROUP BY
                        uri_den_no
                )
                AND delete_flg = 0
                GROUP BY
                     uri_den_no
                    ,history_no
                    ,delete_flg
            ) TR_UM2
            ON  TR_UM.uri_den_no  = TR_UM2.uri_den_no
            AND TR_UM.history_no  = TR_UM2.history_no
            AND TR_UM.msi_no      = TR_UM2.msi_no
            AND TR_UM2.delete_flg = 0
            LEFT OUTER JOIN (
                SELECT   UDH_A.uri_den_no, UDH_A.history_no, UDH_A.data_kbn, UDH_A.seko_no
                        ,UDH_A.uri_prc_sum, UDH_A.genka_prc_sum, UDH_A.uri_hepn_sum
                        ,UDH_A.uri_nebk_sum, UDH_A.hoshi_prc_sum, UDH_A.out_zei_prc, UDH_A.in_zei_prc
                        ,UDH_A.genka_prc_sum, UDH_A.arari_prc, UDH_A.delete_flg
                FROM uriage_denpyo_history UDH_A
                INNER JOIN (
                    SELECT   uri_den_no
                            ,MIN(history_no) AS min_history_no
                    FROM uriage_denpyo_history
                    WHERE delete_flg = 0
                    GROUP BY uri_den_no
                ) AS UDH_B
                ON  UDH_A.uri_den_no = UDH_B.uri_den_no
                AND UDH_A.history_no = UDH_B.min_history_no
                AND UDH_A.delete_flg = 0
            ) UR_HD
            ON  TR_UR.uri_den_no = UR_HD.uri_den_no
            AND TR_UR.history_no = UR_HD.history_no
            AND UR_HD.delete_flg = 0
            LEFT OUTER JOIN code_nm_mst AS MS_CN_data_kbn -- 名称（データ区分）
            ON  MS_CN_data_kbn.code_kbn   = '0920'
            AND MS_CN_data_kbn.delete_flg = 0
            AND TR_UR.data_kbn            = MS_CN_data_kbn.kbn_value_cd_num
            LEFT OUTER JOIN code_nm_mst AS MS_CN_denpyo_kbn --名称（伝区）
            ON  MS_CN_denpyo_kbn.code_kbn   = '2030'
            AND MS_CN_denpyo_kbn.delete_flg = 0
            AND TR_UM.denpyo_kbn            = MS_CN_denpyo_kbn.kbn_value_cd_num
            LEFT OUTER JOIN  (
                SELECT   sm.shohin_cd
                        ,sm.shohin_nm
                        ,km.kamoku_cd
                        ,km.kamoku_nm
                        ,khm.hojo_cd
                        ,khm.hojo_nm
                FROM shohin_mst sm
                INNER JOIN kamoku_mst km
                ON  sm.uri_kamoku_cd = km.kamoku_cd
                AND sm.delete_flg = 0
                AND km.delete_flg = 0
                LEFT OUTER JOIN kamoku_hojo_mst khm
                ON  sm.uri_kamoku_cd = khm.kamoku_cd
                AND sm.uri_hojo_cd   = khm.hojo_cd
                AND sm.delete_flg    = 0
                AND khm.delete_flg   = 0
            ) ACCT
            ON  TR_UM.shohin_cd  = ACCT.shohin_cd
            AND TR_UM.delete_flg = 0
            INNER JOIN bumon_mst BM
            ON  TR_UM.bumon_cd   = BM.bumon_cd
            AND TR_UM.delete_flg = 0
            AND BM.delete_flg    = 0
            INNER JOIN bumon_mst BM2
            ON TR_UR.bumon_cd    = BM2.bumon_cd
            AND TR_UM.delete_flg = 0
            AND BM2.delete_flg   = 0
            LEFT OUTER JOIN tanto_mst AS MS_TA
            ON  TR_UR.tanto_cd   = MS_TA.tanto_cd
            AND MS_TA.delete_flg = 0
            INNER JOIN shohin_dai_bunrui_mst DBM
            ON  TR_UM.dai_bunrui_cd = DBM.dai_bunrui_cd
            AND TR_UM.delete_flg    = 0
            INNER JOIN shohin_chu_bunrui_mst CBM
            ON  TR_UM.chu_bunrui_cd = CBM.chu_bunrui_cd
            AND TR_UM.delete_flg    = 0
            INNER JOIN shohin_kbn_mst SKM
            ON  TR_UM.shohin_kbn = SKM.shohin_kbn
            AND TR_UM.delete_flg = 0
            LEFT OUTER JOIN shohin_bunrui_mst SBM
            ON  TR_UM.shohin_cd     = SBM.shohin_cd
            AND TR_UM.dai_bunrui_cd = SBM.dai_bunrui_cd
            AND TR_UM.chu_bunrui_cd = SBM.chu_bunrui_cd
            AND TR_UM.shohin_kbn    = SBM.shohin_kbn
            AND TR_UM.delete_flg    = 0
            LEFT OUTER JOIN code_nm_mst AS MS_CN_syoukei_group_cd
            ON  MS_CN_syoukei_group_cd.code_kbn         = '2350'
            AND MS_CN_syoukei_group_cd.delete_flg       = 0
            AND MS_CN_syoukei_group_cd.kbn_value_cd_num = SBM.syoukei_group_cd
            LEFT OUTER JOIN (
                SELECT   MS_CN_kaiin_kbn.kbn_value_lnm      AS kaiin_kbn_lnm
                        ,MS_CN_keishiki_kbn.kbn_value_lnm   AS keishiki_kbn_lnm
                        ,MS_CN_syushi_kbn.kbn_value_lnm     AS syushi_kbn_lnm
                        ,SPM.seko_plan_cd                   AS seko_plan_cd
                        ,SPM.web_disp_plan                  AS web_disp_plan_nm
                        ,SN.nitei_shibou_ymd                AS shibou_ymd
                        ,SN.nitei_tsuya_ymd                 AS tsuya_ymd
                        ,MS_CN_moushi_kbn.kbn_value_lnm     AS moushi_kbn_lnm
                        ,seko_kihon_info.delete_flg         AS ski_delete_flg
                        ,* 
                FROM seko_kihon_info
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_moushi_kbn
                ON  MS_CN_moushi_kbn.code_kbn         = '0010'
                AND MS_CN_moushi_kbn.delete_flg       = 0
                AND MS_CN_moushi_kbn.kbn_value_cd_num = seko_kihon_info.moushi_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_kaiin_kbn
                ON  MS_CN_kaiin_kbn.code_kbn     = '0030'
                AND MS_CN_kaiin_kbn.delete_flg   = 0
                AND MS_CN_kaiin_kbn.kbn_value_cd = seko_kihon_info.kaiin_cd
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_keishiki_kbn
                ON  MS_CN_keishiki_kbn.code_kbn         = '0040'
                AND MS_CN_keishiki_kbn.delete_flg       = 0
                AND MS_CN_keishiki_kbn.kbn_value_cd_num = seko_kihon_info.keishiki_kbn
                LEFT OUTER JOIN code_nm_mst     AS MS_CN_syushi_kbn
                ON  MS_CN_syushi_kbn.code_kbn         = '0240'
                AND MS_CN_syushi_kbn.delete_flg       = 0
                AND MS_CN_syushi_kbn.kbn_value_cd_num = seko_kihon_info.syushi_kbn
                LEFT OUTER JOIN (
                    SELECT  DISTINCT
                             seko_plan_cd
                            ,gojokai_kbn
                            ,main_pt_cd
                            ,web_disp_plan
                    FROM seko_plan_mst 
                    WHERE delete_flg = 0
                ) SPM
                ON  seko_kihon_info.seko_plan_cd = SPM.seko_plan_cd
                AND seko_kihon_info.gojokai_kbn  = SPM.gojokai_kbn
                AND seko_kihon_info.main_pt_cd   = SPM.main_pt_cd 
                AND seko_kihon_info.delete_flg   = 0
                LEFT OUTER JOIN (
                    SELECT   seko_no                                                        AS nitei_seko_no
                            ,CAST(max(CASE WHEN nitei_kbn = 1 THEN nitei_ymd END) AS DATE)  AS nitei_shibou_ymd
                            ,CAST(max(CASE WHEN nitei_kbn = 4 THEN nitei_ymd END) AS DATE)  AS nitei_tsuya_ymd
                    FROM
                        seko_nitei 
                    WHERE
                        nitei_kbn IN (1, 4) 
                    AND delete_flg = 0 
                    GROUP BY
                        seko_no
                ) SN
                ON  seko_kihon_info.seko_no     = SN.nitei_seko_no
                AND seko_kihon_info.moushi_kbn <> 5
                AND seko_kihon_info.delete_flg  = 0
            ) SKI
            ON  TR_UM.seko_no      = SKI.seko_no
            AND SKI.ski_delete_flg = 0
            AND TR_UM.delete_flg   = 0
            WHERE TR_UR.keijo_ymd BETWEEN :st_ymd AND :ed_ymd
            AND   TR_UR.delete_flg = 0
                   -- $where
            ORDER BY
                 TR_UR.bumon_cd
                ,TR_UR.keijo_ymd
                ,TR_UR.uri_den_no
                ,TR_UR.history_no
                ,TR_UM.msi_no
                ,TR_UM.dai_bunrui_cd
                ,TR_UM.chu_bunrui_cd
                ,TR_UM.shohin_kbn
END_OF_SQL
                    , array(
                  'st_ymd' => $ymd_st
                , 'ed_ymd' => $ymd_ed
            ));
        } else if ($output_kbn == 3) {
            //入金データ
            $select = $db->easySelect(<<< END_OF_SQL
            SELECT
                 to_char(TR_ND.nyukin_ymd,'YYYYMM')                                     AS 入金年月
                ,TR_ND.denpyo_no                                                        AS 入金伝票番号
                ,to_char(TR_ND.nyukin_ymd,'YYYY/MM/DD')                                 AS 入金日
                ,NK.nyu_prc01 + NK.nyu_prc02 + NK.nyu_prc03 + NK.nyu_prc04 + NK.nyu_prc05 + NK.nyu_prc14 AS 入金額計
                ,NK.nyu_prc01                                                           AS （現金・小切手）
                ,NK.nyu_prc02                                                           AS （振込）
                ,NK.nyu_prc03                                                           AS （入金時値引き）
                ,NK.nyu_prc04                                                           AS （手数料）
                ,NK.nyu_prc05                                                           AS （カード）
                ,NK.nyu_prc14                                                           AS （立替金）
                ,COALESCE(TR_UR.data_kbn,0) || '：' || COALESCE(MS_CN_data_kbn.kbn_value_lnm,'') AS 売上区分
                ,CASE TR_UR.data_kbn
                    WHEN 3 THEN COALESCE(TR_JC.k_free1,0)  || '：' || COALESCE(MS_CN_uriage_shurui.kbn_value_lnm,'') -- 一般受注（単品受注）の場合
                    ELSE ''
                 END                                                                    AS 売上種類
                ,TR_UR.uri_den_no                                                       AS 売上伝票番号
                ,CASE TR_UR.data_kbn
                    WHEN 3 THEN ''      -- 一般受注（単品受注）の場合
                    ELSE TR_UR.seko_no
                 END                                                                    AS 施行番号
                ,CASE TR_UR.data_kbn
                    WHEN 1 THEN ''  --葬儀の場合
                    ELSE TR_UR.denpyo_no
                 END                                                                    AS 受注伝票番号
                ,TR_UR.bumon_cd                                                         AS 部門コード
                ,MS_BM.bumon_lnm                                                        AS 部門名
                ,TR_UR.sekyu_cd                                                         AS 請求先コード
                ,TR_UR.sekyu_nm                                                         AS 請求先名
            FROM (
                SELECT
                     NK.denpyo_no       AS nyukin_denpyo_no
                    ,SUM(nyu_prc01)     AS nyu_prc01 --現金・小切手
                    ,SUM(nyu_prc02)     AS nyu_prc02 --振込
                    ,SUM(nyu_prc03)     AS nyu_prc03 --入金時値引き
                    ,SUM(nyu_prc04)     AS nyu_prc04 --手数料
                    ,SUM(nyu_prc05)     AS nyu_prc05 --カード
                    ,SUM(nyu_prc14)     AS nyu_prc14 --立替金
                FROM (
                    SELECT
                         TR_ND.denpyo_no
                        ,CASE MS_KMK.toriatukai_kbn WHEN  1 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc01 --現金・小切手
                        ,CASE MS_KMK.toriatukai_kbn WHEN  2 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc02 --振込
                        ,CASE MS_KMK.toriatukai_kbn WHEN  3 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc03 --入金時値引き
                        ,CASE MS_KMK.toriatukai_kbn WHEN  4 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc04 --手数料
                        ,CASE MS_KMK.toriatukai_kbn WHEN  5 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc05 --カード
                        ,CASE MS_KMK.toriatukai_kbn WHEN 14 THEN TR_NM.nyukin_prc ELSE 0 END AS nyu_prc14 --立替金
                    FROM
                        nyukin_denpyo AS TR_ND
                    LEFT OUTER JOIN nyukin_denpyo_msi       AS TR_NM 
                    ON  TR_ND.denpyo_no  = TR_NM.denpyo_no
                    AND TR_NM.delete_flg = 0
                    LEFT OUTER JOIN kamoku_mst AS MS_KMK 
                    ON  MS_KMK.kamoku_kbn = 3 
                    AND TR_NM.denpyo_kbn  = MS_KMK.nyukin_sbt
                    AND TR_NM.kamoku_cd   = MS_KMK.kamoku_cd 
                    AND MS_KMK.delete_flg = 0
                    WHERE
                        TR_ND.nyukin_ymd BETWEEN :st_ymd AND :ed_ymd
                    AND TR_ND.nyukin_prc <> 0
                ) AS NK
                GROUP BY
                    NK.denpyo_no
            ) AS NK
            LEFT OUTER JOIN nyukin_denpyo   AS TR_ND 
            ON  NK.nyukin_denpyo_no = TR_ND.denpyo_no
            AND TR_ND.delete_flg    = 0
            LEFT OUTER JOIN uriage_denpyo   AS TR_UR
            ON  TR_ND.seikyu_no     = TR_UR.uri_den_no
            AND TR_UR.delete_flg    = 0
            LEFT OUTER JOIN juchu_denpyo    AS TR_JC 
            ON  TR_UR.denpyo_no     = TR_JC.denpyo_no
            AND TR_JC.delete_flg    = 0
            LEFT OUTER JOIN bumon_mst       AS MS_BM 
            ON  TR_UR.bumon_cd      = MS_BM.bumon_cd
            AND MS_BM.delete_flg    = 0
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_data_kbn       -- 名称（データ区分）
            ON  MS_CN_data_kbn.code_kbn   = '0920'
            AND MS_CN_data_kbn.delete_flg = 0
            AND TR_UR.data_kbn            = MS_CN_data_kbn.kbn_value_cd_num
            LEFT OUTER JOIN code_nm_mst     AS MS_CN_uriage_shurui  -- 名称（データ区分）
            ON  MS_CN_uriage_shurui.code_kbn   = '3810'
            AND MS_CN_uriage_shurui.delete_flg = 0
            AND TR_JC.k_free1                  = MS_CN_uriage_shurui.kbn_value_cd_num
            ORDER BY
                 TR_ND.nyukin_ymd
                ,TR_ND.denpyo_no
END_OF_SQL
                    , array(
                  'st_ymd' => $ymd_st
                , 'ed_ymd' => $ymd_ed
            ));
        } else if ($output_kbn == 4) {
            //会費利用（葬儀）データ
            $select = $db->easySelect(<<< END_OF_SQL
            SELECT
                 SGM.seko_no                                    AS 施行番号
                ,SKI.souke_nm || ' 家'                          AS 葬家名
                ,TO_CHAR(SKI.sougi_ymd,'YYYY/MM/DD')            AS 葬儀日
                ,SGM.kain_no                                    AS 会員番号
                ,SGM.kanyu_nm                                   AS 会員名
                ,GCM.gojokai_cose_iw                            AS 加入コースコード
                ,GCM.gojokai_cose_nm                            AS 加入コース
                ,SGM.kanyu_dt                                   AS 加入日
                ,SGM.last_nyukin_dt                             AS 最終入金日
                ,SGM.harai_no                                   AS 回数
                ,SGM.keiyaku_gaku                               AS 会費利用額
                ,SGM.harai_gaku                                 AS 払込金額
                ,SGM.wari_gaku                                  AS 割引額
                ,SGM.early_use_cost                             AS 早期利用費
                ,SGM.yoto_kbn                                   AS 用途区分
                ,CNM_YOTO.kbn_value_lnm                                                                                                         AS 用途
                ,UD.uri_den_no                                  AS 請求番号
                ,COALESCE(SGM.seko_gaku,0)                                                                                                      AS  返還金
                ,COALESCE(SGM.v_free2,'0')                                                                                                      AS  割増
                ,COALESCE(SGM.v_free3,'0')                                                                                                      AS  認め
                ,COALESCE(SGM.v_free4,'0')                                                                                                      AS  金券
                ,bm.bumon_lnm                                   AS "部門（斎場）"
            FROM
                seko_gojokai_member AS SGM
            LEFT JOIN uriage_denpyo     AS UD 
            ON  SGM.seko_no   = UD.seko_no
            AND UD.data_kbn   = 1
            AND UD.delete_flg = 0
            LEFT JOIN seko_kihon_info   AS SKI 
            ON  SGM.seko_no    = SKI.seko_no
            AND SKI.delete_flg = 0
            LEFT JOIN gojokai_couse_mst AS GCM 
            ON  SGM.gojokai_cose_cd = GCM.gojokai_cose_cd
            AND GCM.delete_flg      = 0
            LEFT JOIN code_nm_mst       AS CNM_YOTO 
            ON  CNM_YOTO.code_kbn   = '0450'
            AND SGM.yoto_kbn        = CNM_YOTO.kbn_value_cd_num
            AND CNM_YOTO.delete_flg = 0
            LEFT JOIN bumon_mst bm
            ON  bm.bumon_cd   = UD.bumon_cd
            AND bm.delete_flg = 0
            WHERE 
                SGM.delete_flg = 0
            AND UD.uri_den_no IS NOT NULL
            AND SKI.sougi_ymd BETWEEN :st_ymd AND :ed_ymd
            ORDER BY
                 SGM.seko_no
                ,SGM.kain_no
END_OF_SQL
                    , array(
                  'st_ymd' => $ymd_st
                , 'ed_ymd' => $ymd_ed
            ));
        } else if ($output_kbn == 5) {
            //会費利用（葬儀以外）データ
            $select = $db->easySelect(<<< END_OF_SQL
            SELECT
                 SGM.seko_no                                    AS 施行番号
                ,SKI.souke_nm || ' 家'                          AS 葬家名
                ,TO_CHAR(SKI.sougi_ymd,'YYYY/MM/DD')            AS 葬儀日
                ,SGM.kain_no                                    AS 会員番号
                ,SGM.kanyu_nm                                   AS 会員名
                ,GCM.gojokai_cose_iw                            AS 加入コースコード
                ,GCM.gojokai_cose_nm                            AS 加入コース
                ,SGM.kanyu_dt                                   AS 加入日
                ,SGM.last_nyukin_dt                             AS 最終入金日
                ,SGM.harai_no                                   AS 回数
                ,SGM.keiyaku_gaku                               AS 会費利用額
                ,SGM.harai_gaku                                 AS 払込金額
                ,SGM.wari_gaku                                  AS 割引額
                ,SGM.early_use_cost                             AS 早期利用費
                ,SGM.yoto_kbn                                   AS 用途区分
                ,CNM_YOTO.biko                                  AS 用途
                ,UD.uri_den_no                                  AS 請求番号
                ,COALESCE(SGM.seko_gaku,0)                                                                                                      AS  返還金
                ,COALESCE(SGM.v_free2,'0')                                                                                                      AS  割増
                ,COALESCE(SGM.v_free3,'0')                                                                                                      AS  認め
                ,COALESCE(SGM.v_free4,'0')                                                                                                      AS  金券
                ,bm.bumon_lnm                                   AS "部門（斎場）"
            FROM
                seko_gojokai_member AS SGM
            LEFT JOIN uriage_denpyo     AS UD 
            ON  SGM.seko_no   = UD.seko_no
            AND UD.data_kbn   = 2
            AND UD.delete_flg = 0
            LEFT JOIN seko_kihon_info   AS SKI 
            ON  SGM.seko_no    = SKI.seko_no
            AND SKI.delete_flg = 0
            LEFT JOIN gojokai_couse_mst AS GCM 
            ON  SGM.gojokai_cose_cd = GCM.gojokai_cose_cd
            AND GCM.delete_flg      = 0
            LEFT JOIN code_nm_mst       AS CNM_YOTO 
            ON  CNM_YOTO.code_kbn   = '1490'
            AND SGM.yoto_kbn        = CNM_YOTO.kbn_value_cd_num
            AND CNM_YOTO.delete_flg = 0
            LEFT JOIN bumon_mst bm
            ON  bm.bumon_cd   = UD.bumon_cd
            AND bm.delete_flg = 0
            WHERE 
                SGM.delete_flg = 0
            AND UD.uri_den_no IS NOT NULL
            AND SKI.sougi_ymd BETWEEN :st_ymd AND :ed_ymd
            ORDER BY
                 SGM.seko_no
                ,SGM.kain_no
END_OF_SQL
                , array(
                  'st_ymd' => $ymd_st
                , 'ed_ymd' => $ymd_ed
            ));
        } else if ($output_kbn == 6) {
            $bum_ary      = array();
            //$data_kbn_ary = array();
            $shohin_ary = DataMapper_Pdf0702::findKbn($db, $ymd_st, $ymd_ed, $bum_ary, $data_kbn_ary);
            if (Msi_Sys_Utils::myCount($shohin_ary) <= 0) {
                return null;
            }
            $head = '部門コード 部門名称 担当者コード 担当者名 申込区分 葬儀日 受付日 死亡日 売上伝票番号 施行番号 台帳番号 葬家名 施行法要 請求先名 ご法要場所 ご法宴場所 葬送儀礼 おもてなし その他 合計 ';
            foreach ($shohin_ary as $value) {
                $head = $head . ' ' . $value['shohin_kbn_nm'];
            }
            $csvData = array(Msi_Sys_Utils::strArrayify_qw($head));
            $rec = DataMapper_Pdf0702::find($db, $ymd_st, $ymd_ed, $bum_ary, $data_kbn_ary, $shohin_ary, true, true);
            if (Msi_Sys_Utils::myCount($rec) == 0) {
                return null;
            }
            $csvData = array_merge($csvData, $this->getCsvDetail($rec, $shohin_ary));
        } else if ($output_kbn == 7) {
            $bum_ary      = array();
            // 対象期間
            $st_date = str_replace("/", "", $ymd_st); // 対象期間開始日「/」を除去
            $ed_date = str_replace("/", "", $ymd_ed); // 対象期間終了日「/」を除去
            $ed_date_y = substr($ed_date,0 , 4);
            // 会社情報取得
            $kaisya_info = DataMapper_KaisyaInfo::findOne($db);
            // 決算開始日
            $kesan_st_date = substr(str_replace("/", "", $kaisya_info['kesan_st_date_']), 4, 8); // 決算開始月日「/」を除去
            // 決算終了日
            $kesan_ed_date = substr(str_replace("/", "", $kaisya_info['kesan_ed_date_']), 4, 8); // 決算終了月日「/」を除去
            $kessan_kijun = $this->setKijunYmd($ed_date_y, $kesan_st_date, $kesan_ed_date, $st_date, $ed_date);
            $select = $this->getBumonCsvDetail($db, $ymd_st, $ymd_ed, $kessan_kijun, $bumon_cd);
            // 不要な項目を削除
            foreach ($select as $key2 => $value2) {
                unset($select[$key2]['keijo_ymd']);
                unset($select[$key2]['seko_no']);
            }
            if(Msi_Sys_Utils::myCount($select) > 0){
                $select = $this->getTotalInfo($db, $select, $ymd_st, $ymd_ed, $kessan_kijun, $bumon_cd);
            }
        }
        if($output_kbn != 6){
            if (Msi_Sys_Utils::myCount($select) === 0) {
                return null;
            }
            $csvData = array();
            $csvline = array();
            
            // CSVのヘッダー情報を設定
            foreach ($select[0] as $column => $value) {
                $csvline[] = $column;
            }
            $csvData[] = $csvline;
            
            // CSVのデータ部を出力
            foreach ($select as $rec) {
                $csvline = array();
                foreach ($rec as $value) {
                    $csvline[] = $value;
                }
                $csvData[] = $csvline;
            }
            if($output_kbn == 7){
                // 部門情報取得
                $bumon = $this->getBumonInfo($db, $bumon_cd);
                // 先頭行に部門コード、部門名追加
                array_unshift($csvData, array($bumon_cd,$bumon['bumon_lnm'],'','','','','','','','','','','','','',''));
            }
        }
        return $csvData;
    }
    /**
     * 
     * 部門別葬儀売上集計データ取得
     * 
     * @param type $db
     * @param type $ymd_st  対象期間開始日
     * @param type $ymd_ed  対象期間終了日
     * @param type $kessan_kijun  決算基準日
     * @param type $bumon_cd  部門コード
     * @return type
     */
    private function getBumonCsvDetail($db, $ymd_st, $ymd_ed, $kessan_kijun, $bumon_cd){
        $select  = $db->easySelect(<<< END_OF_SQL
            SELECT 
                 ROW_NUMBER() OVER (ORDER BY T.sougi_ymd,T.seko_no) AS "件数"
                ,T.sougi_ymd                                AS "施行日"
                ,T.sekyu_nm                                 AS "施主名"
                ,T.k_nm                                     AS "故人氏名"
                ,T.sekyu_addr                               AS "住所"
                ,T.sekyu_tel                                AS "電話番号"
                ,T.seko_plan_nm                             AS "プラン名"
                ,T.sougi_prc                                AS "葬儀代"
                ,T.henrei_prc                               AS "返礼品代"
                ,T.ryori_prc                                AS "料理代"
                ,T.betto_prc                                AS "供花類"
                ,T.other_prc                                AS "その他"
                ,T.sougi_prc + T.henrei_prc + T.ryori_prc + T.betto_prc + T.other_prc
                                                            AS "合計"
                ,0                                          AS "累計"
                ,T.basho_nm                                 AS "葬儀場所"
                ,T.seko_tanto_nm                            AS "担当者"
                ,T.sougi_ymd2
                ,T.seko_no
            FROM (
                SELECT
                     sk.seko_no
                    ,ud.uri_den_no
                    ,TO_CHAR(sk.sougi_ymd,'MM月DD日')                           AS sougi_ymd
                    ,ud.sekyu_nm                                                AS sekyu_nm
                    ,sk.k_nm                                                    AS k_nm
                    ,CONCAT(COALESCE(ud.sekyu_addr1,''),COALESCE(ud.sekyu_addr2,'')) AS sekyu_addr
                    ,ud.sekyu_tel                                               AS sekyu_tel
                    ,CASE 
                        WHEN LENGTH(COALESCE(spm.seko_plan_cd,'')) = 0 THEN NULL
                        ELSE CONCAT(spm.seko_plan_cd,':',spm.seko_plan_nm)              
                     END                                                        AS seko_plan_nm
                    ,COALESCE(udm0010.sum,0)                                    AS sougi_prc    -- 葬儀代
                    ,COALESCE(udm0020.sum,0)                                    AS henrei_prc   -- 返礼品代
                    ,COALESCE(udm0030.sum,0)                                    AS ryori_prc    -- 料理代
                    ,COALESCE(udmBetto.sum,0)                                   AS betto_prc    -- 供花類
                    ,0                                                          AS other_prc    -- その他
                    ,CASE sk.keishiki_cd
                        WHEN '4' THEN nitei_k.basho_nm
                        ELSE          nitei_s.basho_nm
                     END                                                        AS basho_nm
                    ,CASE 
                        WHEN LENGTH(COALESCE(sk.seko_tanto_cd,'')) = 0 THEN NULL
                        ELSE CONCAT(sk.seko_tanto_cd,':',tanto.tanto_nm)
                     END                                                        AS seko_tanto_nm
                    ,TO_CHAR(sk.sougi_ymd, 'YYYYMMDD')                          AS sougi_ymd2
                FROM seko_kihon_info sk -- 施行基本情報
                INNER JOIN uriage_denpyo ud         -- 売上伝票
                ON  ud.seko_no    = sk.seko_no
                AND ud.data_kbn   = 1
                AND ud.delete_flg = 0
                LEFT JOIN seko_nitei nitei_s        -- 施行日程（葬儀）
                ON  nitei_s.seko_no    = sk.seko_no
                AND nitei_s.nitei_kbn  = 11
                AND nitei_s.delete_flg = 0
                LEFT JOIN seko_nitei nitei_k        -- 施行日程（火葬）
                ON  nitei_k.seko_no    = sk.seko_no
                AND nitei_k.nitei_kbn  = 6
                AND nitei_k.delete_flg = 0
                LEFT JOIN seko_plan_mst spm         -- 施行プラン
                ON  spm.seko_plan_cd = sk.seko_plan_cd
                AND spm.gojokai_kbn  = sk.gojokai_kbn
                AND spm.delete_flg   = 0
                LEFT JOIN ( -- 葬儀代
                    SELECT 
                         udm.uri_den_no
                        ,COALESCE(SUM(udm.uri_prc + udm.out_zei_prc + udm.nebiki_prc + udm.gojokai_nebiki_prc),0) AS sum
                    FROM uriage_denpyo_msi udm
                    INNER JOIN uriage_denpyo ud
                    ON  ud.uri_den_no = udm.uri_den_no
                    AND ud.data_kbn   = 1
                    AND ud.delete_flg = 0
                    INNER JOIN seko_kihon_info sk
                    ON  sk.seko_no    = ud.seko_no
                    AND sk.delete_flg = 0
                    WHERE udm.dai_bunrui_cd NOT IN('0020','0030') -- 返礼品代、料理代以外
                    AND udm.delete_flg = 0
                    AND TO_CHAR(sk.sougi_ymd, 'YYYY/MM/DD') BETWEEN :st_ymd AND :ed_ymd
                    GROUP BY udm.uri_den_no
                ) udm0010
                ON  udm0010.uri_den_no = ud.uri_den_no
                LEFT JOIN ( -- 返礼品代
                    SELECT 
                         udm.uri_den_no
                        ,COALESCE(SUM(udm.uri_prc + udm.out_zei_prc + udm.nebiki_prc + udm.gojokai_nebiki_prc),0) AS sum
                    FROM uriage_denpyo_msi udm
                    INNER JOIN uriage_denpyo ud
                    ON  ud.uri_den_no = udm.uri_den_no
                    AND ud.data_kbn   = 1
                    AND ud.delete_flg = 0
                    INNER JOIN seko_kihon_info sk
                    ON  sk.seko_no    = ud.seko_no
                    AND sk.delete_flg = 0
                    WHERE udm.dai_bunrui_cd IN('0020')
                    AND udm.delete_flg = 0
                    AND TO_CHAR(sk.sougi_ymd, 'YYYY/MM/DD') BETWEEN :st_ymd AND :ed_ymd
                    GROUP BY udm.uri_den_no
                ) udm0020
                ON  udm0020.uri_den_no = ud.uri_den_no
                LEFT JOIN ( -- 料理代
                    SELECT 
                         udm.uri_den_no
                        ,COALESCE(SUM(udm.uri_prc + udm.out_zei_prc + udm.nebiki_prc + udm.gojokai_nebiki_prc),0) AS sum
                    FROM uriage_denpyo_msi udm
                    INNER JOIN uriage_denpyo ud
                    ON  ud.uri_den_no = udm.uri_den_no
                    AND ud.data_kbn   = 1
                    AND ud.delete_flg = 0
                    INNER JOIN seko_kihon_info sk
                    ON  sk.seko_no    = ud.seko_no
                    AND sk.delete_flg = 0
                    WHERE udm.dai_bunrui_cd IN('0030')
                    AND udm.delete_flg = 0
                    AND TO_CHAR(sk.sougi_ymd, 'YYYY/MM/DD') BETWEEN :st_ymd AND :ed_ymd
                    GROUP BY udm.uri_den_no
                ) udm0030
                ON  udm0030.uri_den_no = ud.uri_den_no
                LEFT JOIN ( -- 供花類
                    SELECT 
                         ud.seko_no
                        ,COALESCE(SUM(ud.nyukin_prc + ud.seikyu_zan),0) AS sum
                    FROM uriage_denpyo ud
                    LEFT JOIN seko_kihon_info sk
                    ON  sk.seko_no    = ud.seko_no
                    AND sk.delete_flg = 0
                    WHERE ud.data_kbn = 4
                    AND ud.delete_flg = 0
                    AND TO_CHAR(sk.sougi_ymd, 'YYYY/MM/DD') BETWEEN :st_ymd AND :ed_ymd
                    GROUP BY ud.seko_no
                ) udmBetto
                ON  udmBetto.seko_no = ud.seko_no
                LEFT JOIN tanto_mst tanto           -- 担当者
                ON  tanto.tanto_cd = sk.seko_tanto_cd
                WHERE 
                    sk.moushi_kbn = 1 -- 申込区分
                AND ud.data_kbn   = 1 -- データ区分
                AND sk.delete_flg = 0
                AND TO_CHAR(sk.sougi_ymd, 'YYYY/MM/DD') BETWEEN :st_ymd AND :ed_ymd
                AND ud.bumon_cd = :bumon_cd
            ) T
            ORDER BY 
                 T.sougi_ymd
                ,T.seko_no
END_OF_SQL
            , array(
                  'st_ymd'   => $ymd_st
                , 'ed_ymd'   => $ymd_ed
                , 'bumon_cd' => $bumon_cd
        ));
        // 累計を取得
        foreach ($select as $key => $value) {
            $select[$key]["累計"] = $this->getRuikeiPrcSum($db, $kessan_kijun, $value['sougi_ymd2'], $bumon_cd);
        }
        return $select;
    }
    /**
     * 
     * 合計行を追加
     * 
     * @param type $db
     * @param type $select
     * @param type $ymd_st
     * @param type $ymd_ed
     * @param type $kessan_kijun
     * @param type $bumon_cd
     * @return type
     */
    private function getTotalInfo($db, $select, $ymd_st, $ymd_ed, $kessan_kijun, $bumon_cd){
        $sougi_sum  = 0;
        $henrei_sum = 0;
        $ryori_sum  = 0;
        $kyoka_sum  = 0;
        $other_sum  = 0;
        $total_sum  = 0;
        // 対象期間
        $st_date = str_replace("/", "", $ymd_st); // 対象期間開始日「/」を除去
        $ed_date = str_replace("/", "", $ymd_ed); // 対象期間終了日「/」を除去
        $ed_date_m = ltrim(substr($ed_date,4 , 2),'0');
        // 件数取得
        $cnt = Msi_Sys_Utils::myCount($select);
        $result = $select;
        // 空行追加
        $result[] = array('','','','','','','','','','','','','','','','');
        // 一般受注、当社葬家合計金額取得
        $bechu_sum = $this->getOtherGokeiPrc($db, $st_date, $ed_date, $bumon_cd);
        // 一般受注、当社葬家累計金額取得
        $bechu_ruikei_prc = $this->getOtherRuikeiPrc($db, $kessan_kijun, $ed_date, $bumon_cd);
        // 合計金額取得
        foreach ($select as $value) {
            $sougi_sum  += $value["葬儀代"];
            $henrei_sum += $value["返礼品代"];
            $ryori_sum  += $value["料理代"];
            $kyoka_sum  += $value["供花類"];
            $other_sum  += $value["その他"];
            $total_sum  += $value["合計"];
        }
        // 合計行追加
        $result[] = array('',$ed_date_m.'月施行件数',$cnt,'','','','合計',$sougi_sum,$henrei_sum,$ryori_sum,$kyoka_sum,$other_sum,$total_sum,'その他売上',$bechu_sum,'');
        // 累計金額取得
        $sougi_ruikei_prc  = $this->getRuikeiPrc($db, $kessan_kijun, $ed_date, $bumon_cd, false, array('0020','0030'));
        $henrei_ruikei_prc = $this->getRuikeiPrc($db, $kessan_kijun, $ed_date, $bumon_cd, true , array('0020'));
        $ryori_ruikei_prc  = $this->getRuikeiPrc($db, $kessan_kijun, $ed_date, $bumon_cd, true , array('0030'));
        $kyoka_ruikei_prc  = $this->getRuikeiPrc($db, $kessan_kijun, $ed_date, $bumon_cd, true , null, 4);
        $other_ruikei_prc  = 0;
        $total_ruikei_prc  = $sougi_ruikei_prc + $henrei_ruikei_prc + $ryori_ruikei_prc + $kyoka_ruikei_prc + $other_ruikei_prc;
        // 累計行追加
        $result[] = array('','','','','','','累計',$sougi_ruikei_prc,$henrei_ruikei_prc,$ryori_ruikei_prc,$kyoka_ruikei_prc,$other_ruikei_prc,$total_ruikei_prc,'その他累計',$bechu_ruikei_prc,'');
        return $result;
    }
    /**
     * 
     * その他売上の合計金額を取得
     * 
     * @param type $db
     * @param type $st_date
     * @param type $ed_date
     * @param type $bumon_cd
     * @return int
     */
    private function getOtherGokeiPrc($db, $st_date, $ed_date, $bumon_cd){
        $select  = $db->easySelOne(<<< END_OF_SQL
            --その他売上の合計金額
            SELECT
                COALESCE(SUM(ud.nyukin_prc + ud.seikyu_zan),0) AS sum
            FROM uriage_denpyo ud
            WHERE ud.data_kbn IN (3)
            AND ud.delete_flg = 0
            AND TO_CHAR(ud.keijo_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
            AND ud.bumon_cd = :bumon_cd
END_OF_SQL
            , array(
                  'st_ymd'   => $st_date
                , 'ed_ymd'   => $ed_date
                , 'bumon_cd' => $bumon_cd
        ));
        if(Msi_Sys_Utils::myCount($select) > 0){
            return $select['sum'];
        }
        return 0;
    }
    /**
     * 
     * その他売上の累計金額を取得
     * 
     * @param type $db
     * @param type $kessan_kijun
     * @param type $keijo_ymd
     * @param type $bumon_cd
     * @return int
     */
    private function getOtherRuikeiPrc($db, $kessan_kijun, $keijo_ymd, $bumon_cd){
        $select  = $db->easySelOne(<<< END_OF_SQL
            --その他売上の累計金額
            SELECT
                COALESCE(SUM(ud.nyukin_prc + ud.seikyu_zan),0) AS sum
            FROM uriage_denpyo ud
            WHERE ud.data_kbn IN (3)
            AND ud.delete_flg = 0
            AND TO_CHAR(ud.keijo_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
            AND ud.bumon_cd = :bumon_cd
END_OF_SQL
            , array(
                  'st_ymd'   => $kessan_kijun[0]
                , 'ed_ymd'   => $keijo_ymd
                , 'bumon_cd' => $bumon_cd
        ));
        if(Msi_Sys_Utils::myCount($select) > 0){
            return $select['sum'];
        }
        return 0;
    }
    /**
     * 
     * 部門別葬儀売上集計 累計金額取得
     * 
     * @param type $db
     * @param type $kessan_kijun    決算日
     * @param type $keijo_ymd       売上計上日
     * @param type $bumon_cd        部門コード
     * @param type $flg             true：大分類コードの条件　false：大分類コードのNOT条件
     * @param type $dai_burnui_cd_ary　大分類コード
     */
    private function getRuikeiPrc($db, $kessan_kijun, $keijo_ymd, $bumon_cd, $flg = true, $dai_burnui_cd_ary = null, $data_kbn = 1){
        if(isset($dai_burnui_cd_ary)){
            $add_sql = "";
            $burnui_cds = "";
            if($flg){
                foreach ($dai_burnui_cd_ary as $value) {
                    if(strlen($burnui_cds) > 0){
                        $burnui_cds .= ",'$value'";
                    }else{
                        $burnui_cds .= "'$value'";
                    }
                }
                $add_sql = "AND udm.dai_bunrui_cd IN ($burnui_cds)";
            }else{
                foreach ($dai_burnui_cd_ary as $value) {
                    if(strlen($burnui_cds) > 0){
                        $burnui_cds .= ",'$value'";
                    }else{
                        $burnui_cds .= "'$value'";
                    }
                }
                $add_sql = "AND udm.dai_bunrui_cd NOT IN ($burnui_cds)";
            }
            $select  = $db->easySelOne(<<< END_OF_SQL
                SELECT
                   COALESCE(SUM(udm.uri_prc + udm.out_zei_prc + udm.nebiki_prc + udm.gojokai_nebiki_prc),0) AS sum
                FROM uriage_denpyo_msi udm
                INNER JOIN uriage_denpyo ud
                ON  ud.uri_den_no = udm.uri_den_no
                AND ud.data_kbn   IN ($data_kbn)
                AND ud.delete_flg = 0
                WHERE udm.delete_flg = 0
                AND TO_CHAR(ud.keijo_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
                AND ud.bumon_cd = :bumon_cd
                $add_sql
END_OF_SQL
                , array(
                      'st_ymd'   => $kessan_kijun[0]
                    , 'ed_ymd'   => $keijo_ymd
                    , 'bumon_cd' => $bumon_cd
            ));
            if(Msi_Sys_Utils::myCount($select) > 0){
                return $select['sum'];
            }
            return 0;
        }else{
            $select  = $db->easySelOne(<<< END_OF_SQL
                SELECT
                    SUM(ud.nyukin_prc + ud.seikyu_zan) AS sum
                FROM uriage_denpyo ud
                INNER JOIN seko_kihon_info ski
                ON  ski.seko_no    = ud.seko_no
                AND ski.delete_flg = 0
                LEFT JOIN uriage_denpyo ud2
                ON  ud2.seko_no  = ud.seko_no
                AND ud2.data_kbn = 1
                AND ud2.delete_flg = 0
                WHERE ud.data_kbn IN ($data_kbn)
                AND ud.delete_flg = 0
                --AND TO_CHAR(ud.keijo_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
                AND CASE ud.data_kbn
                        WHEN 4 THEN TO_CHAR(ud2.keijo_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
                        ELSE        TO_CHAR(ud.keijo_ymd, 'YYYYMMDD')  BETWEEN :st_ymd AND :ed_ymd
                    END 
                AND ud.bumon_cd = :bumon_cd
END_OF_SQL
                , array(
                      'st_ymd'   => $kessan_kijun[0]
                    , 'ed_ymd'   => $keijo_ymd
                    , 'bumon_cd' => $bumon_cd
            ));
            if(Msi_Sys_Utils::myCount($select) > 0){
                return $select['sum'];
            }
            return 0;
        }
    }
    /**
     * 
     * 決算日を取得
     * 
     * @param type $ed_date_y
     * @param type $kesan_st_date 決算開始日（月日）
     * @param type $kesan_ed_date 決算終了日（月日）
     * @param type $st_date       対象開始日
     * @param type $ed_date       対象終了日
     * @param type $num           マイナス年数
     * @return type
     */
    private function setKijunYmd($ed_date_y, $kesan_st_date, $kesan_ed_date, $st_date, $ed_date, $num = 1){
        // 前年
        $year = date("Y",strtotime("-$num year"));
        if($ed_date_y.$kesan_ed_date >= $ed_date && $year.$kesan_st_date > $ed_date){
            // 決算終了日と決算開始日が、対象期間終了日以下の場合
            $num++;
            return $this->setKijunYmd($year, $kesan_st_date, $kesan_ed_date, $st_date, $ed_date, $num);
        }else{
            return array($year.$kesan_st_date, $ed_date_y.$kesan_ed_date);
        }
    }
    
    /**
     * CSV明細を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2021/12/xx
     * @param array $rec 入力レコード
     * @param array $shohin_ary	 商品コード一覧
     * @return array 該当データがない場合はarray()を返す
     */
    private function getCsvDetail($rec, $shohin_ary) {
        $csvData = array();
        foreach ($rec as $row) {
            $souke_nm = null;
            if (isset($row['souke_nm'])) {
                $souke_nm = $row['souke_nm'] . '家';
            }
            $csvWork = array();
            $csvWork[] = $row['bumon_cd'];
            $csvWork[] = $row['bumon_lnm'];
            $csvWork[] = $row['tanto_cd'];
            $csvWork[] = $row['tanto_nm'];
            $csvWork[] = $row['data_kbn_nml'];
            $csvWork[] = $row['juchu_ymd'];
            $csvWork[] = $row['uketuke_ymd'];
            $csvWork[] = $row['die_ymd'];           // 死亡日
            $csvWork[] = $row['uri_den_no'];
            $csvWork[] = $row['seko_no'];
            $csvWork[] = $row['daicho_no'];         // 過去法事・葬儀施行番号
            $csvWork[] = $souke_nm;
            $csvWork[] = $row['houyo_nm'];          // 施行法要名
            $csvWork[] = $row['sekyu_nm'];
            $csvWork[] = $row['houyo_basho_nm'];    // ご法要場所
            $csvWork[] = $row['houen_basho_nm'];    // ご法宴場所
            $csvWork[] = $row['sosogirei_prc'];     // 葬送儀礼
            $csvWork[] = $row['omotenashi_prc'];    // おもてなし
            $csvWork[] = $row['sonota_prc'];        // その他
            $csvWork[] = $row['gokei_prc'];         // 合計
            foreach ($shohin_ary as $key => $value) {
                $csvWork[] = $row['prc' . $key];
            }
            $csvData[] = $csvWork;
        }
        return $csvData;
    }
    
    	/**
     * WHERE句を取得
     * <AUTHOR> Matsuda
     * @since      2024/04/
     * @param type $data_kbn データ区分
     * @return string WHERE句
     */
    private static function getWhere($data_kbn)
    {
        $where = null;
        // データ区分
        if (isset($data_kbn) && count($data_kbn) > 0) {
            $ary = array();
            foreach ($data_kbn as $value) {
                $ary[] = $value;
            }
            if (isset($where)) { $where .= ' AND '; }
            $where .= "h.data_kbn IN (".implode(',', $ary).")";
        }
        return $where;
    }
    /**
     * 部門情報を取得
     * 
     * @param type $db
     * @param type $bumon_cd
     */
    private function getBumonInfo($db, $bumon_cd){
        // 部門別葬儀売上集計データ
        $bumon = DataMapper_BumonMst::findOne($db,array('bumon_cd' => $bumon_cd));
        if(Msi_Sys_Utils::myCount($bumon) > 0){
            if(!isset($bumon['bumon_lnm'])){
                $bumon['bumon_lnm'] = "";
            }
            if(!isset($bumon['bumon_snm'])){
                $bumon['bumon_snm'] = "";
            }
            return $bumon;
        }else{
            $bumon = array(
                'bumon_snm' => "",
                'bumon_lnm' => "",
            );
            return $bumon;
        }
    }
    /**
     * 
     * 部門別葬儀売上集計 累計金額合計取得
     * 
     * @param type $db
     * @param type $kessan_kijun    決算日
     * @param type $sougi_ymd       葬儀日
     * @param type $bumon_cd        部門コード
     */
    private function getRuikeiPrcSum($db, $kessan_kijun, $sougi_ymd, $bumon_cd){
        $prc_sum = 0;
        $select  = $db->easySelect(<<< END_OF_SQL
            SELECT
                ud.seko_no,
                COALESCE(SUM(ud.nyukin_prc + ud.seikyu_zan),0) AS sum
            FROM uriage_denpyo ud
            INNER JOIN seko_kihon_info ski
            ON  ski.seko_no    = ud.seko_no
            AND ski.delete_flg = 0
            WHERE ud.data_kbn IN (1)
            AND ud.delete_flg = 0
            AND TO_CHAR(ski.sougi_ymd, 'YYYYMMDD') BETWEEN :st_ymd AND :ed_ymd
            AND ud.bumon_cd = :bumon_cd
            GROUP BY ud.seko_no
END_OF_SQL
            , array(
                  'st_ymd'   => $kessan_kijun[0]
                , 'ed_ymd'   => $sougi_ymd
                , 'bumon_cd' => $bumon_cd
        ));
        if(Msi_Sys_Utils::myCount($select) > 0){
            foreach ($select as $value) {
                $prc_sum += $value['sum'];
                $select2  = $db->easySelect(<<< END_OF_SQL
                    SELECT
                        COALESCE(SUM(ud.nyukin_prc + ud.seikyu_zan),0) AS sum
                    FROM uriage_denpyo ud
                    WHERE ud.data_kbn IN (4)
                    AND ud.delete_flg = 0
                    AND ud.bumon_cd = :bumon_cd
                    AND ud.seko_no  = :seko_no
END_OF_SQL
                    , array(
                          'seko_no'  => $value['seko_no']
                        , 'bumon_cd' => $bumon_cd
                ));
                if(Msi_Sys_Utils::myCount($select2) > 0){
                    foreach ($select2 as $value2) {
                        $prc_sum += $value2['sum'];
                    }
                }
            }
            return $prc_sum;
        }
        return 0;
    }
}
