@charset "UTF-8";
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.small {
  font-size: 85%;
}

.small2 {
  font-size: 80%;
}

.small3 {
  font-size: 75%;
}

.smallest {
  font-size: 70%;
}

.select2-container .select2-choice {
  background-image: none;
}

.select2-drop-mask {
  background-image: none;
}

.select2-dropdown-open .select2-choice {
  background-image: none;
}

.select2-container-multi .select2-choices {
  background-image: none;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background-image: none;
}

.my-error {
  border: 1px solid red !important;
}

#order {
  position: fixed;
  width: 100%;
  height: 87.5%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  top: 40px;
  left: 0;
  background-color: #D4E5F4;
  box-shadow: -2px 0 2px rgba(0, 0, 0, 0.0);
  line-height: 1;
  z-index: 2;
  /* cf. juchu.estimate.css */
  /*disable状態のアクティブを元のボタンの状態にする*/
}

#order a {
  text-decoration: none;
  color: inherit;
}
#order h2 {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 16px;
  color: #266CA5;
}
#order input {
  font-weight: bold;
}
#order input:disabled, #order input[readonly] {
  color: #545454;
  pointer: auto;
}
#order .page-title {
  display: inline;
  margin-top: 5px;
  margin-bottom: 10px;
  float: left;
}
#order .page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #ff9900;
  font-size: 1.2em;
}
#order fieldset.type_1 {
  margin-top: -20px;
  display: inline;
  margin-bottom: 0;
}
#order fieldset.base_1 {
  border: 1px solid #88B1D1;
}
#order fieldset.base_2 {
  border: 1px solid #88B1D1;
  border-top: none;
}
#order .dlg_note,
#order .dlg_date,
#order .dlg_deliv,
#order .dlg_denpyo_no,
#order .dlg_sekyu_cd,
#order .dlg_staff,
#order .dlg_item,
#order .dlg_bill,
#order .dlg_summary {
  width: 2%;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  border-top: none;
  border-right: 1px solid #CCC;
  border-bottom: none;
  border-left: 1px solid #CCC;
}
#order .dlg_staff {
  border-right: none;
}
#order fieldset.note_1 {
  margin-top: 10px;
}
#order .header .row {
  width: 4%;
}
#order .header .control {
  width: 4%;
}
#order .header .type {
  width: 6%;
}
#order .header .item_id {
  width: 10%;
  border-right: 1px solid #88B1D1;
}
#order .header .item {
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}
#order .header .quantity {
  width: 7%;
  left: 50%;
}
#order .header .unit {
  width: 7%;
  left: 50%;
}
#order .header .price, #order .header .cost {
  width: 9%;
  border-bottom: 1px solid #88B1D1;
}
#order .header .nonyu_dt {
  width: 12%;
  left: 50%;
}
#order .header .soko_nm,
#order .header .kamoku_nm {
  width: 20%;
  left: 50%;
}
#order .header .nyuka_dt {
  width: 12%;
  left: 50%;
}
#order .header .memo {
  width: 20%;
  border-bottom: 1px dotted #88B1D1;
  border-right: 1px dotted #88B1D1;
}
#order .header table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background: none;
  background-color: #E8F3FB;
  font-size: 13px;
}
#order .header table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  font-weight: bold;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
}
#order .items {
  height: 58%;
  overflow: auto;
  border: 1px solid #88B1D1;
  background-color: #EBF3FA;
  -webkit-overflow-scrolling: touch;
}
#order .items #add {
  width: 100%;
  height: 60px;
  background-image: url(../../../img/plus_1.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 26px 26px;
  text-indent: -999px;
}
#order .items #add:active {
  background-image: url(../../../img/plus_1_on.png);
}
#order .label,
#order label,
#detail .label,
#detail label,
#order .header table td{
	background-color: #b0e0e6;
}
#order .lbl_tax {
  width: 12%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_denpyo_no,
#order .lbl_order_ymd,
#order .lbl_siire_cd,
#order .lbl_nonyu_cd {
  width: 7%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_sime_ymd,
#order .lbl_harai_yotei_ymd {
  width: 8%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_harai_kbn {
    margin-left: 38.22%;
	width: 7.5%;
	border-bottom: none;
	border-top: 1px solid #88B1D1;
	border-left: 1px solid #88B1D1;
}    
#order .lbl_staff,
#order .lbl_tax {
  width: 9.6%;
  border-bottom: none;
  border-top: none;
}
#order .lbl_nonyu_dt {
  width: 9%;
  border-bottom: none;
  border-top: none;
}
#order .note_1 .lbl_note,
#order .note_2 .lbl_note,
#order .note_x .lbl_note {
  width: 7%;
  border-bottom: 1px solid #88B1D1;
  border-left: 1px solid #88B1D1;
}
#order .note_x .lbl_note,
#order .note_x input {
  visibility: hidden;
}
#order .note_1 input,
#order .note_2 input,
#order .note_x input,
#order .total_title,
#order .total_price {
  border-bottom: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .note_1 .lbl_note,
#order .note_1 input,
#order .note_1 .label {
  border-top: 1px solid #88B1D1;
}
#order .total_title {
  border-left: 1px solid #88B1D1;
  width: 10%;
  text-align: center;
  padding: 10px 0;
}
#order .total_price {
  width: 9%;
  text-align: right;
  font-weight: normal;
  font-size: inherit;
  color: inherit;
  background-color: #f4f4f4;
}
#order .staff {
  width: 14.22%;
}
#order #note_id_1,
#order #note_id_2,
#order #note_id_x,
#order #juchu_ymd,
#order #nonyu_dt,
#order #denpyo_no,
#order #siire_cd,
#order #nonyu_cd,
#order #tanto_cd,
#order #tanto_nm {
  border-right: none;
}
#order #order_ymd,
#order #nonyu_dt,
#order #denpyo_no,
#order #siire_cd,
#order #nonyu_cd,
#order #tanto_cd,
#order #tanto_nm,
#order #siire_nm,
#order #nonyu_nm,
#order #sime_ymd,
#order #harai_yotei_ymd {
  border-bottom: none;
  border-top: none;
}
#order #nonyu_nm {
  width: 15.6%;
}
#order #siire_nm {
  width: 15.6%;
}
#order #denpyo_biko1,
#order #denpyo_biko2,
#order #note_txt_x {
  width: 43.3%;
  border-right: none;
}
#order .list table {
  width: 100%;
  border-bottom: 1px solid #88B1D1;
  border-collapse: collapse;
  background-color: #FFF;
}
#order .list table tbody:nth-child(even) {
  background-color: #F5F9FD;
}
#order .list table td {
  width: 5%;
  height: 30px;
  background-color: transparent;
  text-align: center;
  color: #286EA6;
  font-weight: bold;
  border-right: 1px solid #88B1D1;
  border-bottom: 1px solid #88B1D1;
  background-color: white;
}
#order .list .row {
  width: 4%;
  text-align: center;
}
#order .list .control {
  width: 4%;
  text-align: center;
}
#order .list .control i {
  cursor: pointer;
}
#order .list .type {
  width: 6%;
  color: #000;
}
#order .list .item_id {
  width: 10%;
  border-right: 1px solid #88B1D1;
}
#order .list .dlg_item {
  width: 3%;
  left: 22%;
}
#order .list .item {
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .item input,
#order .list .item div {
  position: absolute;
  top: 0;
  left: 0;
  height: 30px;
  border-right: none;
}
#order .list .quantity {
  width: 7%;
}
#order .list .unit {
  width: 7%;
  color: #000;
}
#order .list .price {
  width: 9%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .cost {
  width: 9%;
  border-bottom: 1px solid #88B1D1;
}
//#order .list .noki_ymd {
//  width: 9%;
//  border-bottom: 1px dotted #88B1D1;
//}
#order .list .nonyu_ymd {
  width: 12%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .nyuka_dt {
//  width: 9%;
  width: 12%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .soko_cd,
#order .list .kamoku_cd
{
  width: 8%;
  border-bottom: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .list .soko_nm,
#order .list .kamoku_nm{
  width: 12%;
  border-bottom: 1px solid #88B1D1;
  border-right: none;
}
#order .list .memo {
  width: 20%;
  border-right: none;
}
#order .list .hachu_gaku_cal input {
  text-align: right;
}

#order .list .dlg_shohin {
  float: right;
  width: 25%;
  border: none;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  background-color: transparent;
}
#order .list .dlg_soko,
#order .list .dlg_kamoku,
#order .list .dlg_date {
  float:right;
//  width: 10%;
  width: 25%;
  border: none;
  background-image: url(../../../img/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  background-color: transparent;
}
#order .list .item_nm {
//  width: 21.95%;
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}
#order .list .tekiyo {
  width: 20%;
  border-bottom: 1px solid #88B1D1;
}

#order .list .no-border-bottom {
  border-bottom: none !important;
}
#order .list .my-border-bottom {
  border-bottom: 1px solid #88B1D1;
}
#order .list .my-input-like {
  padding-right: 7px;
}
#order .list .soko_cd,
#order .list .kamoku_cd   {
  width: 8%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  height: 28px;
}
#order .list .soko_nm,
#order .list .kamoku_nm {
  width: 12%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  height: 28px;
}

#order .list .my-txt2, #order .list .my-txt30, #order .list .my-txt50, #order .list .my-txt60, #order .list .my-txt75, #order .list .my-txt80, #order .list .my-txt85, #order .list .my-txt90, #order .list .my-txt95 {
  border: none !important;
  width: 95%;
  vertical-align: middle;
  border-radius: 4px;
  padding: 1px 4px;
  background-color: inherit;
  height: 28px;
}
#order .list .my-txt2:enabled, #order .list .my-txt50:enabled, #order .list .my-txt60:enabled, #order .list .my-txt75:enabled, #order .list .my-txt80:enabled, #order .list .my-txt85:enabled, #order .list .my-txt90:enabled, #order .list .my-txt95:enabled {
  cursor: pointer;
}
#order .list .my-txt30 {
  width: 30% !important;
}
#order .list .my-txt50 {
  width: 50% !important;
}
#order .list .my-txt60 {
  width: 60% !important;
}
#order .list .my-txt75 {
  width: 75% !important;
}
#order .list .my-txt80 {
  width: 80% !important;
}
#order .list .my-txt85 {
  width: 85% !important;
}
#order .list .my-txt90 {
  width: 90% !important;
}
#order .list .my-txt95 {
  width: 95% !important;
}
#order .list .my-error {
  border: 1px solid red !important;
}
#order .lbl_collect_type {
  margin-left: 64%;
  width: 20%;
  border: 1px solid #88B1D1;
  border-right: 3px solid #A1D7F4;
}
//#order .kaishu_kbn-cls {
//  float: left;
//  width: 16%;
//}
#order .tax_kbn-cls {
  float: left;
  width: 8.41%;
}
#order .zei_cd-cls {
  float: left;
  width: 7.85%;
}
#order .select2-choice, #order .select-container {
  height: 28px !important;
  border-radius: 0px !important;
  border: 0px solid #DDD;
  padding-top: 2px !important;
  border-left: none;
}
#order .select2-choices {
  height: 28px !important;
  border-radius: 0px !important;
  padding-top: 2px !important;
}
#order .select2-arrow {
  border-radius: 0px !important;
}
#order .select2-container-multi .select2-choices {
  height: 28px !important;
}
#order .list .select2-choice, #order .list .select-container {
  margin: 0;
  padding: 0;
  border: 1px solid #DDD;
  background-color: transparent;
  background-image: none;
}
#order .list .unit .select2-choice, #order .list .unit .select-container {
  margin: 0;
  padding: 0;
  border: 0px solid #DDD;
  background-color: transparent;
  background-image: none;
}
#order fieldset.base_1 .select2-choice, #order fieldset.base_1 .select-container, #order fieldset.base_2 .select2-choice, #order fieldset.base_2 .select-container {
  height: 30px !important;
  border-top: none;
  border-bottom: none;
  border-right: none;
}
#order #s2id_tax_kbn {
  border-right: 1px solid #88B1D1;
}
#order .my-akaji {
  color: red;
  font-weight: bold;
}
#order .list .my-akaji {
  color: red;
  font-weight: bold;
}
#order .my-bold {
  font-weight: bold;
}
#order .my-error {
  border: 1px solid red !important;
}
#order .my-disabled {
  background-color: #f4f4f4 !important;
  cursor: auto;
}
#order input[readonly], #order input:disabled {
  background-color: #f4f4f4 !important;
  cursor: auto;
}
#order .my-type-date[readonly],
#order .my-type-date:disabled,
#order .my-type-datetime-local[readonly],
#order .my-type-datetime-local:disabled {
  cursor: auto;
}
#order input[type="button"]:disabled {
  opacity: .5;
}
#order input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94B9D6;
  box-shadow: 1px 1px 0 #FFF;
  color: #296FA7;
}
#order .dlg_note,
#order .dlg_date,
#order .dlg_deliv,
#order .dlg_denpyo_no,
#order .dlg_sekyu_cd,
#order .dlg_staff,
#order .dlg_item,
#order .dlg_bill,
#order .dlg_summary {
  background-color:white; 
}
#order .harai_kbn-cls {
  float: left;
  width: 8%;
  border-top: 1px solid #88B1D1;
  border-right: 1px solid #88B1D1;
}
#order .copy_cls span {
    position: absolute;
    display: inline;
    padding: .3em .6em .3em;
    font-weight: bold;
    line-height: 1;
    color: red;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    /*background-color: #ff9900;*/
    font-size: 1.3em;
}
#order .buttons #btn_new {
	background-image: url(../../../img/ico_pen.png);
	background-repeat: no-repeat;
	background-position: 26px 4px;
	background-size: 20px 20px;
}
#order .buttons #btn_new:active {
	background-image: url(../../../img/ico_pen.png);
}
#order .buttons #btn_copy {
	background-image: url(../../../img/ico_doc.png);
	background-repeat: no-repeat;
	background-position: 26px 4px;
	background-size: 20px 20px;
}
#order .buttons #btn_copy:active {
	background-image: url(../../../img/ico_doc.png);
}
#order fieldset.upd_info {
  margin-top: 10px;
}
#order .upd_info .cre_user,
#order .upd_info .mod_user {
    width: 20%;
    border: none;
    background-color:  rgba(0,0,255,0);
}

#order .lbl_juchu_no,
#order .lbl_juchu_seikyusaki {
	width: 5%;
}

#order #juchu_no{
	width: 7%;
}
#order #juchu_seikyusaki{
	width: 14%;
}
#order #juchu_nafuda{
	width: 14%;
}
