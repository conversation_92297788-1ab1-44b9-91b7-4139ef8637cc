@charset "UTF-8";
.page-title {
  margin-top: 60px;
  margin-left: 20px;
  float: left;
}

.page-title span {
  display: inline;
  padding: .3em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  background-color: #f7931e;
  font-size: 1.2em;
}

#order {
  margin-top: 20px;
  padding: 0;
  width: 50%;
  left: 25%;
  border: none;
  box-shadow: none;
  height: 100%;
}

#my-form-id {
  height: 100%;
}

#input-field {
  overflow: auto;
  height: 80%;
  padding-bottom: 5px;
}

#my-form-id .base_1 label {
  width: 30%;
}

#my-form-id .base_1 input.txt {
  width: 70%;
}

#my-form-id .base_1 .file_nm {
  width: 60%;
}

#my-form-id .dlg_zip {
  width: 5%;
  border-right: 1px solid #CCC;
  background-image: url(../../../img-m1/ico_dialog_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  cursor: pointer;
}

#my-form-id .dlg_map {
  width: 5%;
  border-right: 1px solid #CCC;
  background-image: url(../../../img-m1/ico_map_2.png);
  background-size: 13px 17px;
  background-repeat: no-repeat;
  background-position: center center;
  cursor: pointer;
}

#my-form-id .dlg_clip {
  width: 5%;
  border-right: 1px solid #CCC;
  background-image: url(../../../img-m1/ico_clip_2.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  cursor: pointer;
}

#my-form-id .dlg_del {
  width: 5%;
  border-right: 1px solid #CCC;
  background-image: url(../../../img-m1/ico_close.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 12px 10px;
  cursor: pointer;
}

#my-form-id .file_nm {
  cursor: pointer;
}
#my-form-id .file_nm:focus {
  background-color: white;
}

#my-form-id .lbl_ryoshusho_kbn {
  line-height: 1;
  margin: 0;
  border-right: none;
  background-color: #FAFAFA;
  background-image: url(../../../img-m1/check_2.png);
  background-repeat: no-repeat;
  background-position: 10% center;
  background-size: 16px 12px;
  border: none;
  border-bottom: 1px solid #CCC;
  border-radius: 0;
  text-align: center;
  color: #999;
  border-right: 1px solid #CCC;
}

#my-form-id .base_1 label.lbl_ryoshusho_kbn {
  width: 35%;
}

#my-form-id .lbl_ryoshusho_kbn.ui-state-active {
  display: block;
  background-color: #65ab73;
  background-image: url(../../../img-m1/check_1.png);
  text-shadow: -1px -1px 0 #69C;
  color: #FFF;
}

#my-form-id .base_1_upperline {
  border-top: 1px solid #CCC;
}

#logo_img, #syaban_img {
  float: left;
  border-bottom: 1px solid #CCC;
  cursor: pointer;
}

#my-form-id input[type="button"]:disabled {
  opacity: .5;
}

/*disable状態のアクティブを元のボタンの状態にする*/
#my-form-id input[type="button"]:disabled:active {
  background: none;
  border: 1px solid #94d69d;
  box-shadow: 1px 1px 0 #FFF;
  color: #244b20;
}

.my-error {
  border: 1px solid red !important;
}

#char-disp-area,
#about_gaiji_area
{
    box-sizing: border-box;
    padding: 5px;
    background-color: white;
}
#char-disp-area {
	font-family: my-font, "Courier New", monospace;
}
#char-disp-area:focus,
#about_gaiji_area:focus
{
  background-color: white;
}
