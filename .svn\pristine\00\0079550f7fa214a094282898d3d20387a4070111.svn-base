/** 
 * @fileoverview 検品仕入確定(博全社)(葬儀・法事)
 * @version      2020/11/xx mihara
 */
msiGlobalObj.func.selpart01 = function (p_root_id) {
return function(p_adj_h)
{
    "use strict";

    if ( p_adj_h === undefined ) p_adj_h = 12;

    var utils = window.msiBbUtils;
    var viewUtils = window.msiBbViewUtils;
    var mixin = window.msiBbMixin;
    var gMstrData = [];
    var root_id = p_root_id,
        $elRoot = $('#' + root_id),
        $elPar = $elRoot.closest('.comp-parent').length ? $elRoot.closest('.comp-parent') : $elRoot.parent();
    var my$ = function ( sel ) {
        var $el = $elRoot.find( sel );
        return $el;
    };
    var $elCompRoot = my$('.component-root');
    var isNoChangeCheck = false; // true: isChanged() チェックをしない
    // var canCheckOnlyMi  = true; // true:EDIステータス=未回答だけ行選択可 |false:常に行選択可

    var cKenpinStatus_mi =  0; // 検品ステータス:未回答
    var cKenpinStatus_ok =  1; // 検品ステータス:検品OK
    var cKenpinStatus_ng = -1; // 検品ステータス:検品NG

    var _itemMapper =
{
// item           label       initVal   validation     selector         bindings
// juchu_ng_note:   '検品NGコメント  ""       max(60)        .juchu_ng_note   rtCheck',
// kenpin_status_nm:   'EDIステータス   ""       null           .kenpin_status_nm   rtCheck',
// line_no:       '行番号            -1        null             .line_no          ',
kenpin_status:    '検品ステータス  ""  null         _na                _na',
my_kenpin_ts:     '納品日    ""        null         .my_kenpin_ts      rtCheck',
my_kenpin_dt:     '検品日    ""        ymd          .my_kenpin_dt      rtCheck',
my_kenpin_biko:   '検品備考  ""        max(60)      .my_kenpin_biko    rtCheck',
    };

    var _ListModelBase = utils.genModelBase( _itemMapper,
    {
        defaults: function() {
            return {}
        },

        idAttribute: 'myid',

        initialize: function() {

            // this.on('change:kenpin_status change:my_kenpin_dt change:my_kenpin_ts', this.chgKenpinEtc);
            this.on('change', this.chgKenpinEtc);

            this.on('change:kenpin_status', this.chgKenpinStatus);
            this.on('change:my_kenpin_dt', this.chgMyKenpinDt);
            this.on('change:my_kenpin_biko', this.chgMyKenpinBiko);

            this.chgKenpinEtc();
        },

        chgKenpinEtc: function() {
            var _is_changed = this.isKenpinChanged();

            app.model.syncSelectedIds();
            app.trigger( 'my_refresh' );
        },

        isKenpinChanged: function() {
// console.log( 'my_kenpin_ts_org=>', this.getOrgItem('my_kenpin_ts') );
            this.setNoDiff(true);
            var boolean = this.isChangedFromOrg('_'); // ,kenpin_status_org,my_kenpin_dt_org,my_kenpin_ts_org,my_kenpin_biko_org') ) {
            this.setNoDiff(false);
            return boolean;

/* -- OLD
            var kenpin_status     = +this.get('kenpin_status');
            var kenpin_status_org = +this.get('kenpin_status_org');
            var my_kenpin_dt      = this.get('my_kenpin_dt');
            var my_kenpin_dt_org  = this.get('my_kenpin_dt_org');
            var my_kenpin_ts      = this.get('my_kenpin_ts');
            var my_kenpin_ts_org  = this.get('my_kenpin_ts_org');
            var my_kenpin_biko    = this.get('my_kenpin_biko');
            var my_kenpin_biko_org  = this.get('my_kenpin_biko_org');
            if ( kenpin_status != kenpin_status_org || my_kenpin_dt != my_kenpin_dt_org || my_kenpin_ts != my_kenpin_ts_org 
                 || my_kenpin_biko != my_kenpin_biko_org ) {
                return true;
            }
            return false;
-- */
        },

        canChangeKenpin: function() {
            var can_change_kenpin = this.get('can_change_kenpin');
            return !! can_change_kenpin;
        },

        chgKenpinStatus: function() {
            if ( !this.get('can_change_kenpin') ) {
                msiLib2.showErr( 'chgKenpinStatus error' );
                return;
            }

            var kenpin_status = +this.get('kenpin_status');

            if ( kenpin_status == cKenpinStatus_mi ) {
                this.set( { my_kenpin_ts: null,
                            my_kenpin_dt: null } );
            } else {
                var d = new Date();
                var year  = d.getFullYear();
                var month = ('0' + (d.getMonth() + 1)).slice(-2);
                var day   = ('0' + d.getDate()).slice(-2);
                var hour  = ('0' + d.getHours()).slice(-2);
                var min   = ('0' + d.getMinutes()).slice(-2);
                var sec   = ('0' + d.getSeconds()).slice(-2);
                var ymd = year + '/' + month + '/' + day;
                var hm  = hour + ':' + min;  // + ':' + sec;

                // #4537 検品日には施行日が入る。施行と関連しないものは発注日となる
                var my_kenpin_dt = this.get('sougi_ymd') ? this.get('sougi_ymd') : this.get('order_ymd');

                this.set( { my_kenpin_ts: ymd + ' ' + hm,
                            my_kenpin_dt: my_kenpin_dt } );
            }

            /*
            var kenpin_status_org = this.get('kenpin_status_org');
            var kenpin_status     = this.get('kenpin_status');

            if ( kenpin_status_org == kenpin_status ) {
                this.set( { my_kenpin_ts: this.get('my_kenpin_ts_org'),
                            my_kenpin_dt: this.get('my_kenpin_dt_org') } );
            } else {
                var d = new Date();
                var year  = d.getFullYear();
                var month = ('0' + (d.getMonth() + 1)).slice(-2);
                var day   = ('0' + d.getDate()).slice(-2);
                var hour  = ('0' + d.getHours()).slice(-2);
                var min   = ('0' + d.getMinutes()).slice(-2);
                var sec   = ('0' + d.getSeconds()).slice(-2);
                var ymd = year + '/' + month + '/' + day;
                var hm  = hour + ':' + min;  // + ':' + sec;
                this.set( { my_kenpin_ts: ymd + ' ' + hm,
                            my_kenpin_dt: ymd } );
            }
             */
        },

        chgMyKenpinDt: function() {
            var my_kenpin_dt = this.get('my_kenpin_dt');
            if ( $.msiJqlib.isNullEx2(my_kenpin_dt) ) {
                this.set('my_kenpin_dt', null);
            }
        },

        chgMyKenpinBiko: function() {
            var my_kenpin_biko = this.get('my_kenpin_biko');
            if ( $.msiJqlib.isNullEx2(my_kenpin_biko) ) {
                this.set('my_kenpin_biko', null);
            }
        },

        validation: {
            _all: function(val, attr, computed) {
                var _is_changed = this.isKenpinChanged(),
                    kenpin_status_org = +this.get('kenpin_status_org'),
                    kenpin_status     = +this.get('kenpin_status'),
                    my_kenpin_dt      = this.get('my_kenpin_dt'),
                    _siire_fixed_ymd  = app.model.get('_siire_fixed_ymd'),
                    aTgt = [],
                    aMsg = [],
                    msg,
                    that = this;
                var _flg_kenpin_kaijyo = app.model.get('_flg_kenpin_kaijyo'); // 検品解除処理フラグ #7555 検品解除ボタン
                if ( !_is_changed ) { // 変更されていなければエラーチェックなし
                    return;
                }

                if ( kenpin_status != cKenpinStatus_mi ) { // 検品OK/NG が設定されているなら
                    if ( $.msiJqlib.isNullEx2(my_kenpin_dt) ) {
                        // 日付設定がなければエラー
                        aMsg.push('検品日が入力されていません');
                        aTgt.push('.my_kenpin_dt');
                    }
                    if ( !$.msiJqlib.isNullEx2(_siire_fixed_ymd) && my_kenpin_dt <= _siire_fixed_ymd ) {
                        aMsg.push('検品日(' + my_kenpin_dt + ')が仕入確定日(' + _siire_fixed_ymd + ')以前です');
                        aTgt.push('.my_kenpin_dt');
                    }
                    if ( kenpin_status_org != cKenpinStatus_mi && kenpin_status_org != kenpin_status ) {
                        aMsg.push('検品OK/検品NG は直接変更できません. 検品OK/検品NG を解除してから設定してください');
                        aTgt.push('.kenpin_status_ck_cls');
                    }
                } else { // kenpin_status == cKenpinStatus_mi
                    if ( !$.msiJqlib.isNullEx2(my_kenpin_dt) ) {
                        // 日付設定があればエラー
                        aMsg.push('検品チェックがないので検品日は無効です');
                        aTgt.push('.my_kenpin_dt');
                        // aTgt.push('.my_kenpin_dt,.kenpin_status_ok,.kenpin_status_ng');
                    }
                    var my_kenpin_dt_org = this.get('my_kenpin_dt_org');
                    if ( !$.msiJqlib.isNullEx2(_siire_fixed_ymd) && !$.msiJqlib.isNullEx2(my_kenpin_dt_org) &&
                         !_flg_kenpin_kaijyo && // 検品解除処理フラグ OFF
                         my_kenpin_dt_org <= _siire_fixed_ymd ) {
                        aMsg.push('検品(' + my_kenpin_dt_org + ')は仕入確定(' + _siire_fixed_ymd + ')されています');
                        aTgt.push('.my_kenpin_dt');
                    }
                }

                // if ( kenpin_status_org == cKenpinStatus_mi ) {
                //     // 初期状態が未回答で、検品ＯＫも検品ＮＧも設定されていない
                //     if ( kenpin_status != cKenpinStatus_ok && kenpin_status != cKenpinStatus_ng ) {
                //         aMsg.push('検品ＯＫ/検品ＮＧ を選択してください');
                //         aTgt.push('.kenpin_status_ok,.kenpin_status_ng');
                //     }
                // }
                if ( aMsg.length ) {
                    msg = aMsg.join(', ');
                    _.defer( function() { that.trigger('decoErrInfo', aTgt.join(','), msg); } );
                    return msg;
                }
            },
        },
    });
    // console.log( '_ListModelBase=>', _ListModelBase );
    var ListModel = Backbone.Model.extend( _ListModelBase );
    _.defaults( ListModel.prototype, mixin.ModelDataChangedMixin ); // データ変更確認機能付与
    // ListModel.prototype.setNoDiff(true);

    var ListCollection = Backbone.Collection.extend({
        model: ListModel,
        nextSeqNo: function() {
            ++ ListCollection.seq_cnt;
            return ListCollection.seq_cnt;
        },
        myReset: function( offset, data, attr ) {
            if ( offset !== null ) {
                this.setOffset( offset );
            }
            this.reset( data, attr );
            this.markAsOrg();
        },
        _my_offset: 0,
        // 行更新
        updateRows: function( updData ) {
            var that = this;
            _.each( updData,
                    function(updModel) {
                        var m = that.findWithId( updModel.myid );
                        if ( m ) { m.set(updModel); m.markAsOrg(); }
                    }
                  );
            this.markAsOrg();
        },
        setOffset: function(offset) {
            this._my_offset = +offset;
        },
        getOffset: function(offset) {
            return +this._my_offset;
        },
        findWithId: function( id ) {
            var m = this.findWhere( { myid: id } );
            return m;
        },
        myUpdate: function( updData, delData ) {
            if ( delData === undefined ) delData = [];
            // console.log( 'myUpdate=>' + JSON.stringify(updData) );
            var that = this;
            _.each( updData, function(data) {
                that.add(data, {silent: true, merge: true}); // merge:既存があれば上書き
            } );
            _.each( delData, function(data) {
                that.remove(data, {silent: true});
            } );
            this.reset( this.models );
        },
        resetLineNo: function() {
            var i, max, m;
            var offset = this.getOffset();
            for ( i = 0, max=this.length ; i < max ; i++ ) {
                m = this.at(i);
                m.set( 'line_no', offset +i+1, {silent: true} ); // 通し番号
                m.set( 'line_no_p', i+1, {silent: true} ); // ページ内番号
                // console.log( 'seq_no=>' + m.get('seq_no') + ' line_no=>' + m.get('line_no') + ' ' + m.get('msi_biko2') );
            }
        },
        // Collection 全要素の id を返す
        getAllIds: function() {
            return this.pluck('myid');
        },
        // １件以上あれば真を返す
        isAny: function() {
            return this.length > 0;
        },
        // 入力チェック
        validateEx: function () {
            var aMsg = [];
            this.each(function(m, i) {
                var msg, line, resLine;
                // console.log( i +'==>>>  msiCol.each=>' + JSON.stringify(m.toJSON()) );
                resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        msg = line + '行目:' + v;
                        aMsg.push( msg );
                    } );
                }
            });
            return aMsg;
        },
        // 選択行入力チェック
        validateSelected: function () {
            var aMsg = [];
            if ( !app ) return;
            var a_item_ids_selected = app.model.getSelectedIds();
            // console.log( 'a_item_ids_selected=>', a_item_ids_selected );

            this.each(function(m, i) {
                var msg, line, resLine;
                // console.log( i +'==>>>  msiCol.each=>' + JSON.stringify(m.toJSON()) );
                var myid = m.get('myid');
                if ( !_.contains(a_item_ids_selected, myid) ) { // 選択されていなければスキップ
                    return;
                }
                resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        msg = line + '行目:' + v;
                        aMsg.push( msg );
                    } );
                }
            });
            return aMsg;
        },
        // 選択されているデータ行を Model で返す
        getSelected: function () {
            if ( !app ) return null;
            var a_item_ids_selected = app.model.getSelectedIds();
            // console.log( 'a_item_ids_selected=>', a_item_ids_selected );
            var models = this.filter( function(m) {
                var myid = m.get('myid');
                return _.contains(a_item_ids_selected, myid);
            } );
            return models;
        },
        // 選択されているデータ行のみを JSON(JS data) で返す
        toJSONSelected: function () {
            if ( !app ) return [];
            var models = this.getSelected();
            var jsData = _.map( models, function(m) { return m.toJSON(); } );
            return jsData;
        },
        // １行でも変更されているなら真を返す
        isAnyKenpinChanged: function () {
            var _any = this.find( function(m) { return m.isKenpinChanged(); } );
            return !!_any;
        },
        // 一括設定
        allKenpinCheck: function (p_kenpin_status) {
            this.map( function(m) {
                if ( m.canChangeKenpin() ) { m.set('kenpin_status', p_kenpin_status); }
            } );
        },
        // 一括設定  cur_kenpin_status でない値に設定されている場合は設定しない
        allKenpinCheckGuard: function (p_kenpin_status, cur_kenpin_status) {
            this.map( function(m) {
                if ( m.canChangeKenpin() ) {
                    var curVal = m.get('kenpin_status');
                    if ( curVal == cur_kenpin_status || curVal == cKenpinStatus_mi ) {
                        m.set('kenpin_status', p_kenpin_status);
                    }
                }
            } );
        },
    },
    { // classProperties
        seq_cnt: 0
    }
    ); // ListCollection
     _.defaults( ListCollection.prototype, mixin.ModelDataChangedMixin ); // データ変更確認機能付与
    var listCol = new ListCollection;

    var _ListViewBase = utils.genViewBase( _itemMapper,
    {
        tagName:  "tbody",
        template: _.template(my$('.item-template').html()),
        events: {
            "click .dlg_my_kenpin_dt": "setDatePicker",
            "click .kenpin_status_ck_cls": "hClickKenpinStatus",
//            "click .result-list-sel .row": "hClickRow",
            "click .shohin_img_link": "dispShohinImg",
        },

        initialize: function() {
            var that = this;

            Backbone.Validation.bind( this, Backbone.Validation.msi_v_iv_callback2({}, undefined, '.') );

            this.listenTo(this.model, 'change', this.render);
            this.listenTo(this.model, 'decoErrInfo', this.decoErrInfo);
            this.listenTo(this.model, 'clearErrInfo', this.clearErrInfo);

            this.$el.html(this.template(this.model.toJSON()));

            this.$(".my_kenpin_dt").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));

            this.stickit();
        },

        // 商品イメージ表示
        dispShohinImg: function(e) {
            var shohin_gazo_img = this.model.get('shohin_gazo_img');
            var shohin_nm       = this.model.get('shohin_nm');
            if ( $.msiJqlib.isNullEx2(shohin_gazo_img) ) return;

            var url = $.msiJqlib.baseUrl() + '/mref/gazodlg/shohinimg/imgid/' + shohin_gazo_img;
            $.colorbox( {href: url,
                         photo:true, opacity:0.4, maxWidth:"600",
                         title: shohin_nm });
        },

        // 日付ピッカー
        setDatePicker: function(e) {
            if ( $(e.currentTarget).is('[disabled]') ) return;
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            // if ( $target.is('[readonly]') ) return;
            $target.datepicker("show");
        },

        render: function() {
            // console.log('ListView render', this.model.toJSON());

            var kenpin_status_org = this.model.get('kenpin_status_org');
            var kenpin_status     = this.model.get('kenpin_status');
            var myid              = this.model.get('myid');
            var canChangeKenpin   = this.model.canChangeKenpin(); // model.get('can_change_kenpin');

            // 選択エリア(.row) カーソル表示
            // if ( !_is_changed ) { // && kenpin_status_org != cKenpinStatus_mi ) {
            //     this.$('.row').removeClass('can-click');
            // } else {
            //     this.$('.row').addClass('can-click')
            // }

            // 検品OK/検品NG カーソル表示
            if ( canChangeKenpin ) { // kenpin_status_org == cKenpinStatus_mi ) {
                this.$('.kenpin_status_ck_cls').addClass('can-click');
                this.$('.kenpin_status_ck_cls').attr('title', '');
            } else {
                this.$('.kenpin_status_ck_cls').removeClass('can-click');
                this.$('.kenpin_status_ck_cls').attr('title', '変更できません');
            }

            // 検品OK/検品NG 表示
            this.$('.kenpin_status_ck_cls').removeClass('selected'); // 一旦クリア
            if ( kenpin_status == cKenpinStatus_ng ) {
                this.$('.kenpin_status_ng').addClass('selected');
            } else if ( kenpin_status == cKenpinStatus_ok ) {
                this.$('.kenpin_status_ok').addClass('selected');
            }

            // 検品日
            if ( canChangeKenpin ) {
                this.$('.my_kenpin_dt').removeAttr('readonly');
                this.$('.dlg_my_kenpin_dt').removeAttr("disabled");
                this.$('.dlg_my_kenpin_dt').addClass("cursor-pointer");
                this.$('.my_kenpin_dt').attr('title', '');
            } else {
                this.$('.my_kenpin_dt').attr('readonly', 'readonly');
                this.$('.dlg_my_kenpin_dt').attr("disabled", "disabled");
                this.$('.dlg_my_kenpin_dt').removeClass("cursor-pointer");
                this.$('.my_kenpin_dt').attr('title', '変更できません');
            }

            // 備考
            if ( true ) { // 備考は常に変更可   canChangeKenpin ) {
                this.$('.my_kenpin_biko').removeAttr('readonly');
            } else {
                this.$('.my_kenpin_biko').attr('readonly', 'readonly');
            }

            // // 検品NGコメント
            // if ( kenpin_status_org == cKenpinStatus_mi && kenpin_status == cKenpinStatus_ng ) {
            //     this.$('.juchu_ng_note').removeAttr('readonly'); // 検品NGコメント入力可
            // } else {
            //     this.$('.juchu_ng_note').attr('readonly', 'readonly'); // 検品NGコメント入力不可
            // }

            // 商品リンク
            var hplink = this.$('.shohin_img_link');
            var shohin_gazo_img = this.model.get('shohin_gazo_img');
            if ( shohin_gazo_img ) {
                hplink.addClass("shohin-img-clickable");
            } else {
                hplink.removeClass("shohin-img-clickable");
            }

            return this;
        },

        decoErrInfo: function(pos, msg) {
            // console.log( 'decoErrInfo=>'+msg + ' pos=>' + pos, this);
            this.$(pos).msiErrDeco(msg);
        },

        clearErrInfo: function(pos) {
            this.$(pos).msiErrClear();
        },

        canChangeSt: function() {
            var canChangeKenpin   = this.model.canChangeKenpin(); // get('can_change_kenpin');
            // var kenpin_status_org = this.model.get('kenpin_status_org');
            if ( canChangeKenpin ) { // kenpin_status_org == cKenpinStatus_mi ) { // 未回答(3)
                return true;
            }
            return false;
        },

        hClickKenpinStatus: function(e) {
            this.model.trigger('clearErrInfo', '.kenpin_status_ok,.kenpin_status_ng,.juchu_ng_note');

            var $myEle = $(e.currentTarget);
            if ( !this.canChangeSt() ) {
                return;
            }
            // console.log( '** click on ' + $myEle.attr('class') );
            var cur_kenpin_status = this.model.get('kenpin_status');
            var clicked_kenpin_status = $myEle.hasClass('kenpin_status_ok') ? cKenpinStatus_ok :
                                     $myEle.hasClass('kenpin_status_ng') ? cKenpinStatus_ng : cKenpinStatus_mi; // 検品OK(1)/未回答(0)/検品NG(-1)
            var new_kenpin_status;
            if ( cur_kenpin_status != cKenpinStatus_mi && cur_kenpin_status == clicked_kenpin_status ) { // クリア
                new_kenpin_status = cKenpinStatus_mi;
                app.unselectRow( this.model.get('myid') ); // 行非選択
            } else {
                new_kenpin_status = clicked_kenpin_status;
                app.selectRow( this.model.get('myid') ); // 行選択
            }

            this.model.set('kenpin_status', new_kenpin_status);
        },

        // hClickRow: function(e) {
        //     var item_id = this.model.get('myid');
        //     if ( canCheckOnlyMi ) { // 検品ステータス:未回答 だけチェック可
        //         var m = app.collection.findWithId( item_id );
        //         if ( !m ) return;
        //         var kenpin_status_org = m.get('kenpin_status_org');
        //         if ( kenpin_status_org != cKenpinStatus_mi ) { return; }
        //     }
        //     setTimeout( function() { app.selectFunc(item_id); }, 0 );
        // },
    } ); // _ListViewBase
    // console.log( '_ListViewBase=>',  _ListViewBase );
    var ListView = Backbone.View.extend( _ListViewBase );

    var srcCondModelOpt = {
        defaults: function() { return {}; },
        validation: {},
        labels: {},
        // 設定されていない初期データを設定する
        initAdj: function(data) {
            var that = this;
            _.each( data, function(v, k) {
                if ( that.has(k) ) return; // 既存はそのまま残す
                that.set(k, v);
            } );
            orgDataApp = this.toJSON();
        },
        // 検索キー(s_*)名を返す
        condKeys: function() {
            var keys = _.reduce( this.toJSON(), function(m, v, k) { if ( /^s_/.test(k) ) m.push(k); return m; }, [] );
            return keys;
        },
        // 検索キー(s_*)だけについてオブジェクトで返す
        condKeyValues: function() {
            var data = _.reduce( this.toJSON(), function(m, v, k) { if ( /^s_/.test(k) ) m[k] = v; return m; }, {} );
            return data;
        },
        // 検索キー(s_*)だけについてオブジェクトで返す. offset 付き
        condKeyValuesWithOffset: function(offset) {
            var data = this.condKeyValues();
            data['s_offset'] = offset;
            return data;
        },
        // 選択されている行の id を返す
        getSelectedIds: function() {
            if ( !this.has('item_ids_selected') ) {
                return [];
            }
            var a_item_ids_selected = _.keys(this.get('item_ids_selected'));
            return a_item_ids_selected;
        },
        // item_ids_selected を設定する
        syncSelectedIds: function() {
            var a_item_ids_selected = [];
            app.collection.each( function(m) {
                if ( m.isKenpinChanged() ) {
                    var myid = m.get('myid');
                    a_item_ids_selected[ myid ] = myid;
                }
            } );
            this.set('item_ids_selected', a_item_ids_selected);
        },
    }; // srcCondModelOpt

    // 追加設定を反映する。別ファイルの設定は msiLib2.injObjPrepare() でなされる
    msiLib2.injObjExtract(srcCondModelOpt, 'selpart01', 'SrcCondModel');
    // console.log( 'SrcCondModel srcCondModelOpt=>', srcCondModelOpt );

    var SrcCondModel = Backbone.Model.extend( srcCondModelOpt );

    var vOpt = {

        el: $elCompRoot,

        myUrlModCont: function() {
            var url = $.msiJqlib.baseUrl() + '/' + $.msiJqlib.urlPathModCont();
            return url;
        },

        myUrlSearch: function() {
            var url = this.myUrlModCont() + '/search';
            return url;
        },

        myUrlCondinit: function() {
            var url = this.myUrlModCont() + '/condinit';
            return url;
        },

        myShowDtlFunc: function() {
            var f = this._showNew; // サブ画面として
            // var f = this._showNewAbs; // 独立画面として
            return f;
        },

        events: {
            "click .btn_search": "doSearch",
            "click .btn_search_prev": "doSearchPrev",
            "click .btn_search_next": "doSearchNext",
            "click .btn_search_redo": "doSearchRedo",
            "click .btn_clear": "doClear",
            "click .chkAllToggle": "toggleChkAll",
            "click .chkAllOk": "toggleChkOk",
            "click .chkAllNg": "toggleChkNg",
        },

        initialize: function() {

            this.listenTo(this.model, 'change', this.render);
            // this.listenTo(this.model, 'change:item_ids_selected', this.renderBtn);
            this.listenTo(Backbone, 'msi.ajaxStop', this.renderBtn);

            this.on('my_refresh', this.render);
            this.listenTo(this.model, 'change:item_ids_selected', this.render);

            this.listenTo(this.model, 'my_rebind', this.rebindings);

            this.listenTo(this.collection, 'reset', this.resetCol);

            this.initialize_sub();

            this.stickit();
            
            this.render();
            // this.renderBtn();
        },

        initialize_sub: function() {}, // for adding some in subclass

        rebindings: function() {
            // console.log( '@@@@ rebindings called.' );

            this.stickit();
            Backbone.Validation.bind( this, Backbone.Validation.msi_v_iv_callback2({}, undefined, '.') );

            this.model.trigger('change');
            // _.defer( function() { } );
        },

        setMstrUI: function() {
            var that = this;
            _.defer( function() {
                $elRoot.find('.msi-local-picker').each( function() {
                    msiLib2.msiLocalPickerBinder.apply(this, [gMstrData]);
                } );

                var obj = msiLib2.getObjViaMsiDelayedBinder( $elCompRoot );
                var modelBase  = obj.modelBase,
                    viewBase   = obj.viewBase,
                    initValues = obj.initValues
                    ;
                // console.log( 'initValues=>' + JSON.stringify(initValues) );

                if ( _.has(modelBase, 'validation') ) {
                    that.model.validation = _.defaults(that.model.validation, modelBase.validation);
                }
                if ( _.has(modelBase, 'labels') ) {
                    that.model.labels = _.defaults(that.model.labels, modelBase.labels);
                }
                if ( _.has(viewBase, 'bindings') ) {
                    that.bindings = _.defaults(that.bindings, viewBase.bindings);
                }
                if ( _.has(viewBase, 'events') ) {
                    that.events = _.defaults(that.events, viewBase.events);
                    that.delegateEvents();
                }
                that.model.initAdj( initValues );

                that.model.trigger('my_rebind');
            } );
        },

        render: function() {
            // console.log( 'AppView render called.' );
            
            // スクロール調整
            var that = this;
            _.defer(function() { that.scrollAdj(); } );
            
            this.renderBtn();

            this.render_sub();

            return this;
        },

        render_sub: function() {},

        resetCol: function(collection, options) {
            // msiGlobalObj.markObj.mark('resetCol Begin');
            var that = this;
            var $list = my$('.component-root .result-list .list');
            $list.empty();
            var html = '';
            if ( collection.length ) {
                collection.resetLineNo();
                var colgroup = my$('.item-template-colgroup').html();
                var $table = $( '<table>' + colgroup + '</table>' ).appendTo($list);
                _.each( collection.models, function(m) {
                    var v = new ListView({model: m});
                    $table.append(v.render().el);
                    // html += v.html();
                    m.markAsOrg();
                } );
            } else {
            }
            if ( collection.length <= 0 &&
                 _.has(options, 'msg') && _.isString(options['msg']) && options['msg'].length > 0 ) {
                var msg = options['msg'],
                    msgTmpl = my$('.list-msg-template').html(),
                    html = _.template( msgTmpl, {msg: msg});
                $list.append( html );
            }
            setTimeout(function() { that.render(); }, 0 );
            // msiGlobalObj.markObj.markOutput();
        },

        renderBtn: function() {
            // var a_item_ids_selected = this.model.getSelectedIds();
            // if ( a_item_ids_selected.length < 1 ) {
            var _isAnyKenpinChanged = this.collection.isAnyKenpinChanged();
            if ( !_isAnyKenpinChanged ) {
                this.btnDisabled('.sekolist-cmd-buttons input[type="button"]');
                this.btnDisabled('.btn_ctrl_selected');
            } else {
                this.btnEnabled('.sekolist-cmd-buttons input[type="button"]');
                this.btnEnabled('.btn_ctrl_selected');
            }

            this.renderCheck();
        },

        // チェック（選択）表示
        renderCheck: function() {
            var trs = this.$('.result-list .list table').find( 'tr' ),
                a_item_ids_selected = this.model.getSelectedIds(), // _.keys(this.model.get('item_ids_selected')),
                bbv = this;
            trs.removeClass('row-selected');
            _.each( a_item_ids_selected,
                    function(item_id) {
                        var $tr = bbv.$('.result-list .list table').find( 'tr[data-item_id="' + item_id + '"]' );
                            $tr.addClass('row-selected');
                    } );
        },

        // スクロールバー表示調整
        scrollAdj: function() {
            var vh = $elPar.height(),
                // var vh = Math.max(document.documentElement.clientHeight, window.innerHeight || 0),
                $list = this.$('.result-list .list'),
                $header = this.$('.result-list .header'),
                sc_of,
                sc_w,
                hh,
                // cont_h = $elCompRoot.height(),
                header_h = $header.outerHeight(true),
                part_1 = this.$('.header-part').outerHeight(true),
                part_2 = this.$('.search-cond-part').outerHeight(true),
                part_3_1 = this.$('.footer-part-1').outerHeight(true),
                part_3_2 = this.$('.footer-part-2').outerHeight(true),
                adj_h = p_adj_h,
                result_part_h = vh - (part_1 + part_2 + part_3_1 + part_3_2 + adj_h),
                list_h = result_part_h - header_h;
            // console.log( 'height=>' + [vh,result_part_h,list_h,header_h,part_1,part_2,part_3_1,part_3_2,adj_h].join(', ') );

            my$('.component-root .result-part').height( result_part_h );

            $list.height( list_h );

            // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
            if ( $list[0].scrollHeight === $list[0].clientHeight ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
            } else {
                sc_of = 'scroll';
                hh = $header.height();
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome.
            }
        },

        // エラー表示をクリア
        clearErr: function() {
            this.$el.msiErrClearAll();
            msiLib2.clearAlert();
        },

        // ボタン非活性化
        btnDisabled: function(elem) {
            // my$(elem).hide();
            my$(elem).attr("disabled", "disabled");
        },

        // ボタン活性化
        btnEnabled: function(elem) {
            // my$(elem).show();
            my$(elem).removeAttr("disabled");
        },

        // 入力チェック
        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line;

            if ( !this.model.validate ) {
                return true;
            }

            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                    // console.log( '*** err ' + k + ': ' + v );
                } );
            }

            // NG
            if ( aMsg.length > 0 ) {
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },

        // 変更可否
        isChanged: function() {
            if ( this.collection && this.collection.isChangedFromOrg('_') ) {
                return true;
            }
            return false;
            // if ( !orgDataApp || $.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) ) {
            //     return false;
            // }
            // return true;
        },

        // 検索結果を表示する
        dispSearchResult: function( offset, mydata ) {
            // console.log( 'dispSearchResult mydata=>', mydata );
            var attr = {};
            if ( mydata.msg ) {
                attr = { msg: mydata.msg };
            }

            this.collection.myReset( offset, mydata.list_data, attr ); // {msg: mydata.msg} );
            // if ( mydata.msg ) {
            //     var lastMsg = 'info:' + mydata.msg;
            //     msiLib2.showInfo( mydata.msg );
                        // }
            var next_offset = mydata.next_offset;
            var prev_offset = mydata.prev_offset;
            if ( next_offset > 0 ) {
                my$('.sp-act-button-search-next').attr('data-offset', next_offset).css('visibility','visible');
            }
            if ( prev_offset >= 0 ) {
                my$('.sp-act-button-search-prev').attr('data-offset', prev_offset).css('visibility','visible');
            }
            if ( true ) { // mydata.list_count > 0
                my$('.sp-act-btn-search-redo').css('visibility','visible');
            }
            if ( _.has(mydata, 'search_cnt_disp') ) {
                my$('.search-cnt-disp-txt').text(mydata.search_cnt_disp);
            }
        },

        // 画面遷移前のチェック
        _isOkPageRefresh: function() {
            if ( !isNoChangeCheck && app.isChanged() ) {
                var iMsg = "保存されていないデータがあります.";
                if ( ! confirm( iMsg + "\nよろしいですか？") ) {
                    return false;
                }
            }
            return true;
        },

        searchFunc_lastOffset: 0, // 直前 searchFunc 実行時の offset 値. 同じ処理の繰り返し実行用

        // 検索処理
        searchFunc: function(e, offset) {
            if ( ! this.isInputOk() ) {
                return;
            }

            if ( ! this._isOkPageRefresh() ) {
                return;
            }

            if ( offset === undefined ) offset = 0;

            this.searchFunc_lastOffset = offset; // 直前の offset 値を

            // 検索条件設定
            var sndData = this.model.condKeyValuesWithOffset(offset);
            // console.log( 'sndData->' + JSON.stringify(sndData) );

            // 次ボタン等クリア
            my$('.sp-act-button-search-next').removeAttr('data-offset').css('visibility','hidden'); // hide();
            my$('.sp-act-button-search-prev').removeAttr('data-offset').css('visibility','hidden'); // hide();
            my$('.sp-act-btn-search-redo').css('visibility','hidden'); // hide();

            // 件数クリア
            my$('.search-cnt-disp-txt').text('');

            // 選択済み施行Noをクリア
            this.selectFunc(null);
            this.model.set('item_ids_selected', {});

            // chkAll クリア
            this.model.set('_chkAll', false);
            this.$('.chkAllToggle').removeClass('selected');

            // chkAllOk/_chkAllNg クリア
            this.model.set('_chkAllOk', false);
            this.$('.chkAllOk').removeClass('selected');
            this.model.set('_chkAllNg', false);
            this.$('.chkAllNg').removeClass('selected');

            var dataJson = JSON.stringify(sndData);

            var bbm = this.model,
                that = this;
            $.ajax( {
                url: this.myUrlSearch(), // this.myUrlModCont() + '/search',
                type: 'POST',
                data: dataJson,
                contentType: 'application/json',
                dataType: 'json',
                success: function( mydata ) {
                    // console.log( 'searchFunc=>', mydata );
                    if ( mydata.status == 'OK' ) {
                        that.dispSearchResult( offset, mydata );
                    } else {
                        msiLib2.showErr2( mydata.msg );
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                },
            } );
        },

        // 検索実行
        doSearch: function(e) {
            // console.log( 'doSearch called' );
            this.searchFunc(e);
        },

        // 検索再実行
        doSearchRedo: function(e) {
            var offset = this.searchFunc_lastOffset;
            this.searchFunc(e, offset);
        },

        // 検索実行(前)
        doSearchPrev: function(e) {
            var $ele = my$('.btn_search_prev'),
                offset = $ele.attr('data-offset');
            this.searchFunc(e, offset);
        },

        // 検索実行(次)
        doSearchNext: function(e) {
            var $ele = my$('.btn_search_next'),
                offset = $ele.attr('data-offset');
            this.searchFunc(e, offset);
        },

        // クリア
        doClear: function() {
            var that = this;

            this.clearErr();

            if ( ! this._isOkPageRefresh() ) {
                return;
            }

            var dataJson = JSON.stringify({});

            $.ajax( {
                url: this.myUrlCondinit(), // this.myUrlModCont() + '/condinit',
                type: 'POST',
                data: dataJson,
                contentType: 'application/json',
                dataType: 'json',
                success: function( mydata ) {
                    // console.log( 'doClear condinit RES=>', mydata );
                    if ( mydata.status == 'OK' ) {
                        setTimeout( function() {
                            _resetData( mydata.data.dataApp );
                            that.doReset();
                        }, 0 );
                    } else {
                        msiLib2.showErr2( mydata.msg );
                    }
                },
            } );
        },

        // クリア
        doReset: function() {
            this.clearErr();

            _resetData( orgDataApp );

            this.model.set('item_ids_selected', {});

            // chkAll クリア
            this.model.set('_chkAll', false);
            this.$('.chkAllToggle').removeClass('selected');

            // chkAllOk/_chkAllNg クリア
            this.model.set('_chkAllOk', false);
            this.$('.chkAllOk').removeClass('selected');
            this.model.set('_chkAllNg', false);
            this.$('.chkAllNg').removeClass('selected');

            // 明細行をクリア
            my$('.component-root .result-list .list').empty();

            // 次ボタン等クリア
            my$('.sp-act-button-search-next').removeAttr('data-offset').css('visibility','hidden'); // hide();
            my$('.sp-act-button-search-prev').removeAttr('data-offset').css('visibility','hidden'); // hide();
            my$('.sp-act-btn-search-redo').css('visibility','hidden'); // hide();

            // 行開始位置
            this.collection.setOffset( 0 );

            // 件数クリア
            my$('.search-cnt-disp-txt').text('');

            this.model.trigger('change');
        },

        // // 選択されていれば真を返す
        // isSelectedRow: function(item_id) {
        //     var $tr = this.$('.result-list .list table').find( 'tr[data-item_id="' + item_id + '"]' );
        //     if ( !$tr || $tr.length < 1 ) return false;
        //     return $tr.hasClass('row-selected');
        // },

        // １行非選択
        unselectRow: function(item_id) {
            var $tr = this.$('.result-list .list table').find( 'tr[data-item_id="' + item_id + '"]' ),
                item_ids_selected = this.model.get('item_ids_selected');

            if ( !$tr || $tr.length < 1 ) return;

            if ( !$tr.hasClass('row-selected') ) return; // 選択されていない

            delete item_ids_selected[item_id];
            $tr.removeClass('row-selected');

            this.model.set('item_ids_selected', item_ids_selected);
            this.model.trigger('change:item_ids_selected');
        },

        // １行選択
        selectRow: function(item_id) {
            var $tr = this.$('.result-list .list table').find( 'tr[data-item_id="' + item_id + '"]' ),
                item_ids_selected = this.model.get('item_ids_selected');

            if ( !$tr || $tr.length < 1 ) return;

            if ( $tr.hasClass('row-selected') ) return; // 選択済

            item_ids_selected[item_id] = item_id;
            $tr.addClass('row-selected');

            this.model.set('item_ids_selected', item_ids_selected);
            this.model.trigger('change:item_ids_selected');
        },

        // 行選択ハンドラ
        selectFunc: function(item_id) {
            var $tr = this.$('.result-list .list table').find( 'tr[data-item_id="' + item_id + '"]' ),
                item_ids_selected = this.model.get('item_ids_selected');

            if ( !$tr || $tr.length < 1 ) return;

            if ( _.has(item_ids_selected, item_id) ) { // toggle
                delete item_ids_selected[item_id];
                $tr.removeClass('row-selected');
            } else {
                item_ids_selected[item_id] = item_id;
                $tr.addClass('row-selected');
            }
            this.model.set('item_ids_selected', item_ids_selected);

            // console.log( '**** selectFunc item_ids_selected=>' + JSON.stringify(item_ids_selected) );

            this.model.trigger('change:item_ids_selected'); // :item_ids_selected');
        },

        // 他データ参照(サブ画面として)
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { // console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },

        // 他データ参照((サブ画面でない)独立した画面として)
        _showNewAbs: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            msiLib2.openNewWinAbs( url );
        },

        // 行全選択
        checkAll: function() {
            this._checkAll(true);
        },

        // 行全選択解除
        uncheckAll: function() {
            this._checkAll(false);
        },

        // 行全設定
        _checkAll: function(isSet) {
            var bbv = this;
            var item_ids_selected = {};
            if ( isSet ) {
                my$('tr[data-item_id]').each( function() {
                    var item_id = my$(this).attr('data-item_id');
                    if ( canCheckOnlyMi ) { // 検品ステータス:未回答 だけチェック可
                        var m = bbv.collection.findWithId( item_id );
                        if ( !m ) return;
                        var kenpin_status_org = m.get('kenpin_status_org');
                        if ( kenpin_status_org != cKenpinStatus_mi ) { return; }
                    }
                    item_ids_selected[item_id] = item_id;
                } );
            }
            this.model.set('item_ids_selected', item_ids_selected);
            this.model.trigger('change:item_ids_selected');

            if ( isSet ) {
                this.$('.chkAllToggle').addClass('selected');
            } else {
                this.$('.chkAllToggle').removeClass('selected');
            }
            this.model.set('_chkAll', isSet);
        },

        // check トグル
        toggleChkAll: function() {
            var _chkAll = ! this.model.get('_chkAll');
            // this.model.set('_chkAll', _chkAll);
            if ( _chkAll ) {
                // this.$('.chkAllToggle').addClass('selected');
                this.checkAll();
            } else {
                // this.$('.chkAllToggle').removeClass('selected');
                this.uncheckAll();
            }
        },

        // 行全設定
        _checkKenpinAll: function(p_kenpin_status) {
            this.collection.allKenpinCheck( p_kenpin_status );
            this.model.syncSelectedIds();
        },

        // 行全設定 cur_kenpin_status でない値に設定されている場合は設定しない
        _checkKenpinAllGuard: function(p_kenpin_status, cur_kenpin_status) {
            this.collection.allKenpinCheckGuard( p_kenpin_status, cur_kenpin_status );
            this.model.syncSelectedIds();
        },

        // OK check トグル
        toggleChkOk: function() {
            var _chkAllOk = ! this.model.get('_chkAllOk');
            if ( _chkAllOk ) {
                // _chkAllNg はクリア
                this.$('.chkAllNg').removeClass('selected');
                this.model.set('_chkAllNg', false);

                this.$('.chkAllOk').addClass('selected');
                this._checkKenpinAll( cKenpinStatus_ok );
            } else {
                this.$('.chkAllOk').removeClass('selected');
                this._checkKenpinAllGuard( cKenpinStatus_mi, cKenpinStatus_ok );
            }

            this.model.set('_chkAllOk', _chkAllOk);
        },

        // NG check トグル
        toggleChkNg: function() {
            var _chkAllNg = ! this.model.get('_chkAllNg');
            if ( _chkAllNg ) {
                // _chkAllOk はクリア
                this.$('.chkAllOk').removeClass('selected');
                this.model.set('_chkAllOk', false);

                this.$('.chkAllNg').addClass('selected');
                this._checkKenpinAll( cKenpinStatus_ng );
            } else {
                this.$('.chkAllNg').removeClass('selected');
                this._checkKenpinAllGuard( cKenpinStatus_mi, cKenpinStatus_ng );
            }
            this.model.set('_chkAllNg', _chkAllNg);
        },

        bindings: {},
    };

    // 追加設定があれば設定. (ex. seikyu.sekolist.ex.js)
//    if ( msiGlobalObj.myAppViewOptExFunc ) {
//        msiGlobalObj.myAppViewOptExFunc( vOpt );
//    }

    // 追加設定を反映する。別ファイルの設定は msiLib2.injObjPrepare() でなされる
    msiLib2.injObjExtract(vOpt, 'selpart01', 'AppViewBase');
    // console.log( 'AppViewBase vOpt=>', vOpt );

    var AppView = Backbone.View.extend( vOpt );

    var app,
        orgDataApp = {},
        _resetData,
        _setInitData;

    app = new AppView( { model: new SrcCondModel, collection: listCol } );

    _resetData = function( myApp ) {
        // console.log( '_resetData myApp=>', myApp );
        app.model.set( myApp );
        orgDataApp = app.model.toJSON();
        my$(document).msiErrClearAll();
        app.collection.myReset( 0, [], {} );
        app.model.trigger('change');
        app.trigger('resetData', myApp);
    };
    app._resetData = _resetData;

    _setInitData = function(mydata) {
        if ( mydata === undefined ) {
            mydata = msiLib2.getJsonFromHtml( my$('.my-data-init-cls') );
        }
        if ( mydata && mydata.mstrData ) {
            gMstrData = mydata.mstrData;
            app._mstrData = mydata.mstrData;
            app.setMstrUI();
        }
        if ( mydata && mydata.exData ) {
            app._exData = mydata.exData;
        }
        if ( mydata && mydata.dataApp ) {
            setTimeout( function() { _resetData( mydata.dataApp ); }, 0 );
            if ( mydata.dataApp.init_search && msiLib2.isValTrue(mydata.dataApp.init_search) ) {
                setTimeout( function() { app.doSearch(); }, 0 );
            } else if ( mydata.dataApp.inline_result ) {
                setTimeout( function() { app.dispSearchResult( 0, mydata.dataApp.inline_result ); }, 0 );
            }
        }
        app.trigger('setInitData', mydata);
    };
    app._setInitData = _setInitData;

    // msiパーツの有効化 
    msiLib2.msiPrepareParts( $elCompRoot );

    // ページ遷移前の確認
    $(window).on('beforeunload', function() {
        if ( !isNoChangeCheck && app.isChanged() ) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on( 'resize', function() { app.render(); } );

    // 初期フォーカス設定
    // setTimeout( function() {$('#s_number').focus();}, 0 );

    $.msiJqlib.initDone( function() { _setInitData();
                                      _.defer( function() {
                                          $('.component-root').fadeIn('fast'); // ちらつきのごまかし
                                          app.model.trigger('change'); // render
                                      } );
                                    } ); // 処理完了

    msiGlobalObj.appView[ root_id ] = app;

} };

