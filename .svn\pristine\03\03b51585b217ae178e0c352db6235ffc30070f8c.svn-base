 #detail #goods.itemex .list ul li {
	width: 216px;
    height: 240px;
	font-size: 10px;
}
 #detail #goods.itemex .list ul li .price .n {
  font-size: 14px;
}
 #detail #goods .list ul li .price .n2
{
  font-size: 14px;
}
 #detail #goods.itemex .list ul li .pic {
  height: 126px;
}
 #detail #goods .list ul li .pic img {
    width: 216px;
    height: 126px;
}
 #detail .label,
 #detail label,
 #detail .subtitle {
  font-size: 10px;
}
 #detail #goods.itemex .list ul li .counter {
	height: 60px;
	margin: -5px auto 0 auto;
}
 #detail .view_style_opt li a,
 #detail .view_style_opt li span {
  width: 30px;
  height: 30px;
}
  #detail #hanbai_tnk {
	font-size: 12px;
}
 #detail #goods.itemex .list ul li .shohin_nm {
	font-size: 10px;
}
#detail #goods.itemex .list ul li .name,
#detail #goods.itemex .list ul li .price {
	width: 90%;
        margin: 5px auto;
}
#detail #lvl1_tab_container {
    height: 25px;
}
#detail #goods.itemex.h2 .list ul li, 
#detail #goods.itemex.h3 .list ul li {
height: 240px;
}


/* for swiper --BEGIN-- */
#itemex-picker-dlg {
	height: 93%;
}
#itemex-picker-dlg .content {
    height:87%;
}
#itemex-picker-dlg .content.my-picker-swiper-cube {
    background-color: #FCFCFC;
}
#itemex-picker-dlg .content.my-picker-swiper-cube .swiper-slide img {
    width: 90%;
    height: 100%;
}
/* for swiper --END-- */

