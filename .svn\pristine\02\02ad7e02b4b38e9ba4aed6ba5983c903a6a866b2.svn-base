<?php
  /**
   * DataMapper_Pdf02004
   *
   * オリジナル礼状専用申込書 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/06/23
   * @filesource 
   */

  /**
   * オリジナル礼状専用申込書 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/06/23
   */
class DataMapper_Pdf02004 extends DataMapper_Abstract
{
    /**
     * オリジナル礼状専用申込書データ 取得
     *
     * <AUTHOR> Sato
     * @since      2014/06/23
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
 SELECT
	 s.seko_no
        ,s.syuha_nm
        ,s.sougi_ymd
        ,s.m_nm
        ,s.m_knm
        ,orei.ori_cnt
        ,orei.teikei_cnt
        ,orei.hagaki_size
        ,bumon.bumon_snm
        ,orei.sz_ymd
        ,orei.sz_st_kbn1
        ,orei.sz_st_kbn2
        ,orei.hd_biko1
        ,orei.hd_biko2
        ,sinzoku.m_kbn
        ,sinzoku.zoku_nm1
        ,sinzoku.kankei_nm1
        ,sinzoku.zoku_nm2
        ,sinzoku.kankei_nm2
        ,sinzoku.zoku_nm3
        ,sinzoku.kankei_nm3
        ,sinzoku.zoku_nm4
        ,sinzoku.kankei_nm4
        ,sinzoku.zoku_nm5
        ,sinzoku.kankei_nm5
        ,sinzoku.k_etc_nm
        ,sinzoku.k_knm_nm
        ,m_zoku2.kbn_value_lnm  AS m_zoku2_nm
        ,syushi.kbn_value_lnm   AS syushi_nm
        ,orei.kamon_code,orei.kamon_nm
        ,CASE orei.sz_kbn WHEN 1 THEN s.m_nm      ELSE orei.sz_nm  END sz_nm
        ,CASE orei.sz_kbn WHEN 1 THEN s.m_knm     ELSE orei.sz_knm END sz_knm
        ,CASE orei.sz_kbn WHEN 1 THEN s.m_zoku_cd ELSE orei.sz_zoku_cd END sz_zoku_kbn
        ,CASE orei.sz_kbn WHEN 1 THEN CASE s.mg_kbn WHEN 1 THEN s.kg_tel ELSE s.mg_tel END ELSE orei.sz_tel END sz_tel
        ,CASE orei.sz_kbn WHEN 1 THEN s.mg_m_tel  ELSE orei.sz_fax END sz_fax
        ,oreidtl.title_nm        
        ,orei.huto_cnt
        ,orei.pattern_nm
        ,orei.v_free1
        ,orei.v_free2
        ,orei.v_free3
        ,orei.free_kbn2
        ,orei.free_kbn3
        ,orei.free_kbn6
        ,orei.free_kbn7
        ,m_kbn.kbn_value_lnm AS m_kbn_nm
        ,CASE orei.sz_kbn WHEN 1 THEN sz_zoku2.kbn_value_lnm ELSE sz_zoku.kbn_value_lnm END sz_zoku_kbn_nm
        ,orei.v_free5          AS sz_zoku_kbn_other
        ,sz_st1.kbn_value_lnm  AS sz_st1_kbn_nm
        ,sz_st2.kbn_value_lnm  AS sz_st2_kbn_nm
        ,sn.basho_kbn
        ,COALESCE (kj.tel, njm.tel ) AS sougi_basho_tel
  FROM seko_kihon_info s
  LEFT JOIN seko_orei_info orei ON s.seko_no = orei.seko_no
  LEFT JOIN seko_oshirase_orei_dtl oreidtl ON s.seko_no = oreidtl.seko_no and orei.orei_cd = oreidtl.oshirase_cd
  LEFT JOIN seko_sinzoku_info sinzoku ON s.seko_no = sinzoku.seko_no AND sinzoku.teikei_kbn = 1 AND sinzoku.add_kbn = 0 AND sinzoku.delete_flg = 0
  LEFT JOIN code_nm_mst m_zoku2 ON m_zoku2.code_kbn = '0190' AND s.m_zoku_cd2 = m_zoku2.kbn_value_cd AND m_zoku2.delete_flg = 0
  LEFT JOIN code_nm_mst syushi ON syushi.code_kbn = '0240' AND s.syushi_cd = syushi.kbn_value_cd AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst m_kbn ON m_kbn.code_kbn = '8070' AND m_kbn.kbn_value_cd_num = sinzoku.m_kbn AND m_kbn.delete_flg = 0
  LEFT JOIN code_nm_mst sz_zoku  ON sz_zoku.code_kbn  = '0190' AND sz_zoku.kbn_value_cd  = orei.sz_zoku_cd AND sz_zoku.delete_flg = 0
  LEFT JOIN code_nm_mst sz_zoku2 ON sz_zoku2.code_kbn = '0190' AND sz_zoku2.kbn_value_cd = s.m_zoku_cd     AND sz_zoku2.delete_flg = 0                
  LEFT JOIN code_nm_mst sz_st1 ON sz_st1.code_kbn = '0890' AND sz_st1.kbn_value_cd_num = orei.sz_st_kbn1 AND sz_st1.delete_flg = 0
  LEFT JOIN code_nm_mst sz_st2 ON sz_st2.code_kbn = '0900' AND sz_st2.kbn_value_cd_num = orei.sz_st_kbn2 AND sz_st2.delete_flg = 0
  LEFT JOIN bumon_mst AS bumon ON orei.chk_bumon_cd = bumon.bumon_cd AND bumon.delete_flg = 0
  LEFT JOIN seko_nitei sn ON s.seko_no = sn.seko_no AND sn.nitei_kbn = 11 AND sn.delete_flg = 0
  LEFT JOIN nm_jyusho_mst njm ON njm.jyusho_kbn = sn.basho_kbn AND njm.jyusho_cd = sn.basho_cd AND njm.delete_flg = 0
  LEFT JOIN kaijyo_mst kj ON kj.kaijyo_cd = sn.basho_cd AND kj.delete_flg = 0
 WHERE sinzoku.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }

    /**
     * オリジナル礼状専用申込書データ 取得
     *
     * <AUTHOR> Sais
     * @since      2016/04/26
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
	s.seko_no, s.syuha_nm, s.sougi_ymd, s.m_nm, s.m_knm,
	orei.ori_cnt,orei.teikei_cnt, orei.hagaki_size,
        bumon.bumon_snm,
	orei.sz_ymd, orei.sz_st_kbn1, orei.sz_st_kbn2,  orei.sz_fax,
	orei.hd_biko1, orei.hd_biko2, 
	sinzoku.m_kbn,
	sinzoku.zoku_nm1, sinzoku.kankei_nm1,
	sinzoku.zoku_nm2, sinzoku.kankei_nm2,
	sinzoku.zoku_nm3, sinzoku.kankei_nm3,
	sinzoku.zoku_nm4, sinzoku.kankei_nm4,
	sinzoku.zoku_nm5, sinzoku.kankei_nm5,
	sinzoku.k_etc_nm, sinzoku.k_knm_nm,
	m_zoku2.kbn_value_lnm m_zoku2_nm,
	syushi.kbn_value_lnm AS syushi_nm
       ,orei.kamon_code,orei.kamon_nm
       ,CASE orei.sz_kbn WHEN 1 THEN s.m_nm ELSE orei.sz_nm END sz_nm
       ,CASE orei.sz_kbn WHEN 1 THEN s.m_zoku_cd ELSE orei.sz_zoku_cd END sz_zoku_kbn
       ,CASE orei.sz_kbn WHEN 1 THEN CASE s.mg_kbn WHEN 1 THEN s.kg_tel ELSE s.mg_tel END ELSE orei.sz_tel END sz_tel
       ,oreidtl.title_nm        
       ,orei.huto_cnt
       ,orei.pattern_nm
       ,orei.f_name2
       ,orei.v_free1
  FROM seko_kihon_info s
  LEFT JOIN seko_orei_info orei ON s.seko_no = orei.seko_no
  LEFT JOIN seko_oshirase_orei_dtl oreidtl ON s.seko_no = oreidtl.seko_no and orei.orei_cd = oreidtl.oshirase_cd
  LEFT JOIN seko_sinzoku_info sinzoku ON s.seko_no = sinzoku.seko_no AND sinzoku.teikei_kbn = 1 AND sinzoku.add_kbn = 0 AND sinzoku.delete_flg = 0
  LEFT JOIN code_nm_mst m_zoku2 ON m_zoku2.code_kbn = '0190' AND s.m_zoku_cd2 = m_zoku2.kbn_value_cd AND m_zoku2.delete_flg = 0
  LEFT JOIN code_nm_mst syushi ON syushi.code_kbn = '0240' AND s.syushi_cd = syushi.kbn_value_cd AND syushi.delete_flg = 0
  LEFT JOIN bumon_mst AS bumon  ON orei.chk_bumon_cd = bumon.bumon_cd AND bumon.delete_flg = 0
 WHERE sinzoku.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
    /**
     * 施行親族情報 取得
     *
     * <AUTHOR> Sato
     * @since      2014/06/23
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findSinzoku($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT
	s.seko_no, s.teikei_kbn, s.disp_no, s.zoku_nm, s.kankei_nm
  FROM seko_kankei_info s
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }


}
