<?php

/**
 * DataMapper_SeikyuDenpyo
 *
 * 請求伝票 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/08/xx
 * @filesource 
 */

/**
 * 請求伝票データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2016/03/xx
 */
class DataMapper_SeikyuDenpyo extends DataMapper_Abstract {

    /**
     * 請求伝票 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_den_no';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
        SELECT d.* 
                ,c.kbn_value_lnm	AS 	seikyu_kbn_nm	-- 請求区分名,c.kbn_value_lnm   AS  seikyu_kbn_nm   -- 請求区分名
                ,TO_CHAR(COALESCE(d.seikyu_ymd,d.juchu_ymd),'YYYY/MM/DD')              AS seikyu_ymdx      -- 請求日
                ,TO_CHAR(d.kaishu_ymd,'YYYY/MM/DD')    AS kaishu_ymdx      -- 回収予定日
                ,d.uri_prc_sum + d.uri_nebk_sum + d.uri_hepn_sum +  d.hoshi_prc_sum
                    + d.sougi_early_use_cost
                    + d.etc_early_use_cost          AS seikyu_prc   -- 請求金額
                ,d.out_zei_prc + d.in_zei_prc       AS zei_prc      -- 消費税額
                ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.etc_harai_prc
                AS  seikyu_zei_prc
                ,COALESCE(d.h_nafuda_nm, '') || ' ' || COALESCE(d.h_nafuda_nm2, '') || ' ' || COALESCE(d.h_nafuda_nm3, '')
                    || ' ' || COALESCE(d.h_nafuda_nm4, '') || ' ' || COALESCE(d.h_nafuda_nm5, '')AS nafuda_nm
                , b.bumon_snm
                , b.bumon_lnm
                , bkm.oya_bumon_cd
                , '見積確定：' || ctt.tanto_nm || '　(' || TO_CHAR(d._cre_ts, 'YYYY/MM/DD HH24:MI:SS') || ')' AS kakutei_user
                , CASE WHEN d.shonin_dt1 IS NOT NULL THEN 
                    '請求承認：' || shonin_tm.tanto_nm || '　(' || TO_CHAR(d.shonin_dt1, 'YYYY/MM/DD HH24:MI:SS') || ')'
                ELSE NULL END AS shonin_user
                ,TO_CHAR(d._cre_ts, 'YYYY/MM/DD') AS cre_ts_ymd
        FROM seikyu_denpyo d
        LEFT JOIN    code_nm_mst    c
            ON 0                = c.delete_flg
            AND '0920'          = c.code_kbn
            AND d.data_kbn      = c.kbn_value_cd_num
        LEFT JOIN tanto_mst tt
            ON tt.delete_flg = 0
            AND d.tanto_cd = tt.tanto_cd   
            $dateWhere2
        LEFT JOIN bumon_mst b
        ON b.delete_flg = 0
	AND d.bumon_cd = b.bumon_cd   
        $dateWhere3		
        LEFT JOIN bumon_kaso_mst bkm
            ON bkm.ko_bumon_cd = d.bumon_cd
            AND bkm.delete_flg = 0
        LEFT JOIN login_mst cltm          
            ON CASE WHEN LENGTH(d._cre_user)-10 > 0 THEN
                    SUBSTR(d._cre_user,10, length(d._cre_user)-9 )
                ELSE
                    NULL
                END = cltm.login_cd
            AND 0 = cltm.delete_flg          
        LEFT JOIN tanto_mst ctt     
            ON cltm.tanto_cd  = ctt.tanto_cd
            AND 0 = ctt.delete_flg        
        LEFT JOIN tanto_mst shonin_tm     
            ON shonin_tm.tanto_cd  = d.input_tanto_cd
            AND 0 = shonin_tm.delete_flg
        WHERE d.delete_flg = 0   
        $dateWhere1
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 請求伝票明細 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/**
     * @param      Msi_Sys_Db $db
     * @param      string  $seikyu_den_no
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi($db, $seikyu_den_no) {

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT m.*
            ,skm.shohin_kbn_nm AS shohin_kbn_nm
            ,sdm.dai_bunrui_nm AS dai_bunrui_nm
            ,sm.nm_input_kbn AS nm_input_kbn
            ,sm.tnk_chg_kbn AS tnk_chg_kbn
            ,sm.tani_ovr_kbn AS tani_ovr_kbn
            ,tani.tani_nm AS tani_nm
            ,COALESCE(m.nafuda_nm || ' ','') || COALESCE(m.nafuda_nm2 || ' ', '') || COALESCE(m.nafuda_nm3 || ' ', '') || COALESCE(m.nafuda_nm4 || ' ', '') ||
                COALESCE(m.nafuda_nm5 || ' ','') || COALESCE(m.nafuda_nm6 || ' ', '') || COALESCE(m.nafuda_nm7 || ' ', '') || COALESCE(m.nafuda_nm8 || ' ', '') AS nafuda
        FROM seikyu_denpyo_msi m
        LEFT JOIN shohin_mst sm
            ON m.shohin_cd = sm.shohin_cd
            AND m.shohin_bumon_cd = sm.bumon_cd
            AND m.kaisya_cd = sm.kaisya_cd
            AND sm.delete_flg=0
        LEFT JOIN shohin_kbn_mst skm 
            ON  m.shohin_kbn = skm.shohin_kbn
            AND  0           = skm.delete_flg
        LEFT JOIN shohin_dai_bunrui_mst sdm 
            ON  m.dai_bunrui_cd = sdm.dai_bunrui_cd
            AND  0           = sdm.delete_flg
        LEFT JOIN tani_mst tani 
            ON m.tani_cd = tani.tani_cd
            AND tani.delete_flg = 0
        WHERE m.delete_flg=0
            AND m.seikyu_den_no = :seikyu_den_no
        ORDER BY disp_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));

        return $select;
    }

    /**
     * 請求伝票取得用
     *
     * <AUTHOR> Kobayashi
     * @since      2020/08/xx
     * @param      $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     * @return     array $select	売上伝票 
     */
    public static function findSeikyuDenpyo($db, $keyHash=array(), $isDateEffective=true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seikyu_den_no ';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
        FROM (
            SELECT
                h.*
                ,TO_CHAR(h.keijo_ymd,'YYYY/MM/DD') AS keijo_ymd_fm -- 売上計上日
                ,TO_CHAR(h.juchu_ymd,'YYYY/MM/DD') AS juchu_ymd_fm -- 受注日
                ,h.seikyu_zan + h.nyukin_prc AS seikyu_prc -- 請求金額
            FROM
                seikyu_denpyo h	
            WHERE  h.delete_flg = 0      
        ) T
     WHERE $whereStr
     $orderBy
     $tailClause
END_OF_SQL
		, $param);
		
        return $select;
    }
    
    /**
     * 請求伝票＋請求請求先情報 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function getSeikyuInfo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_den_no';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
        SELECT sssi.*
                ,CASE WHEN sssi.free_kbn1 = 1 THEN sssi.sekyu_nm 
                    ELSE COALESCE(sssi.sekyu_nm1, '') || ' ' || COALESCE(sssi.sekyu_nm2, '') 
                    END AS sekyu_nm
                ,TO_CHAR(sssi.kaishu_ymd,'YYYY/MM/DD')              AS harai_ymd      -- 支払日
                ,moushi.kbn_value_lnm         AS moushi_kbn
                ,d.juchusaki_kbn
                ,juchusaki.kbn_value_lnm      AS juchusaki_kbn_nm
                ,d.bun_gas_kbn_num
                ,CASE d.bun_gas_kbn_num WHEN '10' THEN 1
                 ELSE 0 END                         AS  selected
                ,d.bun_gas_seikyu_den_no            AS  seikyu_den_bun_gas_seikyu_den_no
                ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.etc_harai_prc
                AS	seikyu_zei_prc    -- 請求金額（消費税込み）
                ,d.nyukin_prc
                ,d.seikyu_zan
                ,d.seikyu_approval_status
                ,CASE d.bun_gas_kbn_num WHEN '20' THEN   -- 合算先伝票は名札の表示なし
                    null
                 ELSE 
                    (SELECT 
                    ARRAY_TO_STRING(
                        ARRAY_AGG(
                            NULLIF(
                                TRIM('/' FROM 
                                    COALESCE(jm.nafuda_nm || '/','') 
                                    || COALESCE(jm.nafuda_nm2 || '/', '') 
                                    || COALESCE(jm.nafuda_nm3 || '/', '') 
                                    || COALESCE(jm.nafuda_nm4 || '/', '') 
                                    || COALESCE(jm.nafuda_nm5 || '/','') 
                                    || COALESCE(jm.nafuda_nm6 || '/', '') 
                                    || COALESCE(jm.nafuda_nm7 || '/', '') 
                                    || COALESCE(jm.nafuda_nm8 || '/', '')
                                )
                            ,'')
                        )
                    , ',')
                    FROM seikyu_denpyo_msi jm
                    WHERE d.seikyu_den_no = jm.seikyu_den_no
                    GROUP BY jm.seikyu_den_no
                ) END AS nafuda_nms
                ,bm.bumon_lnm AS bumon_nm
                ,sssi.v_free1 AS kokyaku_no
                ,sssi.v_free2 AS sekyu_soufu_nm2
                ,TO_CHAR(d.nonyu_dt, 'YYYY/MM/DD') AS nonyu_dt
                ,sssi.sekyu_nm AS tokuisaki_nm
                ,sssi.v_free3 AS bank_cd
                ,sssi.free_kbn1 AS kokyaku_kbn
                ,br.bank_nm || ' ' || br.shiten_nm || ' ' || 
                    (CASE WHEN br.yokin_sbt=0 THEN '普' WHEN br.yokin_sbt=1 THEN '当座' ELSE cm1980.kbn_value_lnm END) || br.st_br_koza_no 
                    AS bank_nm     
                ,d.status_kbn
                ,d._mod_cnt
                ,TO_CHAR(ski.sougi_ymd,'YYYY/MM/DD') AS sougi_ymd     
        FROM seikyu_denpyo d
        LEFT JOIN seikyu_sekyu_saki_info sssi
            ON sssi.seikyu_den_no = d.seikyu_den_no
            AND sssi.delete_flg = 0
        LEFT JOIN seko_kihon_info ski
            ON ski.seko_no = d.seko_no
            AND ski.delete_flg = 0
        LEFT JOIN bumon_mst bm
            ON bm.bumon_cd = d.bumon_cd
            AND bm.delete_flg = 0
        LEFT JOIN code_nm_mst moushi
            ON '0920'           =   moushi.code_kbn  
            AND d.data_kbn  =   moushi.kbn_value_cd_num 
            AND 0               =   moushi.delete_flg
        LEFT JOIN code_nm_mst juchusaki
            ON '1140'           =   juchusaki.code_kbn  
            AND d.juchusaki_kbn =   juchusaki.kbn_value_cd_num 
            AND 0               =   juchusaki.delete_flg
        LEFT JOIN br_koza_kanri_mst br
            ON  br.transfer_bank_cd = sssi.v_free3
        LEFT JOIN code_nm_mst cm1980
            ON  cm1980.kbn_value_cd_num = br.yokin_sbt
            AND cm1980.code_kbn = '1980' -- 預金種別(1980)
        WHERE d.delete_flg = 0   
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    /**
     * 請求伝票ダイアログ用(入金入力画面)
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForNyukin($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_den_no';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
                ,T.nyukin_prc 
                    + T.uchikin_prc 
                    + T.kouden_uchikin_prc
                    + T.cupon_prc
                    + T.bad_debt_loss_prc AS nyukin_prc
        FROM (
        SELECT d.* 
                ,c.kbn_value_lnm	AS 	seikyu_kbn_nm	-- 請求区分名,c.kbn_value_lnm   AS  seikyu_kbn_nm   -- 請求区分名
                ,TO_CHAR(COALESCE(d.seikyu_ymd,d.juchu_ymd),'YYYY/MM/DD')              AS seikyu_ymdx      -- 請求日
                ,TO_CHAR(d.kaishu_ymd,'YYYY/MM/DD')    AS kaishu_ymdx      -- 回収予定日
                ,d.uri_prc_sum + d.uri_nebk_sum + d.uri_hepn_sum +  d.hoshi_prc_sum
                    + d.sougi_early_use_cost
                    + d.etc_early_use_cost          AS seikyu_prc   -- 請求金額
                ,d.out_zei_prc + d.in_zei_prc       AS zei_prc      -- 消費税額
                    ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc + d.sougi_wari_zei
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.sougi_tokuten_prc
                    + COALESCE(d.n_free9, 0) 
                    + COALESCE(d.n_free10, 0) 
                AS  seikyu_zei_prc
                ,(SELECT 
                    ARRAY_TO_STRING(
                        ARRAY_AGG(
                            NULLIF(
                                TRIM('/' FROM 
                                    COALESCE(jm.nafuda_nm || '/','') 
                                    || COALESCE(jm.nafuda_nm2 || '/', '') 
                                    || COALESCE(jm.nafuda_nm3 || '/', '') 
                                    || COALESCE(jm.nafuda_nm4 || '/', '') 
                                    || COALESCE(jm.nafuda_nm5 || '/','') 
                                    || COALESCE(jm.nafuda_nm6 || '/', '') 
                                    || COALESCE(jm.nafuda_nm7 || '/', '') 
                                    || COALESCE(jm.nafuda_nm8 || '/', '')
                                )
                            ,'')
                        )
                    , ',')
                    FROM seikyu_denpyo_msi jm
                    WHERE d.seikyu_den_no = jm.seikyu_den_no
                    GROUP BY jm.seikyu_den_no
                ) AS nafuda_nm
                , b.bumon_snm
                , b.bumon_lnm
                ,CASE WHEN sssi.free_kbn1 = 1 THEN sssi.sekyu_nm 
                    ELSE concat_ws('　', sssi.sekyu_nm1, sssi.sekyu_nm2)  
                    END AS sekyusaki_sekyu_nm
                ,COALESCE(concat_ws('　', sssi.sekyu_knm1, sssi.sekyu_knm2), d.sekyu_knm) AS sekyusaki_sekyu_knm
                ,COALESCE(COALESCE(sssi.sekyu_addr1, '') || COALESCE(sssi.sekyu_addr2, ''), COALESCE(d.sekyu_addr1, '') || COALESCE(d.sekyu_addr2, '')) AS sekyusaki_sekyu_addr
                ,COALESCE(sssi.sekyu_cd, d.sekyu_cd) AS sekyusaki_sekyu_cd
                ,sssi.v_free1 AS kokyaku_no
                ,TO_CHAR(d.nonyu_dt,'YYYY/MM/DD') AS nonyu_ymd
                ,spt.tanto_nm   AS seikyu_print_tanto_nm
        FROM seikyu_denpyo d
        LEFT JOIN seikyu_sekyu_saki_info sssi   -- ダイアログ用で分割元は表示されない想定
            ON sssi.seikyu_den_no = d.seikyu_den_no
            AND sssi.delete_flg = 0
        LEFT JOIN    code_nm_mst    c
            ON 0                = c.delete_flg
            AND '0920'          = c.code_kbn
            AND d.data_kbn      = c.kbn_value_cd_num
        LEFT JOIN tanto_mst tt
            ON tt.delete_flg = 0
            AND d.tanto_cd = tt.tanto_cd   
            $dateWhere2
        LEFT JOIN tanto_mst spt
            ON spt.delete_flg = 0
            AND d.seikyu_print_tanto_cd = spt.tanto_cd   
        LEFT JOIN bumon_mst b
        ON b.delete_flg = 0
	AND d.bumon_cd = b.bumon_cd   
        $dateWhere3			
        WHERE d.delete_flg = 0   
        $dateWhere1
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    /**
     * 請求伝票 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlg($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_den_no';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT T.*
        FROM (
        SELECT d.* 
                ,c.kbn_value_lnm AS seikyu_kbn_nm   -- 請求区分名
                ,TO_CHAR(COALESCE(d.seikyu_ymd,d.juchu_ymd),'YYYY/MM/DD') AS seikyu_ymdx      -- 請求日
                ,TO_CHAR(d.kaishu_ymd,'YYYY/MM/DD') AS kaishu_ymdx      -- 回収予定日
                ,d.uri_prc_sum 
                + d.uri_nebk_sum 
                + d.uri_hepn_sum 
                + d.hoshi_prc_sum
                + d.sougi_early_use_cost
                + d.etc_early_use_cost          
                AS seikyu_prc   -- 請求金額
                ,d.out_zei_prc + d.in_zei_prc       
                AS zei_prc      -- 消費税額
                ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.etc_harai_prc
                AS  seikyu_zei_prc
                ,(SELECT 
                    ARRAY_TO_STRING(
                        ARRAY_AGG(
                            NULLIF(
                                TRIM('/' FROM 
                                    COALESCE(jm.nafuda_nm || '/','') 
                                    || COALESCE(jm.nafuda_nm2 || '/', '') 
                                    || COALESCE(jm.nafuda_nm3 || '/', '') 
                                    || COALESCE(jm.nafuda_nm4 || '/', '') 
                                    || COALESCE(jm.nafuda_nm5 || '/','') 
                                    || COALESCE(jm.nafuda_nm6 || '/', '') 
                                    || COALESCE(jm.nafuda_nm7 || '/', '') 
                                    || COALESCE(jm.nafuda_nm8 || '/', '')
                                )
                            ,'')
                        )
                    , ',')
                    FROM seikyu_denpyo_msi jm
                    WHERE d.seikyu_den_no = jm.seikyu_den_no
                    GROUP BY jm.seikyu_den_no
                ) AS nafuda_nm
                , b.bumon_snm
                , b.bumon_lnm
                , bkm.oya_bumon_cd
                , d.nyukin_prc 
                + d.uchikin_prc
                + d.cupon_prc
                + d.kouden_uchikin_prc
                AS  nyukin_prc
                ,COALESCE(COALESCE(sssi.sekyu_nm1, '') || COALESCE(sssi.sekyu_nm2, ''), d.sekyu_nm) AS sekyusaki_sekyu_nm
                ,COALESCE(COALESCE(sssi.sekyu_knm1, '') || COALESCE(sssi.sekyu_knm2, ''), d.sekyu_knm) AS sekyusaki_sekyu_knm
                ,COALESCE(COALESCE(sssi.sekyu_addr1, '') || COALESCE(sssi.sekyu_addr2, ''), COALESCE(d.sekyu_addr1, '') || COALESCE(d.sekyu_addr2, '')) AS sekyusaki_sekyu_addr
                ,COALESCE(sssi.sekyu_cd, d.sekyu_cd) AS sekyusaki_sekyu_cd
        FROM seikyu_denpyo d
        LEFT JOIN seikyu_sekyu_saki_info sssi   -- ダイアログ用で分割元は表示されない想定
            ON sssi.seikyu_den_no = d.seikyu_den_no
            AND sssi.delete_flg = 0
        LEFT JOIN    code_nm_mst    c
            ON 0                = c.delete_flg
            AND '0920'          = c.code_kbn
            AND d.data_kbn      = c.kbn_value_cd_num
        LEFT JOIN tanto_mst tt
            ON tt.delete_flg = 0
            AND d.tanto_cd = tt.tanto_cd   
            $dateWhere2
        LEFT JOIN bumon_mst b
        ON b.delete_flg = 0
	AND d.bumon_cd = b.bumon_cd   
        $dateWhere3		
        LEFT JOIN bumon_kaso_mst bkm
            ON bkm.ko_bumon_cd = d.bumon_cd
            AND bkm.delete_flg = 0
        WHERE d.delete_flg = 0   
        $dateWhere1
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

}
