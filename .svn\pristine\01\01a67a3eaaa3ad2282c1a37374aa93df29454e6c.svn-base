/**
 * 写真加工処理 
 */
var apppho = apppho || {};
$(function () {
    "use strict";

        // select2バインディング共通処理
        var _getSelect2Binding = function (cd, kbn) {
            var binding = {
                observe: cd,
                afterUpdate: function ($el) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el) {
                    // 区分値コード数値設定処理
                    if (!$.msiJqlib.isNullEx2(kbn)) {
                        _setKbnCdVal($el, this.model, kbn);
                    }
                    return $el.val();
                }
            };
            return binding;
        };
	// select2のvalを設定する
	var _setSelect2Val = function($el, val) {
		$el.select2("val", val);
	};
    
	// 区分値コード数値設定処理
	var _setKbnCdVal = function($el, m, target) {
		var item = $el.select2("data");
		if ($.msiJqlib.isNullEx2(item)) {
			m.set(target, null);
		} else {
			m.set(target, item.kbn_value_cd_num);
		}
	};

    // 画面クラスとモデルのプロパティのオブジェクト
    apppho.pro = {
        // 写真加工
        s_output_dt: '#print_date, #print_time', // 出力希望時間
        p_limit_dt: '#panel_deliv_date, #panel_deliv_time', // パネル到着期限
        s_han_kbn: '#s2id_photo_copy_1 .select-container', // 半切り区分
        s_han_cnt: '#s2id_photo_size_1 .select-container', // 半切り枚数
        s_cab_kbn: '#s2id_photo_copy_2 .select-container', // キャビネ区分
        s_cab_cnt: '#s2id_photo_size_2 .select-container', // キャビネ枚数
        s_sitei_yoko1: '#photo_size_3_width', // 指定1横
        s_sitei_tate1: '#photo_size_3_height', // 指定1縦
        s_sitei_yoko2: '#photo_size_4_width', // 指定2横
        s_sitei_tate2: '#photo_size_4_height' // 指定2縦
    };
    // 写真加工の新規と焼増ラジオボタン初期値は0:新規
//    apppho.printIdx = "0";


    // 写真枚数select2内容
    var _cuts = [
        {id: '1', text: '1'},
        {id: '2', text: '2'},
        {id: '3', text: '3'},
        {id: '4', text: '4'},
        {id: '5', text: '5'}
    ];
    // 写真新規・焼増select2内容
    var _copy_kbn = [
        {id: '0', text: '新規'},
        {id: '1', text: '焼増'}
    ];

    // select2のdataを取得する
    var _getSelect2Data = function ($el) {
        return $el.select2("data");
    };

    // チェックしたラジオボタンのvalueを取得する
    var _getTargetVal = function (e) {
        return $("#" + e.currentTarget.id + ':checked').val();
    };
    // プロパティを設定する
    var _setAttrChecked = function ($el) {
        $el.attr("checked", "checked");
    };

    // 画像ヘルパー処理 
    var _gazoHelper = function (t, hookData, gazo) {
        var m = t.model;
        t.$el.msiPickHelper({
            action: 'gazo',
            onSelect: function (data) {
                if (data.code) {
                    m.set(gazo.oid, data.gazo_img);
                    m.set(gazo.code, data.code);
                    m.set(gazo.name, data.gazo_nm);
                } else {
                    m.set(gazo.oid, null);
                    m.set(gazo.code, null);
                    m.set(gazo.name, null);
                }
            },
            onClear: function () {
                m.set(gazo.oid, null);
                m.set(gazo.code, null);
                m.set(gazo.name, null);
            },
            hookSetData: function () {
                return hookData;
            }
        });
    };
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                sougi_ymd: null, // 葬儀日
                panel_size_info_1: null, // 半マットパネルサイズ説明文
                panel_size_info_2: null, // 全紙マットパネルサイズ説明文
                panel_size_info_3: null, // 全倍サイズ説明文
                panel_size_info_4: null, // 大全倍サイズ説明文
                panel_size_info_5: null, // 超全倍サイズ説明文
                panel_price_1: null, // 半マットパネル金額
                panel_price_2: null, // 全紙マットパネル金額
                panel_price_3: null, // 全倍金額
                panel_price_4: null, // 大全倍金額
                panel_price_5: null // 超全倍金額
            };
        },
        validation: {
        }
    }); // AppModel

    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "change #print_set input": "clearPrintClass",
            "click #print_set input": "doPrintClick",
            "click #btn_save": "doSave",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click .tab li a": "changeTab",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "change #seko_date #photo_copy_1,#photo_copy_2,#photo_size_1,#photo_size_2, input[name=panel_type], input[name=color_type],#photo_frame_size, #photo_frame_color": function () {
                apppho.dpCreFlg = true;
            },

        },
        bindings: {
        },
        initialize: function () {
            this.render();
        },
        render: function () {
            this.stickit();
            return this;
        },
        // 写真加工、新規と焼増ボタンチェンジ処理
        clearPrintClass: function () {
            this.$('input[name=print]').removeClass("onCheck");
        },
        // 写真加工、新規と焼増ボタンクリック処理
        doPrintClick: function (e) {
            this.setPrinButton($(e.currentTarget));
            apppho.printIdx = _getTargetVal(e);
            this.setPhotoView();
        },
        // 写真加工、新規と焼増ボタン設定処理
        setPrinButton: function ($target) {
            if ($target.hasClass("onCheck")) {
                $target.attr("checked", false);
                $target.button("refresh");
                $target.removeClass("onCheck");
            } else {
                $target.toggleClass("onCheck");
            }
        },
        // 写真加工画面設定処理
        setPhotoView: function () {
            this.$("#photo-tab div").remove();
            if (apppho.printIdx >= 0) {
                var strIdx = String(apppho.printIdx);
                var exist = false;
                _.each(apppho.photoCol.models, function (m) {
                    if (m.get("yakimashi_kbn") === strIdx) {
                        apppho.photoModel = m;
                        exist = true;
                    }
                });
                if (!exist) {
                    apppho.photoModel = new PhotoModel({yakimashi_kbn: strIdx});
                    apppho.photoCol.add(apppho.photoModel);
                }
                apppho.photoModel.set("sougi_ymd", apppho.appModel.get("sougi_ymd"));
                apppho.photoModel.set(apppho.appModel.toJSON());
                apppho.photoView = new PhotoView({model: apppho.photoModel});
                //apppho.photoModel.trigger('change');
                this.$("#photo-tab").append(apppho.photoView.render().el);
                /* ラジオボックスのボタン化 */
                this.$('.radio_set').buttonset();
                if (apppho.appModel.get("juchu_kakute_ymd")) {
                    this.$("#photo_copy_1,#photo_copy_2,#photo_size_1,#photo_size_2,#photo_size_3,#photo_size_4").attr("disabled", "disabled");
                    this.$("input[name=panel_type]").button("disable");
                }
            }
            this.setButtonsStatus();
        },
        // 写真加工画面写真１～写真５ボタンのチェック状態を設定する
        setPhotoCheckedButton: function () {
            $('#print_set .lbl_print').removeClass("checked");
            _.each(apppho.photoCol.models, function (m) {
                var yakimashiKbn = m.get("yakimashi_kbn");
                $('#print_set .lbl_print').eq(yakimashiKbn).addClass("checked");
            });
        },
        // 保存ボタンと削除ボタンの活性・非活性を設定する
        setButtonsStatus: function () {
            var $btnSave = this.$('#btn_save');
            var $btnDel = this.$('#btn_delete');
            var idx = this.$('.tab li').index(this.$('.tab li span').closest("li"));
            if (idx === 0) { // 写真加工
                $btnDel.show();
                if (apppho.printIdx >= 0) {
                    $.msiJqlib.setBtnEnable($btnSave);
                } else {
                    $.msiJqlib.setBtnDisable($btnSave);
                }
                if (this.$('#print_set .lbl_print').eq(apppho.printIdx).hasClass("checked")) {
                    $.msiJqlib.setBtnEnable($btnDel);
                } else {
                    $.msiJqlib.setBtnDisable($btnDel);
                }
            } else if (idx === 1) { // モニタ遺影
                $.msiJqlib.setBtnEnable($btnSave);
                $btnDel.hide();
//                $.msiJqlib.setBtnDisable($btnDel);
            }

        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            this.setButtonsStatus();
        },
        isInputOk: function () {
            var aMsg = [];
            // 写真加工モデルチェック
            if (apppho.printIdx >= 0) {
                var result = apppho.photoModel.validate();
                if (result) {
                    _.each(result, function (v, k) {
                        aMsg.push(v);
                    });
                }
            }
            // NG
            if (aMsg.length > 0) {
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            // OK 
            $.msiJqlib.clearAlert();
            return true;
        },
        doSave: function () {
            $.msiJqlib.clearAlert();
            if (!this.isInputOk()) {
                return;
            }

            // 写真加工イコール
            var photoEq = $.msiJqlib.isEqual(apppho.photoModel.toJSON(), apppho.orgDataPhoto);
            photoEq = ($.msiJqlib.isNullEx2(apppho.printIdx) || photoEq);
            // モニタ遺影イコール
            var monitorEq = $.msiJqlib.isEqual(apppho.monitorModel.toJSON(), apppho.orgDataMonitor);
            if (photoEq && monitorEq) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            var dataPhotoJson = JSON.stringify(apppho.photoModel.toJSON());
            var dataMonitorJson = JSON.stringify(apppho.monitorModel.toJSON());
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                photoChangeFlg: !photoEq,
                monitorChangeFlg: !monitorEq
            });
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/photosave',
                data: {
                    dataApp: JSON.stringify(apppho.appModel.toJSON()),
                    dataPhoto: dataPhotoJson,
                    dataMonitor: dataMonitorJson,
                    changeFlg: changeFlg,
                    dpCreFlg: apppho.dpCreFlg
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        apppho.resetData(mydata.dataApp, mydata.dataPhotoCol, mydata.dataMonitor);
//                        apppho.appView.setButtonsStatus();
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doDelete: function () {
            var $targetPrint = this.$('#print_set .lbl_print').eq(apppho.printIdx);
            var lblPrint = $targetPrint.text();
            if ($.msiJqlib.isNullEx2(lblPrint) || !$targetPrint.hasClass("checked")) {
                return;
            }
            if (!confirm('「' + lblPrint + '」を削除します。よろしいですか？')) {
                return;
            }
            var dataPhotoJson = JSON.stringify(apppho.photoModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/photodelete',
                data: {
                    dataApp: JSON.stringify(apppho.appModel.toJSON()),
                    dataPhoto: dataPhotoJson
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        apppho.appView.setPrinButton($('input[name=print]').eq(apppho.printIdx));
                        apppho.printIdx = -1;
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        apppho.resetData(mydata.dataApp, mydata.dataPhotoCol, mydata.dataMonitor);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        }
    }); // AppView

    // 写真加工モデル
    var PhotoModel = Backbone.Model.extend({
        defaults: function () {
            return {
                yakimashi_kbn: null,
                s_input_bumon_cd: null,
                s_cab_cnt: null,
                s_sitei_cnt1: null,
                s_sitei_cnt2: null,
                s_haikei_kbn: null,
                s_kamon_nm: null,
                s_huku_type: null,
                s1_shohin_nm: null,
                s1_shohin_cd: null,
                s2_shohin_nm: null,
                s2_shohin_cd: null,
                s3_shohin_nm: null,
                s3_shohin_cd: null,
                i_free5: null,
                free_kbn1: null,
                free_kbn2: null,
                free_kbn3: null,
                free_kbn4: null,
                free_kbn5: null,
                free_kbn6: null,
                free_kbn7: null,
                free_kbn8: null,
                free_kbn9: null,
                free_kbn10: null,
                free_kbn11: null,
                free_kbn12: null,
                s_output_dt: null,
                free_kbn13: null,
                free_kbn14: null,
                free_kbn15: null,
                v_free1: null,
                biko1: null,
                siire_cd: null,
                s_output_bumon_cd: null,
                s_kisekae_kbn: null,
                s_huku_kbn: null,
                s_haikei_type: null,
                s_kamon_kbn: null,
                gazo_nm: null,
                s_jotai_kbn: null,
                size_kbn: null,
                s_kako_kbn: null,
                s_han_kbn: null,
                s_han_cnt: null,
                s_cab_kbn: null,
                s_sitei_yoko1: null,
                s_sitei_tate1: null,
                s_sitei_yoko2: null,
                s_sitei_tate2: null,
                p_size_kbn: null,
                p_limit_dt: null,
                seko_no: null,
                delete_flg: 0
            };
        },
        validation: {
            s_output_dt:{
                    required: false,
                    pattern: 'ymd',
                    msg: '納品日時[日付]の入力に誤りがあります。'
                },
            s_sitei_cnt1: [{
                    required: false,
                    maxLength: 3,
                    msg: '上下(段)は3桁までです。'
                },{
                    required: false,
                    pattern: 'number',
                    msg: '上下(段)に数値以外が入力されています。'
                }
            ],
            s_sitei_cnt2: [{
                    required: false,
                    maxLength: 3,
                    msg: '左右(段)は3桁までです。'
                },{
                    required: false,
                    pattern: 'number',
                    msg: '左右(段)に数値以外が入力されています。'
                }
            ],
            s_cab_cnt: [{
                    required: false,
                    maxLength: 3,
                    msg: '写真枚数は3桁までです。'
                },{
                    required: false,
                    pattern: 'number',
                    msg: '写真枚数に数値以外が入力されています。'
                }
            ],
            i_free5:[{
                    required: false,
                    maxLength: 3,
                    msg: 'キャビネ枚数は3桁までです。'
                },{
                    required: false,
                    pattern: 'number',
                    msg: 'キャビネ枚数に数値以外が入力されています。'
                }
            ],
            biko:{
                    required: false,
                    maxLength: 256,
                    msg: '備考は256文字までです。'
            }
        },
        // 指定サイズ横・縦の整合性チェック（両方入っているまたは両方入っていないはOK）
        shiteiCheck: function (idx) {
            var yokoVal = this.get("s_sitei_yoko" + idx);
            var tateVal = this.get("s_sitei_tate" + idx);
            var yokoFlg = $.msiJqlib.isNullEx2(yokoVal);
            var tateFlg = $.msiJqlib.isNullEx2(tateVal);
            if ((yokoFlg && tateFlg) || (!yokoFlg && !tateFlg)) {
                return false;
            } else {
                return true;
            }
        },
        // 半切り、キャビネの区分と枚数の整合性チェック
        kbnCntCheck: function (attr) {
            if ($.msiJqlib.isNullEx2(this.get(attr))) {
                return false;
            } else {
                return true;
            }
        }
    }); // PhotoModel

    // 写真加工コレクション
    var PhotoCollection = Backbone.Collection.extend({
        model: PhotoModel
    });
    // 写真加工ビュー
    var PhotoView = Backbone.View.extend({
        tagName: 'div',
        template: _.template($('#tmpl-photo').html()),
        events: {
//            "select2-selecting #photo_copy_1": "copyCheck",
//            "select2-selecting #photo_copy_2": "copyCheck",
            "click #lbl_dlg_crest": "kamonHelper",
            "click #cloth_name, #dlg_cloth": "hukuHelper",
            "click #bg_name, .sl_back1": "denshokuHaikeiHelper",
            "click .sl_back2": "yotugiriHaikeiHelper",
            "click .sl_back3": "kyabineHaikeiHelper",
            'change #siage_grade':"setSiageTechnique",
            // 加工方法クリック処理
            "click input[name='color_type']": function (e) {
                this.setModel(e, "s_kako_kbn"); // 0：カラー 1：白黒
            },
            // 切り抜きサイズクリック処理
            "click input[name='trim_type']": function (e) {
                this.setModel(e, "size_kbn"); // 0：A 1：B 2：C
            },
            // 着せ替えクリック処理
            "click input[name='change_cloth']": function (e) {
                this.setModel(e, "s_kisekae_kbn"); // 0：しない 1：する
            },
            // 背景クリック処理
            "click input[name='bg_setting']": function (e) {
                this.setModel(e, "s_haikei_kbn"); // 0：消す 1：そのまま
            },
            // 額縁クリック処理
            "click input[name='select_kbn']": function (e) {
                this.setModel(e, "free_kbn14"); // 0:黒額 1:カラー額
            },
            // 服クリック処理
            "click input[name='cloth_type']": function (e) {
                this.setModel(e, "s_huku_kbn"); // 0：和服 1：洋服
            },
            // 家紋クリック処理
            "click input[name='crest_setting']": function (e) {
                this.setModel(e, "s_kamon_kbn");  // 0：必要 1：ぼかす 2：消す
            },
            // パネル写真クリック処理
            "click input[name='panel_type']": function (e) {
                this.setModel(e, "p_size_kbn"); // 0：不要 1：半マットパネル 2：全紙マットパネル 3：全倍 4：大全倍 5：超全倍
            },
            // 写真状態区分クリック処理
            "click input[name='answer']": function (e) {
                this.setModel(e, "s_jotai_kbn"); // 0：電話連絡必要 1：連絡不要
            }
            
        },
        bindings: {
            '#input_bumon_cd': {
                observe: 's_input_bumon_cd',
                onSet: "empty2null"
            },
            '#output_bumon_cd': {
                observe: 's_output_bumon_cd',
                onSet: "empty2dateNow"
            },
            '#seko_date': {
                observe: 's_output_dt',
                onSet: "empty2null"
            },
            '#print_time': {
                observe: 's_output_time',
                onSet: "getTsuyaDate"
            },
            '#panel_deliv_date': {
                observe: 'p_limit_date',
                getVal: function ($el, event, options) {
                    this.setDate($el.val(), this.model.get('p_limit_time'), 'p_limit_dt', this.model);
                    return $el.val();
                }

            },
            '#panel_deliv_time': {
                observe: 'p_limit_time',
                getVal: function ($el, event, options) {
                    this.setDate(this.model.get('p_limit_date'), $el.val(), 'p_limit_dt', this.model);
                    return $el.val();
                }
            },
            '#print_date_2': 's_sitei_cnt1',
            '#photo_copy_1': {
                observe: 's_han_kbn',
//                update: window.msiBbUtils.updateSel2,
                onSet: "empty2null"
            },
            '#photo_size_1': {
                observe: 's_han_cnt',
                onSet: "empty2null"
            },
            '#photo_frame_size': {
                observe: 'v_free1',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
//            '#photo_frame_color': {
//                observe: 'free_kbn1',
//                onSet: "empty2null",
//                afterUpdate: function ($el, event, options) {
//                    $.msiJqlib.setSelect2Val($el, $el.val());
//                }
//            },
            '#siage_grade':_getSelect2Binding('free_kbn1'),
            
            '#photo_size': {
                observe: 'free_kbn2',
                onSet: "empty2null"
            },
            '#updown': {
                observe: 'free_kbn3',
                onSet: "empty2null"
            },
            '#lr': {
                observe: 'free_kbn4',
                onSet: "empty2null"
            },
            '#kisekae': {
                observe: 'free_kbn5',
                onSet: "empty2null"
            },
            '#photo_coler': {
                observe: 'free_kbn6',
                onSet: "empty2null"
            },
            '#yotugiri': {
                observe: 'free_kbn7',
                onSet: "empty2null"
            },
            '#kyabine': {
                observe: 'free_kbn8',
                onSet: "empty2null"
            },
            '#iei_type': {
                observe: 'free_kbn13',
                onSet: "empty2null"
            },
            '#seko_type': {
                observe: 'free_kbn12',
                onSet: "empty2null"
            },
            '#photo_num' : {
                observe:  's_cab_cnt',
                onSet: "empty2null"
            },
             '#producer1'  : { //製作人物1
                observe:  's_sitei_cnt1',
                onSet: "empty2null"
            },
             '#producer2'  : { //製作人物2
                observe:  's_sitei_cnt2',
                onSet: "empty2null"
            },
//	    '#kisekae_other': {
//                observe: 'i_free1',
//                onSet: "empty2null"
//            },
            '#photo_copy_2': {
                observe: 's_cab_kbn',
//                update: window.msiBbUtils.updateSel2,
                onSet: "empty2null"
            },
            '#photo_size_2': {
                observe: 's_cab_cnt',
                onSet: "empty2null"
            },
            '#photo_size_3': {
                observe: 's_sitei_cnt1',
                onSet: "empty2null"
            },
            '#photo_size_4': {
                observe: 's_sitei_cnt2',
                onSet: "empty2null"
            },
            '#photo_size_3_width': {
                observe: 's_sitei_yoko1',
                onSet: "empty2null"
            },
            '#photo_size_3_height': {
                observe: 's_sitei_tate1',
                onSet: "empty2null"
            },
            '#photo_size_4_width': {
                observe: 's_sitei_yoko2',
                onSet: "empty2null"
            },
            '#photo_size_4_height': {
                observe: 's_sitei_tate2',
                onSet: "empty2null"
            },
            '#lbl_panel_info_1': 'panel_size_info_1',
            '#lbl_panel_info_2': 'panel_size_info_2',
            '#lbl_panel_info_3': 'panel_size_info_3',
            '#lbl_panel_info_4': 'panel_size_info_4',
            '#lbl_panel_info_5': 'panel_size_info_5',
            '#lbl_panel_price_1': {
                observe: 'panel_price_1',
                onGet: 'commaAdd',
                onSet: "empty2null"
            },
            '#lbl_panel_price_2': {
                observe: 'panel_price_2',
                onGet: 'commaAdd'
            },
            '#lbl_panel_price_3': {
                observe: 'panel_price_3',
                onGet: 'commaAdd'
            },
            '#lbl_panel_price_4': {
                observe: 'panel_price_4',
                onGet: 'commaAdd'
            },
            '#lbl_panel_price_5': {
                observe: 'panel_price_5',
                onGet: 'commaAdd'
            },
             '#kyabine_num': {
                observe: 'i_free5',
                onSet: "empty2null"
            },
            //'#cloth_crest_name': 's_kamon_nm',
            '#cloth_name'   : 's_huku_nm',
            '#bg_name'      : 's_haikei_nm',
            '#photo_memo'   : 'biko1',
            '#kisekae_other' : 's_kamon_nm',
            '#pack_denshoku':{//電飾No
                   observe: 's1_shohin_nm',	
                   onSet: "empty2null"
            } ,
            '#pack_yotugiri': {//四切No
                observe:'s2_shohin_nm',
                onSet: "empty2null"
            },	
            '#pack_kyabine' : {	//キャビネNo
                observe:'s3_shohin_nm',
                onSet: "empty2null",
            },	
            '#s_kyabine':{
                observe:'free_kbn9',
                onSet: "empty2null",
            },
            '#s_yotugiri':{
                observe:'free_kbn10',
                onSet: "empty2null",
            },
            '#s_denshoku':{
                observe:'free_kbn11',
                onSet: "empty2null",
            },
            '#special_siage':''
            
        },
        commaAdd: function (val, options) {
            if (!$.msiJqlib.isNullEx2(val)) {
                return $.msiJqlib.commaAdd(val) + '円';
            }
        },
        empty2null: function (val) {
            if ($.msiJqlib.isNullEx2(val)) {
                return null;
            }
            return val;
        },
        initialize: function () {
            
            this.listenTo(this.model, 'change:s1_shohin_nm', this.setPickupNmStatus);
            this.listenTo(this.model, 'change:gazo_oid change:s_haikei_oid1 change:s_haikei_oid2 change:s_haikei_oid3  change:s_huku_oid', this.setGazo);
            this.listenTo(this.model, 'change:v_free1', this.setFrame);
            this.listenTo(this.model, 'change:free_kbn1', this.setSiageTechnique);
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(apppho.pro, "error1"));
            
        },
        render: function () {
            this.$el.html(this.template({baseUrl: $.msiJqlib.baseUrl()}));
            this.stickit();
            this.$(".seko_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            
            //施行日程
            
            // 写真取込先
            $.msiJqlib.setSelect2Com1(this.$("#input_bumon_cd"), ($.extend({data: $.msiJqlib.objToArray2(data.bumonkbn)}, $.msiJqlib.setSelect2Default1)));
            // 写真出力先
            $.msiJqlib.setSelect2Com1(this.$("#output_bumon_cd"), ($.extend({data: $.msiJqlib.objToArray2(data.bumonkbn)}, $.msiJqlib.setSelect2Default1)));
            // 半切
            $.msiJqlib.setSelect2Com1(this.$("#photo_copy_1"), ($.extend({data: _copy_kbn}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#photo_size_1"), ($.extend({data: _cuts}, $.msiJqlib.setSelect2Default1)));
            // 写真額
            $.msiJqlib.setSelect2Com1(this.$("#photo_frame_size"), ($.extend({data: data.dataKbn.photo_frame}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#photo_frame_color"), ($.extend({data: data.dataKbn.photo_frame_color}, $.msiJqlib.setSelect2Default1)));
            // キャビネ
            $.msiJqlib.setSelect2Com1(this.$("#photo_copy_2"), ($.extend({data: _copy_kbn}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#photo_size_2"), ($.extend({data: _cuts}, $.msiJqlib.setSelect2Default1)));
            // 指定1
            $.msiJqlib.setSelect2Com1(this.$("#photo_size_3"), ($.extend({data: _cuts}, $.msiJqlib.setSelect2Default1)));
            // 指定2
            $.msiJqlib.setSelect2Com1(this.$("#photo_size_4"), ($.extend({data: _cuts}, $.msiJqlib.setSelect2Default1)));
            // 仕上げの方法
            $.msiJqlib.setSelect2Com1(this.$("#siage_grade"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.siage_kbn)}, $.msiJqlib.setSelect2Default1)));
            //写真の形
            $.msiJqlib.setSelect2Com1(this.$("#photo_layout"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.photo_layout_kbn)}, $.msiJqlib.setSelect2Default1)));
            //写真サイズ
            $.msiJqlib.setSelect2Com1(this.$("#photo_size"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.photo_size_kbn)}, $.msiJqlib.setSelect2Default1)));
            //着せ替え
            $.msiJqlib.setSelect2Com1(this.$("#kisekae"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.kisekae_kbn)}, $.msiJqlib.setSelect2Default1)));
            //背景
            $.msiJqlib.setSelect2Com1(this.$("#haiekei"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.haikei_kbn)}, $.msiJqlib.setSelect2Default1)));
            //写真の色
            $.msiJqlib.setSelect2Com1(this.$("#photo_coler"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.photo_coler_kbn)}, $.msiJqlib.setSelect2Default1)));
            //四切
            $.msiJqlib.setSelect2Com1(this.$("#yotugiri"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.yotugiri_kbn)}, $.msiJqlib.setSelect2Default1)));
            //額縁
            $.msiJqlib.setSelect2Com1(this.$("#gakubuti"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.gakubuti_kbn)}, $.msiJqlib.setSelect2Default1)));
            //上下
            $.msiJqlib.setSelect2Com1(this.$("#updown"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.updown_kbn)}, $.msiJqlib.setSelect2Default1)));
            //左右
            $.msiJqlib.setSelect2Com1(this.$("#lr"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.lr_kbn)}, $.msiJqlib.setSelect2Default1)));
            //キャビネ
             $.msiJqlib.setSelect2Com1(this.$("#kyabine"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.kyabine_kbn)}, $.msiJqlib.setSelect2Default1)));
            //施工区分
             $.msiJqlib.setSelect2Com1(this.$("#seko_type"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.seko_kbn)}, $.msiJqlib.setSelect2Default1)));
            //遺影区分
             $.msiJqlib.setSelect2Com1(this.$("#iei_type"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.photo_layout_kbn)}, $.msiJqlib.setSelect2Default1)));

            // ラジオボタンを初期設定
            this.setRadioBoxIni();
            
            this.addGazo(this.$('#crest_pic_cloth'), this.model.get("gazo_oid")); // 家紋画像
            this.addGazo(this.$('#photo_pic'), this.model.get("s_huku_oid")); // 服装画像
            this.addGazo(this.$('#photo_pic_back1'), this.model.get("s_haikei_oid1")); // 背景画像
            this.addGazo(this.$('#photo_pic_back2'), this.model.get("s_haikei_oid2")); // 背景画像
            this.addGazo(this.$('#photo_pic_back3'), this.model.get("s_haikei_oid3")); // 背景画像

            // 写真額設定処理
            this.setFrame();
            this.setDefaultFrame();
            this.setTsuyaDate();
            this.setSiageTechnique();
            this.setGazoDoc();
            
            //ドキュメント設定
            this.setGazoDoc(this.$('.kisekae_doc'), this.model.get("s_huku_oid")); // 電飾画像
            this.setGazoDoc(this.$('.denshoku_doc'), this.model.get("s_haikei_oid1")); // 電飾画像
            this.setGazoDoc(this.$('.yotugiri_doc'), this.model.get("s_haikei_oid2")); // 四切画像
            this.setGazoDoc(this.$('.kyabine_doc'), this.model.get("s_haikei_oid3")); // キャビネ画像
            
            return this;
        },
         //特別セットの活性状態設定処理
         setSiageTechnique : function(){
             var val = this.model.get('free_kbn1'); 
             if(val === ""){
                 this.model.set('free_kbn1', null);
             }
             if(val === '5'){
                    //this.$("#special_siage").removeAttr("disabled");
                    this.$("#special_siage").removeAttr("disabled");
                    this.$("#label_siage").removeAttr("disabled");
             } else {
                   //this.$("#special_siage").attr("disabled", "disabled");
                   this.$("#special_siage").attr("disabled", "disabled");
                   this.$("#label_siage").attr("disabled", "disabled");
                   this.model.set('v_free1', null);
             }
             
         },
        // ラジオボタンを初期設定処理
        setRadioBoxIni: function () {
            // 加工方法
            this.setRadioBox("s_kako_kbn", ["#color", "#mono"]);
            // 切り抜きサイズ
            this.setRadioBox("size_kbn", ["#trim_a", "#trim_b", "#trim_c"]);
            // 着せ替え
            this.setRadioBox("s_kisekae_kbn", ["#nochange", "#change"]);
            // 背景
            this.setRadioBox("s_haikei_kbn", ["#bg_hide", "#bg_show"]);
            // 写真額
            this.setRadioBox("free_kbn14", ["#size_2go", "#size_3go"]);
            // 服
            this.setRadioBox("s_huku_kbn", ["#japanese_cloth", "#foreign_cloth"]);
            // 家紋
            this.setRadioBox("s_kamon_kbn", ["#crest_show", "#crest_shade", "#crest_hide"]);
            // パネル写真
            this.setRadioBox("p_size_kbn", ["#no_panel", "#panel_size_1", "#panel_size_2", "#panel_size_3", "#panel_size_4", "#panel_size_5"]);
            // 写真状態区分
            this.setRadioBox("s_jotai_kbn", ["#answer_1", "#answer_2"]);
        },
        setRadioBox: function (pro, els) {
            var that = this;
            _.each(els, function (el, key) {
                if (that.model.get(pro) === String(key)) {
                    _setAttrChecked(that.$(el));
                }
            });
        },
        setModel: function (e, pro) {
            var val = _getTargetVal(e);
            this.model.set(pro, val);
        },
        // 日付と時刻を結合してタイムスタンプに設定する処理
        setDate: function (date, time, pro, model) {
            var ymd = null;
            if (!$.msiJqlib.isNullEx2(date)) {
                if ($.msiJqlib.chkDate(date)) {
                    ymd = date;
                }
            }
            if (!$.msiJqlib.isNullEx2(time)) {
                if (!$.msiJqlib.isNullEx2(date)) {
                    ymd += " ";
                }
                ymd += time;
            }
            model.set(pro, ymd);
        },
        setGazo: function (m, val) {
            var c = m.changed;
            var el;
            var doc;
            if (_.has(c, 'gazo_oid')) {
                el = "#crest_pic_cloth";
            } else if (_.has(c, 's_huku_oid')) {
                el = "#photo_pic";
                doc = ".kisekae_lbl";
            } else if (_.has(c, 's_haikei_oid1')) {
                el = "#photo_pic_back1";
                doc = ".denshoku_doc";
            } else if (_.has(c, 's_haikei_oid2')) {
                el = "#photo_pic_back2";
                doc = ".yotugiri_doc";
            }else if (_.has(c, 's_haikei_oid3')) {
                el = "#photo_pic_back3";
                doc = ".kyabine_doc";
            } else {
                return;
            }
            this.addGazo(this.$(el), val, doc);
        },
        setGazoDoc:function(cls, val){
            if ($.msiJqlib.isNullEx2(val)) {
                $(cls).css("color" , "#D4E5F4");
            }
        },
        addGazo: function ($el, val, doc) {
            $el.find('img').css("visibility","hidden");
            var count = $el.find('img').size();
            if(count >= 1){
                $el.find('img').remove();
            }
            if (!$.msiJqlib.isNullEx2(val)) {
                $('<img>').appendTo($el).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + val);
                $(doc).css("color" , "#276DA5");
            } else{
                 $(doc).css("color" , "#D4E5F4");
            }
        },
        // 家紋ヘルパー処理 
        kamonHelper: function () {
            _gazoHelper(this, {s_gazo_kbn: 1, limit: 20}, {oid: 'gazo_oid', code: 'gazo_nm', name: 's_kamon_nm'});
        },
        // 服ヘルパー処理 
        hukuHelper: function () {
            var sex_kbn = apppho.appModel.get("k_sex_kbn");
            if (sex_kbn === "1") {
                sex_kbn = "0";
            } else if (sex_kbn === "2") {
                sex_kbn = "1";
            } else {
                sex_kbn = "9";
            }
            _gazoHelper(this, {s_gazo_kbn: 5, limit: 20, s_k_sex_kbn: sex_kbn}, {oid: 's_huku_oid', code: 's_huku_type', name: 's_kamon_nm'});
        },
        
        //各背景連動処理
        setPickupNmStatus : function(){
           //var val_denshoku_cd = this.model.get("s1_shohin_cd");
           var val_denshoku_nm = this.model.get("s1_shohin_nm");
           var val_yotugiri =  this.model.get("s2_shohin_cd");
           var val_kyabine  =  this.model.get("s3_shohin_cd");
            if($.msiJqlib.isNullEx2(val_yotugiri)){
                 this.model.set("s2_shohin_nm", val_denshoku_nm);
            } 
            if($.msiJqlib.isNullEx2(val_kyabine)){
                 this.model.set("s3_shohin_nm", val_denshoku_nm);
            }
        },
        // 電飾背景ヘルパー処理 
        denshokuHaikeiHelper: function () {
            _gazoHelper(this, {s_gazo_kbn: 4, limit: 20}, {oid: 's_haikei_oid1', code: 's1_shohin_cd', name: 's1_shohin_nm'});
        },
        yotugiriHaikeiHelper: function () {
            _gazoHelper(this, {s_gazo_kbn: 4, limit: 20}, {oid: 's_haikei_oid2', code: 's2_shohin_cd', name: 's2_shohin_nm'});
        },
        kyabineHaikeiHelper: function () {
            _gazoHelper(this, {s_gazo_kbn: 4, limit: 20}, {oid: 's_haikei_oid3', code: 's3_shohin_cd', name: 's3_shohin_nm'});
        },
        // 半切りとキャビネの新規と焼増の整合性チェック
        copyCheck: function (e) {
            var $target_, $target = $(e.currentTarget);
            if ($target.is("#photo_copy_1")) {
                $target_ = this.$("#photo_copy_2");
            } else {
                $target_ = this.$("#photo_copy_1");
            }
            var val = e.val;
            var val_ = _getSelect2Data($target_);
            if (!$.msiJqlib.isNullEx2(val) && !$.msiJqlib.isNullEx2(val_)) {
                if (val === val_.id) {
                    $.msiJqlib.showWarn('新規または焼増を複数選択することができません');
                    e.preventDefault();
                    $target.select2("close");
                    return;

                }
            }
        },
        setFrame: function () {
            var item = $.msiJqlib.getSelect2Data(this.$("#photo_frame_size"));
            if (!$.msiJqlib.isNullEx2(item) && item.index === 0) {
                this.$("#photo_frame_color").removeAttr("disabled");
            } else {
                this.$("#photo_frame_color").attr("disabled", "disabled");
                //this.model.set("free_kbn1", null);
            }
        },
        setDefaultFrame: function () {
            if ($.msiJqlib.isNullEx2(this.model.get("v_free1"))) {
                if (!$.msiJqlib.isNullEx2(data.dataKbn.frame_shohin_cd)) {
                    this.model.set("v_free1", data.dataKbn.frame_shohin_cd);
                }
            }
        },
         setTsuyaDate : function(){
             if ($.msiJqlib.isNullEx2(this.model.get("s_output_dt"))) {
                    if (!$.msiJqlib.isNullEx2(data.dataApp.sougi_ymd)) {
                         return  this.model.set("s_output_dt", data.dataApp.sougi_ymd);
                }
            }
        },
    }); // PhotoView

    // モニタ遺影モデル
    var MonitorModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号  
                m_view_shot_kbn: "0", // ビューアーショットのみ依頼区分
                m_kbn: "0", // モニター遺影区分 0：なし 1：あり
                m_input_bumon_cd: null, // 写真取込先部門
                m_output_bumon_cd: null, // 写真出力先部門
                m_muki_kbn: null, // モニター頭の向き 0：左 1：右
                m_haikei_kbn: null, // モニター背景区分 0：原文のまま 1：遺影写真と同背景 2：遺影写真文字入り 3：遺影写真と違背景
                gazo_oid2: null, // モニター背景oid
                gazo_cd2: null, // モニター背景コード
                gazo_cd2_nm: null, //モニター背景名
                biko: null // 注意事項
            };
        },
      validation: {
                      biko:{
                    required: false,
                    maxLength: 200,
                    msg: '備考は200文字までです。'
            }
      }
    }); // MonitorModel

    // モニタ遺影ビュー
    var MonitorView = Backbone.View.extend({
        el: $("#monitor-tab"),
        events: {
            "change #layout_set input": "clearLayoutClass",
            "click #layout_set input": "doLayoutClick",
            "click #dlg_bg_monitor":"haikeiHelper",
            "click #layout_left": function() {
                // モニター頭の向き（左）設定
                this.model.set("m_muki_kbn", 0);
            },
            "click #layout_right": function() {
                // モニター頭の向き（右）設定
                this.model.set("m_muki_kbn", 1);
            },
            // モニター選択 
            "click #monitor_list .sample .title, #monitor_list .sample .pic": function (e) {
                var li = $(e.currentTarget).parent('li');
                var idx = this.$('#monitor_list li.sample').index(li);
                if (li.hasClass('selected')) {
                    li.removeClass('selected');
                    this.model.set("m_kbn", "0");
                    this.model.set("m_haikei_kbn", null);
                } else {
                    $('#monitor .sample').removeClass('selected');
                    li.addClass('selected');
                    this.model.set("m_kbn", "1");
                    this.model.set("m_haikei_kbn", String(idx));
                }
//                this.setHaikei();
            },
            "change #view_shot_kbn": function (e) {
                // ビューアーショットのみの依頼チェックボックス値を設定
                this.setCheckBox(e, "lbl_layout_left", '#view_shot_kbn');
            }
            ,"click #monitor_bg_name, .dlg_monitor_bg, #dlg_bg_monitor": "haikeiHelper"
        },
        bindings: {
            '#memo': 'biko',
            '#input_bumon_cd_2': {
                observe: 'm_input_bumon_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                onSet: "empty2null"
            },
            '#output_bumon_cd_2': {
                observe: 'm_output_bumon_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                onSet: "empty2null"
            },
            '#bg_monitor': { //希望バック
                observe: 'm_view_shot_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                onSet: "empty2null"
            },
            '#bg_monitor_txt': 'gazo_cd2_nm',
            '#title_txt': {
                observe:'masthead_txt',
                onSet: "empty2null"
            }
        },
        empty2null: function (val) {
            if ($.msiJqlib.isNullEx2(val)) {
                return null;
            }
            return val;
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(apppho.pro, "error1"));
            this.listenTo(this.model, 'change:gazo_oid2', this.setGazo);
            this.listenTo(this.model, 'change:m_haikei_kbn', this.setHaikei);
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(apppho.pro, "error1"));
            this.stickit();
//            this.setHaikei();
            // 写真取込先
            $.msiJqlib.setSelect2Com1(this.$("#input_bumon_cd_2"), ($.extend({data: $.msiJqlib.objToArray2(data.bumonkbn)}, $.msiJqlib.setSelect2Default1)));
            // 写真出力先
            $.msiJqlib.setSelect2Com1(this.$("#output_bumon_cd_2"), ($.extend({data: $.msiJqlib.objToArray2(data.bumonkbn)}, $.msiJqlib.setSelect2Default1)));
            //背景区分
            $.msiJqlib.setSelect2Com1(this.$("#bg_monitor"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbn.monitor_bg_kbn)}, $.msiJqlib.setSelect2Default1)));
            this.addGazo(this.$('#crest_pic_cloth'), this.model.get("gazo_oid2"), ".haikei_doc_aska"); // 家紋画像
            return this;
        },
        // 背景コードのアイコン切り替え処理
        setHaikei: function () {
            if ((this.model.get("m_haikei_kbn")) === "3") { // 3：遺影写真と違背景
                this.$("#monitor_bg_name").removeAttr("disabled");
            } else {
                this.$("#monitor_bg_name").attr("disabled", "disabled");
                this.model.set("gazo_cd2", null);
            }
        },
        // モニタ遺影ヘルパー処理
        haikeiHelper: function () {
          //  if ((this.model.get("m_haikei_kbn")) === "3") { // 3：遺影写真と違背景
                _gazoHelper(this, {s_gazo_kbn: 4, limit: 20}, {oid: 'gazo_oid2', code: 'gazo_cd2', name: 'gazo_cd2_nm'});
           // }
        },
        // （縦向きの場合）頭の向きボタンチェンジ処理
        clearLayoutClass: function () {
            this.$('input[name=layout]').removeClass("onCheck");
        },
        // （縦向きの場合）頭の向きボタンクリック処理

         setGazoDoc:function(cls, val){
            if ($.msiJqlib.isNullEx2(val)) {
                $(cls).css("color" , "#D4E5F4");
            }
          },
         addGazo: function ($el, val, doc) {
              $el.find('img').remove();
            if (!$.msiJqlib.isNullEx2(val)) {
                $('<img>').appendTo($el).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + val);
                $(doc).css("color" , "#276DA5");
            } else{
                 $(doc).css("color" , "#D4E5F4");
            }
        },
        setGazo: function (m, val) {
            var c = m.changed;
            var el;
            var doc;
            if (_.has(c, 'gazo_oid2')) {
                el = "#photo_pic_back4";
                doc = ".haikei_doc_aska";
            } else {
                return;
            }
            this.addGazo(this.$(el), val, doc);
        },
    }); // MonitorView

    // データ取得
    var data = msiLib2.getJsonFromHtml($('#data-photo'));

    // 初期化処理
    apppho.appModel = new AppModel();
    apppho.photoModel = new PhotoModel();
    apppho.photoCol = new PhotoCollection();
    apppho.monitorModel = new MonitorModel();
    apppho.appView = new AppView({model: apppho.appModel});
    apppho.monitorView = new MonitorView({model: apppho.monitorModel});

    // サイドメニュー設定
    $.msiSideMenuLib.setSideMenu({showFooter: true});

    // データ設定処理
    apppho.resetData = function (dataApp, dataPhotoCol, dataMonitor) {
        // 受注伝票自動作成フラグ
        apppho.dpCreFlg = false;
        // APPデータを設定する
        apppho.appModel.set(dataApp);
        // 写真加工コレクションを設定する
        apppho.photoCol.set(dataPhotoCol);
        // 写真加工画面写真１～写真５ボタンのチェック状態を設定する
        apppho.appView.setPhotoCheckedButton();
        // 写真加工画面を設定する
        apppho.appView.setPhotoView();
        // モニタ遺影データを設定する
        apppho.monitorModel.set(dataMonitor);
        
        if (dataMonitor) {
            if (dataMonitor.m_kbn === '1') {
                // モニタ遺影の背景を設定
                $('#monitor_list li.sample').eq(dataMonitor.m_haikei_kbn).addClass("selected");
            }
            // モニタ遺影の頭の向きを設定
            if (dataMonitor.m_muki_kbn === "1") {
//                $("#layout_right").click();
                $("#layout_right").attr("checked", true)
                        .button("refresh")
                        .addClass("onCheck");
            } else if (dataMonitor.m_muki_kbn === "0") {
//                $("#layout_left").click();
                $("#layout_left").attr("checked", true)
                        .button("refresh")
                        .addClass("onCheck");
            }
            
            // ビューアーショットのみ依頼区分
            if (dataMonitor.m_view_shot_kbn === "1" && $('#view_shot_kbn:checked').val() !== "1") {
                $('#view_shot_kbn').click();
            }
        }
        // データを退避する
        apppho.orgDataPhoto = apppho.photoModel.toJSON();
        apppho.orgDataPhotoCol = apppho.photoCol.toJSON();
        apppho.orgDataMonitor = apppho.monitorModel.toJSON();
    };
    apppho.resetData(data.dataApp, data.dataPhotoCol, data.dataMonitor);
    $("#photo-div-wrapper").show();
});