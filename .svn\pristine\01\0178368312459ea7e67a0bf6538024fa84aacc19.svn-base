@import url('juchu.itemex.css');

#detail #goods.itemex .list ul li .pic img {
	width:auto;
	height:auto;
	max-width:100%;
	max-height:100%;
}
#lvl1_tab_arrow {
    top: 40px;
}
#detail #goods.itemex .list ul li .pic img[src*="imgid//v/"]{
    width: 100%;
    height: 100%;
}
#detail .plan-name {
	color: white;
	bottom: 150px;
	width: 160px;
}
#detail .plan-name-1 {
  font-size: 12px;
  top:5px;
}
#detail span.kaiin
{
    font-size: 18px;
}
#detail #goods .list ul li .pricex {
	/*color: #65686b;*/
	display: block;
	width: 100%;
	font-size: 10px;
	margin-top: 10px;
}
#detail #goods .list ul li .pricex .zeikomi {
	color: rgb(69, 35, 206);
}
#detail #goods .list ul li .pricex .n {
	font-size: 18px;
}
#detail #goods .list ul li .pricex .n3 {
	font-size: 18px;
}
#detail #goods .list ul li .pricex .n4 {
	font-size: 18px;	
}
detail #goods.itemex .list ul li .pricex .zeikomi {
	color: #C60;
}
#detail #goods.itemex .list ul li {
	height: 355px;
}
#detail #goods.itemex.h2 .list ul li {
    height: 355px;
}
#detail #goods .list ul li .limited_time {
    position: absolute;
    left:   5%;
    top:   45%;
    font-size: 14px;
    color: red;
}
#detail #goods .list ul li .limited_date {
    position: absolute;
    left:   5%;
    top:   52%;
    font-size: 14px;
    color: red;
}
#detail #goods .list ul li .web_disp_char {
    position: absolute;
    left:  80%;
    top:   50%;
    font-size: 14px;
    color: white;
    background-color: brown;
    height: 20px;
    width: 68px;
    vertical-align: middle;
    text-align: center;
    padding: 8px 0px 0px 0px;
}
#detail #kakutei_info {
    position: relative;
    float: right;
	top: -5px;
	right: 15%;
	width: 10%;
    z-index: -1;
	color: #FFF;
	font-size: 16px;
	background-color: brown;
	padding: 8px 0px 0px 0px;
	font-weight: bold;
	vertical-align: middle;
	text-align: center;
	height: 30px;
}
#detail .gojokai_tkn {
    left: 20px;
}
#detail .plan-service {
    position: absolute;
    left: 50px;
    bottom: 8px;
    font-weight: bold;
    padding: 0 2px 0 0;
    margin: 0;
    vertical-align: middle;
    text-align: center;
    border-radius: 50%;
    background-color: #0006ff;
    height: 40px;
    width: 40px;
    color: #FFF;
}
#detail #goods.itemex .list ul li .price {
    font-size: 9px;
    margin-top: 8px;
    height: 23px;
}
#detail #goods.itemex .list ul li .name {
    margin-top: 9px;
}
#detail #goods.itemex .list ul li {
    font-size: 16px;
}
#detail #goods.itemex .list ul li .price .n {
    font-size: 16px;
}
#detail #goods .list ul li .price .n2 {
    font-size: 16px;
}
#detail #goods .list ul li .pricex {
    font-size: 10px;
    margin-top: 0;
    margin-bottom: 3px;
}
#detail #hanbai_tnk {
    height: 21px;
    font-size: 17px;
}
#detail #goods .list ul li .pricex .n2 {
    font-size: 18px;
}
#detail #goods.itemex .list ul li .shohin_nm input {
    font-size: 16px;
}
#detail #goods.itemex .list ul li .counter {
    margin: 8px auto 0 auto;
}
#detail #goods.itemex .list ul li {
    margin: 0px 20px 20px 0;
}
#detail .lbl_service_charge.ui-state-active {
    margin: 3px 0 5px 5px;
}
#detail #goods.itemex .list ul li .keigen-deco-div {
    position: absolute;
    bottom: 0;
    right: 0;
	box-sizing: border-box;
}

#detail #goods.itemex .list ul li .keigen-deco-div .keigen-cap {
	display: inline-block;
	box-sizing: border-box;
    float: right;
    margin-right: 5px;
    margin-bottom: 5px;
    font-size:13px;
    padding: 2px 5px;
    color: white;
	background-color: #00cc99; /* SeaGreen */
    border: 2px solid #00956f;
	border-radius: 1px;
}

#detail #goods.itemex .list ul li .keigen-deco-div img {
    margin-right: 5px;
    margin-bottom: 5px;
    width: 60px;
}

#detail .dlg_edi{
    width: 20%;
    position: absolute;
    display: block;
    right: 0;
    top: 32px;
    border-bottom: none;
    border-right: none;
    background-image: url(../../../img/ico_dialog_2_edi.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 12px 10px;
}
#detail .grade-up {
    position: absolute;
    z-index: 100;
    right:5px;
    bottom: 150px;
    font-weight: bold;
    padding: 0 2px 0 0;
    margin: 0;
    vertical-align: middle;
    text-align: center;
    height:30px;
    min-width: 150px;
    color: white;
}
#detail .grade-up-1 {
  position: relative;
  padding: 3px 5px 0 5px;
  font-size: 12px;
  top:5px;
}
#detail .gradeup-disable {
    pointer-events: none;
}