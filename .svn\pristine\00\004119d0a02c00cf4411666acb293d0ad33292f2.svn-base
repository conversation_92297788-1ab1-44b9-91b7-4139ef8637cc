/** 
 * @fileoverview 印刷指示画面
 */
$(function () {
    "use strict";

    var BumonModel = Backbone.Model.extend({

        defaults: function () {
            return {
                select_type: 1, // null
                chk_bumon: true,
                bumon_cd: null,
                bumon_nm: null,
            };
        },

        validation: {
            select_type: {
                required: true,
            },
            bumon_cd: {
                required: true,
            },
            bumon_nm: {
                required: true,
            },
        },

        labels: {
            select_type: '選択',
            bumon_cd: '部門CD',
            bumon_nm: '部門名',
        },

    }); // BumonModel

    var BumonCollection = Backbone.Collection.extend({
        model: BumonModel
    }); // BumonCollection

    var AppModel = Backbone.Model.extend({

        defaults: function () {
            return {
                print_ymd: null,
                print_kbn: null,
                taisho_st_ymd: null,
                taisho_ed_ymd: null,
                taisho_st_ym: null,
                taisho_ed_ym: null,
                seko_no_st: null, // 施行番号(自) 2016/12/28 ADD Kayo
                seko_no_ed: null, // 施行番号(至) 2016/12/28 ADD Kayo
                moke_no_st: null, // 喪家番号(自) 
                moke_no_ed: null, // 喪家番号(至)
                taisho_ymd: {
                    'display': '', // 非表示の場合、「'none'」
                    'pattern': 'ymd'    // 年月指定の場合、「'ym'」
                },
                bumon: {
                    'display': ''      // 非表示の場合、「'none'」
                },
                taisho_gengo: null,
                taisho_ym: null,
                moke_no: null,
                souke_nm: null,
                m_nm: null,
                chu_bunrui:null
            };
        },

        validation: {
            print_ymd: {
                required: true,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            taisho_st_ymd: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            taisho_ed_ymd: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            seko_no_st: {// 施行番号(自) 2016/12/28 ADD Kayo
                required: false
            },
            seko_no_ed: {// 施行番号(至) 2016/12/28 ADD Kayo
                required: false
            },
            uri_den_no_st: {
                required: false

            },
            uri_den_no_ed: {
                required: false
            },
            taisho_st_ym: function (value, name) {
                if (this.get('taisho_ymd').pattern === 'ym') {
                    if ($.msiJqlib.isNullEx2(value)) {
                        return this.labels[name] + 'は必須項目です';
                    }
                    return $.msiJqlib.chkYmd(value + '/01');
                } else {
                    return;
                }
            },
            taisho_ed_ym: function (value, name) {
                if (this.get('taisho_ymd').pattern === 'ym') {
                    if ($.msiJqlib.isNullEx2(value)) {
                        return this.labels[name] + 'は必須項目です';
                    }
                    return $.msiJqlib.chkYmd(value + '/01');
                } else {
                    return;
                }
            },
        },

        labels: {
            print_ymd: '印刷年月日',
            taisho_st_ymd: '対象年月日(自)',
            taisho_ed_ymd: '対象年月日(至)',
            taisho_st_ym: '対象年月(自)',
            taisho_ed_ym: '対象年月(至)',
            seko_no_st: '施行番号(自)', // 2016/12/28 ADD Kayo
            seko_no_ed: '施行番号(至)', // 2016/12/28 ADD Kayo
            uri_den_no_st: '請求番号(自)',
            uri_den_no_ed: '請求番号(至)'
        }

    }); // AppModel

    var BumonView = Backbone.View.extend({

        tagName: "tbody",
        template: _.template($('#item-tmpl_bumon').html()),

        initialize: function () {
            Backbone.Validation.bind(this);
            this.listenTo(this.model, 'destroy', this.remove);
        },

        render: function () {

            Backbone.Validation.bind(this, {
                valid: function (view, attr, selector) {
                    var $el = view.$('.' + attr),
                            $group = $el;

                    $el.removeClass('my-error');
                    $el.attr('title', '');
                    $group.removeClass('has-error');
                },
                invalid: function (view, attr, error, selector) {
                    var $el = view.$('.' + attr),
                            $group = $el;

                    $el.addClass('my-error');
                    $el.attr('title', error);
                    $group.addClass('has-error');
                }
            });

            if (this.template === null) {
                this.template = _.template($('#item-tmpl_bumon').html());
            }
            this.$el.html(this.template({idx: this.model.cid}));

            $('.msi-picker', this.$el).each(msiLib2.msiPickerBinder);

            this.stickit();

            return this;
        },
        // Remove the item, destroy the model.
        clear: function () {
            this.model.destroy();
        },

        bindings: {
            '.chk_bumon': {
                observe: 'chk_bumon',
                events: ['change']
            },
            '.bumon_cd': {
                observe: 'bumon_cd',
                events: ['change']
            },
            '.bumon_nm': {
                observe: 'bumon_nm',
                events: ['change']
            },
            '.seko_no_st': {// 施行番号(自) 2016/12/28 ADD Kayo
                observe: 'seko_no_st',
                events: ['change']
            },
            '.seko_no_ed': {// 施行番号(至) 2016/12/28 ADD Kayo
                observe: 'seko_no_ed',
                events: ['change']
            },
            '.moke_no_st': {// 喪家番号(自) 2016/12/28 ADD Kayo
                observe: 'moke_no_st',
                events: ['change']
            },
            '.moke_no_ed': {// 喪家番号(至) 2016/12/28 ADD Kayo
                observe: 'moke_no_ed',
                events: ['change']
            },
            '.uri_den_no_st': {
                observe: 'uri_den_no_st',
                pattern: 'number',
            },
            '.uri_den_no_ed': {
                observe: 'uri_den_no_ed',
                pattern: 'number',
            },
            '#chu_bunrui': {
                observe: 'chu_bunrui',
            },
        }
    }); // BumonView

    var AppView = Backbone.View.extend({

        el: document, // '#my-form-id', // for #hall_cd

        events: {
            "click #bumon_all": "doCheckedBumon",
            "click #bumon_rel": "doUnCheckedBumon",
            "click .seko_no_st-ref": "sekoNoHelperStart",
            "click .seko_no_ed-ref": "sekoNoHelperEnd",
            "click .seko_no-ref": "sekoNoHelper",
            "click #btn_print": "doPrint",
            "click #btn_excelout": "doExcelPrint",
            "click #btn_csvout": "doCsvOut",
            "click #btn_print_prev": "doPrintPrev",
            "click #btn_printChg": "doPrintJuchu",
            "click #btn_incentive_out": "doIncentiveOut",
            "click #btn_byoinexcelout": "doByoinExcelOut",
            "click #shohin_kbn_all": "doCheckedShohinKbn",
            "click #shohin_kbn_rel": "doUnCheckedShohinKbn",
            "click #chohyo_ari": function () {
                this.model.set("chouhyou_kbn", "1");
            },
            "click #chohyo_nashi": function () {
                this.model.set("chouhyou_kbn", "0");
            }
        },

        initialize: function () {

            Backbone.Validation.bind(this);

            this.listenTo(bumonCol, 'reset', this.resetColBumon);

            this.render();
        },

        resetColBumon: function (collection) {
            var $table = this.$("#bumon_dtl-table-id");
            $table.find('tbody').remove();
            _.each(collection.models, function (m) {
                var v = new BumonView({model: m});
                $table.append(v.render().el);
            });

            this.render();
        },

        render: function () {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind(this, {
                valid: function (view, attr, selector) {
                    var $el = view.$('#' + attr),
                            $group = $el.closest('.input-group');
                    $group = $group || $el;

                    $el.removeClass('my-error');
                    $el.attr('title', '');
                    $group.removeClass('has-error');
                },
                invalid: function (view, attr, error, selector) {
                    var $el = view.$('#' + attr),
                            $group = $el.closest('.input-group');
                    $group = $group || $el;

                    $el.addClass('my-error');
                    $el.attr('title', error);
                    $group.addClass('has-error');
                }
            });

            this.stickit();

            // スクロール調整
            this.scrollAdj();

           // this.btnEnabled('#btn_print, #btn_csvout'); コメント By MSI Salma on 2019/04/18
           this.btnEnabled('#btn_print');
            return this;
        },
        // 施行No(自) pickup
        sekoNoHelperStart: function () {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                    function (data) {
                        // console.log( JSON.stringify(data) );
                        bbm.set('seko_no_st', data.seko_no);
                    });
        },
        // 施行No(至) pickup
        sekoNoHelperEnd: function () {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                    function (data) {
                        // console.log( JSON.stringify(data) );
                        bbm.set('seko_no_ed', data.seko_no);
                    });
        },
        sekoNoHelper: function () {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                    function (data) {
                        // console.log( JSON.stringify(data) );
                        bbm.set('moke_no', data.n_free1);
                        bbm.set('souke_nm', data.souke_nm);
                        bbm.set('m_nm', data.m_nm);
                    });
        },
        // select2設定処理
        setSelect2: function (id, select2Data) {
            var isMultiple = ($(id).attr("data-picker-param") === "multiple:true");
            if (isMultiple) {
                $.msiJqlib.setSelect2Com1(this.$(id), {data: select2Data, multiple: isMultiple, placeholder: '(未設定)'});
            } else {
                $.msiJqlib.setSelect2Com1(this.$(id), {data: select2Data});
            }
        },
        // スクロールバー表示調整
        scrollAdj: function () {
            var $list = this.$('.items .list'),
                    $header = this.$('.items .header'),
                    sc_of,
                    sc_w,
                    hh;
            if ($list.length > 0) {
                if ($list[0].scrollHeight === $list[0].clientHeight) {
                    sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                    sc_w = '44.4%';
                    $list.css("overflow-y", sc_of);
                    $header.css("overflow-y", sc_of);
                } else {
                    sc_of = 'scroll';
                    sc_w = '43.3%';
                    hh = $header.height();
                    $list.css("overflow-y", sc_of);
                    $header.css("overflow-y", sc_of);
                    $header.height(hh); // for Chrome. XXX
                }
            }
        },

        btnDisabled: function (elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },

        btnEnabled: function (elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },

        isInputOk: function () {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }
            //対象期間 （開始）
            var taisho_st_ymd = this.model.get('taisho_st_ymd');
            if(!$.msiJqlib.isNullEx2(taisho_st_ymd)){
                var new_taisho_st_ymd = this.toDoubleDigitsYMD(taisho_st_ymd);
                this.model.set("taisho_st_ymd", new_taisho_st_ymd);
            }
            //対象期間 （完了）
            var taisho_ed_ymd = this.model.get('taisho_ed_ymd');
            if(!$.msiJqlib.isNullEx2(taisho_ed_ymd)){
                var new_taisho_ed_ymd = this.toDoubleDigitsYMD(taisho_ed_ymd);
                this.model.set("taisho_ed_ymd", new_taisho_ed_ymd);
            }
            if((!$.msiJqlib.isNullEx2(new_taisho_st_ymd))&&(!$.msiJqlib.isNullEx2(new_taisho_ed_ymd)))
            if ( (new_taisho_st_ymd) >  (new_taisho_ed_ymd)) {
                    msiLib2.showErr( '日付の範囲エラーです' );
                    return;
            }

            //対象期間（開始）
            var taisho_st_ym = this.model.get('taisho_st_ym');
            if(!$.msiJqlib.isNullEx2(taisho_st_ym)){
                var new_taisho_st_ym = this.toDoubleDigitsYM(taisho_st_ym);
                this.model.set("taisho_st_ym", new_taisho_st_ym);
            }
            //対象期間（完了）
            var taisho_ed_ym = this.model.get('taisho_ed_ym');
            if(!$.msiJqlib.isNullEx2(taisho_ed_ym)){
                var new_taisho_ed_ym = this.toDoubleDigitsYM(taisho_ed_ym);
                this.model.set("taisho_ed_ym", new_taisho_ed_ym);
            }
             if((!($.msiJqlib.isNullEx2(new_taisho_st_ym))) && (!($.msiJqlib.isNullEx2(new_taisho_ed_ym)))){
                if ( new_taisho_st_ym >  new_taisho_ed_ym) {
                        msiLib2.showErr( '日付の範囲エラーです' );
                        return;
                }
             }      
            // OK
            msiLib2.clearAlert();
            return true;
        },
        toDoubleDigitsYMD: function (ymd) {
            var result = ymd.split( '/' );
            var year = result[0];
            var month = result[1];
            var day = result[2];
            // 2ケタに0詰め
            if(month.length === 1){ // 月
                var new_month = "0" + month;
            } else {
                var new_month = month;
            }
            if(day.length === 1){ // 日
                var new_day = "0" + day;
            } else {
                var new_day = day;
            }
            var new_ymd = year + "/" + new_month + "/" + new_day;
            return new_ymd;
        },
        
        toDoubleDigitsYM: function (ym) {
            var result = ym.split( '/' );
            var year = result[0];
            var month = result[1];
            // 2ケタに0詰め
            if(month.length === 1){ // 月
                var new_month = "0" + month;
            } else {
                var new_month = month;
            }
            var new_ym = year + "/" + new_month;
            return new_ym;
        },

        clearErr: function () {
            this.$el.msiErrClearAll();
        },

        // 印刷ボタン押下時
        doPrint: function (ev) {
            this.exePrint();
        },

        // CSVボタン押下時
        doCsvOut: function (ev) {
            this.exePrint(true);
        },
        
        doPrintPrev: function () {
            if (!this.isInputOk()) {
                return;
            }
            this.clearErr();
            var appObj = this.model.toJSON();
            appObj.print_kbn = 2;
            var dataAppJson = JSON.stringify(appObj, changeDataAppJson);
            var dataBumonColJson = JSON.stringify(this.getSelectedBumon());

            function changeDataAppJson(key, value) {
                if (!value)
                    return;
                var bindingsKey = bindingsKeyOf(key);
                // カンマ区切り文字列は配列に変換　※商品区分など
                if (bindingsKey && $(bindingsKey).attr("data-picker-param") && $(bindingsKey).attr("data-picker-param").search("multiple:true") >= 0) {
                    return value.split(",");
                }
                return value;
            }

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/kanri/' + this.model.get("report_cd"),
                data: {
                    dataAppJson: dataAppJson,
                    dataBumonColJson: dataBumonColJson
                }
            });
        },

        //別注品一覧表.芳名一覧表設定
        doPrintJuchu: function (csv) {
            var bbm = this.model;
            if (bbm.get('chouhyou_kbn') === "0") {
                this.model.set("report_cd", "pdf0603");
            } else {
                this.model.set("report_cd", "pdf0601");
            }
            this.exePrint();
        },

        // 印刷ボタン押下時
        doExcelPrint: function (ev) {
            if (!this.isInputOk()) {
                return;
            }
            var prnt_ymd = this.model.get("print_ymd");
            var st_ymd = this.model.get("taisho_st_ymd");
            var ed_ymd = this.model.get("taisho_ed_ymd");
            var kaisya = this.model.get("kaisya");
            var taisho_st_ym = this.model.get("taisho_st_ym");
            var tax = this.model.get("tax");
            var data_kbn = this.model.get("data_kbn");
            var mode = this.model.get('mode');
            var url = null;
            var dataBumonColJson = JSON.stringify(this.getSelectedBumon());
            if ($.msiJqlib.isNullEx2(mode)) {
                if ($.msiJqlib.isNullEx2(kaisya) || $.msiJqlib.isNullEx2(tax)) {
                    return;
                }
                url = '/kanri/excel';
            } else {
                url = '/kanri/' + this.model.get('report_cd');
            }
            msiLib2.showWarn('処理中です...  時間がかかります');
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + url,
                data: {
                    preview: 'off',
                    send: 'off',
                    prnt_kbn: '00001',
                    prnt_ymd: prnt_ymd,
                    st_ymd: st_ymd,
                    ed_ymd: ed_ymd,
                    kaisya: kaisya,
                    tax: tax,
                    taisho_st_ym:taisho_st_ym,
                    data_kbn: data_kbn,
                    dataBumonColJson: dataBumonColJson,
                    mode:mode
                }
            }, function (response) {
                msiLib2.clearAlert();
                if (response.msg != null) {
                    msiLib2.showErr(response.msg);
                }
            });
        },

        //インセンティブ計算表出力処理
        doIncentiveOut: function () {
            var prnt_ymd = this.model.get("print_ymd");
            var st_ymd = this.model.get("taisho_st_ymd");
            var ed_ymd = this.model.get("taisho_ed_ymd");
            if ($.msiJqlib.isNullEx2(st_ymd) || $.msiJqlib.isNullEx2(ed_ymd)) {
                return;
            }
            msiLib2.showWarn('処理中です...  時間がかかります');
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/kanri/incentive',
                data: {
                    preview: 'off',
                    send: 'off',
                    prnt_kbn: '00003',
                    prnt_ymd: prnt_ymd,
                    st_ymd: st_ymd,
                    ed_ymd: ed_ymd,
                }
            }, function (response) {
                msiLib2.clearAlert();
                if (response.msg != null) {
                    msiLib2.showErr(response.msg);
                }
            });
        },
        // 病院別発生状況Excel
        doByoinExcelOut: function (ev) {
            var fileKey = '1-2';
            this.exeExcelPrint(true, fileKey);
        },
        exeExcelPrint: function (excel, fileKey) {
            if (!this.isInputOk()) {
                return;
            }

            this.clearErr();

            var appObj = this.model.toJSON();
            appObj["Excel"] = excel;
            appObj["FileKey"] = fileKey;

            var dataAppJson = JSON.stringify(appObj, changeDataAppJson);
            var dataBumonColJson = JSON.stringify(this.getSelectedBumon());

            function changeDataAppJson(key, value) {
                if (!value)
                    return;

                var bindingsKey = bindingsKeyOf(key);
                // カンマ区切り文字列は配列に変換　※商品区分など
                if (bindingsKey && $(bindingsKey).attr("data-picker-param") && $(bindingsKey).attr("data-picker-param").search("multiple:true") >= 0) {
//                if (bindingsKey && $(bindingsKey).attr("data-picker-param") === "multiple:true") {
                    return value.split(",");
                }
                return value;
            }

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/kanri/' + this.model.get("report_cd"),
                data: {
                    dataAppJson: dataAppJson,
                    dataBumonColJson: dataBumonColJson
                }
            });
        },

        exePrint: function (csv) {
            if (!this.isInputOk()) {
                return;
            }

            this.clearErr();

            var appObj = this.model.toJSON();
            appObj["csv"] = csv;

            var dataAppJson = JSON.stringify(appObj, changeDataAppJson);
            var dataBumonColJson = JSON.stringify(this.getSelectedBumon());

            function changeDataAppJson(key, value) {
                if (!value)
                    return;

                var bindingsKey = bindingsKeyOf(key);
                // カンマ区切り文字列は配列に変換　※商品区分など
                if (bindingsKey && $(bindingsKey).attr("data-picker-param") && $(bindingsKey).attr("data-picker-param").search("multiple:true") >= 0) {
//                if (bindingsKey && $(bindingsKey).attr("data-picker-param") === "multiple:true") {
                    return value.split(",");
                }
                return value;
            }

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/kanri/' + this.model.get("report_cd"),
                data: {
                    dataAppJson: dataAppJson,
                    dataBumonColJson: dataBumonColJson
                }
            });
        },

        //　年月日を指定して指定した年月の初日を取得
        getFirstDay: function (dateStr) {
            var dt = new Date(dateStr);
            var year = dt.getFullYear();
            var month = dt.getMonth() + 1;
            var day = dt.getDate();
            return year + '/' + ('00' + month).slice(-2) + '/01';
        },

        //　年月日を指定して指定した年月の末日を取得
        getLastDay: function (dateStr) {
            var dt = new Date(dateStr);
            var year = dt.getFullYear();
            var month = dt.getMonth() + 1;
            dt = new Date(year, month, 0);
            var day = dt.getDate();
            return year + '/' + ('00' + month).slice(-2) + '/' + day;
        },

        getSelectedBumon: function () {
            var selectedRows = new Array();
            var i = 0;
            _.each(bumonCol.models, function (m) {
                if (m.attributes.chk_bumon == 1) {
                    selectedRows[i] = m.attributes.bumon_cd;
                    i++;
                }
            });
            return selectedRows;
        },

        // 部門 全選択ボタン押下時
        doCheckedBumon: function (ev) {
            var items = this.$('.chk_bumon').not(":checked");
            items.click(); //アイテムを全部checkedにする
        },

        // 部門 全解除ボタン押下時
        doUnCheckedBumon: function (ev) {
            var items = this.$('.chk_bumon:checked');
            items.click(); //アイテムを全部checkedにする
        },
        // 商品区分 全選択ボタン押下時
        doCheckedShohinKbn: function () {
            var kbnArray = data.select2DataAry;
            var params = '';
            _.each(kbnArray, function(arr) {          
                    if (arr.id === '#chu_bunrui') {
                        _.each(arr.data, function(v) {          
                            params += v.id + ',';
                        })
                    }
            }); 
            this.model.set('chu_bunrui', params.slice(0,-1))
            $('#s2id_chu_bunrui').onSelect();
        },

        // 商品区分 全解除ボタン押下時
        doUnCheckedShohinKbn: function () {
            this.model.set('chu_bunrui', null)
        },

        bindings: {
            '#print_ymd': 'print_ymd',
            '#taisho_st_ymd': 'taisho_st_ymd',
            '#taisho_ed_ymd': 'taisho_ed_ymd',
            '#taisho_st_ym': 'taisho_st_ym',
            '#taisho_ed_ym': 'taisho_ed_ym',
            '#kaisya': 'kaisya',
            '#tax': 'tax',
            '#data_kbn': 'data_kbn',
            '#taisho_gengo': 'taisho_gengo',
            '#taisho_ym': 'taisho_ym',
            '#moke_no': 'moke_no',
            '#souke_nm': 'souke_nm',
            '#m_nm': 'm_nm'
        },
    }); // AppView

    var _getSelect2Data = function (dataAry) {
        var select2Data = new Array();
        _.each(dataAry, function (m) {
            select2Data.push({id: m.id, text: m.text});
        });
        return select2Data;
    };

    // 値を指定してAppViewのbindingsのキーを取得
    function bindingsKeyOf(value) {
        for (var key in app.bindings) {
            if (app.bindings[key] === value) {
                return key;
            }
        }
        return null;
    }

    var bumonCol = new BumonCollection();
    var app = new AppView({model: new AppModel});
    var data;

    var _resetData = function (myApp, myCol, myValidations, myLabels, myBindings, mySel2DtAry) {
        app.model.set(myApp);
        // 対象期間の非表示チェック
        if (app.model.get("taisho_ymd").display === 'none') {
            app.model.validation["taisho_st_ymd"].required = false;
            app.model.validation["taisho_ed_ymd"].required = false;
            $('.taisho_ymd').css("display", "none");
        } else if (app.model.get("taisho_ymd").pattern === 'ym') {
            // 年月指定
            app.model.validation["taisho_st_ymd"].required = false;
            app.model.validation["taisho_ed_ymd"].required = false;
            $('.taisho_ymd').css("display", "none");
            $('.taisho_ym').css("display", "");
        } else {
            // 年月日指定
//            app.model.validation["taisho_st_ym"].required = false;
//            app.model.validation["taisho_ed_ym"].required = false;
        }
        // 部門リストの非表示チェック
        if (app.model.get("bumon").display === "none") {
            $('.sel_bumon').css("display", "none");
            $('#bumon_list').css("display", "none");
        }
        // バリデーション設定
        if (myValidations !== undefined) {
            for (var key in myValidations) {
                app.model.validation[key] = myValidations[key];
            }
        }
        // ラベル設定
        if (myLabels !== undefined) {
            for (var key in myLabels) {
                app.model.labels[key] = myLabels[key];
            }
        }
        // バインディング設定
        if (myBindings !== undefined) {
            for (var key in myBindings) {
                app.bindings[key] = myBindings[key];
            }
        }
        bumonCol.reset(myCol);
        // select2設定
        if (mySel2DtAry !== undefined) {
            for (var i = 0, len = mySel2DtAry.length; i < len; i++) {
                app.setSelect2(mySel2DtAry[i].id, _getSelect2Data(mySel2DtAry[i].data));
//                app.setSelect2(mySel2DtAry[i].id, _getSelect2Data(mySel2DtAry[i].data), mySel2DtAry[i].multiple);
            }
        }
        $(document).msiErrClearAll();
        app.model.trigger('change'); // patch
        $('#btn_csvout').hide(); 
    };

    var _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
        data = mydata;
        _resetData(mydata.dataApp, mydata.dataBumonCol, mydata.validations, mydata.labels, mydata.bindings, mydata.select2DataAry);
    };

    // リサイズ処理
    $(window).on('resize', function () {
        app.render();
    });

    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    // ラジオボタン・チェックボックスのボタン化
    $('.radio_set, .checkbox_set').buttonset();
    $('#order').fadeIn('fast'); // ちらつきのごまかし
});
