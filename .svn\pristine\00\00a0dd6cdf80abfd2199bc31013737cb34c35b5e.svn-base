<?php
/**
 * Logic_ZaikoHk_NyuShukoJzShuko
 *
 * 博全社: 受注在庫品出庫（出庫登録）
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2021/11/xx
 * @filesource 
 */

/**
 * 博全社: 受注在庫品出庫（出庫登録）
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2021/11/xx
 */
class Logic_ZaikoHk_NyuShukoJzShuko extends Logic_ZaikoHk_NyuShukoAbst
{
    /**
     * 受注在庫品 出庫登録処理
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $uri_den_no   売上伝票番号
     * @param      array      $param        設定データ (出庫倉庫、担当者、部門）
     * @return     array(boolean, string)  boolean:true(登録した)|false(何もしない) string:メッセージ
     */
    public static function update($db, $uri_den_no, $param)
    {
        $myObj = new static($db);
        $rtn = $myObj->_update($uri_den_no, $param);
        return $rtn;
    }

    /**
     * 受注在庫品 出庫登録処理
     *
     * <AUTHOR> Mihara
     * @since      2021/11/xx
     * @param      string     $uri_den_no   売上伝票番号
     * @param      array      $param        設定データ (出庫倉庫、担当者、部門）
     * @return     array(boolean, string)  boolean:true(登録した)|false(何もしない) string:メッセージ
     */
    protected function _update($uri_den_no, $param)
    {
        $db = $this->_db;
        // $this->_rtn_msg = null; // 返却メッセージ

        $p_bumon_cd = $param['bumon_cd'];
        $p_tanto_cd = $param['tanto_cd'];
        $p_soko_cd  = $param['soko_cd'];

        if ( !$db->isRefDataExists('bumon_mst', array('bumon_cd'=>$p_bumon_cd))
             || !$db->isRefDataExists('soko_mst', array('soko_cd'=>$p_soko_cd)) ) {
            $errMsg = sprintf( "倉庫(%s)/部門CD(%s)が不正です", $p_soko_cd, $p_bumon_cd );
            throw new Msi_Sys_Exception_InputException($errMsg);
        }
        $tantoRec = DataMapper_TantoMst::findOne($db, array('tanto_cd'=>$p_tanto_cd));
        if ( $tantoRec === null ) {
            $errMsg = sprintf( "担当者(%s)が不正です", $p_tanto_cd );
            throw new Msi_Sys_Exception_InputException($errMsg);
        }
        $p_tanto_nm = $tantoRec['tanto_nm'];

        $procCnt = 0;

        $cond = array( 'uri_den_no' => $uri_den_no,
                       '__raw_1'    => 'nyuko_shuko_denpyo_no IS NULL' ); // 未出庫のみ

        // (未出庫)売上伝票明細取得
        $uriDenMsies = DataMapper_ZaikoHk_JuchuZaikoShuko::find( $db, $cond );

        if ( count($uriDenMsies) <= 0 ) {
            $msg = '未出庫データはありません';
            return array( false, $msg );
        }

        $isDenpyoReg = false; // 入庫出庫伝票 作成済可否
        foreach ( $uriDenMsies as $dtl ) {

            // 入庫出庫伝票 作成
            if ( $isDenpyoReg === false ) {
                $nyuko_shuko_denpyo_no = Logic_ZaikoHk_NyuShukoUtils::getAutoNyukoShukoDenpyoNo($db);
                $recNyukoShukoDen = array( 'denpyo_no'        => $nyuko_shuko_denpyo_no,        // 伝票番号
                                           'denpyo_ymd'       => $this->getCurDate(),           // 伝票日付（出庫）
                                           'nyuko_denpyo_ymd' => null,                          // 伝票日付(入庫)
                                           'data_kbn'         => $dtl['data_kbn'],              // データ区分       1(葬儀)/2(法事)
                                           'nyuko_shuko_kbn'  => 1,                             // 入庫出庫区分  1：在庫品出庫
                                           'data_sbt'         => 0,                             // データ種別  0：売上伝票
                                           'seko_no'          => $dtl['seko_no'],               // 施行番号     0000000000
                                           'sbt_den_no'       => null,                          // 伝票番号
                                           'uriage_den_no'    => $uri_den_no,                   // 売上伝票番号
                                           'siire_den_no'     => null,                          // 仕入伝票番号
                                           'kaisya_cd'        => '00000000',                    // 会社コード
                                           'bumon_cd'         => $p_bumon_cd,                   // 部門コード
                                           'kaishu_dt'        => null,                          // 回収予定日
                                           'shuko_dt'         => $this->getCurDatetime(),       // 出庫完了日時
                                           'nyuko_dt'         => null,                          // 入庫完了日時
                                           'denpyo_biko1'     => null );                        // 伝票備考１

                list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'nyuko_shuko_denpyo', $recNyukoShukoDen );
                $cnt = $db->easyExecute($sql, $param);
                $isDenpyoReg = true;
            }

            $suryo = $dtl['juchu_suryo']; // 数量

            // 入庫出庫明細 作成
            $recNyukoShukoMsi = array( 'denpyo_no'           => $nyuko_shuko_denpyo_no,        // 伝票番号
                                       'msi_no'              => $dtl['msi_no'],                // 明細番号
                                       'juchu_den_msi_no'    => null,                          // 受注伝票明細番号
                                       'uriage_den_msi_no'   => $dtl['msi_no'],                // 売上伝票明細番号
                                       'bunkatu_no'          => 0,                             // 分割番号   0：分割なし
                                       'denpyo_kbn'          => 1,                             // 伝票区分   1:出庫
                                       'dai_bunrui_cd'       => $dtl['dai_bunrui_cd'],         // 大分類コード
                                       'chu_bunrui_cd'       => $dtl['chu_bunrui_cd'],         // 中分類コード
                                       'shohin_kbn'          => $dtl['shohin_kbn'],            // 商品区分
                                       'set_shohin_cd'       => null,                          // セット商品コード
                                       'kaisya_cd'           => '00000000',                    // 会社コード
                                       'bumon_cd'            => $dtl['shohin_bumon_cd'],       // 商品部門コード
                                       'shohin_cd'           => $dtl['shohin_cd'],             // 商品コード
                                       'shohin_nm'           => $dtl['shohin_nm'],             // 商品名
                                       'shohin_tkiyo_nm'     => $dtl['shohin_tkiyo_nm'],       // 商品摘要名
                                       'suryo'               => $suryo,                        // 数量
                                       'nyuko_suryo'         => null,                          // 入庫数量
                                       'uchiwk_shohin_suryo' => null,                          // セット内訳商品数量
                                       'tani_cd'             => $dtl['tani_cd'],               // 単位コード
                                       'tnk'                 => $dtl['uri_tnk'],               // 単価
                                       'prc'                 => $dtl['uri_prc'],               // 金額
                                       'kaishu_dt'           => null,                          // 回収予定日
                                       'kaishu_sumi_dt'      => null,                          // 回収完了日
                                       'shuko_dt'            => $this->getCurDatetime(),       // 出庫日時
                                       'nyuko_dt'            => null,                          // 入庫日時
                                       'soko_cd'             => $p_soko_cd,                    // 出庫倉庫コード
                                       'nyuko_soko_cd'       => '00000',                       // 入庫倉庫コード (null)
                                       'tanto_cd'            => $p_tanto_cd,                   // 出庫担当者コード
                                       'tanto_nm'            => $p_tanto_nm,                   // 出庫担当者
                                       'nyuko_tanto_cd'      => '0000000000',                  // 入庫担当者コード (null)
                                       'nyuko_tanto_nm'      => null,                          // 入庫担当者
                                       'shuko_sumi_kbn'      => 1,                             // 出庫済区分  1:正常出庫済み
                                       'nyuko_sumi_kbn'      => 0,                             // 入庫済区分
                                       'msi_biko1'           => null );                        // 明細備考１
            
            list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'nyuko_shuko_msi', $recNyukoShukoMsi );
            $cnt = $db->easyExecute($sql, $param);

            $procCnt += $cnt;

            // 売上伝票明細更新
            $cnt_uri = $db->easyExecute( <<< END_OF_SQL
UPDATE uriage_denpyo_msi SET v_free1 = :nyuko_shuko_denpyo_no
 WHERE uri_den_no = :uri_den_no
   AND msi_no = :msi_no
   AND delete_flg=0
END_OF_SQL
                                         , array( 'nyuko_shuko_denpyo_no' => $nyuko_shuko_denpyo_no,
                                                  'uri_den_no'            => $uri_den_no,
                                                  'msi_no'                => $dtl['msi_no'] ) );
            if ( $cnt_uri !== 1 ) {
                $errMsg = sprintf( "受注在庫品出庫:売上伝票明細(%s.%s)更新に失敗しました", $uri_den_no, $dtl['msi_no'] );
                throw new Msi_Sys_Exception_InputException($errMsg);
            }

            // 商品在庫更新
            Logic_ZaikoHk_ZaikoUpdate::pushShukoJuchuZaiko( $db, $p_soko_cd, $suryo, $dtl );
        }

        // 入出庫更新実行
        Logic_ZaikoHk_ZaikoUpdate::finish($db);

        $rtn = array( true, null );
        return $rtn;
    }

}
