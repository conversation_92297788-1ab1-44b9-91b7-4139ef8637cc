/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 *
 * @version 2016/08/09 Mihara 「その他場所」追加
 * @version 2016/12/28 Matsuyama 納品場所区分「7：寺院」を追加
 */
 var apphh = apphh || {};
$(function() {
    "use strict";
    
    
    var pro = {
        mofukudate:'.i_date',
//        kstdate:   '.i_date2',
//        keddate:   '.i_date3',
        komokku:       '.komokku2',
        siire_lnm:  '.i_hachusk',
	shohin_cd: '.i_hinmk_cmb'
    };
    
    //明細モデル（衣裳）
    var HachuModel = Backbone.Model.extend({
        defaults: function() {
            return {
                seko_no            : null,  // 施行番号
                msi_no             : 0,
                hachu_no           : 0,     // 発注管理番号
                crt_row_no         : 0,     // 行番号
                siire_cd           : null,  // 仕入先コード
                report_cd          : null,  // 帳票コード
                ha_rp_code_kbn     : null,  // 発注書区分コード区分
                ha_rp_cd           : null,  // 発注書区分コード
                ha_rp_kbn          : 0,     // 発注書区分
                ha_entry_kbn       : 0,     // 発注書入力区分
                ha_syori_kbn       : 0,     // 発注書処理区分
                ha_etc_kbn         : 0,     // 発注書その他区分
                hachu_den_kbn      : 0,     // 発注伝票区分
                dai_bunrui_cd      : null,  // 大分類コード
                chu_bunrui_cd      : null,  // 中分類コード
                
                shohin_kbn         : null,  // 商品区分
                shohin_cd          : null,  // 商品コード
                shohin_nm          : null,  // 商品名
                shohin_tkiyo_nm    : null,  // 商品摘要名
                hachu_suryo        : 0,     // 数量

//hachu_suryo_moto
                tani_cd            : 0,     // 単位コード
                hanbai_tnk         : 0,     // 販売単価
                hachu_tnk          : 0,     // 発注単価
                hachu_prc          : 0,     // 発注金額
                nebiki_prc         : 0,     // 値引額
ndate: null,     // 納品
ntime: null,     // 納品

                //SIIRE_MST 仕入先マスタ
                siire_lnm          : null,  // 仕入先名
                siire_knm          : null,  // 仕入先カナ名
                siire_snm          : null,  // 仕入先簡略名    
                delivery_kbn       : 0,     // 納品場所区分
                nonyu_cd           : null,  // 納入先コード
                nonyu_nm           : null,  // 納品場所（納入先名）
                nonyu_ymd          : $.msiJqlib.getStdDate(),  // 納品（納入）日時
                nonyu_knm          : null,
                nonyu_yubin_no     : null,
                nonyu_addr1        : null,
                nonyu_addr2        : null,
                nonyu_tel          : null,
                nonyu_fax          : null,
                nonyu_biko1        : null,
                nonyu_biko2        : null,
                chk_order_flg      : 0,     // 発注確認済み
                order_ymd          : $.msiJqlib.getStdDate(),  // 発注日時
                order_tanto_cd     : null,  // 発注担当者コード
                chk_order_ymd      : $.msiJqlib.getStdDate(),  // 発注確認日時
                chk_order_tanto_cd : null,  // 発注確認担当者コード
                hacyu_cnt          : 0,     // 発注回数
                status_kbn         : 0,     // 発注ステータス
                hachu_no_moto      : null,  // コピー元発注管理番号
                hd_biko1           : null,  // 備考１
                hd_biko2           : null,  // 備考２
                delete_flg         : 0,     // 削除フラグ
                //SHOHIN_KBN_MST 商品区分マスタ
                shohin_kbn_nm      : null,  // 商品区分名

                order_flg          : 0,     // 発注済み
                is_added           : false,  //コピー先行フラグ
                jc_denpyo_no       : 0,     // 受注伝票NO
                jc_msi_no          : 0,     // 受注明細№
                hc_denpyo_no       : 0,     // 発注伝票NO
                
                tr_kbn             : 0,
                kasi_kbn           : 1,
                fm_size_code_kbn   : 0,
                fm_size_cd         : 0,
                fm_size_kbn        : 0,
                fm_age             : 0,
                ma_height          : 0,
                ma_weight          : 0,
                ma_waist           : 0,
                ma_inseam          : 0,
                kimono_kbn         : 0,
                size_code_kbn      : 0,
                size_cd            : 0,
                size_kbn           : 0,
                set_size_code_kbn  : 0,
                set_size_cd        : 0,
                set_size_kbn       : 0,
                tabi_length        : 0,
                mofuku_nonyu_ymd   : $.msiJqlib.getStdDate(),  // 納品（納入）日時
                data_kbn           : 0,
                msi_biko           : null,
//                nonyu2_cd          : null
            };
        },
        validation: {
            siire_lnm: {
                required: true
            },
            komokku: {
                required: function () {
                    return false;
                },
                msg: '準備場所が未設定です。'
            },
            msi_biko: {
                required: false,
                maxLength: 60
            },
        },
        labels: {
            siire_lnm: '発注先',
            msi_biko: '備考'
        }
    }); // HachuModel
//    var AppModel = new HachuModel();
    
    
    //明細コレクション（貸衣装・販売品・着付け）
    var HachuCollection = Backbone.Collection.extend({
        model      : HachuModel,
        nextSeqNo: function() {
            var i = mydata.dataCol[0].next_hachu_no;
            
            mydata.dataCol[0].next_hachu_no = mydata.dataCol[0].next_hachu_no + 1;
            return i;
        },
        nextMsiNo: function() {
            var i = mydata.dataCol[0].next_msi_no;
            
            mydata.dataCol[0].next_msi_no = mydata.dataCol[0].next_msi_no + 1;
            return i;
        },
        sumKingaku : function(){
            
            var sumkingaku1 = 0;  //貸出品
            var sumkingaku2 = 0;  //販売品
            var i, max;
            var tanka,suryo;
            for ( i = 0, max=this.length ; i < max ; i++ ) {                
                tanka = this.models[i].attributes.hanbai_tnk;
                suryo = this.models[i].attributes.hachu_suryo;
                //カンマはずし
                tanka = tanka.split(",").join("");      
                suryo = suryo.split(",").join("");   
                if (this.models[i].attributes.tr_kbn==0){
                    sumkingaku1 = sumkingaku1 + tanka * suryo;
                } else {
//                    sumkingaku2 = sumkingaku2 + tanka * suryo;
                }
            }
            $("#detail").find("#undersum_kashi").val($.msiJqlib.commaAdd(sumkingaku1));    
            $("#detail").find("#undersum_han").val($.msiJqlib.commaAdd(sumkingaku2)); 
            
            return 0;
        },      
        sumKingaku_kitsuke : function(){
            
            var sumkingaku = 0;  
            var i, max;
            var tanka,suryo;
            for ( i = 0, max=this.length ; i < max ; i++ ) {                
                tanka = this.models[i].attributes.hanbai_tnk;
                suryo = this.models[i].attributes.hachu_suryo;
                //カンマはずし
                tanka = tanka.split(",").join("");      
                suryo = suryo.split(",").join("");   
                sumkingaku = sumkingaku + tanka * suryo;
            }
            $("#detail").find("#undersum_kitsuke").val($.msiJqlib.commaAdd(sumkingaku));  
            
            return 0;
        }     
    });    
    var hachuCol = new HachuCollection();    //喪服
//    var hachuCol2 = new HachuCollection();   //着付     
        
    //明細ビュー（貸衣装・販売品・着付け）
    var HachuView = Backbone.View.extend({    
        tagName   : "fieldset",
        className : "member",    
    	template  : _.template($('#item-template').html()),
    	events: {
            "click .i_chk"  : "chg_chk",
            "click .hachusk_button" : "dlghachusk",
            //"click .dlg_place"    : "dlgplace",
            "click .dlg_place" : "dlgplace",
            "click .ymdcalendar"    : "dlgdate",
            "click .dlg_time"       : "dlgtime",
            "change .komokku2"      : "setNonyuName",
//            "change .komoku1"       : "komokutrkbn", 
//            "change .komoku2"       : "komokuchange", 
//            "change .i_hinmk_cmb"   : "hinmkchange", 
//            "keypress .i_tanka, .i_amount, .agetxt "        : "preventEimoji",
//            "keypress .heighttxt,mohuku_heighttxt,daburu_heighttxt, .weighttxt, .waisttxt, .legstxt, .tabitxt"    : "preventEimoji2"  , 
//            "keyup .i_tanka, .i_amount"       : "calcSum",
            "change .heighttxt,.mohuku_heighttxt,.daburu_heighttxt .weighttxt, .waisttxt, .legstxt, .agetxt, .tabitxt, .i_date, .i_time, .size, .gosu, .komoku3"  :"titleSet"
    	},
    	bindings: {
           
	    '.i_hinmk'     : 'shohin_nm',
	    '.i_hachusk'   : 'siire_lnm',
	    '.hinmk'       : 'hachu_no',
	    '.siire_cd'    : 'siire_cd',
	    '.i_n_place'   : 'nonyu_nm',
	    '.n_def_place' : 'def_place',
	    '.i_date'      : 'ndate',
	    '.i_time'      : 'ntime',
	    '.i_tanka'    : {
                observe    : 'hanbai_tnk',
                nSet       : 'commaOmit',
                onGet      : 'commaAdd'
	    },     
	    '.i_amount'   : {
                observe    : 'hachu_suryo',
                onSet      : 'commaOmit',
                onGet      : 'commaAdd'
	    },
	    '.i_stats2'    : {
                observe    : 'order_flg',
                initialize : 'setStatus'
	    },
            'select.komokku2': {
                //observe: 'nonyu2_cd',
                observe: 'delivery_kbn',
                selectOptions: {
                    collection: function () { 
                        if(typeof apphh.delivery_kbn_arr != "undefined") {
                            return apphh.delivery_kbn_arr;
                        }
                        else { return false;}                        
                    },
                }
            },
            '.i_biko' : 'msi_biko',
            '.is_checked'   : 'is_checked'
    	},
        initialize: function () {
            this.listenTo(this.model, 'destroy', this.remove);  
            this.render();       
        },
        render : function(e){        
            var m = this.model;
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(pro, "error2"));
//            Backbone.Validation.bind(this, {
//                valid: function(view, attr, selector) {
//                    var $el = view.$('.' + attr);
//                    $el.removeClass('error1');
//                    $el.attr('title', '');
//                },
//                invalid: function(view, attr, error, selector) {
//                    var $el = view.$('.' + attr);
//                    if ($el.length) {
//                        $el.addClass('error1');
//                        $el.attr('title', error);
//                    }
//                }
//            });
        
            if(typeof this.$el !== "undefined") {
               this.$el.html(this.template(this.model.toJSON()));
            }
            
//            //納品場所
//            if(this.model.get('delivery_kbn') === '9') {
//                $(this.$el).find('.i_n_place').attr("readonly", false);
//            }
//            else{
//                $(this.$el).find('.i_n_place').attr("readonly", true);    
//            }           
            
            this.$(".i_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".i_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            //buttonsetを正しく実行ためにattrは動的に代わる
            var currAttr = $(this.$el).find(".lbl_stamp_check").attr("for");
            var newAttr = currAttr + this.model.attributes.hachu_no + '_' + this.model.attributes.msi_no;
   //         var newAttr = currAttr + this.model.attributes.hachu_no;
            $(this.$el).find(".lbl_stamp_check").attr("for", newAttr);

            $(this.$el).find(".lbl_stamp_check").next().attr("id", newAttr);
            $(this.$el).find("#hachu").attr("id", 'hachu' + this.model.attributes.hachu_no + '_' + this.model.attributes.msi_no);
            
            //End attrネーミング
            var msi_no =  this.model.attributes.msi_no;
            var listModel = this.model;
            
            //納品区分コンボボックスで動的IDを追加
            $(this.$el).find('.komokku2').attr("data-id", listModel.cid);
//            $(this.$el).find('.lbl_stamp_check').on('click', function(e) {
//            //console.log($(e.currentTarget).class("ui-state-active"));
//            if(typeof $(e.currentTarget).attr("aria-pressed") == "undefined" || $(e.currentTarget).attr("aria-pressed") == "false" ) {
//                $(e.currentTarget).next().val("1");
//                listModel.set('is_checked', 1);
//                }
//                else
//                {
//                     $(e.currentTarget).next().val("0");
//                     listModel.set('is_checked', 0);
//                }
//            });            
            
            $(this.$el).find('.i_amount').attr("id", "hachu_suryo_" + msi_no);      
            
            $(this.$el).find('#firstbox').attr("id", "firstbox" + msi_no);
            //$(this.$el).find('#secondbox').attr("id", "secondbox" + msi_no);
            $(this.$el).find('#thirdbox').attr("id", "thirdbox" + msi_no);
            $(this.$el).find('#fourthbox').attr("id", "fourthbox" + msi_no);
            $(this.$el).find('#kasi_box').attr("id", "kasi_box" + msi_no);
            $(this.$el).find('#tr_box').attr("id", "tr_box" + msi_no);
            
            $(this.$el).find('#shohin_box1').attr("id", "shohin_box1_" + msi_no);
            $(this.$el).find('#shohin_box2').attr("id", "shohin_box2_" + msi_no);  
            $(this.$el).find('#i_hinmk_cmb').attr("id", "i_hinmk_cmb_" + msi_no);                           
                                   
            //this.model.validate();
            this.stickit();
            this.calcSum(); 
            this.titleSet();
            return this;            
        },
        titleSet : function(){
/*            
            var arrayItem = ['.i_hachusk', '.tr_kbn', '.i_hinmk', '.heighttxt','.mohuku_heighttxt','.daburu_heighttxt', '.weighttxt', '.waisttxt', '.legstxt', '.agetxt', '.tabitxt',
                             '.i_tanka', '.i_amount',  '.i_date', '.i_time', '.i_stats2'];
            for(var $i=0; $i<arrayItem.length; $i++){                
                $(this.$el).find(arrayItem[$i]).attr("title", $(this.$el).find(arrayItem[$i]).val());
            }
            
            var arrayKbn1 = ['貸衣装', '販売品'];
            $(this.$el).find('.komoku1').attr("title", arrayKbn1[$(this.$el).find('.komoku1').val()]);
            
            var arrayKbn2 = ['', '喪服', '洋装', 'ダブル'];
            $(this.$el).find('.komoku2').attr("title", arrayKbn2[$(this.$el).find('.komoku2').val()]);

            var arrayKbn3 = ['袷', '絽'];
            $(this.$el).find('.komoku3').attr("title", arrayKbn3[$(this.$el).find('.komoku3').val()]);
          
            var $select = $(this.$el).find('.komokku2').val();
            //$(this.$el).find('.komokku2').attr("title", apphh.junbi_kbn_arr[$select]["label"]);  
            for(var $i=0; $i<apphh.delivery_kbn_arr.length; $i++){ 
                if (apphh.delivery_kbn_arr[$i]["value"] == $select){
                    $(this.$el).find('.komokku2').attr("title", apphh.delivery_kbn_arr[$i]["label"]);      
                }
            }                   
            
            $select = $(this.$el).find('.gosu').val();
            //$(this.$el).find('.gosu').attr("title", apphh.fmsize_kbn_arr[$select]["label"]);
            for(var $i=0; $i<apphh.fmsize_kbn_arr.length; $i++){ 
                if (apphh.fmsize_kbn_arr[$i]["value"] == $select){
                    $(this.$el).find('.gosu').attr("title", apphh.fmsize_kbn_arr[$i]["label"]);    
                }
            }              
            
            $select = $(this.$el).find('.size').val();
            //$(this.$el).find('.size').attr("title", apphh.size_kbn_arr[$select]["label"]);
            for(var $i=0; $i<apphh.size_kbn_arr.length; $i++){ 
                if (apphh.size_kbn_arr[$i]["value"] == $select){
                    $(this.$el).find('.size').attr("title", apphh.size_kbn_arr[$i]["label"]);
                }
            }
*/            
            
            return true;
        },
	unbindFromAll: function () {
            _.each(this.events, function (ev) {
            	ev = {};
            });
            this.bindings = [];
    	},
        chg_chk : function(e){
//            var bbm = this.model;
//            var chk = $(e.currentTarget).val();
//            $.msiJqlib.showInfo(chk +':'+ bbm.get('hachu_no'));  
//            _.each( hachuCol.models, function(m) {
//               if (m.attributes.hachu_no == bbm.get('hachu_no')){
//                    $('.lbl_stamp_check').addClass("ui-state-active");
//                    m.attributes.is_checked = chk;
//                }
//            });
        },
        calcSum : function() {                  
            hachuCol.sumKingaku();       
        },
        komokutrkbn : function(event){         
/*            
            var trKbn = $(event.currentTarget).val();            
            this.model.set({tr_kbn  : trKbn});
            hachuCol.sumKingaku();
            var msi_no = this.model.get('msi_no');
            var kasi_kbn = this.model.get('kasi_kbn');
            document.getElementById('firstbox'+msi_no).style.display  = "none";
            //document.getElementById('secondbox'+msi_no).style.display = "none";
            document.getElementById('thirdbox'+msi_no).style.display  = "none";
            document.getElementById('fourthbox'+msi_no).style.display = "none"; 
            document.getElementById('kasi_box'+msi_no).style.display  = "none";
            document.getElementById('tr_box'+msi_no).style.display  = "none";
            
            if (trKbn == "0"){
                document.getElementById('kasi_box'+msi_no).style.display  = ""; 
                switch (kasi_kbn){
                    case "1": document.getElementById('firstbox'+msi_no).style.display  = "";
                            break;
                    case "2": document.getElementById('firstbox'+msi_no).style.display  = "";
                            break;
                    case "3": document.getElementById('thirdbox'+msi_no).style.display  = "";
                            break;
                }
            }
            else if(trKbn == "1"){
                document.getElementById('tr_box'+msi_no).style.display = "";   
                document.getElementById('fourthbox'+msi_no).style.display = ""; 
            }
*/            
        },
        komokuchange : function(event){ 
/*            
           var selectedKbn = $(event.currentTarget).val();
            var msi_no =  this.model.attributes.msi_no;
            document.getElementById('firstbox'+msi_no).style.display  = "none";
           // document.getElementById('secondbox'+msi_no).style.display = "none";
            document.getElementById('thirdbox'+msi_no).style.display  = "none";
            
            switch (selectedKbn){
                case "1": document.getElementById('firstbox'+msi_no).style.display  = "";
                          break;
                case "2": document.getElementById('firstbox'+msi_no).style.display  = "";
                          break;
                case "3": document.getElementById('thirdbox'+msi_no).style.display  = "";
                          break;
            }
            this.titleSet();
*/            
        },
        hinmkchange : function(event){ 
            var bbm = this.model;
            var tokuten_cd = $(event.currentTarget).val();
            var tokuten_nm = '';
            for(var $i=0; $i<apphh.tokuten_data.length; $i++){ 
                if (apphh.tokuten_data[$i]["value"] == tokuten_cd){
                    tokuten_nm = apphh.tokuten_data[$i]["label"];      
                }
            }      
            bbm.set({shohin_cd  : tokuten_cd,
                     shohin_nm : tokuten_nm}); 
            //var tokuten_nm = $(event.currentTarget).text();
//            var msi_no =  this.model.attributes.msi_no;
//            document.getElementById('firstbox'+msi_no).style.display  = "none";
//            document.getElementById('secondbox'+msi_no).style.display = "none";
//            document.getElementById('thirdbox'+msi_no).style.display  = "none";
//            
//            switch (selectedKbn){
//                case "1": document.getElementById('firstbox'+msi_no).style.display  = "";
//                          break;
//                case "2": document.getElementById('secondbox'+msi_no).style.display  = "";
//                          break;
//                case "3": document.getElementById('thirdbox'+msi_no).style.display  = "";
//                          break;
//            }
//            this.titleSet();
        },
    	dlghachusk : function(event){
            var bbm = this.model;
            var hachu_kbn = this.model.get('tsujo_hachu_flg');
            if(hachu_kbn == '1'){                                  // #823 発注区分(通常発注フラグ)"0"商品のみ発注先変更可
                msiLib2.showWarn("発注先変更不可の商品です");
                return;
            }
            // #1474 発注済み商品は修正不可
            var order_flg = this.model.get('order_flg');
            if(order_flg == '1'){
                msiLib2.showWarn("発注済みの商品です");
                return;
            }
            var that = this;
            $(event.currentTarget).msiPickHelper({
                'action' : 'siire', 
                data : {dummy_flg: bbm.get('tsujo_hachu_flg')},      // 通常発注フラグ　#700
                onSelect: function(data) {
                     //$('textbox').removeAttr('readonly').val('Changed Value');
                     $(event.currentTarget).prev().val(data.siire_lnm);
                     $(event.currentTarget).next(".siire_cd").val(data.code);
                     $(event.currentTarget).next(".siire_cd").change();
                     bbm.set({siire_cd  : data.code,
                              siire_lnm : data.siire_lnm}); 
                     that.titleSet();     
                },
                onClose : function(data) { //console.log("onClose here");
                },
                hookSetData : function(data) { //console.log("hookSetData"); 
                    return {};
                }
            });    	
    	},
        dlgplace: function () {
            var that = this;
            var bbm = this.model;
            //喪服場所が「9:その他」の場合
            if ($(this.$el).find('.komokku2').val() == "9")
            {
                msiLib2.setPickHelperForm('seko.nohinbasho', this.$('.i_n_place')
                        , function () { // hookSetData
                            var p = {init_search: 1,
                                no_cond: 1,
                                s_seko_no: mydata.dataCol[0].seko_no,
                                s_basho_kbn: mydata.dataCol[0].bahso_kbn, // 9,   
                                form_data: {nonyu_cd: bbm.get('nonyu_cd'),
                                    nonyu_nm: bbm.get('nonyu_nm'),
                                    nonyu_knm: bbm.get('nonyu_knm'),
                                    nonyu_yubin_no: bbm.get('nonyu_yubin_no'),
                                    nonyu_addr1: bbm.get('nonyu_addr1'),
                                    nonyu_addr2: bbm.get('nonyu_addr2'),
                                    nonyu_tel: bbm.get('nonyu_tel'),
                                    nonyu_fax: bbm.get('nonyu_fax'),
                                    nonyu_biko1: bbm.get('nonyu_biko1'),
                                    nonyu_biko2: bbm.get('nonyu_biko2'),
                                    nonyu_delivery_kbn: 9,
                                    seko_no: mydata.dataCol[0].seko_no},
                            };
                            return p;
                        }
                , function (data) { // onUpdate
                    console.log(JSON.stringify(data));
                    var formJson = {nonyu_cd: data.nonyu_cd,
                        nonyu_nm: data.nonyu_nm,
                        nonyu_knm: data.nonyu_knm,
                        nonyu_yubin_no: data.nonyu_yubin_no,
                        nonyu_addr1: data.nonyu_addr1,
                        nonyu_addr2: data.nonyu_addr2,
                        nonyu_tel: data.nonyu_tel,
                        nonyu_fax: data.nonyu_fax,
                        nonyu_biko1: data.nonyu_biko1,
                        nonyu_biko2: data.nonyu_biko2,
                        nonyu_delivery_kbn: 9,
                        seko_no: mydata.dataCol[0].seko_no};
                    $.ajax({
                        url: $.msiJqlib.baseUrl() + '/juchu/etc/regnohin',
                        type: 'POST',
                        data: {'form_data': formJson},
                        dataType: 'json',
                        success: function (data) {
                            console.log('np2-nohin-basho-reg=> ' + JSON.stringify(data));
                            if (data.status == 'OK') {
                                that.data_set(bbm, data);
                                that.titleSet();
                            } else {
                                //    msiLib2.showErr( data.msg );
                            }
                        }
                    });
                }
                );
            }
/*            
            //「3:自社会館」の場合
            if ($(this.$el).find('.komokku2').val() == "3") {
                var n = this.model;
                var kind2 = 0;
                this.$el.msiPickHelper({
                    action: 'kaijyosiki',
                    kind2: kind2,
                    onSelect: function (data) {
                        n.set("nonyu2_nm", data.name);		//喪服場所
                    },
                    onClear: function () {
                        n.set("biko1", "");
                    }
                });
            }
*/            
        },
        data_set: function (curModel, param) {
            curModel.set({nonyu_cd: param.data.nonyu_cd,
                nonyu_nm: param.data.nonyu_nm,
                nonyu_knm: param.data.nonyu_knm,
                nonyu_yubin_no: param.data.yubin_no,
                nonyu_addr1: param.data.addr1,
                nonyu_addr2: param.data.addr2,
                nonyu_tel: param.data.tel,
                nonyu_fax: param.data.fax,
                nonyu_biko1: param.data.biko1,
                nonyu_biko2: param.data.biko2
            });
        },
    	dlgdate : function(event){
  	    $(event.currentTarget).prev().datetimepicker("show");    	
    	},
    	dlgtime : function(event){
  	    $(event.currentTarget).prev().timepicker("show");    	
    	},
        setStatus : function($el, model, options) {
            if ($el.val() == "1") {
                $el.val("○");
            } 
            else {
                $el.val("×");
            }
        },
        setZeroValue  : function($el) {
            if (($el.val() == "0.0") || ($el.val() == "0")) {  
                $el.val("");
            }  
        },
        commaOmit: function(val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        preventEimoji: function(e) {
            $.msiJqlib.preventEimoji(e);
        },
        preventEimoji2 : function(e) {
        var input = String.fromCharCode(e.charCode);
            if (input.match(/[-|.|\d]/)) {
            } else {
                e.preventDefault();
            } 
        },
        setNonyuName : function(event) {    
            var selectedKbn = $(event.currentTarget).val();
            var id = $(event.currentTarget).data("id");
            this.$('.i_n_place').attr("readonly","readonly");
            //故人宅や喪主宅の場合納品場所は空欄にする
            $.msiJqlib.clearAlert();
            if (selectedKbn == "0" || selectedKbn == "1" || selectedKbn == "2") {
                $(event.currentTarget).next().val("");
                $(event.currentTarget).next().change();
                this.nonyuclear(this.model);
                this.titleSet();
                return false;
            }
            //葬儀式場の場合
            else if (selectedKbn == "3" || selectedKbn == "4" || selectedKbn == "5" || selectedKbn == "6" ||
                     selectedKbn == "7" || selectedKbn == "8" ) { // 8(その他場所) Mihara 2016/08/09
                this.nonyuclear(this.model);
                var params = JSON.stringify({seko_no: mydata.dataCol[0].seko_no, delivery_kbn: selectedKbn});
                var $result = this.sendRequest(params, selectedKbn, $(event.currentTarget), this.model);
                //エラー時は納品場所を空欄にする
                if ($result !== "0") {
                    $(event.currentTarget).next().val("");
                    $(event.currentTarget).next().change();
                    this.nonyuclear(this.model);
                    this.titleSet();
                }
            }
            else if(selectedKbn == "9"){
                $(this.$el).find('.i_n_place').val("");
                $(this.$el).find('.i_n_place').change();
                this.nonyuclear(this.model);
                this.titleSet();
                this.$('.i_n_place').removeAttr("readonly");

            }
            else {
                $(this.$el).find('.i_n_place').val("");
                $(this.$el).find('.i_n_place').change();
                this.nonyuclear(this.model);
                this.titleSet();
            }

/*            
            //故人宅や喪主宅の場合納品場所は空欄にする
            $.msiJqlib.clearAlert();
            if(selectedKbn == "0" || selectedKbn == "1" || selectedKbn == "2" ) {
               $(event.currentTarget).next().val("");
               $(event.currentTarget).next().change();
               this.nonyuclear(this.model);
               this.titleSet();
         //      $(this.$el).find('.i_n_place').attr("readonly", true);
               return false;
            }
            //葬儀式所の場合
            else if(selectedKbn == "3" || selectedKbn == "4" || selectedKbn == "5" || selectedKbn == "6" ||
                    selectedKbn == "7" || selectedKbn == "8" ) { // 8(その他場所) Mihara 2016/08/09
                var params = JSON.stringify({seko_no : mydata.dataCol[0].seko_no, delivery_kbn : selectedKbn});
                var $result = this.sendRequest(params , selectedKbn, $(event.currentTarget), this.model);
                //エラー時は納品場所を空欄にする
                if ($result !== "0"){
                    $(event.currentTarget).next().val("");
                    $(event.currentTarget).next().change();  
                    this.nonyuclear(this.model);
                    this.titleSet();
                }
       //        $(this.$el).find('.i_n_place').attr("readonly", true);
            }
            else {
               $(event.currentTarget).next().val("");
               $(event.currentTarget).next().change()
                this.nonyuclear(this.model);
                this.titleSet();
         //       $(this.$el).find('.i_n_place').attr("readonly", false);
            }
*/            
        },
        nonyuclear : function(currentModel){
            currentModel.attributes.nonyu_cd = 0;
            currentModel.attributes.nonyu_knm = "";
            currentModel.attributes.yubin_no = "";
            currentModel.attributes.nonyu_addr1 = "";
            currentModel.attributes.nonyu_addr2 = "";
            currentModel.attributes.nonyu_tel = "";
            currentModel.attributes.nonyu_fax = "";
            currentModel.attributes.nonyu_biko1 = "";
            currentModel.attributes.nonyu_biko2 = "";  
        },
        sendRequest : function(params, selectedKbn, currentElement, currentModel) {
             var errorMsg = this.getErrorText(selectedKbn);
             var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/searchnonyuinfo',
                data: {
                    dataAppJson: params
                },
                type: 'POST',
                success: function(mydata) {
                    
                    if (mydata.status === 'OK') {
                        //取得された納入場所名所を表示
                        currentElement.next().val(mydata.nonyu_nm);
                        currentElement.next().change();
                        //選択された行のモデルデータ更新
                        currentModel.attributes.nonyu_cd = mydata.nonyu_cd;
                        currentModel.attributes.nonyu_knm = mydata.nonyu_knm;
                        currentModel.attributes.yubin_no = mydata.yubin_no;
                        currentModel.attributes.nonyu_addr1 = mydata.addr1;
                        currentModel.attributes.nonyu_addr2 = mydata.addr2;
                        currentModel.attributes.nonyu_tel = mydata.tel;
                        currentModel.attributes.nonyu_fax = mydata.fax;
                        currentModel.attributes.nonyu_biko1 = mydata.biko1;
                        currentModel.attributes.nonyu_biko2 = mydata.biko2;
                        that.titleSet();
                        return "0";
                    } else if (mydata.status === 'Error'){
                        //console.log(mydata);
                        $.msiJqlib.showWarn(errorMsg);
                        return "-1";
                    }
                    //console.log('ajax res msg==>' + data.msg);
                }
            });
        },
        getErrorText : function(selectedKbn) {
            var errorMsg = "";
            var basho_nm = "";
            for(var $i=0; $i<apphh.delivery_kbn_arr.length; $i++){ 
                if (apphh.delivery_kbn_arr[$i]["value"] == selectedKbn){
                    basho_nm = apphh.delivery_kbn_arr[$i]["label"];
                }
            }  
            
            switch(selectedKbn) {
                case "1" :
                    errorMsg = "1番納品場所区分情報エラー"; //デバッグ用設定
                    break;
                case "2" :
                    errorMsg = "2番納品場所区分情報エラー"; //デバッグ用設定
                    break;
                //case "3" :
                //    errorMsg = "葬儀式場の情報が登録されていない為、データの取得ができません。";
                //    break;
                //case "4":
                //   errorMsg = "通夜会場の情報が登録されていない為、データの取得ができません。";
                //   break;
                //case "5":
                //   errorMsg = "壇払いの情報が登録されていない為、データの取得ができません。";
                //   break;
                case "3" :
                case "4" :
                case "5" :
                case "6" :
                case "8" : // mihara add 2016/08/09
                    errorMsg = basho_nm + "の情報が登録されていない為、データの取得ができません。";
                    break;
                default :
                   errorMsg = "納品場所区分の区分値情報エラー";
                   break;
            }
            return errorMsg;
        },
    });

//***********
/*
    //明細ビュー（着付け）
    var HachuView2 = Backbone.View.extend({    
        tagName   : "fieldset",
        className : "member",    
    	template  : _.template($('#item-template2').html()),
    	events: {
            "click .hachusk_button2" : "dlghachusk",
            "click .dlg_place"    : "dlgplace",
            "click .ymdcalendar"    : "dlgdate",
            "click .dlg_time"       : "dlgtime",
            "change .komokku"       : "setNonyuName",
            "keypress .i_kitukesum, .i_people"        : "preventEimoji", 
            "change .i_date2, .i_time2, .i_date3, .i_time3, .i_kituke"  :"titleSet"
    	},
    	bindings: {
	    '.hinmk'        : 'hachu_no',
	    '.i_hachusk2'   : 'siire_lnm',
	    '.siire_cd2'    : 'siire_cd',
	    '.i_kituke'     : 'nonyu_nm',
	    '.i_date2'      : 'kstdate',
	    '.i_time2'      : 'ksttime',
	    '.i_date3'      : 'keddate',
	    '.i_time3'      : 'kedtime',
	    '.shohin_kbn'   : 'shohin_kbn_nm',
	    '.i_n_koutei'   : 'shohin_nm',
	    '.i_tekiyo'     : 'shohin_tkiyo_nm',
	    '.i_kitukesum'    : {
                observe    : 'hanbai_tnk',
                nSet       : 'commaOmit',
                onGet      : 'commaAdd'
	    },     
	    '.i_people'   : {
                observe    : 'hachu_suryo',
                onSet      : 'commaOmit',
                onGet      : 'commaAdd'
	    },
	    '.i_stats'    : {
                observe    : 'order_flg',
                initialize : 'setStatus'
	    },
            'select.komokku': {
                observe: 'delivery_kbn',
                selectOptions: {
                    collection: function () { 
                        if(typeof apphh.delivery_kbn_arr != "undefined") {
                            return apphh.delivery_kbn_arr;
                        }
                        else { return false;}                        
                    },
                }
                    }
            },
        initialize: function () {
            this.listenTo(this.model, 'destroy', this.remove);  
            this.render();       
        },
        render : function(e){        
            var m = this.model;
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(pro, "error2"));
//            Backbone.Validation.bind(this, {
//                valid: function(view, attr, selector) {
//                    var $el = view.$('.' + attr);
//                    $el.removeClass('error1');
//                    $el.attr('title', '');
//                },
//                invalid: function(view, attr, error, selector) {
//                    var $el = view.$('.' + attr);
//                    if ($el.length) {
//                        $el.addClass('error1');
//                        $el.attr('title', error);
//                    }
//                }
//            });
        
            if(typeof this.$el !== "undefined") {
               this.$el.html(this.template(this.model.toJSON()));
            }
            
//            //納品場所
//            if(this.model.get('delivery_kbn') === '9') {
//                $(this.$el).find('.i_n_place').attr("readonly", false);
//            }
//            else{
//                $(this.$el).find('.i_n_place').attr("readonly", true);    
//            }           
            
            this.$(".i_date2").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".i_time2").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$(".i_date3").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".i_time3").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            
            //buttonsetを正しく実行ためにattrは動的に代わる
            var currAttr = $(this.$el).find(".lbl_stamp_check").attr("for");
            var newAttr = currAttr + this.model.attributes.hachu_no + '_' + this.model.attributes.msi_no;
            $(this.$el).find(".lbl_stamp_check").attr("for", newAttr);
           
            $(this.$el).find(".lbl_stamp_check").next().attr("id", newAttr);
            $(this.$el).find("#hachu").attr("id", 'hachu' + this.model.attributes.hachu_no + '_' + this.model.attributes.msi_no);
            
            
            //End attrネーミング
            var msi_no =  this.model.attributes.msi_no;
            var listModel = this.model;
            
            //納品区分コンボボックスで動的IDを追加
            $(this.$el).find('.komokku').attr("data-id", listModel.cid);
            $(this.$el).find('.lbl_stamp_check').on('click', function(e) {
            //console.log($(e.currentTarget).class("ui-state-active"));
            if(typeof $(e.currentTarget).attr("aria-pressed") == "undefined" || $(e.currentTarget).attr("aria-pressed") == "false" ) {
                $(e.currentTarget).next().val("1");
                listModel.set('is_checked', 1);
                }
                else
                {
                     $(e.currentTarget).next().val("0");
                     listModel.set('is_checked', 0);
                }
            });            
            
            $(this.$el).find('.i_kitukesum').attr("id", "hachu_suryo_" + msi_no);      
            
            this.model.validate();
            this.stickit(); 
            this.calcSum_kistuke(); 
            this.titleSet();
            return this;            
        },
        titleSet : function(){
        },
	unbindFromAll: function () {
            _.each(this.events, function (ev) {
            	ev = {};
            });
            this.bindings = [];
    	},
    	dlghachusk : function(event){
            var that = this;
            $(event.currentTarget).msiPickHelper({'action' : 'siire', data : {s_siire_kbn : '9'}, onSelect: function(data) {
                     //$('textbox').removeAttr('readonly').val('Changed Value');
                     $(event.currentTarget).prev().val(data.siire_lnm);
                     $(event.currentTarget).next(".siire_cd2").val(data.code);
                     $(event.currentTarget).next(".siire_cd2").change();
                     that.titleSet();
                },
                onClose : function(data) { //console.log("onClose here");
                },
                hookSetData : function(data) { //console.log("hookSetData"); 
                    return {};
                }
            });    	
    	},
        dlgplace : function(){
            var that = this;
            var bbm = this.model;
             //着付け場所が「9:その他」の場合
            if ($(this.$el).find('.komokku').val() == "9" )
                  {
                  msiLib2.setPickHelperForm( 'seko.nohinbasho', this.$('.i_n_place')
                                       ,function() { // hookSetData
                                            var p = { init_search: 1,
                                                      no_cond : 1,
                                                      s_seko_no: mydata.dataCol[0].seko_no,
                                                      s_basho_kbn: mydata.dataCol[0].bahso_kbn, // 9,   
                                                      form_data: {nonyu_cd       : bbm.get('nonyu_cd'),
                                                                   nonyu_nm       : bbm.get('nonyu_nm'),
                                                                   nonyu_knm      : bbm.get('nonyu_knm'),
                                                                   nonyu_yubin_no : bbm.get('nonyu_yubin_no'),
                                                                   nonyu_addr1    : bbm.get('nonyu_addr1'),
                                                                   nonyu_addr2    : bbm.get('nonyu_addr2'),
                                                                   nonyu_tel      : bbm.get('nonyu_tel'),
                                                                   nonyu_fax      : bbm.get('nonyu_fax'),
                                                                   nonyu_biko1    : bbm.get('nonyu_biko1'),
                                                                   nonyu_biko2    : bbm.get('nonyu_biko2'),
                                                                   nonyu_delivery_kbn:9,
                                                                   seko_no:mydata.dataCol[0].seko_no},
                                            };
                                            return p;
                                        }
                                        ,function(data) { // onUpdate
                                            console.log( JSON.stringify(data) );
                                            var formJson = {nonyu_cd  : data.nonyu_cd,
                                                             nonyu_nm  : data.nonyu_nm,
                                                             nonyu_knm : data.nonyu_knm,
                                                             nonyu_yubin_no : data.nonyu_yubin_no,
                                                             nonyu_addr1 : data.nonyu_addr1,
                                                             nonyu_addr2 : data.nonyu_addr2,
                                                             nonyu_tel : data.nonyu_tel,
                                                             nonyu_fax : data.nonyu_fax,
                                                             nonyu_biko1 : data.nonyu_biko1,
                                                             nonyu_biko2 : data.nonyu_biko2,
                                                             nonyu_delivery_kbn : 9,
                                                             seko_no : mydata.dataCol[0].seko_no};
                                            $.ajax( {
                                                url: $.msiJqlib.baseUrl() + '/juchu/etc/regnohin',
                                                type: 'POST',
                                                data: { 'form_data': formJson },
                                                dataType: 'json',
                                                success: function(data) {
                                                    console.log( 'np2-nohin-basho-reg=> ' + JSON.stringify(data) );
                                                    if ( data.status == 'OK' ) {
                                                        that.data_set(bbm,data);
                                                        that.titleSet();
                                                    } else {
                                                    //    msiLib2.showErr( data.msg );
                                                    }
                                                }
                                            } );
                                        }
                                     );
                         }
            //「3:自社会館」の場合
            if ($(this.$el).find('.komokku').val() == "3" ){
                    var n = this.model;
                    var kind2 = 0;
    			this.$el.msiPickHelper({
    				action: 'kaijyosiki',
    				kind2: kind2,
    				onSelect: function(data) {
    					n.set("nonyu_nm", data.name);		//着付け場所
    				},
    				onClear: function() {
    					n.set("biko1", "");
    				}          
    			}); 
        }
    },
 
        data_set : function(curModel,param){
             curModel.set({nonyu_cd  : param.data.nonyu_cd,
                           nonyu_nm  : param.data.nonyu_nm,
                           nonyu_knm : param.data.nonyu_knm,
                           nonyu_yubin_no : param.data.yubin_no,
                           nonyu_addr1 : param.data.addr1,
                           nonyu_addr2 : param.data.addr2,
                           nonyu_tel   : param.data.tel,
                           nonyu_fax   : param.data.fax,
                           nonyu_biko1 : param.data.biko1,
                           nonyu_biko2 : param.data.biko2                                            
             });            
        },
    	dlgdate : function(event){
  	    $(event.currentTarget).prev().datetimepicker("show");    	
    	},
    	dlgtime : function(event){
  	    $(event.currentTarget).prev().timepicker("show");    	
    	},
        setStatus : function($el, model, options) {
            if ($el.val() == "1") {
                $el.val("○");
            } 
            else {
                $el.val("×");
            }
        },
        commaOmit: function(val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        preventEimoji: function(e) {
            $.msiJqlib.preventEimoji(e);
        },
        setNonyuName : function(event) {
            var selectedKbn = $(event.currentTarget).val();
            var id = $(event.currentTarget).data("id");
            //console.log(this.collection.get(id));
            //故人宅や喪主宅の場合納品場所は空欄にする
            $.msiJqlib.clearAlert();
            if(selectedKbn == "0" || selectedKbn == "1" || selectedKbn == "2" ) {
               $(event.currentTarget).next().val("");
               $(event.currentTarget).next().change();
               this.nonyuclear(this.model);
               this.titleSet();
         //      $(this.$el).find('.i_n_place').attr("readonly", true);
               return false;
            }
            //葬儀式所の場合
            else if(selectedKbn == "3" || selectedKbn == "4" || selectedKbn == "5" || selectedKbn == "6" ||
                    selectedKbn == "7" || selectedKbn == "8" ) { // 8(その他場所) Mihara 2016/08/09
                var params = JSON.stringify({seko_no : mydata.dataCol[0].seko_no, delivery_kbn : selectedKbn});
                var $result = this.sendRequest(params , selectedKbn, $(event.currentTarget), this.model);
                //エラー時は納品場所を空欄にする
                if ($result !== "0"){
                    $(event.currentTarget).next().val("");
                    $(event.currentTarget).next().change();  
                    this.nonyuclear(this.model);
                    this.titleSet();
                }
       //        $(this.$el).find('.i_n_place').attr("readonly", true);
            }
            else {
               $(event.currentTarget).next().val("");
               $(event.currentTarget).next().change()
                this.nonyuclear(this.model);
                this.titleSet();
         //       $(this.$el).find('.i_n_place').attr("readonly", false);
            }
        },
        nonyuclear : function(currentModel){
            currentModel.attributes.nonyu_cd = 0;
            currentModel.attributes.nonyu_knm = "";
            currentModel.attributes.yubin_no = "";
            currentModel.attributes.nonyu_addr1 = "";
            currentModel.attributes.nonyu_addr2 = "";
            currentModel.attributes.nonyu_tel = "";
            currentModel.attributes.nonyu_fax = "";
            currentModel.attributes.nonyu_biko1 = "";
            currentModel.attributes.nonyu_biko2 = "";  
        },
        sendRequest : function(params, selectedKbn, currentElement, currentModel) {
             var errorMsg = this.getErrorText(selectedKbn);
             var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/searchnonyuinfo',
                data: {
                    dataAppJson: params
                },
                type: 'POST',
                success: function(mydata) {
                    
                    if (mydata.status === 'OK') {
                        //取得された納入場所名所を表示
                        currentElement.next().val(mydata.nonyu_nm);
                        currentElement.next().change();
                        //選択された行のモデルデータ更新
                        currentModel.attributes.nonyu_cd = mydata.nonyu_cd;
                        currentModel.attributes.nonyu_knm = mydata.nonyu_knm;
                        currentModel.attributes.yubin_no = mydata.yubin_no;
                        currentModel.attributes.nonyu_addr1 = mydata.addr1;
                        currentModel.attributes.nonyu_addr2 = mydata.addr2;
                        currentModel.attributes.nonyu_tel = mydata.tel;
                        currentModel.attributes.nonyu_fax = mydata.fax;
                        currentModel.attributes.nonyu_biko1 = mydata.biko1;
                        currentModel.attributes.nonyu_biko2 = mydata.biko2;   
                        that.titleSet();
                        return "0";
                    } else if (mydata.status === 'Error'){
                        //console.log(mydata);
                        $.msiJqlib.showWarn(errorMsg);
                        return "-1";
                    }
                    //console.log('ajax res msg==>' + data.msg);
                }
            });
        },
        getErrorText : function(selectedKbn) {
            var errorMsg = "";
            var basho_nm = "";
            for(var $i=0; $i<apphh.delivery_kbn_arr.length; $i++){ 
                if (apphh.delivery_kbn_arr[$i]["value"] == selectedKbn){
                    basho_nm = apphh.delivery_kbn_arr[$i]["label"];
                }
            }  
            
            switch(selectedKbn) {
                case "1" :
                    errorMsg = "1番納品場所区分情報エラー"; //デバッグ用設定
                    break;
                case "2" :
                    errorMsg = "2番納品場所区分情報エラー"; //デバッグ用設定
                    break;
                //case "3" :
                //    errorMsg = "葬儀式場の情報が登録されていない為、データの取得ができません。";
                //    break;
                //case "4":
                //   errorMsg = "通夜会場の情報が登録されていない為、データの取得ができません。";
                //   break;
                //case "5":
                //   errorMsg = "壇払いの情報が登録されていない為、データの取得ができません。";
                //   break;
                case "3" :
                case "4" :
                case "5" :
                case "6" :
                case "8" : // mihara add 2016/08/09
                    errorMsg = basho_nm + "の情報が登録されていない為、データの取得ができません。";
                    break;
                default :
                   errorMsg = "納品場所区分の区分値情報エラー";
                   break;
            }
            return errorMsg;
        },
        calcSum_kistuke : function() {                  
//            hachuCol2.sumKingaku_kitsuke();       
        }
    });
*/                                            
//*********** 
      
    // 共通部分
    var AppView = Backbone.View.extend({
        el: "#detail",
        events: {
            "click #btn_back"    : "doBack",
            "click #btn_save"    : "doSaveAll",
            "click #btn_preview" : "doPreview",
            "click #btn_del"     : "doDelete",
            "click #btn_add"     : "addRow",
            "click #btn_order"   : "doOrder",
            "click #btn_cancel"  : "doakaCancel",
            "click #btn_cancel2" : "doakaCancel"
        },
        bindings: {

        },
        initialize: function() {                
            this.listenTo(hachuCol,  'reset', this.resetCol); 
//            this.listenTo(hachuCol2, 'reset', this.resetCol2);
        },
        render : function(){
            $("#dataContent").find('.radio_set').buttonset(); 
//            $("#dataContent2").find('.radio_set').buttonset(); 
        },
        doBack: function() {
//            location.href = $.msiJqlib.baseUrl()+'/hachu/hachushori/hachusholist/sn'+mydata2.seko_no;     
        },
        doSave: function(mode) {
            //apphh.params = JSON.stringify(this.collection.toJSON());
  
            if(!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
//            if ((hachuCol.length == 0) && (hachuCol2.length == 0)){
//                $.msiJqlib.showInfo('データがありません。');
//                return false;
//            }        
            if ((hachuCol.length == 0)){
                $.msiJqlib.showInfo('データがありません。');
                return false;
            }        
/*            
            if ( this.hachucheck()) {
                $.msiJqlib.showInfo('異なる発注先が選択されているため、保存できません。');
                return false;
            }    
            
            //if ( this.junbicheck()) {
            //    $.msiJqlib.showInfo('準備場所が選択されていないデータが存在するため、保存できません。');
            //    return false;
            //}    
            //             
            if ( this.shohincheck(0)) {
                $.msiJqlib.showWarn('商品が選択されていないデータが存在するため、保存できません。');
                return false;
            }   
*/
            
//            if ($.msiJqlib.isNullEx2($("#detail").find("#pay_type").val())) {
//                $.msiJqlib.showWarn('請求方法が選択されていません。');
//                return false;
//            }             
            
//            if ( this.nonyucheck()) {
//                $.msiJqlib.showInfo('着付場所が選択されていないデータが存在するため、保存できません。');
//                return false;
//            }     
            
            //バリデーション
            this.clearErr();
            //if ( ! this.isInputOk_all() ) {
            if ( ! this.isInputOk() ) {
                return false;
            }            
        
            //apphh.params =  this.getAllRows();
            apphh.params = this.getSelectedRows();
            // ACOS発注番号採番フラグ
            var acos_hachu = true;
            if(mode != "1" && mode != "2"&& mode != "3"){
                acos_hachu = false;
            }
            
          //  var    bikoinfodata = {biko       : $("#detail").find("#memo").val()};            
            var that = this; 
            //console.log(apphh.params); return;
            $.ajax({
                url      : $.msiJqlib.baseUrl() + '/hachu/hachushori/mohukusave',
                type     : 'POST',
                dataType : "json",
                data     : {dataAppJSON: apphh.params,
                             pay   : $("#detail").find("#pay_type").val(), acos_hachu: acos_hachu, all_row: false},
//                             bikodata   : $("#detail").find("#memo").val(),
//                             pay   : $("#detail").find("#pay_type").val()},
                success : function(respData) {
                    console.log(respData);
                    //$('#item-template').replaceWith(html);
                    /* ラジオボタン・チェックボックスのボタン化 */
                    // $('.radio_set, .checkbox_set').buttonset();
                    if(respData.status == "OK") {     
                        var sidedata = {};
                        sidedata = JSON.parse( respData.sidedata ); 
                        $.msiSideMenuLib.setSideMenu({data: sidedata});
                        
                        that.colrefreshMsi(respData.delete_nos);
                        that.colrefreshOK2();
                        if (mode == "1") {that.doPreview_Sub();}
                        else if (mode == "2") {that.doOrder_Sub();} 
                        else if (mode == "3") {that.doakaCancel_Sub();}
                        else {$.msiJqlib.showInfo(respData.msg)}  
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                    }
                }
            });                  
        },
        doSaveAll: function(mode) {
            if ((hachuCol.length == 0)){
                $.msiJqlib.showInfo('データがありません。');
                return false;
            }        
            
            //バリデーション
            this.clearErr();
            if ( ! this.isInputOk_all() ) {
                return false;
            }            
        
            apphh.params =  this.getAllRows();
            // ACOS発注番号採番フラグ
            var acos_hachu = true;
            if(mode != "1" && mode != "2"&& mode != "3"){
                acos_hachu = false;
            }
            
            var that = this; 
            //console.log(apphh.params); return;
            $.ajax({
                url      : $.msiJqlib.baseUrl() + '/hachu/hachushori/mohukusave',
                type     : 'POST',
                dataType : "json",
                data     : {dataAppJSON: apphh.params,
                             pay   : $("#detail").find("#pay_type").val(), acos_hachu: acos_hachu, all_row: true},
                success : function(respData) {
                    console.log(respData);
                    if(respData.status == "OK") {     
                        var sidedata = {};
                        sidedata = JSON.parse( respData.sidedata ); 
                        $.msiSideMenuLib.setSideMenu({data: sidedata});
                        
                        that.colrefreshMsi(respData.delete_nos);
                        that.colrefreshOK2();
                        if (mode == "1") {that.doPreview_Sub();}
                        else if (mode == "2") {that.doOrder_Sub();} 
                        else if (mode == "3") {that.doakaCancel_Sub();}
                        else {$.msiJqlib.showInfo(respData.msg)}  
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                    }
                }
            });                  
        },
        colrefreshMsi : function(data) { 
            _.each( hachuCol.models, function(m) {          
                for (var i=0 ; i< data.length;i++){
                    if ((m.attributes.hachu_no === data[i]["hachu_no_old"] ) && (m.attributes.msi_no === data[i]["msi_no"] )) {
                       m.set({hachu_no  : data[i]["hachu_no_new"],
                              jc_msi_no : data[i]["denpyo_msino_new"]}); 
                        $("#dataContent").find('#hachu' + data[i]["hachu_no_old"] + '_' + m.attributes.msi_no).attr("id", 'hachu'+data[i]["hachu_no_new"] + '_' + 'hachu'+data[i]["msi_no"]);
                    }
                }   
            }); 
        },
        hachucheck : function(){
            var cnt = 0;
            var err_flg = false;
            var last_cd  = 0;
            
            _.each( hachuCol.models, function(m) {                
                if ((last_cd != m.get('siire_cd')) && (last_cd != 0)){
                    err_flg = true;
                }
                
                last_cd = m.get('siire_cd');
                cnt++;
            });   
//            _.each( hachuCol2.models, function(m) {
//                if ((last_cd != m.get('siire_cd')) && (last_cd != 0)){
//                    err_flg = true;
//                }
//                last_cd = m.get('siire_cd');
//                cnt++;
//            });
            
            return err_flg;
        },     
        shohincheck : function(mode){
            var err_flg = false;
            _.each( hachuCol.models, function(m) {                
                if ((m.get('shohin_cd') == "0")){
                    err_flg = true;
                }
                if (mode == 1){
                    if (m.get('tokuten_kbn') == "1") {
                        for(var $i=0; $i<apphh.tokuten_data.length; $i++){ 
                            if (apphh.tokuten_data[$i]["value"] == m.get('shohin_cd')){
                                if ($i < 2){
                                    err_flg = true;    
                                }   
                            }
                        }                
                    }
                }
            });   
            return err_flg;
        },
        nonyucheck : function(){
            var err_flg = false;
 
//            _.each( hachuCol2.models, function(m) {                
//                if (m.get('data_kbn') == 4){                    
//                }else{                    
//                    if (m.get('delivery_kbn') === null || m.get('delivery_kbn') == '0') {
//                        err_flg = true;
//                    }
//                    if (m.get('delivery_kbn') == '9' && (m.get('nonyu_addr1') == null || m.get('nonyu_addr1') == '')){
//                        err_flg = true;
//                    }
//                }    
//            });
            
            return err_flg;
        },
        junbicheck : function(){
            var err_flg = false;
 
            _.each( hachuCol.models, function(m) {                
                if (m.get('data_kbn') == 4){                    
                }else{                    
                    if (m.get('nonyu_cd') === null || m.get('nonyu_cd') == '0') {
                        err_flg = true;
                    }
                }      
            });
            
            return err_flg;
        },        
        colrefreshOK2 : function() {       
            _.each( hachuCol.models, function(m) {
       //          if(m.attributes.is_checked == 1 ) {
                     m.set({is_added : false, hachu_suryo_moto:"0"})                      
       //       	 }
            });   
/*
            _.each( hachuCol2.models, function(m) {
       //          if(m.attributes.is_checked == 1 ) {
                     m.set({is_added : false, hachu_suryo_moto:"0"})                      
       //       	 }
            }); 
*/            
        },    
        doPreview : function(){
             if (this.doSave(1) === false) {
                return false; 
             }; 
        },   
        doPreview_Sub : function(){
            
            if ( this.shohincheck(1)) {
                
                //$.msiJqlib.showWarn('商品名が「特典参列者衣裳」の発注データが存在するため、発注書を表示できません。こちらの商品を、「和装」または「洋装」へ変更してください。');
                //return false;
                if (!confirm('商品名が「特典参列者衣裳」の発注データは、発注書に表示されません。\n' + 
                             '発注書に表示するには、こちらの商品を、「和装」または「洋装」へ変更する必要があります。\n'+
                             'このまま発注書を出力しますか？')) {
                    return;
                }
            }     
            
            //発注済みのデータが含まれる場合はプレビュー画面で発注不可にする
            var selectedRows =  this.getSelectedRows(true, true);
            var canOrder = true;
            var mode = 'auto'
            _.each(selectedRows, function(row) {
                 if(row.get('order_flg') === "1" || row.get('order_flg') === "○") {
                    mode = 'off'
                }  
            });   
            
            //pdf出力
            var sekoNo = mydata.dataCol[0].seko_no;
            var hachu_no = new Array();
            //var selectedRows =  this.getAllRows(true, true);
            //var selectedRows =  this.getSelectedRows(true, true);
            var tokutenflg = false;
            _.each(selectedRows, function(row) {  
                tokutenflg = false;    
                    if (row.get('tokuten_kbn') == "1") {
                        for(var $i=0; $i<apphh.tokuten_data.length; $i++){ 
                            if (apphh.tokuten_data[$i]["value"] == row.get('shohin_cd')){
                                if ($i < 2){
                                    tokutenflg = true;  
                                }   
                            }
                        }                
                    }                
                
                if (tokutenflg == false){
                    //重複する発注Noは除く
                    if (hachu_no.indexOf(row.get('hachu_no')) < 0){
                        hachu_no.push(row.get('hachu_no'));
                    }
                }
            });   
    
            var hachu_param = "";
            for (var i = 0; i < hachu_no.length; i++){
                 hachu_param = hachu_param + '&hachu_no[]=' + hachu_no[i];
            }    
            
//             $.ajax({
//                url: $.msiJqlib.baseUrl() + '/hachu/pdf/dividePerHachusho',
//                type: 'POST'
//            });
        
//            this._showOpen( '/hachu/pdf?preview=on&seko_no='+sekoNo+hachu_param+'&hachu=off'); 
            msiLib2.openWinPv(
                $.msiJqlib.baseUrl() + '/hachu/pdf?preview=on&seko_no='+sekoNo+hachu_param+'&hachu='+mode,
                {
                    p1: 'v1', // ...
                }
            );  
        },
        _showOpen: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            window.open( url ,"_blank");
        },        
        doDelete: function() {
            if( !this.checkSelected()) {
                 $.msiJqlib.showInfo("行選択されていません。");
                 return false;
            }
            //コピーされたデータ以外の場合エラーメッセージを表示
            var selectedRows =  this.getSelectedRows(true, true); 
//            var canDelete = true;
//            _.each(selectedRows, function(row) {
//                if(row.get('hachu_no_moto') === null) {
//                    $.msiJqlib.showErr("受注画面から作成された明細行は、行削除はできません。受注画面から削除してください。");
//                    canDelete = false;
//                    return false;
//                }            
//            });
//            //何もせず終了
//            if(!canDelete) {
//                return false;
//            }
            
            if (!confirm('喪服情報を削除します。よろしいですか？')) {
                return;
            }

            //配列でJSONに変換してモデルを取得             
            var paramData =  this.getSelectedRows(true);
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/hachu/hachushori/delete',
                data: {
                    dataAppJSON: JSON.stringify(paramData),
                    mode : "mofuku"
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        that.removeRows(paramData);
                        $.msiJqlib.showInfo(mydata.msg);
                        window.location.reload();
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        //一括削除
        removeRows : function(selectedRows) {
            _.each(selectedRows, function(row) {
                $("#i_chk" + row.hachu_no).parent().parent().parent().parent().remove();
            });
            //チェックの全クリア
            _.each( hachuCol.models, function(m) {
                m.attributes.is_checked = 0;                                 
            });
//            //チェックの全クリア
//            _.each( hachuCol2.models, function(m) {
//                m.attributes.is_checked = 0;                                 
//            });
        },
        addRow: function() {
            if( !this.checkSelected()) {
               	$.msiJqlib.showInfo("行選択されていません。");
               	return false;
            }
            var that = this;
            var selectedRows =  that.getSelectedRows(true, true); //console.log(selectedRows);
            var tmpModel = {};
            var totalModels = this.collection.length;
            var new_hachu_no = this.collection.nextSeqNo();
            var new_msi_no = this.collection.nextMsiNo();
            
            var selectedKbn,tr_kbn;
            _.each(selectedRows, function(row) {
                tmpModel = $.extend(true,{},row);
                if(row.get('hachu_no_moto') !== null) {
                    $.msiJqlib.showErr("行コピーで作成された明細行のため、行コピーはできません。\n受注画面から作成された元の明細行を、再度選択してください。");
                    return false;
                }                
                selectedKbn = row.get('kasi_kbn');
                tr_kbn      = row.get('tr_kbn');
                
                //console.log(tmpModel);
                tmpModel.set('hachu_no', new_hachu_no);
                tmpModel.set('msi_no', new_msi_no);  
                tmpModel.set('delivery_kbn', row.get('delivery_kbn'));
                tmpModel.set('hachu_no_moto', row.get('hachu_no'));  
                
                //行コピーマークを付く
                tmpModel.set('is_added', true);
                that.collection.add(tmpModel.attributes);
                tmpModel = that.collection.models[totalModels];

                that.addOne(tmpModel);
                tmpModel.set('hachu_suryo', "1");
                tmpModel.set('msi_no', new_msi_no);  
                tmpModel.set('crt_row_no', totalModels+1);
                tmpModel.set('order_flg', "×"); 
                tmpModel.set('is_checked', 0);
                tmpModel.set('kasi_kbn', selectedKbn); 
                tmpModel.set('tr_kbn', tr_kbn);  
                
                document.getElementById('firstbox'+new_msi_no).style.display  = "none";
                //document.getElementById('secondbox'+new_msi_no).style.display = "none";
                document.getElementById('thirdbox'+new_msi_no).style.display  = "none";
                document.getElementById('fourthbox'+new_msi_no).style.display = "none";      
                document.getElementById('kasi_box'+new_msi_no).style.display  = "none";
                document.getElementById('tr_box'+new_msi_no).style.display  = "none";
                
                if (tr_kbn == "0"){
                    document.getElementById('kasi_box'+new_msi_no).style.display = "";   
                    switch (selectedKbn){
                        case "1": document.getElementById('firstbox'+new_msi_no).style.display  = "";
                                break;
                        case "2": document.getElementById('firstbox'+new_msi_no).style.display = "";
                                break;
                        case "3": document.getElementById('thirdbox'+new_msi_no).style.display  = "";
                                break;
                    }  
                }
                else if(tr_kbn == "1"){
                    document.getElementById('tr_box'+new_msi_no).style.display = "";   
                    document.getElementById('fourthbox'+new_msi_no).style.display = ""; 
                }
                hachuCol.sumKingaku(); 
                new_hachu_no ++;
                new_msi_no ++;
                totalModels ++;
            });
            $(this.el).find('.radio_set').buttonset();              
        },
        //行コピー
        addOne : function (hachuModel_P) {
            var $div = this.$("#dataContent");
            var v = new HachuView({model : hachuModel_P});
            var domObj = v.render().el;
            $div.append(domObj);
        },
        doOrder: function() {              
             if (this.doSave(2) === false) {
                return false; 
             };         
        },
        doOrder_Sub: function() {
            //pdf出力
            var sekoNo = mydata.dataCol[0].seko_no;
            var hachu_no = new Array();
            //var selectedRows =  this.getAllRows(true, true);
            var selectedRows =  this.getSelectedRows(true, true);
            var that = this;
            var canOrder = true;
            _.each(selectedRows, function(row) {
                 if(row.get('order_flg') === "1" || row.get('order_flg') === "○") {
                    canOrder = false;
                }  
                //重複する発注Noは除く
                if (hachu_no.indexOf(row.get('hachu_no')) < 0){
                    hachu_no.push(row.get('hachu_no'));
                }
            });   
            //何もせず終了
            if(!canOrder) {
                $.msiJqlib.showInfo('発注済のデータが選択されています。');
                return false;
            }
            
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/hachu/pdf',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    hachu_no: hachu_no,
                }
            },
            function(data) {
                 if (data.status === 'OK'){                  
                     that.ordersave();
                     that.colrefreshOK();
                 }
            });             
        },
        colrefreshOK : function() {       
            _.each( hachuCol.models, function(m) {
                     m.set({order_flg  : "○"})        
                     $("#dataContent").find('#hachu' + m.attributes.hachu_no + '_' + m.attributes.msi_no).attr("title", $("#dataContent").find('#hachu' + m.attributes.hachu_no + '_' + m.attributes.msi_no).val());
                   });      
//            _.each( hachuCol2.models, function(m) {
//                     m.set({order_flg  : "○"})      
//                     $("#dataContent2").find('#hachu' + m.attributes.hachu_no + '_' + m.attributes.msi_no).attr("title", $("#dataContent2").find('#hachu' + m.attributes.hachu_no + '_' + m.attributes.msi_no).val());
//                 }); 
        },      
        ordersave : function(){
            //apphh.params =  this.getAllRows();
            apphh.params =  this.getSelectedRows();
             
            $.ajax({
                url      : $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuorder',
                type     : 'POST',
                dataType : "json",
                data     : {dataAppJSON: apphh.params},          
                success : function(respData) {
                    console.log(respData);
                    if(respData.status == "OK") {
                        $.msiJqlib.showInfo(respData.msg); 
                     //   window.location.reload();
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                    }
                }
            });         

        },
        doCancel: function() {
           if(!this.checkSelected()) {
                $.msiJqlib.showInfo('行選択されていません。');
                return false;
            }
            //バリデーション
            this.clearErr();
            if ( ! this.isInputOk() ) {
                return false;
            }
            var selectedRows =  this.getSelectedRows(true, true);
            var canOrder = true;
            _.each(selectedRows, function(row) {
                if(row.get('order_flg') === "0") {
                    canOrder = false;
                }                
            });   
            //何もせず終了
            if(!canOrder) {
                $.msiJqlib.showInfo('未発注のデータが選択されています。');
                return false;
            }
            
            apphh.params =  this.getSelectedRows();
 
            $.ajax({
                url      : $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuCancel',
                type     : 'POST',
                dataType : "json",
                data     : {dataAppJSON: apphh.params},          
                success : function(respData) {
                    console.log(respData);
                    if(respData.status == "OK") {
                        $.msiJqlib.showInfo(respData.msg); 
                  //      window.location.reload();
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                        return false;
                    }
                }
            });
        },      
       doakaCancel: function() {
            if (this.doSave(3) === false) {
               return false; 
            };
        },      
       doakaCancel_Sub: function() {
//            var selectedRows =  this.getAllRows(true, true);
//            var canOrder = true;
            var that = this;
//            _.each(selectedRows, function(row) {
//                if(row.get('order_flg') === "0" || row.get('order_flg') === "×") {
//                    canOrder = false;
//                }                
//            });   
//            //何もせず終了
//            if(!canOrder) {
//                $.msiJqlib.showInfo('未発注のデータが選択されています。');
//                return false;
//            }
            var sekoNo = mydata.dataCol[0].seko_no;
            //apphh.params =  this.getAllRows();
            apphh.params =  this.getSelectedRows();
                            
            $.ajax({
                url      : $.msiJqlib.baseUrl() + '/hachu/hachushori/hachuakacancel',
                type     : 'POST',
                dataType : "json",
                data     : {dataAppJSON: apphh.params},          
                success : function(respData) {
                    console.log(respData);
                    if(respData.status == "OK") {
                         if ((respData.akaden.length) != 0){
                            msiLib2.fileDlAjax({
                                    url: $.msiJqlib.baseUrl() + '/hachu/pdf',
                                    data: {
                                        preview: 'off',
                                        send: 'off',
                                        seko_no: sekoNo,
                                        hachu_no: respData.akaden,
                                    }
                                },
                                function(data) {
                            });                              
                         }       
                         //that.colrefreshNO(respData.cancelno);
                         that.colrefreshNO();
                         $.msiJqlib.showInfo(respData.msg);              
                    }
                    else {
                        $.msiJqlib.showErr(respData.msg);
                        return false;
                    }
                }
            });                 
        },  
        colrefreshNO : function() {       
            _.each( hachuCol.models, function(m) {
           //      if(m.attributes.is_checked == 1 ) {
           /*
                    for (var i=0 ; i< data.length;i++){
                        if (m.attributes.hachu_no === data[i]["old_hachuno"] ) {
                           m.set({order_flg : "×",
                                  hachu_no  : data[i]["new_hachuno"]}); 
                            $("#dataContent").find('#hachu' + data[i]["old_hachuno"] + '_' + m.attributes.msi_no).attr("title", $("#dataContent").find('#hachu' + data[i]["old_hachuno"] + '_' + m.attributes.msi_no).val());  
                            $("#dataContent").find('#hachu' + data[i]["old_hachuno"] + '_' + m.attributes.msi_no).attr("id", 'hachu'+data[i]["new_hachuno"] + '_' + m.attributes.msi_no);
                        }  
                        if (mydata.dataCol[0].next_hachu_no <(data[i]["new_hachuno"] +1)){
                            mydata.dataCol[0].next_hachu_no = data[i]["new_hachuno"] + 1;                                
                        }    
           //         }                            
              	 }
                 */
                m.set({order_flg : "×", order_ymd : null, status_kbn : 0}); 
                $('#i_hinmk_cmb_'+m.attributes.msi_no).attr("disabled", false)
            });     
/*
            _.each( hachuCol2.models, function(m) {
            //     if(m.attributes.is_checked == 1 ) {
                   m.set({order_flg : "×"}); 
            //  	 }
            });  
*/            
        },   
        //アプリのコレクションを設定
        resetCol: function(collection) {
            var $div = this.$("#dataContent");
            $div.find('fieldset').remove();
            var i = 1;
            _.each( collection.models, function(m) {
                var v =  new HachuView( {model : m, row : i});
                var domObj = v.render().el;
                    $div.append(domObj);
                    var komokku = v.$('.komokku2').val();
                    if(komokku === "9"){
                        v.$('.i_n_place').removeAttr("readonly");                    
                    }                    
              	    v = {};
            	    i++;
            } );
/*            
            _.each( collection.models, function(m) {
               
                var msi_no = m.get('msi_no');
                var tr_kbn = m.get('tr_kbn');
                var selectedKbn = m.get('kasi_kbn');
                var tokutenkbn = m.get('tokuten_kbn');
                var orderflg = m.get('order_flg');
                
                document.getElementById('firstbox'+msi_no).style.display  = "none";
                //document.getElementById('secondbox'+msi_no).style.display = "none";
                document.getElementById('thirdbox'+msi_no).style.display  = "none";
                document.getElementById('fourthbox'+msi_no).style.display = "none"; 
                document.getElementById('kasi_box'+msi_no).style.display  = "none";
                document.getElementById('tr_box'+msi_no).style.display = "none"; 
                
                document.getElementById('shohin_box1_'+msi_no).style.display = "none"; 
                document.getElementById('shohin_box2_'+msi_no).style.display = "none"; 
                
                
                if (tr_kbn == "0"){
                    document.getElementById('kasi_box'+msi_no).style.display  = "";
                    switch (selectedKbn){
                        case "1": document.getElementById('firstbox'+msi_no).style.display  = "";
                                break;
                        case "2": document.getElementById('firstbox'+msi_no).style.display  = "";
                                break;
                        case "3": document.getElementById('thirdbox'+msi_no).style.display  = "";
                                break;
                    }
                }
                else if(tr_kbn == "1"){
                    document.getElementById('tr_box'+msi_no).style.display = ""; 
                    document.getElementById('fourthbox'+msi_no).style.display  = "";    
                }
                
                if (tokutenkbn == "1"){
                    document.getElementById('shohin_box2_'+msi_no).style.display = "";
                    if (orderflg == 1){ 
                        $('#i_hinmk_cmb_'+msi_no).attr("disabled", true);
                    }else{
                        $('#i_hinmk_cmb_'+msi_no).attr("disabled", false)
                    }
                }else{
                    document.getElementById('shohin_box1_'+msi_no).style.display = "";    
                }
            });
            
//            $('.lbl_stamp_check').attr("aria-pressed", "true");
//            $('.lbl_stamp_check').addClass("ui-state-active");             
//            for (var loop = 0; loop < hachuCol.models.length; loop++){
//                $('#i_chk'+hachuCol.models[loop].attributes.hachu_no + '_' + hachuCol.models[loop].attributes.msi_no).click();
//                $('#i_chk'+hachuCol.models[loop].attributes.hachu_no + '_' + hachuCol.models[loop].attributes.msi_no).prev().attr("aria-pressed","true");
//                hachuCol.models[loop].attributes.is_checked =1;
//            }
*/  
            this.render();
            
        },
        //アプリのコレクションを設定
        resetCol2: function(collection) {
            var $div = this.$("#dataContent2");
            $div.find('fieldset').remove();
            var i = 1;
            _.each( collection.models, function(m) {
                var v =  new HachuView2( {model : m, row : i});
                var domObj = v.render().el;
                    $div.append(domObj);
              	    v = {};
            	    i++;
            } );
//            $('.lbl_stamp_check').attr("aria-pressed", "true");
//            $('.lbl_stamp_check').addClass("ui-state-active");  
//            for (var loop = 0; loop < hachuCol.models.length; loop++){
//                $('#i_chk'+hachuCol.models[loop].attributes.hachu_no + '_' + hachuCol.models[loop].attributes.msi_no).click();
//                $('#i_chk'+hachuCol.models[loop].attributes.hachu_no + '_' + hachuCol.models[loop].attributes.msi_no).prev().attr("aria-pressed","true");
//                hachuCol.models[loop].attributes.is_checked =1;
//            }
//            for (var loop = 0; loop < hachuCol2.models.length; loop++){
//                $('#i_chk2'+hachuCol2.models[loop].attributes.hachu_no + '_' + hachuCol2.models[loop].attributes.msi_no).click();
//                $('#i_chk2'+hachuCol2.models[loop].attributes.hachu_no + '_' + hachuCol2.models[loop].attributes.msi_no).prev().attr("aria-pressed","true");
//                hachuCol2.models[loop].attributes.is_checked =1;   
//            }

            this.render();
            
        },
        //選択された行のデータをパラメタで渡す
        getSelectedRows : function(isArray, isModel) {        
            var selectedRows = new Array();
            var i = 0;
            _.each( hachuCol.models, function(m) {
                 //モデル全体を渡す
                 if(typeof isModel != "undefined" && m.attributes.is_checked == 1 ) {
                     selectedRows[i]= m;
                     i++;
              	 }
             	 //モデルの値だけ渡す
             	 if(m.attributes.is_checked == 1 && (typeof isModel == "undefined" || !isModel)) {
                    selectedRows[i]= m.attributes;
                    i++;
             	 }
            });
/*
            _.each( hachuCol2.models, function(m) {
                 //モデル全体を渡す
                 if(typeof isModel != "undefined" && m.attributes.is_checked == 1 ) {
                     selectedRows[i]= m;
                     i++;
              	 }
             	 //モデルの値だけ渡す
             	 if(m.attributes.is_checked == 1 && (typeof isModel == "undefined" || !isModel)) {
                    selectedRows[i]= m.attributes;
                    i++;
             	 }
            });
*/            
            if(typeof isArray == "undefined" || !isArray) {
                 selectedRows = JSON.stringify(selectedRows);
            }        
            return selectedRows;
        },
        //全行のデータをパラメタで渡す
        getAllRows : function(isArray, isModel) {        
            var selectedRows = new Array();
            var i = 0;
            _.each( hachuCol.models, function(m) {
                 //モデル全体を渡す
                 if(typeof isModel != "undefined") {
                     selectedRows[i]= m;
                     i++;
              	 }
             	 //モデルの値だけ渡す
             	 if(typeof isModel == "undefined" || !isModel) {
                    selectedRows[i]= m.attributes;
                    i++;
             	 }
            });
/*            
            _.each( hachuCol2.models, function(m) {
                 //モデル全体を渡す
                 if(typeof isModel != "undefined") {
                     selectedRows[i]= m;
                     i++;
              	 }
             	 //モデルの値だけ渡す
             	 if(typeof isModel == "undefined" || !isModel) {
                    selectedRows[i]= m.attributes;
                    i++;
             	 }
            });
*/            
            if(typeof isArray == "undefined" || !isArray) {
                 selectedRows = JSON.stringify(selectedRows);
            }        
            return selectedRows;
        },
        //行選択チェック
        checkSelected : function(checkMultiple) {
            var selectedCount = 0;
            _.each( hachuCol.models, function(m) {
                if(m.attributes.is_checked == 1) {
                    selectedCount ++;
                }       

            });
/*            
            _.each( hachuCol2.models, function(m) {
                if(m.attributes.is_checked == 1) {
                    selectedCount ++;
                }       

            });
*/            
            //複数行選択チェック
            if(typeof checkMultiple !="undefined" && selectedCount > 1) {
              	$.msiJqlib.showWarn("複数行選択されています。");
               	return false;
            }
            if(selectedCount) {
            	return true;
            }
            else {
               	return false;
            }
        },
        //バリデーションを実行
        isInputOk: function() {
            this.$el.addClass('my-ctxt-show-error');

            var aMsg = [], line;

            var line = 0;
            hachuCol.each( function(m, i) {
                //選択中の行のみチェック
                if(m.attributes.is_checked == 1) {
                    var resLine = m.validate();
                    if ( resLine ) {
                        line = i + 1;
                        _.each( resLine, function(v, k) {
                            aMsg.push( '明細' + line + '行目 ' + v );
                            console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                        } );
                    }
                }
            } );
            
            line = 0;
/*            
            hachuCol2.each( function(m, i) {
                //選択中の行のみチェック
                if(m.attributes.is_checked == 1) {
                    var resLine = m.validate();
                    if ( resLine ) {
                        line = i + 1;
                        _.each( resLine, function(v, k) {
                            aMsg.push( '明細' + line + '行目 ' + v );
                            console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                        } );
                    }
                }
            } );
*/
            // NG
            if ( aMsg.length > 0 ) {
               	msiLib2.showErr( aMsg.join(', ') );
               	return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },
        //バリデーションを実行
        isInputOk_all: function() {
            this.$el.addClass('my-ctxt-show-error');

            var aMsg = [], line;

            var line = 0;
            hachuCol.each( function(m, i) {
                var resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        aMsg.push( '明細' + line + '行目 ' + v );
                        console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                    } );
                }
            } );
            
            line = 0;
/*            
            hachuCol2.each( function(m, i) {
                var resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        aMsg.push( '明細' + line + '行目 ' + v );
                        console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                    } );
                }                
            } );
*/            

            // NG
            if ( aMsg.length > 0 ) {
               	msiLib2.showErr( aMsg.join(', ') );
               	return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },
        //赤枠エラークリア
        clearErr: function () {
            //項目の赤枠をクリア
            this.$el.msiErrClearAll({errCls: 'error2'});

            //画面上部エラークリア
            msiLib2.clearAlert();
        }
    });
            

    //画面を実行
    var _resetData = function( myCol ,dataflg) {
        $("body").find(".lbl_stamp_check").unbind("click");
        $("body").find(".mycheckBox").unbind("click");
//        $("body").find("#memo").val(mydata.dataCol[0].biko);
        $("body").find("#pay_type").val(mydata.dataApp.biko);
    
        var app = new AppView({ collection: hachuCol });
        if (dataflg){
            //if (mydata.dataCol[0].data1get === true) {
                hachuCol.reset(myCol); 
            //}
//            if (mydata.dataCol[0].data2get === true) {
//                hachuCol2.reset(myCol[0].otherdata);
//            }
        }
    };

    var mydata = {};
//    var mydata2 = {};
var m_data= {};
    try {
        mydata = JSON.parse( _.unescape($('#my-data-init-id').text()) );       
//        mydata2 = JSON.parse( _.unescape($('#my-data-init-seko').text()) );
        m_data = JSON.parse( _.unescape($('#my-data-init-tokuten').text()) );
        apphh.tokuten_data = $.msiJqlib.objToArray5(m_data);
        var dataflg = true;

        if(mydata.dataCol.length > 0) {
            apphh.delivery_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].kbn_value_lnm);
//            apphh.fmsize_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].kbn_fmsize_lnm);
//            apphh.size_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].kbn_size_lnm);
//            apphh.junbi_kbn_arr = $.msiJqlib.objToArray5(mydata.dataCol[0].kbn_junbi_lnm);
           //console.log( apphh.delivery_kbn_arr);
        }
       if (typeof mydata.dataCol[0].seko_no == "undefined" || mydata.dataCol.length == 0) {
            $.msiJqlib.showInfo("データがありません。");
            dataflg = false;
        }
        _resetData( mydata.dataCol, dataflg);
    } catch (e) {
        console.log('JSON error. ' + e);
        msiLib2.showErr( 'JSON error. ' + e );
    }    
 

            
 return Backbone.Router.extend({});
});

