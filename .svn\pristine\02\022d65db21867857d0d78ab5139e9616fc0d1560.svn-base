/** 
 * @fileoverview 彩苑 会員データ取込
 */
$( function() {
    "use strict";

    var isNoChangeCheck = false; // true: isChanged() チェックをしない

    var gDataHist = []; // 取込履歴データ

    var utils = window.msiBbUtils;

    var AppModel = Backbone.Model.extend({
        defaults: function() {
            return {
                f00_file_nm: null,
                filekind: 1
            };
        },

        validation: {
            f00_file_nm: {
                required: true,
                maxLength: 64,
            },
        },
        labels: {
            f00_file_nm: 'CSVファイル名', 
        },
    }); // AppModel

    var AppView = Backbone.View.extend({

        el: '#my-form-id',

        events: {
            "click  .file-ref": "fileUpDlg",
            "click  .file_del-ref": "fileDeleteDlg",
            "click  #btn_save": "doSave",
            "click  #btn_cancel": "doCancel",
            'change input[type="file"]': "selectFile",
        },

        initialize: function() {
            Backbone.Validation.bind( this, Backbone.Validation.msi_err_setting_std() );

            this.listenTo(this.model, 'change', this.render);
            this.on('histRender', this.histRender);

            this.stickit();
            this.render();
        },

        render: function() {
            this.adjView();
            this.scrollAdj();
            return this;
        },
        
        adjView: function() {
            var bbv = this;

            var file_val = this.model.get('f00_file_nm');
            
            if (file_val) {
                this.btnEnabled('#btn_save');
            } else {
                this.btnDisabled('#btn_save', 'ファイルを選択してください');
            }
        },

        // スクロールバー表示調整
        scrollAdj: function() {
            var $list = this.$('.result-list .list'), // $('#result-area'),
                $header = this.$('.result-list .header'),
                cont_h = $('#order').height(),
                src_h = $('#input-field').height(),
                adj_h = 220, // button, etc.
                sc_of,
                hh,
                my_h;
            my_h = cont_h - src_h - adj_h;
            console.log( 'height=>' + [cont_h,src_h,adj_h].join(', ') + ' my_h=>' + my_h );

            if ( $list[0].scrollHeight === $list[0].clientHeight ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
            } else {
                sc_of = 'scroll';
                hh = $header.height();
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome. XXX
            }

            $list.height( my_h );
        },

        histRender: function() {
            var collection = gDataHist;
            var options = {};

            // msiGlobalObj.markObj.mark('resetCol Begin');
            var that = this;
            var $list = this.$('.result-list .list');
            $list.empty();
            var html = '';
            var itemTmpl = this.$('.list-item-template').html();
            if ( collection.length ) {
                html = "<table>\n";
                // var $table = $( '<table></table>' ).appendTo($list);
                _.each( collection, function(rec00) {
                    var html00 = _.template( itemTmpl, rec00 );
                    html += html00 + "\n";
                } );
                html += "</table>\n";
                $list.append( html );
            } else {
            }
            if ( collection.length <= 0 ) {
                var msg = 'データがありません',
                    msgTmpl = this.$('.list-msg-template').html(),
                    html;
                if ( _.has(options, 'msg') && _.isString(options['msg']) && options['msg'].length > 0 ) {
                    msg = options['msg'];
                }
                html = _.template( msgTmpl, {msg: msg});
                $list.append( html );
            }
            setTimeout( function() { that.render(); }, 0 );
            this.model.trigger('change');
        },

        // ボタン非活性化
        btnDisabled: function(elem, msg) {
            $(elem).attr("disabled", "disabled");
            if ( msg ) {
                $(elem).attr("title", msg);
            }
        },

        // ボタン活性化
        btnEnabled: function(elem) {
            $(elem).removeAttr("disabled").attr("title", '');
        },

        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                    // console.log( '*** err ' + k + ': ' + v );
                } );
            }

            // NG
            if ( aMsg.length > 0 ) {
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },

        clearErr: function() {
            this.$el.msiErrClearAll();
        },

        clearInput: function() {
            var bbv = this;
            this.$('.key-contains').each( function() {
                var $ele = $(this),
                    key = $ele.data('key'),
                    kind = $ele.data('kind'); // not used
                // console.log( 'clearInput key=>' + key );
                $('#input_'+key+'_file').val(null);
                $('#'+key+'_file_nm').val(null);
                bbv.model.set( key+'_file_nm', null );
            } );
            this.model.trigger('change');
            // this.render();
        },

        doSave: function(ev) {
            // console.log( 'doSave called' );
            
            var bbv = this,
                isNoCheckOpt = !!ev.shiftKey; // ラフな登録(参照データをチェックしない等)

            ev.stopImmediatePropagation();

            if ( ! this.isInputOk() ) {
                return;
            }

            this.clearErr();

            _.defer( function() { bbv._doSave(isNoCheckOpt); } , 0 );
        },

        _doSave: function(isNoCheckOpt) {
            if ( ! confirm('会員データを取り込みます.\nよろしいですか？') ) {
                return;
            }

            var bbv = this,
                formData = new FormData(),
                mykinds = [],
                cnt=0;
            // this.$('#input-fields .key-contains').each( function($el) {
            this.$('.key-contains').each( function() {
                var $ele = $(this),
                    key = $ele.data('key'),
                    kind = $ele.data('kind'), // not used
                    fileId = '#input_' + key + '_file',
                    file = $(fileId)[0].files[0];
                // console.log( '****==>' + key + ' kind=>' + kind + ' file=>' +
                //     file + ( file ? (' etc=>' + [file.name, file.size, file.type]) : '' ) );
                if ( file ) { // [object File]
                    formData.append('file_' + (++cnt), file);
                    mykinds.push( kind );
                }
            } );
            formData.append('file_cnt', cnt);
            // formData.append('file_kind', mykinds.join(','));
            formData.append('file_kind', 'ke_imp_kaiin_info');
            formData.append('is_no_check', isNoCheckOpt ? '1' : '0');

            $('#result-area').html('');

            msiLib2.showInfo2('処理中です...');
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/kanri/impkekaiin/save',
                type: 'POST',
                timeout: 1200000, // 1200 sec
                data: formData,
                processData: false,
                contentType: false,
                success: function(mydata) {
                    // console.log( '*** mydata=>' + JSON.stringify(mydata) );
                    if (mydata.status === 'OK') {
                        msiLib2.showInfo2( mydata.msg );
                        // bbv.model.set(modelKey, mydata.oid);
                    } else {
                        var msg = mydata.msg;
                        // msg = "（参考:Shiftキーを押しながら実行するとエラーチェックが緩和されます）\n" + msg;
                        msiLib2.showErr2( msg );
                    }
                    if ( mydata.detail ) {
                        $('#result-area').html('<pre>' + mydata.detail + '</pre>');
                    }
                    if ( mydata.dataHist ) {
                        gDataHist = mydata.dataHist;
                        app.trigger('histRender');
                    }
                    setTimeout( function() { app.clearInput(); }, 0 );
                    // defObj.resolve();
                }
            });
        },

        doCancel: function(ev) {
            // console.log( 'doCancel called' );

            if ( ! confirm('初期状態に戻してよろしいですか？') ) {
                return;
            }

            isNoChangeCheck = true;
            window.location.reload();
        },

        // ファイル仮選択
        selectFile: function(e) {
            var $ele = $(e.currentTarget).closest(".key-contains"),
                fileKey = $ele.data('key') + '_file';
            // console.log( '*** selectFile fileKey=>' + fileKey );
            this._selectFile( fileKey );
        },

        // ファイル仮選択 実処理
        _selectFile: function(fileIdKey, modelKey) {
            var modelKey = fileIdKey + '_nm',        // ex. f00_file_nm
                fileInputId = '#input_' + fileIdKey, // ex. #input_f00_file
//                changedKey = fileIdKey + '_changed', // ex. logo_file_changed
                fileInput = $(fileInputId)[0],
                file = fileInput.files[0],
                fileName = file.name,
                fileSize = file.size,
                fileType = file.type;

            this.model.set( modelKey, fileName );
            $('#'+modelKey).val( fileName );
//            this.model.set( changedKey, true );

            this.render();
        },

        // 初期状態から変更されているか
        isChanged: function() {
            var isChanged = false;
            this.$('.key-contains').each( function() {
                var $ele = $(this),
                    fileKey = $ele.data('key') + '_file',
                    modelKey = fileKey + '_nm',
                    fileVal = $('#'+modelKey).val();
                // console.log( ' @@@@@@@ ****==>' + modelKey + ' val=>' + fileVal );
                if ( fileVal ) {
                    isChanged = true;
                }
            } );
            return isChanged;
            /*
            if ( !orgDataApp || $.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) ) {
                return false;
            }
            return true;
             */
        },

        // 添付ファイル開くダイアログ表示処理
        fileUpDlg: function(e) {
            // console.log( 'fileUpDlg called' );
            var $target = $(e.currentTarget);
            if ($.msiJqlib.isNullEx2($target.attr("disabled"))) {
                $(e.currentTarget).parent().find('input[type="file"]').click();
            }
        },

        // ファイル削除
        fileDeleteDlg: function(e) {
            var $ele = $(e.currentTarget).closest(".key-contains"),
                key = $ele.data('key');
            // console.log( 'fileDeleteDlg key=>' + key );
            this._fileDeleteDlg(key);
        },

        // ファイル削除
        _fileDeleteDlg: function(key) {
            var m = this.model;
            $('#input_'+key+'_file').val(null);
            $('#'+key+'_file_nm').val(null);
            this.model.set( key+'_file_nm', null );
            this.render();
        },

        bindings: {
        },
    }); // AppView

    var orgDataApp; // 初期データ

    var app = new AppView( { model: new AppModel } );

    var _resetData = function( myApp, myDataHist ) {
        // myApp.f00_file_changed = false;
        app.model.set( myApp );
        orgDataApp = app.model.toJSON();
        $(document).msiErrClearAll();
        app.model.trigger('change');
        // app.adjView();

        gDataHist = myDataHist;
        app.trigger('histRender');
    };

    var _setInitData = function() {
        var mydata = msiLib2.getJsonFromHtml( $('#my-data-init-id') );
        _resetData( mydata.dataApp, mydata.dataHist );
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function() {
        if ( !isNoChangeCheck && app.isChanged() ) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on( 'resize', function() { app.render(); } );


    // 処理完了
    $.msiJqlib.initDone( function() { _setInitData(); } );

    $('#my-form-id').fadeIn('fast'); // ちらつきのごまかし
} );
