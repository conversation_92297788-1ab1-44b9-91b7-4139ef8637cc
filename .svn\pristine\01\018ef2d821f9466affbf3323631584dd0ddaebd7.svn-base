#customer-tab .off 
,#report-tab .cls-report-tab
{
    display: none;
}

#detail .lbl_ido_moto
,#detail .lbl_ido_saki
{
    text-align: center;
    width: 7%;
}

#detail .ido_moto
,#detail .ido_saki
{
    width: 22%;
    float: left;
}

#detail .cls_apply_type
,#detail .cls_funeral_type
,#detail .cls_member
,#detail .cls_main_pt
,#detail .cls_spouse
,#detail .cls_steps
,#detail .cls_copys
,#detail .cls_pickup_type
,#detail .cls_certificate
,#detail .cls_portrait
,#detail .cls_dropoff_type
,#detail .nitei_spot_cd
,#detail .cls_delivery
,#detail .cls_kaikan_use
{
    width: 10%;
    float: left;
}
#detail .cls_noukotsu
,#detail .cls_kaisouninzuu
,#detail .cls_ippansekisuu
,#detail .cls_uketsuketetsudai
,#detail .cls_tetsudaininzuu
,#detail .cls_syu<PERSON><PERSON><PERSON><PERSON>
,#detail .cls_sougeimicro
,#detail .cls_syukanmicro
,#detail .cls_syukanmicro_kasou
,#detail .cls_iei_gaku
{
    width: 13%;
    float: left;
}
#detail .cls_sinzokusekikbn
{
    width: 7%;
    float: left;
}
#detail .cls_sinzokusekisuu
{
    width: 6%;
    float: left;
}
#detail .cls_sougeimicro_kokubetsu{
    width: 12%;
    float: left;
}
#detail .cls_sougeimicro_tuya
, #detail .cls_syukanmicro_kasou{
    width: 12%;
    float: left;
}
#detail .i_usage {
    width: 9%;
    float: left;
}
#detail .cls_personal_info {
    width: 19%;
    float: left;
}
#detail .cls_birthday_era
{
    width: 8%;
    float: left;
}
#detail .cls_head_1
,#detail .cls_head_2
,#detail .cls_head_3
{
    width: 26%;
    float: left;
}
#detail .cls_employee
{
    width: 7%;
    float: left;
}
#detail .cls_funeral_style
,#detail .cls_tera_shokai
{
    width: 19%;
    float: left;
}
#detail .cls_other_entry_group
,#detail .cls_spec_agent
,#detail .cls_has_membership
,#detail .cls_has_family
,#detail .cls_account
,#detail .cls_change_account
,#detail .cls_catalog_area
,#detail .cls_catalog_item
{
    width: 20%;
    float: left;
}
#detail .cls_s_chief_relationship
,#detail .cls_s_bill_relationship
,#detail #infochief-tab .cls_birthday_era
{
    width: 12%;
    float: left;
}
#detail .cls_catalog_area 
,#detail .cls_catalog_item 
{
    width: 47%;
    float: left;

}

/* お客様基本情報の宗旨・宗派を二つに分割したため mais.cssをオーバーライド*/
#detail .cls_syushi_cd 
,#detail .cls_syuha_cd
,#detail .cls_keishiki_cd
{
    width: 14.5%;
    float: left;
}

/* お客様基本情報の互助会確認タブの加入状況の加入年月日と消費税基準日*/
#detail input.i_entry
,#detail input.i_tax {
    ime-mode: disabled;
    width: 8%;
    border-right: none;
}
#detail .i_member_name {
    width: 11%;
}
#detail #member_header ul .h_card {
    width: 4%;
}
#detail .lbl_staff {
    width: 9%;
}
#detail #staff_1 
,#detail #staff_2 {
    width: 9%;
}
#detail .cls_main_pt {
    width: 26%;
}

/* お客様基本情報の基本タブの故人名ファイル添付項目を追加のため mais.cssをオーバーライド*/
#detail .lbl_name {
    width: 12%;
}
#detail #name {
    width: 19%;
}
/*#detail .dlg_clip {
    border-left: 1px solid #CCC;
}*/

#detail #copys 
,#detail .lbl_personal_info
{
    width: 10%;
}
#detail #name_clip_link 
{
    width: 15%;
    border-right: 1px solid #CCC;
    color: #296FA7;
}
#detail #name_clip_link a 
{
    color: #296FA7;
    outline: none;
    text-decoration: none;
}

#detail #name_clip_clear 
{
    left: 50%;
    top:10px;
    position: absolute;
    height:12px;
    width:12px;
    border: none;
    background-image: url(../../../img/ico_close.png);
    background-repeat: no-repeat;
    background-size: 10px 10px;	
    background-position: 1px 1px;
    cursor: pointer;
    display:none;
}
#detail #name_clip_clear:active
, #detail #inforental-tab .delete:active 
{
    background-image: url(../../../img/ico_close_on.png);
}
#detail #name_clip_clear:hover
, #detail #inforental-tab .delete:hover 
{
    background-color: lightcoral;
    border-radius: 10px;
}
#detail #inforental-tab .delete {
    right: 6px;
    top: 8px;
    position: absolute;
    height:14px;
    width:14px;
    border: none;
    background-image: url(../../../img/ico_close.png);
    background-repeat: no-repeat;
    background-size: 10px 10px;	
    background-position: 2px 2px;
    cursor: pointer;
    z-index: 2;
}
#detail #catalog_time_from 
,#detail #catalog_time_to {
    border-right: none;
}
#detail .catalog_3 
,#detail .catalog_4 
,#detail .catalog_5 
,#detail .catalog_6 
{
    margin-bottom: 0;
}
#detail .lbl_catalog_area 
,#detail .lbl_catalog_item 
,#detail .lbl_catalog_memo
,#detail .lbl_portrait
{
    margin-left: 12%;
    width: 14%;
}

.select2-container-multi .select2-choices {
    min-height: 28px !important;
    padding-top: 2px !important;
    border-color: #CCC;
    border-left-color: #FFF;
    border-top-color: #FFF;
    background-image:none !important;
}

#detail #pickup_name 
,#detail #pickup_name2 
,#detail #dropoff_name {
    border-right: none;
    width: 34%;
}

#detail .dlg_pickup_name.disabled 
,#detail .dlg_pickup_name2.disabled 
,#detail .dlg_dropoff_name.disabled
,#detail .dlg_place.disabled 
,#detail .dlg_zip.disabled 
,#detail .dlg_staff.disabled 
,#detail .dlg_sibo_basho.disabled 
{
    background-image: url(../../../img/ico_dialog.png);
    cursor: default;
}
#detail .lbl_i_member_card {
    width: 4%;
}
#detail .place.haskbn {
    width: 26%;
}
#detail #nights 
,#detail #total_1 
{
    border-left: 1px solid #CCC;

}
#detail #nights2 {
    margin-left: 51%;
    border-left: 1px solid #CCC;
}
#detail .add_place_1 
,#detail .add_place_2 
{
    border-right: none;
}
#detail #family_name {
    width: 16%;
    border-right: none;
}
#detail #lbl_family_name2 
,#detail .lbl_name_sama 
{
    width: 3%;
    border-right: 1px solid #CCC;
    font-weight: normal;
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}
#detail #age_at_death_pref {
    width: 3.5%;
    border-right: none;
    font-size: 13px;
    font-weight: normal;
    text-align: right;
    padding-left: 0;
    padding-right: 1px;
}
#detail #age_at_death_suf {
    width: 3.5%;
    font-size: 13px;
    font-weight: normal;
    border-right: 1px solid #CCC;
    text-align: left;
    padding-left: 1px;
    padding-right: 0;
}
#detail #age_at_death {
    width: 3%;
    border-right: none;
}
#detail .lbl_zip {
    text-overflow: clip;
}
#detail #infochief-tab #memo {
    width: 50%;
    height: 32px;
    float: left;
}
#detail #infochief-tab .lbl_memo {
    float: left;
    width: 12%;
    border-bottom: 1px solid #CCC;
    border-right: 3px solid #6C0;
}
#detail .neighborhood_2 {
    margin-bottom: 0px;
}
#detail .lbl_area,
#detail .lbl_group
{
    width: 14%;
}
#detail .lbl_area {
    margin-left: 12%;
}
#detail .cls_area,
#detail .cls_group{
    width: 16.5%;
    float: left;
}
#detail #items .item .name {
    /*padding-top: 10px;*/
}
#detail #items .item .name.nm_input{
    text-align: center;
    width: 78%;
    height: 21px;
    padding: 0 0px;
    color:#296FA7;
    font-weight: normal;
    margin-left: 11%;
    margin-top: 4px;
    margin-bottom: 5px;
    border: 1px solid #FFF;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#detail #catalog_place {
    width: 37%;
}
#detail #member_header ul .h_no 
{
    width: 9%;
}
#detail .i_member_id {
    width: 9%;
}
#detail .i_apply_no 
,#detail .h_a_no 
{
    width: 4%;
}
#detail #member_header ul .h_cose {
    width: 6%;
}
#detail #member_header ul .h_name {
    width: 11%;
}
#detail #member_header ul .h_entry {
    width: 12%;
}
#detail #member_header ul .h_plan_convert {
    width: 8%;
    /*font-size: 12px;*/
    /*padding-top: 10px;*/
}
#detail #member_header ul .h_usage {
    width: 9%;
}
#detail #member_header ul .h_deposit 
,#detail #member_header ul .h_pay
,#detail #member_header ul .h_discount 
,#detail #member_header ul .h_early 
,#detail .i_deposit
,#detail .i_pay
,#detail .i_discount

,#detail .i_early
{
    width: 7%;
}

#detail .i_cose {
    width:6%;
    float: left;
}
#detail .i_entry_era {
    width:6%;
    float: left;
}
#detail input.i_entry {
    width:9%;
    border-right: 1px solid #CCC;
}
#detail .i_plan_convert 
,#detail .join_convert_cnt
{
    ime-mode: disabled;
    width: 8%;
    text-align: right;
}
#detail #doors
,#detail #persons {
    width: 7%;
    border-right: none;
    text-align: center;
}
#detail .tab li {
    /*width: 14.2857%;*/
    width: 16.6666%;
}

#detail #plan_use_prc 
,#detail #plan_change_prc
,#detail #plan_use_cnt
{
    width: 10%;
    text-align: right;
}
#detail .plan_change_info {
    margin-top: 10px;
    margin-bottom: 0;
    /*display: none;*/
}
#detail .lbl_plan_use_prc
,#detail .lbl_plan_change
,#detail .lbl_plan_change_agree
{
    width: 34%;
}
#detail .member_info {
    margin-top: 0px;
}
#detail .cls_delivery_memo_cd {
    width: 47%;
    float: left;
}

#detail #delivery_memo 
{
    margin-left: 12%;
    width: 61%;
    height: 62px;
    padding: 4px;
}
.foot-margin-bottom {
    margin-bottom: 90px !important;
}
#detail .lbl_family_tel
,#detail .lbl_keishiki_cd
,#detail .lbl_religion
,#detail .lbl_denomination
{
    width: 7%;
}
#detail .lbl_family_name_soke 
,#detail .lbl_religion
,#detail .lbl_temple
{
    width: 9%;
}
#detail #family_name_kana {
    width: 14.5%;
}
#detail #family_tel {
    width: 17%;
}
#detail #member_1 {
    padding-top: 12px;
    border-bottom: none;
}
#detail #member_2 {
    border-top: none;
}
#detail #staff_3 
,#detail #staff_4
,#detail #syodou_staff_1_1
,#detail #syodou_staff_2_1
{
    width: 38%;
}
#detail #staff_3 
{
    border-right: 1px solid #CCC;
}
#staff_3_2
, #detail #staff_4_2
,#detail #syodou_staff_1_2
,#detail #syodou_staff_2_2
{
    width: 34%;
}
#detail #syuha_knm
,#detail #syuha_knm2
,#detail #temple_knm
{
    border-right: 1px solid #ccc;
    width: 24%;
    font-weight: normal;
}

#detail #syuha_knm2 {
    display: none;
}
#detail #temple_knm
{
    width: 16.5%;
}
#detail #temple {
    width: 11.5%;
}
#detail #mobile_tel_1 
,#detail #mobile_tel_2 
{
    width: 26%;
}
#detail .lbl_finished {
    border-right: 1px solid #ccc;
}
#detail #add_unei {
    width: 16%;
    text-align: right;
}
#detail .iso_kbn
,#detail .syodou_kbn
{
    width: 10%;
    float: left;
}
#detail .file_link {
    border-right: solid 1px #ccc;
}
#detail #file_link_portrait {
    width: 34%;
}
#detail .lbl_kaikan_use {
    width: 10%;
    border-right: solid 1px #ccc;
    text-align: center;
}
/*喪主に同じ*/
/*#detail .lbl_as_address_5 
{
    padding-top: 2px;
    margin-top: 1px;
}
#detail .lbl_as_address_5_2
{
    padding-top: 2px;
    margin-top: 4px;
}
#detail .subtitle.sub2 {
    padding-top: 2px;
}*/
#detail #uketuke_date 
,#detail #pickup_date
,#detail #pickup_date2
,#detail #pickup_date3
{
    width: 13%;
    border-right: none;
}
#detail #uketuke_time 
,#detail #pickup_time
,#detail #pickup_time2
,#detail #pickup_time3
,#detail #sibo_time
{
    width: 8%;
    border-right: none;
}
#detail #uketuke_danto {
    width: 16%;
    border-right: none;
}
#detail #sibo_date{
    width: 14%;
    border-right: none;
}
#detail #uketuke_kojin_name 
,#detail #renraku_name
,#detail #renraku_name2
,#detail #renraku_name3
{
    width: 24%;
    border-right: none;
}
#detail #uketuke_kojin_kana 
{
    width: 19%;
}
#detail #u_zip_1 {
    width: 11% ;
    border-right: none;
}
#detail #u_address_1_1{
    width: 30%;
    border-right: none;
}
#detail #u_address_1_2 {
    width: 50%;
    margin-left: 12%;
}
#detail #u_tel_1 {
    width: 26%;
}
#detail .cls_sibo_basho 
,#detail .cls_renraku_zoku
{
    float: left;
    width: 10%;
}
#detail #sibo_basho_name {
    width: 28%;
    border-right: none;
}
#detail #byouto {
    width: 31%;
    border-right: none;
}
#detail .lbl_name_byouto {
    width: 5%;
}
#detail #byouto_gai {
    width: 8%;
    border-right: none;
}
#detail fieldset.base_00 {
    margin-top: 10px;
    margin-bottom: 0;
}
#detail .lbl_dropoff 
,#detail .lbl_pickup
{
    margin-left: 0;
}
#detail .lbl_renraku_zoku {
    width: 14%;
}
#detail #renraku_tel 
,#detail #renraku_tel2 
,#detail #renraku_tel3 
{
    width: 25%;
}
#detail #syuha_nm_div {
    float: left;
    width: 14.5%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#detail .cls_syuha_cd {
    width: 100%;
}
#detail #syuha_nm_other {
    display: none;
    width: 100%;
    position: absolute;
    top : 0;
    left: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#detail .h_zankin 
,#detail .h_zei_1 
,#detail .h_zei_2 
,#detail .h_mg_chg_cost 
,#detail .zankin 
,#detail .zei_1 
,#detail .zei_2 
,#detail .i_mg_chg_cost 
{
    width: 6.5%;
}

#detail .zankin 
,#detail .zei_1 
,#detail .zei_2 
,#detail .i_mg_chg_cost 
{
    text-align: right;
}
#detail .nitei_st_date {
    width:11%;
}
#detail .nitei_st_time {
    width: 15%;
}
#detail .nitei_ed_time 
,#detail .sogi_st_time 
,#detail .tuya_st_time
{
    width: 6%;
}
#detail .lbl_nitei_other {
    width: 7%;
}
#detail #misc_6 {
    border-bottom: none;
}
#detail .lbl_irai_kakunin_1 
,#detail .lbl_irai_kakunin_2 
{
    margin-left: 12%;
    width: 41%;
}
#detail .cls_irai_kakunin_1
,#detail .cls_irai_kakunin_2
{
    width: 20%;
    float: left;
}
#detail .lbl_jyokenka {
    margin-left: 12%;
    width: 14%;
}
#detail #jyokenka {
    width: 47%;
}
#detail .lbl_carn_no {
    width: 10%;
}
#detail #carn_no {
    width: 10%;
}
#detail .cls_sikijo_cd
{
    width: 10%;
    float: left;
}
#detail .sikijo_nm_other {
    width: 10%;
    display: none;
}
#detail .publish_width {
    width: 8%;
}
#detail .buttons #btn_consult {
    margin-left: 30px;
    display: none;
}
#detail .buttons #btn_seko_copy {
    width: 140px;
    display: none;
}

#detail .cls_dm_print
,#detail .cls_dm_sofu {
    width: 13%;
    float: left;
}

#detail #dm_riyu {
    width: 38%;
}
#detail #memo {
    margin-bottom: 0px;
}
#detail #kaishu_ymd {
    width: 23%;
    border-right: none;
}
#detail #kumotsu_prc {
    width: 13%;
}
#detail #kumotsu_biko {
    width: 25%;
}
#detail #kaiin_cd {
    font-size: 13px;
    width: 10%;
    border-right: 0px solid #CCC;
}
#detail #kaiin_no {
    width: 10%;
}
#detail #kaiin_nm {
    width: 15%;
}
#detail #kaiin_kbnnm {
    width: 21%;
}
#detail #chief #position {
    width: 50%;
    border-right: 1px solid #CCC;
}
#detail #bill #bill_relationship_name {
    width: 24%;
}


/*ごんきや仕様*/
/* <1>申込区分の背景色を選択値によって変更するので、ドロップダウン項目の背景色を一旦なしにする */
.select2-choice {
    background-color: transparent !important;
    background-image: none !important;
}

/* <2>画面上部の部門選択の背景色を設定（mystyles_new.cssの.select2-choiceのbackground-colorに合わせる） */
.sel-bumon-cls {
    background-color: #D4E5F4 !important;
}

/* <3>申込区分の背景色を設定（初期状態の"葬儀"は白） */
/*    ■背景画像は設定しない（背景色が生きないので） */
/* TEST
.cls_apply_type{
    background-color: #fff !important;
}
*/
/* <4>全ドロップダウン項目の背景色（白）、背景画像を設定 */
.cls_funeral_type
, .cls_personal_info
, .cls_member
, .cls_head_1
, .cls_sibo_basho
, .cls_dropoff_type
, .cls_pickup_type
, .cls_pickup_type
, .cls_renraku_zoku
, .iso_kbn cls_iso_knn1
, .iso_kbn
, .cls_spouse
, .cls_birthday_era
, .cls_head_1
, .cls_head_2
, .cls_head_3
, .cls_employee
, .cls_sibo_basho
, .cls_dropoff_type
, .cls_pickup_type
, .cls_pickup_type
, .cls_renraku_zoku
, .iso_kbn cls_iso_knn1
, .iso_kbn
, .syodou_kbn cls_syodou_knn1
, .syodou_kbn
, .cls_keishiki_cd
, .cls_funeral_style
, .cls_syushi_cd
, .cls_syuha_cd
, .cls_tera_shokai
, .cls_s_chief_relationship
, .cls_s_chief_relationship
, .cls_birthday_era
, .cls_employee
, .cls_s_bill_relationship
, .cls_kaikan_use
, .cls_steps
, .cls_copys
, .cls_certificate
, .cls_portrait
, .cls_area
, .cls_group
, .cls_catalog_area
, .cls_catalog_item
, .cls_delivery
, .cls_delivery_memo_cd
, .nitei_spot_cd
, .kaso_kyo
, .shishikisha_ninzu
, .nanoka

, .cls_makura_hana_um
, .cls_makura_hana_tehai
, .cls_rin
, .cls_dango
, .cls_kityugami
, .cls_yoyaku_umu
, .cls_nitei_sogi_basho_kbn
, .cls_seko_basho
, .cls_douyi_kakunin
, .cls_douyi_kakunin2
, .cls_syasin_kakunin
, .cls_yukan_kakunin
, .cls_dry_hour
, .cls_itai_eisei1
, .cls_danbarai_umu1
, .cls_danbarai_umu2
, .cls_haka_umu1
, .cls_haka_umu2
, .cls_haka_umu3
, .cls_haka_umu4
, .cls_renraku_houhou
, #customerinfo-form-id .select2-container
, .cls_noukotsu
, .cls_kaisouninzuu
, .cls_sinzokusekisuu
, .cls_ippansekisuu
, .cls_uketsuketetsudai
, .cls_tetsudaininzuu
, .cls_syukuhakusya
, .cls_sougeimicro
, .cls_sougeimicro_tuya
, .cls_sougeimicro_kokubetsu
, .cls_syukanmicro
, .cls_syukanmicro_kasou
{
    background-color: #fff !important;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff));
}

/*TEST*/
.msi-picker
{
    background-color: #fff !important;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff));
}

#detail .lbl_pacemaker_1 
,#detail .lbl_pacemaker_2 
{
    width: 13%;
    border-right: 1px solid #ccc;
}

#detail #todoke_name {
    width: 15%;
    border-right: none;
}
#detail #todoke_kana {
    width: 18%;
}
#detail #todoke_birthday_date 
{
    width: 10%;
}
#detail #todoke_age {
    width: 12%;
}
#detail .cls_todoke_relationship 
,#detail .cls_todoke_birthday_era
{
    width : 10%;
    float :left;
}
#detail #todoke_zip_1 
,#detail #todoke_zip_3
{
    width: 11% !important;
    border-right: none;
}
#detail #todoke_address_1_1 
,#detail #todoke_address_3_1 
{
    width: 30%;
    border-right: none;
}
#detail .address_4 {
    margin-bottom: 2px;
}
#detail #todoke_tel
,#detail #todoke_mobile_tel 
{
    width: 26%;
}

#detail #todoke_address_3_1  {
    width: 30%;
}
/*#detail .cls_spouse {
    width: 8.5%;
}*/
#detail .lbl_head_change_2 
,#detail .lbl_kasoba_hikae_2 
{
    border-right: 1px solid #ccc;
}
#detail .cls_head_new {
    width : 20%;
    float :left;
}
#detail .lbl_kasoba_hikae_1 
,#detail .lbl_kasoba_hikae_2
{
    width: 10%;
}
#detail .cls_todoke_kankei {
    width : 17%;
    float :left;
}
#detail #todoke_kankei_other {
    width: 33%;
}
#detail #todoke_head_nm {
    width: 26%;
}
/*#detail .cls_member {
    width: 30%;
}*/
#detail #staff_3.cls_hanso 
,#detail #staff_4.cls_hanso 
{
    width: 28%;
}
#staff_3_2.cls_hanso
,#detail #staff_4_2.cls_hanso 
{
    width: 19%;
}
#detail .lbl_hanso_km 
{
    width: 10%;
}
#detail .lbl_bochi_nm{
    width: 9%;
}
#detail #hanso_km 
,#detail #bochi_nm
{
    width: 15%;
}
#detail .makerWid{
    width: 12%;
}
#detail #btn_copy {
    width: 180px;
    height: 22px;
    font-weight: bold;
    padding-top: 1px;
    color: #BBB;
    letter-spacing: 0.1em;
    text-indent: 15px;
    background: none;
    border: none;
    border-radius: 3px;
    box-shadow: 1px 1px 0 #FFF;
    margin: 7px 5px 0 8px;
    background-color: #FFF;
    font-size: 12px;
}
#detail #btn_copy:active {
    background-color: #369;
    border: 1px solid #FFF;
    box-shadow: none;
    box-shadow: inset 1px 1px 1px rgba(0,0,0,0.2);
    color: #FFF;
}
#detail #chief h3 {
    float: left;
}
#detail .lbl_main_pt {
    width: 10%;
}

#detail #copy_moto_seko_no,
#detail #copy_saki_seko_no {
    width: 17%;
}
#detail .misc input.width20 {
    width: 20%;
}
#detail .misc input.width10,
#detail .misc label.width10{
    width: 10%;
}
#detail .lbl_select_kbn {
    width: 10%;
}
#detail .misc .lbl_bus {
    padding-top: 25px;
    height: 64px;
}
#detail .misc #bus_toujitu_daisu {
    margin-right: 25%;
}
#detail #uketsukekbn
, #detail #sougeimicro_tuya_jikan
, #detail #haikei
, #detail #kigae
{
    width: 13%;
}
#detail #bosho
, #detail #sekizaiten
, #detail #kamon{
    width: 10%;
}
#detail #lbl_dlg_crest{
    height: 32px;
}
#detail #temple_tel{
    width: 13%;
}
#detail #tuya_nin
,#detail #sougi_nin{
    width: 5%;
    border-right: none;
}
#detail #syukanmicro_sonota{
    width: 25%;
}
#detail #careful_memo
, #detail #tetsudaichuijiko{
    width: 63%;
}
#detail #sougeimicro_tuya_jikan
, #detail #sougeimicro_kokubetsu_jikan
, #detail #syukanmicro_kasou_jikan{
    width: 10%;
}
#detail .w16{
    width: 16% !important;
}
#detail #misc_7 {
    border-top: none;
    border-bottom: none;
}
#detail #misc_7 .catalog_1 {
    margin-top: 30px;
    margin-bottom: 10px;
}
#detail #misc_7 .lbl_kaisou_ari
, #detail #misc_7 .lbl_kaisou_nasi{
    width: 9%;
    line-height: 1;
    margin: 0;
    border-right: none;
    background-color: #FAFAFA;
    background-image: url(../../../img/check_2.png);
    background-repeat: no-repeat;
    background-position: 10% center;
    background-size: 16px 12px;
    border: none;
    border-bottom: 1px solid #CCC;
    border-radius: 0;
    text-align: center;
    color: #999;
}
#detail #misc_7 .ui-state-active{
    display: block;
    background-color: #6CACD7;
    background-image: url(../../../img/check_1.png);
    text-shadow: -1px -1px 0 #69c;
    color: #FFF;
}
#detail #crest_pic_cloth img{
    width: 64px;
    height: 64px;
}