<?php
  /**
   * DataMapper_SekyuExData
   *
   * 請求情報 データマッパークラス
   *
   * @deprecated
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   * @filesource 
   */

  /**
   * 請求情報 データマッパークラス
   * 
   * @deprecated
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2014/xx/xx
   */
class DataMapper_SekyuExData extends DataMapper_Abstract
{
    /**
     * 請求情報を検索する
     * cf. DataMapper_UriageDenpyo
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
	 * @version 2014/07/26 消費税差額を加算するように修正　Kayo
     * @version 2015/03/03 入金状況のステータスを修正　Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array(), $isDateEffective=false)
    {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seikyu_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
    SELECT
        u.uri_den_no AS seikyu_no	-- 売上伝票№
        ,u.denpyo_no				-- 受注伝票№
        ,u.kaisya_cd                -- 会社コード   2017/03/07 ADD Kayo        
        ,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD') AS	juchu_ymd		-- 売上日
        ,u.data_kbn					-- データ区分
        ,k.free7_kbn                                    -- 法事申込区分（法事　催事）
        -- ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=u.data_kbn AND code_kbn='0920'
        -- AND delete_flg=0 limit 1) as data_kbn_nm -- データ区分名
        ,CASE u.data_kbn WHEN 1 THEN '葬儀'
            WHEN 2 THEN 
                (CASE k.free7_kbn WHEN 8 THEN '新盆'
                                  ELSE '法事' 
                 END)
            WHEN 3 THEN '単品'
            WHEN 4 THEN '別注品'
            WHEN 5 THEN '生前依頼'
            WHEN 6 THEN 'その他施行'
            ELSE '?' END AS data_kbn_nm -- データ区分名
        ,u.seko_no					-- 施行番号
        ,u.seko_no_sub				-- 施行番号（枝番）
        ,u.bumon_cd					-- 売上部門コード
        ,b.bumon_lnm				-- 売上部門名
        ,b.bumon_lnm AS bumon_nm
        ,b.bumon_snm				-- 売上部門名（簡略）
        ,u.tanto_cd					-- 担当者コード
        ,tt.tanto_nm				-- 担当者名		
        ,u.gojokai_cose_cd			-- 互助会コースコード
        ,u.seko_plan_cd				-- 施行プランコード
        ,TO_CHAR(u.seikyu_ymd,'YYYY/MM/DD')		AS	seikyu_ymd		-- 請求日
        ,TO_CHAR(u.zen_seikyu_ymd,'YYYY/MM/DD') AS 	zen_seikyu_ymd	-- 前回請求日
        ,u.sekkyu_kaisu				-- 請求回数
        ,CASE 
            WHEN u.pay_method_cd IN ('4', '5', '6') THEN 
                pay.kbn_value_lnm
            ELSE
                CASE 
                    WHEN u.sekkyu_kaisu > 0 THEN '発行済み'
                    ELSE '未発行' 
                END
        END AS issue_st_nm -- 請求状況
        ,CASE 
            WHEN u.pay_method_cd IN ('4', '5', '6') THEN 
                '－'
            ELSE
                CASE 
                    WHEN u.seikyu_zan = 0 THEN '入金済み'                         -- 2015/03/03 UPD Kayo
                    WHEN u.nyukin_prc - u.sougi_keiyaku_prc > 0  THEN '一部入金'  -- 2015/03/03 UPD Kayo
                    ELSE '未入金' 
                END
        END AS nyukin_st_nm                          -- 入金状況
        ,CASE WHEN u.shonin_dt1 IS NULL THEN '未承認'
            WHEN u.shonin_dt2 IS NULL THEN '事務承認'
            ELSE '上長承認' END AS shonin_st_nm  -- 承認区分
        ,ARRAY_TO_STRING(ARRAY(SELECT nafuda_nm FROM uriage_denpyo_msi udm
                                    WHERE udm.uri_den_no=u.uri_den_no AND udm.delete_flg=0
                                    ORDER BY disp_no, msi_no), '、')           AS nafuda -- 名札
        ,u.kaishu_kbn				-- 回収区分
        ,TO_CHAR(u.kaishu_ymd,'YYYY/MM/DD')		AS	kaishu_ymd		-- 回収予定日
        ,u.nyukin_prc				-- 入金金額
        ,u.seikyu_zan - u.sougi_harai_prc AS seikyu_zan -- 請求残高
        ,u.sekyu_cd					-- 請求先コード
        ,u.sekyu_nm					-- 請求先名
        ,u.sekyu_knm				-- 請求先名カナ
        ,u.sekyu_soufu_nm			-- 請求書送付先名
        ,u.sekyu_yubin_no			-- 請求先郵便番号
        ,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'')	AS seikyu_addr	-- 請求先住所
        ,u.sekyu_tel				-- 請求先電話番号
        ,u.sekyu_fax				-- 請求先FAX
        ,u.uri_prc_sum				-- 売上金額合計
        ,u.genka_prc_sum			-- 原価金額合計
        ,u.uri_hepn_sum				-- 売上返品合計
        ,u.uri_nebk_sum				-- 売上値引合計
        ,u.hoshi_prc_sum			-- 奉仕料合計
        -- ,u.uri_prc_sum 
        --    + u.uri_hepn_sum 
        --    + u.uri_nebk_sum 
        --    + u.hoshi_prc_sum 
        --    + u.sougi_zei_sagaku_prc
        --    + u.sougi_early_use_cost
        --    + u.sougi_keiyaku_zei	-- 葬儀契約消費税	2016/03/26 ADD Kayo
        --    + u.etc_keiyaku_zei		-- 壇払等の契約消費税 	2016/03/26 ADD Kayo
        --    + u.etc_early_use_cost    AS seikyu_prc	-- 請求金額
        ,u.arari_prc 
            - u.sougi_keiyaku_prc
            + u.sougi_zei_sagaku_prc
            + u.sougi_keiyaku_prc
        AS seikyu_prc
        ,u.szei_katax_taisho_prc	-- 外税課税対象額
        ,u.uzei_katax_taisho_prc	-- 内税課税対象額
        ,u.hitax_katax_taisho_prc	-- 非税課税対象額
        ,u.tax_code_kbn				-- 税区分コード区分
        ,u.tax_cd					-- 税区分コード
        ,u.tax_kbn					-- 税区分
        ,u.zei_cd					-- 消費税コード
        ,u.out_zei_prc				-- 外税消費税額
        ,u.in_zei_prc				-- 内税消費税額
        ,u.out_zei_prc + u.in_zei_prc		AS	zei_prc	-- 消費税額 		
        --,u.uri_prc_sum 
        --	+ u.uri_hepn_sum 
        --	+ u.uri_nebk_sum 
        --	+ u.hoshi_prc_sum
        --	+ u.out_zei_prc  
        --	+ u.sougi_zei_sagaku_prc 
        --      + u.sougi_early_use_cost 
        --	+ u.sougi_early_use_cost_zei
        --	+ u.sougi_keiyaku_zei			-- 葬儀契約消費税	2016/03/26 ADD Kayo
        --	+ u.etc_keiyaku_zei				-- 壇払等の契約消費税 	2016/03/26 ADD Kayo
        --      +  u.sougi_keiyaku_prc + u.sougi_harai_prc +  u.sougi_meigi_chg_cost -- 会費残額と名義変更手数料を加味する
        --      + u.etc_early_use_cost
        --      + u.etc_early_use_cost_zei    AS	seikyu_zei_prc	-- 請求金額（消費税込み） 
        --,u.arari_prc + u.out_zei_prc - u.sougi_keiyaku_prc + u.sougi_zei_sagaku_prc + (u.sougi_keiyaku_prc + u.sougi_harai_prc) AS seikyu_zei_prc
        ,u.szei_katax_taisho_prc 
            + u.hitax_katax_taisho_prc
            + u.uzei_katax_taisho_prc 
            + u.out_zei_prc 
            - u.sougi_keiyaku_prc 
            + u.sougi_zei_sagaku_prc 
            + u.sougi_keiyaku_prc
        AS seikyu_zei_prc
        ,u.arari_prc				-- 粗利益額
        ,u.denpyo_biko1				-- 伝票備考１
        ,u.denpyo_biko2				-- 伝票備考２
        ,u.shonin_dt1				-- 承認１日時
        ,u.shonin_dt2				-- 承認２日時
        ,u.delete_flg				-- 削除フラグ
        ,u.status_kbn               -- ステータス
        ,CASE u.status_kbn WHEN 1 THEN '見積中'
                         WHEN 2 THEN '施行中'
                         WHEN 3 THEN '請求済'
                         WHEN 4 THEN '入金済'
                         WHEN 9 THEN '失注'
                         ELSE '-' END AS status_kbn_nm  -- ステータス表示名
--
       ,k.moushi_kbn       -- 申込区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
         AND delete_flg=0 limit 1) as moushi_kbn_nm -- 申込区分名
            ,k.sougi_cd         -- 葬儀区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
         AND delete_flg=0 limit 1) as sougi_cd_nm -- 葬儀区分名
            ,k.daicho_no_eria   -- 台帳番号（エリア）
            ,k.daicho_no_mm     -- 台帳番号（月）
            ,k.daicho_no_seq    -- 台帳番号（連番）
       ,(k.daicho_no_eria || '-' || k.daicho_no_mm || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
       ,k.kaiin_kbn        -- 会員区分
       ,cm3.kbn_value_lnm -- 会員区分名
       ,k.kaiin_sonota -- 会員区分（その他）
       ,k.uketuke_tanto_cd -- 受付担当者コード
       ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
       ,k.seko_tanto_cd    -- 施行担当者コード
       ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
       ,k.k_nm             -- 故人名
       ,k.m_nm       -- 喪主
       ,k.k_knm              -- 故人名カナ
       ,k.m_knm              -- 喪主名カナ
       ,k.souke_knm          -- 葬家名カナ
       ,CASE mg_kbn WHEN 0 THEN k.mg_yubin_no
                           ELSE k.kg_yubin_no END AS mg_yubin_no -- 喪主郵便番号
       ,CASE mg_kbn WHEN 0 THEN k.mg_addr1
                           ELSE k.kg_addr1 END AS mg_addr1   -- 喪主現住所1
       ,CASE mg_kbn WHEN 0 THEN k.mg_addr2
                           ELSE k.kg_addr2 END AS mg_addr2   -- 喪主現住所2
       ,CASE mg_kbn WHEN 0 THEN k.mg_tel
                           ELSE k.kg_tel END AS mg_tel       -- 喪主TEL
       ,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD') as sougi_ymd  -- 葬儀日
       ,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD') as nakijitu -- 亡日
       ,n2.basho_cd -- 式場CD
       ,n2.basho_nm -- 式場名
       ,(SELECT juchu_prc_sum FROM juchu_denpyo  WHERE seko_no = k.seko_no
         AND delete_flg=0 limit 1) as juchu_prc_sum -- 受注金額合計
       ,(SELECT uri_prc_sum FROM uriage_denpyo  WHERE seko_no = k.seko_no
         AND delete_flg=0 limit 1) as uri_prc_sum -- 売上金額合計
       -- ,k.status_kbn -- ステータス
       -- ,CASE k.status_kbn WHEN 1 THEN '見積中'
       --                   WHEN 2 THEN '施行中'
       --                   WHEN 3 THEN '請求済み（完了）'
       --                   WHEN 9 THEN 'その他（失注）'
       --                   ELSE '-' END AS status_kbn_nm  -- ステータス表示名
       ,k.souke_nm         -- 葬家
       ,k.keishiki_kbn     -- 葬儀形式
       ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
       -- ,k.bumon_cd         -- 売上部門コード
       -- ,bm.bumon_lnm  AS bumon_nm        -- 売上部門名
       -- ,bm.bumon_lnm       -- 売上正式部門名       
       -- ,bm.bumon_snm       -- 売上簡略部門名
       ,k.order_finish_flg AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
       ,CASE k.order_finish_flg WHEN 0 THEN '未発注'
                                WHEN 1 THEN '発注済み'
                                ELSE '-' END AS order_flg_nm  -- 発注完了表示名
--
       ,TO_CHAR(u.seikyu_print_date, 'YYYY/MM/DD HH24:MI:SS') AS  seikyu_print_date
       ,spt.tanto_nm                                          AS  seikyu_print_tanto_nm
       ,(SELECT max(nyukin_ymd) FROM nyukin_denpyo WHERE seikyu_no = u.uri_den_no AND delete_flg = 0) AS nyukin_ymd
        ,cnm2.kbn_value_lnm AS pay_method_cd_nm
  FROM uriage_denpyo u
  LEFT JOIN seko_kihon_info k
    ON k.delete_flg	    =   0
   AND k.seko_no		=	u.seko_no
  LEFT JOIN tanto_mst t1 
	ON k.uketuke_tanto_cd = t1.tanto_cd 
	AND t1.delete_flg = 0
  LEFT JOIN tanto_mst t2 
	ON k.seko_tanto_cd    = t2.tanto_cd 
	AND t2.delete_flg = 0
  LEFT JOIN nm_jyusho_mst jm 
	  ON k.kasoba_cd = jm.jyusho_cd   
	  AND jm.jyusho_kbn = 3 
	  AND jm.delete_flg = 0
 LEFT JOIN code_nm_mst cnm2
    ON cnm2.kbn_value_cd_num=u.pay_method_cd::int
   AND cnm2.code_kbn='1130' -- 支払方法
   AND cnm2.delete_flg=0
  LEFT JOIN code_nm_mst cm3 
	ON cm3.kbn_value_cd = k.kaiin_cd 
	AND cm3.code_kbn='0030' 
	AND cm3.delete_flg=0
  LEFT JOIN code_nm_mst pay 
	ON  pay.code_kbn     = '1130' 
	AND pay.kbn_value_cd = u.pay_method_cd 
	AND pay.delete_flg   = 0
  LEFT JOIN seko_nitei n1 
	ON n1.seko_no=k.seko_no 
	AND n1.nitei_kbn=1 
	AND n1.delete_flg=0
  LEFT JOIN seko_nitei n2 
	ON n2.seko_no=k.seko_no 
	AND n2.nitei_kbn=7 
	AND n2.delete_flg=0
  LEFT JOIN bumon_mst bm 
	ON k.bumon_cd = bm.bumon_cd 
	AND bm.delete_flg=0
  LEFT JOIN tanto_mst tt
    ON tt.delete_flg	=	0
	AND u.tanto_cd		=	tt.tanto_cd   $dateWhere2
  LEFT JOIN bumon_mst b
    ON b.delete_flg		=	0
	AND u.bumon_cd		=	b.bumon_cd   $dateWhere3
LEFT JOIN tanto_mst spt
   ON	spt.delete_flg	=	0
   AND	u.seikyu_print_tanto_cd		=	spt.tanto_cd   $dateWhere2
 WHERE u.delete_flg		=	0   $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
          , $param );
        return $select;
    }

}
