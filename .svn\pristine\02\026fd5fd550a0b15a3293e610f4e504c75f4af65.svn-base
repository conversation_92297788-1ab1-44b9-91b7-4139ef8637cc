<?php
  /**
   * DataMapper_MstrTmScheduleUchiwk
   *
   * タイムスケジュール内訳 データマッパー
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> m.<PERSON>ashi
   * @since      2014/02/25
   * @filesource
   */

require_once dirname(__FILE__).'/Mstr/Abstract.php';
  /**
   * タイムスケジュール内訳 データマッパー
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> m.Hayashi
   * @since      2014/02/25
   */
class DataMapper_MstrTmScheduleUchiwk  extends DataMapper_Mstr_Abstract
{
    // table情報
    protected $tables = array(
        'tm_schedule_uchiwk' => array(
            'logicalName' => 'タイムスケジュール内訳',
            'primary'     => array(
                'schedule_cd', 
                'page_kbn', 
                'line_no', 
                'orikaeshi_kbn'
            ),
            'denyUpdate'  => array(),
        ),
    );
    protected $order = array(
        'schedule_cd' => 'asc', 
        'page_kbn'    => 'asc',
        'line_no'     => 'asc',
    );
    // modelフィールド
    /*
    CREATE TABLE public.TM_SCHEDULE_UCHIWK (
      schedule_cd   CHAR(4)                NOT NULL, -- スケジュールコード
      page_kbn      NUMERIC(2,0) DEFAULT 1 NOT NULL, -- ページ区分
      line_no       NUMERIC(3,0) DEFAULT 0 NOT NULL, -- 行番号
      jikou_sabun   NUMERIC(4,0) DEFAULT 0 NOT NULL, -- 時刻差分
      kijyun_kbn    NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 基準区分
      orikaeshi_kbn NUMERIC(1,0) DEFAULT 0         , -- 折り返し区分
      line_su       NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 行数
      nitei_line1   VARCHAR(20)                    , -- 日程(1行目)
      biko_line1    VARCHAR(30)                    , -- 備考(1行目)
      nitei_line2   VARCHAR(20)                    , -- 日程(2行目)
      biko_line2    VARCHAR(30)                    , -- 備考(2行目)
      nitei_line3   VARCHAR(20)                    , -- 日程(3行目)
      biko_line3    VARCHAR(30)                    , -- 備考(3行目)
      delete_flg    NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 削除フラグ
      _req_id       BIGINT                 NOT NULL, -- 
      _cre_user     VARCHAR(64)            NOT NULL, -- 
      _cre_ts       TIMESTAMPTZ            NOT NULL, -- 
      _mod_user     VARCHAR(64)            NOT NULL, -- 
      _mod_ts       TIMESTAMPTZ            NOT NULL, -- 
      _mod_cnt      BIGINT       DEFAULT 0 NOT NULL, -- 

      PRIMARY KEY ( schedule_cd,page_kbn,line_no,orikaeshi_kbn ) USING INDEX TABLESPACE pg_default
    );
     */
    protected $fields = array(
        'schedule_cd'     => array(   // CHAR(4) NOT NULL, -- スケジュールコード
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => 'スケジュールコード',
            'required'      => true,
            'preset'        => 'code4',
        ),
        'page_kbn'        => array(   // NUMERIC(2,0) NOT NULL DEFAULT 1, -- ページ区分 [1:1ページ目,2:２ページ目,3:３ページ目]
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => 'ページ区分',
            'preset'        => 'kbn2',
            'defs'          => 0,
        ),
        'line_no'         => array(   // NUMERIC(3,0) DEFAULT 0 NOT NULL, -- 行番号
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '行番号',
            'preset'        => 'num3',
            'defs'          => 0,
        ),
        'jikou_sabun'     => array(   // NUMERIC(4,0) DEFAULT 0 NOT NULL, -- 時刻差分 [30分]
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '時刻差分',
            'preset'        => 'num4',
            'defs'          => 0,
        ),
        'kijyun_kbn'      => array(   // NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 基準区分 [1:亡日,2:湯灌,3:入棺,4:通夜,5:出棺,6:火葬,7:葬儀]
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '基準区分',
            'preset'        => 'kbn2',
            'defs'          => 0,
        ),
        /* 未使用
        'orikaeshi_kbn'   => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 折り返し区分 [0：右側 1：左側]
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '折り返し区分',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
        */
        'line_su'         => array(   // NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 行数
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '行数',
            'preset'        => 'num2',
            'defs'          => 0,
        ),
        'nitei_line1'     => array(   // VARCHAR(20), -- 日程(1行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '日程(1行目)',
            'preset'        => 'string20',
        ),
        'biko_line1'      => array(   // VARCHAR(30), -- 備考(1行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '備考(1行目)',
            'preset'        => 'string30',
        ),
        'nitei_line2'     => array(   // VARCHAR(20), -- 日程(2行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '日程(2行目)',
            'preset'        => 'string20',
        ),
        'biko_line2'      => array(   // VARCHAR(30), -- 備考(2行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '備考(2行目)',
            'preset'        => 'string30',
        ),
        'nitei_line3'     => array(   // VARCHAR(20), -- 日程(3行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '日程(3行目)',
            'preset'        => 'string20',
        ),
        'biko_line3'      => array(   // VARCHAR(30), -- 備考(3行目)
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '備考(3行目)',
            'preset'        => 'string30',
        ),
        'delete_flg'      => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 削除フラグ [0:有効ﾃﾞｰﾀ,1:削除ﾃﾞｰﾀ]
            'tables'        => array('tm_schedule_uchiwk'),
            'logicalName'   => '削除フラグ',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
    );
}
