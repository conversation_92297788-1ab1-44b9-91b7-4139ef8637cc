<?php
  /**
   * Mref_Jsonlist_Keidantai2
   *
   * マスタデータ参照用に JSON データを取得するクラス
   *   cf. JsonlistController.php
   *
   * @category   App
   * @package    controllers\Mref
   * <AUTHOR> Mihara
   * @since      2016/04/xx
   * @filesource 
   */

  /**
   * マスタデータ参照用に JSON データを取得するクラス
   *   cf. JsonlistController.php
   *
   * @category   App
   * @package    controllers\Mref
   * <AUTHOR> Mihara
   * @since      2016/04/xx
   */
class Mref_Jsonlist_Keidantai2 extends Mref_Jsonlist_ExAbst
{
    /**
     * データ取得
     *
     * <AUTHOR> Mihara
     * @since 2016/04/xx
     */
	public function getData()
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $params  = Msi_Sys_Utils::webInputs();

        // Msi_Sys_Utils::debug( 'Keidantai2.php params==>' . Msi_Sys_Utils::dump($params) );

        $cond = array();

        @ $q = $params['q'];
        if ( strlen($q) > 0 ) {
            $cond['partner_cd'] = DataMapper_Utils::condOneOf( 'partner_cd', $q );
        }

        $data0 = DataMapper_KeiyakuSaki::find( $db, $cond );

        $data = array();
        foreach ( $data0 as $rec ) {
            $data[] = array(
                            'id' => $rec['partner_cd'], // 
                            // 'text' => sprintf("%s %s", $rec['partner_cd'], $rec['partner_lnm']),
                            'text' => $rec['partner_lnm'],
                            // 'my_cd' => $rec['partner_cd'],
                            // 'my_nm' => $rec['partner_lnm'],
                            );
        }

        // Msi_Sys_Utils::debug( 'Keidantai2.php data==>' . Msi_Sys_Utils::dump($data) );

        return $data;
    }


}
