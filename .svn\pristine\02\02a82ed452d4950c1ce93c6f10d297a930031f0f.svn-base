/* 
 * 見積書と請求書の共通処理
 * @version 2019/04/30 MSI mihara 軽減税率対応
 */
var applms = applms || {}; // 見積書と請求書共通処理
var appmtsk = appmtsk || {}; // 見積書または請求書個別処理
$(function() {
    "use strict";
    // 見積書と請求書でデフォルト
    applms = {
        o_price: '', // 金額文言
        printURI: '', // 印刷URI
        setSideMenu: function() {
        }, // サイドメニュー設定処理
        initializeView: function() {
        }, // viewの初期処理
        setShonin: function(dataApp) {
            // 1．権限が「システム管理者」と「マネージャ」、「事務」以外で、印鑑が「担当」のみ押されいてる場合は「印刷」と「コンビ二請求書」ボタンは押下できないように制御する
            // 2．「承認」と「取消」(承認取消)ボタンは「システム管理者」と「マネージャ」、「事務」以外は押下できないように制御する
            if (!dataApp.role_kbn) {
                applms.setDisabledButtonStyle($(".buttons #btn_shonin"));
                applms.setDisabledButtonStyle($(".buttons #btn_cancel"));
                if (dataApp.shonin.hasShonin) {
                    applms.clearDisabledButtonStyle($(".buttons .btn_hoji_print"));
                    applms.clearDisabledButtonStyle($(".buttons #btn_print_konbini"));
                } else {
                    applms.setDisabledButtonStyle($(".buttons .btn_hoji_print"));
                    applms.setDisabledButtonStyle($(".buttons #btn_print_konbini"));
                }
            }
        },
        setNyukin: function() {
        }, // 入金行追加処理(請求書)
        doSave: function() {
        }, // 保存処理
        doShonin: function() {
        }, // 承認処理(請求書)
        doCancel: function() {
        }, // 再表示処理(見積書)と承認取消処理(請求書)
        mitsukakutei: function() {
        }, // 見積確定処理(見積書)
        mitsutorikesi: function() {
        }, 
        // コンビニー請求発行
        doPrint_konbini: function() {
        },
        // 押下不可のボタンスタイルを設定
        setDisabledButtonStyle: function($button) {
            $button.attr("disabled", "disabled").addClass("disable_btn");
        },
        // 押下不可のボタンスタイルをクリア
        clearDisabledButtonStyle: function($button) {
            $button.removeAttr("disabled").removeClass("disable_btn");
        }
    };
    // 見積書または請求書個別処理を継承する
    $.extend(applms, appmtsk);

    // 現在選択したタブのインデックス
    applms.tabindex = 0;
    // 現在選択した行のインデックス
    applms.rowindex = -1; // 選択されていないとき

    /** タブ区分：1=>法要・墓参り */
    var TAB_KBN_S = '1';
    /** タブ区分：2=>返礼品 */
    var TAB_KBN_H = '2';
    /** タブ区分：3=>飲食費 */
    var TAB_KBN_I = '3';
    /** タブ区分：4=>立替 */
    var TAB_KBN_T = '4';
    /** タブ区分：5=>その他タブ*/
    var TAB_KBN_O = '5';

    /** タブサブ区分：1 */
    var TAB_SUB_KBN_1 = '1';
    /** タブ区分：2 */
    var TAB_SUB_KBN_2 = '2';

    /** 見積書または請求書の行区分：1=>ヘッダー行 */
    var ROW_KBN_H = '1';
    /** 見積書または請求書の行区分：2=>明細のタイトル行 */
    var ROW_KBN_T = '2';
    /** 見積書または請求書の行区分：3=>明細行 */
    var ROW_KBN_D = '3';
    /** 見積書または請求書の行区分：4=>消費税等行 */
    var ROW_KBN_S = '4';
    /** 見積書または請求書の行区分：9=>合計行 */
    var ROW_KBN_G = '9';


    /** レコード区分：1=>タイトル行 */
    var REC_KBN_T = '1';
    /** レコード区分：2=>明細行 */
    var REC_KBN_M = '2';
    /** レコード区分：3=>小計行 */
    var REC_KBN_S = '3';
    /** レコード区分：4=>奉仕料行 */
    var REC_KBN_H = '4';
    /** レコード区分：5=>互助会充当行 */
    var REC_KBN_J = '5';
    /** レコード区分：9=>合計行 */
    var REC_KBN_G = '9';

    /** 選択行追加可能区分 0=>追加不可 */
    var ADD_KBN_NG = '0';
    /** 選択行追加可能区分 1=>追加可 */
    var ADD_KBN_OK = '1';

    /** 受注伝票の追加区分 1=>見積入力データ */
    var ADD_KBN_MITSU = '1';

    /** 行選択可能区分 1=>選択可能 */
    var CHECK_KBN_OK = '1';

    /** 値引き区分 0=>通常商品 */
    var NEBIKI_KBN_IPPAN = '0';
    /** 値引き区分 1=>互助会 */
    var NEBIKI_KBN_GOJOKAI = '1';
    /** 値引き区分 1=>互助会値引き商品 */
    var NEBIKI_KBN_NEBIKI = '2';

    /** disabledプロパティ */
    var ATTR_NO_INPUT = 'disabled';

    /** 米印 */
    var KOME = '※';
    /** × */
    var KAKE = '×';
    /** パーセント */
    var PERCENT = '％';
    /** 法要費用 */
    var HOUYO = '法要費用';
    /** 法要・墓参り費用 */
    var HOUYOHAKA = '法要・墓参り費用';
    /** 返礼品費用 */
    var HENREI = '返礼品費用';
    /** 飲食費用 */
    var INSHOKU = '会食費用';
    /** 立替費用 */
    var TATEKAE = '税込項目費用';
    /** その他費用' */
    var SONOTA = 'その他費用';
    /** 【小　計】 */
    var SHOKEI = '【小　計】';
    /** 奉仕料 */
    var HOSHIRYO = $(".estimate-head .houshi_disp").text();

    var MSG_WARN_1 = '保存されていないデータが存在します。保存後、もう一度行ってください。';

    var DAI_HOUJI = '0110'; // 大分類 0110:法事
    var CHU_HOUYO = '1000'; // 中分類 0510:法要・他
    var CHU_JIGO    =  '1000';   // 1事後          
    var CHU_IHAI    =  '1010';   // 2位牌          
    var CHU_BUTUDAN =  '1020';   // 3仏壇          
    var CHU_BOSHO   =  '1030';   // 4墓所・手元供養
    var CHU_ANNAI   =  '1040';   // 5案内状        
    var CHU_SAIDAN  =  '1050';   // 6祭壇・式場    
    var CHU_HOYO    =  '1070';   // 8法要          
    var CHU_BOSAN   =  '1080';   // 9墓参          
    var CHU_HOUJI   =  '1110';   // 10法事・催事    
	
    var CHU_HENREI = '1090'; // 中分類 0240:返礼品
    var CHU_DANBARAI = '1120'; // 中分類 0170:料理
    var CHU_BETO = '1140'; // 中分類 0320:別途費用
    var CHU_TATE = '1130'; // 中分類 0310:立替金
    var CHU_NEBIKI = '1150'; // 中分類 0340:値引き
    //
    // リサイズ処理
    $(window).resize(function() {
        // 高さ変更処理
        _responsiveResize();
        // ヘッダーのスクロール表示非表示処理
        _responsiveScroll();
    });
    // グリッド高さ変更処理
    var _responsiveResize = function() {
        var colLen = applms.appView.getMeisaiColLength();
        if (colLen === 0) {
            return;
        }
        var $target, maxheight, height;
        var dh = $("#detail").height();
        var heightOnerow = 32;
        if (applms.tabindex === 0) {
            $target = $("#detail #estimate.mitsu");
            // 非表示以外のtbodyの数
            var disLen = $target.find(".display-none").length;
            maxheight = dh - 240;
            colLen = colLen - disLen;
            height = (colLen) * heightOnerow;
        } else {
            $target = $("#detail #estimate.meisai");
            maxheight = (heightOnerow * 24);
            height = (colLen - 1) * heightOnerow;
        }
        if (height > maxheight) {
            $target.css("height", maxheight);
            applms.meisai_scroll = true;
        } else {
            $target.css("height", "auto");
            applms.meisai_scroll = false;
        }
    };
    // ヘッダーのスクロール表示非表示処理
    var _responsiveScroll = function() {
        // 見積書または請求書のタブ以外の場合
        if (applms.tabindex === 0) {
            return;
        }
        var $targets = $("#detail #estimate.meisai");
        var $target = $targets.eq(applms.tabindex - 1);
        var $tabContents = $target.closest(".tab-contents");
        // 元の高さと表示されている高さを比較
        if (!applms.meisai_scroll) {
            $tabContents.find(".estimate-head").css("overflow-y", "hidden");
            $tabContents.find(".meisai").css("overflow-y", "hidden");
            $tabContents.find(".estimate-foot").css("overflow-y", "hidden");
        } else {
            $tabContents.find(".estimate-head").css("overflow-y", "scroll");
            $tabContents.find(".meisai").css("overflow-y", "scroll");
            $tabContents.find(".estimate-foot").css("overflow-y", "scroll");
        }
    };

    // 見積書または請求書設定処理 
    var _setSummary = function() {
        applms.summaryCol.reset();
        var m;
        var prc1, prc2, prc3, prc4, prc5 = 0;
        if (applms.meisaiCol1.length === 0) {
            return;
        }
        // エレメント削除する前に明細のon|off情報を退避する
        var $dspNoneIds = [];
        $('#estimate .bloc .head').each(function(index) {
            if ($(this).hasClass("display-none")) {
                $dspNoneIds.push(index);
            }
        });

        // すべてのエレメント削除
        $("#estimate .bloc,#estimate .all").remove();
        var shohizeiObj = applms.appModel.get('shohizei');
        var kagamiInfo = applms.appModel.get('kagamiInfo');
        applms.prcSum = 0;

        // 法要・墓参り費用
            m = new SummaryModel({price: kagamiInfo.syokeiA.prc});
            _setSummaryModel(m, kagamiInfo.syokeiA.title, '小計(A)', ROW_KBN_H);
        // プラン
        if (!$.msiJqlib.isNullEx2(kagamiInfo.plan.title)) {
            m = new SummaryModel({price: kagamiInfo.plan.prc});
            _setSummaryModel(m, kagamiInfo.plan.title, null, ROW_KBN_D);
        }
        // 基本費用
        if (kagamiInfo.houyou.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.houyou.prc});
            _setSummaryModel(m, kagamiInfo.houyou.title, null, ROW_KBN_D);
        }
        // 割引金額
        if (kagamiInfo.waribiki.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.waribiki.prc});
            _setSummaryModel(m, kagamiInfo.waribiki.title, null, ROW_KBN_D);   
        }
        // おもてなし費用
            m = new SummaryModel({price: kagamiInfo.syokeiB.prc});
            _setSummaryModel(m, kagamiInfo.syokeiB.title, '小計(B)', ROW_KBN_H);
        // 会葬返礼
        if (kagamiInfo.henrei.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.henrei.prc});
            _setSummaryModel(m, kagamiInfo.henrei.title, null, ROW_KBN_D);   
        }
        // 料理
        if (kagamiInfo.ryori.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.ryori.prc});
            _setSummaryModel(m, kagamiInfo.ryori.title, null, ROW_KBN_D);   
        }
        // 割引金額
        if (kagamiInfo.nebiki_omt.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.nebiki_omt.prc});
            _setSummaryModel(m, kagamiInfo.nebiki_omt.title, null, ROW_KBN_D);   
        }
        // 計(A＋B)
            m = new SummaryModel({price: kagamiInfo.syokeiAB.prc});
            _setSummaryModel(m, kagamiInfo.syokeiAB.title, null, ROW_KBN_H);
        // 別途費用
        if (kagamiInfo.betto.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.betto.prc});
            _setSummaryModel(m, kagamiInfo.betto.title, null, ROW_KBN_D);   
        }
        // 対象額と消費税を列記する
        // 外税
        if (kagamiInfo.szei_katax_taisho_prc.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.szei_katax_taisho_prc.prc});
            _setSummaryModel(m, kagamiInfo.szei_katax_taisho_prc.title, null, ROW_KBN_H);
            m = new SummaryModel({price: kagamiInfo.out_zei_prc.prc});
            _setSummaryModel(m, kagamiInfo.out_zei_prc.title, null, ROW_KBN_H);   
        }
        // 外税(軽減)
        if (kagamiInfo.szei_katax_taisho_prc_keigen.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.szei_katax_taisho_prc_keigen.prc});
            _setSummaryModel(m, kagamiInfo.szei_katax_taisho_prc_keigen.title, null, ROW_KBN_H);
            m = new SummaryModel({price: kagamiInfo.out_zei_prc_keigen.prc});
            _setSummaryModel(m, kagamiInfo.out_zei_prc_keigen.title, null, ROW_KBN_H);   
        }
        // 内税
        if (kagamiInfo.uzei_katax_taisho_prc.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.uzei_katax_taisho_prc.prc});
            _setSummaryModel(m, kagamiInfo.uzei_katax_taisho_prc.title, null, ROW_KBN_H);
            m = new SummaryModel({price: kagamiInfo.in_zei_prc.prc});
            _setSummaryModel(m, kagamiInfo.in_zei_prc.title, null, ROW_KBN_H);   
        }
        // 内税(軽減)
        if (kagamiInfo.uzei_katax_taisho_prc_keigen.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.uzei_katax_taisho_prc_keigen.prc});
            _setSummaryModel(m, kagamiInfo.uzei_katax_taisho_prc_keigen.title, null, ROW_KBN_H);
            m = new SummaryModel({price: kagamiInfo.in_zei_prc_keigen.prc});
            _setSummaryModel(m, kagamiInfo.in_zei_prc_keigen.title, null, ROW_KBN_H);   
        }
        // 非課税
        if (kagamiInfo.hitax_katax_taisho_prc.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.hitax_katax_taisho_prc.prc});
            _setSummaryModel(m, kagamiInfo.hitax_katax_taisho_prc.title, null, ROW_KBN_H); 
        }
        // 計
        m = new SummaryModel({price: kagamiInfo.syokeiC.prc});
        _setSummaryModel(m, kagamiInfo.syokeiC.title, null, ROW_KBN_H);
        // 早期利用費
        if (kagamiInfo.early_use_cost.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.early_use_cost.prc});
            _setSummaryModel(m, kagamiInfo.early_use_cost.title, null, ROW_KBN_D);   
        }
        // 早期利用費の消費税
        if (kagamiInfo.early_use_cost.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.early_use_cost_zei.prc});
            _setSummaryModel(m, kagamiInfo.early_use_cost_zei.title, null, ROW_KBN_D);   
        }
        // 掛金の消費税
        if (kagamiInfo.keiyaku_zei.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.keiyaku_zei.prc});
            _setSummaryModel(m, kagamiInfo.keiyaku_zei.title, null, ROW_KBN_D);   
        }
        // 掛金残金
        if (kagamiInfo.keiyaku_zan.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.keiyaku_zan.prc});
            _setSummaryModel(m, kagamiInfo.keiyaku_zan.title, null, ROW_KBN_D);   
        }
        // 施行解約金
        if (kagamiInfo.kaiyaku_prc.prc != 0) {
            m = new SummaryModel({price: kagamiInfo.kaiyaku_prc.prc});
            _setSummaryModel(m, kagamiInfo.kaiyaku_prc.title, null, ROW_KBN_D);   
        }
        applms.prcSum = kagamiInfo.total.prc;

        // 請求書の入金行設定処理
        applms.setNyukin(new SummaryModel());

        // 御見積りまたは御請求金額
        m = new SummaryModel({price: applms.prcSum});
        _setSummaryModel(m, applms.o_price, '合計', ROW_KBN_G);

        // 明細のon|offを設定する
        var $target = $('#estimate .bloc .head');
        _.each($dspNoneIds, function(val) {
            $target.eq(val).trigger("click");
        });
    };
    /**
     * 見積書または請求書モデル設定処理
     * @param {SummaryModel} m 見積書または請求書モデル
     * @param {string} title タイトル
     * @param {string} sub サブタイトル
     * @param {string} rowKbn 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4=>消費税等行 9:合計行
     * @param {SummaryCollection} addCol 存在すれば、ここにモデルを追加する
     */
    var _setSummaryModel = function(m, title, sub, rowKbn, addCol) {
        if (!$.msiJqlib.isNullEx2(m)) {
            m.set('title', title);
            m.set('sub', sub);
            m.set('row_kbn', rowKbn);
            if ($.msiJqlib.isNullEx2(addCol)) {
                applms.summaryCol.add(m);
            } else {
                addCol.add(m);
            }
        }
    };
    /**
     * 見積書または請求書消費税等モデル設定処理
     * @param {string} title タイトル
     * @param {number} prc 金額
     */
    var _setShohiEtc = function(title, prc) {
        if (!$.msiJqlib.isNullEx2(prc)) {
            prc = prc * 1;
            if (prc !== 0) {
                var m = new SummaryModel({price: prc});
                _setSummaryModel(m, title, null, ROW_KBN_D);
                applms.prcSum += prc;
            }
        }
    };
    /**
     * 見積書または請求書一行の金額を設定して返す処理
     * @param {MeisaiCollection} meisaiCol 明細コレクション
     * @param {object} filters 絞り込み条件
     */
    var _getSummaryOne = function(meisaiCol, filters) {
        var m, refms, refm;
        refms = meisaiCol.where(filters);
        if (refms.length === 1) {
            refm = refms[0];
            m = new SummaryModel();
            m.set('price', refm.get('juchu_prc_kei'));// 金額
        }
        return m;
    };

    /**
     * 見積書または請求書一行の金額を設定して返す処理
     * @param {MeisaiCollection} meisaiCol 明細コレクション
     * @param {object} filters 絞り込み条件
     * @param {string} attr モデル属性名
     */
    var _getSummaryOne2 = function (meisaiCol, filters, attr) {
        var m, refms, refm;
        refms = meisaiCol.where(filters);
        if (refms.length === 1) {
            refm = refms[0];
            m = new SummaryModel();
            m.set('price', refm.get(attr));// 金額
        }
        return m;
    };
    
    // 法要・墓参りデータ設定処理
    var _setHouyo = function() {
        // 法要・墓参りデータ
        var dataH = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_S;
        });
        var dataColH = new MeisaiCollection();
        dataColH.add(dataH);
        // 合計行を追加する
        _setGokeiRow(dataColH, TAB_KBN_H, HOUYOHAKA);
        applms.meisaiCol1.reset(dataColH.toJSON());

    };

    // 返礼品データ設定処理
    var _setHenrei = function() {
        // 返礼品データ
        var dataH = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_H;
        });
        var dataColH = new MeisaiCollection();
        dataColH.add(dataH);
        // 返礼品互助会充当行を追加する
//        _setHenreiRow(dataColH, TAB_KBN_H);
        // 合計行を追加する
        _setGokeiRow(dataColH, TAB_KBN_H, HENREI);
        applms.meisaiCol2.reset(dataColH.toJSON());

    };

    // 飲食費データ設定処理
    var _setInshoku = function() {
        // 飲食費データ
        var dataI = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_I;
        });
        var dataColI = new MeisaiCollection();
        dataColI.add(dataI);
        // 奉仕料行を追加する
        _setHoshiRow(dataColI, TAB_KBN_I);
        // 合計行を追加する
        _setGokeiRow(dataColI, TAB_KBN_I, INSHOKU);
        applms.meisaiCol3.reset(dataColI.toJSON());

    };

    // 立替金データ設定処理
    var _setTatekae = function() {
        // 立替金データ
        var dataT = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_T;
        });
        var dataColT = new MeisaiCollection();
        dataColT.add(dataT);
        // 合計行を追加する
        _setGokeiRow(dataColT, TAB_KBN_T, TATEKAE);
        applms.meisaiCol4.reset(dataColT.toJSON());

    };

    // その他データ設定処理
    var _setSonota = function() {

        // その他データ
        var dataS = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_O;
        });
        var dataColS = new MeisaiCollection();
        dataColS.add(dataS);
        // 合計行を追加する
        _setGokeiRow(dataColS, TAB_KBN_O, SONOTA);
        applms.meisaiCol5.reset(dataColS.toJSON());
    };

    // 奉仕料行設定処理
    var _setHoshiRow = function(dataCol, tabKbn) {
        var hoshi = 0;
        // 飲食費の奉仕料データ
        var dataH = dataCol.filter(function(item) {
            return item.get("hoshi_umu_kbn") === '1'; // 奉仕料あり
        });
        hoshi = _calcSum(dataH, 'hoshi_prc');
        var m = new MeisaiModel();
        m.set('shohin_kbn_nm', HOSHIRYO);
//        m.set('shohin_tkiyo_nm', KOME + KAKE + applms.appModel.get('hoshi_ritu_org') + PERCENT);
        m.set('tab_kbn', tabKbn);
        m.set('record_kbn', REC_KBN_H);
        m.set('add_down_kbn', ADD_KBN_NG);
        m.set('juchu_prc_kei', hoshi);
        m.set('juchu_prc', hoshi);
        dataCol.add(m);
    };

    // 合計行設定処理
    var _setGokeiRow = function(dataCol, tabKbn, title) {
        var gokei = 0;
        // 小計を除く
        var dataE = dataCol.filter(function(item) {
            return item.get("record_kbn") !== REC_KBN_S;
        });
        gokei = _calcSum(dataE, 'juchu_prc_kei');
        var juchu_prc = _calcSum(dataE, 'juchu_prc');
        var gojokai_nebiki_prc_kei = _calcSum(dataE, 'gojokai_nebiki_prc');
        var nebiki_prc_kei = _calcSum(dataE, 'nebiki_prc');
        var nebiki_prc2_kei = _calcSum(dataE, 'nebiki_prc2');
        var m = new MeisaiModel();
        m.set('shohin_kbn_nm', title);
        m.set('tab_kbn', tabKbn);
        m.set('record_kbn', REC_KBN_G);
        m.set('add_down_kbn', ADD_KBN_NG);
        m.set('juchu_prc_kei', gokei);
        m.set('juchu_prc', juchu_prc);
        m.set('gojokai_nebiki_prc_kei', gojokai_nebiki_prc_kei);
        m.set('nebiki_prc_kei', nebiki_prc_kei);
        m.set('nebiki_prc2_kei', nebiki_prc2_kei);
        dataCol.add(m);
    };

    /**
     * 金額合計処理
     * @param {array} dataCol モデル配列
     * @param {string} tarPro 集計対象モデルプロパティ
     */
    var _calcSum = function(dataCol, tarPro) {
        var gokei = 0;
        _.each(dataCol, function(m) {
            var prc = m.get(tarPro);
            if (!$.msiJqlib.isNullEx2(prc) && !isNaN(prc)) {
                gokei += Number(prc);
            }
        });
        return gokei;
    };

    /**
     * validation valid時処理
     * @param {MeisaiView} view
     * @param {string} attr
     */
    var _valid = function(view, attr) {
        var $el = view.$("." + attr);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };
    /**
     * validation invalid時処理
     * @param {MeisaiView} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function(view, attr, error) {
        var $el = view.$("." + attr);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function() {
            return {
                jichu_kakute_ymd: null, // 受注確定日
                tanto_nm: null, // 受注確定担当者名
                kakutei_info: null, // 受注確定日 + 受注確定担当者名
                hoshi_ritu_org: null, // 奉仕率
                hoshi_ritu: null, // 奉仕率 / 100
                hasu_kbn: null, // 端数区分
                shohi_zei_rtu: null, // 消費税率
                shohi_zei_cd: null, // 消費税コード
                sosogorei_plan_a_nm: null, // 【Ａ】葬送儀礼費用基本プラン
                sosogorei_plan_b_nm: null, // 【Ｂ】葬送儀礼費用プラン外選択品目
                kaiin_no: null, // 互助会会員番号
                seko_plan_cd: null, // 施行プランコード
                harai_gaku: null, // 互助会払込金額A
                no_seko_plan_msg: null, // 施行プランコードが存在しない時のメッセージ
                print_kbn: '1', // 印刷区分
                mitsu_biko: null

            };
        }
    }); // AppModel

    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click .tab li a": "changeTab",
            "click #estimate .closable table .head": "summaryHeaderClick",
            "click .function #btn_add": "addRow",
            "click .function #btn_del": "delRow",
            "click .function #btn_kakutei": "mitsukakutei",
            "click .function #btn_torikesi": "mitsutorikesi",
            "click #btn_save": "doSave",
            "click #btn_cancel": "doCancel",
            "click #btn_shonin": "doShonin",
            "click #btn_print_confirm": "doConfirmPrint",
            "click #btn_print_konbini": "doPrint_konbini",
            "click #btn_print": "doPrint"
        },
        bindings: {
            '#kakutei_info': 'kakutei_info',
            '#print_kbn': 'print_kbn',
            '#mitsu_biko': 'mitsu_biko'
        },
        initialize: function() {
            this.listenTo(applms.summaryCol, 'add', this.addSummaryOne);
            this.listenTo(applms.meisaiCol1, 'reset', this.addSosogireiCol);
            this.listenTo(applms.meisaiCol2, 'reset', this.addHenreiCol);
            this.listenTo(applms.meisaiCol3, 'reset', this.addInshokuCol);
            this.listenTo(applms.meisaiCol4, 'reset', this.addTatekaeCol);
            this.listenTo(applms.meisaiCol5, 'reset', this.addSonotaCol);
            this.listenTo(applms.meisaiCol1, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol2, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol3, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol4, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol5, 'add', this.addOneAfter);
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function() {
            Backbone.Validation.bind(this, {
                valid: function(view, attr) {
                    _valid(view, attr);
                },
                invalid: function(view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            return this;
        },
        // 見積書または請求書一行追加処理（最終行に追加）
        addSummaryOne: function(m) {
            var v = new SummaryView({model: m});
            var el = v.render().el;

            // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            var rowKbn = m.get("row_kbn");
            if (rowKbn === ROW_KBN_T || rowKbn === ROW_KBN_D) { // 2:明細のタイトル行, 3:明細行
                this.$("#estimate .bloc").last().find("table").append(el);
            } else {
                var $div;
                if (rowKbn === ROW_KBN_H) { // 1:ヘッダー行
                    $div = $('<div class="bloc closable"></div>');
                } else if (rowKbn === ROW_KBN_S) { // 4:消費税等行
                    $div = $('<div class="bloc"></div>');

                } else if (rowKbn === ROW_KBN_G) { // 9:合計行
                    $div = $('<div class="all"></div>');
                }
                var $table = $('<table/>');
                $table.append(el);
                $div.append($table);
                this.$("#estimate").append($div);
            }

        },
        // 法要・墓参り～その他タブ一行追加処理（最終行に追加）
        addOne: function(m) {
            var v = new MeisaiView({model: m});
            if (m.get('record_kbn') === REC_KBN_G) {
                // 合計行追加
                this.$tabTarget.find(".total table").append(v.render().el);
            } else {
                // 合計行以外の行追加
                this.$tabTarget.find(".meisai .list table").append(v.render().el);
            }
        },
        // 法要・墓参り～その他タブ一行追加処理（指定行の次に追加）
        addOneAfter: function(m) {
            if (applms.tabindex === 1) {
                this.$tabTarget = this.$("#estimate1-tab");
            } else if (applms.tabindex === 2) {
                this.$tabTarget = this.$("#estimate2-tab");
            } else if (applms.tabindex === 3) {
                this.$tabTarget = this.$("#estimate3-tab");
            } else if (applms.tabindex === 4) {
                this.$tabTarget = this.$("#estimate4-tab");
            } else if (applms.tabindex === 5) {
                this.$tabTarget = this.$("#estimate5-tab");
            }
            m.set('tab_kbn', String(applms.tabindex));
            var v = new MeisaiView({model: m});
            if (applms.rowindex === 0) {
                this.$tabTarget.find(".meisai .list table").prepend(v.render().el);
            } else {
                this.$tabTarget.find(".meisai .list table tbody").eq(applms.rowindex - 1).after(v.render().el);
            }
        },
        addCol: function(collection) {
            this.$tabTarget.find('tbody').remove();
            collection.each(this.addOne, this);
        },
        // 法要・墓参りコレクション追加処理
        addSosogireiCol: function(collection) {
            this.$tabTarget = this.$("#estimate1-tab");
            this.addCol(collection);
        },
        // 返礼品コレクション追加処理
        addHenreiCol: function(collection) {
            this.$tabTarget = this.$("#estimate2-tab");
            this.addCol(collection);
        },
        // 飲食コレクション追加処理
        addInshokuCol: function(collection) {
            this.$tabTarget = this.$("#estimate3-tab");
            this.addCol(collection);
        },
        // 立替コレクション追加処理
        addTatekaeCol: function(collection) {
            this.$tabTarget = this.$("#estimate4-tab");
            this.addCol(collection);
        },
        // その他コレクション追加処理
        addSonotaCol: function(collection) {
            this.$tabTarget = this.$("#estimate5-tab");
            this.addCol(collection);
        },
        // タブ切り替え処理
        changeTab: function(e) {
            $.msiJqlib.clearAlert();
            var idx = $("#estimate-div-wrapper .tab li").index($(e.currentTarget).closest("li"));
            if (idx === 0 && !this.mesaiIsEq()) {
                $.msiJqlib.showWarn(MSG_WARN_1);
                return;
            }
            // タブ切り替え
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            // 選択行解除
            this.$('.list tbody tr').removeClass('active');
            // 一旦追加ボタンを活性化
            applms.clearDisabledButtonStyle(this.$(".function #btn_add"));
            // 一旦削除ボタンを非活性化
            applms.setDisabledButtonStyle(this.$(".function #btn_del"));
            // タブインデックス
            applms.tabindex = $("#estimate-div-wrapper .tab li").index($("#estimate-div-wrapper .tab li span").closest("li"));
            // 行インデックスインデックス初期化
            applms.rowindex = -1;
            // リサイズ発火
            $(window).trigger("resize");
        },
        // 明細イコールが判定する
        mesaiIsEq: function() {
            // 明細イコール
            var meisaiEq1 = $.msiJqlib.isEqual(applms.meisaiCol1.toJSON(), applms.orgDataCol1);
            var meisaiEq2 = $.msiJqlib.isEqual(applms.meisaiCol2.toJSON(), applms.orgDataCol2);
            var meisaiEq3 = $.msiJqlib.isEqual(applms.meisaiCol3.toJSON(), applms.orgDataCol3);
            var meisaiEq4 = $.msiJqlib.isEqual(applms.meisaiCol4.toJSON(), applms.orgDataCol4);
            var meisaiEq5 = $.msiJqlib.isEqual(applms.meisaiCol5.toJSON(), applms.orgDataCol5);
            return meisaiEq1 && meisaiEq2 && meisaiEq3 && meisaiEq4 && meisaiEq5;
        },
        // 見積書または請求書ヘッダー行クリック
        summaryHeaderClick: function(e) {
            var $target = $(e.currentTarget);
            this.toggleView($target);
        },
        // 見積書または請求書ヘッダー行クリックにより明細表示・非表示処理
        toggleView: function($target) {
            var $shosai = $target.parent('table').find(".shosai");
            $shosai.toggle();
            $target.toggleClass("display-none");
            $shosai.toggleClass("display-none");
            // リサイズ発火
            $(window).trigger("resize");
        },
        //　行追加処理
        addRow: function() {
            if (applms.tabindex > 0) {
                var mesaiCol = this.getMeisaiCol();
                var m = new MeisaiModel();
                m.set('shohin_kbn_nm', 'New');
                m.set('add_kbn', ADD_KBN_MITSU);
                applms.rowindex = 0;
                mesaiCol.add(m, {at: 0});
                //var selectedModel = mesaiCol.at(applms.rowindex);
//                if (applms.rowindex === -1) {
//                    applms.rowindex = mesaiCol.length - 1;
//                    // 小計飲食の場合は奉仕料があるため、マイナス１をする
//                    if (applms.tabindex === 3) {
//                        applms.rowindex -= 1;
//                    } else if (applms.tabindex === 2) { // 返礼品の場合、互助会充当行分引く
//                        var len = applms.meisaiCol2.where({record_kbn: REC_KBN_J}).length;
//                        if (len > 0) {
//                            applms.rowindex -= 1;
//                        }
//                    }
//                    mesaiCol.add(m, {at: applms.rowindex});
//                } else {
//                    applms.rowindex += 1;
//                    mesaiCol.add(m, {at: applms.rowindex});
//                }
//                // 追加した行を選択
                this.$tabTarget.find(".meisai .list table tbody").eq(0).find("tr").click();
                // スクロールが出現される可能性があるためリサイズ発火
                $(window).trigger("resize");
            }
        },
        //　行削除処理
        delRow: function() {
            if (applms.tabindex > 0 && applms.rowindex > -1) {
                var mesaiCol = this.getMeisaiCol();
                var m = mesaiCol.at(applms.rowindex);
                // 追加区分 0:トランデータ, 1:見積画面新規入力データ
                if (m.get('add_kbn') === ADD_KBN_MITSU && !$.msiJqlib.isNullEx2(m.get('msi_no'))) {
                    // データの場合は削除コレクションに追加する
                    applms.meisaiDelCol.add(m.clone());
                }
                m.destroy();
                applms.rowindex = -1;
                this.calcSum();
                // スクロールがなくなる可能性があるためリサイズ発火
                $(window).trigger("resize");
            }
            // 削除ボタンを非活性化
            applms.setDisabledButtonStyle(this.$(".function #btn_del"));
            // 追加ボタンを活性化
            applms.clearDisabledButtonStyle(this.$(".function #btn_add"));
        },
        // 明細行の金額の小計と合計計算処理
        calcSum: function() {
            if (applms.tabindex > 0) {
                var mesaiCol = applms.appView.getMeisaiCol();
                var shokei = 0;
                var gokei = 0;
                var hoshi = 0;
                var gojokai_nebiki_prc_shokei = 0;
                var nebiki_prc_shokei = 0;
                var nebiki_prc2_shokei = 0;
                var gojokai_nebiki_prc_gokei = 0;
                var nebiki_prc_gokei = 0;
                var nebiki_prc2_gokei = 0;
                _.each(mesaiCol.models, function(m) {
                    var prc = m.get('juchu_prc_kei');
                    var gojokai_nebiki_prc = m.get('gojokai_nebiki_prc');
                    var nebiki_prc = m.get('nebiki_prc');
                    var nebiki_prc2 = m.get('nebiki_prc2');
                    var hoshi_prc = m.get('hoshi_prc');
                    var recordKbn = m.get('record_kbn');// レコード区分 1:タイトル行, 2:明細行, 3:小計行,4:奉仕料行 9:合計行
                    var hoshiKbn = m.get('hoshi_umu_kbn');// 奉仕料有無区分 0：奉仕料なし 1：奉仕料あり

                    if (hoshiKbn === '1') {
                        if (!$.msiJqlib.isNullEx2(hoshi_prc) && !isNaN(hoshi_prc)) {
                            hoshi += Number(m.get('hoshi_prc'));
                        }
                    }
                    if (!$.msiJqlib.isNullEx2(prc) && !isNaN(prc)) {
                        if (recordKbn === REC_KBN_M) { // 2:明細行
                            shokei += Number(prc);
                            gokei += Number(prc);
                            gojokai_nebiki_prc_shokei += Number(gojokai_nebiki_prc);
                            nebiki_prc_shokei += Number(nebiki_prc);
                            nebiki_prc2_shokei += Number(nebiki_prc2);
                            gojokai_nebiki_prc_gokei += Number(gojokai_nebiki_prc);
                            nebiki_prc_gokei += Number(nebiki_prc);
                            nebiki_prc2_gokei += Number(nebiki_prc2);
                        } else if (recordKbn === REC_KBN_S) {// 3:小計行
                            m.set('juchu_prc_kei', shokei);
                            m.set('gojokai_nebiki_prc_kei', gojokai_nebiki_prc_shokei);
                            m.set('nebiki_prc_kei', nebiki_prc_shokei);
                            m.set('nebiki_prc2_kei', nebiki_prc2_shokei);
                            shokei = 0;
                            gojokai_nebiki_prc_shokei = 0;
                            nebiki_prc_shokei = 0;
                            nebiki_prc2_shokei = 0;
                        } else if (recordKbn === REC_KBN_H) {// 4:奉仕料行
                            m.set('juchu_prc', hoshi);
                            m.set('juchu_prc_kei', hoshi);
                            gokei += Number(hoshi);
                        } else if (recordKbn === REC_KBN_G) {// 9:合計行
                            m.set('juchu_prc_kei', gokei);
                            m.set('gojokai_nebiki_prc_kei', gojokai_nebiki_prc_gokei);
                            m.set('nebiki_prc_kei', nebiki_prc_gokei);
                            m.set('nebiki_prc2_kei', nebiki_prc2_gokei);
     m.set('juchu_prc', (gokei-gojokai_nebiki_prc_gokei-nebiki_prc_gokei-nebiki_prc2_gokei));// mihara patch not 軽減 keigen
                        }
                    }

                });
            }
        },
        // タブインデックスによる明細コレクションを取得する
        getMeisaiCol: function() {
            switch (applms.tabindex) {
                case 0:
                    return applms.summaryCol;
                case 1:
                    return applms.meisaiCol1;
                case 2:
                    return applms.meisaiCol2;
                case 3:
                    return applms.meisaiCol3;
                case 4:
                    return applms.meisaiCol4;
                case 5:
                    return applms.meisaiCol5;
            }
        },
        // タブインデックスによる明細コレクションの件数を取得する
        getMeisaiColLength: function() {
            return this.getMeisaiCol().length;
        },
        isInputOk: function() {
            var aMsg = [];

            var meisaiCol = [applms.meisaiCol1, applms.meisaiCol2, applms.meisaiCol3, applms.meisaiCol4, applms.meisaiCol5];
            _.each(meisaiCol, function(col) {
                col.each(function(m, i) {
                    var addKbn = m.get('add_kbn');
                    if (addKbn === ADD_KBN_MITSU) {
                        var resLine = m.validate();
                        if (resLine) {
                            _.each(resLine, function(v, k) {
                                if ($.inArray(v, aMsg) < 0) {
                                    aMsg.push(v);
                                }
                                ;
                            });
                        }
                    }
                });
            });

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#estimate-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#estimate1-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#estimate2-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                } else if (this.$("#estimate3-tab").find(errClsNm).length) {
                    $li.eq(3).find("a").click();
                } else if (this.$("#estimate4-tab").find(errClsNm).length) {
                    $li.eq(4).find("a").click();
                } else if (this.$("#estimate5-tab").find(errClsNm).length) {
                    $li.eq(5).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }

            // OK 
            $.msiJqlib.clearAlert();
            return true;
        },
        mitsukakutei: function() {
            applms.mitsukakutei(this);
        },
        mitsutorikesi: function() {
            this.exeCheck();
//            applms.mitsutorikesi(this);
        },
        doPrint_konbini:function() {
            applms.doPrint_konbini(this);
        },
        exeCheck: function() {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkgetujifixmitsu',
                data: {dataAppJson:JSON.stringify(applms.appModel.toJSON())},
                type: 'POST',
                success: function(mydata) {
                    $.msiJqlib.setProgressing( false );
                    if (mydata.status === 'OK') {
                        applms.mitsutorikesi(that);
                    } else if (mydata.status === 'INFO') {
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        applms.mitsutorikesi(that);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doSave: function() {
            $.msiJqlib.clearAlert();
            if (!this.isInputOk()) {
                return;
            }
            var appEq = $.msiJqlib.isEqual(applms.appModel.toJSON(), applms.orgDataApp);
            // 明細イコール
            var meisaiEq1 = $.msiJqlib.isEqual(applms.meisaiCol1.toJSON(), applms.orgDataCol1);
            var meisaiEq2 = $.msiJqlib.isEqual(applms.meisaiCol2.toJSON(), applms.orgDataCol2);
            var meisaiEq3 = $.msiJqlib.isEqual(applms.meisaiCol3.toJSON(), applms.orgDataCol3);
            var meisaiEq4 = $.msiJqlib.isEqual(applms.meisaiCol4.toJSON(), applms.orgDataCol4);
            var meisaiEq5 = $.msiJqlib.isEqual(applms.meisaiCol5.toJSON(), applms.orgDataCol5);

            if (appEq && meisaiEq1 && meisaiEq2 && meisaiEq3 && meisaiEq4 && meisaiEq5) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                meisaiChangeFlg1: !meisaiEq1,
                meisaiChangeFlg2: !meisaiEq2,
                meisaiChangeFlg3: !meisaiEq3,
                meisaiChangeFlg4: !meisaiEq4,
                meisaiChangeFlg5: !meisaiEq5
            });
            // app情報
            var dataAppJson = JSON.stringify(applms.appModel.toJSON());
            // 明細情報
            var dataCol1 = this.filterCol(applms.meisaiCol1); // 法要・墓参り
            var dataCol2 = this.filterCol(applms.meisaiCol2); // 返礼品
            var dataCol3_2 = this.filterCol(applms.meisaiCol3); // 飲食費(壇払い) 
            var dataCol4 = this.filterCol(applms.meisaiCol4); // 立替金
            var dataCol5_1 = this.filterCol2(applms.meisaiCol5, CHU_BETO); // その他(別途費用) 中分類コード:0240
            var dataCol5_2 = this.filterCol2(applms.meisaiCol5, CHU_NEBIKI); // その他(値引き) 中分類コード:0120

            var dataColJson1 = JSON.stringify(dataCol1);
            var dataColJson2 = JSON.stringify(dataCol2);
            var dataColJson3_1 = JSON.stringify([]);
            var dataColJson3_2 = JSON.stringify(dataCol3_2);
            var dataColJson4 = JSON.stringify(dataCol4);
            var dataColJson5_1 = JSON.stringify(dataCol5_1);
            var dataColJson5_2 = JSON.stringify(dataCol5_2);
            // 明細削除データ
            var dataDelColJson = JSON.stringify(applms.meisaiDelCol.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/estimatesave',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson1: dataColJson1,
                    dataColJson2: dataColJson2,
                    dataColJson3_1: dataColJson3_1,
                    dataColJson3_2: dataColJson3_2,
                    dataColJson4: dataColJson4,
                    dataColJson5_1: dataColJson5_1,
                    dataColJson5_2: dataColJson5_2,
                    dataDelColJson: dataDelColJson,
                    changeFlg: changeFlg
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        filterCol: function(meisaiCol) {
            var dataCol = meisaiCol.filter(function(item) {
                return item.get("record_kbn") === REC_KBN_M;
            });
            return dataCol;
        },
        filterCol2: function(meisaiCol, chubunruiCd) {
            var dataCol = meisaiCol.filter(function(item) {
                return item.get("record_kbn") === REC_KBN_M && item.get("chu_bunrui_cd") === chubunruiCd;
            });
            return dataCol;
        },
        // 見積確認書
        doConfirmPrint: function () {
            var sekoNo = applms.appModel.get("seko_no");
            var dataKbn = '2';
            var printKbn = 1;
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/kanri/pdf0602',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    data_kbn: dataKbn,
                    printKbn: printKbn
                }
            });
        },
        doPrint: function() {
            if (this.isChangeShowWarn()) {
                return;
            }
            var sekoNo = applms.appModel.get("seko_no");
//            var moushiKbn = applms.appModel.get("moushi_kbn");
            var printKbn = applms.appModel.get("print_kbn");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0113',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    data_kbn: '2',
                    printKbn: printKbn,
                    output_kbn: 1
                }
            });
            return;
//            var $form = $("#estimate-form");
//            $.msiJqlib.setHiddenInput('preview', 'off', $form);
//            $.msiJqlib.setHiddenInput('seko_no', sekoNo, $form);
//            $.msiJqlib.setHiddenInput('data_kbn', moushiKbn, $form);
//            $form.attr('action', $.msiJqlib.baseUrl() + applms.printURI);
//            $form.attr('method', 'POST');
//            $form.submit();
        },
        doCancel: function() {
            applms.doCancel(this);
        },
        doShonin: function() {
            applms.doShonin(this);
        },
        isChangeShowWarn: function() {
            $.msiJqlib.clearAlert();
            if (!this.mesaiIsEq()) {
                $.msiJqlib.showWarn(MSG_WARN_1);
                return true;
            } else {
                return false;
            }
        }
    }); // AppView

    /**
     * @description見積書または請求書タブ処理
     */
    // 見積書または請求書モデル
    var SummaryModel = Backbone.Model.extend({
        defaults: function() {
            return {
                title: null, // タイトル
                sub: null, // サブタイトル
                price: null, // 金額
                row_kbn: '3' // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            };
        }
    }); // SummaryModel

    // 見積書または請求書コレクション
    var SummaryCollection = Backbone.Collection.extend({
        model: SummaryModel
    });
    // 見積書または請求書ビュー
    var SummaryView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl1: _.template($('#tmpl-mitsu-head').html()), // 見積書または請求書タブヘッダーテンプレート
        tmpl2: _.template($('#tmpl-mitsu-meisai').html()), // 見積書または請求書タブ明細テンプレート
        events: {
            "click .checkable": "setActive"
        },
        bindings: {
            '.title': 'title',
            '.sub': 'sub',
            '.price': {
                observe: 'price',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            }
        },
        commaOmit: function(val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val) {
            return $.msiJqlib.commaAdd(val);
        },
        initialize: function() {
            this.render();
        },
        render: function() {
            // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            var rowKbn = this.model.get("row_kbn");
            if (rowKbn === ROW_KBN_H || rowKbn === ROW_KBN_S) { // 1:ヘッダー行 4:消費税等行
                this.$el.html(this.tmpl1(this.model.toJSON()));
                this.$el.addClass("head");
            } else if (rowKbn === ROW_KBN_G) {// 9:合計行
                this.$el.html(this.tmpl1(this.model.toJSON()));
            } else { // 明細行
                this.$el.html(this.tmpl2(this.model.toJSON()));
                this.$el.addClass("shosai");
                var $tr = this.$("tr");
                if (rowKbn === ROW_KBN_T) {
                    $tr.addClass("plan");
                } else if (rowKbn === ROW_KBN_D) {
                    $tr.addClass("detail");
                }
            }
            this.toggleClass();
            this.stickit();
            return this;
        },
        // 金額がマイナスのときに赤字クラスを追加する処理
        toggleClass: function() {
            var prc = this.model.get('price');
            if (!$.msiJqlib.isNullEx2(prc) && prc < 0) {
                this.$('.price').addClass('com-akaji');
            } else {
                this.$('.price').removeClass('com-akaji');
            }
        }
    });

    /**
     * @description 法要・墓参り費用・返礼品・飲食費・立替・その他タブ処理
     */
    // 明細モデル
    var MeisaiModel = Backbone.Model.extend({
        defaults: function() {
            return {
                dai_bunrui_cd: null, // 大分類コード
                chu_bunrui_cd: null, // 中分類コード
                chu_bunrui_nm: null, // 中分類名
                shohin_kbn: null, // 商品区分
                shohin_kbn_nm: null, // 商品区分名
                shohin_cd: null, // 商品コード
                shohin_nm: null, // 商品名称
                shohin_tkiyo_nm: '', // 商品摘要
                mokuteki_kbn: '0', // 目的区分
                juchu_suryo: null, // 数量
                juchu_tnk: null, // 単価
                juchu_prc: null, // 受注金額
                gojokai_nebiki_prc: null, // 付帯値引き
                nebiki_prc: null, // 値引き額
                nebiki_prc2: null, // 値引き額
                juchu_prc_kei: null, // 受注金額差引計
                gojokai_nebiki_prc_kei: null, //付帯・割引計
                nebiki_prc_kei: null, // 割引金額計
                nebiki_prc2_kei: null, // 割引金額計
                hoshi_umu_kbn: null, // 奉仕料有無区分
                hoshi_prc: null, // 奉仕料金額
                tab_kbn: null, // タブ区分 1:法要・墓参り, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ,
                tab_sub_kbn: null, // タブサブ区分 同じタブ内の複数分類がある場合1～連番
                nebiki_kbn: '0', // 値引き区分 0:通常商品, 1:互助会値引き商品
                record_kbn: '2', // レコード区分 1:タイトル行, 2:明細行, 3:小計行,4:奉仕料行 9:合計行
                checkable_kbn: '1', // 行選択可能区分 0:選択不可, 1:選択可
                add_down_kbn: '1', // 選択行下に行追加可能区分 0:追加不可, 1:追加可
                del_kbn: '1', // 行削除可能区分 0:削除不可, 1:削除可
                add_kbn: '0', // 追加区分 0:トランデータ, 1:見積画面新規入力データ 9:写真加工等で自動作成データ
//                data_up_flg: '0', // データ更新対象フラグ 0:対象外 1:対象(画面より変更)
                'zei_cd': null, // 軽減税率対応 keigen mihara
                'reduced_tax_rate': null, // 軽減税率対応  軽減税率区分: 1：対象外 2：軽減8%  keigen mihara
                'waribiki_kbn': null, // 値引区分
                'upgrade_kbn': '0', // アップグレード区分
            };
        },
        validation: {
            shohin_cd: {
                required: true
            },
            shohin_nm: {
                required: true,
                maxLength: 40
            },
            shohin_tkiyo_nm: {
                required: false,
                maxLength: 40
            },
            juchu_suryo: {
                required: true,
                pattern: 'number'
            },
            juchu_tnk: {
                required: true,
                pattern: 'number'
            },
            gojokai_nebiki_prc: {
                required: true,
                pattern: 'number'
            },
            nebiki_prc: {
                required: true,
                pattern: 'number',
                customFun: function (value) {
                    if (value > 0) {
                        return 'ゼロより上は入力できません。';
                    }
                }
            },
            nebiki_prc2: {
                required: true,
                pattern: 'number',
                customFun: function (value) {
                    if (value > 0) {
                        return 'ゼロより上は入力できません。';
                    }
                }
            }
        },
        labels: {
            shohin_nm: '品目',
            shohin_tkiyo_nm: '摘要',
            juchu_suryo: '数量',
            juchu_tnk: '単価',
            gojokai_nebiki_prc: '付帯・割引',
            nebiki_prc: '割引額',
            nebiki_prc2: '値引額'
        }
    }); // MeisaiModel

    // 明細コレクション
    var MeisaiCollection = Backbone.Collection.extend({
        model: MeisaiModel
                //comparator: ''
    });

    // 明細ビュー
    var MeisaiView = Backbone.View.extend({
        tagName: 'tbody',
        // 1:明細行（小計含む） 2:合計行
        tmpl_kei1: _.template($('#tmpl-meisai-kei-1').html()),
        tmpl_kei2: _.template($('#tmpl-meisai-kei-2').html()),
        tmpl1: _.template($('#tmpl-meisai-1').html()),  // 法要
        tmpl2: _.template($('#tmpl-meisai-2').html()),  // 料理
        tmpl_add: _.template($('#tmpl-meisai-add').html()),
        tmpl_add2: _.template($('#tmpl-meisai-add-2').html()),
        events: {
            "click .checkable": "setActive",
            "click .juchu_tnk": "preventSelIfReadOnly",
            "click .juchu_suryo": "preventSelIfReadOnly",
            "click .gojokai_nebiki_prc": "preventSelIfReadOnly",
            "click .nebiki_prc": "preventSelIfReadOnly",
            "click .nebiki_prc2": "preventSelIfReadOnly",
            "click .dlg_shohin": "itemHelper",
            "change .hoshi_check": "setHoshi",
            "click .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen mihara
            "keyup .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen mihara
            "click .dialog-item": "showItemDialog",
        },
        // 軽減税率対応 消費税選択 mihara keigen
        showKeigenSel2: function(ev) {
            msiGlobalObj.keigenLib.f01ShowKeigenSel2.apply(this, [ev]);
        },

        bindings: {
            '.shohin_kbn_nm': 'shohin_kbn_nm',
            '.shohin_nm': 'shohin_nm',
            '.shohin_tkiyo_nm': {
//                observe: ['shohin_tkiyo_nm', 'hoshi_umu_kbn'],
                observe: 'shohin_tkiyo_nm'
//                onGet: function(val) {
//                    if (this.model.get('hoshi_umu_kbn') === '1' && this.model.get('chu_bunrui_cd') === CHU_DANBARAI) {
//                        if (!$.msiJqlib.isNullEx2(val)) {
//                            return KOME + " " + val;
//                        } else {
//                            return KOME;
//
//                        }
//                    } else {
//                        return val;
//                    }
//                }
            },
            '.houshi': {
                observe: 'hoshi_umu_kbn',
                onGet: function (val) {
                    if (this.model.get("record_kbn") === REC_KBN_H) {
                        return KOME + KAKE + applms.appModel.get('hoshi_ritu_org') + PERCENT;
                    } else {
                        if (val === '1' && this.model.get('chu_bunrui_cd') === CHU_DANBARAI) {
                            return KOME;
                        } else {
                            return null;
                        }
                    }
                }
            },
            '.juchu_suryo': {
                observe: 'juchu_suryo',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.juchu_tnk': {
                observe: 'juchu_tnk',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.juchu_prc': {
                observe: 'juchu_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.gojokai_nebiki_prc': {
                observe: 'gojokai_nebiki_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nebiki_prc': {
                observe: 'nebiki_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nebiki_prc2': {
                observe: 'nebiki_prc2',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.juchu_prc_kei': {
                observe: 'juchu_prc_kei',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.gojokai_nebiki_prc_kei': {
                observe: 'gojokai_nebiki_prc_kei',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nebiki_prc_kei': {
                observe: 'nebiki_prc_kei',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nebiki_prc2_kei': {
                observe: 'nebiki_prc2_kei',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            // 軽減税率対応 keigen mihara
            '.zei_cd': {
                observe: 'zei_cd',
                onGet: function(val, options) { 
                    msiGlobalObj.keigenLib.f01UpdKeigenZeiCd(val, this);
                    return val;
                },
                afterUpdate: function($el, val, options) {
                    msiGlobalObj.keigenLib.f01UpdKeigenZeiCd(val, this);
                },
            },
            '.cls_waribiki_kbn': {
                observe: 'waribiki_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
        },
        commaOmit: function(val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val) {
            return $.msiJqlib.commaAdd(val);
        },
        initialize: function() {
            this.listenTo(this.model, 'destroy', this.remove);
            this.listenTo(this.model, 'change:juchu_tnk change:juchu_suryo change:waribiki_kbn', this.setNebiki);
            this.listenTo(this.model, 'change:juchu_tnk change:juchu_suryo change:gojokai_nebiki_prc change:nebiki_prc change:nebiki_prc2 change:waribiki_kbn', this.calcGokei);
            this.listenTo(this.model, 'change:juchu_tnk change:juchu_suryo change:gojokai_nebiki_prc change:nebiki_prc change:nebiki_prc2', this.toggleClass);
//            this.listenTo(this.model, 'change', this.setUpFlg);
            Backbone.Validation.bind(this);
//            this.render();
        },
        render: function() {
            Backbone.Validation.bind(this, {
                valid: function(view, attr) {
                    _valid(view, attr);
                },
                invalid: function(view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            // レコード区分
            var recordKbn = this.model.get("record_kbn");
            if (recordKbn === REC_KBN_G) { // 合計行
                if (tabKbn === TAB_KBN_I) {
                    this.$el.html(this.tmpl_kei2(this.model.toJSON()));
                } else {
                    this.$el.html(this.tmpl_kei1(this.model.toJSON()));
                }
            } else { // 明細行(小計含む)
                // 追加区分
                var addKbn = this.model.get("add_kbn");
                // タブ区分
                var tabKbn = this.model.get("tab_kbn");
                // 発注済み 0:未発注 1:発注済み
                var orderFlg = this.model.get("order_flg");
                if ((addKbn === ADD_KBN_MITSU) && $.msiJqlib.isNullEx(applms.appModel.get('jichu_kakute_ymd')) && orderFlg !== '1') { // 画面で追加されたデータかつ未確定
                    if (tabKbn === TAB_KBN_I) {
                        this.$el.html(this.tmpl_add2(this.model.toJSON()));
                        // 中分類コード
                        var chu_bunrui_cd = this.model.get("chu_bunrui_cd");
                        if (chu_bunrui_cd === CHU_DANBARAI) { // 壇払い
                            this.setCheckBoxStatus();
                        } else {
                            this.$(".hoshi_check").hide();
                        }
                    } else {
                        this.$el.html(this.tmpl_add(this.model.toJSON()));
                    }
                    this.afterPickUp();
                } else {
                    if (tabKbn === TAB_KBN_I) { // 飲食費タブ
                        this.$el.html(this.tmpl2(this.model.toJSON()));
                    } else {
                        this.$el.html(this.tmpl1(this.model.toJSON()));
                    }
                    if (orderFlg === '1') {
                        this.$el.find("tr").attr("title", "発注済みです");
                    }
                }
                this.setCssClass();
            }
            this.toggleClass();
            this.stickit();
            this.setSelect2();
            return this;
        },
        // 各種区分によるCSSクラスを設定する
        setCssClass: function() {
            var $tr = this.$("tr");
            // レコード区分
            var recordKbn = this.model.get("record_kbn");
            if (recordKbn === REC_KBN_T) {
                // タイトル行の場合
                $tr.addClass("plan");
            } else {
                $tr.addClass("detail");
            }
            // 値引き区分
            var nebikiKbn = this.model.get("nebiki_kbn");
            // 中分類コードが 0260:値引きも赤表示
            var chuBunruiCd = this.model.get("chu_bunrui_cd");
            if (nebikiKbn === NEBIKI_KBN_NEBIKI || chuBunruiCd === CHU_NEBIKI) {
                $tr.addClass("minus");
            }
            // 行選択可能区分
            var checkKbn = this.model.get("checkable_kbn");
            if (checkKbn === CHECK_KBN_OK) {
                $tr.addClass("checkable");
            }
            // 奉仕料行は摘要を右寄せ
            if (recordKbn === REC_KBN_H) {
                $tr.find(".summary").css("text-align", "right");
            }

        },
        // 明細行選択処理 ORG:main.jsの /* 見積り行選択 */
        setActive: function(e) {
            var $tr = this.$("tr");
            // 選択した行のインデックス 
            applms.rowindex = $tr.closest("table").find("tbody").index($tr.closest("tbody"));

            // 軽減税率設定要素の場合は選択状態を変更しない  mihara keigen
            // if ( $(e.target).closest('td').hasClass("keigen_disp") ) {
            //      return;
            // }

            // 入力項目の場合は選択状態を変更しない
            if ($(e.target).hasClass("input_field")) {
                $('.list tbody tr').removeClass('active');
                $tr.toggleClass('active', true);
            } else {
                if ($tr.hasClass('active')) {
                    $tr.removeClass('active');
                    // 行インデックスインデックス初期化
                    applms.rowindex = -1;
                } else {
                    $('.list tbody tr').removeClass('active');
                    $tr.addClass('active');
                }
            }
            this.setAddAndDelButton();

        },
        // 行追加と削除のボタン制御処理
        setAddAndDelButton: function() {

            var $tr = this.$("tr");
            // 親のタブ
            var $tab = $tr.closest(".tab-contents");
            // 選択行下に行追加可能区分 0:追加不可, 1:追加可
            var addDownKbn = this.model.get("add_down_kbn");
            // 行選択されていない、または追加可の場合
            if (!$tr.hasClass('active') || addDownKbn === '1') {
                applms.clearDisabledButtonStyle($tab.find("#btn_add"));
            } else {
                applms.setDisabledButtonStyle($tab.find("#btn_add"));
            }
            // 追加区分 0:トランデータ, 1:見積画面新規入力データ（トランに保存済み）, 2:見積画面新規入力データ（トランに保存前）
            var addKbn = this.model.get("add_kbn");
            // 発注済み 0:未発注 1:発注済み
            var orderFlg = this.model.get("order_flg");
            if ((addKbn === ADD_KBN_MITSU) && applms.rowindex !== -1 && orderFlg !== '1') {
                applms.clearDisabledButtonStyle($tab.find("#btn_del"));
            } else {
                applms.setDisabledButtonStyle($tab.find("#btn_del"));
            }
        },
        // 商品ヘルパー処理
        itemHelper: function() {
            var m = this.model;
            var that = this;
            var wheredata = this.getWhere();
            wheredata['kijun_ymd'] = applms.appModel.get('seko_juchu_ymd') || null; // 軽減税率対応  keigen mihara
            wheredata.s_hihyoji_kbn = 0;
            this.$el.msiPickHelper({
                action: 'shohin0',
                mydata: wheredata,
                onSelect: function(data) {
                    // console.log('itemHelper data=>', data);
                    m.set({
                        'dai_bunrui_cd': data.dai_bunrui_cd
                        , 'chu_bunrui_cd': data.chu_bunrui_cd
                        , 'shohin_kbn': data.shohin_kbn
                        , 'shohin_kbn_nm': data.shohin_kbn_nm
                        , 'shohin_cd': data.code
                        , 'shohin_nm': data.name
                        , 'shohin_tkiyo_nm': data.shohin_tkiyo_nm
                        , 'juchu_suryo': 1
                        , 'juchu_tnk': that.commaOmit(data.tanka)
                        , 'juchu_prc': that.commaOmit(data.tanka)
                        , 'gojokai_nebiki_prc': 0
                        , 'nebiki_prc': 0
                        , 'nebiki_prc2': 0
                        , 'hoshi_umu_kbn': data.hoshi_umu_kbn
                        , 'nm_input_kbn': data.nm_input_kbn
                        , 'siire_cd': data.siire_cd
                        , 'siire_lnm': data.siire_lnm
                        , 'gen_tnk': that.commaOmit(data.genka)
                        , 'tani_cd': data.tani_cd
                        , 'zei_kbn': data.uri_zei_kbn
                        , 'uri_kamoku_cd': data.uri_kamoku_cd
                        , 'tnk_chg_kbn': data.tnk_chg_kbn
                        , 'hachu_kbn': data.hachu_kbn
                        , 'mokuteki_kbn': data.mokuteki_kbn
                        , 'zei_cd': data.uri_zei_cd  // 軽減税率対応   keigen mihara
                        , 'reduced_tax_rate': data.uri_reduced_tax_rate_ex  // 軽減税率対応  keigen mihara
                        , 'upgrade_kbn': '0'
                    });
                    that.afterPickUp();
                },
                onClear: function() {
//                    m.set({
//                        'dai_bunrui_cd': null
//                        , 'chu_bunrui_cd': null
//                        , 'shohin_kbn': null
//                        , 'shohin_kbn_nm': null
//                        , 'shohin_cd': null
//                        , 'shohin_nm': null
//                        , 'shohin_tkiyo_nm': null
//                        , 'juchu_suryo': null
//                        , 'juchu_tnk': null
//                        , 'juchu_prc': null
//                        , 'hoshi_umu_kbn': null
//                        , 'nm_input_kbn': null
//                        , 'siire_cd': null
//                        , 'siire_lnm': null
//                        , 'gen_tnk': null
//                        , 'tani_cd': null
//                        , 'zei_kbn': null
//                        , 'uri_kamoku_cd': null
//                        , 'tnk_chg_kbn': null
//                    });
//                    that.afterPickUp();
                }
            });
        },
        // 商品ピックアップ条件を取得する
        getWhere: function() {
            switch (applms.tabindex) {
                case 1:
                    //  大分類 0110:法事 中分類 IN 0210:法要・墓参り
                    var hoyo = CHU_JIGO				// 1事後          
							 + ',' + CHU_IHAI   	// 2位牌          
							 + ',' + CHU_BUTUDAN	// 3仏壇           
							 + ',' + CHU_BOSHO  	// 4墓所・手元供養
							 + ',' + CHU_ANNAI  	// 5案内状        
							 + ',' + CHU_SAIDAN 	// 6祭壇・式場    
							 + ',' + CHU_HOYO   	// 8法要          
							 + ',' + CHU_BOSAN  	// 9墓参          
							 + ',' + CHU_HOUJI;  	// 10法事・催事   
                    return {s_dai_bunrui_cd: DAI_HOUJI, s_chu_bunrui_cd: hoyo};
                case 2:
                    // 大分類 0110:法事 中分類 IN 0220:返礼品
                    return {s_dai_bunrui_cd: DAI_HOUJI, s_chu_bunrui_cd: CHU_HENREI};
                case 3:
                    // 大分類 0110:法事 中分類 IN 0230:壇払
                    return {s_dai_bunrui_cd: DAI_HOUJI, s_chu_bunrui_cd: CHU_DANBARAI};
                case 4:
                    // 大分類 0110:法事 IN 0100:立替金
                    return {s_dai_bunrui_cd: DAI_HOUJI, s_chu_bunrui_cd: CHU_TATE};
                case 5:
                    // 大分類 0110:法事, 中分類 IN 0240:別途費用,
                    var beto = CHU_BETO + ',' + CHU_NEBIKI;
                    return {s_dai_bunrui_cd: DAI_HOUJI, s_chu_bunrui_cd: beto};
//                    // 大分類 0050:別途費用,0070:値引 中分類 IN 0090:別途費用,0120:値引き
//                    return {s_dai_bunrui_cd: '0050,0070', s_chu_bunrui_cd: '0090,0120'};
            }

            msiGlobalObj.keigenLib.f01UpdKeigenZeiCd(this.model.get('zei_cd'), this); // 軽減税率対応  keigen mihara
        },
        // pickup終了後処理
        afterPickUp: function() {
            // 名称入力区分による制御
            this.setInputType();
            // 売上単価変更区分による制御
            this.setTankaType();
            // 受注数量制御
            if ($.msiJqlib.isNullEx2(this.model.get("juchu_suryo"))) {
                this.$('.juchu_suryo').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            } else {
                this.$('.juchu_suryo').removeAttr(ATTR_NO_INPUT);
            }
            // 奉仕料チェックボックス制御
            if (this.model.get("chu_bunrui_cd") === CHU_DANBARAI) {
                this.$(".hoshi_check").show();
                this.setCheckBoxStatus();
            } else {
                this.$(".hoshi_check").hide();
            }
            if ($.msiJqlib.isNullEx2(this.model.get("gojokai_nebiki_prc"))) {
                this.$('.gojokai_nebiki_prc').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            } else {
                this.$('.gojokai_nebiki_prc').removeAttr(ATTR_NO_INPUT);
            }
            this.$('.nebiki_prc').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            if ($.msiJqlib.isNullEx2(this.model.get("nebiki_prc2"))) {
                this.$('.nebiki_prc2').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            } else {
                this.$('.nebiki_prc2').removeAttr(ATTR_NO_INPUT);
            }

        },
        /**
         * 名称入力区分による制御
         * 1：名称変更なし（摘要も入力不可）、
         * 2：名称変更あり（摘要は入力不可）、
         * 3：摘要追加のみ（摘要入力可）、
         * 4：名称＋摘要（両方入力可）
         */
        setInputType: function() {
            var nmInputKbn = this.model.get("nm_input_kbn");
            if (nmInputKbn === '2' || nmInputKbn === '4') {
                // 商品名入力可にする
                this.$('.shohin_nm').removeAttr(ATTR_NO_INPUT);
            } else {
                // 商品名入力不可にする
                this.$('.shohin_nm').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
            if (nmInputKbn === '3' || nmInputKbn === '4') {
                // 摘要入力可にする
                this.$('.shohin_tkiyo_nm').removeAttr(ATTR_NO_INPUT);
            } else {
                // 摘要入入力不可にする
                this.$('.shohin_tkiyo_nm').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
        },
        /**
         * 売上単価変更区分による制御
         * 0：単価変更不可、1：単価変更可
         */
        setTankaType: function() {
            var taniKbn = this.model.get("tnk_chg_kbn");
            if (taniKbn === "1") {
                this.$('.juchu_tnk').removeAttr(ATTR_NO_INPUT);
            } else {
                this.$('.juchu_tnk').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
        },
        // 奉仕料チェックボックスチェック処理
        setHoshi: function(e) {
//            e.stopImmediatePropagation();　// クリック処理でやる必要がある
            this.setCheckBoxModel(e, 'hoshi_umu_kbn');
            this.calcGokei();
        },
        // チェックボックス切り替え処理
        setCheckBoxModel: function(e, attr) {
            var val = this.$('.hoshi_check:checked').val();
            this.model.set(attr, val === "1" ? "1" : "0");
        },
        // チェックボックス初期設定処理
        setCheckBoxStatus: function() {
            // 奉仕料チェックボックス初期設定処理
            if (this.model.get("hoshi_umu_kbn") === "1" && this.$('.hoshi_check:checked').val() !== "1") {
                this.$('.hoshi_check').attr("checked", "checked");
            }
        },
        setNebiki: function () {
            var tm = this.model;
            var waribiki_kbn = tm.get('waribiki_kbn');
            var tnk = Number(tm.get("juchu_tnk"));
            var suryo = tm.get("juchu_suryo");
            var nebikiMst = data.dataApp.nebikiKbn
            // コード名称マスタの備考で値引設定方法を決定する
            // 現状はritsuのみ
            if ($.msiJqlib.isNullEx2(waribiki_kbn)) {
                tm.set('nebiki_prc', 0);
            }
            _.each(nebikiMst, function (val) {
                if (val.kbn_value_cd == waribiki_kbn) {
                    if (!$.msiJqlib.isNullEx2(val.biko) && val.biko.length > 0) {
                        if (val.biko == 'ritsu') {
                            tm.set('nebiki_prc', (tnk * suryo) * val.kbn_value_snm * -1);
                        }
                    }
                }
            });
        },
        // 一行の合計処理
        calcGokei: function() {
            var m = this.model;
            var tnk = Number(m.get("juchu_tnk"));
            var suryo = Number(m.get("juchu_suryo"));
            var gojokai_nebiki_prc = Number(m.get("gojokai_nebiki_prc"));
            var nebiki_prc = Number(m.get("nebiki_prc"));
            var nebiki_prc2 = Number(m.get("nebiki_prc2"));
            if (!$.msiJqlib.isNullEx2(tnk) && !$.msiJqlib.isNullEx2(suryo) && !isNaN(tnk) && !isNaN(suryo)) {
                var hoshiPrc = 0;
                var hasuKbn = applms.appModel.get('hasu_kbn');
                var hoshiRitu = applms.appModel.get('hoshi_ritu');
                // 奉仕料有り 
                if (m.get("hoshi_umu_kbn") === "1") {
                    // 丸め処理 0:切捨て 1:四捨五入 2:切上げ
                    if (hasuKbn === "0") {
                        hoshiPrc = Math.floor(((tnk * suryo) + nebiki_prc + nebiki_prc2) * hoshiRitu);
                    } else if (hasuKbn === "1") {
                        hoshiPrc = Math.round(((tnk * suryo) + nebiki_prc + nebiki_prc2) * hoshiRitu);
                    } else if (hasuKbn === "2") {
                        hoshiPrc = Math.ceil(((tnk * suryo) * nebiki_prc + nebiki_prc2) * hoshiRitu);
                    }
                    m.set('hoshi_prc', hoshiPrc);
                } else {
                    m.set('hoshi_prc', 0);
                }
                m.set('juchu_prc', tnk * suryo);
                var juchu_prc_kei = m.get('juchu_prc');
                if (!$.msiJqlib.isNullEx2(gojokai_nebiki_prc) && !isNaN(gojokai_nebiki_prc)) {
                    juchu_prc_kei += gojokai_nebiki_prc * 1;
                }
                if (!$.msiJqlib.isNullEx2(nebiki_prc) && !isNaN(nebiki_prc)) {
                    juchu_prc_kei += nebiki_prc * 1;
                }
                if (!$.msiJqlib.isNullEx2(nebiki_prc2) && !isNaN(nebiki_prc2)) {
                    juchu_prc_kei += nebiki_prc2 * 1;
                }
                m.set('juchu_prc_kei', juchu_prc_kei);
            } else {
                m.set('juchu_prc', null);
                m.set('juchu_prc_kei', null);
            }
            // 小計・合計計算処理
            applms.appView.calcSum();

        },
        // 金額がマイナスのときに赤字クラスを追加する処理
        toggleClass: function() {
            var suryo = this.model.get('juchu_suryo');
            var tnk = this.model.get('juchu_tnk');
            var prc = this.model.get('juchu_prc');
            var gojokai_nebiki_prc = this.model.get("gojokai_nebiki_prc");
            var nebiki_prc = this.model.get("nebiki_prc");
            var nebiki_prc2 = this.model.get("nebiki_prc2");
            var juchu_prc_kei = this.model.get("juchu_prc_kei");
            var gojokai_nebiki_prc_kei = this.model.get("gojokai_nebiki_prc_kei");
            var nebiki_prc_kei = this.model.get("nebiki_prc_kei");
            var nebiki_prc2_kei = this.model.get("nebiki_prc2_kei");
            if (!$.msiJqlib.isNullEx2(suryo) && suryo < 0) {
                this.$('.juchu_suryo').addClass('com-akaji');
            } else {
                this.$('.juchu_suryo').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(tnk) && tnk < 0) {
                this.$('.juchu_tnk').addClass('com-akaji');
            } else {
                this.$('.juchu_tnk').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(prc) && prc < 0) {
                this.$('.juchu_prc').addClass('com-akaji');
            } else {
                this.$('.juchu_prc').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(gojokai_nebiki_prc) && gojokai_nebiki_prc < 0) {
                this.$('.gojokai_nebiki_prc').addClass('com-akaji');
            } else {
                this.$('.gojokai_nebiki_prc').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(nebiki_prc) && nebiki_prc < 0) {
                this.$('.nebiki_prc').addClass('com-akaji');
            } else {
                this.$('.nebiki_prc').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(nebiki_prc2) && nebiki_prc2 < 0) {
                this.$('.nebiki_prc2').addClass('com-akaji');
            } else {
                this.$('.nebiki_prc2').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(juchu_prc_kei) && juchu_prc_kei < 0) {
                this.$('.juchu_prc_kei').addClass('com-akaji');
            } else {
                this.$('.juchu_prc_kei').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(gojokai_nebiki_prc_kei) && gojokai_nebiki_prc_kei < 0) {
                this.$('.gojokai_nebiki_prc_kei').addClass('com-akaji');
            } else {
                this.$('.gojokai_nebiki_prc_kei').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(nebiki_prc_kei) && nebiki_prc_kei < 0) {
                this.$('.nebiki_prc_kei').addClass('com-akaji');
            } else {
                this.$('.nebiki_prc_kei').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(nebiki_prc2_kei) && nebiki_prc2_kei < 0) {
                this.$('.nebiki_prc2_kei').addClass('com-akaji');
            } else {
                this.$('.nebiki_prc2_kei').removeClass('com-akaji');
            }
        },
        // readonly時は自動選択しない
        preventSelIfReadOnly: function(e) {
            var $target = $(e.currentTarget);
            if ($target.attr(ATTR_NO_INPUT) !== ATTR_NO_INPUT) {
                $target.select();
            }
        },
        showItemDialog: function (e) {
            e.stopImmediatePropagation();
            applms.appModel.set('gamen_kbn', '2');
            var opt = {
                getUrl: '/juchu/mitsu/itemdialogex',
                putUrl: '/juchu/mitsu/savedialogitemex',
                'm': this.model,
                'appmodel': applms.appModel
            };
            m_dialog.showDialog(opt);
        },
        // select2設定処理
        setSelect2: function () {
            // 値引区分
            $.msiJqlib.setSelect2Com1(this.$(".cls_waribiki_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataApp.nebikiKbn)}, $.msiJqlib.setSelect2Default1)));
        }
    }); // MeisaiView

    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-estimate').text()));
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    // 初期化処理
    applms.summaryCol = new SummaryCollection(); // 見積書または請求書
    applms.meisaiCol = new MeisaiCollection(); // 法要・墓参り～その他
    applms.meisaiCol1 = new MeisaiCollection(); // 法要・墓参り
    applms.meisaiCol2 = new MeisaiCollection(); // 返礼品
    applms.meisaiCol3 = new MeisaiCollection(); // 飲食費
    applms.meisaiCol4 = new MeisaiCollection(); // 立替金
    applms.meisaiCol5 = new MeisaiCollection(); // その他
    applms.appModel = new AppModel();
    applms.appView = new AppView({model: applms.appModel});

    // サイドメニュー設定
    applms.setSideMenu();

    applms.resetData = function(dataApp, dataCol) {
        // 印刷区分
        $.msiJqlib.setSelect2Com1($("#print_kbn"), {data: $.msiJqlib.objToArray3(dataApp.printKbn)});
        if (!applms.initializeView(dataApp)) {
            return;
        }
        // 承認設定
        applms.setShonin(dataApp);
        
        $("#detail").show();
        $(".buttons #btn_print").show();// 印刷ボタン

        applms.meisaiDelCol = new MeisaiCollection();
        // APPデータを設定する
        applms.appModel.set(dataApp);
        // 明細コレクションを設定する
        applms.meisaiCol.set(dataCol);
        // タブごとに設定する
        _setHouyo();
        _setHenrei();
        _setInshoku();
        _setTatekae();
        _setSonota();
        // 見積書または請求書を設定する
        _setSummary();
        // データを退避する
        applms.orgDataApp = applms.appModel.toJSON();
        applms.orgDataCol1 = applms.meisaiCol1.toJSON();
        applms.orgDataCol2 = applms.meisaiCol2.toJSON();
        applms.orgDataCol3 = applms.meisaiCol3.toJSON();
        applms.orgDataCol4 = applms.meisaiCol4.toJSON();
        applms.orgDataCol5 = applms.meisaiCol5.toJSON();
    };

    applms.resetData(data.dataApp, data.dataCol);

    // リサイズ発火
    $(window).trigger("resize");
});
