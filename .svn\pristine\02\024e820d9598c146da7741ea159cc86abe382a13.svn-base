//コンビニ入金処理
var appupload = appupload || {};
$(function () {
	"use strict";

	var utils = window.msiBbUtils;

	// 画面クラスとモデルのプロパティのオブジェクト 
	appupload.pro = {
        no						:	'.no',						// NO.
		history_no				:	'.history_no',				// 履歴番号
		msi_no					:	'.msi_no',					// 明細番号
		nyukin_ymd				:	'.nyukin_ymd',				// 入金日
		uri_den_no				:	'.uri_den_no',				// 売上伝票№
		denpyo_no				:	'.denpyo_no',				// 受注伝票№
		seko_no					:	'.seko_no',					// 施行番号
		nyukin_denpyo_no		:	'.nyukin_denpyo_no',		// 入金伝票NO
		data_kbn				:	'.data_kbn',				// データ区分
		data_kbn_nm				:	'.data_kbn_nm',				// データ区分名
		data_sbt				:	'.data_sbt',				// データ種別
		data_sbt_nm				:	'.data_sbt_nm',				// データ種別名
		syunou_ymd				:	'.syunou_ymd',				// 収納日時
		seikyu_no				:	'.seikyu_no',				// 請求番号
		seikyu_nm				:	'.seikyu_nm',				// 請求先名
		souke_nm				:	'.souke_nm',				// 葬家名
		nyukin_prc				:	'.nyukin_prc',				// 入金金額
//		center_syori_ymd		:	'.center_syori_ymd',		// センター処理日
		furikomi_yoteibi		:	'.furikomi_yoteibi',		// 振込予定日
//		seisan_kijitu_ymd		:	'.seisan_kijitu_ymd',		// 清算期日
		convenience_cd			:	'.convenience_cd',			// ＣＶＳコード
		convenience_nm			:	'.convenience_nm',			// コンビニ企業名
		tenpo_cd				:	'.tenpo_cd',				// 店舗コード
		data_syutoku_ymd		:	'.data_syutoku_ymd',		// データ取得日
//		convenience_eigyo_ymd	:	'.convenience_eigyo_ymd',	// コンビニ営業日
		barcode_sbt				:	'.barcode_sbt',				// バーコード種別
		barcode1				:	'.barcode1',				// バーコード１段
//		barcode2				:	'.barcode2',				// バーコード２段
//		barcode3				:	'.barcode3',				// バーコード３段
//		barcode4				:	'.barcode4',				// バーコード４段
//		riaru_flg				:	'.riaru_flg',				// リアルフラグ
		bumon_cd				:	'.bumon_cd',				// 売上部門コード
		bumon_nm				:	'.bumon_nm',				// 売上部門名
		tanto_cd				:	'.tanto_cd',				// 売上担当者コード
		tanto_nm				:	'.tanto_nm',				// 担当者名			
	};
	var _select_syori_kbn = [
		{id: '1', text: '新規連結'},
		{id: '2', text: '履歴表示'},
	];

	// select2のvalを設定する
	var _setSelect2Val = function ($el, val) {
		$el.select2("val", val);
	};

	// 全体ビュー
	var AppView = Backbone.View.extend({
		el: $("#mst-form-id"),
		events: {
			"click #btn_save": "doSave",
			"click #btn_delete": "doDelete",
			"click #btn_cancel": "doCancel",
            "click #btn_csvout": "doPrint",
			"change #syori_kbn": "changeSelectData",
            "click #btn_search": "changeSelectData",
            "click .chkAllToggle": "toggleChkAll"
		},
		initialize: function () {
			this.listenTo(appupload.uploadCol, 'reset', this.addAllCol);
			this.listenTo(appupload.uploadCol, 'add'  , this.addOneNext);
			this.render();
			this.setSelect2();
		},
		addOne: function (m) {
			var v = new UploadView({model: m});
			this.$("#t_dtl").append(v.render().el);
		},
        addOneNext: function (m) {
            var index = m.get('no') - 2;
            if (index > 0) {
                var v = new UploadView({model: m});
                this.$("#t_dtl").find('tbody').eq(index).after(v.render().el);
            }
            //行番号再計算                
            appupload.setUploadData();
        },
		addAllCol: function (collection) {
			this.$("#t_dtl tbody").remove();
			if (appupload.uploadCol.length > 0) {
				collection.each(this.addOne, this);
			}
            appupload.setUploadData();
		},
		isInputOk: function () {
			$.msiJqlib.clearAlert();
			this.clearErr();
            var totalLines = 0;
			var aMsg = [], line;
			// 明細コレクションチェック            
			appupload.uploadCol.each(function (m, i) {
				var resLine = m.validate();
				if (resLine) {
					line = i + 1;
					_.each(resLine, function (v, k) {
						aMsg.push(line + '行目 ' + v);
					});
				}
                if ( m.get('_selected') ) {
                    totalLines++;
                }    
			});
			// NG
			if (aMsg.length > 0) {
				$.msiJqlib.showErr(aMsg.join(', '));
				return false;
			}
            //if ( totalLines < 1 ) {
            //    msiLib2.showWarn( 'ダウンロードする行を選択して下さい。' );
            //    return false;
            //}
			// OK
			return true;
		},
		//エラークラスクリア
		clearErr: function () {
			this.$el.msiErrClearAll({errCls: 'error1'});
		},
		// 保存
		doSave: function () {
			//エラーチェック
			if (!this.isInputOk()) {
				return;
			}
			//データ取得            
			var DataColJson = JSON.stringify(appupload.uploadCol);
			var syoriKbn = $.msiJqlib.getSelect2Val(this.$("#syori_kbn"));
			var taishostymd = this.model.get("taisho_st_ymd");
			var taishoedymd = this.model.get("taisho_ed_ymd");
                        appupload.setBtnDisable();
			$.ajax({
				url: $.msiJqlib.baseUrl() + '/saiken/convenienceupload/save',
				data: {
					DataColJson: DataColJson,
					syori_kbn: syoriKbn,
                    taisho_st_ymd : taishostymd,
                    taisho_ed_ymd : taishoedymd
				},
				type: 'POST',
				success: function (mydata) {
					if (mydata.status === 'OK') {
						appupload.reset(mydata);
						$.msiJqlib.showInfo(mydata.msg);
					} else {
						$.msiJqlib.showErr(mydata.msg);
					}
				}
			});

		},
		// 削除
		doDelete: function () {
			//エラーチェック
			if (!this.isInputOkDel()) {
				return;
			}
			//データ取得            
			var DataColJson = JSON.stringify(appupload.uploadCol);
			var syoriKbn = $.msiJqlib.getSelect2Val(this.$("#syori_kbn"));
			var taishostymd = this.model.get("taisho_st_ymd");
			var taishoedymd = this.model.get("taisho_ed_ymd");
                        appupload.setBtnDisable();
			$.ajax({
				url: $.msiJqlib.baseUrl() + '/saiken/convenienceupload/delete',
				data: {
					DataColJson: DataColJson,
					syori_kbn: syoriKbn,
                    taisho_st_ymd : taishostymd,
                    taisho_ed_ymd : taishoedymd
				},
				type: 'POST',
				success: function (mydata) {
					if (mydata.status === 'OK') {
						appupload.reset(mydata);
						$.msiJqlib.showInfo(mydata.msg);
					} else {
						$.msiJqlib.showErr(mydata.msg);
					}
				}
			});

		},
		isInputOkDel: function () {
			$.msiJqlib.clearAlert();
			this.clearErr();
            var totalLines = 0;
			var aMsg = [], line;
			// 明細コレクションチェック            
			appupload.uploadCol.each(function (m, i) {
				var resLine = m.validate();
				if (resLine) {
					line = i + 1;
					_.each(resLine, function (v, k) {
						aMsg.push(line + '行目 ' + v);
					});
				}
                if ( m.get('_selected') ) {
                    totalLines++;
                }    
			});
			// NG
			if (aMsg.length > 0) {
				$.msiJqlib.showErr(aMsg.join(', '));
				return false;
			}
            if ( totalLines < 1 ) {
                msiLib2.showWarn( '削除する行を選択して下さい。' );
                return false;
            }
			// OK
			return true;
		},
		doCancel: function () {
			if (!confirm('初期状態に戻してよろしいですか？')) {
				return;
			}
			window.location.reload();
		},
        //CSV出力
        doPrint: function() {
            if ( ! this.isInputOk() ) {
                return;
            }
            var that = this;
			var syoriKbn = $.msiJqlib.getSelect2Val(this.$("#syori_kbn"));
            var appObj         = this.model.toJSON();
            appObj["csv"]       = true;             // CSV出力
            appObj["syori_kbn"] = syoriKbn;     // 選択分類
			var DataColJson = JSON.stringify(appupload.uploadCol);
            var url = $.msiJqlib.baseUrl() + '/saiken/convenienceupload/getcsvdata/';
            var dataAppJson = JSON.stringify(appObj);
            appupload.setBtnDisable();
            msiLib2.fileDlAjax({
                url: url,
                data: {
                    dataAppJson: dataAppJson,
                    msiDataJson: DataColJson
                }
            },
            function() {
				// リフレッシュを実行
				that.changeSelectData();
            });             
        },
		render: function () {
			this.stickit();
		},
		// select2設定処理
		setSelect2: function () {
			$.msiJqlib.setSelect2Com1(this.$("#syori_kbn"), {data: _select_syori_kbn});
			$.msiJqlib.setSelect2Val($("#syori_kbn"));			//デフォルト値を選択状態にする
		},
		bindings: {
			'#syori_kbn': {
				observe: 'syori_kbn',
				afterUpdate: function ($el, event, options) {
					_setSelect2Val($el, $el.val());
				}
			},
            '#taisho_st_ymd':  {        // 施工日（自）
				observe: 'taisho_st_ymd',
				events: ['change']
            },    
            '#taisho_ed_ymd':   {        // 施工日（至）
				observe: 'taisho_ed_ymd',
				events: ['change']
            },
            '.file_name':   {             // アップロードファイル名
				observe: 'file_name',
				events: ['change']
            }
            
		}, //bindings
        // 検索ボタン
		changeSelectData: function () {
			//データ取得            
			var syorikbn     = $.msiJqlib.getSelect2Val(this.$("#syori_kbn"));
			var taishostymd = this.model.get("taisho_st_ymd");
			var taishoedymd = this.model.get("taisho_ed_ymd");
			$.ajax({
				url: $.msiJqlib.baseUrl() + '/saiken/convenienceupload/getdownload',
				data: {
					syori_kbn: syorikbn,
                    taisho_st_ymd : taishostymd,
                    taisho_ed_ymd : taishoedymd
				},
				type: 'POST',
				success: function (mydata) {
					if (mydata.status === 'OK') {
						appupload.reset(mydata);
                                                // 履歴データが抽出できなかった場合はCSVボタンを非活性とする
                                                if (mydata.dataUpload.length > 0) {
                                                    $.msiJqlib.setBtnEnable($("#btn_csvout"));
                                                } else {
                                                    $.msiJqlib.setBtnDisable($("#btn_csvout"));
                                                }
                        $("#file_clear_s1").click();
					} else {
						$.msiJqlib.showErr(mydata.msg);
					}
				}
			})
		},
       // check トグル
        toggleChkAll: function() {
            var _chkAll = ! this.model.get('_chkAll');
            this.model.set('_chkAll', _chkAll);
            if ( _chkAll ) {
                this.$('.chkAllToggle').addClass('selected');
                this.checkAll();
            } else {
                this.$('.chkAllToggle').removeClass('selected');
                this.uncheckAll();
            }
        },
        // 行全選択
        checkAll: function() {
            var $tr = this.$('tr');
            this.collection.each( function(v, k) {
                v.set('_selected', true);
                if ( v.get('_selected') ) {
                    $tr.addClass('row-selected');
                } else {
                    $tr.removeClass('row-selected');
                }
            } );
        },
        // 行全選択解除
        uncheckAll: function() {
            var $tr = this.$('tr');
            this.collection.each( function(v, k) {
                v.set('_selected', false);
                if ( v.get('_selected') ) {
                    $tr.addClass('row-selected');
                } else {
                    $tr.removeClass('row-selected');
                }
            } );
        },
 	});

	// 全体モデル    
	var AppModel = Backbone.Model.extend({
		defaults: function () {
			return {
				syori_kbn: 1,               // 処理区分
                taisho_st_ymd:  null,       // 施工日（自）
                taisho_ed_ymd:  null,       // 施工日（至）
                file_name:  null,           // アップロードファイル名
                _chkAll: false	            // 全選択
			};
		},
		validation: {
			syori_kbn: {             // 処理区分
				required: true
			},
            taisho_st_ymd:  {      // 施工日（自）
				required: true
			},
            taisho_ed_ymd:  {      // 施工日（至）
				required: true
			},
            file_name:  {          // アップロードファイル名
				required: true
			},
 		},
		labels: {
			syori_kbn:          '処理区分',
            taisho_st_ymd:      '入金日（自）',
            taisho_ed_ymd:      '入金日（至）',
            file_name:          'アップロードファイル名'
		}
	});
    
	// 明細モデル    
	var UploadModel = Backbone.Model.extend({
		defaults: function () {
			return {
				no						: '1',      // 行番号
				history_no				:	null,	// 履歴番号
				msi_no					:	null,	// 明細番号
				nyukin_ymd				:	null,	// 入金日
				uri_den_no				:	null,	// 売上伝票№
				denpyo_no				:	null,	// 受注伝票№
				seko_no					:	null,	// 施行番号
				nyukin_denpyo_no		:	null,	// 入金伝票NO
				data_kbn				:	null,	// データ区分
				data_kbn_nm				:	null,	// データ区分名
				data_sbt				:	null,	// データ種別
				data_sbt_nm				:	null,	// データ種別名
				syunou_ymd				:	null,	// 収納日時
				seikyu_no				:	null,	// 請求番号
				seikyu_nm				:	null,	// 請求先名
				souke_nm				:	null,	// 葬家名
				nyukin_prc				:	0,		// 入金金額
//				center_syori_ymd		:	null,	// センター処理日
				furikomi_yoteibi		:	null,	// 振込予定日
//				seisan_kijitu_ymd		:	null,	// 清算期日
				convenience_cd			:	null,	// ＣＶＳコード
				convenience_nm			:	null,	// コンビニ企業名
				tenpo_cd				:	null,	// 店舗コード
				data_syutoku_ymd		:	null,	// データ取得日
//				convenience_eigyo_ymd	:	null,	// コンビニ営業日
				barcode_sbt				:	null,	// バーコード種別
				barcode1				:	null,	// バーコード１段
//				barcode2				:	null,	// バーコード２段
//				barcode3				:	null,	// バーコード３段
//				barcode4				:	null,	// バーコード４段
//				riaru_flg				:	null,	// リアルフラグ
				bumon_cd				:	null,	// 売上部門コード
				bumon_nm				:	null,	// 売上部門名
				tanto_cd				:	null,	// 売上担当者コード
				tanto_nm				:	null,	// 担当者名			
                _selected:		false
			};
		},
		validation: {
			history_no				:	{	// 履歴番号
				required: false
			},
			msi_no					:	{	// 明細番号
				required: false
			},
			nyukin_ymd				:	{	// 入金日
				required: false
			},
			uri_den_no				:	{	// 売上伝票№
				required: true
			},
			denpyo_no				:	{	// 受注伝票№
				required: false
			},
			seko_no					:	{	// 施行番号
				required: false
			},
			nyukin_denpyo_no		:	{	// 入金伝票NO
				required: false
			},
			data_kbn				:	{	// データ区分
				required: true
			},
			data_kbn_nm				:	{	// データ区分名
				required: true
			},
			data_sbt				:	{	// データ種別
				required: true
			},
			data_sbt_nm				:	{	// データ種別名
				required: true
			},
			syunou_ymd				:	{	// 収納日時
				required: true
			},
			seikyu_no				:	{	// 請求番号
				required: true
			},
			seikyu_nm				:	{	// 請求先名
				required: false
			},
			souke_nm				:	{	// 葬家名
				required: false
			},
			nyukin_prc				:	{	// 入金金額
				required: true
			},
//			center_syori_ymd		:	{	// センター処理日
//				required: false
//			},
			furikomi_yoteibi		:	{	// 振込予定日
				required: true
			},
//			seisan_kijitu_ymd		:	{	// 清算期日
//				required: true
//			},
			convenience_cd			:	{	// ＣＶＳコード
				required: false
			},
			convenience_nm			:	{	// コンビニ企業名
				required: false
			},
			tenpo_cd				:	{	// 店舗コード
				required: false
			},
			data_syutoku_ymd		:	{	// データ取得日
				required: true
			},
//			convenience_eigyo_ymd	:	{	// コンビニ営業日
//				required: false
//			},
			barcode_sbt				:	{	// バーコード種別
				required: false
			},
			barcode1				:	{	// バーコード１段
				required: false
			},
//			barcode2				:	{	// バーコード２段
//				required: false
//			},
//			barcode3				:	{	// バーコード３段
//				required: false
//			},
//			barcode4				:	{	// バーコード４段
//				required: false
//			},
//			riaru_flg				:	{	// リアルフラグ
//				required: false
//			},
			bumon_cd				:	{	// 売上部門コード
				required: false
			},
			bumon_nm				:	{	// 売上部門名
				required: false
			},
			tanto_cd				:	{	// 売上担当者コード
				required: false
			},
			tanto_nm				:	{	// 担当者名			
				required: false
			},
 		},
		labels: {
			no						:	'行番号',
			history_no				:	'履歴番号',
			msi_no					:	'明細番号',
			nyukin_ymd				:	'入金日',
			uri_den_no				:	'売上伝票№',
			denpyo_no				:	'受注伝票№',
			seko_no					:	'施行番号',
			nyukin_denpyo_no		:	'入金伝票NO',
			data_kbn				:	'データ区分',
			data_kbn_nm				:	'データ区分名',
			data_sbt				:	'データ種別',
			data_sbt_nm				:	'データ種別名',
			syunou_ymd				:	'収納日時',
			seikyu_no				:	'請求番号',
			seikyu_nm				:	'請求先名',
			souke_nm				:	'葬家名',
			nyukin_prc				:	'入金金額',
//			center_syori_ymd		:	'センター処理日',
			furikomi_yoteibi		:	'振込予定日',
//			seisan_kijitu_ymd		:	'清算期日',
			convenience_cd			:	'ＣＶＳコード',
			convenience_nm			:	'コンビニ企業名',
			tenpo_cd				:	'店舗コード',
			data_syutoku_ymd		:	'データ取得日',
//			convenience_eigyo_ymd	:	'コンビニ営業日',
			barcode_sbt				:	'バーコード種別',
			barcode1				:	'バーコード１段',
//			barcode2				:	'バーコード２段',
//			barcode3				:	'バーコード３段',
//			barcode4				:	'バーコード４段',
//			riaru_flg				:	'リアルフラグ',
			bumon_cd				:	'売上部門コード',
			bumon_nm				:	'売上部門名',
			tanto_cd				:	'売上担当者コード',
			tanto_nm				:	'担当者名',			
		}
	});

	// コレクション    
	var uploadCollection = Backbone.Collection.extend({
		model: UploadModel
	});

	// ビュー    
	var UploadView = Backbone.View.extend({
		tagName: 'tbody',
		className: '',
		tmpl: _.template($('#tmpl').html()),
		events: {
            "click .no" : 'toggleSelect'
		},
		bindings: {
			'.no': 'no',
			'.history_no'			:	{	// 履歴番号
				observe: 'history_no',
				events: ['change']
			},
			'.msi_no'				:	{	// 明細番号
				observe: 'msi_no',
				events: ['change']
			},
			'.nyukin_ymd'			:	{	// 入金日
				observe: 'nyukin_ymd',
				events: ['change']
			},
			'.uri_den_no'			:	{	// 売上伝票№
				observe: 'uri_den_no',
				events: ['change']
			},
			denpyo_no				:	{	// 受注伝票№
				observe: 'uri_den_no',
				events: ['change']
			},
			'.seko_no'				:	{	// 施行番号
				observe: 'seko_no',
				events: ['change']
			},
			'.nyukin_denpyo_no'		:	{	// 入金伝票NO
				observe: 'nyukin_denpyo_no',
				events: ['change']
			},
			'.data_kbn'				:	{	// データ区分
				observe: 'data_kbn',
				events: ['change']
			},
			'.data_kbn_nm'			:	{	// データ区分名
				observe: 'data_kbn_nm',
				events: ['change']
			},
			'.data_sbt'				:	{	// データ種別
				observe: 'data_sbt',
				events: ['change']
			},
			'.data_sbt_nm'			:	{	// データ種別名
				observe: 'data_sbt_nm',
				events: ['change']
			},
			'.syunou_ymd'			:	{	// 収納日時
				observe: 'syunou_ymd',
				events: ['change']
			},
			'.seikyu_no'			:	{	// 請求番号
				observe: 'seikyu_no',
				events: ['change']
			},
			'.seikyu_nm'			:	{	// 請求先名
				observe: 'seikyu_nm',
				events: ['change']
			},
			'.souke_nm'				:	{	// 葬家名
				observe: 'souke_nm',
				events: ['change']
			},
			'.nyukin_prc'			:	{	// 入金金額
				observe: 'nyukin_prc',
				events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},
//			'.center_syori_ymd'		:	{	// センター処理日
//				observe: 'center_syori_ymd',
//				events: ['change']
//			},
			'.furikomi_yoteibi'		:	{	// 振込予定日
				observe: 'furikomi_yoteibi',
				events: ['change']
			},
//			'.seisan_kijitu_ymd'	:	{	// 清算期日
//				observe: 'seisan_kijitu_ymd',
//				events: ['change']
//			},
			'.convenience_cd'		:	{	// ＣＶＳコード
				observe: 'convenience_cd',
				events: ['change']
			},
			'.convenience_nm'		:	{	// コンビニ企業名
				observe: 'convenience_nm',
				events: ['change']
			},
			'.tenpo_cd'				:	{	// 店舗コード
				observe: 'tenpo_cd',
				events: ['change']
			},
			'.data_syutoku_ymd'		:	{	// データ取得日
				observe: 'data_syutoku_ymd',
				events: ['change']
			},
//			'.convenience_eigyo_ymd':	{	// コンビニ営業日
//				observe: 'convenience_eigyo_ymd',
//				events: ['change']
//			},
			'.barcode_sbt'			:	{	// バーコード種別
				observe: 'barcode_sbt',
				events: ['change']
			},
			'.barcode1'				:	{	// バーコード１段
				observe: 'barcode1',
				events: ['change']
			},
//			'.barcode2'				:	{	// バーコード２段
//				observe: 'barcode2',
//				events: ['change']
//			},
//			'.barcode3'				:	{	// バーコード３段
//				observe: 'barcode3',
//				events: ['change']
//			},
//			'.barcode4'				:	{	// バーコード４段
//				observe: 'barcode4',
//				events: ['change']
//			},
//			'.riaru_flg'			:	{	// リアルフラグ
//				observe: 'riaru_flg',
//				events: ['change']
//			},
			bumon_cd				:	{	// 売上部門コード
				observe: 'bumon_cd',
				events: ['change']
			},
			bumon_nm				:	{	// 売上部門名
				observe: 'bumon_nm',
				events: ['change']
			},
			tanto_cd				:	{	// 売上担当者コード
				observe: 'tanto_cd',
				events: ['change']
			},
			tanto_nm				:	{	// 担当者名			
				observe: 'tanto_nm',
				events: ['change']
			},
		},
		initialize: function () {
			Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appupload.pro, "error1"));
			this.listenTo(this.model, 'change', function () {
				$.msiJqlib.setBtnEnable($("#btn_save"));
			});
			this.listenTo(this.model, 'destroy', this.remove);
            this.listenTo(this.model, 'change:nyukin_prc', this.toggleClass);
		},
		render: function () {
			//テンプレートから明細部分のHTML作成
			this.$el.html(this.tmpl(this.model.toJSON()));
			this.stickit();

			var $tr = this.$('tr');
            if ( this.model.get('_selected') ) {
                $tr.addClass('row-selected');
            } else {
                $tr.removeClass('row-selected');
            }
            // 赤字クラス切り替え処理
            this.toggleClass();
			return this;
		},
        toggleSelect: function() {
            var nowSelected = this.model.get('_selected');
            this.model.set('_selected', !nowSelected);
            var $tr = this.$('tr');
            if ( this.model.get('_selected') ) {
                $tr.addClass('row-selected');
            } else {
                $tr.removeClass('row-selected');
            }
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function(e) {
            appupload.toggleAkajiClass(this, ['nyukin_prc']);
        },
 	});
    // 赤字クラスの追加削除処理
    appupload.toggleAkajiClass = function(that, targets) {
        _.each(targets, function(val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appupload.pro[val]).addClass('com-akaji');
            } else {
                that.$(appupload.pro[val]).removeClass('com-akaji');
            }
        });
    };

    // APP初期化処理
    appupload.appModel = new AppModel();
	appupload.uploadCol = new uploadCollection();
	appupload.appView     = new AppView({model: appupload.appModel , collection: appupload.uploadCol});
	appupload.UploadView= new UploadView({model: new UploadModel, collection: appupload.uploadCol});
    appupload.file1 = fileUpLib.upload({m: appupload.appModel
        , attr_oid: 'img_free2'
        , attr_fnm: 'v_free2'
        , el: '#file_clip_s1'
        , fileptn: /.(dat)$/
        , filekbn: null
        , url: '/saiken/convenienceupload/writeblob'
        , onSelect: function (data) {
            appupload.importChangeFlg = true;
			appupload.reset(data.impResult);
			$.msiJqlib.setBtnEnable($("#btn_save"));
        }
        , onClose: function () {
            appupload.importChangeFlg = true;
        }
        , fail: function (data) {
            $('#file_clear_s1').click();
            appupload.uploadCol.reset();
            $.msiJqlib.setBtnDisable($("#btn_save"));
        }
    });
	appupload.reset = function (data) {
		//データ保持
		appupload.data = data;
		var mode = data
        // モデルのデータを設定
        appupload.appView.model.set(data);
		//データセット
		appupload.uploadCol.reset(data.dataUpload);
		// 退避データ
		appupload.orgdownloadColJson = appupload.uploadCol.toJSON();
        $(document).msiErrClearAll();

		//コンボ等を制御
        appupload.setUploadData();
		$.msiJqlib.setBtnDisable($("#btn_save"));
	};
        
    appupload.setBtnEnable = function() {
         $.msiJqlib.setBtnEnable($("#btn_save"));
         $.msiJqlib.setBtnEnable($("#btn_delete"));
         $.msiJqlib.setBtnEnable($("#btn_cancel"));
         $.msiJqlib.setBtnEnable($("#btn_csvout"));
    };
    
    appupload.setBtnDisable = function() {
         $.msiJqlib.setBtnDisable($("#btn_save"));
         $.msiJqlib.setBtnDisable($("#btn_delete"));
         $.msiJqlib.setBtnDisable($("#btn_cancel"));
         $.msiJqlib.setBtnDisable($("#btn_csvout"));
    }; 
        
    // 表示。非表示を設定
    appupload.setUploadData = function () {
        appupload.setBtnEnable();
        if (appupload.data.syori_kbn == 1) {   // 新規
			$(".file_upload").show();
            $("#lbl_taisho_st_ymd").hide();
            $("#taisho_st_ymd").hide();
            $(".dlg_date").hide();
            $(".lbl_taisho_kikan").hide();
            $("#taisho_ed_ymd").hide();
            $("#searchbtnarea").hide();
            $("#btn_save").show();
            $("#btn_delete").hide();
            $("#btn_csvout").hide();
            $(".items").width('100%');   // 一覧の幅を調整
        }    
        if (appupload.data.syori_kbn == 2) {   // 履歴表示
			$(".file_upload").hide();
			$("#lbl_taisho_st_ymd").show();
            $("#taisho_st_ymd").show();
            $(".dlg_date").show();
            $(".lbl_taisho_kikan").show();
            $("#taisho_ed_ymd").show();
            $("#searchbtnarea").show();
            $("#btn_save").hide();
            $("#btn_delete").show();
            $("#btn_csvout").show();
            $(".items").width('100%');   // 一覧の幅を調整
        }
        $('.chkAllToggle').removeClass('selected');
    };
	try {
		//データ取得
		var data = msiLib2.getJsonFromHtml($('#data-json'));
	} catch (e) {
		console.log('JSON error. ' + e);
		$.msiJqlib.showErr('JSON error. ' + e);
	}
	// データ設定    
	appupload.reset(data);
    $(".items").width('100%');   // 一覧の幅を調整
});
