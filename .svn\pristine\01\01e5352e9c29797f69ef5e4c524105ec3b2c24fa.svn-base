<?php

/**
 * Hachu_HachuCustomerinfo
 *
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Hachu\HachuCustomerinfo
 * <AUTHOR>  KyouNan
 * @since      2014/02/12
 * @filesource 
 */

/**
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Hachu\HachuCustomerinfo
 * <AUTHOR>
 * @since      2014/02/12
 */
class Hachu_HachuCustomerinfo extends Hachu_HachukakuninAbstract {

    /** コード区分: 0010=>申込区分 */
    const CODE_KBN_MOUSHI_KBN = '0010';

    /** コード区分: 0020=>葬儀区分 */
    const CODE_KBN_SOUGI_KBN = '0020';

    /** コード区分: 0030=>会員区分 */
    const CODE_KBN_KAIIN_KBN = '0030';

    /** コード区分: 0070=>配偶者 */
    const CODE_KBN_HAIGU_KBN = '0070';

    /** コード区分: 0440=>元号 */
    const CODE_KBN_GENGO = '0440';

    /** コード区分: 0060=>世帯主 */
    const CODE_KBN_SETAI_KBN = '0060';

    /** コード区分: 0050=>筆頭者 */
    const CODE_KBN_HITO_KBN = '0050';

    /** コード区分: 0040=>葬儀形式 */
    const CODE_KBN_KEISHIKI_KBN = '0040';

    /** コード区分: 0470=>入棺経 */
    const CODE_KBN_NYUKAN_KYO = '0470';

    /** コード区分: 0480=>出棺経 */
    const CODE_KBN_SYUKAN_KYO = '0480';

    /** コード区分: 0490=>火葬経 */
    const CODE_KBN_KASO_KYO = '0490';

    /** コード区分: 0210=>加入団体 */
    const CODE_KBN_KANYU_DANTAI = '0210';

    /** コード区分: 0450=>用途 */
    const CODE_KBN_YOTO_KBN = '0450';

    /** コード区分: 0220=>搬送業務区分 */
    const CODE_KBN_HS_GYOMU_KBN = '0220';

    /** コード区分: 0200=>宗派区分 */
    const CODE_KBN_SYUHA_KBN = '0200';

    /** コード区分: 0240=>宗旨区分 */
    const CODE_KBN_SYUSHI_KBN = '0240';

    /** コード区分: 0190=>続柄区分 */
    const CODE_KBN_ZOKU_KBN = '0190';

    /** コード区分:一時参照用 */
    private $_codeKbn = '';

    /**
     * 初期情報取得処理
     *
     * <AUTHOR>  KyouNan
     * @since 2014/01/14
     * @param string $sekoNo 施行番号
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
    public function getInitData($sekoNo, $controllerName) {

        $this->_sekoNo = $sekoNo;
        // 施行基本情報を設定する
        $this->setInitParam();

        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        $dataKbns = array(
            // 基本タブ
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN), // 申込区分
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'haigu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAIGU_KBN), // 配偶者区分
            'gengo' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENGO), // 元号
            'setai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETAI_KBN), // 世帯主
            'hito_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HITO_KBN), // 筆頭者
            'keishiki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEISHIKI_KBN), // 葬儀形式
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            // 日程タブ
            'nyukan_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_NYUKAN_KYO), // 入棺経
            'syukan_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUKAN_KYO), // 出棺経
            'kaso_kyo' => $this->filter($dataCodeNameMst, self::CODE_KBN_KASO_KYO), // 火葬経
            // 喪主タブ
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            // 互助会タブ
            'kanyu_dantai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KANYU_DANTAI), // 加入団体
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN), // 用途
            // その他タブ
            'hs_gyomu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HS_GYOMU_KBN), // 用途
        );
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
//            'dataSekyuInfo' => $dataSekyuInfo,
//            'dataGojokaiInfo' => $dataGojokaiInfo,
//            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
//            'dataKashidasiCol' => $dataKashidasiCol,
//            'gojokaiCouseMst' => $gojokaiCouseMst,
//            'dataSekoHaChuCol' => $dataSekoHaChuCol,
            'dataKbns' => $dataKbns,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }
    
    /**
     * 湯灌情報取得処理
     *
     * <AUTHOR> Ogino
     * @since 2014/03/24
     * @param string $sekoNo 施行番号
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
    public function getYukanData($sekoNo, $controllerName) {

        $this->_sekoNo = $sekoNo;
        // 施行基本情報を設定する
        $this->setInitParam();
        
        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        $dataYukan = $this->dataYukan($sekoNo);
        
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        $dataKbns = array(
            // 基本タブ
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
        );
        
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
            'dataYukan' => $dataYukan,
            'dataKbns' => $dataKbns,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }
    
    /**
     * 花運搬情報取得処理
     *
     * <AUTHOR> Ogino
     * @since 2014/06/11
     * @param string $sekoNo 施行番号
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
	public function getHanaunpanData($sekoNo, $controllerName) {

		$this->_sekoNo = $sekoNo;
		// 施行基本情報を設定する
		$this->setInitParam();

		// 施行基本情報を取得する
		$dataSekoKihon = $this->getSekoKihon();
		$dataHanaunpan = $this->dataHanaunpan($sekoNo);

		// 画面データを設定する
		$data = array(
			'controllerName' => $controllerName,
			'dataSekoKihon' => $dataSekoKihon,
			'dataHanaunpan' => $dataHanaunpan,
		);
		$jsonData = Msi_Sys_Utils::json_encode($data);
		return $jsonData;
	}

    /**
     * 発注処理
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/05/22
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function hachuOrder($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 1;//発注済
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $status_kbn= 1;//手配中
//        $i_chk=implode(',',$params);
//        $i_chk=implode(' AND hachu_no=',$params);
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,hacyu_cnt=hacyu_cnt + 1
            ,status_kbn=:status_kbn
            ,modifiable_kbn=1
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
           
            foreach ($params as $value) {
                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn 
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        return $cnt;
    }
     /**
     * 発注処理(花輪)
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/07/03
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function hachuOrderHanawa($login_cd,$params,$seko_no,$hachu_no_moto){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 1;//発注済
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $status_kbn= 1;//手配中
        $count = 0;
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,hacyu_cnt=hacyu_cnt + 1
            ,status_kbn=:status_kbn
            ,modifiable_kbn=1
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
           
            foreach ($params as $value) {
                
                if ($count == 0){
                    $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn 
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $hachu_no_moto
                                        ));
                }
                
                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn 
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));
                 
                 $count++;
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        return $cnt;
    }   
     /**
     * 発注処理(喪服)
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/07/0422
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function hachuOrderMofuku($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 1;//発注済
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $status_kbn= 1;//手配中
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,hacyu_cnt=hacyu_cnt + 1
            ,status_kbn=:status_kbn
            ,modifiable_kbn=1
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        $sql2 = <<< END_OF_SQL
        UPDATE
            seko_mofuku_msi
        SET 
             order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
           
            foreach ($params as $value) {
                //seko_hachu_info
                $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn 
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));
                 
                //seko_mofuku_msi
                $cnt = $db->easyExecute($sql2, array(
                                        'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        return $cnt;
    }  
    /**
     * 発注処理（1件）
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/05/23
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function onehachuOrder($login_cd,$hachu_no,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 1;//発注済
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $status_kbn= 1;//手配中
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,hacyu_cnt=hacyu_cnt + 1
            ,status_kbn=:status_kbn
            ,modifiable_kbn=1
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
             $cnt = $db->easyExecute($sql, array(
                                    'hc_denpyo_no' => $hc_denpyo_no
                                   ,'order_flg' => $order_flg
                                   ,'order_ymd' => $order_ymd
                                   ,'order_tanto_cd' => $order_tanto_cd
                                   ,'status_kbn' => $status_kbn 
                                   ,'seko_no' => $seko_no
                                   ,'hachu_no' => $hachu_no
                                        ));
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();

        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);        
        return $cnt;
    }

    /**
     * 発注処理の発注確認
     *
     * <AUTHOR>  KyouNan
     * @since 2014/03/20
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function onehachuKakunin($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 1;
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $chk_order_flg=1;
        $chk_order_ymd=date("Y/m/d H:i:s");
        $chk_order_tanto_cd= $login_cd;
        $hacyu_cnt= 1;
        $status_kbn= 1;
//        $i_chk=implode(',',$params);
//        $i_chk=implode(' AND hachu_no=',$params);
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,chk_order_flg=:chk_order_flg
            ,chk_order_ymd=:chk_order_ymd
            ,chk_order_tanto_cd=:chk_order_tanto_cd
            ,hacyu_cnt=:hacyu_cnt
            ,status_kbn=:status_kbn
            ,modifiable_kbn=1
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no = :i_chk
END_OF_SQL;
        try {

                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
//                                       ,'order_flg' => $order_flg　//発注済み
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'chk_order_flg' => $chk_order_flg
                                       ,'chk_order_ymd' => $chk_order_ymd
                                       ,'chk_order_tanto_cd' => $chk_order_tanto_cd
                                       ,'hacyu_cnt' => $hacyu_cnt
                                       ,'status_kbn' => $status_kbn 
                                       ,'seko_no' => $seko_no
                                       ,'i_chk' => $params
                                        ));

        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        
            return $cnt;
    }
    
    /**
     * 発注取消
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/05/22
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function hachuCancel($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 0;//未発注
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
//        $hacyu_cnt= 1;
        $status_kbn= 2;  //キャンセル
//        $i_chk=implode(',',$params);
//        $i_chk=implode(' AND hachu_no=',$params);
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,status_kbn=:status_kbn
            ,modifiable_kbn=0
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
            foreach ($params as $value) {
                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();

        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        
        return $cnt;
    }
    
    /**
     * 発注取消(赤伝データ作成追加)
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/05/29
     * @param string $login_cd user code
     * @param array $params selected hachu
     * @param string $sekoNo 施行番号
     */
    public function hachuakaCancel($login_cd,$params,$seko_no,$ha_rp_kbn){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $akaden_no = array();
     //   $cancel_no = array();
        $rec_ary   = array();
        $hachu_no_ary = array();
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,status_kbn=:status_kbn
            ,order_flg = 0
            ,chk_order_flg = 0
            ,chk_order_ymd=:order_ymd   
            ,chk_order_tanto_cd=:order_tanto_cd  
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        //赤伝作成元データ取得
        $sql2 = <<< END_OF_SQL
        SELECT
            *
        FROM 
            seko_hachu_info
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no = :hachu_no
END_OF_SQL;
        //現在の最大番号取得
        $sql3 = <<< END_OF_SQL
        SELECT
            max(hachu_no) as maxno
        FROM 
            seko_hachu_info
        WHERE
            seko_no = :seko_no
END_OF_SQL;
        //赤伝データ作成
        $sql4 = <<< END_OF_SQL
        INSERT INTO seko_hachu_info 
        (seko_no,hachu_no,crt_row_no,siire_cd,report_cd,ha_rp_code_kbn,ha_rp_cd,ha_rp_kbn,
         ha_entry_kbn,ha_syori_kbn,ha_etc_kbn,hachu_den_kbn,data_kbn,dai_bunrui_cd,chu_bunrui_cd,
         shohin_kbn,shohin_cd,shohin_nm,shohin_tkiyo_nm,hachu_suryo,tani_cd,hanbai_tnk,hachu_tnk,
         hachu_prc,nebiki_prc,delivery_kbn,nonyu_cd,nonyu_nm,nonyu_ymd,jc_denpyo_no,jc_msi_no,
         hc_denpyo_no,order_flg,order_ymd,order_tanto_cd,chk_order_flg,chk_order_ymd,
         chk_order_tanto_cd,hacyu_cnt,status_kbn,copy_kbn,hachu_no_moto,hd_biko1,hd_biko2,
         v_free1,v_free2,v_free3,v_free4,v_free5,n_free1,n_free2,n_free3,n_free4,n_free5,
         d_free1,d_free2,d_free3) 
        VALUES (:seko_no,:hachu_no,:crt_row_no,:siire_cd,:report_cd,:ha_rp_code_kbn,:ha_rp_cd,:ha_rp_kbn,
                :ha_entry_kbn,:ha_syori_kbn,:ha_etc_kbn,:hachu_den_kbn,:data_kbn,:dai_bunrui_cd,:chu_bunrui_cd,
                :shohin_kbn,:shohin_cd,:shohin_nm,:shohin_tkiyo_nm,:hachu_suryo,:tani_cd,:hanbai_tnk,:hachu_tnk,
                :hachu_prc,:nebiki_prc,:delivery_kbn,:nonyu_cd,:nonyu_nm,:nonyu_ymd,:jc_denpyo_no,:jc_msi_no,
                :hc_denpyo_no,:order_flg,:order_ymd,:order_tanto_cd,:chk_order_flg,:chk_order_ymd,
                :chk_order_tanto_cd,:hacyu_cnt,:status_kbn,:copy_kbn,:hachu_no_moto,:hd_biko1,:hd_biko2,
                :v_free1,:v_free2,:v_free3,:v_free4,:v_free5,:n_free1,:n_free2,:n_free3,:n_free4,:n_free5,
                :d_free1,:d_free2,:d_free3)                       
END_OF_SQL;
        //コピー元発注書番号変更
        $sql5 = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hachu_no_moto = :new_hachu_no
        WHERE
            hachu_no_moto = :old_hachu_no                
END_OF_SQL;
        //施工車輛元データ取得
        $sql6 = <<< END_OF_SQL
        SELECT
            *
        FROM 
            seko_syaryo_msi
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no = :hachu_no
END_OF_SQL;
        //施工車輛作り直し
        $sql7 = <<< END_OF_SQL
        INSERT INTO seko_syaryo_msi 
        (seko_no, msi_no, hachu_no, siire_cd, disp_no, sy_used_code_kbn, sy_used_cd, sy_used_kbn,
         rq_car_code_kbn, rq_car_cd, rq_car_kbn, car_suryo, person_suryo, departure_dt, route_dep_nm,
         route_via_nm, route_arr_nm) 
        VALUES (:seko_no,:msi_no,:hachu_no,:siire_cd,:disp_no,:sy_used_code_kbn,:sy_used_cd,:sy_used_kbn,
                :rq_car_code_kbn,:rq_car_cd,:rq_car_kbn,:car_suryo,:person_suryo,:departure_dt,:route_dep_nm,
                :route_via_nm,:route_arr_nm)                       
END_OF_SQL;
        //現在の最大番号取得
        $sql8 = <<< END_OF_SQL
        SELECT
            max(msi_no) as maxno
        FROM 
            seko_syaryo_msi
        WHERE
            seko_no = :seko_no
END_OF_SQL;
        //コピー元発注書番号変更(花輪明細用)
        $sql9 = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hachu_no_moto = :new_hachu_no,
            order_flg     = 0    
        WHERE
            hachu_no_moto = :old_hachu_no                
END_OF_SQL;
        //コピー元発注書番号変更(喪服明細用)
        $sql10 = <<< END_OF_SQL
        UPDATE
            seko_mofuku_msi
        SET 
            hachu_no = :new_hachu_no,
            order_flg     = 0    
        WHERE
            hachu_no = :old_hachu_no                
END_OF_SQL;
        $sql11 = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,status_kbn=:status_kbn
            ,order_flg = 0
            ,chk_order_flg = 0
            ,chk_order_ymd=:order_ymd   
            ,chk_order_tanto_cd=:order_tanto_cd  
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no_moto IN (:hachu_no)
END_OF_SQL;
        $sql12= <<< END_OF_SQL
        SELECT
             hachu_no,hc_denpyo_no
        FROM
            seko_hachu_info
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no_moto IN (:hachu_no)
        AND order_flg = 1
END_OF_SQL;
        try {
            //現在の施工番号から最大の発注管理番号取得
            $req = $db->easySelect($sql3, array(
                                        'seko_no' => $seko_no
                                        ));    
            $max_seko_no = $req[0]["maxno"];
            //現在の施工番号から最大の明細番号取得
            $req = $db->easySelect($sql8, array(
                                        'seko_no' => $seko_no
                                        ));    
            $max_msi_no = $req[0]["maxno"];
            
            foreach ($params as $value) {
            	$in_hachu_no = $value["hachu_no"];

                // すでに実行済みの発注管理番号の場合スキップ
                if (in_array($in_hachu_no,$hachu_no_ary)) { 
                    continue;
                }
                //処理実行の発注管理番号を確保
                $hachu_no_ary[] = $in_hachu_no;
                
                //元データ取得
                $rec = $db->easySelect($sql2, array(
                                       'seko_no' => $seko_no
                                      ,'hachu_no' => $value["hachu_no"]
                                        ));    
                
                //DEL:stats_kbnを2:キャンセルに
                //stats_kbnを0:なしに
                $cnt = $db->easyExecute($sql, array(
                                       // 'hc_denpyo_no' => $hc_denpyo_no
                                       //,
                                       'order_ymd' => null
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => 0
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value["hachu_no"]
                                        ));



/*                
                //発注管理番号 + 1
                $max_seko_no = $max_seko_no + 1;
                $hc_denpyo_no= $this->getAutoDenpyoNo($db);
                //キャンセルデータを作り直す
                $cnt = $db->easyExecute($sql4, array(
                        		 'seko_no'    => $seko_no
					,'hachu_no'   => $max_seko_no
					,'crt_row_no' => $rec[0]["crt_row_no"]
					,'siire_cd'   => $rec[0]["siire_cd"]
					,'report_cd'  => $rec[0]["report_cd"]
					,'ha_rp_code_kbn'=> $rec[0]["ha_rp_code_kbn"]
					,'ha_rp_cd'      => $rec[0]["ha_rp_cd"]
					,'ha_rp_kbn'     => $rec[0]["ha_rp_kbn"]
					,'ha_entry_kbn'  => $rec[0]["ha_entry_kbn"]
					,'ha_syori_kbn'  => $rec[0]["ha_syori_kbn"]
					,'ha_etc_kbn'    => $rec[0]["ha_etc_kbn"]
					,'hachu_den_kbn' => $rec[0]["hachu_den_kbn"]
					,'data_kbn'      => $rec[0]["data_kbn"]
					,'dai_bunrui_cd' => $rec[0]["dai_bunrui_cd"]
					,'chu_bunrui_cd' => $rec[0]["chu_bunrui_cd"]
					,'shohin_kbn'    => $rec[0]["shohin_kbn"]
					,'shohin_cd'     => $rec[0]["shohin_cd"]
					,'shohin_nm'     => $rec[0]["shohin_nm"]
					,'shohin_tkiyo_nm' => $rec[0]["shohin_tkiyo_nm"]
					,'hachu_suryo'   => $rec[0]["hachu_suryo"]
					,'tani_cd'       => $rec[0]["tani_cd"]
					,'hanbai_tnk'    => $rec[0]["hanbai_tnk"]
					,'hachu_tnk'     => $rec[0]["hachu_tnk"]
					,'hachu_prc'     => $rec[0]["hachu_prc"]
					,'nebiki_prc'    => $rec[0]["nebiki_prc"]
					,'delivery_kbn'  => $rec[0]["delivery_kbn"]
					,'nonyu_cd'      => $rec[0]["nonyu_cd"]
					,'nonyu_nm'      => $rec[0]["nonyu_nm"]
					,'nonyu_ymd'     => $rec[0]["nonyu_ymd"]
                                	,'jc_denpyo_no'  => $rec[0]["jc_denpyo_no"]
					,'jc_msi_no'     => $rec[0]["jc_msi_no"]
//					,'hc_denpyo_no'  => $rec[0]["hc_denpyo_no"]
					,'hc_denpyo_no'  => 0
					,'order_flg'     => 0
					,'order_ymd'     => $order_ymd
					,'order_tanto_cd'=> $order_tanto_cd
					,'chk_order_flg' => $rec[0]["chk_order_flg"]
					,'chk_order_ymd' => $rec[0]["chk_order_ymd"]
					,'chk_order_tanto_cd' => $rec[0]["chk_order_tanto_cd"]
					,'hacyu_cnt'     => $rec[0]["hacyu_cnt"]
					,'status_kbn'    => 0
					,'copy_kbn'      => $rec[0]["copy_kbn"]
					,'hachu_no_moto' => $rec[0]["hachu_no_moto"]
					,'hd_biko1'      => $rec[0]["hd_biko1"]
					,'hd_biko2'      => $rec[0]["hd_biko2"]
					,'v_free1'       => $rec[0]["v_free1"]
					,'v_free2'       => $rec[0]["v_free2"]
					,'v_free3'       => $rec[0]["v_free3"]
					,'v_free4'       => $rec[0]["v_free4"]
					,'v_free5'       => $rec[0]["v_free5"]
					,'n_free1'       => $rec[0]["n_free1"]
					,'n_free2'       => $rec[0]["n_free2"]
					,'n_free3'       => $rec[0]["n_free3"]
					,'n_free4'       => $rec[0]["n_free4"]
					,'n_free5'       => $rec[0]["n_free5"]
					,'d_free1'       => $rec[0]["d_free1"]
					,'d_free2'       => $rec[0]["d_free2"]
					,'d_free3'       => $rec[0]["d_free3"]
                                        ));   
                //花輪の場合のみ
                if ($ha_rp_kbn == "17"){
                    //既存データのコピー元発注管理番号も変更する
                    $cnt2 = $db->easyExecute($sql9, array(
                                'old_hachu_no'   => $value["hachu_no"],
                                'new_hachu_no'   => $max_seko_no
                    ));   
                }
                else if ($ha_rp_kbn == "19" || $ha_rp_kbn == "20"){
                    //既存データのコピー元発注管理番号も変更する
                    $cnt2 = $db->easyExecute($sql10, array(
                                'old_hachu_no'   => $value["hachu_no"],
                                'new_hachu_no'   => $max_seko_no
                    ));   
                }
                else {
                    //既存データのコピー元発注管理番号も変更する
                    $cnt2 = $db->easyExecute($sql5, array(
                                'old_hachu_no'   => $value["hachu_no"],
                                'new_hachu_no'   => $max_seko_no
                    )); 
                }
                $old_msino = 0;
                $new_msino = 0;
                //施工車輛情報元データ取得
                $rec_syaryo = $db->easySelect($sql6, array(
                                       'seko_no' => $seko_no
                                      ,'hachu_no' => $value["hachu_no"]
                                        ));  

                if (count($rec_syaryo) > 0) { 
                    $max_msi_no  = $max_msi_no + 1;
                    $old_msino = $rec_syaryo[0]["msi_no"];
                    $new_msino = $max_msi_no;

                    //キャンセルされたデータを作り直す
                    $cnt2 = $db->easyExecute($sql7, array(
                                	 'seko_no'         => $seko_no
                                        ,'msi_no'          => $max_msi_no
                                     	,'hachu_no'        => $max_seko_no
                                        ,'siire_cd'        => $rec_syaryo[0]["siire_cd"]
                                        ,'disp_no'         => $rec_syaryo[0]["disp_no"]
                                        ,'sy_used_code_kbn'=> $rec_syaryo[0]["sy_used_code_kbn"]
                                        ,'sy_used_cd'      => $rec_syaryo[0]["sy_used_cd"]
					,'sy_used_kbn'     => $rec_syaryo[0]["sy_used_kbn"]
					,'rq_car_code_kbn' => $rec_syaryo[0]["rq_car_code_kbn"]
					,'rq_car_cd'       => $rec_syaryo[0]["rq_car_cd"]
					,'rq_car_kbn'      => $rec_syaryo[0]["rq_car_kbn"]
					,'car_suryo'       => $rec_syaryo[0]["car_suryo"]     
					,'person_suryo'    => $rec_syaryo[0]["person_suryo"]
					,'departure_dt'    => $rec_syaryo[0]["departure_dt"]
					,'route_dep_nm'    => $rec_syaryo[0]["route_dep_nm"]
					,'route_via_nm'    => $rec_syaryo[0]["route_via_nm"]
					,'route_arr_nm'    => $rec_syaryo[0]["route_arr_nm"]
                                        ));   
                }
                
                $cancel_no[] = array('old_hachuno'  => $value["hachu_no"],
                                     'new_hachuno'  => $max_seko_no,
                                     'old_msino'    => $old_msino,
                                     'new_msino'    => $new_msino);
*/
                    //花輪の場合のみ
                    if (($ha_rp_kbn == "17") || ($ha_rp_kbn == "18")){
                        
                        $rechanawa = $db->easySelect($sql12, array(
                                       'seko_no' => $seko_no
                                      ,'hachu_no' => $value["hachu_no"]
                                        ));   
                        foreach ($rechanawa as $value_hanawa) {
                            $sqlGetDenpyoMsi = "SELECT hachu_prc, nebiki_prc, zei_kbn, out_zei_prc , in_zei_prc".
                                               " FROM hachu_denpyo_msi WHERE denpyo_no=:denpyo_no AND hachu_no=:hachu_no";
                            $selectGetMsi = $db->easySelect($sqlGetDenpyoMsi, array('denpyo_no' => $value_hanawa["hc_denpyo_no"], 'hachu_no' => $value_hanawa["hachu_no"]));

                            if (count($selectGetMsi)>0){
                                //発注伝票明細に削除フラグを立てる
                                $sqlDelDenpyoMsi = "UPDATE hachu_denpyo_msi set delete_flg = 1 WHERE denpyo_no=:denpyo_no AND hachu_no=:hachu_no";

                                $db->easyExecute($sqlDelDenpyoMsi, array('denpyo_no' => $value_hanawa["hc_denpyo_no"], 'hachu_no' => $value_hanawa["hachu_no"]));

                                //ヘッダのデータを確保
                                $sqlGetDenpyoHead = "SELECT hachu_prc_sum, hachu_nebk_sum, szei_katax_taisho_prc, uzei_katax_taisho_prc,".
                                                    " hitax_katax_taisho_prc, out_zei_prc, in_zei_prc".
                                                    " FROM hachu_denpyo WHERE denpyo_no=:denpyo_no";
                                $selectGetHead = $db->easySelect($sqlGetDenpyoHead, array('denpyo_no' => $value_hanawa["hc_denpyo_no"]));

                                //明細の内容をヘッダから減算
                                $hachu_prc_sum         =$selectGetHead[0]["hachu_prc_sum"]         -$selectGetMsi[0]["hachu_prc"];
                                $hachu_nebk_sum        =$selectGetHead[0]["hachu_nebk_sum"]        -$selectGetMsi[0]["nebiki_prc"];
                                $szei_katax_taisho_prc =$selectGetHead[0]["szei_katax_taisho_prc"];
                                $uzei_katax_taisho_prc =$selectGetHead[0]["uzei_katax_taisho_prc"];
                                $hitax_katax_taisho_prc=$selectGetHead[0]["hitax_katax_taisho_prc"];

                                if ($selectGetMsi[0]["zei_kbn"] == 0){//非課税
                                    $hitax_katax_taisho_prc = $hitax_katax_taisho_prc - $selectGetMsi[0]["hachu_prc"];              
                                }else if($selectGetMsi[0]["zei_kbn"] == 1){//内税
                                    $uzei_katax_taisho_prc = $uzei_katax_taisho_prc   - $selectGetMsi[0]["hachu_prc"];
                                }
                                else{//外税
                                    $szei_katax_taisho_prc = $szei_katax_taisho_prc   - $selectGetMsi[0]["hachu_prc"];
                                }

                                $out_zei_prc           =$selectGetHead[0]["out_zei_prc"]           -$selectGetMsi[0]["out_zei_prc"];
                                $in_zei_prc            =$selectGetHead[0]["in_zei_prc"]            -$selectGetMsi[0]["in_zei_prc"];

                                //明細に生きているデータ残っているか確認
                                $sqlCntDenpyo = "SELECT denpyo_no FROM hachu_denpyo_msi WHERE denpyo_no=:denpyo_no and delete_flg = 0 ";
                                $selectCnt = $db->easySelect($sqlCntDenpyo, array('denpyo_no' => $value_hanawa["hc_denpyo_no"]));

                                //発注伝票の更新
                                //発注伝票ヘッダに削除フラグを立てる
                                $sqlDelDenpyo = "UPDATE hachu_denpyo set ".
                                                "  hachu_prc_sum=:hachu_prc_sum".
                                                " ,hachu_nebk_sum=:hachu_nebk_sum".                                    
                                                " ,szei_katax_taisho_prc=:szei_katax_taisho_prc".                                    
                                                " ,uzei_katax_taisho_prc=:uzei_katax_taisho_prc".                                    
                                                " ,hitax_katax_taisho_prc=:hitax_katax_taisho_prc".                                    
                                                " ,out_zei_prc=:out_zei_prc".                                    
                                                " ,in_zei_prc=:in_zei_prc";

                                //データが0件の場合、削除フラグを立てる                
                                if (count($selectCnt) == 0) {
                                    $sqlDelDenpyo = $sqlDelDenpyo.",delete_flg = 1";                    
                                }   

                                $sqlDelDenpyo=$sqlDelDenpyo." WHERE denpyo_no=:denpyo_no";   

                                $cnt = $db->easyExecute($sqlDelDenpyo, array('denpyo_no' => $value_hanawa["hc_denpyo_no"],
                                                         'hachu_prc_sum'=> $hachu_prc_sum, 'hachu_nebk_sum' => $hachu_nebk_sum,
                                                         'szei_katax_taisho_prc' => $szei_katax_taisho_prc,
                                                         'uzei_katax_taisho_prc' => $uzei_katax_taisho_prc,
                                                         'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc,
                                                         'out_zei_prc' => $out_zei_prc, 'in_zei_prc' => $in_zei_prc ));
                                if ($cnt > 1) {
                                    $success = false;
                                    break;
                                }               
                            }    
                        }                
                        
                        $cnt = $db->easyExecute($sql11, array(
                                               // 'hc_denpyo_no' => $hc_denpyo_no
                                               //,
                                               'order_ymd' => null
                                               ,'order_tanto_cd' => $order_tanto_cd
                                               ,'status_kbn' => 0
                                               ,'seko_no' => $seko_no
                                               ,'hachu_no' => $value["hachu_no"]
                                                ));
                        }
//20141201 追加 St      
                    //削除対象のデータを確保
                    $sqlGetDenpyoMsi = "SELECT hachu_prc, nebiki_prc, zei_kbn, out_zei_prc , in_zei_prc".
                                       " FROM hachu_denpyo_msi WHERE denpyo_no=:denpyo_no AND hachu_no=:hachu_no";
                    $selectGetMsi = $db->easySelect($sqlGetDenpyoMsi, array('denpyo_no' => $rec[0]["hc_denpyo_no"], 'hachu_no' => $value["hachu_no"]));
                    
                    if (count($selectGetMsi)>0){
                        //発注伝票明細に削除フラグを立てる
                        $sqlDelDenpyoMsi = "UPDATE hachu_denpyo_msi set delete_flg = 1 WHERE denpyo_no=:denpyo_no AND hachu_no=:hachu_no";

                        $db->easyExecute($sqlDelDenpyoMsi, array('denpyo_no' => $rec[0]["hc_denpyo_no"], 'hachu_no' => $value["hachu_no"]));

                        //ヘッダのデータを確保
                        $sqlGetDenpyoHead = "SELECT hachu_prc_sum, hachu_nebk_sum, szei_katax_taisho_prc, uzei_katax_taisho_prc,".
                                            " hitax_katax_taisho_prc, out_zei_prc, in_zei_prc".
                                            " FROM hachu_denpyo WHERE denpyo_no=:denpyo_no";
                        $selectGetHead = $db->easySelect($sqlGetDenpyoHead, array('denpyo_no' => $rec[0]["hc_denpyo_no"]));

                        //明細の内容をヘッダから減算
                        $hachu_prc_sum         =$selectGetHead[0]["hachu_prc_sum"]         -$selectGetMsi[0]["hachu_prc"];
                        $hachu_nebk_sum        =$selectGetHead[0]["hachu_nebk_sum"]        -$selectGetMsi[0]["nebiki_prc"];
                        $szei_katax_taisho_prc =$selectGetHead[0]["szei_katax_taisho_prc"];
                        $uzei_katax_taisho_prc =$selectGetHead[0]["uzei_katax_taisho_prc"];
                        $hitax_katax_taisho_prc=$selectGetHead[0]["hitax_katax_taisho_prc"];

                        if ($selectGetMsi[0]["zei_kbn"] == 0){//非課税
                            $hitax_katax_taisho_prc = $hitax_katax_taisho_prc - $selectGetMsi[0]["hachu_prc"];              
                        }else if($selectGetMsi[0]["zei_kbn"] == 1){//内税
                            $uzei_katax_taisho_prc = $uzei_katax_taisho_prc   - $selectGetMsi[0]["hachu_prc"];
                        }
                        else{//外税
                            $szei_katax_taisho_prc = $szei_katax_taisho_prc   - $selectGetMsi[0]["hachu_prc"];
                        }

                        $out_zei_prc           =$selectGetHead[0]["out_zei_prc"]           -$selectGetMsi[0]["out_zei_prc"];
                        $in_zei_prc            =$selectGetHead[0]["in_zei_prc"]            -$selectGetMsi[0]["in_zei_prc"];

                        //明細に生きているデータ残っているか確認
                        $sqlCntDenpyo = "SELECT denpyo_no FROM hachu_denpyo_msi WHERE denpyo_no=:denpyo_no and delete_flg = 0 ";
                        $selectCnt = $db->easySelect($sqlCntDenpyo, array('denpyo_no' => $rec[0]["hc_denpyo_no"]));

                        //発注伝票の更新
                        //発注伝票ヘッダに削除フラグを立てる
                        $sqlDelDenpyo = "UPDATE hachu_denpyo set ".
                                        "  hachu_prc_sum=:hachu_prc_sum".
                                        " ,hachu_nebk_sum=:hachu_nebk_sum".                                    
                                        " ,szei_katax_taisho_prc=:szei_katax_taisho_prc".                                    
                                        " ,uzei_katax_taisho_prc=:uzei_katax_taisho_prc".                                    
                                        " ,hitax_katax_taisho_prc=:hitax_katax_taisho_prc".                                    
                                        " ,out_zei_prc=:out_zei_prc".                                    
                                        " ,in_zei_prc=:in_zei_prc";

                        //データが0件の場合、削除フラグを立てる                
                        if (count($selectCnt) == 0) {
                            $sqlDelDenpyo = $sqlDelDenpyo.",delete_flg = 1";                    
                        }   

                        $sqlDelDenpyo=$sqlDelDenpyo." WHERE denpyo_no=:denpyo_no";   

                        $cnt = $db->easyExecute($sqlDelDenpyo, array('denpyo_no' => $rec[0]["hc_denpyo_no"],
                                                 'hachu_prc_sum'=> $hachu_prc_sum, 'hachu_nebk_sum' => $hachu_nebk_sum,
                                                 'szei_katax_taisho_prc' => $szei_katax_taisho_prc,
                                                 'uzei_katax_taisho_prc' => $uzei_katax_taisho_prc,
                                                 'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc,
                                                 'out_zei_prc' => $out_zei_prc, 'in_zei_prc' => $in_zei_prc ));
                        if ($cnt > 1) {
                            $success = false;
                            break;
                        }               
                    }
//20141201 追加 Ed
//          
                //発注確認済みの場合、マイナスデータ作成 
                if ($value["chk_order_flg"] === "1") {
                    //発注管理番号 + 1
                    $max_seko_no = $max_seko_no + 1;                    
                    //赤伝データの発注管理番号格納
                    array_push($akaden_no,$max_seko_no);
                    $hc_denpyo_no= $this->getAutoDenpyoNo($db);
                    //赤伝データ作成
                    $cnt = $db->easyExecute($sql4, array(
					 'seko_no'    => $seko_no
					,'hachu_no'   => $max_seko_no
					,'crt_row_no' => $rec[0]["crt_row_no"]
					,'siire_cd'   => $rec[0]["siire_cd"]
					,'report_cd'  => $rec[0]["report_cd"]
					,'ha_rp_code_kbn'=> $rec[0]["ha_rp_code_kbn"]
					,'ha_rp_cd'      => $rec[0]["ha_rp_cd"]
					,'ha_rp_kbn'     => $rec[0]["ha_rp_kbn"]
					,'ha_entry_kbn'  => $rec[0]["ha_entry_kbn"]
					,'ha_syori_kbn'  => $rec[0]["ha_syori_kbn"]
					,'ha_etc_kbn'    => $rec[0]["ha_etc_kbn"]
					,'hachu_den_kbn' => $rec[0]["hachu_den_kbn"]
					,'data_kbn'      => $rec[0]["data_kbn"]
					,'dai_bunrui_cd' => $rec[0]["dai_bunrui_cd"]
					,'chu_bunrui_cd' => $rec[0]["chu_bunrui_cd"]
					,'shohin_kbn'    => $rec[0]["shohin_kbn"]
					,'shohin_cd'     => $rec[0]["shohin_cd"]
					,'shohin_nm'     => $rec[0]["shohin_nm"]
					,'shohin_tkiyo_nm' => $rec[0]["shohin_tkiyo_nm"]
					,'hachu_suryo'   => $rec[0]["hachu_suryo"] * (-1)
					,'tani_cd'       => $rec[0]["tani_cd"]
					,'hanbai_tnk'    => $rec[0]["hanbai_tnk"]
					,'hachu_tnk'     => $rec[0]["hachu_tnk"]
					,'hachu_prc'     => $rec[0]["hachu_prc"]
					,'nebiki_prc'    => $rec[0]["nebiki_prc"]
					,'delivery_kbn'  => $rec[0]["delivery_kbn"]
					,'nonyu_cd'      => $rec[0]["nonyu_cd"]
					,'nonyu_nm'      => $rec[0]["nonyu_nm"]
					,'nonyu_ymd'     => $rec[0]["nonyu_ymd"]
					,'jc_denpyo_no'  => $rec[0]["jc_denpyo_no"]
					,'jc_msi_no'     => $rec[0]["jc_msi_no"]
					,'hc_denpyo_no'  => $hc_denpyo_no
					,'order_flg'     => $rec[0]["order_flg"]
					,'order_ymd'     => $order_ymd
					,'order_tanto_cd'=> $order_tanto_cd
					,'chk_order_flg' => $rec[0]["chk_order_flg"]
					,'chk_order_ymd' => $rec[0]["chk_order_ymd"]
					,'chk_order_tanto_cd' => $rec[0]["chk_order_tanto_cd"]
					,'hacyu_cnt'     => $rec[0]["hacyu_cnt"]
					,'status_kbn'    => 3  //赤伝
					,'copy_kbn'      => $rec[0]["copy_kbn"]
					,'hachu_no_moto' => $rec[0]["hachu_no_moto"]
					,'hd_biko1'      => $rec[0]["hd_biko1"]
					,'hd_biko2'      => $rec[0]["hd_biko2"]
					,'v_free1'       => $rec[0]["v_free1"]
					,'v_free2'       => $rec[0]["v_free2"]
					,'v_free3'       => $rec[0]["v_free3"]
					,'v_free4'       => $rec[0]["v_free4"]
					,'v_free5'       => $rec[0]["v_free5"]
					,'n_free1'       => $rec[0]["n_free1"]
					,'n_free2'       => $rec[0]["n_free2"]
					,'n_free3'       => $rec[0]["n_free3"]
					,'n_free4'       => $rec[0]["n_free4"]
					,'n_free5'       => $rec[0]["n_free5"]
					,'d_free1'       => $rec[0]["d_free1"]
					,'d_free2'       => $rec[0]["d_free2"]
					,'d_free3'       => $rec[0]["d_free3"]
                                        ));       
                    
                                        $hachu_rec = array();      
                                        //伝票用に赤伝データの確保
//                                        $hachu_rec = array( "seko_no" => $rec[0]["seko_no"], "hachu_no" => $rec[0]["hachu_no"] ,
//                                                            "siire_cd" => $rec[0]["siire_cd"], "report_cd" => $rec[0]["report_cd"], 
//                                                            "ha_etc_kbn" => $rec[0]["ha_etc_kbn"], "ha_syori_kbn" => $rec[0]["ha_syori_kbn"] );
                                        $hachu_rec = array( "seko_no" => $rec[0]["seko_no"], "hachu_no" => $max_seko_no ,
                                                            "siire_cd" => $rec[0]["siire_cd"], "report_cd" => $rec[0]["report_cd"], 
                                                            "ha_etc_kbn" => $rec[0]["ha_etc_kbn"], "ha_syori_kbn" => $rec[0]["ha_syori_kbn"] );
                                        $rec_ary[] = $hachu_rec;         
                    
                }
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        
        //return array($cnt,$akaden_no,$rec_ary, $cancel_no);
        return array($cnt,$akaden_no,$rec_ary);
    }    
    /**
     * 発注取消(発注確認リスト)
     *
     * <AUTHOR>  Yanagiso
     * @since 2014/05/22
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function hachulistCancel($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 0;//未発注
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
//        $hacyu_cnt= 1;
        $status_kbn= 2;  //キャンセル
//        $i_chk=implode(',',$params);
//        $i_chk=implode(' AND hachu_no=',$params);
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,status_kbn=:status_kbn
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no IN (:hachu_no)
END_OF_SQL;
        try {
            foreach ($params as $value) {
                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'status_kbn' => $status_kbn
                                       ,'seko_no' => $seko_no
                                       ,'hachu_no' => $value
                                        ));
            }
        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();

        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        
        return $cnt;
    }
    
    /**
     * 発注処理の発注確認
     *
     * <AUTHOR>  KyouNan
     * @since 2014/03/20
     * @param string $sekoNo 施行番号
     * @param string $login_cd user code
     * @param array $params selected hachu
     */
    public function onehachuCancel($login_cd,$params,$seko_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $hc_denpyo_no= $this->getAutoDenpyoNo($db);
        $order_flg= 0;
        $order_ymd=date("Y/m/d H:i:s");
        $order_tanto_cd= $login_cd;
        $chk_order_flg=0;
        $chk_order_ymd=date("Y/m/d H:i:s");
        $chk_order_tanto_cd= $login_cd;
        $hacyu_cnt= 1;
        $status_kbn= 1;
//        $i_chk=implode(',',$params);
//        $i_chk=implode(' AND hachu_no=',$params);
        $sql = <<< END_OF_SQL
        UPDATE
            seko_hachu_info
        SET 
            hc_denpyo_no = :hc_denpyo_no
            ,order_flg=:order_flg
            ,order_ymd=:order_ymd
            ,order_tanto_cd=:order_tanto_cd
            ,chk_order_flg=:chk_order_flg
            ,chk_order_ymd=:chk_order_ymd
            ,chk_order_tanto_cd=:chk_order_tanto_cd
        WHERE
            delete_flg = 0
        AND seko_no = :seko_no AND hachu_no = :i_chk
END_OF_SQL;
        try {

                 $cnt = $db->easyExecute($sql, array(
                                        'hc_denpyo_no' => $hc_denpyo_no
                                       ,'order_flg' => $order_flg
                                       ,'order_ymd' => $order_ymd
                                       ,'order_tanto_cd' => $order_tanto_cd
                                       ,'chk_order_flg' => $chk_order_flg
                                       ,'chk_order_ymd' => $chk_order_ymd
                                       ,'chk_order_tanto_cd' => $chk_order_tanto_cd
                                       ,'seko_no' => $seko_no
                                       ,'i_chk' => $params
                                        ));

        } catch (Exception $exc) {
            echo $exc->getTraceAsString();
        }
        $db->commit();
        
        //発注完了フラグを更新する
        App_HachuLib::updateOrderFinish($seko_no);
        
        return $cnt;
    }
    
    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR>  KyouNan
     * @since 2014/2/12
     * @return array 施行基本情報
     */
    public function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.bumon_cd         -- 部門コード
            ,k.moushi_cd        -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,k.daicho_no_eria   -- 台帳番号（エリア）
            ,k.daicho_no_mm     -- 台帳番号（月）
            ,k.daicho_no_seq    -- 台帳番号（連番）
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,TRIM(k.k_seinengappi_ymd) AS k_seinengappi_ymd  -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kg_setai_cd      -- 世帯主コード
            ,k.kg_setai_kbn     -- 世帯主区分
            ,k.kj_kbn           -- 住民登録住所の現住所に同じチェックボックス
            ,k.kj_yubin_no      -- 住民登録住所郵便番号
            ,k.kj_addr1         -- 住民登録住所1
            ,k.kj_tel           -- 住民登録住所TEL
            ,k.kj_addr2         -- 住民登録住所2
            ,k.kj_setai_cd      -- 住民登録住所世帯主コード
            ,k.kj_setai_kbn     -- 住民登録住所世帯主区分
            ,k.kh_kbn           -- 本籍の現住所に同じチェックボックス
            ,k.kh_yubin_no      -- 本籍郵便番号
            ,k.kh_addr1         -- 本籍住所1
            ,k.kh_hito_cd       -- 筆頭者コード
            ,k.kh_hito_kbn      -- 筆頭者区分
            ,k.kh_addr2         -- 本籍住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.souke_nm         -- 葬家
            ,k.souke_tel        -- 葬家TEL
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.syushi_cd       -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_cd        -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
            ,k.sd_hakko_kbn     -- 診断書発行区分
            ,k.sd_step_kbn      -- 診断書手続
            ,k.sd_yotei_ymd     -- 診断書発行予定時刻
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD HH24:MI') AS sd_yotei_ymd-- 診断書発行予定スタンプ
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD') AS sd_yotei_date-- 診断書発行予定時日付のみ
            ,TO_CHAR(k.sd_yotei_ymd ,'HH24:MI') AS sd_yotei_time-- 診断書発行予定時刻のみ
            ,k.sd_copy_cnt      -- 診断書コピー枚数
            ,k.tk_cyonaikai_nm  -- 町内会名
            ,k.tk_kumicyo_nm    -- 隣組長
            ,k.tk_house_cnt     -- 軒数
            ,k.tk_person_cnt    -- 人数
            ,k.hs_gyomu_cd      -- 搬送業務コード
            ,k.hs_gyomu_kbn     -- 搬送業務区分
            ,k.hs_spot_cd       -- お伺い先コード
            ,k.hs_anchi_nm      -- 安置先
            ,k.kasoba_cd        -- 火葬場コード
            ,jm.jyusho_lnm AS kasoba_nm -- 火葬場名
            ,k.az_death_cnt     -- 死亡診断書枚数
            ,k.az_inkan_kbn     -- 印鑑
            ,k.az_photo_cnt     -- 御写真枚数
            ,k.az_gojokai_nm    -- 互助会証書名称
            ,k.m_nm             -- 喪主名
            ,k.m_knm            -- 喪主名カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_gengo          -- 喪主生年月日元号
            ,k.m_seinengappi_ymd-- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_kbn           -- 喪主 故人に同じ
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mj_kbn           -- 喪主 住民登録住所の故人に同じ
            ,k.mj_yubin_no      -- 喪主住民登録住所郵便番号
            ,k.mj_addr1         -- 喪主住民登録住所1
            ,k.mj_tel           -- 喪主住民登録住所TEL
            ,k.mj_addr2         -- 喪主住民登録住所2
            ,k.mh_kbn           -- 喪主 本籍の故人に同じ
            ,k.mh_yubin_no      -- 喪主本籍住所郵便番号
            ,k.mh_addr1         -- 喪主本籍住所1
            ,k.mh_addr2         -- 喪主本籍住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.sekyu_kbn        -- 請求先の喪主に同じ
            ,k.sekyu_cd         -- 請求先コード
            ,TO_CHAR(k.sougi_ymd ,'YYYY/MM/DD') AS sougi_ymd         -- 葬儀日
        FROM
            seko_kihon_info k
            LEFT OUTER JOIN tanto_mst t1
            ON  (
                    k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t2
            ON  (
                    k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
                )
            LEFT OUTER JOIN nm_jyusho_mst jm
            ON  (
                    k.kasoba_cd = jm.jyusho_cd
                AND jm.jyusho_kbn = 3
                AND jm.delete_flg = 0
                )
        WHERE
            k.seko_no = :seko_no
        AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * コード名称マスタ取得処理
     * 
     * <AUTHOR>  KyouNan
     * @since      2014/02/13
     * @return  array コード名称マスタ
     */
    private function getCodeNameMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタを取得する

        $sql = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
        FROM
            code_nm_mst
        WHERE
            delete_flg = 0
        AND code_kbn IN (
             ?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
                        )
        ORDER BY
            code_kbn
            ,kbn_value_cd_num
                ";

        $select = $db->easySelect($sql, array(
            self::CODE_KBN_MOUSHI_KBN,
            self::CODE_KBN_SOUGI_KBN,
            self::CODE_KBN_KAIIN_KBN,
            self::CODE_KBN_HAIGU_KBN,
            self::CODE_KBN_GENGO,
            self::CODE_KBN_KEISHIKI_KBN,
            self::CODE_KBN_SETAI_KBN,
            self::CODE_KBN_HITO_KBN,
            self::CODE_KBN_NYUKAN_KYO,
            self::CODE_KBN_SYUKAN_KYO,
            self::CODE_KBN_KASO_KYO,
            self::CODE_KBN_KANYU_DANTAI,
            self::CODE_KBN_YOTO_KBN,
            self::CODE_KBN_HS_GYOMU_KBN,
            self::CODE_KBN_SYUHA_KBN,
            self::CODE_KBN_SYUSHI_KBN,
            self::CODE_KBN_ZOKU_KBN
        ));
        return $select;
    }

    /**
     * コード名称マスタフィルター処理
     * 
     * <AUTHOR>  KyouNan
     * @since      2014/02/14
     * @param $dataCodeNameMst コード名称マスタ配列
     * @param $kbn 区分値コード数値
     * @param $key 通常はkbn_value_cd_numをキーにtureの場合はkbn_value_cdをキーに、
     * @return  array コード名称マスタ
     */
    private function filter($dataCodeNameMst, $kbn) {
        $this->_codeKbn = $kbn;
        $codeNames = array_filter($dataCodeNameMst, function ($item) {
            return $item['code_kbn'] === $this->_codeKbn;
        }
        );
        return $codeNames;
    }   
    
    /**
     * 湯灌情報保存処理 
     *
     * <AUTHOR> Kyounan
     * @since 2014/02/20
     * @param Msi_Sys_Db $db db
     * @param array $dataYukanInfo 施行互助会情報データ
     * @return int 更新件数
     */
    public function saveYukanInfo($db, $dataYukanInfo, $seko_no) {
        // 更新対象外項目設定
        $except = array();
//        $this->_sekoNo = App_Utils::getSessionData('seko_no');
        if (is_null($dataYukanInfo["seko_no"])){
            $this->_sekoNo = $seko_no;
        }else{
            $this->_sekoNo = $dataYukanInfo["seko_no"];
        }
//Msi_Sys_Utils::debug('saveYukanInfo dataYukanInfo['. Msi_Sys_Utils::dump($dataYukanInfo).']');
//Msi_Sys_Utils::debug('saveYukanInfo seko_no['. $this->_sekoNo.']');

        $sql = "SELECT seko_no
                from seko_yukan_info
                where seko_no= :seko_no";
        $select_yukan = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));


        $selectYukanInfo = $this->dataYukan($this->_sekoNo);
        if ($select_yukan['seko_no'] == '') {
//            array_push($except, 'yukan_date','yukan_time','hachusk_1','siire_cd','order_flg',"seko_tanto_nm","seko_tanto_cd","yukanbasho_kbn","yukanbasho_addr","yukanbasho_name","kg_yubin_no","kg_addr1","kg_addr2","_mod_ts","hachu_no","syushi_code_kbn","syushi_cd","syushi_kbn","syuha_code_kbn","syuha_cd","syuha_kbn","syuha_nm",'order_flg',"chk_order_flg","chk_order_ymd","chk_order_tanto_cd");
            array_push($except, 'yukan_date'
					,'yukan_time','hachusk_1'
					,'siire_cd','order_flg'
					,"seko_tanto_nm","seko_tanto_cd"
					,"yukanbasho_kbn","yukanbasho_addr"
					,"yukanbasho_name","kg_yubin_no"
					,"kg_addr1","kg_addr2","_mod_ts"
					,"hachu_no","syushi_code_kbn"
					,"syushi_cd","syushi_kbn"
					,"syuha_code_kbn","syuha_cd"
					,"syuha_kbn","syuha_nm"
					,'order_flg'
					,"chk_order_flg","chk_order_ymd"
					,"chk_order_tanto_cd"
					,"shiire_zei_kbn"
					,"shohin_kbn"
                                        ,"kg_tel");
            $dataYukanInfo['seko_no']=$this->_sekoNo;	// 施行№
            
            $dataYukanInfo['delete_flg']=0;				// 削除フラグ
            $sql = $this->makeInsertSQL("seko_yukan_info", $dataYukanInfo,$except);			// 施行湯灌情報
            
            $where['seko_no'] = $dataYukanInfo['seko_no'];  // 施行番号
            $where['shohin_kbn'] =$dataYukanInfo['shohin_kbn'];  // 商品区分
            $where['delete_flg'] = 0;  // 削除フラグ
 
            $hachuOneRow['siire_cd']=$dataYukanInfo['siire_cd'];			// 仕入先コード
	        if ($dataYukanInfo['yu_basyo_cd'] === '02') {
	            $hachuOneRow['delivery_kbn']=1;		// 納品場所区分
	        } else {
	            $hachuOneRow['delivery_kbn']=9;		// 納品場所区分
	        }
            $hachuOneRow['nonyu_cd']=null;									// 納入先コード
            $hachuOneRow['nonyu_nm']=$dataYukanInfo['yu_spot_nm'];			// 納品場所（納入先名）
            $hachuOneRow['nonyu_ymd']=$dataYukanInfo['yukan_ymd'];			// 納品（納入）日時
            $hachusql=$this->makeUpdateSQL('seko_hachu_info', $hachuOneRow, $where);		// 施行発注管理情報
//Msi_Sys_Utils::debug('saveYukanInfo insert update seko_hachu_info['. $hachusql.']');
            
            $denpyomsiOneRow['siire_cd']=$dataYukanInfo['siire_cd'];			// 仕入先コード
	        if ($dataYukanInfo['yu_basyo_cd'] === '02') {
	            $denpyomsiOneRow['delivery_kbn']=1;		// 納品場所区分
	        } else {
	            $denpyomsiOneRow['delivery_kbn']=9;		// 納品場所区分
	        }
            $denpyomsiOneRow['nonyu_nm']=$dataYukanInfo['yu_spot_nm'];			// 納品場所（納入先名）
            $denpyomsiOneRow['nonyu_dt']=$dataYukanInfo['yukan_ymd'];			// 納品（納入）日時
            $denpyomsisql=$this->makeUpdateSQL('juchu_denpyo_msi', $denpyomsiOneRow, $where);	// 受注伝票明細
			
            //2018/12/05 DEL Kayo $kihonwhere['seko_no'] = $dataYukanInfo['seko_no'];  // 施行番号
            //2018/12/05 DEL Kayo $kihonOneRow['seko_tanto_cd']=$dataYukanInfo['seko_tanto_cd'];
            //2018/12/05 DEL Kayo $sekokihonsql=$this->makeUpdateSQL('seko_kihon_info', $kihonOneRow, $kihonwhere);
        } else {
//            array_push($except, 'seko_no', 'yukan_date','yukan_time','hachusk_1','tanto_nm','siire_cd','order_flg','seko_tanto_cd','seko_tanto_nm',"syushi_code_kbn","syushi_cd","syushi_kbn","syuha_code_kbn","syuha_cd","syuha_kbn","syuha_nm","seko_tanto_nm","seko_tanto_cd","yukanbasho_kbn","yukanbasho_addr","yukanbasho_name","kg_yubin_no","kg_addr1","kg_addr2","_mod_ts","hachu_no","chk_order_flg","chk_order_ymd","chk_order_tanto_cd");
            array_push($except, 'seko_no', 'yukan_date','yukan_time','hachusk_1','tanto_nm','siire_cd','order_flg'
					,'seko_tanto_cd','seko_tanto_nm',"syushi_code_kbn","syushi_cd","syushi_kbn","syuha_code_kbn"
					,"syuha_cd","syuha_kbn","syuha_nm","seko_tanto_nm","seko_tanto_cd","yukanbasho_kbn"
					,"yukanbasho_addr","yukanbasho_name","kg_yubin_no","kg_addr1","kg_addr2","_mod_ts"
					,"hachu_no","chk_order_flg","chk_order_ymd","chk_order_tanto_cd","shiire_zei_kbn"
					,"_req_id"
					,"_cre_user"
					,"_cre_ts"
					,"_mod_user"
					,"_mod_ts"
					,"_mod_cnt"
					,"shohin_kbn"
                                        ,"kg_tel"
					);
            $dataYukanInfo['delete_flg']=0;				// 削除フラグ
            // 条件部
            $where['seko_no'] = $this->_sekoNo;		//$dataYukanInfo['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 湯灌更新SQL
            $sql = $this->makeUpdateSQL('seko_yukan_info', $dataYukanInfo, $where, $except);		// 施行湯灌情報
            
            $where['shohin_kbn'] =$dataYukanInfo['shohin_kbn'];  //商品区分
            $hachuOneRow['siire_cd']=$dataYukanInfo['siire_cd'];			// 仕入先コード
	        if ($dataYukanInfo['yu_basyo_cd'] === '02') {
	            $hachuOneRow['delivery_kbn']=1;		// 納品場所区分
	        } else {
	            $hachuOneRow['delivery_kbn']=9;		// 納品場所区分
	        }
            $hachuOneRow['nonyu_cd']=null;									// 納入先コード
            $hachuOneRow['nonyu_nm']=$dataYukanInfo['yu_spot_nm'];			// 納品場所（納入先名）
            $hachuOneRow['nonyu_ymd']=$dataYukanInfo['yukan_ymd'];			// 納品（納入）日時
            $hachusql=$this->makeUpdateSQL('seko_hachu_info', $hachuOneRow, $where);				// 施行発注管理情報
//Msi_Sys_Utils::debug('saveYukanInfo update update seko_hachu_info['. $hachusql.']');

            $denpyomsiOneRow['siire_cd']=$dataYukanInfo['siire_cd'];			// 仕入先コード
	        if ($dataYukanInfo['yu_basyo_cd'] === '02') {
	            $denpyomsiOneRow['delivery_kbn']=1;		// 納品場所区分
	        } else {
	            $denpyomsiOneRow['delivery_kbn']=9;		// 納品場所区分
	        }
            $denpyomsiOneRow['nonyu_nm']=$dataYukanInfo['yu_spot_nm'];			// 納品場所（納入先名）
            $denpyomsiOneRow['nonyu_dt']=$dataYukanInfo['yukan_ymd'];			// 納品（納入）日時
            $denpyomsisql=$this->makeUpdateSQL('juchu_denpyo_msi', $denpyomsiOneRow, $where);		// 受注伝票明細
            
            //2018/12/05 DEL Kayo $kihonwhere['seko_no'] = $this->_sekoNo;//$dataYukanInfo['seko_no'];  // 施行番号
            //2018/12/05 DEL Kayo $kihonOneRow['seko_tanto_cd']=$dataYukanInfo['seko_tanto_cd'];
            //2018/12/05 DEL Kayo $sekokihonsql=$this->makeUpdateSQL('seko_kihon_info', $kihonOneRow, $kihonwhere);
        }
        $cnt = $db->easyExecute($sql);
        $hachuCnt = $db->easyExecute($hachusql);
        $denpyomsiCnt = $db->easyExecute($denpyomsisql);
        // 2018/12/05 DEL Kayo $kihonCnt = $db->easyExecute($sekokihonsql);
        
        $keyHash = array( 'seko_no'         => $this->_sekoNo,
                          'nitei_kbn'       => "2");
        $dataHash = array('spot_code_kbn'   => $dataYukanInfo['yu_basyo_code_kbn'],
                          'spot_cd'         => $dataYukanInfo['yu_basyo_cd'],
                          'basho_kbn'       => $dataYukanInfo['yu_basyo_kbn'],
                          'basho_cd'        => $dataYukanInfo['yu_spot_cd'],
                          'basho_nm'        => $dataYukanInfo['yu_spot_nm']);

        DataMapper_Utils::upsert( $db, 'seko_nitei', $keyHash, $dataHash );
        
        return $cnt;
    }

	/**
	* 花運搬情報保存処理 
	*
	* <AUTHOR> Ogino
	* @since 2014/06/21
	* @param Msi_Sys_Db $db db
	* @param array $dataHanaunpanInfo 花運搬情報データ
	* @return int 更新件数
	*/
	public function saveHanaunpanInfo($db, $dataHanaunpanInfo, $seko_no) {
		// 更新対象外項目設定
		$except = array();
//        $this->_sekoNo = App_Utils::getSessionData('seko_no');
        $this->_sekoNo = $dataHanaunpanInfo["seko_no"];
//Msi_Sys_Utils::debug('saveHanaunpanInfo dataHanaunpanInfo['. Msi_Sys_Utils::dump($dataHanaunpanInfo).']');
//Msi_Sys_Utils::debug('saveHanaunpanInfo seko_no['. $this->_sekoNo.']');
		$selectHanaunpanInfo = $this->dataHanaunpan($this->_sekoNo);
        try {
		if ($dataHanaunpanInfo['seko_no'] == '') {
			array_push($except, "siire_cd","syukandate","syukantime","sougidate","sougitime","_mod_ts","hachu_no","report_cd","hachusk_1","order_flg","ikidate_1","ikitime_from","ikitime_to","kaeridate_1","kaeritime_from","kaeritime_to","hachu_no","tanto_cd","chk_order_flg","chk_order_ymd","chk_order_tanto_cd"
                                    ,"syukan_dt_ymd","syukan_dt_time","sougi_dt_ymd","sougi_dt_time"
                                    ,"start_from_dt_ymd","start_from_dt_time","start_to_dt_ymd","start_to_dt_time"
                                    ,"return_from_dt_ymd","return_from_dt_time","return_to_dt_ymd","return_to_dt_time");
			$dataHanaunpanInfo['seko_no']=$this->_sekoNo;	// 施行№
                        
                        if (is_null($dataHanaunpanInfo['seko_no'])){
                            $dataHanaunpanInfo['seko_no'] = $seko_no;
                        }else if ($dataHanaunpanInfo['seko_no'] == ''){
                            $dataHanaunpanInfo['seko_no'] = $seko_no;
                        }
                        
			$dataHanaunpanInfo['delete_flg']=0;				// 削除フラグ
			$sql = $this->makeInsertSQL("seko_hanaunpan_info", $dataHanaunpanInfo,$except);			// 花運搬湯灌情報
//Msi_Sys_Utils::debug('saveHanaunpanInfo insert sql['. $sql.']');

			$where['seko_no'] = $dataHanaunpanInfo['seko_no'];			// 施行番号                        
			//$where['shohin_kbn'] = "0900";  							// 商品区分
			//$where['shohin_cd'] = "0001130";							// 商品コード
                        $req_hachu = $db->easySelect("select shohin_kbn, shohin_cd from seko_hachu_info where seko_no=:seko_no and hachu_no=:hachu_no",
                                array('seko_no' => $dataHanaunpanInfo['seko_no'],'hachu_no' => $dataHanaunpanInfo['hachu_no'])); 
			$where['shohin_kbn'] = $req_hachu[0]['shohin_kbn']; 							// 商品区分
			$where['shohin_cd'] = $req_hachu[0]['shohin_cd'];							// 商品コード                      
                        
			$where['delete_flg'] = 0;									// 削除フラグ
			$hachuOneRow['siire_cd']=$dataHanaunpanInfo['siire_cd'];	// 仕入先コード
			$hachusql=$this->makeUpdateSQL('seko_hachu_info', $hachuOneRow, $where);		// 施行発注管理情報

			$denpyomsiOneRow['siire_cd']=$dataHanaunpanInfo['siire_cd'];			// 仕入先コード
			$denpyomsiOneRow['siire_lnm']=$dataHanaunpanInfo['hachusk_1'];			// 仕入先名
			$denpyomsisql=$this->makeUpdateSQL('juchu_denpyo_msi', $denpyomsiOneRow, $where);	// 受注伝票明細

//			$kihonwhere['seko_no'] = $dataHanaunpanInfo['seko_no'];  // 施行番号
//			$kihonOneRow['seko_tanto_cd']=$dataHanaunpanInfo['seko_tanto_cd'];
//			$sekokihonsql=$this->makeUpdateSQL('seko_kihon_info', $kihonOneRow, $kihonwhere);
		} else {
			array_push($except, "siire_cd","syukandate","syukantime","sougidate","sougitime","_mod_ts","hachu_no","report_cd","hachusk_1","order_flg","ikidate_1","ikitime_from","ikitime_to","kaeridate_1","kaeritime_from","kaeritime_to","hachu_no","tanto_cd","chk_order_flg","chk_order_ymd","chk_order_tanto_cd","delete_flg"
                                    ,"syukan_dt_ymd","syukan_dt_time","sougi_dt_ymd","sougi_dt_time"
                                    ,"start_from_dt_ymd","start_from_dt_time","start_to_dt_ymd","start_to_dt_time"
                                    ,"return_from_dt_ymd","return_from_dt_time","return_to_dt_ymd","return_to_dt_time");
			// 条件部
			$where['seko_no'] = $dataHanaunpanInfo['seko_no'];  // 施行番号
			$where['delete_flg'] = 0;  // 削除フラグ
			// 湯灌更新SQL
			$sql = $this->makeUpdateSQL('seko_hanaunpan_info', $dataHanaunpanInfo, $where, $except);		// 施行花運搬情報
//Msi_Sys_Utils::debug('saveHanaunpanInfo update sql['. $sql.']');

			$where['seko_no'] = $dataHanaunpanInfo['seko_no'];			// 施行番号
			//$where['shohin_kbn'] = "0900";  							// 商品区分
			//$where['shohin_cd'] = "0001130";// 商品コード
                        $where['hachu_no'] = $dataHanaunpanInfo['hachu_no'];							
			$where['delete_flg'] = 0;									// 削除フラグ
			$hachuOneRow['siire_cd']=$dataHanaunpanInfo['siire_cd'];	// 仕入先コード
			$hachusql=$this->makeUpdateSQL('seko_hachu_info', $hachuOneRow, $where);		// 施行発注管理情報
                        
                        $req_hachu = $db->easySelect("select jc_msi_no from seko_hachu_info where seko_no=:seko_no and hachu_no=:hachu_no",
                                array('seko_no' => $dataHanaunpanInfo['seko_no'],'hachu_no' => $dataHanaunpanInfo['hachu_no'])); 
                        
                        $where2['seko_no'] = $dataHanaunpanInfo['seko_no'];		
                        $where2['msi_no'] = $req_hachu[0]['jc_msi_no'];
			$where2['delete_flg'] = 0;
                        
			$denpyomsiOneRow['siire_cd']=$dataHanaunpanInfo['siire_cd'];			// 仕入先コード
			$denpyomsiOneRow['siire_lnm']=$dataHanaunpanInfo['hachusk_1'];			// 仕入先名
			$denpyomsisql=$this->makeUpdateSQL('juchu_denpyo_msi', $denpyomsiOneRow, $where2);	// 受注伝票明細

//			$kihonwhere['seko_no'] = $dataHanaunpanInfo['seko_no'];  // 施行番号
//			$kihonOneRow['seko_tanto_cd']=$dataHanaunpanInfo['seko_tanto_cd'];
//			$sekokihonsql=$this->makeUpdateSQL('seko_kihon_info', $kihonOneRow, $kihonwhere);
		}
		$cnt = $db->easyExecute($sql);
		$hachuCnt = $db->easyExecute($hachusql);
		$denpyomsiCnt = $db->easyExecute($denpyomsisql);
//		$kihonCnt = $db->easyExecute($sekokihonsql);
		return $cnt;
        } catch (Exception $e) {
			return 0;
        }


	}

    /**
     *
     * currentのcss名を取得する
     * 'customer' => 'お客様情報'
     * 'schedule' => 'タイムスケジュール'
     * 'sosogirei' => '葬送儀礼'
     * 'gift' => '返礼品'
     * 'cook' => '料理'
     * 'danbarai' => '壇払'
     * 'betto' => '別途費用'
     * 'betto' => '立替費用'
     * 'nebiki' => '値引'
     *
     * <AUTHOR>  KyouNan
     * @since 2014/2/4
     * @return schedule
     */
    public function getCssClassName() {
        return 'customer';
    }
    
    /**
     *
     * 発注情報
     *
     * <AUTHOR> KyouNan
     * @since 2014/2/21
     * @return array 発注情報
     */
    public function getSekoHaChuCol($seko_no) {
        
       $datakbn = App_Utils::getSessionData('data_kbn');
       
       if(!isset($datakbn) || empty($datakbn)) {
           $datakbn  = 0;
       }       
       
       //if ($datakbn == 0){           
       //    $sqlsb2 = " AND (shi.data_kbn <> 4) ";   //未設定の場合は別注品以外が対象になる
       //} 
       //else {           
       //    $sqlsb2 = " AND (shi.data_kbn = ".$datakbn.") ";  
       //}     
       $sqlsb2 = "";
       if ($datakbn == 4){           
           $sqlsb2 = " AND (shi.data_kbn = 4) ";   //別注品を指定した場合は別注品のみが対象になる
       } else{
           $sqlsb2 = " AND (shi.data_kbn <> 4) ";
       } 
       
        $dataSekoHaChuCol = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                shi.seko_no  -- 施行番号
                ,shi.hachu_no  -- 発注管理番号
                ,shi.crt_row_no  -- 行番号
                ,shi.siire_cd  -- 仕入先コード
                ,shi.report_cd  -- 帳票コード
                ,shi.ha_rp_code_kbn  -- 発注書区分コード区分
                ,shi.ha_rp_cd  -- 発注書区分コード
                ,shi.ha_rp_kbn  -- 発注書区分
                ,shi.ha_entry_kbn  -- 発注書入力区分
                ,shi.ha_syori_kbn  -- 発注書処理区分
                ,shi.ha_etc_kbn  -- 発注書その他区分
                ,shi.hachu_den_kbn  -- 発注伝票区分
                ,shi.dai_bunrui_cd  -- 大分類コード
                ,shi.chu_bunrui_cd  -- 中分類コード
                ,shi.shohin_kbn  -- 商品区分
                ,shi.shohin_cd  -- 商品コード
                ,shi.shohin_nm  -- 商品名
                ,shi.shohin_tkiyo_nm  -- 商品摘要名
                ,shi.hachu_suryo  -- 数量
                ,shi.tani_cd  -- 単位コード
                ,shi.hanbai_tnk  -- 販売単価
                ,shi.hachu_tnk  -- 発注単価
                ,shi.hachu_prc  -- 発注金額
                ,shi.nebiki_prc  -- 値引額
                ,shi.nonyu_cd  -- 納入先コード
                ,shi.nonyu_nm  -- 納品場所（納入先名）
                ,shi.nonyu_ymd  -- 納品（納入）日時
                ,TO_CHAR(shi.nonyu_ymd,'yyyy/mm/dd HH24:MI') as nonyu_ymd_sub  -- 納品（納入）日時         
                ,shi.jc_denpyo_no  -- 受注伝票NO
                ,shi.jc_msi_no  -- 受注明細№
                ,shi.hc_denpyo_no  -- 発注伝票NO
                ,shi.order_flg  -- 発注済み
                ,shi.order_ymd  -- 発注日時
                ,shi.order_tanto_cd  -- 発注担当者コード
                ,shi.chk_order_flg  -- 発注確認済み
                ,shi.chk_order_ymd  -- 発注確認日時
                ,shi.chk_order_tanto_cd  -- 発注確認担当者コード
                ,shi.hacyu_cnt  -- 発注回数
                ,shi.status_kbn  -- 発注ステータス
                ,shi.hachu_no_moto  -- コピー元発注管理番号
                ,shi.hd_biko1  -- 備考１
                ,shi.hd_biko2  -- 備考２
                ,shi.v_free1  -- 文字フリー項目１
                ,shi.v_free2  -- 文字フリー項目２
                ,shi.v_free3  -- 文字フリー項目３
                ,shi.v_free4  -- 文字フリー項目４
                ,shi.v_free5  -- 文字フリー項目５
                ,shi.n_free1  -- 数字フリー項目１
                ,shi.n_free2  -- 数字フリー項目２
                ,shi.n_free3  -- 数字フリー項目３
                ,shi.n_free4  -- 数字フリー項目４
                ,shi.n_free5  -- 数字フリー項目５
                ,shi.d_free1  -- 日付フリー項目１
                ,shi.d_free2  -- 日付フリー項目２
                ,shi.d_free3  -- 日付フリー項目３
                ,shi.delete_flg  -- 削除フラグ
                ,shi._req_id  -- 処理要求ID
                ,shi._cre_user  -- 作成者
                ,shi._cre_ts  -- 作成日時
                ,shi._mod_user  -- 最終更新者
                ,shi._mod_ts  -- 最終更新日時
                ,shi._mod_cnt  -- 更新回数
                ,si.siire_lnm -- 仕入先名
                ,rkm.report_lnm -- 帳票コード
                ,rkm.ha_entry_kbn -- 発注書入力区分
                ,skm.shohin_kbn_nm -- 商品区分名
                ,skn.sougi_ymd -- 葬儀日
                ,skn.souke_nm -- 葬家
                ,skn.k_nm -- 故人名
                ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn='0010' AND kbn_value_cd_num=skn.moushi_kbn) AS moushi_kbn  -- 申込区分code_kbn='0010'skn.moushi_code_kbn
                ,(CASE WHEN shi.status_kbn = 0 THEN 'なし' WHEN shi.status_kbn = 1 THEN '手配中' WHEN shi.status_kbn = 2 THEN 'キャンセル' WHEN shi.status_kbn = 3 THEN '赤伝' ELSE 'なし' END) AS status_kbn
            FROM
                seko_hachu_info shi
                LEFT JOIN siire_mst si
                ON  (
                    shi.siire_cd = si.siire_cd
                    AND si.delete_flg = 0
                )
                LEFT JOIN report_kanri_mst rkm
                ON  (
                    shi.report_cd = rkm.report_cd
                    AND rkm.delete_flg = 0
                )
                LEFT JOIN SHOHIN_KBN_MST skm
                ON  (
                    shi.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0
                )
                LEFT JOIN seko_kihon_info skn
                ON  (
                    shi.seko_no = skn.seko_no
                    AND skm.delete_flg = 0
                )
            WHERE
                shi.seko_no='{$seko_no}'
                AND shi.delete_flg = 0
                 and (shi.status_kbn in (0,1))
                 and shi.ha_etc_kbn = 0".$sqlsb2.
           "ORDER BY
                skn.sougi_ymd DESC
                ,shi.ha_rp_cd ASC
                ,shi.hachu_no ASC
                ,shi.seko_no ASC
                ";
        $dataSekoHaChuCol = $db->easySelect($sql);
//        var_dump($dataSekoHaChuCol);die();
        return $dataSekoHaChuCol;
    }

    /**
     *
     * 発注情報(在庫データを取得)
     *
     * <AUTHOR> Kayo
     * @since 2014/2/21
     * @return array 発注情報(在庫商品)
     */
    public function getSekoHaChuColZaiko($seko_no) {

        $db = Msi_Sys_DbManager::getMyDb();
        $kaisya_cd  = null;
        $kaisyaInfo = DataMapper_KaisyaInfo::find($db);
        if(count($kaisyaInfo) > 0){
            $kaisya_cd = $kaisyaInfo[0]['kaisya_cd'];    // 貸出品出庫区分
        }
        $dataSekoHaChuCol = $db->easySelect(<<< END_OF_SQL
            SELECT
                 sk.seko_no		AS	seko_no				-- 施行番号
                ,'9999999997'	AS	hachu_no			-- 発注管理番号
                ,1				AS	crt_row_no			-- 行番号
                ,null			AS	siire_cd			-- 仕入先コード
                ,'99997'		AS	report_cd			-- 帳票コード
                ,null			AS	ha_rp_code_kbn		-- 発注書区分コード区分
                ,null			AS	ha_rp_cd			-- 発注書区分コード
                ,97				AS	ha_rp_kbn			-- 発注書区分
                ,99				AS	ha_entry_kbn		-- 発注書入力区分
                ,0				AS	ha_syori_kbn		-- 発注書処理区分
                ,0				AS	ha_etc_kbn			-- 発注書その他区分
                ,0				AS	hachu_den_kbn		-- 発注伝票区分
                ,null			AS	dai_bunrui_cd		-- 大分類コード
                ,null			AS	chu_bunrui_cd		-- 中分類コード
                ,null			AS	shohin_kbn			-- 商品区分
                ,'99999997'		AS	shohin_cd			-- 商品コード
                ,'貸出品出庫'	AS	shohin_nm			-- 商品名
                ,null			AS	shohin_tkiyo_nm		-- 商品摘要名
                ,1				AS	hachu_suryo			-- 数量
                ,null			AS	tani_cd				-- 単位コード
                ,0				AS	hanbai_tnk			-- 販売単価
                ,0				AS	hachu_tnk			-- 発注単価
                ,0				AS	hachu_prc			-- 発注金額
                ,0				AS	nebiki_prc			-- 値引額
                ,null			AS	nonyu_cd			-- 納入先コード
                ,null			AS	nonyu_nm			-- 納品場所（納入先名）
                ,null			AS	nonyu_ymd			-- 納品（納入）日時
                ,null			AS	nonyu_ymd_sub		-- 納品（納入）日時
                ,null			AS	jc_denpyo_no		-- 受注伝票NO
                ,1				AS	jc_msi_no			-- 受注明細№
                ,null			AS	hc_denpyo_no		-- 発注伝票NO
                ,CASE WHEN EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                                                        WHERE sk.seko_no	=	skb01.seko_no
                                                        AND skb01.delete_flg=	0
                                                        AND skb01.suryo		>	0)
                THEN -- 出荷対象あり
                CASE WHEN NOT EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                                                                  WHERE sk.seko_no		=	skb01.seko_no
                                                                  AND skb01.delete_flg	=	0
                                                                  AND skb01.suryo		>	0
                                                                AND skb01.shuko_status_kbn NOT IN (2,9) ) THEN 1 -- 出荷済み
                        WHEN EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                                                                WHERE sk.seko_no		=	skb01.seko_no
                                                                AND skb01.delete_flg	=	0
                                                                AND skb01.suryo			>	0
                                                                AND skb01.shuko_status_kbn IN (1,2,9) ) THEN 2 -- 一部出荷済み
                        ELSE 0 -- 未出庫
                END
                 ELSE 9           -- 対象外
                 END			AS	order_flg			-- 発注済み
                ,null			AS	order_ymd			-- 発注日時
                ,null			AS	order_tanto_cd		-- 発注担当者コード
                ,9				AS	chk_order_flg		-- 発注確認済み
                ,null			AS	chk_order_ymd		-- 発注確認日時
                ,null			AS	chk_order_tanto_cd	-- 発注確認担当者コード
                ,0				AS	hacyu_cnt			-- 発注回数
                ,0				AS	status_kbn			-- 発注ステータス
                ,null			AS	hachu_no_moto		-- コピー元発注管理番号
                ,null			AS	hd_biko1			-- 備考１
                ,null			AS	hd_biko2			-- 備考２
                ,null			AS	v_free1				-- 文字フリー項目１
                ,null			AS	v_free2				-- 文字フリー項目２
                ,null			AS	v_free3				-- 文字フリー項目３
                ,null			AS	v_free4				-- 文字フリー項目４
                ,null			AS	v_free5				-- 文字フリー項目５
                ,null			AS	n_free1				-- 数字フリー項目１
                ,null			AS	n_free2				-- 数字フリー項目２
                ,null			AS	n_free3				-- 数字フリー項目３
                ,null			AS	n_free4				-- 数字フリー項目４
                ,null			AS	n_free5				-- 数字フリー項目５
                ,null			AS	d_free1				-- 日付フリー項目１
                ,null			AS	d_free2				-- 日付フリー項目２
                ,null			AS	d_free3				-- 日付フリー項目３
                ,0				AS	delete_flg			-- 削除フラグ
                ,null			AS	req_id				-- 処理要求ID
                ,null			AS	_cre_user			-- 作成者
                ,null			AS	_cre_ts				-- 作成日時
                ,null			AS	_mod_user			-- 最終更新者
                ,null			AS	_mod_ts				-- 最終更新日時
                ,0				AS	_mod_cnt			-- 更新回数
                ,'倉庫'			AS	siire_lnm			-- 仕入先名
                ,'貸出品出庫'	AS	report_lnm			-- 帳票名
                ,'貸出品'		AS	shohin_kbn_nm		-- 商品区分名
                ,sk.sougi_ymd	AS	sougi_ymd			-- 葬儀日
                ,sk.souke_nm	AS	souke_nm			-- 葬家
                ,sk.k_nm		AS	k_nm				-- 故人名
                ,null			AS	moushi_kbn			-- 申込区分
                ,null			AS status_kbn
            FROM  seko_kihon_info sk
            LEFT JOIN (SELECT seko_no, sum(suryo) AS suryo FROM seko_kashidasi_bihn
            WHERE delete_flg = 0
            GROUP BY seko_no) skb
            ON skb.seko_no =  sk.seko_no
            INNER JOIN kaisya_info kaisya
            ON kaisya.kaisya_cd = :kaisya_cd
            AND 1 = kaisya.kashidashi_syuko_kbn
            AND 0 = kaisya.delete_flg
            WHERE sk.seko_no= :seko_no
            AND sk.delete_flg = 0
            AND (skb.suryo IS not null AND skb.suryo <> 0)
        UNION ALL
            SELECT
                sk.seko_no		AS	seko_no				-- 施行番号
               ,'9999999998'	AS	hachu_no			-- 発注管理番号
               ,1				AS	crt_row_no			-- 行番号
               ,null			AS	siire_cd			-- 仕入先コード
               ,'99998'		AS	report_cd			-- 帳票コード
               ,null			AS	ha_rp_code_kbn		-- 発注書区分コード区分
               ,null			AS	ha_rp_cd			-- 発注書区分コード
               ,98				AS	ha_rp_kbn			-- 発注書区分
               ,99				AS	ha_entry_kbn		-- 発注書入力区分
               ,0				AS	ha_syori_kbn		-- 発注書処理区分
               ,0				AS	ha_etc_kbn			-- 発注書その他区分
               ,0				AS	hachu_den_kbn		-- 発注伝票区分
               ,null			AS	dai_bunrui_cd		-- 大分類コード
               ,null			AS	chu_bunrui_cd		-- 中分類コード
               ,null			AS	shohin_kbn			-- 商品区分
               ,'99999998'		AS	shohin_cd			-- 商品コード
               ,'在庫品出庫'	AS	shohin_nm			-- 商品名
               ,null			AS	shohin_tkiyo_nm		-- 商品摘要名
               ,1				AS	hachu_suryo			-- 数量
               ,null			AS	tani_cd				-- 単位コード
               ,0				AS	hanbai_tnk			-- 販売単価
               ,0				AS	hachu_tnk			-- 発注単価
               ,0				AS	hachu_prc			-- 発注金額
               ,0				AS	nebiki_prc			-- 値引額
               ,null			AS	nonyu_cd			-- 納入先コード
               ,null			AS	nonyu_nm			-- 納品場所（納入先名）
               ,null			AS	nonyu_ymd			-- 納品（納入）日時
               ,null			AS	nonyu_ymd_sub		-- 納品（納入）日時
               ,null			AS	jc_denpyo_no		-- 受注伝票NO
               ,1				AS	jc_msi_no			-- 受注明細№
               ,null			AS	hc_denpyo_no		-- 発注伝票NO
               ,CASE WHEN EXISTS( SELECT * FROM juchu_denpyo_msi jdm00
                               ,juchu_denpyo jd00
                               ,shohin_mst sm00
                               WHERE sk.seko_no		=	jd00.seko_no
                               AND jd00.denpyo_no		=	jdm00.denpyo_no
                               AND jdm00.shohin_cd		=	sm00.shohin_cd
                               AND jd00.delete_flg		=	0
                               AND jdm00.delete_flg	=	0
                               AND sm00.delete_flg		=	0
                               AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
                               AND sm00.delete_flg		=	0
                               AND sk.moushi_kbn		=	jd00.data_kbn	-- 2015/02/08 ADD Kayo
                               AND sm00.zaiko_knri_kbn IN (1,2) )
               THEN -- 出荷対象あり
               CASE WHEN NOT EXISTS( SELECT * FROM juchu_denpyo_msi jdm00
                               ,juchu_denpyo	jd00
                               ,shohin_mst		sm00
                               WHERE sk.seko_no	=	jd00.seko_no
                               AND jd00.denpyo_no	=	jdm00.denpyo_no
                               AND jdm00.shohin_cd	=	sm00.shohin_cd
                               AND jd00.delete_flg	=	0
                               AND jdm00.delete_flg=	0
                               AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
                               AND sm00.delete_flg		=	0
                               AND sk.moushi_kbn		=	jd00.data_kbn	-- 2015/02/08 ADD Kayo
                               AND sm00.zaiko_knri_kbn IN (1)
                               AND jdm00.shuko_status_kbn NOT IN (2,9) )
               THEN 1 -- 出荷済み
               WHEN EXISTS( SELECT * FROM juchu_denpyo_msi jdm00
                               , juchu_denpyo jd00
                               , shohin_mst sm00
                               WHERE sk.seko_no	=	jd00.seko_no
                                       AND jd00.denpyo_no	=	jdm00.denpyo_no
                                       AND jdm00.shohin_cd	=	sm00.shohin_cd
                                       AND jd00.delete_flg	=	0
                                       AND jdm00.delete_flg=	0
                                       AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
                                       AND sm00.delete_flg		=	0
                                       AND sk.moushi_kbn		=	jd00.data_kbn	-- 2015/02/08 ADD Kayo
                                       AND sm00.zaiko_knri_kbn IN (1)
                                       AND jdm00.shuko_status_kbn IN (1,2,9) )
               THEN 2 -- 一部出荷済み
               ELSE 0 -- 未出庫
               END
               ELSE 9           -- 対象外
               END			AS	order_flg			-- 発注済み
               ,null			AS	order_ymd			-- 発注日時
               ,null			AS	order_tanto_cd		-- 発注担当者コード
               ,9				AS	chk_order_flg		-- 発注確認済み
               ,null			AS	chk_order_ymd		-- 発注確認日時
               ,null			AS	chk_order_tanto_cd	-- 発注確認担当者コード
               ,0				AS	hacyu_cnt			-- 発注回数
               ,0				AS	status_kbn			-- 発注ステータス
               ,null			AS	hachu_no_moto		-- コピー元発注管理番号
               ,null			AS	hd_biko1			-- 備考１
               ,null			AS	hd_biko2			-- 備考２
               ,null			AS	v_free1				-- 文字フリー項目１
               ,null			AS	v_free2				-- 文字フリー項目２
               ,null			AS	v_free3				-- 文字フリー項目３
               ,null			AS	v_free4				-- 文字フリー項目４
               ,null			AS	v_free5				-- 文字フリー項目５
               ,null			AS	n_free1				-- 数字フリー項目１
               ,null			AS	n_free2				-- 数字フリー項目２
               ,null			AS	n_free3				-- 数字フリー項目３
               ,null			AS	n_free4				-- 数字フリー項目４
               ,null			AS	n_free5				-- 数字フリー項目５
               ,null			AS	d_free1				-- 日付フリー項目１
               ,null			AS	d_free2				-- 日付フリー項目２
               ,null			AS	d_free3				-- 日付フリー項目３
               ,0				AS	delete_flg			-- 削除フラグ
               ,null			AS	req_id				-- 処理要求ID
               ,null			AS	_cre_user			-- 作成者
               ,null			AS	_cre_ts				-- 作成日時
               ,null			AS	_mod_user			-- 最終更新者
               ,null			AS	_mod_ts				-- 最終更新日時
               ,0				AS	_mod_cnt			-- 更新回数
               ,'倉庫'			AS	siire_lnm			-- 仕入先名
               ,'在庫品出庫'	AS	report_lnm			-- 帳票名
               ,'在庫品'		AS	shohin_kbn_nm		-- 商品区分名
               ,sk.sougi_ymd	AS	sougi_ymd			-- 葬儀日
               ,sk.souke_nm	AS	souke_nm			-- 葬家
               ,sk.k_nm		AS	k_nm				-- 故人名
               ,null			AS	moushi_kbn			-- 申込区分
               ,null			AS status_kbn
            FROM  seko_kihon_info sk
            LEFT JOIN	juchu_denpyo h
            ON	sk.seko_no		=	h.seko_no
            AND sk.moushi_kbn	=	h.data_kbn
            AND 0				=	h.delete_flg
            LEFT JOIN (SELECT denpyo_no, sum(juchu_suryo) AS suryo
            FROM juchu_denpyo_msi d
            LEFT JOIN shohin_mst sm
            ON	d.shohin_cd = sm.shohin_cd
            AND 0			= sm.delete_flg
            WHERE d.delete_flg = 0
            AND sm.zaiko_knri_kbn IN (1,2)
            GROUP BY denpyo_no) jd
            ON jd.denpyo_no =  h.denpyo_no
            INNER JOIN kaisya_info kaisya
            ON kaisya.kaisya_cd = :kaisya_cd
            AND 1 = kaisya.zaiko_syuko_kbn
            AND 0 = kaisya.delete_flg
            WHERE sk.seko_no= :seko_no
            AND sk.delete_flg = 0
            AND (jd.suryo IS not null AND jd.suryo <> 0)
        UNION ALL
            SELECT
                sk.seko_no		AS	seko_no				-- 施行番号
               ,'9999999999'	AS	hachu_no			-- 発注管理番号
               ,1				AS	crt_row_no			-- 行番号
               ,null			AS	siire_cd			-- 仕入先コード
               ,'99999'		AS	report_cd			-- 帳票コード
               ,null			AS	ha_rp_code_kbn		-- 発注書区分コード区分
               ,null			AS	ha_rp_cd			-- 発注書区分コード
               ,99				AS	ha_rp_kbn			-- 発注書区分
               ,99				AS	ha_entry_kbn		-- 発注書入力区分
               ,0				AS	ha_syori_kbn		-- 発注書処理区分
               ,0				AS	ha_etc_kbn			-- 発注書その他区分
               ,0				AS	hachu_den_kbn		-- 発注伝票区分
               ,null			AS	dai_bunrui_cd		-- 大分類コード
               ,null			AS	chu_bunrui_cd		-- 中分類コード
               ,null			AS	shohin_kbn			-- 商品区分
               ,'99999998'		AS	shohin_cd			-- 商品コード
               ,'貸出品回収'	AS	shohin_nm			-- 商品名
               ,null			AS	shohin_tkiyo_nm		-- 商品摘要名
               ,1				AS	hachu_suryo			-- 数量
               ,null			AS	tani_cd				-- 単位コード
               ,0				AS	hanbai_tnk			-- 販売単価
               ,0				AS	hachu_tnk			-- 発注単価
               ,0				AS	hachu_prc			-- 発注金額
               ,0				AS	nebiki_prc			-- 値引額
               ,null			AS	nonyu_cd			-- 納入先コード
               ,null			AS	nonyu_nm			-- 納品場所（納入先名）
               ,null			AS	nonyu_ymd			-- 納品（納入）日時
               ,null			AS	nonyu_ymd_sub		-- 納品（納入）日時
               ,null			AS	jc_denpyo_no		-- 受注伝票NO
               ,1				AS	jc_msi_no			-- 受注明細№
               ,null			AS	hc_denpyo_no		-- 発注伝票NO
               ,CASE WHEN EXISTS( SELECT * FROM nyuko_shuko_denpyo nsd
                   LEFT JOIN nyuko_shuko_msi nsdm
                           ON 	nsd.denpyo_no = nsdm.denpyo_no
                           AND 0			  = nsdm.delete_flg
                   WHERE sk.seko_no	=	nsd.seko_no
                   AND nsd.data_kbn IN (1,2,6)	-- 2015/12/20 UPD Kayo
                   AND nsd.delete_flg=	0
                   AND nsd.nyuko_shuko_kbn = 3
                   AND COALESCE(nsdm.suryo,0)	<>	0) THEN -- 出荷対象あり
               CASE WHEN NOT EXISTS( SELECT * FROM nyuko_shuko_denpyo nsd
                   LEFT JOIN nyuko_shuko_msi nsdm
                           ON 	nsd.denpyo_no = nsdm.denpyo_no
                           AND 0			  = nsdm.delete_flg
                   WHERE sk.seko_no		=	nsd.seko_no
                   AND nsd.data_kbn IN (1,2,6)		-- 2015/12/20 UPD Kayo
                   AND nsd.delete_flg		= 0
                   AND nsd.nyuko_shuko_kbn = 3
                   AND COALESCE(nsdm.nyuko_suryo,0) <>	COALESCE(nsdm.suryo,0) ) THEN 1 -- 入荷済み
               WHEN EXISTS( SELECT * FROM nyuko_shuko_denpyo nsd
                   LEFT JOIN nyuko_shuko_msi nsdm
                           ON 	nsd.denpyo_no = nsdm.denpyo_no
                           AND 0			  = nsdm.delete_flg
                   WHERE sk.seko_no		=	nsd.seko_no
                   AND nsd.data_kbn IN (1,2,6)	-- 2015/12/20 UPD Kayo
                   AND nsd.delete_flg		=	0
                   AND nsd.nyuko_shuko_kbn =	3
                   AND COALESCE(nsdm.nyuko_suryo,0)	=	COALESCE(nsdm.suryo,0) ) THEN 2 -- 一部出荷済み
               ELSE
                   0 -- 未出庫
               END
               ELSE 9           -- 対象外
               END			AS	order_flg			-- 発注済み
               ,null			AS	order_ymd			-- 発注日時
               ,null			AS	order_tanto_cd		-- 発注担当者コード
               ,9				AS	chk_order_flg		-- 発注確認済み
               ,null			AS	chk_order_ymd		-- 発注確認日時
               ,null			AS	chk_order_tanto_cd	-- 発注確認担当者コード
               ,0				AS	hacyu_cnt			-- 発注回数
               ,0				AS	status_kbn			-- 発注ステータス
               ,null			AS	hachu_no_moto		-- コピー元発注管理番号
               ,null			AS	hd_biko1			-- 備考１
               ,null			AS	hd_biko2			-- 備考２
               ,null			AS	v_free1				-- 文字フリー項目１
               ,null			AS	v_free2				-- 文字フリー項目２
               ,null			AS	v_free3				-- 文字フリー項目３
               ,null			AS	v_free4				-- 文字フリー項目４
               ,null			AS	v_free5				-- 文字フリー項目５
               ,null			AS	n_free1				-- 数字フリー項目１
               ,null			AS	n_free2				-- 数字フリー項目２
               ,null			AS	n_free3				-- 数字フリー項目３
               ,null			AS	n_free4				-- 数字フリー項目４
               ,null			AS	n_free5				-- 数字フリー項目５
               ,null			AS	d_free1				-- 日付フリー項目１
               ,null			AS	d_free2				-- 日付フリー項目２
               ,null			AS	d_free3				-- 日付フリー項目３
               ,0				AS	delete_flg			-- 削除フラグ
               ,null			AS	req_id				-- 処理要求ID
               ,null			AS	_cre_user			-- 作成者
               ,null			AS	_cre_ts				-- 作成日時
               ,null			AS	_mod_user			-- 最終更新者
               ,null			AS	_mod_ts				-- 最終更新日時
               ,0				AS	_mod_cnt			-- 更新回数
               ,'倉庫'			AS	siire_lnm			-- 仕入先名
               ,'貸出品回収'	AS	report_lnm			-- 帳票名
               ,'貸出品'		AS	shohin_kbn_nm		-- 商品区分名
               ,sk.sougi_ymd	AS	sougi_ymd			-- 葬儀日
               ,sk.souke_nm	AS	souke_nm			-- 葬家
               ,sk.k_nm		AS	k_nm				-- 故人名
               ,null			AS	moushi_kbn			-- 申込区分
               ,null			AS status_kbn
            FROM  seko_kihon_info sk
            LEFT JOIN nyuko_shuko_denpyo nsd
            ON nsd.seko_no =  sk.seko_no
            AND nsd.data_kbn IN (1,2,6)		-- 2016/12/20 UPD Kayo
            AND nsd.nyuko_shuko_kbn =	3
            AND nsd.delete_flg		=	0
            INNER JOIN kaisya_info kaisya
            ON kaisya.kaisya_cd = :kaisya_cd
            AND 1 = kaisya.kashidashi_kaisyu_kbn
            AND 0 = kaisya.delete_flg
            WHERE sk.seko_no= :seko_no
            AND sk.delete_flg = 0
            AND nsd.seko_no is not null
            AND nsd.shuko_dt is not null -- 2015/03/22 ADD Kayo
END_OF_SQL
		,array(
                    'seko_no'   => $seko_no
                   ,'kaisya_cd' => $kaisya_cd));

		return $dataSekoHaChuCol;
    }
    
    /**
     *
     * 発注情報
     *
     * <AUTHOR> KyouNan
     * @since 2014/2/21
     * @return array 発注情報
     */
    public function getSekoHaChusCol() {

        $dataSekoHaChuCol = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                shi.seko_no  -- 施行番号
                ,shi.hachu_no  -- 発注管理番号
                ,shi.crt_row_no  -- 行番号
                ,shi.siire_cd  -- 仕入先コード
                ,shi.report_cd  -- 帳票コード
                ,shi.ha_rp_code_kbn  -- 発注書区分コード区分
                ,shi.ha_rp_cd  -- 発注書区分コード
                ,shi.ha_rp_kbn  -- 発注書区分
                ,shi.ha_entry_kbn  -- 発注書入力区分
                ,shi.ha_syori_kbn  -- 発注書処理区分
                ,shi.ha_etc_kbn  -- 発注書その他区分
                ,shi.hachu_den_kbn  -- 発注伝票区分
                ,shi.dai_bunrui_cd  -- 大分類コード
                ,shi.chu_bunrui_cd  -- 中分類コード
                ,shi.shohin_kbn  -- 商品区分
                ,shi.shohin_cd  -- 商品コード
                ,shi.shohin_nm  -- 商品名
                ,shi.shohin_tkiyo_nm  -- 商品摘要名
                ,shi.hachu_suryo  -- 数量
                ,shi.tani_cd  -- 単位コード
                ,shi.hanbai_tnk  -- 販売単価
                ,shi.hachu_tnk  -- 発注単価
                ,shi.hachu_prc  -- 発注金額
                ,shi.nebiki_prc  -- 値引額
                ,shi.nonyu_cd  -- 納入先コード
                ,shi.nonyu_nm  -- 納品場所（納入先名）
                ,shi.nonyu_ymd  -- 納品（納入）日時
                ,shi.jc_denpyo_no  -- 受注伝票NO
                ,shi.jc_msi_no  -- 受注明細№
                ,shi.hc_denpyo_no  -- 発注伝票NO
                ,shi.order_flg  -- 発注済み
                ,shi.order_ymd  -- 発注日時
                ,shi.order_tanto_cd  -- 発注担当者コード
                ,shi.chk_order_flg  -- 発注確認済み
                ,shi.chk_order_ymd  -- 発注確認日時
                ,shi.chk_order_tanto_cd  -- 発注確認担当者コード
                ,shi.hacyu_cnt  -- 発注回数
                ,shi.status_kbn  -- 発注ステータス
                ,shi.hachu_no_moto  -- コピー元発注管理番号
                ,shi.hd_biko1  -- 備考１
                ,shi.hd_biko2  -- 備考２
                ,shi.v_free1  -- 文字フリー項目１
                ,shi.v_free2  -- 文字フリー項目２
                ,shi.v_free3  -- 文字フリー項目３
                ,shi.v_free4  -- 文字フリー項目４
                ,shi.v_free5  -- 文字フリー項目５
                ,shi.n_free1  -- 数字フリー項目１
                ,shi.n_free2  -- 数字フリー項目２
                ,shi.n_free3  -- 数字フリー項目３
                ,shi.n_free4  -- 数字フリー項目４
                ,shi.n_free5  -- 数字フリー項目５
                ,shi.d_free1  -- 日付フリー項目１
                ,shi.d_free2  -- 日付フリー項目２
                ,shi.d_free3  -- 日付フリー項目３
                ,shi.delete_flg  -- 削除フラグ
                ,shi._req_id  -- 処理要求ID
                ,shi._cre_user  -- 作成者
                ,shi._cre_ts  -- 作成日時
                ,shi._mod_user  -- 最終更新者
                ,shi._mod_ts  -- 最終更新日時
                ,shi._mod_cnt  -- 更新回数
                ,si.siire_lnm -- 仕入先名
                ,rkm.report_lnm -- 帳票コード
                ,rkm.ha_entry_kbn -- 発注書入力区分
                ,skm.shohin_kbn_nm -- 商品区分名
                ,skn.sougi_ymd -- 葬儀日
                ,skn.souke_nm -- 葬家
                ,skn.k_nm -- 故人名
                ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn='0010' AND kbn_value_cd_num=skn.moushi_kbn) AS moushi_kbn  -- 申込区分code_kbn='0010'skn.moushi_code_kbn
                ,(CASE WHEN shi.status_kbn = 0 THEN 'なし' WHEN shi.status_kbn = 1 THEN '手配中' WHEN shi.status_kbn = 2 THEN 'キャンセル' WHEN shi.status_kbn = 3 THEN '赤伝' ELSE 'なし' END) AS status_kbn
            FROM
                seko_hachu_info shi
                LEFT JOIN siire_mst si
                ON  (
                    shi.siire_cd = si.siire_cd
                    AND si.delete_flg = 0
                )
                LEFT JOIN report_kanri_mst rkm
                ON  (
                    shi.report_cd = rkm.report_cd
                    AND rkm.delete_flg = 0
                )
                LEFT JOIN SHOHIN_KBN_MST skm
                ON  (
                    shi.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0
                )
                LEFT JOIN seko_kihon_info skn
                ON  (
                    shi.seko_no = skn.seko_no
                    AND skm.delete_flg = 0
                )
            WHERE
                shi.delete_flg = 0
            ORDER BY
                skn.sougi_ymd DESC
                ,shi.ha_rp_cd ASC
                ,shi.hachu_no ASC
                ,shi.seko_no ASC
                ";
        $dataSekoHaChuCol = $db->easySelect($sql);
//        var_dump($dataSekoHaChuCol);die();
        return $dataSekoHaChuCol;
    }
    
    /**
     *
     * 発注情報
     *
     * <AUTHOR> KyouNan
     * @since 2014/2/21
     * @return array 発注情報
     */
    public function getSearHaChuCol($seko_no,$cond) {
        
        if(count($cond)>0){
            foreach ($cond as $key => $value) {
                $where.= " AND shi.".$key."= '$value'";
            }
        }  else {
            $where="";
        }

        $dataSekoHaChuCol = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                shi.seko_no  -- 施行番号
                ,shi.hachu_no  -- 発注管理番号
                ,shi.crt_row_no  -- 行番号
                ,shi.siire_cd  -- 仕入先コード
                ,shi.report_cd  -- 帳票コード
                ,shi.ha_rp_code_kbn  -- 発注書区分コード区分
                ,shi.ha_rp_cd  -- 発注書区分コード
                ,shi.ha_rp_kbn  -- 発注書区分
                ,shi.ha_entry_kbn  -- 発注書入力区分
                ,shi.ha_syori_kbn  -- 発注書処理区分
                ,shi.ha_etc_kbn  -- 発注書その他区分
                ,shi.hachu_den_kbn  -- 発注伝票区分
                ,shi.dai_bunrui_cd  -- 大分類コード
                ,shi.chu_bunrui_cd  -- 中分類コード
                ,shi.shohin_kbn  -- 商品区分
                ,shi.shohin_cd  -- 商品コード
                ,shi.shohin_nm  -- 商品名
                ,shi.shohin_tkiyo_nm  -- 商品摘要名
                ,shi.hachu_suryo  -- 数量
                ,shi.tani_cd  -- 単位コード
                ,shi.hanbai_tnk  -- 販売単価
                ,shi.hachu_tnk  -- 発注単価
                ,shi.hachu_prc  -- 発注金額
                ,shi.nebiki_prc  -- 値引額
                ,shi.nonyu_cd  -- 納入先コード
                ,shi.nonyu_nm  -- 納品場所（納入先名）
                ,shi.nonyu_ymd  -- 納品（納入）日時
                ,shi.jc_denpyo_no  -- 受注伝票NO
                ,shi.jc_msi_no  -- 受注明細№
                ,shi.hc_denpyo_no  -- 発注伝票NO
                ,shi.order_flg  -- 発注済み
                ,shi.order_ymd  -- 発注日時
                ,shi.order_tanto_cd  -- 発注担当者コード
                ,shi.chk_order_flg  -- 発注確認済み
                ,shi.chk_order_ymd  -- 発注確認日時
                ,shi.chk_order_tanto_cd  -- 発注確認担当者コード
                ,shi.hacyu_cnt  -- 発注回数
                ,shi.status_kbn  -- 発注ステータス
                ,shi.hachu_no_moto  -- コピー元発注管理番号
                ,shi.hd_biko1  -- 備考１
                ,shi.hd_biko2  -- 備考２
                ,shi.v_free1  -- 文字フリー項目１
                ,shi.v_free2  -- 文字フリー項目２
                ,shi.v_free3  -- 文字フリー項目３
                ,shi.v_free4  -- 文字フリー項目４
                ,shi.v_free5  -- 文字フリー項目５
                ,shi.n_free1  -- 数字フリー項目１
                ,shi.n_free2  -- 数字フリー項目２
                ,shi.n_free3  -- 数字フリー項目３
                ,shi.n_free4  -- 数字フリー項目４
                ,shi.n_free5  -- 数字フリー項目５
                ,shi.d_free1  -- 日付フリー項目１
                ,shi.d_free2  -- 日付フリー項目２
                ,shi.d_free3  -- 日付フリー項目３
                ,shi.delete_flg  -- 削除フラグ
                ,shi._req_id  -- 処理要求ID
                ,shi._cre_user  -- 作成者
                ,shi._cre_ts  -- 作成日時
                ,shi._mod_user  -- 最終更新者
                ,shi._mod_ts  -- 最終更新日時
                ,shi._mod_cnt  -- 更新回数
                ,si.siire_lnm -- 仕入先名
                ,rkm.report_lnm -- 帳票コード
                ,rkm.ha_entry_kbn -- 発注書入力区分
                ,skm.shohin_kbn_nm -- 商品区分名
                ,skn.sougi_ymd -- 葬儀日
                ,skn.souke_nm -- 葬家
                ,skn.k_nm -- 故人名
                ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn='0010' AND kbn_value_cd_num=skn.moushi_kbn) AS moushi_kbn  -- 申込区分code_kbn='0010'skn.moushi_code_kbn
                ,(CASE WHEN shi.status_kbn = 0 THEN 'なし' WHEN shi.status_kbn = 1 THEN '手配中' WHEN shi.status_kbn = 2 THEN 'キャンセル' WHEN shi.status_kbn = 3 THEN '赤伝' ELSE 'なし' END) AS status_kbn
            FROM
                seko_hachu_info shi
                LEFT JOIN siire_mst si
                ON  (
                    shi.siire_cd = si.siire_cd
                    AND si.delete_flg = 0
                )
                LEFT JOIN report_kanri_mst rkm
                ON  (
                    shi.report_cd = rkm.report_cd
                    AND rkm.delete_flg = 0
                )
                LEFT JOIN SHOHIN_KBN_MST skm
                ON  (
                    shi.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0
                )
                LEFT JOIN seko_kihon_info skn
                ON  (
                    shi.seko_no = skn.seko_no
                    AND skm.delete_flg = 0
                )
            WHERE
                shi.seko_no='{$seko_no}'
                AND shi.delete_flg = 0 $where
            ORDER BY 
                skn.sougi_ymd DESC
                ,shi.ha_rp_cd ASC
                ";
        $dataSekoHaChuCol = $db->easySelect($sql);
//        var_dump($dataSekoHaChuCol);die();
        return $dataSekoHaChuCol;
    }

    /**
     *
     * 施行発注情報、施行湯灌情報の取得
     *
     * <AUTHOR> Ogino
     * @since 2014/2/21
     * @return array 発注情報
     */
    public function dataYukan($seko_no) {
        
       $datakbn = App_Utils::getSessionData('data_kbn');
       
       if(!isset($datakbn) || empty($datakbn)) {
           $datakbn  = 0;
       }       
       
       //if ($datakbn == 0){           
       //    $sqlsb2 = " AND (shi.data_kbn <> 4) ";   //未設定の場合は別注品以外が対象になる
       //} 
       //else {           
       //    $sqlsb2 = " AND (shi.data_kbn = ".$datakbn.") ";  
       //}
       $sqlsb2 = "";
       if ($datakbn == 4){           
           $sqlsb2 = " AND (shi.data_kbn = 4) ";   //別注品を指定した場合は別注品のみが対象になる
       }  else{
           $sqlsb2 = " AND (shi.data_kbn <> 4) ";
       }
       
        $dataYukan = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                 syi.seko_no										-- 施行番号
                ,syi.yukan_tanto_cd									-- 湯灌担当者コード
                ,syi.yukan_code_kbn									-- 湯灌コード 区分
--                ,coalesce(syi.yukan_cd,case when coalesce(shi.shohin_cd,'') = '0002670' then '2' else '1' end) as yukan_cd	-- 湯灌コード
                ,case when sm.hoshi_umu_kbn = 9 then '2' else '1' end as yukan_cd	-- 湯灌コード
                ,syi.yukan_kbn										-- 湯灌区分
                ,coalesce(syi.yu_te_code_kbn,'0350') AS yu_te_code_kbn		-- 湯灌手当コード 区分
                ,coalesce(syi.yu_te_cd,'01') AS yu_te_cd			-- 湯灌手当コード
                ,coalesce(syi.yu_te_kbn,8) AS yu_te_kbn				-- 湯灌手当区分
                ,TO_CHAR(syi.yukan_ymd ,'YYYY/MM/DD HH24:MI') AS yukan_ymd	-- 湯灌日時
                ,TO_CHAR(syi.yukan_ymd ,'YYYY/MM/DD') AS yukan_date	-- 湯灌日付のみ
                ,TO_CHAR(syi.yukan_ymd ,'HH24:MI') AS yukan_time	-- 湯灌時刻のみ
                ,syi.yu_basyo_code_kbn								-- 湯灌場所コード 区分
                ,syi.yu_basyo_cd									-- 湯灌場所コード
                ,syi.yu_basyo_kbn									-- 湯灌場所区分
                ,syi.yu_spot_cd										-- お伺い先住所コード
                ,syi.yu_spot_nm										-- お伺い先名
                ,syi.yukan_yubin_no									-- 湯灌住所郵便番号
                ,syi.yukan_addr1									-- 湯灌住所1
                ,syi.yukan_addr2									-- 湯灌住所2
                ,ski.syushi_code_kbn								-- 宗旨コード 区分
                ,ski.syushi_cd										-- 宗旨コード
                ,ski.syushi_kbn										-- 宗旨区分
                ,ski.syuha_code_kbn									-- 宗派コード 区分
                ,ski.syuha_cd										-- 宗派コード
                ,ski.syuha_kbn										-- 宗派区分
                ,ski.syuha_nm										-- 宗派名
                ,syi.bleed_kbn										-- 出血区分
                ,syi.mouth_kbn										-- 口区分
                ,syi.nose_kbn										-- 鼻区分
                ,syi.ear_kbn										-- 耳区分
                ,syi.etc_body_kbn									-- その他区分
                ,syi.etc_body_nm									-- その他出血箇所
                ,syi.death_spot										-- 死亡場所
                ,syi.death_cause									-- 死因
                ,syi.br_butsui_kbn									-- 持ち物仏衣区分
                ,syi.br_butsui_nm									-- 持ち物仏衣名
                ,syi.br_jyoui_kbn									-- 持ち物浄衣区分
                ,syi.br_waraji_kbn									-- 持ち物わらじ区分
                ,syi.br_amigasa_kbn									-- 持ち物編み笠区分
                ,syi.br_etc_kbn										-- その他持ち物区分
                ,syi.br_etc_nm										-- その他持ち物
                ,syi.biko1											-- 特記事項１
                ,syi.biko2											-- 特記事項２
                ,ski.kg_yubin_no									-- 現住所郵便番号
                ,ski.kg_addr1										-- 現住所1
                ,ski.kg_addr2										-- 現住所2
                ,syi.delete_flg										-- 削除フラグ
                ,syi._req_id										-- 処理要求ID
                ,syi._cre_user										-- 作成者
                ,syi._cre_ts										-- 作成日時
                ,syi._mod_user										-- 最終更新者
                ,TO_CHAR(syi._mod_ts,'YYYY/MM/DD') AS _mod_ts		-- 最終更新日時
                ,syi._mod_cnt										-- 更新回数
                ,(SELECT tanto_nm FROM tanto_mst WHERE tanto_cd=ski.seko_tanto_cd) AS seko_tanto_nm	-- 担当者名
                ,ski.seko_tanto_cd									-- 担当者cd
                ,shi.hachu_no										-- 発注管理番号
                ,coalesce(shi.siire_cd,'') as siire_cd										-- 仕入先コード
                ,coalesce((SELECT siire_lnm FROM siire_mst WHERE siire_cd=shi.siire_cd),'') AS hachusk_1			-- 仕入先
                ,shi.chk_order_flg									-- 発注確認済み
                ,shi.chk_order_ymd									-- 発注確認日時
                ,shi.chk_order_tanto_cd								-- 発注確認担当者コード
                ,shi.order_flg										-- 発注済み
                ,sm.shiire_zei_kbn
                ,syi.yukan_tel	
                ,coalesce(syi.item1_use_kbn,0) as item1_use_kbn	
                ,coalesce(syi.item2_use_kbn,0) as item2_use_kbn	
                ,coalesce(syi.item3_use_kbn,0) as item3_use_kbn	
                ,coalesce(syi.item4_use_kbn,0) as item4_use_kbn	
                ,coalesce(syi.item5_use_kbn,0) as item5_use_kbn	
                ,coalesce(syi.item6_use_kbn,0) as item6_use_kbn	
                ,coalesce(syi.item7_use_kbn,0) as item7_use_kbn	
                ,coalesce(syi.item8_use_kbn,0) as item8_use_kbn
				,coalesce(syi.k_free1,0) as k_free1
                ,shi.shohin_kbn
            FROM
                seko_hachu_info shi
                LEFT JOIN seko_yukan_info syi
                ON (
                    shi.seko_no = syi.seko_no
                    AND syi.delete_flg = 0
                )
                LEFT JOIN seko_kihon_info ski
                ON (
                    syi.seko_no = ski.seko_no
                    AND ski.delete_flg = 0
                )
                LEFT OUTER JOIN shohin_mst sm ON(shi.shohin_cd = sm.shohin_cd) 
            WHERE
                shi.seko_no='{$seko_no}'
				AND shi.status_kbn in ( 0,1)
                AND shi.delete_flg = 0".
                $sqlsb2.        
                "AND shi.ha_rp_kbn = '15'
                ";
        $dataYukan = $db->easySelOne($sql);
        return $dataYukan;
    }


    /**
     *
     * 施行発注情報、施行花運搬情報の取得
     *
     * <AUTHOR> Ogino
     * @since 2014/2/21
     * @return array 発注情報
     */
	public function dataHanaunpan($seko_no) {
                if(!isset($datakbn) || empty($datakbn)) {
                    $datakbn  = 0;
                }       

                if ($datakbn == 0){           
                    $sqlsb2 = " AND (shi.data_kbn <> 4) ";   //未設定の場合は別注品以外が対象になる
                } 
                else {           
                    $sqlsb2 = " AND (shi.data_kbn = ".$datakbn.") ";  
                }
                
		$dataHanaunpan = array();
		$db = Msi_Sys_DbManager::getMyDb();
		$sql = "
			SELECT
				 shai.seko_no						-- 施行番号
				,shai.start_1seika_cnt				-- 運搬予定行き１段生花
				,shai.start_2seika_cnt				-- 運搬予定行き2段生花
				,shai.start_kago_cnt				-- 運搬予定行き盛籠
				,shai.start_butsu_cnt				-- 運搬予定行き仏具
				,shai.return_1seika_cnt				-- 運搬予定帰り１段生花
				,shai.return_2seika_cnt				-- 運搬予定帰り2段生花
				,shai.return_kago_cnt				-- 運搬予定帰り盛籠
				,shai.return_butsu_cnt				-- 運搬予定帰り仏具
				,CASE WHEN shai.syukan_dt IS NOT NULL THEN
						to_char(shai.syukan_dt, 'yyyy/mm/dd hh24:mi:ss')
					ELSE
						to_char(snsk.nitei_ymd, 'yyyy/mm/dd hh24:mi:ss')	
					END												AS	syukan_dt		-- 出棺時間
				,CASE WHEN shai.syukan_dt IS NOT NULL THEN
						to_char(shai.syukan_dt, 'yyyy/mm/dd')
					ELSE
						to_char(snsk.nitei_ymd, 'yyyy/mm/dd')	
					END												AS	syukan_dt_ymd	-- 出棺時間
				,CASE WHEN shai.syukan_dt IS NOT NULL THEN
						to_char(shai.syukan_dt, 'hh24:mi')
					ELSE
						to_char(snsk.nitei_ymd, 'hh24:mi')	
					END												AS	syukan_dt_time	-- 出棺時間
				,CASE WHEN shai.sougi_dt IS NOT NULL THEN
						to_char(shai.sougi_dt, 'yyyy/mm/dd hh24:mi:ss')	
					ELSE
						to_char(sns.nitei_ymd, 'yyyy/mm/dd hh24:mi:ss')	
					END												AS	sougi_dt		-- 葬儀時間
				,CASE WHEN shai.sougi_dt IS NOT NULL THEN	
						to_char(shai.sougi_dt, 'yyyy/mm/dd')
					ELSE
						to_char(sns.nitei_ymd, 'yyyy/mm/dd')	
					END												AS	sougi_dt_ymd	-- 葬儀日
				,CASE WHEN shai.sougi_dt IS NOT NULL THEN
						to_char(shai.sougi_dt, 'hh24:mi')
					ELSE
						to_char(sns.nitei_ymd, 'hh24:mi')	
					END												AS	sougi_dt_time	-- 葬儀時間
				,shai.start_spot_nm					-- 出発場所
				,shai.return_spot_nm				-- 返却場所
				,shai.nouhin_spot_nm				-- 納品場所
				,to_char(shai.start_from_dt, 'yyyy/mm/dd hh24:mi:ss') start_from_dt			-- 行き日時から
                                ,to_char(shai.start_from_dt, 'yyyy/mm/dd') start_from_dt_ymd			-- 行き日時から
                                ,to_char(shai.start_from_dt, 'hh24:mi') start_from_dt_time			-- 行き日時から
				,to_char(shai.start_to_dt, 'yyyy/mm/dd hh24:mi:ss') start_to_dt				-- 行き日時まで
                                ,to_char(shai.start_to_dt, 'yyyy/mm/dd') start_to_dt_ymd				-- 行き日時まで
                                ,to_char(shai.start_to_dt, 'hh24:mi') start_to_dt_time				-- 行き日時まで
				,coalesce(shai.start_ban_kbn,1) start_ban_kbn		-- 行き留守番
				,to_char(shai.return_from_dt, 'yyyy/mm/dd hh24:mi:ss') return_from_dt		-- 帰り日時から
                                ,to_char(shai.return_from_dt, 'yyyy/mm/dd') return_from_dt_ymd		-- 帰り日時から
                                ,to_char(shai.return_from_dt, 'hh24:mi') return_from_dt_time		-- 帰り日時から
				,to_char(shai.return_to_dt, 'yyyy/mm/dd hh24:mi:ss') return_to_dt			-- 帰り日時まで
                                ,to_char(shai.return_to_dt, 'yyyy/mm/dd') return_to_dt_ymd			-- 帰り日時まで
                                ,to_char(shai.return_to_dt, 'hh24:mi') return_to_dt_time			-- 帰り日時まで
				,coalesce(shai.return_ban_kbn,1) return_ban_kbn		-- 帰り留守番
				,shai.biko1							-- 備考1
				,shai.biko2							-- 備考2
				,shai.delete_flg					-- 削除フラグ
				,shi.hachu_no						-- 発注管理番号
				,shi.siire_cd						-- 仕入先コード
				,shi.report_cd						-- 帳票コード
				,smst.siire_lnm AS hachusk_1		-- 仕入先
                ,shi.chk_order_flg					-- 発注確認済み
                ,shi.chk_order_ymd					-- 発注確認日時
                ,shi.chk_order_tanto_cd				-- 発注確認担当者コード
				,shi.order_flg						-- 発注済み
                ,shi.hc_denpyo_no					-- 発注伝票
			FROM
				seko_hachu_info shi
				LEFT JOIN seko_hanaunpan_info shai
				ON (
					shi.seko_no = shai.seko_no
					AND shai.delete_flg = 0
				)
				LEFT JOIN seko_nitei sns	-- 葬儀日程
					ON	shi.seko_no = sns.seko_no
					AND 7			= sns.nitei_kbn
					AND 0			= sns.delete_flg
				LEFT JOIN seko_nitei snsk	-- 出棺日程
					ON	shi.seko_no = snsk.seko_no
					AND 5			= snsk.nitei_kbn
					AND 0			= snsk.delete_flg
				LEFT JOIN siire_mst smst
				ON (
					smst.siire_cd=shi.siire_cd
					AND smst.delete_flg = 0
				)
			WHERE
					shi.seko_no='{$seko_no}'
				AND shi.status_kbn in ( 0,1)
				AND shi.delete_flg = 0
				AND shi.shohin_kbn in ('0660','0900')".
                                $sqlsb2.                
			      "AND shi.shohin_cd = '0001130'
		";
		$dataHanaunpan = $db->easySelOne($sql);
		return $dataHanaunpan;
	}

}
