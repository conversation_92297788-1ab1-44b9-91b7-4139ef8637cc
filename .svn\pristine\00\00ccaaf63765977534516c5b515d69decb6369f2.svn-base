<?php
  /**
   * Mref_Jsonlist_Kashishohin2
   *
   * 商品（貸出品） データ取得
   *   cf. JsonlistAbstController.php   Mref_JsonlistAbstController::get_kashishohin()
   *
   * @category   App
   * @package    controllers\Mref
   * <AUTHOR> Mihara
   * @since      2020/12/xx
   * @filesource 
   */

  /**
   * 商品（貸出品） データ取得
   *
   * @category   App
   * @package    controllers\Mref
   * <AUTHOR> Mihara
   * @since      2020/12/xx
   */
class Mref_Jsonlist_Kashishohin2 extends Mref_Jsonlist_ExAbst
{
    /**
     * データ取得
     *   cf. Mref_JsonlistAbstController::get_kashishohin() in JsonlistAbstController.php
     *
     * <AUTHOR> Mihara
     * @since 2020/12/xx
     */
	public function getData()
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $params  = Msi_Sys_Utils::webInputs();
		$kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2017/07/13 ADD Kayo
        $data = $db->easySelect(<<< END_OF_SQL
SELECT DISTINCT
	 shohin_cd  AS	id		-- 商品コード
	,shohin_nm  AS	text	-- 商品名
FROM    shohin_mst
WHERE kashidashi_kbn IN (1,2)
AND kaisya_cd  = :kaisya_cd                
AND delete_flg = 0
AND hanbai_st_ymd <= CURRENT_DATE AND hanbai_end_ymd >= CURRENT_DATE
ORDER BY shohin_cd
END_OF_SQL
        ,array('kaisya_cd' => $kaisyacd));
        return $data;
    }

}
