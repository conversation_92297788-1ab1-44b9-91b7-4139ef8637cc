<?php
  /**
   * DataMapper_MitsuCalc
   *
   * 見積書金額計算 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/16
   * @filesource 
   */

  /**
   * 見積書金額計算 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/05/16
   */
class DataMapper_MitsuCalc extends DataMapper_Abstract
{
    /**
     * 消費税額を取得
     *
     * <AUTHOR> Sato
     * @since      2014/05/16
     * @param      Msi_Sys_Db $db
     * @param array $where_ary
     * @return     array      消費税額
     */
    public static function getZeiPrc($db, $where_ary)
    {
		$param = array();
		$where = NULL;
		foreach ($where_ary as $key => $value) {
			$where .= $key.' = :'.$key.' AND ';
			$param[$key] = $value;
		}
        $select = $db->easySelOne( <<< END_OF_SQL
SELECT out_zei_prc, in_zei_prc FROM juchu_denpyo
  WHERE
	$where delete_flg = 0
END_OF_SQL
        , $param);
        return $select;
    }
    /**
     * 消費税額を取得
     *
     * <AUTHOR> Sai
     * @since      2016/02/10
     * @param      Msi_Sys_Db $db
     * @param array $where_ary
     * @return     array      消費税額
     */
    public static function getZeiPrcFromHistory($db, $where_ary)
    {
		$param = array();
		$where = NULL;
		foreach ($where_ary as $key => $value) {
			$where .= $key.' = :'.$key.' AND ';
			$param[$key] = $value;
		}
        $select = $db->easySelOne( <<< END_OF_SQL
SELECT out_zei_prc, in_zei_prc FROM juchu_denpyo_history
  WHERE
	$where delete_flg = 0
END_OF_SQL
        , $param);
        return $select;
    }

}
