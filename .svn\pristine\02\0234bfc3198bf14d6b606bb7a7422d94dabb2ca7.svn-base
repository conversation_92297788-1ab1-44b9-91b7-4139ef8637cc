<?php
  /**
   * PDF 御葬儀打合せ事項
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Kobayashi
   * @since      2016/10/27
   * @filesource 
   */

  /**
   * PDF 御葬儀打合せ事項
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Kobayashi
   * @since      2016/10/27
   * @version	   2016/12/08 Kobayashi 「続柄」手打ち入力対応
   */
class Juchu_Pdf0101Controller extends Zend_Controller_Action
{
    private static $title = '御葬儀打合せ事項';
    private static $sourceFileName = 'pdf_tmpl/0101.pdf';

    /**
     * アクション
     *
     * <AUTHOR> Kobayashi
     * @since 2016/10/27
     */
    public function indexAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        if (isset($params['preview'])) {
            $preview = htmlspecialchars($params['preview']);          // プレビュー有無
        } else {
            $preview = 'off';
        }
        $seko_no = htmlspecialchars($params['seko_no']);          // 施行番号

        $pdfObj = new App_Pdf( self::$title );
        $db = Msi_Sys_DbManager::getMyDb();

        self::outData($pdfObj, $db, $seko_no);

        if ($preview == 'on') {
			$buf = $pdfObj->fileOutBuf();
			$file_name = $pdfObj->getFileName();
			$key = Msi_Sys_Utils::prepareMyTempFileEasy( $buf, $file_name );
            $this->view->file = $file_name;
            $this->view->key = $key;
            $this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
            $this->_helper->viewRenderer->setScriptAction( 'pdf' );
        } else {
            $pdfObj->download();
        }
    }

    static public function outData($pdfObj, $db, $seko_no)
    {
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName);
        $pdfObj->SetFont('kozminproregular','');

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        // 会社ロゴ
//        $pdfObj->kaisyalogo_out($db, $seko_no, 375, 70);
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();
        $recCmn = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no, "kaisya_cd" => $kaisya_cd));
        $seko_no = $recCmn[0]['seko_no'];
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();
        $recLogo = self::logo($db, $kaisya_cd, $seko_no);
        if (count($recCmn) > 0) {
            if (isset($recLogo[0]['syamei_logo_img'])) {
                $logo = $db->readBlobCont($recLogo[0]['syamei_logo_img']);
            }
            $kaisyalogo = array(
                'logo' => $logo,
                'nm' => '',
                'bumon_nm' => $recCmn[0]['bumon_lnm'],
                'bumon_tel' => $recCmn[0]['tel']
            );
            switch($kaisya_cd){   //ロゴの位置調整
                case '06644460':  //セレモ
                    $pdfObj->kaisyalogo_ary_out(370, 65, $kaisyalogo, 115, 120, null, null, 24);    // (x, y, ロゴ, 文字幅, ロゴ幅, , , ロゴ高さ)
                    break;
                case '06644461':  //ハートフル
                    $pdfObj->kaisyalogo_ary_out(370, 62, $kaisyalogo, 115, 120, null, null, 28);    // (x, y, ロゴ, 文字幅, ロゴ幅, , , ロゴ高さ)    
            }
        }

        $rec = DataMapper_Pdf0101::find7( $db, array( "seko_no" => $seko_no ) );
        if (count($rec) == 0) {
            return;
        }
        $rec = $rec[0];

        //直送
        $tyoku = '';
        if(isset($rec['daicho_no_seq']) && $rec['daicho_no_seq'] === '1'){
            $tyoku = '直';
        }
        
        // 受付
        $recTantoU = DataMapper_TantoMst::find( $db, array( "tanto_cd" => $rec['uketuke_tanto_cd'] ) );
        if (count($recTantoU) > 0 && isset($recTantoU[0]['inkan_img'])) {
            $img = $db->readBlobCont($recTantoU[0]['inkan_img']);
            $pdfObj->seal_out(500, 70, $img);
        }

        // 担当
        $recTantoS = DataMapper_TantoMst::find( $db, array( "tanto_cd" => $rec['seko_tanto_cd'] ) );
        if (count($recTantoS) > 0 && isset($recTantoS[0]['inkan_img'])) {
            $img = $db->readBlobCont($recTantoS[0]['inkan_img']);
            $pdfObj->seal_out(540, 70, $img);
        }
        // 故人生年月日
        $k_gengo = null;
        $k_seinengappi = null;
        $k_dayformat = null;              
        if($rec['k_gengo'] === 'Y'){    //西暦
            $k_seinengappi = $rec['k_seinengappi_ymd_y'];
            $k_dayformat = "Y年 n月 j日";                       
        }else{
            $k_gengo = $rec['k_gengo'];
            $k_seinengappi = $rec['k_seinengappi_ymd'];
            $k_dayformat = "y年 n月 j日";
        }
        if (isset($k_seinengappi) && strlen(trim($k_seinengappi, " ")) > 0) {
            $pdfObj->write_date(array('x' =>410, 'y' =>329, 'width' =>140, 'height' =>15), $k_seinengappi, $k_dayformat, 'gG', $k_gengo);
        }
        if (strlen($rec['pacemaker_kbn']) > 0) {
            $pdfObj->write_string(array('x' =>410, 'y' =>343, 'width' =>140, 'height' =>15), 'ペースメーカー'.' '.$rec['pacemaker_kbn']);
        }
        // 喪主生年月日
        $m_gengo = null;
        $m_seinengappi = null;
        $m_dayformat = null;              
        if($rec['m_gengo'] === 'Y'){    //西暦
            $m_seinengappi = $rec['m_seinengappi_ymd_y'];
            $m_dayformat = "Y年 n月 j日";                       
        }else{
            $m_gengo = $rec['m_gengo'];
            $m_seinengappi = $rec['m_seinengappi_ymd'];
            $m_dayformat = "y年 n月 j日";
        } 
        if (isset($m_seinengappi) && strlen(trim($m_seinengappi, " ")) > 0) {
            $pdfObj->write_date(array('x' =>410, 'y' =>403, 'width' =>140, 'height' =>15), $m_seinengappi, $m_dayformat, 'gG', $m_gengo);
        }
        
        // コピー
        $copy_disp = '';
        if (empty($rec['sd_copy_cnt'])) {
        } else if ($rec['sd_copy_cnt'] == 0) {
            $copy_disp = '未';
        } else if ($rec['sd_copy_cnt'] == 1) {
            $copy_disp = '済';
        }
        
        // 連絡者様
        $renraku = '';
        if(!empty($rec['contact_nm1'])){
            $renraku = '連絡者様：'.$rec['contact_nm1'].' 様';
        }
        if(!empty($rec['noti_name'])){
            $renraku = $renraku.'（'.$rec['noti_name'].'）';
        }
        if(!empty($rec['contact_tel1'])){
            $renraku = $renraku.' 　TEL：'.$rec['contact_tel1'];
        }
        if ($rec['wait_flg'] == 1)  {
            $pdfObj->write_string(array('x' =>150, 'y' =>50, 'width' =>300, 'height' =>30, 'align' => 'C', 'font_size' => 30, 'color'=>'red'), '（連絡待ち）');
        }
        $pdfObj->write_multi(
            array(
                array('type' => 'string', 'x' => 27, 'y' => 93, 'width' =>  90, 'height' =>  15, 'value' => '施行No：'.$tyoku.$rec['input_no']),
                array('type' => 'string', 'x' => 90, 'y' => 115, 'width' =>  90, 'height' =>  15, 'value' => $rec['daicho_no_eria'], 'align' => 'C'),
//                array('type' => 'string', 'x' => 90, 'y' => 115, 'width' =>  90, 'height' =>  15, 'value' => $rec['seko_no'], 'align' => 'C'),
                array('type' => 'string', 'x' =>200, 'y' => 115, 'width' => 145, 'height' =>  15, 'value' => $rec['kaiin_nm'].$rec['kaiin_sonota'], 'align' => 'C'),
                array('type' => 'string', 'x' => 83, 'y' => 141, 'width' =>  85, 'height' =>  15, 'value' => $rec['souke_nm'], 'align' => 'C'),
                array('type' => 'string', 'x' =>255, 'y' => 141, 'width' =>  90, 'height' =>  15, 'value' => $rec['mg_tel'], 'align' => 'C'),
                array('type' => 'string', 'x' => 90, 'y' => 167, 'width' =>  90, 'height' =>  15, 'value' => $rec['keishiki_nm'], 'align' => 'C'),
                array('type' => 'string', 'x' =>255, 'y' => 167, 'width' =>  90, 'height' =>  15, 'value' => $rec['syushi_nm'], 'align' => 'C'),
                array('type' => 'string', 'x' => 90, 'y' => 192, 'width' =>  90, 'height' =>  15, 'value' => $rec['jiin_nm'], 'align' => 'C'),
                array('type' => 'string', 'x' =>255, 'y' => 192, 'width' =>  90, 'height' =>  15, 'value' => $rec['syuha_nm'], 'align' => 'C'),
                array('type' =>'strings', 'x' => 31, 'y' => 225, 'width' => 320, 'height' => 70, 'value' => $rec['biko1'], 'align' => 'L'),
                array('type' => 'string', 'x' =>31, 'y' => 288, 'width' =>  320, 'height' =>  15, 'value' => '発生場所：'.$rec['shibo_basho_nm'].'　'.$rec['free_v_free3']),
                array('type' => 'string', 'x' =>205, 'y' => 300, 'width' =>  180, 'height' =>  15, 'value' => '安置先：'.$rec['hs_anchi_nm']),
                // 故人
                array('type' => 'string', 'x' =>130, 'y' => 328, 'width' => 205, 'height' =>  15, 'value' => $rec['k_knm']),
                array('type' => 'string', 'x' =>130, 'y' => 348, 'width' => 205, 'height' =>  15, 'value' => $rec['k_nm']),
                array('type' => 'string', 'x' =>356, 'y' => 348, 'width' =>  40, 'height' =>  15, 'value' => $rec['m_zoku_nm2_t'], 'align' => 'C'),
                array('type' => 'string', 'x' =>423, 'y' => 356, 'width' =>  25, 'height' =>  15, 'value' => $rec['k_nenrei_man'], 'align' => 'C'),
                // 故人　現住所
                array('type' => 'string', 'x' =>150, 'y' => 371, 'width' =>  240, 'height' =>  15, 'value' => $rec['kg_yubin_no'].'　'.$rec['kg_addr1']),
                array('type' => 'string', 'x' =>150, 'y' => 385.5, 'width' =>  240, 'height' =>  15, 'value' => $rec['kg_addr2']),
                array('type' => 'string', 'x' =>450, 'y' => 371, 'width' =>  120, 'height' =>  15, 'value' => $rec['kg_tel']),
                array('type' => 'string', 'x' =>450, 'y' => 385.5, 'width' =>  120, 'height' =>  15, 'value' => $rec['renraku_tel_ceremo']),
                // 喪主
                array('type' => 'string', 'x' =>130, 'y' =>402, 'width' =>  205, 'height' =>  15, 'value' => $rec['m_knm']),
                array('type' => 'string', 'x' =>130, 'y' => 422, 'width' =>  205, 'height' =>  15, 'value' => $rec['m_nm']),
                array('type' => 'string', 'x' =>356, 'y' => 422, 'width' =>  40, 'height' =>  15, 'value' => $rec['m_zoku_nm_t'], 'align' => 'C'),
                array('type' => 'string', 'x' =>423, 'y' => 429, 'width' =>  25, 'height' =>  15, 'value' => $rec['m_nenrei_man'], 'align' => 'C'),
                array('type' => 'string', 'x' =>450, 'y' =>459, 'width' =>120, 'height' => 15, 'value' => $rec['mg_m_tel']),
                // 紹介                
                array('type' => 'string', 'x' =>130, 'y' =>553, 'width' => 30, 'height' => 15, 'value' => $rec['syoukai_kbn']),
                array('type' => 'string', 'x' =>235, 'y' =>553, 'width' => 150, 'height' => 15, 'value' => $rec['syokai_riyu']),
                // 俗名
//                array('type' => 'string', 'x' =>130, 'y' =>569, 'width' => 400, 'height' => 15, 'value' => $rec['k_kaimyo']),
                array('type' => 'string', 'x' =>130, 'y' =>569, 'width' => 150, 'height' => 15, 'value' => $rec['zoku_kai']),
                array('type' => 'string', 'x' =>180, 'y' =>569, 'width' => 150, 'height' => 15, 'value' => $rec['kai_name1'].'　'.$rec['kai_name2']),
                // 注意事項
                array('type' =>'strings', 'x' =>121, 'y' => 603, 'width' => 460, 'height' => 30, 'value' => $rec['careful_memo'], 'align' => 'L'),
                // 連絡者様
                array('type' => 'string', 'x' =>130, 'y' =>633, 'width' => 460, 'height' => 15, 'value' => $renraku),
            )
        );

        // 喪主　現住所　故人に同じ
        if ($rec['mg_kbn'] == 1) {
            $pdfObj->write_string(array('x' =>255, 'y' => 141, 'width' =>  90, 'height' =>  15, 'align' => 'C'), $rec['kg_tel']);
            $pdfObj->write_string(array('x' =>130, 'y' =>458.5, 'width' => 60, 'height' => 15), '故人に同じ');
        }else{
            // 喪主　現住所
            $pdfObj->write_string(array('x' =>150, 'y' =>444, 'width' => 240, 'height' => 15), $rec['mg_yubin_no'].'　'.$rec['mg_addr1']);
            $pdfObj->write_string(array('x' =>150, 'y' =>459, 'width' => 240, 'height' => 15), $rec['mg_addr2']);
            $pdfObj->write_string(array('x' =>450, 'y' =>444, 'width' => 120, 'height' => 15), $rec['mg_tel']);
        }
        // 請求先　現住所　喪主に同じ
        if ($rec['sekyu_kbn'] == 1) {
            if ($rec['free2_kbn'] == 1) {
                $pdfObj->write_string(array('x' =>130, 'y' =>490.5, 'width' => 205, 'height' => 15), $rec['sekyu_nm']);
                $pdfObj->write_string(array('x' =>130, 'y' =>475, 'width' => 205, 'height' => 15), $rec['sekyu_knm']);
                $pdfObj->write_string(array('x' =>360, 'y' =>489, 'width' => 210, 'height' => 15, 'align' => 'C'), $rec['moshu_kankei_kbn'].$rec['moshu_kankei']);
                $pdfObj->write_string(array('x' =>130, 'y' =>521, 'width' => 60, 'height' => 15), '故人に同じ');
                $pdfObj->write_string(array('x' =>450, 'y' =>506, 'width' => 120, 'height' => 15), $rec['sekyu_tel']);
                $pdfObj->write_string(array('x' =>450, 'y' =>521.5, 'width' => 120, 'height' => 15), $rec['sekyu_mobile_tel']);
                $pdfObj->write_string(array('x' =>130, 'y' =>536.5, 'width' => 440, 'height' => 15), $rec['sekyu_biko1'].'　'.$rec['sekyu_biko2']);
            } else {
                $pdfObj->write_string(array('x' =>130, 'y' =>521, 'width' => 60, 'height' => 15), '喪主に同じ');
            }
        }else{
            // 請求先
            $pdfObj->write_string(array('x' =>130, 'y' =>490.5, 'width' => 205, 'height' => 15), $rec['sekyu_nm']);
            $pdfObj->write_string(array('x' =>130, 'y' =>475, 'width' => 205, 'height' => 15), $rec['sekyu_knm']);
            $pdfObj->write_string(array('x' =>360, 'y' =>489, 'width' => 210, 'height' => 15, 'align' => 'C'), $rec['moshu_kankei_kbn'].$rec['moshu_kankei']);
            // 請求先　現住所
            if ($rec['free2_kbn'] == 1) {
                $pdfObj->write_string(array('x' =>130, 'y' =>521, 'width' => 60, 'height' => 15), '故人に同じ');
            } else {
                $pdfObj->write_string(array('x' =>150, 'y' => 506, 'width' =>  240, 'height' =>  15), $rec['sekyu_yubin_no'].'　'.$rec['sekyu_addr1']);
                $pdfObj->write_string(array('x' =>150, 'y' =>521.5, 'width' => 240, 'height' => 15), $rec['sekyu_addr2']);
            }
            $pdfObj->write_string(array('x' =>450, 'y' =>506, 'width' => 120, 'height' => 15), $rec['sekyu_tel']);
            $pdfObj->write_string(array('x' =>450, 'y' =>521.5, 'width' => 120, 'height' => 15), $rec['sekyu_mobile_tel']);
            $pdfObj->write_string(array('x' =>130, 'y' =>536.5, 'width' => 440, 'height' => 15), $rec['sekyu_biko1'].'　'.$rec['sekyu_biko2']);
        }

        // 日程
        $row = 1;
        $y = 115;
        $week = array("日", "月", "火", "水", "木", "金", "土"); 
        $date_format = "y年n月j日";
        $date_format3 = "G時i分";
        $remarks_font_size = 8;
        $sikijo = null;

        $recNitei = DataMapper_Pdf0101Nitei::find( $db, array( "seko_no" => $seko_no ) );
        foreach ($recNitei as $value) {

            if($value['nitei_kbn'] === '11'||$value['nitei_kbn'] === '4'){  //名称変更可能
                $nitei_nm = $value['ts_based_nm'];
            }elseif($value['nitei_kbn'] === '12' ||$value['nitei_kbn'] === '13'||$value['nitei_kbn'] === '14'){ //日程の表に表示しない
                $nitei_nm = null;                
            }else{
                $nitei_nm = $value['kbn_value_snm'];      
            }
            //曜日
            $date = $value['nitei_ymd'];
            $datetime = new DateTime($date);
            $w = (int)$datetime->format('w');
            $date_format2 = "(" . $week[$w] . ")";

            switch ($value['nitei_kbn']) {
                case 1: // 亡日
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
                    break;
                case 2: // 湯灌
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
                    break;
                case 3: // 納棺
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
//                    $pdfObj->write_string(array('x' =>545, 'y' => $y - 3, 'width' =>  30, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), '入棺経');
//                    $pdfObj->write_string(array('x' =>545, 'y' => $y + 7, 'width' =>  30, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), $value['remarks']);
                    break;
                case 4: // 通夜
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
                    break;
                case 5: // 出棺
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
//                    $pdfObj->write_string(array('x' =>545, 'y' => $y - 3, 'width' =>  30, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), '出棺経');
//                    $pdfObj->write_string(array('x' =>545, 'y' => $y + 7, 'width' =>  30, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), $value['remarks']);
                    break;
                case 6: // 火葬
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
                    $pdfObj->write_string(array('x' =>542, 'y' => $y - 3, 'width' =>  33, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), '炉前読経');
                    $pdfObj->write_string(array('x' =>545, 'y' => $y + 7, 'width' =>  30, 'height' =>  10, 'align' => 'C', 'font_size' => $remarks_font_size), $value['remarks']);
                    $pdfObj->write_string(array('x' =>130, 'y' => 585, 'width' =>  440, 'height' => 15,) ,$value['basho_nm']);
                    break;
                case 8: // 初七日
                        $pdfObj->write_date(array('type' => 'time', 'x' => 415, 'y' => $y, 'width' => 63, 'height' => 15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                        $pdfObj->write_date(array('type' => 'time', 'x' => 473, 'y' => $y, 'width' => 20, 'height' => 15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                        if($value['free3_kbn'] != '1'){
                            $pdfObj->write_date(array('type' => 'time', 'x' => 490, 'y' => $y, 'width' => 55, 'height' => 15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                        }
                    break;
                case 11: // 葬儀
                    //$pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  125, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format);
                    $pdfObj->write_date(array('type' => 'time', 'x' =>415, 'y' => $y, 'width' =>  63, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format, 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>473, 'y' => $y, 'width' =>  20, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>490, 'y' => $y, 'width' =>  55, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], $date_format3);
                    }
                    if ($value['basho_kbn'] === '0') {  //式場
                        $sikijo = '自宅';
                    } else {
                        $sikijo = $value['basho_nm'];
                    }
                    break;
                case 13: // お迎え搬送
                    $pdfObj->write_string(array('x' =>31, 'y' => 300, 'width' =>  90, 'height' =>  15), 'お迎え予定日時：');
                    $pdfObj->write_date(array('type' => 'time', 'x' =>111, 'y' => 300, 'width' =>  35, 'height' =>  15, 'align' => 'C'), $value['nitei_ymd'], 'n/j', 'g', 'AUTO');
                    $pdfObj->write_date(array('type' => 'time', 'x' => 142, 'y' => 300, 'width' => 25, 'height' => 15), $value['nitei_ymd'], $date_format2);
                    if($value['free3_kbn'] != '1'){
                        $pdfObj->write_date(array('type' => 'time', 'x' =>165, 'y' => 300, 'width' =>  35, 'height' =>  15), $value['nitei_ymd'], 'G:i');
                    }
                    break;
                default:
                    break;
            }
            
            $pdfObj->write_string(array('x' =>370, 'y' => $y, 'width' =>  40, 'height' =>  15, 'align' => 'C'), $nitei_nm);           
            if($value['nitei_kbn'] === '7'){
                $y -= 25.5;
            }
            if(!($value['nitei_kbn'] === '12' ||$value['nitei_kbn'] === '13' || $value['nitei_kbn'] === '14')){    //日程の表に表示しない
                $y += 25.8;
                $row++;
            }
            if ($row > 8) {
                break;
            }
        }

        // 式場
            $pdfObj->write_string(array('x' =>375, 'y' => 297, 'width' =>  30, 'height' =>  15, 'align' => 'C'), '式場');
        if (isset($sikijo)) {           
            $pdfObj->write_string(array('x' =>420, 'y' => 297, 'width' =>  145, 'height' =>  15, 'align' => 'C'), $sikijo);
        }
                
        //施行関係者情報        
        $y2 = 671.5;      
        $count = 1;
        $recKankeisha = DataMapper_Pdf0101::find4( $db, array( "seko_no" => $seko_no ) );
        foreach ($recKankeisha as $value) {
            $pdfObj->write_string(array('x' =>30, 'y' => $y2, 'width' =>100, 'height' =>  15) , $value['kankei_nm']);
            $pdfObj->write_string(array('x' =>113, 'y' => $y2, 'width' =>100, 'height' =>  15) , $value['kankei_age']); 
            $pdfObj->write_string(array('x' =>150, 'y' => $y2, 'width' =>40, 'height' =>  15, 'align' => 'C') , $value['zoku_nm']); 
            if($value['kankei_yakushoku'] != null){
                $pdfObj->write_string(array('x' =>196, 'y' => $y2, 'width' =>182, 'height' =>  15) , $value['kankei_kaisya'].' / '.$value['kankei_yakushoku']);
            }else{
                $pdfObj->write_string(array('x' =>196, 'y' => $y2, 'width' =>182, 'height' =>  15) , $value['kankei_kaisya']);
            }
            $pdfObj->write_string(array('x' =>385, 'y' => $y2-6.5, 'width' =>190, 'height' =>  15) , $value['kankei_addr1']);
            $pdfObj->write_string(array('x' =>385, 'y' => $y2+7, 'width' =>190, 'height' =>  15) , $value['kankei_tel']);
            $y2 += 27;
            $count++;
            if ($count > 6) {
                break;
            }
        }
    }   
    /**
     * 会社によってロゴ画像を変える
     *
     * <AUTHOR> Kobayashi
     * @since      2016/10/26
     * @param      Msi_Sys_Db $db
     * @param      $kaisya_cd
     * @param      $seko_no
     * @return     array      該当データがない場合はarray()を返す
     */
    static public function  logo($db, $kaisya_cd, $seko_no) {
        // コード名称マスタ
        $select = $db->easySelect(<<< END_OF_SQL
       SELECT
          s.seko_no
          ,k.kaisya_cd              
          ,k.syamei_logo_img    
        FROM    kaisya_info AS k
                ,seko_kihon_info AS s
       WHERE    k.delete_flg = 0
       AND      k.kaisya_cd  =   :kaisya_cd	-- 会社コード
       AND      s.seko_no    =   :seko_no	-- 会社コード             
       ORDER BY k.kaisya_cd            
END_OF_SQL
        , array('kaisya_cd' => $kaisya_cd, 'seko_no' => $seko_no));
        return $select;
    }
}


