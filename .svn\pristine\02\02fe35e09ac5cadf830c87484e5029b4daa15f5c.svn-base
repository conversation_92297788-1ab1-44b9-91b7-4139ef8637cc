<?php
  /**
   * Logic_BechuNyuknMake
   *
   * 別注品入金伝票　作成処理
   *
   * @category	 App
   * @package	 models\Logic
   * <AUTHOR> Kayo
   * @since 	 2017/02/13
   * @filesource 
   */

  /**
   * 別注品入金伝票　作成処理
   * 
   * @category	 App
   * @package	 models\Logic
   * <AUTHOR> Kayo
   * @since 	 2017/02/13
   */
class Logic_BechuNyuknMake
{
	static protected $_data_kbn = 4;

	/**
	 * 別注品入金伝票 作成処理メイン
	 *
	 * <AUTHOR> Kayo
	 * @since	   2017/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array  $uri_den_no_ary 売上伝票№
	 * @param	   string  $nyukin_ymd 入金日
	 * @return	   bool   true：成功 false:該当なし
	 */
	public static function Main($db, $uri_den_no_ary, $nyukin_ymd)
	{
		$cnt = 0;
		foreach($uri_den_no_ary as $rec) {
			// 売上伝票を取得
			$uri_data = static::getUriDenpyo($db, $rec);
			if (count($uri_data) <= 0) {
				// 売上伝票が存在しない場合は、処理対象外
				continue;
			}
			// 2:現金 3:クレジット 以外は作成しない 
			if (trim($uri_data['pay_method_cd']) != 2   // 現金	
			&& trim($uri_data['pay_method_cd'])  != 3   // クレジット（モニペイ）
			&& trim($uri_data['pay_method_cd'])  != 5   // クレジット(きよらか)
			&& trim($uri_data['pay_method_cd'])  != 7   // クレジット(三井住友)
			&& trim($uri_data['pay_method_cd'])  != 8   // クレジット(PayPay)
			&& trim($uri_data['pay_method_cd'])  != 9   // アスカ
                        ) {	
				continue;
			}
			// 入金伝票を取得
			$nyukin_data = static::getNyukinDenpyo($db, $rec);
			if (Msi_Sys_Utils::myCount($nyukin_data) > 0) {
				// 入金済は、処理対象外
				continue;
			}
			// 売上伝票明細を取得
                        $msi_data = DataMapper_SekyuSakiBechu::find2($db, array('seikyu_no' => $uri_data['uri_den_no']));
                        $shohin_kbn = array();
                        $shohin_ary = array();
                        $total_suryo = 0;
                        $seq_no = null;
                        foreach ($msi_data as $msi) {
                            if (isset($shohin_kbn[$msi['shohin_cd']])) { //商品コード毎の基数
//                                $shohin_kbn[$msi['shohin_cd']]['kyoka_num'] += $msi['kyoka_num'];
                            } else {
                                $shohin_kbn[$msi['shohin_cd']]['shohin_nm'] = $msi['shohin_nm'];
//                                $shohin_kbn[$msi['shohin_cd']]['kyoka_num'] = $msi['kyoka_num'];
//                                $shohin_kbn[$msi['shohin_cd']]['shohin_cd'] = $msi['shohin_cd'];
                            }
                            if ($msi['kyoka_num'] > 1) { //連番 ex.(1)(2)(3～4)
                                $seq_no .= '(' . $msi['seq_no'] . '～' . ($msi['seq_no'] + ($msi['kyoka_num'] - 1)) . ')';
                            } else {
                                $seq_no .= '(' . $msi['seq_no'] . ')';
                            }
                            $total_suryo += $msi['kyoka_num'];
                        }
                        foreach ($shohin_kbn as $shohin_rec) {
                            $shohin_ary[] = $shohin_rec['shohin_nm'];
                        }
                        $shohin_nms = implode('，', array_unique($shohin_ary));
                        $uri_data['shohin_nm'] = $shohin_nms. $seq_no;
                        $uri_data['total_suryo'] = $total_suryo;
			// 入金伝票を作成
			$nyukin_data = static::NyukinDenpyoMake($db, $uri_data, $nyukin_ymd);
			// 入金伝票明細を作成
			$nyukin_msi = static::NyukinDenpyoMsiMake($db, $nyukin_data, trim($uri_data['pay_method_cd']));
			// 会社コードを取得する
			if ( !App_Utils::isFukusuKaisyaKanri() ) {
				$kaisya = DataMapper_KaisyaInfo::find($db);
			} else {
				$kaisyacd = App_Utils::getCtxtKaisyaEasy();
				$kaisya = DataMapper_KaisyaInfo::find($db, array('kaisya_cd' => $kaisyacd));
			}	 
			if (count($kaisya)<= 0) {
				$soukiriyozeikbn= 0;	// 早期利用費消費税区分 0：外税計算 1:内税計算
			} else {
				$soukiriyozeikbn= $kaisya[0]['souki_riyo_zei_kbn']; // 早期利用費消費税区分 0：外税計算 1:内税計算
			}
			// 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
			Logic_SyukeiTblUpdateSub::updNyukinPrc($db, $uri_data['uri_den_no'], $soukiriyozeikbn);

			$ret = Logic_DenpyoHistoryMake::NyukinHistory($db, $nyukin_data);
			// 各種集計テーブル作成、更新処理
			Logic_SyukeiTblUpdate::SyukeiMain($db, $uri_data['denpyo_no'], $uri_data['data_kbn'], 1);
		}	 
		return $cnt;
	}
	
	/**
	 * 売上伝票を取得
	 *
	 * <AUTHOR> Kayo
	 * @since	   2017/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array $upload_rec アップロードレコード
	 * @return	   array $uriData 売上伝票レコード
	 */
	private function getUriDenpyo($db, $uri_den_no)
	{
		// 売上伝票
		$uriData = $db->easySelOne( <<< END_OF_SQL
SELECT *
FROM uriage_denpyo
WHERE delete_flg	= 0
AND	uri_den_no		   = :uri_den_no
END_OF_SQL
		, array('uri_den_no' => $uri_den_no['uri_den_no']));
		
		return $uriData;
	}
	
	/**
	 * 売上伝票明細を取得
	 *
	 * <AUTHOR> Tosaka
	 * @since	   2019/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array $uriData 売上伝票レコード
	 * @return	   array $msiData 売上伝票明細レコード
	 */
    private function getUriMsi($db, $uriData) {
        $msiData = array();
        // 売上伝票
        $select = $db->easySelect(<<< END_OF_SQL
            SELECT m.shohin_nm || COALESCE('(' || m.n_free5 || ')', '') AS shohin_nm
            FROM uriage_denpyo_msi m
            WHERE m.delete_flg = 0
                AND m.uri_den_no = :uri_den_no
            ORDER BY m.n_free5
END_OF_SQL
        , array('uri_den_no' => $uriData['uri_den_no']));
        if (count($select) > 0) {
            $msiData = $select;
        }
        return $msiData;
    }

    /**
	 * 入金伝票を取得
	 *
	 * <AUTHOR> Kayo
	 * @since	   2017/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array $uri_den_no 売上№レコード
	 * @return	   array $uriData 売上伝票レコード
	 */
	private function getNyukinDenpyo($db, $uri_den_no)
	{
		// 入金伝票
		$nyukinData = $db->easySelOne( <<< END_OF_SQL
SELECT *
FROM nyukin_denpyo
WHERE delete_flg	= 0
AND	seikyu_no		  = :seikyu_no
END_OF_SQL
		, array('seikyu_no' => $uri_den_no['uri_den_no']));
		
		return $nyukinData;
	}

	/**
	 * 入金伝票を作成
	 *
	 * <AUTHOR> Kayo
	 * @since	   2016/05/14
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array $uri_rec 売上伝票レコード
	 * @param	   string  $nyukin_ymd 入金日
	 * @return	   array $hdrData 入金伝票レコード
	 */
	private function NyukinDenpyoMake($db, $uri_rec, $nyukin_ymd)
	{
		$hdrData = array();
		// 存在しない入金伝票番号を取得
		$denpyo_no = App_ClsGetCodeNo::GetCodeNo($db, 'nyukin_denpyo', 'denpyo_no',$nyukin_ymd);
		$hdrData['denpyo_no']		=	$denpyo_no;					// 伝票NO
		$hdrData['data_kbn']		=	$uri_rec['data_kbn'];		// データ区分 1：葬儀 2：法事 3：単品 4：別注品
		$hdrData['seko_no'] 		=	$uri_rec['seko_no'];		// 施行番号
		$hdrData['seko_no_sub'] 	=	'00';						// 施行番号（枝番）
		$hdrData['seikyu_no']		=	$uri_rec['uri_den_no']; 	// 請求書№
		$hdrData['nyukin_ymd']		=	$nyukin_ymd;				// 入金日
		$hdrData['nyu_kbn'] 		=	0;							// 入金区分 0:通常	1:FB入金 2：クレジットカード入金 99:互助会入金
		$hdrData['kaisya_cd']		=	$uri_rec['kaisya_cd'];		// 会社コード
		$hdrData['bumon_cd']		=	$uri_rec['bumon_cd'];		// 部門コード 
		$hdrData['tanto_cd']		=	$uri_rec['tanto_cd'];		// 担当者コード
		$hdrData['nyukin_prc']		=	 $uri_rec['uri_prc_sum']	// 売上金額合計
										+$uri_rec['uri_hepn_sum']	// 売上返品合計
										+$uri_rec['uri_nebk_sum']	// 売上値引合計
										+$uri_rec['hoshi_prc_sum']	// 奉仕料合計
										+$uri_rec['out_zei_prc'];	 // 外税消費税額
		$hdrData['text_free1']		=	$uri_rec['shohin_nm'];							// 供花供物の商品名	
		$hdrData['n_free1']		=	$uri_rec['total_suryo'];							// 供花供物の総数量
		$hdrData['out_zei_prc']		=	0;							// 外税消費税額	
		$hdrData['in_zei_prc']		=	0;							// 内税消費税額	
		$hdrData['denpyo_biko1']	=	null;						// 伝票備考１
		$hdrData['denpyo_biko2']	=	'';							// 伝票備考２
		$hdrData['uri_history_no']	=	0;							// 売上履歴番号
		$hdrData['delete_flg']		=	0;							// 削除フラグ

		list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'nyukin_denpyo', $hdrData );
		$cnt = $db->easyExecute($sql, $param);
		return $hdrData;
	}
	
	/**
	 * 入金伝票明細を作成
	 *
	 * <AUTHOR> Kayo
	 * @since	   2017/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array $nyukin_data 入金伝票レコード
	 * @return	   array $msi 入金伝票明細レコード
	 */
	private function NyukinDenpyoMsiMake($db, $nyukin_data, $pay_method_cd)
	{
		$toriatukai_kbn =	99;
		// 現金
		if ($pay_method_cd == 2)	{
			$toriatukai_kbn =	1;
		}
		// クレジット(モニペイ)
		if ($pay_method_cd == 3)	{
			$toriatukai_kbn =	5;
		}
		// クレジット(きよらか)
		if ($pay_method_cd == 5)	{
			$toriatukai_kbn =	6;
		}
		// クレジット(三井住友)
		if ($pay_method_cd == 7)	{
			$toriatukai_kbn =	7;
		}
		// クレジット(PayPay)
		if ($pay_method_cd == 8)	{
			$toriatukai_kbn =	15;
		}
		// アスカ
		if ($pay_method_cd == 9)	{
			$toriatukai_kbn =	17;
		}
		// 科目マスタから「現金」科目を取得する。(今回は手数料は委託者負担のため不要)
		$kamoku = DataMapper_Kamoku::find($db,
				array(
					'delete_flg'	=> 0,
					'kamoku_kbn'	=> 3,                               // 入金科目
					'toriatukai_kbn'=> $toriatukai_kbn,                 // 1:現金 5:クレジット を取得
					'kaisya_cd' 	=> $nyukin_data['kaisya_cd'],       // 会社コード
					'__etc_orderby' => array(array('toriatukai_kbn', 'ASC'))
				));
		//科目が存在しないのはありえないのでエラー	
		if (count($kamoku) <= 0)	{
			return false;
		}
		$count = 0;
		$cnt = 0;
		$tesuryo_prc = 0;
		$nyukin_prc  = $nyukin_data['nyukin_prc'] - $tesuryo_prc;
		// 手数料が払込人負担の場合を考慮してループ処理としておく
		foreach($kamoku as $rec) {
			$dtlRec = array();
			// 入金伝票No
			$dtlRec['denpyo_no'] = $nyukin_data['denpyo_no'];
			++$count;
			// 入金明細No
			$dtlRec['msi_no']		= $count;
			// 表示順
			$dtlRec['disp_no']		= $count;
			// 伝票区分
			$dtlRec['denpyo_kbn']	= $rec['nyukin_sbt'];
			$dtlRec['kaisya_cd']	= $nyukin_data['kaisya_cd'];	  // 会社コード
			// 売上部門コード
			if ($rec['bumon_inp_kbn'] == 0)	{	// 部門入力区分
				$dtlRec['bumon_cd']		= null;
			} else {
				$dtlRec['bumon_cd']		= $nyukin_data['bumon_cd'];
			}	
			// 入金科目コード
			$dtlRec['kamoku_cd']	= $rec['kamoku_cd'];
			// 補助科目コード
			$dtlRec['hojo_cd']		= null;
			$in_zei_prc				= 0;
			// 税コード
			if ($rec['zei_cd_inp_kbn'] == 0)	{	// 税コード入力区分
				$dtlRec['zei_cd'] = null;				// 税コード
			} else { 
				// 税コードマスタから税コードを取得する
				$zeicode = DataMapper_ZeiCodeMst::find($db,
						array(
							'delete_flg'	=> 0,
							'kamoku_cd'		=> $dtlRec['kamoku_cd'],
							'shoihi_zei_cd'	=> $zeicd
						));
				//税コードマスタが存在しないのはありえないがエラーとしない	
				if (count($zeicode) <= 0)	{
					$dtlRec['zei_cd'] = null;				// 税コード
					// 内税金額を算出
					if ($count == 1)	{
						$tax_data = App_ClsTaxLib::GetCalcTax($db, $nyukin_data['nyukin_ymd'], $nyukin_prc, 1);
						$in_zei_prc  =	 $tax_data['ZeiPrc'];
					} else {
						$tax_data = App_ClsTaxLib::GetCalcTax($db, $nyukin_data['nyukin_ymd'], $tesuryo_prc, 1);
						$in_zei_prc  =	 $tax_data['ZeiPrc'];
					}	
				} else {	
					$dtlRec['zei_cd'] = $zeicode[0]['zei_cd'];			// 税コード
					if ($dtlRec['zei_cd'] != 0) {
						// 内税金額を算出
						if ($count == 1)	{
							$tax_data = App_ClsTaxLib::GetCalcTaxZeiCd($db, $dtlRec['zei_cd'], $nyukin_prc, 1);
							$in_zei_prc  =	 $tax_data['ZeiPrc'];
						} else {
							$tax_data = App_ClsTaxLib::GetCalcTaxZeiCd($db, $dtlRec['zei_cd'], $tesuryo_prc, 1);
							$in_zei_prc  =	 $tax_data['ZeiPrc'];
						}	
					} else {
						// 内税金額を算出
						if ($count == 1)	{
							$tax_data = App_ClsTaxLib::GetCalcTax($db, $nyukin_data['nyukin_ymd'], $nyukin_prc, 1);
							$in_zei_prc  =	 $tax_data['ZeiPrc'];
						} else {
							$tax_data = App_ClsTaxLib::GetCalcTax($db, $nyukin_data['nyukin_ymd'], $tesuryo_prc, 1);
							$in_zei_prc  =	 $tax_data['ZeiPrc'];
						}	
					}	
				}
			}	
			if ($count == 1)	{
				$dtlRec['nyukin_prc']	= $nyukin_prc;		 // 入金金額
			} else {
				$dtlRec['nyukin_prc']	= $tesuryo_prc; 	  // 入金金額
			}	
			$dtlRec['out_zei_prc']	=	0;		//外税消費税額
			$dtlRec['in_zei_prc']	= $in_zei_prc;	   //内税消費税額	
			// 明細備考１
			$dtlRec['msi_biko1']	= '';
			// 明細備考２
			$dtlRec['msi_biko2']	= '';
			$dtlRec['delete_flg']	=  0;	 	//削除フラグ
			list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'nyukin_denpyo_msi', $dtlRec );
			$cnt += $db->easyExecute($sql, $param);
		} // for	

		return $cnt;
	}
   
	/**
	 *　入金伝票削除処理メイン
	 *
	 * <AUTHOR> Kayo
	 * @since	   2017/02/13
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array  $uri_den_no_ary 売上伝票№
	 * @param	   string  $nyukin_ymd 入金日
	 * @return	   bool   true：成功 false:該当なし
	 */
	public static function Delete($db, $uri_den_no_ary, $nyukin_ymd)
	{
		$cnt = 0;
		foreach($uri_den_no_ary as $rec) {
			// 売上伝票を取得
			$uri_data = static::getUriDenpyo($db, $rec);
			if (count($uri_data) <= 0) {
				// 売上伝票が存在しない場合は、処理対象外
				continue;
			}
			// 2:現金 3:クレジット 以外は作成しない 
			if (trim($uri_data['pay_method_cd']) != 2   // 現金	
			&& trim($uri_data['pay_method_cd'])  != 3   // クレジット（モニペイ）
			&& trim($uri_data['pay_method_cd'])  != 5   // クレジット(きよらか)
			&& trim($uri_data['pay_method_cd'])  != 7   // クレジット(三井住友)
			&& trim($uri_data['pay_method_cd'])  != 8   // クレジット(PayPay)
			&& trim($uri_data['pay_method_cd'])  != 9   // アスカ
                        ) {	
				continue;
			}
			// 入金伝票を取得
			$nyukin_data = static::getNyukinDenpyo($db, $rec);
			if (Msi_Sys_Utils::myCount($nyukin_data) <= 0) {
				// 存在しない場合は、処理対象外
				continue;
			}
			// 入金伝票、入金伝票明細を削除
			$cnt += static::DenpyoDelete($db, $nyukin_data);
			// 入金伝票、入金伝票明細履歴の削除処理
			$ret = Logic_DenpyoHistoryMake::NyukinHistoryDelete($db, $nyukin_data['denpyo_no']);

			// 会社コードを取得する
			if ( !App_Utils::isFukusuKaisyaKanri() ) {
				$kaisya = DataMapper_KaisyaInfo::find($db);
			} else {
				$kaisyacd = App_Utils::getCtxtKaisyaEasy();
				$kaisya = DataMapper_KaisyaInfo::find($db, array('kaisya_cd' => $kaisyacd));
			}	 
			if (count($kaisya)<= 0) {
				$soukiriyozeikbn= 0;	// 早期利用費消費税区分 0：外税計算 1:内税計算
			} else {
				$soukiriyozeikbn= $kaisya[0]['souki_riyo_zei_kbn']; // 早期利用費消費税区分 0：外税計算 1:内税計算
			}

			// 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
			Logic_SyukeiTblUpdateSub::updNyukinPrc($db, $uri_data['uri_den_no'], $soukiriyozeikbn);

			// 各種集計テーブル作成、更新処理
			Logic_SyukeiTblUpdate::SyukeiMain($db, $uri_data['denpyo_no'], $uri_data['data_kbn'], 1);
		}			 
		return $cnt;
	}
	
	/**
	 * 入金伝票、入金伝票明細を削除
	 *
	 * <AUTHOR> Kayo
	 * @since	   2016/05/15
	 * @param	   Msi_Sys_Db $db	データベース
	 * @param	   array  $rec アップロードレコード
	 * @return	   bool   true：成功 false:該当なし
	 */
	public static function DenpyoDelete($db, $rec)
	{
		$cnt = 0;
		// 入金伝票を削除
		$cnt = $db->easyExecute( <<< END_OF_SQL
		UPDATE nyukin_denpyo
		   SET	 delete_flg =	1
		 WHERE	delete_flg = 0
		 AND	denpyo_no = :denpyo_no
END_OF_SQL
		, array( 'denpyo_no'  => $rec['denpyo_no']
		));
		// 入金伝票明細を削除
		$cnt = $db->easyExecute( <<< END_OF_SQL
		UPDATE nyukin_denpyo_msi
		   SET	 delete_flg =	1
		 WHERE	delete_flg	= 0
		 AND	denpyo_no  = :denpyo_no
END_OF_SQL
		, array( 'denpyo_no'   => $rec['denpyo_no']
		));
		return $cnt;
	}
	
}
