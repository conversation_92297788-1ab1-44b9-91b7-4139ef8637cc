#detail #infochief-tab #birthday_date 
,#detail #infochief-tab #age
,#detail #infochief-tab .lbl_birthday
,#detail #infochief-tab .lbl_houji_seko
,#detail #infochief-tab .lbl_sougi_seko
,#detail #infochief-tab .cls_birthday_era 
,#detail #infochief-tab .cls_sougi_seko
,#detail #infochief-tab .cls_houji_seko
{
    width: 12%;
}
#detail #input-tab .cls_nengo {
    width: 13%;
}
#detail #infochief-tab .cls_birthday_era 
,#detail #infochief-tab .cls_sougi_seko
,#detail #infochief-tab .cls_houji_seko
,#detail #input-tab .cls_nengo
{
    float: left;
}
#detail #input-tab #age_at_death {
    width: 13%;
}
#detail .lbl_1 {
    width: 10%;
}
#detail #input-tab .lbl_age {
    border-right: 1px solid #CCC;
}
#detail #input-tab #date_death {
    width: 15%;
    border-right: none;
}
#detail #input-tab #kojin #kaimyo  
,#detail #input-tab #kojin #kana{
    width: 40%;
}
#detail #input-tab #date_1 
,#detail #input-tab #date_2 
,#detail #input-tab #date_3 
,#detail #input-tab #time_1 
,#detail #input-tab #time_2 
,#detail #input-tab #time_3 
{
    width:13%;
}
#detail #code_1
, #detail #code_2
, #detail #code_3 {
    width: 5.5%;
}
#detail #memo {
    margin-bottom: 0px;
}
#detail #family_name {
    width: 15%;
}
#detail #family_name_kana {
    width: 15%;
}
#detail #s_seko_no 
{
    border-right: none;
    width: 19%;
}
#detail #seko_clear:hover {
    background-color: lightcoral;
    border-radius: 10px;
}
#detail #seko_clear {
    left: 30%;
    top: 10px;
    position: absolute;
    height: 12px;
    width: 12px;
    border: none;
    background-image: url(../../../img/ico_close.png);
    background-repeat: no-repeat;
    background-size: 10px 10px;
    background-position: 1px 1px;
    cursor: pointer;
    display: none;
    z-index: 1;
}
#detail #btn_copy {
    width: 160px;
    height: 22px;
    font-weight: bold;
    padding-top: 1px;
    color: #BBB;
    letter-spacing: 0.1em;
    text-indent: 15px;
    background: none;
    border: none;
    border-radius: 3px;
    box-shadow: 1px 1px 0 #FFF;
    margin: 7px 5px 0 8px;
    background-color: #FFF;
    font-size: 12px;
    height: 26px;
    margin-top: 0px;
    display: none;
}
#detail #btn_copy:active {
    background-color: #369;
    border: 1px solid #FFF;
    box-shadow: none;
    box-shadow: inset 1px 1px 1px rgba(0,0,0,0.2);
    color: #FFF;
}

#detail .lbl_memo2 
,#detail .lbl_memo3 
,#detail .lbl_memo4
{
    border-bottom: 3px solid #A1D7F4;
    border-right: 1px solid #CCC;
}
#detail .lbl_memo2
, #detail #memo2 {
    width: 100%;
    float: none;
}
#detail .lbl_memo3, #detail .lbl_memo4
, #detail #memo3, #detail #memo4  {
    width: 80%;
    float: none;
}
#detail #memo2 ,#detail #memo3 ,#detail #memo4
{
    height: 90px;
    padding: 8px;
}
#detail #memo4 {
    height: 240px;
    float: left;
}
#detail #memo2 {
    margin-bottom: 40px;
}
#detail #input-tab .memo {
    margin-bottom: 0px;
}
#detail .lbl_code {
    width: 10%;
}
#detail #member_detail {
    width: 23%;
}
#detail .lbl_uketuke_date {
    width: 10%;
}
#detail #staff_1 
,#detail #staff_2 
,#detail #uketuke_date
{
    width: 14.5%;
}
#detail .cls_personal_info
{
    width: 13%;
}
#detail .cls_apply_type2
{
    width: 10%;
    float: left;
}
#detail #infodate 
,#detail #sonota 
{
    margin-top: 15px;
}
#detail .lbl_religion
,#detail .lbl_temple
{
    width: 12%;
}
#detail #infodate fieldset {
    margin-bottom: 0;
}
#detail .cls_syushi_cd {
    width: 16%;
}
#detail #temple {
    width: 13%;
}
#detail .lbl_denomination {
    width: 8.5%;
}
#detail #temple_knm {
    width: 23%;
}
#detail #syuha_knm {
    width: 29%;
}
#detail #temple_tel 
,#detail #temple_addr
{
    border-right: 1px solid #ccc;
    font-weight: normal;
}
#detail .lbl_temple_tel {
    width: 11%;
}
#detail #temple_tel {
    width: 18%;
}
#detail #temple_addr {
    width: 68%;
}
#detail #houen h3 
,#detail #zuizen h3 
{
    padding: 5px 0;
    font-size: 15px;
    font-weight: bold;
    color: #276DA5;
}
#detail #sinzoku_ninzu 
,#detail #doshi_ninzu 
,#detail #tonari_ninzu 
,#detail #microbus_daisu 
{
    width: 6%;
    border-right: none;
}

#detail .lbl_sinzoku_ninzu 
,#detail .lbl_doshi_ninzu 
,#detail .lbl_tonari_ninzu 
{
    width: 7%;
}
#detail .lbl_microbus 
{
    width: 9%;
}
#detail .lbl_microbus_set
{
    border-right: 1px solid #CCC;
    width: 7%;
}
#detail #houen {
    margin-bottom: 10px;
}
#detail .lbl_zuizen_set
{
    width: 9%;
    border-right: 1px solid #CCC;
}
#detail .lbl_higan_set {    
    width: 6%;
    
}
#detail input.lbl_zuizen_date {
    border-right: none;
    width: 12%;
}
#detail input.lbl_zuizen_biko {
    width: 35%;
}
#detail #zuizen {
    margin-bottom: 90px;
}
#detail #ki_free {
    width: 5%;
    /*border-right: none;*/
}
#detail .lbl_ki_houyo {
    width: 7%;
}
#detail #address_temple_2 {
    width: 39%;
    margin-left: 12%;
}
#detail #zip_temple {
    width: 10%;
    border-right: none;
}
#detail #address_temple_1 {
    width: 49%;
    border-right: none;
}
#detail #tel_temple {
    width: 19%;
}
/*#detail .lbl_hikae_room {
    width: 10%;
    margin-left: 12%;
}
#detail .lbl_kaishoku_room {
    width: 10%;
}
#detail .lbl_houen {
    position: absolute;
    height: 64px;
    top: 0;
    left: 0;
}
#detail .nitei_date_houen {
    margin-left: 12%;
}
#detail .nitei_basho_1
{
    width: 19%;
    float: left;
}
#detail .nitei_basho_2
{
    width: 29%;
    float: left;
}*/
#detail .lbl_as_address {
    margin: 10px auto 0 auto;
}
#detail .cls_birthday_era {
    width: 8%;
}
#detail #houyo_kenmei {
    width: 76%;
}
#detail #kaiin_cd {
    width: 9%;
    border-right: 0px solid #CCC;
}

#detail #btn_print_sekiji 
,#detail #btn_print_sekihuda 
{    
    background-image: url(../../../img/ico_print.png);
    background-repeat: no-repeat;
    background-position: 10px 8px;
    background-size: 18px 15px;
    display: none;
}
#sekiji-tab .cls_sekiji_pt 
,#sekiji-tab .cls_kamiseki_kbn 
,#sekiji-tab .cls_keisyo_kbn 
{
    float: left;
    width: 12%;
}
#sekiji-tab .cls_keisyo_kbn {
    width: 9%;
}
#sekiji-tab .lbl_kamiseki_kbn 
,#sekiji-tab .lbl_keisyo_kbn {
    width: 8%;
}
#sekiji-tab #kamiseki_ninzu {
    width: 6%;
    text-align: right;
    border-right: none;
}
#sekiji-tab #sekiji_div {
    margin-top: 10px;
    width: 70%;
    /*    position: fixed;
        height: 55%;*/
    height: 550px;
    margin-bottom: 130px;
}
#sekiji_div .sj_header,
#sekiji_div .sj_list
{
    overflow-y: scroll;
}
#sekiji_div .sj_header{
    border-left: 1px solid #88B1D1;
    border-top: 1px solid #88B1D1;
    border-right: 1px solid #88B1D1;

}
#sekiji_div .sj_list{
    height: 100%;
    border-left: 1px solid #88B1D1;
    border-right: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;
    background-color: #E8F3FB;
}
#sekiji_div #sekiji_head, 
#sekiji_div #sekiji_dtl 
{
    width: 100%;
}
#sekiji_div #sekiji_head tr  {
    background-color: #E9F3FB;
}
#sekiji_div #sekiji_head tr td, 
#sekiji_div #sekiji_dtl tr td 
,#sekiji_div .ui-draggable-dragging tr td
{
    height: 30px;
    width: 10%;
    border-right: 1px solid #88B1D1;
    border-bottom: 1px solid #88B1D1;

}
#sekiji_div #sekiji_head tr td 
{
    height: 35px;
    color: #286EA6;
    background-color: transparent;
    text-align: center;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
}

#sekiji_div #sekiji_dtl tr td {
    background-color: #fff;
}
#sekiji_div #sekiji_dtl tr td input
,#sekiji_div .ui-draggable-dragging tr td input
{
    border: none;
    height: 100%;
    width: 100%;
    padding: 2px 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#sekiji_div #sekiji_dtl tr td .lbl_select_kbn
,#sekiji_div .ui-draggable-dragging tr td .lbl_select_kbn
{
    width: 100%;
    height: 100%;
    background-position: center center;
    border: none;
}
#sekiji_div #sekiji_head tr td.h_row_no,
#sekiji_div #sekiji_dtl tr td.d_row_no 
{
    width: 10%;
    text-align: center;
}
#sekiji_div #sekiji_head tr td.h_add_del,
#sekiji_div #sekiji_dtl tr td.d_add_del 
{
    width: 10%;
    text-align: center;
}
#sekiji_div #sekiji_dtl tr .d_add_del a 
,#sekiji_div .ui-draggable-dragging tr .d_add_del a
{
    color: #286EA6;
    height: 100%;
}
#sekiji_div #sekiji_head tr td.h_select,
#sekiji_div #sekiji_dtl tr td.d_select 
{
    width: 10%;
    text-align: center;
}
#sekiji_div #sekiji_head tr td.h_select {
    cursor: pointer;
}
#sekiji_div #sekiji_head tr td.h_select.select_on {
    text-align: center;background-image: url(../../../img/check_3.png);
    background-repeat: no-repeat;
    background-position: center center;
}
#sekiji_div #sekiji_head tr td.h_sekiji_nm,
#sekiji_div #sekiji_dtl tr td.d_sekiji_nm 
{
    width: 30%;
}
#sekiji_div #sekiji_head tr td.h_biko,
#sekiji_div #sekiji_dtl tr td.d_biko 
{
    width: 40%;
}

#sekiji_div .ui-draggable-dragging tr td.d_row_no
,#sekiji_div .ui-draggable-dragging tr td.d_add_del
,#sekiji_div .ui-draggable-dragging tr td.d_select  
{
    width: 8%;
    text-align: center;
}
#sekiji_div .ui-draggable-dragging tr td.d_sekiji_nm {
    width: 20%;
}
#sekiji_div .ui-draggable-dragging tr td.d_biko {
    width: 30%;
}
    
#sekiji_div #sekiji_dtl tbody.tr-hover tr {
    border-top: 5px solid #88B1D1;
}
#detail #seko_hoyo_memo {
    width: 27%;
}