<?php

/**
 * PDF 見積書
 *
 * @category   App
 * @package    controller
 * <AUTHOR>
 * @since      2014/02/19
 * @since      2015/01/26 佐野商店カスタマイズ
 * @since      2015/02/17 サンメンバーズカスタマイズ
 * @version    2017/08/02 Sai 二世会カスタマイズ
 * @filesource 
 */

/**
 * PDF 見積書
 *
 * @category   App
 * @package    controller
 * <AUTHOR>
 * @since      2014/02/19
 */
class Juchu_Pdf0113Controller extends Zend_Controller_Action {

    private static $title = '見積書';
    private static $sourceFileName = array('pdf_tmpl/0113_00.pdf', 'pdf_tmpl/0113.pdf', 'pdf_tmpl/0113_other.pdf', 'pdf_tmpl/0113_00_houji.pdf');
    private static $sourceFileName_s = array('pdf_tmpl/0112_00.pdf', 'pdf_tmpl/0112.pdf');
    private static $sourceFileHyosi = 'pdf_tmpl/0113_H.pdf';
    private static $sourceFileKagami = 'pdf_tmpl/0113_K.pdf';
    private static $sourceFileSaiji= 'pdf_tmpl/0113_other2.pdf';
    private static $color = array(0, 32, 96);

    const CHU_BUNRUI_CD_A = '0020';
    const CHU_BUNRUI_CD_B = '0030';
    const CHU_BUNRUI_CD_PLAN = '0040';
    const CHU_BUNRUI_CD_OKUNI = '0050';
    const CHU_BUNRUI_CD_C = '0060';
    const CHU_BUNRUI_CD_D = '0070';
    const CHU_BUNRUI_CD_E = '0080';
    const CHU_BUNRUI_CD_F = '0100';
    const CHU_BUNRUI_CD_G = '0090';
    const CHU_BUNRUI_CD_H = '0120';
    const ADD_KBN_GOJYOKAITOKUTEN = 8;
    const SERVICE_KBN_GOJYOKAI = 1;
    const SERVICE_KBN_GOJYOKAI_NEBIKI = 2;

    /** 大分類コード：0005=>プラン　中分類コード0050プランを　大分類0005とみなす（SQLで処理済） */
    const DAI_BUNRUI_P = "0005";
    /** 大分類コード：0010=>葬送儀礼 */
    const DAI_BUNRUI_1 = "0010";
    /** 大分類コード：0110=>法事 */
    const DAI_BUNRUI_1H = "0110";

    /** 大分類コード：0020=>会葬返礼 */
    const DAI_BUNRUI_2 = "0020";

    /** 大分類コード：0030=>料理 */
    const DAI_BUNRUI_3 = "0030";

    /** 大分類コード：0050=>別途費用 */
    const DAI_BUNRUI_5 = "0050";

    /** 大分類コード：0060=>立替費用 */
    const DAI_BUNRUI_6 = "0060";

    /** 大分類コード：0070=>値引 */
    const DAI_BUNRUI_7 = "0070";
    const SUMID_KEIYAKUGAKU = 'keiyaku_gaku';
    const SUMID_A_PLAN = 'a_plan';
    const SUMID_A_GOJOKAI = 'a_gojokai';
    const SUMID_A_GOJOKAINEBIKI = 'a_gojokai_nebiki';
    const SUMID_A_NEBIKI_PRC = 'a_nebiki';
    const SUMID_A_GOJOKAIGAI = 'a_gojokai_gai';
    const SUMID_AZUKARI_DAN = 'azukari_dan';
    const SUMID_AZUKARI_HEN = 'azukari_hen';
    const SUMID_MEIGICGCOST = 'mg_chg_cost';
    const SUMID_EARLYUSE = 'early_use';
    const SUMID_EARLYUSEZEI = 'early_use_zei';
    const SUMID_COSECHGGAKU = 'cose_chg_gaku';
    const SUMID_GOJOHARAI = 'gojo_harai';
    const SUMID_ZENNOWARI = 'zen_wari';
    const SUMID_KAKEZEISAGAKU = 'kake_zei_sagaku';
    const SUMID_KAKEZAN = 'zan_gaku';
    const SUMID_KAKEZANZEI = 'zan_gaku_zei';

    private static $sum_name = array(
        self::SUMID_A_PLAN => '葬送儀礼',
        self::SUMID_A_GOJOKAI => '互助会品目合計',
        self::SUMID_A_GOJOKAINEBIKI => '会員値引き',
        self::SUMID_A_NEBIKI_PRC => '割引金額',
        self::SUMID_A_GOJOKAIGAI => '互助会外プラン品目合計',
        self::SUMID_AZUKARI_DAN => '預り金',
        self::SUMID_AZUKARI_HEN => '預り金',
        self::SUMID_MEIGICGCOST => '名義変更手数料',
        self::SUMID_EARLYUSE => '早期利用費',
        self::SUMID_EARLYUSEZEI => '早期利用費消費税',
        self::SUMID_KAKEZAN => '会費残額',
        self::SUMID_KAKEZANZEI => '会費消費税',
        self::SUMID_COSECHGGAKU => 'コース変更差額',
        self::SUMID_GOJOHARAI => '互助会払込金額',
        self::SUMID_ZENNOWARI => '前納割引',
        self::SUMID_KAKEZEISAGAKU => '掛金消費税差額'
    );
    private static $msi_name_gojokai = 'A-1（互助会）';
    private static $msi_name_gojokaitokuten = '互助会会員特典';
    private static $msi_name_gojokaigai = 'A-2（プラン選択商品）';

    /** 申込区分 1：葬儀 2：法事 5：生前依頼 6：その他依頼 */
    private $_moushiKbn;

    /** 申込区分 1：葬儀 2：法事 5：生前依頼 6：その他依頼 */
    private $_history_no = null; // 受注伝票履歴より印刷する時の履歴番号
    
    /** ヘッダーに表示する施行番号 */
    private $_disp_seko_no = null;

    /**
     * アクション
     *
     * <AUTHOR> Sato
     * @since 2014/xx/xx
     */
    public function indexAction() {
        $params = Msi_Sys_Utils::webInputs();
        if (isset($params['preview'])) {
            $preview = htmlspecialchars($params['preview']);          // プレビュー有無
        } else {
            $preview = 'off';
        }
//        $issue_date = htmlspecialchars($params['issue_date']);    // 発行日
        $seko_no = htmlspecialchars($params['seko_no']);          // 施行番号
        if (array_key_exists('seko_no_sub', $params)) {
            $seko_no_sub = htmlspecialchars($params['seko_no_sub']);
        } // 施行番号（枝番）
        else {
            $seko_no_sub = '00';
        }
        $data_kbn = htmlspecialchars($params['data_kbn']);        // データ区分　1：葬儀 2：法事 3：単品 4：別注品
        $this->_moushiKbn = DataMapper_PdfCommon::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
        if (isset($params['history_no'])) {
            $this->_history_no = $params['history_no'];
        }
        if (isset($params['printKbn'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_kbn = $params['printKbn'];
        } else {
            $print_kbn = 0;
        }

        // 軽減税率対応 sugiyama keigen   基準日の設定
        $this->_prepKijunYmdKeigen($seko_no, $seko_no_sub, $data_kbn);

        $issue_date = date('Y/m/d');

        $db = Msi_Sys_DbManager::getMyDb();
        $pdfObj = new App_Pdf($this->getFileName($db, $seko_no, self::$title));

        // 御葬儀打合せ事項
		if ($data_kbn == 1)	{
			require_once 'Pdf0101Controller.nise01.php';
			Juchu_Pdf0101Controller::outData($pdfObj, $db, $seko_no);
		}	
        if ($this->_moushiKbn == 2){
            //「法事」場合
			$this->outData_houi($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);
		} else {
			//「葬儀」の場合
			$this->outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);
		}	

        $row = DataMapper_Pdf0113::getSekoKihonInfo($db, $seko_no);
        // 見積確定後は、『施行申込書』と『御葬儀打合せ事項』も出力する。見積履歴の場合除く
        if ($row['status_kbn'] > 1 && $this->_moushiKbn != 2 && is_null($this->_history_no)) {
            // 施行申込書
            $this->outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, true);
        }

        if ($preview == 'on') {
            $buf = $pdfObj->fileOutBuf();
            $file_name = $pdfObj->getFileName();
            $key = Msi_Sys_Utils::prepareMyTempFileEasy($buf, $file_name);
            $this->view->file = $file_name;
            $this->view->key = $key;
            $this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
            $this->_helper->viewRenderer->setScriptAction('pdf');
        } else {
            $pdfObj->download();
        }
    }

    /**
     * 見積書（葬儀）
     * @since 2015/05/20
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outData($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei = false) {

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数
//        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[1]);
		if ($isMituKakutei) {
			$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName_s[1]);
		} else {
			$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[1]);
		}

        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $recCmn = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
        if (count($recCmn) == 0) {
            return;
        }

        // 軽減税率対応  事業者登録番号  2019/04/30 sugiyama keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ( $toroku_bango ) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x'=>450,'y'=>25,'width'=>125,'height'=>10,'font_size'=>10,'align'=>'R'), $bango_str);
            }
        }

        // 社判
//        $pdfObj->syaban_out($db, 1, 360, 70);
        $pdfObj->syaban_out($db, 1, 405, 65, 60);		//位置調整
        $logo = null;
        if (isset($recCmn[0]['syaban4_img'])) {
            $logo = $db->readBlobCont($recCmn[0]['syaban4_img']);
        }
        $kaisyalogo = array(
            'logo' => $logo,
            'nm' => '',
            'bumon_nm' => $recCmn[0]['bumon_lnm'],
            'bumon_tel' => $recCmn[0]['tel']
        );

        $recWk = DataMapper_Pdf0113::find($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub));
        if (count($recWk) == 0) {
            return;
        }
        $rec = $recWk[0];

        // 請求先
        $sekyu = App_Utils::getSekoSekyuInfo($seko_no);
        $sekyu_nm = '';
        if (isset($sekyu)) {
            $pdfObj->write_string(array('x' => 45, 'y' => 67, 'width' => 180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_nm']);
            $pdfObj->write_string(array('x' => 45, 'y' => 88, 'width' => 200, 'height' => 10, 'font_size' => 10), $sekyu['yubin_no'] . '　' . $sekyu['addr1']);
            $pdfObj->write_string(array('x' => 45, 'y' => 98, 'width' => 200, 'height' => 10, 'font_size' => 10), $sekyu['addr2']);
            $pdfObj->write_string(array('x' => 165, 'y' => 108, 'width' => 80, 'height' => 15), $sekyu['tel']);
            $sekyu_nm = $sekyu['sekyu_nm'];
        }
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 165, 'y' => 118, 'width' => 80, 'height' => 15), $rec['sougi_ymd'], 'Y年m月d日');

        // 印鑑
        if (isset($rec['inkan_img'])) {
            $img = $db->readBlobCont($rec['inkan_img']);
            $pdfObj->seal_out(534, 746, $img);
        }

        // 受付部門
        $pdfObj->write_multi(
                array(
                    array('type' => 'string', 'x' => 460, 'y' => 781, 'width' => 115, 'height' => 15, 'font_size' => 10, 'value' => $recCmn[0]['bumon_lnm']),
                    array('type' => 'string', 'x' => 460, 'y' => 792, 'width' => 115, 'height' => 15, 'font_size' => 10, 'value' => $rec['tanto_nm']),
                    array('type' => 'ymd', 'x' => 460, 'y' => 802, 'width' => 115, 'height' => 15, 'font_size' => 10, 'value' => $issue_date, 'format' => 'Y年m月d日')
                )
        );

        // 会社情報会員名取得する 互助会の文字列を置き換え
        $kain_nm = App_Utils::getKainNm();
        self::$sum_name[self::SUMID_A_GOJOKAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAI]);
        self::$sum_name[self::SUMID_A_GOJOKAINEBIKI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        self::$sum_name[self::SUMID_A_GOJOKAIGAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAIGAI]);
        self::$sum_name[self::SUMID_GOJOHARAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_GOJOHARAI]);
        self::$msi_name_gojokai = str_replace('互助会', $kain_nm, self::$msi_name_gojokai);
        self::$msi_name_gojokaitokuten = str_replace('互助会', $kain_nm, self::$msi_name_gojokaitokuten);

        $pages = $pdfObj->getNumPages();
		// 明細書
		$sumArr = $this->outDataDetail($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $rec['kain_no'], $print_kbn, $isMituKakutei);

		// 表紙の金額
		if ($isMituKakutei) {
			$pdfObj->setPage($pages);
		} else {
			$pdfObj->setPage(2);
		}
        $kingaku = App_MitsuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn, $this->_history_no);
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_MitsuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_MitsuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_MitsuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_MitsuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_MEIGICGCOST] = array('name' => self::$sum_name[self::SUMID_MEIGICGCOST], 'sum' => $kingaku[App_MitsuLib::ID_MEIGICGCOST]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_MitsuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_MitsuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        if (is_null($this->_history_no)) {
            $sum = App_Utils::getMitsuKingaku($seko_no, $seko_no_sub, $data_kbn);
            // 軽減税率対応のためデータ設定  2019/04/30 sugiyama keigen
            $this->_p_tgt_type    = 'normal';
            $this->_p_seko_no     = $seko_no;
            $this->_p_seko_no_sub = $seko_no_sub;
            $this->_p_data_kbn    = $data_kbn;
            $this->_p_history_no  = null;
        } else {
            $sum = App_Utils::getMitsuKingakuFromHistory($seko_no, $seko_no_sub, $data_kbn, $this->_history_no);
            // 軽減税率対応のためデータ設定  2019/04/30 sugiyama keigen
            $this->_p_tgt_type    = 'history';
            $this->_p_seko_no     = $seko_no;
            $this->_p_seko_no_sub = $seko_no_sub;
            $this->_p_data_kbn    = $data_kbn;
            $this->_p_history_no  = $this->_history_no;
        }
        $sumArr['total'] = array('name' => '御見積金額', 'sum' => $sum);

        // 表紙の金額を出力
		$this->outDataFaceSum($pdfObj, $sumArr, $rec['gojokai_cose_nm'], $recWk , $seko_no);
		$this->outKaiinInfo($db, $pdfObj, $seko_no);

        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            // 確認用のグリッドを出力
            //$pdfObj->test_line_out(600, 1000);

            if ((!$isMituKakutei)) { // 見積書
                $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' => 15, 'align' => 'C'), ($page - 1) . '/' . ($numPages - 1));
            } else { // 施行申込書
                $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' => 15, 'align' => 'C'), ($page + 1 - $pages) . '/' . ($numPages + 1 - $pages));
            }

            // 明細書
            if ((!$isMituKakutei && $page > 2) || ($isMituKakutei && $page > $pages)) {
                // 請求先名
                $pdfObj->write_string(array('x' => 45, 'y' => 59, 'width' => 180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu_nm);

                // 会社ロゴ
                $pdfObj->kaisyalogo_ary_out(400, 35, $kaisyalogo, 200, 140, 14, 12, 16);

                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' => 470, 'y' => 810, 'width' => 115, 'height' => 9, 'font_size' => 9), $issue_date, 'Y年m月d日');

                // 軽減税率対応 凡例   2019/04/30 sugiyama keigen
                if ( $this->_isKeigenAppliedCtxt() ) { // &&
                    // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                    $pdfObj->write_string(array('x'=>45, 'y'=>810, 'width'=>180, 'height'=>9, 'font_size'=>9, 'align' => 'L'),
                                      '税欄の＊は軽減税率対象');
                }
            }
            
            $pdfObj->write_string(array('x' => 470, 'y' => 820, 'width' => 130, 'height' => 16, 'font_size' => 9), $this->_disp_seko_no); // 施行番号
            
        }
    }

    /**
     * 会員番号を取得
     * @param type $db
     * @param type $seko_no
     * @param type $yoto_kbn
     * @return null
     */
    private function getKainNo($db, $seko_no, $yoto_kbn) {
        return DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no, "yoto_kbn" => $yoto_kbn));
    }

    /**
     * 明細を出力
     * 
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param string $seko_no_sub　施行番号枝番
     * @param string $kain_no	会員番号
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @return array    明細金額を集計した配列
     */
    private function outDataDetail($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $kain_no, $print_kbn, $isMituKakutei) {
        static $meisai_top = 110;
        static $meisai_row_height = 23.4;
        static $meisai_row_count = 30;

        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);

        $set_arr[] = array('x' =>  45, 'y' => $meisai_top, 'width' => 160, 'height' => 15); // 項目1 摘要がない場合
        $set_arr[] = array('x' =>  45, 'y' => $meisai_top - 7, 'width' => 160, 'height' => 15); // 項目2 摘要がある場合
        $set_arr[] = array('x' =>  45, 'y' => $meisai_top + 4, 'width' => 160, 'height' => 15); // 摘要
        $set_arr[] = array('x' => 200, 'y' => $meisai_top, 'width' => 40, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 240, 'y' => $meisai_top, 'width' => 55, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 295, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 355, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 付帯・割引
        $set_arr[] = array('x' => 415, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 475, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 請求金額
        $set_arr[] = array('x' => 533, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // 税
        $set_arr[] = array('x' => 555, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // サ

        $breakKey = '';
        $dai_bunrui_nm = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $itemAry = array();
        $beforeRow = null;
        $kbn_nm = '';
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki1 = 0;// 項目ごとの互助会値引き小計
        $shokei_nebiki2 = 0;// 項目ごとの割引小計
        
//        $chubunrui_cd_a = self::CHU_BUNRUI_CD_A;
//        if ($data_kbn == 2){
//            $chubunrui_cd_a = self::CHU_BUNRUI_CD_H_B;
//        }
        // 【A】葬送儀礼費用基本プランの項目
        $kainNo = $this->getKainNo($db, $seko_no, 1);
        if (count($kainNo) > 0) {
            $itemAry[self::DAI_BUNRUI_1][] = self::$msi_name_gojokai;
            $i = 0;
            while ($i < count($kainNo)) {
                $itemAry[self::DAI_BUNRUI_1][] = '(' . $kainNo[$i]['kain_no'] . ')';
                $i++;
            }
        }

        if (is_null($this->_history_no)) {
            $recMsi = DataMapper_Pdf0113::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub), $print_kbn);
        } else {
            $recMsi = DataMapper_Pdf0113::findMsiHistory($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "history_no" => $this->_history_no), $print_kbn);
        }
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (count($row_arr) > 1) {
                    if ($shokei_nebiki1 == 0) {
                        $shokei_nebiki1 = null;
                    }
                    if ($shokei_nebiki2 == 0) {
                        $shokei_nebiki2 = null;
                    }
                    // 合計
                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, null, null);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }

                // 項目
                $row_arr[] = array('◆' . $value['dai_bunrui_nm'] . '◆', null, null, null, null, null, null, null, null, null, null);

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
                
                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
                $shokei_nebiki2 = 0; // 項目ごとの割引小計
            }
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $gojokai_nebiki_prc + $nebiki_prc; // 請求金額
            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }
            $shohin_nm = '　　' . $value['shohin_nm'];

            // 軽減税率対応  2019/04/30 sugiyama keigen 
            if ( $this->_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['zei_disp'] = $value['zei_disp'] . '＊';
                }
            }
            if (empty($value['shohin_tkiyo_nm'])) { // 摘要無し
                $row_arr[] = array($shohin_nm, null, null, $value['juchu_suryo'], $value['juchu_tnk'], $value['juchu_prc'], $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $value['zei_disp'], $value['hoshi_disp']);
            } else { // 摘要あり
                $row_arr[] = array(null, $shohin_nm, '　　' . $value['shohin_tkiyo_nm'], $value['juchu_suryo'], $value['juchu_tnk'], $value['juchu_prc'], $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $value['zei_disp'], $value['hoshi_disp']);
            }

            // 葬送儀礼
            if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_1) {
                $sumArr[self::SUMID_A_PLAN]['sum'] += $value['juchu_prc']; // 基本費用
                $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $value['gojokai_nebiki_prc']; // 付帯特典
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] += $value['nebiki_prc']; // 割引金額
            }
            $total_ippan += $value['juchu_prc'];
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $shokei_ippan += $value['juchu_prc']; // 項目ごとの金額小計
            $shokei_nebiki1 += $value['gojokai_nebiki_prc']; // 項目ごとの互助会値引き小計
            $shokei_nebiki2 += $value['nebiki_prc']; // 項目ごとの割引小計
            $chuCount++;
            $beforeRow = $value;
        }

        if (count($recMsi) > 0) {
            // 合計
            if ($shokei_nebiki1 == 0) {
                $shokei_nebiki1 = null;
            }
            if ($shokei_nebiki2 == 0) {
                $shokei_nebiki2 = null;
            }
            $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, null, null);
            $rec = $recMsi[count($recMsi) - 1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }
        $fileName = self::$sourceFileName[0];
        if ($isMituKakutei) {
            $fileName = self::$sourceFileName_s[0];
        }

        $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr, $meisai_row_count, __DIR__ . '/' . $fileName);

        return $sumArr;
    }

    /**
     * 表紙の金額を出力
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    private function outDataFaceSum($pdfObj, $sumArr, $coseNm, $recWk, $seko_no) {
        $row_height = 15.8;
        static $item_indent = '　　';
        $cosechginfo = '';
        //互助会複数対応
        if (count($recWk) > 1) {

            $coseNm = "";
            $coseNm_bk = "";
            $coseNm_dub = null;
            $cose_ary = array();
            $coses = false;
            $i = 0;
            while ($i < count($recWk)) {
                if (isset($recWk[$i]['gojokai_cose_nm'])) {
                    $pos = array_search($recWk[$i]['gojokai_cose_nm'], $cose_ary);

                    //同コースが2回目の場合はスキップ
                    if ($coseNm_bk == $recWk[$i]['gojokai_cose_nm']) {
                        $coseNm_dub = $recWk[$i]['gojokai_cose_nm'];
                    }
                    //前回とコース名称が違う場合はコース名に追記する
                    else if ($coseNm_bk != $recWk[$i]['gojokai_cose_nm']) {
                        if ($coseNm_bk != '') {
                            $coses = true;
                        }
                    }
                    //前回分のコース名称確保
                    $coseNm_bk = $recWk[$i]['gojokai_cose_nm'];
                    $cose_ary[] = $recWk[$i]['gojokai_cose_nm'];
                }
                $i++;
            }

            //複数コースがある場合
            if ($coses == true) {
                $i = 0;
                $gojokai_prc = 0;
                while ($i < count($recWk)) {
                    $gojokai_prc += $recWk[$i]['gojokai_prc'];
                    $i++;
                }

                if ($gojokai_prc == 180000) {
                    $coseNm = 'Ｌ２コース';
                    $cosechginfo = '互助会はＬ2コース（18万）にコース変更して使用いたします。';
                } else if ($gojokai_prc == 270000) {
                    $coseNm = 'Ｌ３コース';
                    $cosechginfo = '互助会はＬ3コース（27万）にコース変更して使用いたします。';
                } else if ($gojokai_prc == 360000) {
                    $coseNm = 'Ｌ４コース';
                    $cosechginfo = '互助会はＬ4コース（36万）にコース変更して使用いたします。';
                }
                $kain_nm = App_Utils::getKainNm();
                $cosechginfo = str_replace('互助会', $kain_nm, $cosechginfo);
            }
        }

        $set_arr[] = array('x' => 50, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 350, 'width' => 80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 440, 'width' => 135, 'height' => 15); // 備考
        $set_arr[] = array('x' => 250, 'width' => 90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 45, 'y1' => -2, 'x2' => 577, 'y2' => -2, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線
        //
        // 葬送儀礼費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 173;
        }

        $total1 = 0;
        $row_arr = array();

        if (isset($sumArr[self::SUMID_COSECHGGAKU]) && $sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
            $biko_cose_chg = floor($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] / 10000) . '万コース→' .
                    floor(($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] + $sumArr[self::SUMID_COSECHGGAKU]['sum']) / 10000) . '万コース';
        } else if (isset($coseNm_dub)) {
            $tmpArray = array_count_values($cose_ary);
            $cnt = $tmpArray[$coseNm_dub];
            if ($cnt > 1) {
                $biko_cose_chg = $coseNm_dub . "(" . $cnt . "口)";
            } else {
                $biko_cose_chg = null;
            }
        } else {
            $biko_cose_chg = null;
        }
        // 葬送儀礼費用基本プラン　小計　　　　　　　　　　　　　（ア）
        // 葬送儀礼費用基本プラン　葬送儀礼費用プラン外選択品目　（イ）
        // 葬送儀礼費用プラン外選択品目　　　　　　　　　　　　　（ウ）
        // 葬送儀礼費用　小計　　　　　　（ア）＋（イ）＋（ウ）
        // の（ア）・（イ）・（ウ）を出力する
        static $fld_no = array('（ア）', '（イ）', '（ウ）');
        $fld_no_index = 0;

        // パックプラン
        if (isset($sumArr[self::DAI_BUNRUI_P])) {
            if ($sumArr[self::DAI_BUNRUI_P]['sum'] != 0) {
                $value = $sumArr[self::DAI_BUNRUI_P];
                $p_sum = $value['sum'];
                $row_arr[] = array($value['name'], $p_sum);
                $total1 += $value['sum'];
            }
        }
        
        // 【A】葬送儀礼費用基本プラン
        if (isset($sumArr[self::DAI_BUNRUI_1])) {
            if ($sumArr[self::SUMID_A_PLAN]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_PLAN];
                $row_arr[] = array($value['name'], $value['sum']);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAINEBIKI];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_NEBIKI_PRC];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            $row_arr[] = null;
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 7; $index++) {
            $row_arr[] = null;
        }

        // 葬送儀礼費用合計
        $fld_no_syokei = null;
        for ($index = 0; $index < $fld_no_index; $index++) {
            if (isset($fld_no_syokei)) {
                $fld_no_syokei .= '＋';
            }
            $fld_no_syokei .= $fld_no[$index];
        }
        $row_arr[] = array('', $total1, null, $fld_no_syokei);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // おもてなし費用
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 335;
        }

        $total2 = 0;
        $row_arr = null;

        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'] . '特典', $value['sum_t']);
            }
            $total2 += $value['sum'];
        }

        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'] . '特典', $value['sum_t']);
            }
            $total2 += $value['sum'];
        }
        // 空行を設定
        for ($index = count($row_arr); $index < 4; $index++) {
            $row_arr[] = null;
        }

        // おもてなし費用合計
        $row_arr[] = array('', $total2);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // その他費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 450;
        }

        $total3 = 0;
        $row_arr = array(); // sugiyama patch 2019/04/30   null;

        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
            if ($value['sum'] != 0) {
                $row_arr[] = array($value['name'], $value['sum']);
                $total3 += $value['sum'];
            }
            if ($value['sum_k'] != 0) {
                $row_arr[] = array('供花(枕花含む)・供物費用', $value['sum_k']);
                $total3 += $value['sum_k'];
            }
        }

        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
            $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 3; $index++) {
            $row_arr[] = null;
        }

        // その他費用合計
        $row_arr[] = array('', $total3);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // 御見積金額
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 552;
        }

        $total5 = 0;
        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
        $row_arr = null;

        // 合計④
        $row_arr[] = array($item_indent . '合計④＝小計①＋小計②＋小計③', $total1 + $total2 + $total3);
        $total4 = $total1 + $total2 + $total3;

        $total_nm = '合計金額＝合計④';
        static $circle_num = array(5 => '⑤', 6 => '⑥', 7 => '⑦', 8 => '⑧');
        $num = 5;

        // 合計④の消費税
        if (isset($sumArr['tax'])) {
            $value = $sumArr['tax'];
            $row_arr[] = array($item_indent . '合計④の消費税　' . $circle_num[$num], $value['sum']);
            $total5 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }

        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum']);
            $total6 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum']);
            $total7 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum']);
            $total8 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }

        // 合計金額
        $row_arr[] = array($item_indent . $total_nm, $total4 + $total5 + $total6 + $total7 + $total8);
        $total_sum = $total4 + $total5 + $total6 + $total7 + $total8;

        $zei_ritu_disp = ''; // 税率文言
        $zei_ritu = ''; // 税率文言
        if (count($recWk) > 0) {
            $kijun = $recWk[0]['sougi_ymd'];
            if (empty($kijun)) {
                $kijun = date('Y/m/d');
            }
            $zeiTbl = App_ClsTaxLib::GetGojoTaxInfo(Msi_Sys_DbManager::getMyDb(), $kijun);
            if (count($zeiTbl) > 0) {
                $zei_ritu = $zeiTbl['zei_rtu'];
                $zei_ritu_disp = '(' . + $zei_ritu . '%)';
            }
        }
        
        $zan_disp = ''; // 残額文言
        if ($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] != 0 && $sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
            $keiyaku = $sumArr[self::SUMID_KEIYAKUGAKU]['sum'];
            $zan = $sumArr[self::SUMID_KAKEZAN]['sum'];
            $harai = $keiyaku - $zan;
            $zan_disp = ' (' .  Msi_Sys_Utils::num_format($keiyaku) . ' - ' . Msi_Sys_Utils::num_format($harai) . ')';
        }
        // 掛金残金
        if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZAN];
            $row_arr[] = array($item_indent . $value['name'] . $zan_disp, $value['sum']);
        }
        
        // 名義変更手数料
        if ($sumArr[self::SUMID_MEIGICGCOST]['sum'] != 0) {
            $value = $sumArr[self::SUMID_MEIGICGCOST];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }
        
        // 早期利用費
        if ($sumArr[self::SUMID_EARLYUSE]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSE];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 早期利用費の消費税
        if ($sumArr[self::SUMID_EARLYUSEZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSEZEI];
            $row_arr[] = array($item_indent . $value['name'] . $zei_ritu_disp, $value['sum']);
        }

        // 掛金残金の消費税
        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZANZEI];
            $gojokai_zei_ritu_disp = DataMapper_SekoGojokaiMember::getZeirtu(Msi_Sys_DbManager::getMyDb(), array('seko_no' => $seko_no));
            $row_arr[] = array($item_indent . $value['name'] . $gojokai_zei_ritu_disp, $value['sum']);
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 10; $index++) {
            $row_arr[] = null;
        }

        // 御見積金額
        if (isset($sumArr['total'])) {
            $value = $sumArr['total'];
            $row_arr[] = array('', $value['sum']);
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 消費税金額の内訳出力     2019/04/30 sugiyama keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake($pdfObj);
        }
    }

    /**
     * 表紙の金額を出力（その他)
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    private function outDataFaceSum_other($pdfObj, $sumArr, $coseNm) {
        $row_height = 15.8;
        static $item_indent = '　　';

        $set_arr[] = array('x' => 50, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 350, 'width' => 80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 440, 'width' => 135, 'height' => 15); // 備考
        $set_arr[] = array('x' => 250, 'width' => 90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 45, 'y1' => -2, 'x2' => 577, 'y2' => -2, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線
        //
        // 葬送儀礼費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 173;
        }

        $total1 = 0;
        $row_arr = null;

        // コース変更
        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
            $biko_cose_chg = floor($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] / 10000) . '万コース→' .
                    floor(($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] + $sumArr[self::SUMID_COSECHGGAKU]['sum']) / 10000) . '万コース';
        } else {
            $biko_cose_chg = null;
        }

        // 葬送儀礼費用基本プラン　小計　　　　　　　　　　　　　（ア）
        // 葬送儀礼費用基本プラン　葬送儀礼費用プラン外選択品目　（イ）
        // 葬送儀礼費用プラン外選択品目　　　　　　　　　　　　　（ウ）
        // 葬送儀礼費用　小計　　　　　　（ア）＋（イ）＋（ウ）
        // の（ア）・（イ）・（ウ）を出力する
        static $fld_no = array('（ア）', '（イ）', '（ウ）');
        $fld_no_index = 0;

        // 【A】葬送儀礼費用基本プラン
        if (isset($sumArr[self::DAI_BUNRUI_1])) {
            $value = $sumArr[self::DAI_BUNRUI_1];
            //          $row_arr[] = array($value['name'], '');
            if ($sumArr[self::SUMID_A_PLAN]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_PLAN];
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAI];
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAINEBIKI];
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAIGAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAIGAI];
                $total1 += $value['sum'];
            }
            //           $row_arr[] = null;
        }

        // 葬送儀礼費用合計
        $fld_no_syokei = null;
        for ($index = 0; $index < $fld_no_index; $index++) {
            if (isset($fld_no_syokei)) {
                $fld_no_syokei .= '＋';
            }
            $fld_no_syokei .= $fld_no[$index];
        }

        $fld_no2 = array('計①', '計②', '計③');
        $fld_total = '';
        if ($total1 != 0) {
            $kei = array_shift($fld_no2);
            $fld_total .= $kei;
            $row_arr[] = array('葬送儀礼費用　 ' . $kei, $total1, null, $fld_no_syokei);
        }

        $total2 = 0;
        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
            $total2 += $value['sum'];
        }

        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
            //           $row_arr[] = array($value['name'], $value['sum']);
            $total2 += $value['sum'];
        }

        // おもてなし費用合計        
        if ($total2 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('おもてなし費用 ' . $kei, $total2);
        }

        $total3 = 0;
        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
            //         $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
            $total3 += $value['sum'];
        }

        // その他費用合計
        if ($total3 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('その他費用 　　' . $kei, $total3);
        }

        $total5 = 0;
        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
        // 空行を追加
        $row_arr[] = null;

        $total_kei = '合計';
        if (!empty($fld_total)) {
            $total_kei = $total_kei . '＝' . $fld_total;
        }
        // 合計④
        $row_arr[] = array($item_indent . $total_kei, $total1 + $total2 + $total3, null, '（ア）');
        $total4 = $total1 + $total2 + $total3;

        $total_nm = '合計金額＝（ア）';
        static $circle_num = array(5 => '⑤', 6 => '⑥', 7 => '⑦', 8 => '⑧');
        $num = 5;

        // 合計④の消費税
        if (isset($sumArr['tax'])) {
            $value = $sumArr['tax'];
            $row_arr[] = array($item_indent . '合計の消費税', $value['sum'], null, '（イ）');
            $total5 = $value['sum'];

            $total_nm .= '＋（イ）';
            $num++;
        }

        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            $row_arr[] = array($item_indent . '立替金', $value['sum'], null, '（ウ）');
            $total6 = $value['sum'];

            $total_nm .= '＋（ウ）';
            $num++;
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($item_indent . $value['name'], $value['sum'], null, '（エ）');
            $total7 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（エ）';
            $num++;
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($item_indent . $value['name'], $value['sum'], null, '（オ）');
            $total8 = $value['sum'];

            $total_nm .= '＋（オ）';
            $num++;
        }

        // 合計金額
        $row_arr[] = array($item_indent . $total_nm, $total4 + $total5 + $total6 + $total7 + $total8);
        $total_sum = $total4 + $total5 + $total6 + $total7 + $total8;

        // 早期利用費
        if ($sumArr[self::SUMID_EARLYUSE]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSE];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 早期利用費の消費税
        if ($sumArr[self::SUMID_EARLYUSEZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSEZEI];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 掛金残金
        if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZAN];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 掛金残金の消費税
        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZANZEI];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 15; $index++) {
            $row_arr[] = null;
        }

        // 御見積金額
        if (isset($sumArr['total'])) {
            $value = $sumArr['total'];
            $row_arr[] = array('', $value['sum']);
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
    }
	
    /**
     * 見積書（法事）
     * @since 2015/05/20
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $issue_date	発行日
     * @param string $seko_no	施行番号
     * @return type
     */
    private function outData_houi($pdfObj, $db, $issue_date, $seko_no, $data_kbn, $seko_no_sub, $print_kbn, $isMituKakutei = false) {

        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数

        $recWk = DataMapper_Pdf0113::find($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub));
        if (count($recWk) == 0) {
            return;
        }
        $rec = $recWk[0];
        if ($rec['free7_kbn'] == '12') { // 催事
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileSaiji);
        } else {
            //「法事」「その他施工」の場合のみ
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[2]);
        }

        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);

        $recCmn = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
        if (count($recCmn) == 0) {
            return;
        }

        // 軽減税率対応  事業者登録番号  2019/04/30 sugiyama keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ( $toroku_bango ) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x'=>450,'y'=>25,'width'=>125,'height'=>10,'font_size'=>10,'align'=>'R'), $bango_str);
            }
        }

        // 社判
        $pdfObj->syaban_out($db, 1, 405, 40, 60);
        $logo = null;
        if (isset($recCmn[0]['syaban4_img'])) {
            $logo = $db->readBlobCont($recCmn[0]['syaban4_img']);
        }
        $kaisyalogo = array(
            'logo' => $logo,
            'nm' => '',
            'bumon_nm' => $recCmn[0]['bumon_lnm'],
            'bumon_tel' => $recCmn[0]['tel']
        );
        
        // 葬家名
        $pdfObj->write_string(array('x' => 120, 'y' => 11, 'width' => 120, 'height' => 35, 'font_size' => 20, 'align' => 'R'), $rec['souke_nm']);
        
        // 施行No.
        $pdfObj->write_string(array('x' => 65, 'y' => 38, 'width' => 120, 'height' => 24, 'font_size' => 10), $seko_no);
        
        // 担当者
        $pdfObj->write_string(array('x' => 462, 'y' => 111, 'width' => 180, 'height' => 27, 'font_size' => 10), $rec['tanto_nm']);

        // 請求先
        $sekyu = App_Utils::getSekoSekyuInfo($seko_no);
        $sekyu_nm = '';
        if (isset($sekyu)) {
            // お客様名
            $pdfObj->write_string(array('x' => 115, 'y' => 140, 'width' => 230, 'height' => 27, 'font_size' => 10), $sekyu['sekyu_nm'] . ' 様');
            // お客様住所
            $pdfObj->write_string(array('x' => 115, 'y' => 162, 'width' => 230, 'height' => 27, 'font_size' => 10), $sekyu['addr1'] . $sekyu['addr2']);
            // 電話番号
            $s_tel = $sekyu['tel'];
            if (empty($s_tel)) {
                $s_tel = $sekyu['mobile_tel'];
            }
            $pdfObj->write_string(array('x' => 435, 'y' => 140, 'width' => 150, 'height' => 27), $s_tel);
            $sekyu_nm = $rec['souke_nm'];
        }
        // ご利用場所
        $pdfObj->write_string(array('x' => 115, 'y' => 185, 'width' => 240, 'height' => 27), $rec['basho_nm']);
        // ご会食場
        $pdfObj->write_string(array('x' => 115, 'y' => 207, 'width' => 240, 'height' => 27), $rec['kaishokujo_nm']);
        // 申込区分
        $pdfObj->write_string(array('x' => 435, 'y' => 162, 'width' => 150, 'height' => 27), $rec['sk_houyo_nm']);
        // ご利用日付
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 435, 'y' => 207, 'width' => 150, 'height' => 27), $rec['riyo_date'], 'Y年m月d日');
        // ご納品日付
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 435, 'y' => 185, 'width' => 150, 'height' => 27), $rec['nohin_date'], 'Y年m月d日');
        
        if ($rec['free7_kbn'] == '12') { // 催事
            // 件名
            $pdfObj->write_string(array('x' => 115, 'y' => 110, 'width' => 230, 'height' => 27, 'font_size' => 10), $rec['v_free10']);
            if (isset($sekyu)) {
                // 備考
                $pdfObj->write_string(array('x' => 115, 'y' => 230, 'width' => 460, 'height' => 27, 'font_size' => 10), $sekyu['biko1']);
            }
        } else {
            // ご宗旨
            $syuha_nm = $rec['syuha_nm'];
            if (empty($syuha_nm)) {
                $syuha_nm = $rec['syushi_nm'];
            }
            $pdfObj->write_string(array('x' => 115, 'y' => 230, 'width' => 240, 'height' => 27), $syuha_nm);
            // 寺院名
            $pdfObj->write_string(array('x' => 435, 'y' => 230, 'width' => 150, 'height' => 27), $rec['jyusho_nm']);
        }
        // 担当者印鑑
        if (isset($rec['inkan_img'])) {
            $img = $db->readBlobCont($rec['inkan_img']);
            $pdfObj->seal_out(483, 737, $img);
        }
        $pdfObj->write_multi(
                array(
                    // 受付部門
                    array('type' => 'string', 'x' => 435, 'y' => 782.5, 'width' => 115, 'height' => 20, 'font_size' => 10, 'value' => $recCmn[0]['bumon_lnm']),
                    // 発行日
                    array('type' => 'ymd', 'x' => 435, 'y' => 801, 'width' => 115, 'height' => 20, 'font_size' => 10, 'value' => $issue_date, 'format' => 'Y年m月d日')
                )
        );

        // 会社情報会員名取得する 互助会の文字列を置き換え
        $kain_nm = App_Utils::getKainNm();
        self::$sum_name[self::SUMID_A_GOJOKAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAI]);
        self::$sum_name[self::SUMID_A_GOJOKAINEBIKI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        self::$sum_name[self::SUMID_A_GOJOKAIGAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAIGAI]);
        self::$sum_name[self::SUMID_GOJOHARAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_GOJOHARAI]);
        self::$msi_name_gojokai = str_replace('互助会', $kain_nm, self::$msi_name_gojokai);
        self::$msi_name_gojokaitokuten = str_replace('互助会', $kain_nm, self::$msi_name_gojokaitokuten);

        $pages = $pdfObj->getNumPages();
        // 明細書を集計
	$sumArr = $this->Sum_houi($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $rec['kain_no'], $print_kbn, $isMituKakutei);
        // 表紙の金額
	if ($isMituKakutei) {
            $pdfObj->setPage($pages);
	} else {
            $pdfObj->setPage(1);
	}
        $kingaku = App_MitsuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn, $this->_history_no);
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_MitsuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_MitsuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_MitsuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_MitsuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_MitsuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_MitsuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        if (is_null($this->_history_no)) {
            $sum = App_Utils::getMitsuKingaku($seko_no, $seko_no_sub, $data_kbn);
            // 軽減税率対応のためデータ設定  2019/04/30 sugiyama keigen
            $this->_p_tgt_type    = 'normal';
            $this->_p_seko_no     = $seko_no;
            $this->_p_seko_no_sub = $seko_no_sub;
            $this->_p_data_kbn    = $data_kbn;
            $this->_p_history_no  = null;
        } else {
            $sum = App_Utils::getMitsuKingakuFromHistory($seko_no, $seko_no_sub, $data_kbn, $this->_history_no);
            // 軽減税率対応のためデータ設定  2019/04/30 sugiyama keigen
            $this->_p_tgt_type    = 'history';
            $this->_p_seko_no     = $seko_no;
            $this->_p_seko_no_sub = $seko_no_sub;
            $this->_p_data_kbn    = $data_kbn;
            $this->_p_history_no  = $this->_history_no;
        }

        // 内訳の各項目を出力
        $this->outDataFaceSum_houji($pdfObj, $sumArr, $rec['gojokai_cose_nm']);
            
        // 御見積金額の合計
        $pdfObj->write_num(array('x' => 440, 'y' => 682, 'width' => 130, 'height' => 30, 'font_size' => 15), $sum);
        
        // 明細書
	$this->outDataDetail_houi($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $rec['kain_no'], $print_kbn, $isMituKakutei);

        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            // 確認用のグリッドを出力
//            $pdfObj->test_line_out(600, 1000);

            if ((!$isMituKakutei)) { // 見積書
                $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' => 15, 'align' => 'C'), ($page) . '/' . ($numPages));
            } else { // 施行申込書
                $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' => 15, 'align' => 'C'), ($page + 1 - $pages) . '/' . ($numPages + 1 - $pages));
            }

            // 明細書
            if ((!$isMituKakutei && $page > 1) || ($isMituKakutei && $page > $pages)) {
                // 請求先名
                $pdfObj->write_string(array('x' => 160, 'y' => 14, 'width' => 130, 'height' => 20, 'font_size' => 20, 'align' => 'C'), $sekyu_nm);

                // 施行No.
                $pdfObj->write_string(array('x' => 65, 'y' => 46, 'width' => 120, 'height' => 24, 'font_size' => 10), $seko_no);
                
                // 会社ロゴ
                $pdfObj->kaisyalogo_ary_out(400, 25, $kaisyalogo, 190, 140, 14, 12, 16);

                // 受付部門
                $pdfObj->write_string(array('x' => 467, 'y' => 786, 'width' => 115, 'height' => 10, 'font_size' => 10), $recCmn[0]['bumon_lnm']);
                
                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' => 467, 'y' => 801, 'width' => 115, 'height' => 10, 'font_size' => 10), $issue_date, 'Y年m月d日');

                // 軽減税率対応 凡例   2019/04/30 sugiyama keigen
                if ( $this->_isKeigenAppliedCtxt() ) { // &&
                    // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                    $pdfObj->write_string(array('x'=>45, 'y'=>786, 'width'=>180, 'height'=>9, 'font_size'=>9, 'align' => 'L'),
                                      '＊は軽減税率対象');
                }
            }
        }
    }
	
    /**
     * 明細を集計（法事）
     * 
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param string $seko_no_sub　施行番号枝番
     * @param string $kain_no	会員番号
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @return array    明細金額を集計した配列
     */
    private function Sum_houi($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $kain_no, $print_kbn, $isMituKakutei) {
 
        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);

        $breakKey = '';
        $chu_bunrui_nm = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $total_tax = 0;
        $itemAry = array();
        $beforeRow = null;

        // 【A】葬送儀礼費用基本プランの項目
        $kainNo = $this->getKainNo($db, $seko_no, 1);
        if (count($kainNo) > 0) {
            $itemAry[self::DAI_BUNRUI_1H][] = self::$msi_name_gojokai;
            $i = 0;
            while ($i < count($kainNo)) {
                $itemAry[self::DAI_BUNRUI_1H][] = '(' . $kainNo[$i]['kain_no'] . ')';
                $i++;
            }
        }
        $recMsi = DataMapper_Pdf0113::findMsi($db
					    , array("seko_no" => $seko_no
						  , "data_kbn" => $data_kbn
						  , "seko_no_sub" => $seko_no_sub
						  , '__etc_orderby' => array('dai_bunrui_cd ASC'
									    ,'chu_bunrui_cd ASC'
									    ,'mitumori_print_seq ASC'
									    ,'disp_no ASC'
									    )
						    )
					    , $print_kbn
				    );
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['chu_bunrui_cd']) {
				// 合計
				$sumArr[$breakKey]['name']	= $chu_bunrui_nm;
				$sumArr[$breakKey]['sum']	= $total;
				$sumArr[$breakKey]['sum_i'] = $total_ippan;
				$sumArr[$breakKey]['sum_t'] = $total_tokuten;
				$sumArr[$breakKey]['sum_k'] = $total_kyoka;
                                $sumArr[$breakKey]['tax'] = $total_tax;

                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];
                $total			= 0;
                $total_ippan	= 0;
                $total_tokuten	= 0;
                $total_kyoka	= 0;
                $total_tax	= 0;
            }
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $gojokai_nebiki_prc + $nebiki_prc; // 請求金額
            $total_tax += $value['out_zei_prc'];
            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }

            // 法事
            if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_1H) {
                $sumArr[self::SUMID_A_PLAN]['sum']			 += $value['juchu_prc']; // 基本費用
                $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum']	 += $value['gojokai_nebiki_prc']; // 付帯特典
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum']	 += $value['nebiki_prc']; // 割引金額
            }
            $total_ippan += $value['juchu_prc'];
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $beforeRow = $value;
        }

        if (count($recMsi) > 0) {
            // 合計
            $rec = $recMsi[count($recMsi) - 1];
            $sumArr[$rec['chu_bunrui_cd']]['name']	= $rec['chu_bunrui_nm'];
            $sumArr[$rec['chu_bunrui_cd']]['sum']	= $total;
            $sumArr[$rec['chu_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['chu_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['chu_bunrui_cd']]['sum_k'] = $total_kyoka;
            $sumArr[$rec['chu_bunrui_cd']]['tax']   = $total_tax;
        }

        return $sumArr;
    }
	
    /**
     * 明細を出力（法事）
     * 
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
     * @param string $seko_no	施行番号
     * @param string $data_kbn	データ区分
     * @param string $seko_no_sub　施行番号枝番
     * @param string $kain_no	会員番号
     * @param string $print_kbn	印刷区分
     * @param boolean $isMituKakutei	見積確定
     * @return void
     */
    private function outDataDetail_houi($pdfObj, $db, $seko_no, $data_kbn, $seko_no_sub, $kain_no, $print_kbn, $isMituKakutei) {
        static $meisai_top = 92;
        static $meisai_row_height = 20.3;
        static $meisai_row_count = 34;
		
        $fileName = self::$sourceFileName[3];
// $isMituKakuteiがtrueとなるケースがないので一旦コメントアウト　2016.08.30
//        if ($isMituKakutei) {
//            $fileName = self::$sourceFileName_s[0];
//        }

        $set_arr[] = array('x' =>  35, 'y' => $meisai_top, 'width' => 190, 'height' => 15); // 品名
        $set_arr[] = array('x' => 225, 'y' => $meisai_top, 'width' => 50,  'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 275, 'y' => $meisai_top, 'width' => 65,  'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 345, 'y' => $meisai_top, 'width' => 80,  'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 425, 'y' => $meisai_top, 'width' => 65,  'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 490, 'y' => $meisai_top, 'width' => 85, 'height' => 15, 'type' => 'num'); // 請求金額
        $set_arr[] = array('x' => 577, 'y' => $meisai_top, 'width' => 20, 'height' => 15); // 軽減税率対象マーク sugiyama keigen

        $breakKey = '';
        $chu_bunrui_nm = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $row_arr = array();
        $beforeRow = null;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki = 0;// 項目ごとの値引き小計

        $recMsi = DataMapper_Pdf0113::findMsi($db
					    , array("seko_no" => $seko_no
						  , "data_kbn" => $data_kbn
						  , "seko_no_sub" => $seko_no_sub
						  , '__etc_orderby' => array('chu_bunrui_cd ASC'
									    ,'dai_bunrui_cd ASC'
									    ,'mitumori_print_seq ASC'
									    ,'disp_no ASC'
									    )
						    )
					    , $print_kbn
				    );
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['chu_bunrui_cd']) {
                
                if (count($row_arr) > 1) {
                    if ($shokei_nebiki == 0) {
                        $shokei_nebiki = null;
                    }
                    // 合計
                    $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
                    $sumArr[$breakKey]['name'] = $chu_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                }
                // 項目
                $row_arr[] = array('◆' . $value['chu_bunrui_nm'] . '◆', null, null, null, null, null);

                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];
                $total			= 0;
                $total_ippan	= 0;
                $total_tokuten	= 0;
                $total_kyoka	= 0;
                
                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki = 0;// 項目ごとの値引き小計
            }
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['juchu_prc'] + $gojokai_nebiki_prc + $nebiki_prc; // 請求金額
            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }
            $nebiki_sum = $gojokai_nebiki_prc + $nebiki_prc;
            if ($nebiki_sum == 0) {
                $nebiki_sum = null;
            }
            $shohin_nm = '　' . $value['shohin_nm'];

            // 軽減税率対応  2019/04/30 sugiyama keigen 
            $value['reduced_tax_rate_txt'] = '';
            if ( $this->_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['reduced_tax_rate_txt'] = '＊';
                }
            }
            $row_arr[] = array($shohin_nm
				, $value['juchu_suryo']
				, $value['juchu_tnk']
				, $value['juchu_prc']
				, $nebiki_sum
                , $seikyu_prc
                , $value['reduced_tax_rate_txt'] ); // 2019/04/30 sugiyama added keigen

            $total_ippan += $value['juchu_prc'];
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $shokei_ippan += $value['juchu_prc']; // 項目ごとの金額小計
            $shokei_nebiki += $gojokai_nebiki_prc + $nebiki_prc; // 項目ごとの値引き小計
            $beforeRow = $value;
        }

        if (count($recMsi) > 0) {
            if ($shokei_nebiki == 0) {
                $shokei_nebiki = null;
            }
            // 合計
            $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
        }
        
        $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr, $meisai_row_count, __DIR__ . '/' . $fileName);

        return;
    }
    
    const CHU_BUNRUI_CD_H_A = '1000'; // 事後
    const CHU_BUNRUI_CD_H_B = '1010'; // 位牌
    const CHU_BUNRUI_CD_H_C = '1020'; // 仏壇
    const CHU_BUNRUI_CD_H_D = '1030'; // 墓所・手元供養
    const CHU_BUNRUI_CD_H_E = '1040'; // 案内状
    const CHU_BUNRUI_CD_H_F = '1050'; // 祭壇・式場
    const CHU_BUNRUI_CD_H_G = '1070'; // 法要
    const CHU_BUNRUI_CD_H_H = '1080'; // 墓参
    const CHU_BUNRUI_CD_H_I = '1090'; // 返礼品
    const CHU_BUNRUI_CD_H_J = '1110'; // 法事・催事
    const CHU_BUNRUI_CD_H_K = '1120'; // 会食
    const CHU_BUNRUI_CD_H_L = '1130'; // 非課税
    const CHU_BUNRUI_CD_H_M = '1140'; // 別途費用 
    const CHU_BUNRUI_CD_H_N = '1150'; // 値引き
    
    private static $houyou = array( //法要・墓参り費用
        self::CHU_BUNRUI_CD_H_A,
        self::CHU_BUNRUI_CD_H_B,
        self::CHU_BUNRUI_CD_H_C,
        self::CHU_BUNRUI_CD_H_D,
        self::CHU_BUNRUI_CD_H_E,
        self::CHU_BUNRUI_CD_H_F,
        self::CHU_BUNRUI_CD_H_G,
        self::CHU_BUNRUI_CD_H_H,
        self::CHU_BUNRUI_CD_H_J
    );
    
    private static $omote = array( //おもてなし費用
        self::CHU_BUNRUI_CD_H_I,
        self::CHU_BUNRUI_CD_H_K
    );
    
    private static $sonota = array( //その他費用
        self::CHU_BUNRUI_CD_H_M,
        self::CHU_BUNRUI_CD_H_N
    );
    
    /**
     * 表紙の金額を出力（法事)
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    private function outDataFaceSum_houji($pdfObj, $sumArr, $coseNm) {
        $row_height = 20.2;
        $set_arr[] = array('x' => 30,  'width' => 65,  'height' => 27, 'align' => 'C');   // 項目
        $set_arr[] = array('x' => 90,  'width' => 100, 'height' => 27, 'type'  => 'num'); // 金額
        $set_arr[] = array('x' => 156, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr[] = array('x' => 256, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 御見積金額
        $set_arr[] = array('x' => 350, 'width' => 90,  'height' => 27, 'type'  => 'num'); // 消費税
        $set_arr[] = array('x' => 450, 'width' => 135, 'height' => 27); // 備考
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 307;
        }

        $hoyo_sum    = 0;	    // 法要・墓参り費用合計
        $zei_nuki_nebiki_sum  = 0;  // 値引き合計(税抜項目)
	$zei_komi_nebiki_sum  = 0;  // 値引き合計(税込項目)
        $omote_sum   = 0;	    // おもてなし費用合計
        $sonota_sum  = null;	    // その他費用合計
        $hikazei     = 0;	    // 非課税
        $azukari_dan = 0;	    // 預金(壇払い)
        $azukari_hen = 0;	    // 預金(返礼品)
        $row_arr = null;
        
        //法要・墓参り中分類費用
        foreach (self::$houyou as $key){ 
            if(isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0 ){
               $hoyo_sum += $sumArr[$key]['sum_i'];
               $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
               $zei_nuki_nebiki_sum += $nebiki;
               $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                , $sumArr[$key]['sum_i'] // 金額
                                , $nebiki                // 値引き額
                                , $sumArr[$key]['sum']   // 見積金額
                                , null			 // 消費税
                                , null                   // 備考
                            );
            }
        }
        
        //おもてなし中分類費用
        foreach (self::$omote as $key){ 
            if(isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0 ){
               $omote_sum += $sumArr[$key]['sum_i'];
               $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
               $zei_nuki_nebiki_sum += $nebiki;
               $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                , $sumArr[$key]['sum_i'] // 金額
                                , $nebiki                // 値引き額
                                , $sumArr[$key]['sum']   // 見積金額
                                , null			 // 消費税
                                , null                   // 備考
                            );
            }
        }
         
        //その他中分類費用
        foreach (self::$sonota as $key){ 
            if(isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0){
               $sonota_sum += $sumArr[$key]['sum_i'];
               $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
               $zei_nuki_nebiki_sum += $nebiki;
               $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                , $sumArr[$key]['sum_i'] // 金額
                                , $nebiki                // 値引き額
                                , $sumArr[$key]['sum']   // 見積金額
                                , null			 // 消費税
                                , null                   // 備考
                            );
            }
        }

        // 税込項目
        if (isset($sumArr[self::CHU_BUNRUI_CD_H_L])) {
            $value = $sumArr[self::CHU_BUNRUI_CD_H_L];
	    $nebiki = $value['sum_t'] == 0 ? null : $value['sum_t'];
	    $zei_komi_nebiki_sum += $nebiki;
            $row_arr[] = array($value['name']
                             , $value['sum_i']	// 金額
                             , $nebiki		// 値引き額
                             , $value['sum']	// 見積金額
                             , null		// 消費税
                             , null		// 備考
                         );
            $hikazei = $value['sum_i'];
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_dan = $value['sum'];
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_hen = $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 15; $index++) {
            $row_arr[] = null;
        }
	
	$zei_nuki_sum = $hoyo_sum + $omote_sum + $sonota_sum + $azukari_dan + $azukari_hen;
	$zei_komi_sum = $hikazei;
        $kingaku_sum  = $zei_nuki_sum + $zei_komi_sum;
	$nebiki_sum   = $zei_nuki_nebiki_sum + $zei_komi_nebiki_sum;
        $zei = null;
        if (isset($sumArr['tax'])) {
            $zei = $sumArr['tax']['sum'];
        }
	
	// 税抜項目小計
        $row_arr[] = array(null
                         , $zei_nuki_sum > 0 ? $zei_nuki_sum : ''						// 金額
                         , $zei_nuki_nebiki_sum < 0 ? $zei_nuki_nebiki_sum : ''					// 値引き額
                         , $zei_nuki_sum + $zei_nuki_nebiki_sum > 0 ? $zei_nuki_sum + $zei_nuki_nebiki_sum : '' // 見積金額
                         , $zei	> 0 ? $zei : ''									// 消費税
                         , null											// 備考
                     );
	
	// 税込項目小計
        $row_arr[] = array(null
                         , $zei_komi_sum > 0 ? $zei_komi_sum : ''						  // 金額
                         , $zei_komi_nebiki_sum < 0 ? $zei_komi_nebiki_sum : ''					  // 値引き額
                         , $zei_komi_sum + $zei_komi_nebiki_sum > 0 ? $zei_komi_sum + $zei_komi_nebiki_sum : ''   // 見積金額
                         , null											  // 消費税
                         , null											  // 備考
                     );

        // 合計
        $row_arr[] = array(''
			, $kingaku_sum > 0 ? $kingaku_sum : ''				    // 金額
			, $nebiki_sum < 0 ? $nebiki_sum : ''				    // 値引き額
			, $kingaku_sum + $nebiki_sum > 0 ? $kingaku_sum + $nebiki_sum : ''  // 見積金額
			, $zei > 0 ? $zei : ''						    // 消費税
			, null								    // 備考
		    );
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 消費税金額の内訳出力     2019/04/30 sugiyama keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake($pdfObj, 2); // 2:法事
        }
    }

    /**
     *
     * 見積表紙に会員情報を出力する
     *
     * <AUTHOR> Sai
     * @since 2015/12/17
     * @param $db
     * @param $pdfObj
     * @param $seko_no
     * @return 
     */
    private function outKaiinInfo($db, $pdfObj, $seko_no) {
        $y = 172;
        // 会員情報出力処理
        $kainNo = $this->getKainNo($db, $seko_no, 1);
        foreach ($kainNo as $key => $rec) {
            if ($key > 6) { // 7行まで印字
                break;
            }
            $pdfObj->write_string(array('x' => 437, 'y' => $y, 'width' => 135, 'height' => 16,), trim($rec['kain_no']) . " " . $rec['apply_no'] . " " . $rec['course_snm_cd'] . " " . $rec['kanyu_nm']);
            $y += 15.8;
        }
    }
    
    /**
     * PDFのファイル名を施行No + 葬家名 + タイトル 形式で取得する
     * 
     * <AUTHOR> Matsuyama
     * @since 2016/09/06
     * @return string   PDFのファイル名
     */
    public function getFileName($db, $seko_no, $title) {
        $file_name = $title;
        $rec = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$seko_no) );
        if (count($rec) > 0) {
//            $file_name = $seko_no.$rec['souke_nm'].'家'.$title;

//            $soke_nm = $rec['souke_nm'];
//            $soke_nm = str_replace("/","／",$soke_nm);
//            $soke_nm = str_replace("\\","￥",$soke_nm);
//            $soke_nm = str_replace("'","’",$soke_nm);
//            $soke_nm = str_replace("\"","”",$soke_nm);
//            $file_name = $seko_no.$soke_nm.'家'.$title;

			//葬家名の特殊文字対応
//            $file_name = htmlspecialchars($seko_no.$rec['souke_nm'].'家'.$title);
			$file_name = Msi_Sys_Utils::normFilename($seko_no.$rec['souke_nm'].'家'.$title);
        }
        return $file_name;
    }

    /**
     * 消費税金額の内訳を出力する
     *
     * <AUTHOR> sugiyama
     * @since 2019/04/30
     * @param App_Pdf  $pdfObj
     * @param integer  $type     1:葬儀, 2:法事
     * @return void
     */
    protected function outZeiUchiwake($pdfObj, $type=1)
    {
        $tgt_type    = $this->_p_tgt_type;
        $seko_no     = $this->_p_seko_no;
        $seko_no_sub = $this->_p_seko_no_sub;
        $data_kbn    = $this->_p_data_kbn;
        $history_no  = $this->_p_history_no;

        $db = Msi_Sys_DbManager::getMyDb();

        if (is_null($history_no)) {
            $shohizei = App_KeigenUtils::getMitsuShohizeiEasy($db, $seko_no, $seko_no_sub, $data_kbn);
        } else {
            $shohizei = App_KeigenUtils::getMitsuShohizeiFromHistoryEasy($db, $seko_no, $seko_no_sub, $data_kbn, $history_no);
        }

        $aUchiwake = $shohizei;

        if ( $type == 2 ) { // 法事
            $optAttr = array( 'left'  => 447.5,   // X
                              'top'   => 310,     // Y
                              'width' => 130,
                              'height' => 20.2 );
        } else { // 葬儀
            $optAttr = array( 'left'  => 440,     // X
                              'top'   => 550.5,   // Y
                              'width' => 132,
                              'height' => 15.8 );
        }

        App_KeigenPdfUtils::outZeiUchiwake01($pdfObj, '合計金額の内訳', $aUchiwake, $optAttr);

        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    /**
     * 消費税基準日(sougi_ymd)を設定する
     * juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     *
     * <AUTHOR> sugiyama
     * @since 2019/04/30
     * @param $pdfObj
     * @return void
     */
    protected function _prepKijunYmdKeigen($seko_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );

        $this->_sougi_ymd = $sougi_ymd; // null もあり得る
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> sugiyama
     * @since 2019/04/30
     * @return boolean
     */
    protected function _isKeigenAppliedCtxt()
    {
        $sougi_ymd = $this->_sougi_ymd;
        if ( $sougi_ymd == null ) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ( $keigenBgnYmd <= $sougi_ymd ) {
            return true;
        }
        return false;
    }

}
