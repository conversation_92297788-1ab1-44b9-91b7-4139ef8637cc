<?php
  /**
   * DataMapper_MstrKamokuMst
   *
   * 科目マスタ データマッパー
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> m.<PERSON>ashi
   * @since      2014/02/25
   * @filesource
   */

require_once dirname(__FILE__).'/Mstr/Abstract.php';
  /**
   * 科目マスタ データマッパー
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> m.Hayashi
   * @since      2014/02/25
   */
class DataMapper_MstrKamokuMst  extends DataMapper_Mstr_Abstract
{
    // table情報
    protected $tables = array(
        'kamoku_mst' => array(
            'logicalName' => '科目マスタ',
            'primary'     => array('kamoku_cd'),
            'denyUpdate'  => array(),
        ),
    );
    // デフォルト順
    protected $order = array(
        'kamoku_cd'    => 'asc',
    );
    // modelフィールド
    /*
    CREATE TABLE public.KAMOKU_MST (
        kamoku_kbn      NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 科目区分
        nyukin_sbt      NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 入金種別
        kamoku_cd       VARCHAR(12)            NOT NULL, -- 科目コード
        kamoku_nm       VARCHAR(30)                    , -- 科目名
        toriatukai_kbn  NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 取扱区分
        taishaku_kbn    NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 貸借区分
        zei_cd_inp_kbn  NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 税コード入力区分
        bumon_inp_kbn   NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 部門入力区分
        seikyu_inp_kbn  NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 請求書№入力区分
        kaikei_bumon_cd CHAR(6)                        , -- 会計部門コード
        delete_flg      NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 削除フラグ
        _req_id         BIGINT                 NOT NULL, -- 
        _cre_user       VARCHAR(64)            NOT NULL, -- 
        _cre_ts         TIMESTAMPTZ            NOT NULL, -- 
        _mod_user       VARCHAR(64)            NOT NULL, -- 
        _mod_ts         TIMESTAMPTZ            NOT NULL, -- 
        _mod_cnt        BIGINT       DEFAULT 0 NOT NULL, -- 

        PRIMARY KEY ( kamoku_kbn,nyukin_sbt,kamoku_cd ) USING INDEX TABLESPACE pg_default
    );
     */
    protected $fields = array(
        'kamoku_kbn'      => array(   // NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 科目区分 [1:売上科目,2:仕入科目,3:入金科目,4:立替金科目]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '科目区分',
            'preset'        => 'kbn2',
            'defs'          => 0,
        ),
        'nyukin_sbt'      => array(   // NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 入金種別 [1:お預かり金,2:払込済互助会掛金,3:互助会前納割引,5:ｸﾚｼﾞｯﾄ,7:値引き,8:その他,9:清算]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '入金種別',
            'preset'        => 'kbn2',
            'defs'          => 0,
        ),
        'kamoku_cd'       => array(   // VARCHAR(12) NOT NULL, -- 科目コード
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '科目コード',
            'required'      => true,
            'preset'        => 'fcode12',
        ),
        'kamoku_nm'       => array(   // VARCHAR(30), -- 科目名
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '科目名',
            'preset'        => 'name30',
        ),
        'toriatukai_kbn'  => array(   // NUMERIC(2,0) DEFAULT 0 NOT NULL, -- 取扱区分
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '取扱区分',
            'preset'        => 'kbn2',
            'defs'          => 0,
        ),
        'taishaku_kbn'    => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 貸借区分 [0:借方発生,1:貸方発生]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '貸借区分',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
        'zei_cd_inp_kbn'    => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 税コード入力区分 [0:入力あり,1:入力あり]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '税コード入力区分',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
        'bumon_inp_kbn'    => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 部門入力区分 [0:入力あり,1:入力あり]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '部門入力区分',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
        'seikyu_inp_kbn' => array(    // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 請求書№入力区分
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '請求書№入力区分',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
        'kaikei_bumon_cd' => array(    // CHAR(6), -- 会計部門コード
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '会計部門コード',
            'preset'        => 'fcode6',
        ),
        'delete_flg'      => array(   // NUMERIC(1,0) DEFAULT 0 NOT NULL, -- 削除フラグ [0:有効ﾃﾞｰﾀ,1:削除ﾃﾞｰﾀ]
            'tables'        => array('kamoku_mst'),
            'logicalName'   => '削除フラグ',
            'preset'        => 'kbn1',
            'defs'          => 0,
        ),
    );
}
