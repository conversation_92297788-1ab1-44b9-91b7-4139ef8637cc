<?php
  /**
   * DataMapper_Pdf0101
   *
   * PDF出力 御葬儀打合せ事項 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/02/17
   * @filesource 
   */

  /**
   * PDF出力 御葬儀打合せ事項 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Sato
   * @since      2014/02/17
   */
class DataMapper_Pdf0101 extends DataMapper_Abstract
{
    /**
     * PDF出力 御葬儀打合せ事項 データ 取得
     *
     * <AUTHOR> Sato
     * @since      2014/02/17
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT  s.seko_no
       ,s.uketuke_tanto_cd
       ,s.seko_tanto_cd
       ,s.kj_kbn
       ,s.kh_kbn
       ,daicho_no_eria
       ,daicho_no_mm
       ,daicho_no_seq
       ,CASE WHEN kaiin_sonota IS NULL THEN 
            NULL 
        ELSE
            '（' || kaiin_sonota || '）' 
        END                                 AS kaiin_sonota
       ,souke_nm
       ,souke_tel
       ,s.biko1
       ,s.biko2
       ,k_nm
       ,k_knm
       ,k_gengo
       ,k_seinengappi_ymd
       ,k_nenrei_man
       ,k_nenrei_kyounen
       ,k_kaimyo			-- 俗名     	2016/10/27 ADD Kobayashi
       ,kg_yubin_no
       ,kg_addr1
       ,kg_addr2
       ,kg_tel
       ,kj_yubin_no
       ,kj_addr1
       ,kj_addr2
       ,kj_tel
       ,kh_yubin_no
       ,kh_addr1
       ,kh_addr2
       ,CASE kk_kinmusaki_kbn WHEN 1 THEN 
           '元' 
         WHEN 2 THEN 
           '現' 
         END                            AS kk_kinmusaki_kbn
       ,kk_kinmusaki_nm
       ,kk_yakusyoku_nm
       ,kk_tel
       ,kk_fax
       ,m_nm
       ,m_knm
       ,m_gengo
       ,m_seinengappi_ymd
       ,m_nenrei_man
       ,mg_kbn
       ,mg_yubin_no
       ,mg_addr1
       ,mg_addr2
       ,mg_tel
       ,mg_m_tel			-- 携帯番号	2016/03/27 ADD Kayo
       ,mj_kbn
,kj_kbn -- 2016/12/15 add mihara
,kh_kbn -- 2016/12/15 add mihara
       ,mj_yubin_no
       ,mj_addr1
       ,mj_addr2
       ,mj_tel
       ,mh_kbn
       ,mh_yubin_no
       ,mh_addr1
       ,mh_addr2
       ,CASE mk_kinmusaki_kbn WHEN 1 THEN 
           '元' 
        WHEN 2 THEN 
          '現'
        END                         AS mk_kinmusaki_kbn
       ,mk_kinmusaki_nm
       ,mk_yakusyoku_nm
       ,mk_tel
       ,mk_fax
       ,sekyu_kbn
       ,CASE sd_hakko_kbn WHEN 0 THEN 
                '未発行'
            WHEN 1 THEN 
                '発行済' 
            END                         AS  sd_hakko_kbn
       ,sd_yotei_ymd
       ,CASE sd_step_kbn WHEN 1 THEN 
                '葬家' 
            WHEN 2 THEN 
                '当社'
            WHEN 3 THEN 
                '隣組' 
            END                         AS sd_step_kbn
       ,sd_copy_cnt
       ,CASE hs_kbn WHEN 0 THEN 
                '未搬送' 
           WHEN 1 THEN 
               '搬送済'
            END                         AS hs_kbn
       ,COALESCE(hs_spot_nm, hs_gyomu.kbn_value_lnm)    AS  hs_spot_nm
       ,COALESCE(hs_anchi_nm, hs_gyomu2.kbn_value_lnm)  AS  hs_anchi_nm
       ,kasoba_nm
       ,az_death_cnt
       ,az_inkan_kbn
       ,az_photo_cnt
       ,az_gojokai_nm
       ,tk_cyonaikai_nm
       ,tk_kumicyo_nm
       ,tk_house_cnt
       ,tk_person_cnt
       ,kaiin.kbn_value_lnm     AS kaiin_nm
       ,keishiki.kbn_value_lnm  AS keishiki_nm
       ,syushi.kbn_value_lnm    AS syushi_nm
       ,s.syushi_cd
       ,syuha.kbn_value_lnm     AS syuha_nm
       ,jiin.jyusho_lnm         AS jiin_nm
       ,k_sex.kbn_value_lnm     AS k_sex_nm
       ,kg_setai.kbn_value_lnm  AS kg_setai_nm
       ,kj_setai.kbn_value_lnm  AS kj_setai_nm
       ,kh_hito.kbn_value_lnm   AS kh_hito_nm
       ,m_zoku.kbn_value_lnm    AS m_zoku_nm
       ,m_zoku2.kbn_value_lnm    AS m_zoku_nm2
       ,m_zoku_nm AS m_zoku_nm_t        	-- 続柄名（手入力）	2016/12/09 ADD Kobayashi
       ,m_zoku_nm2 AS m_zoku_nm2_t        	-- 喪主からみた続柄名（手入力）	2016/12/09 ADD Kobayashi        
       ,sekyu.sekyu_nm
       ,sekyu.sekyu_knm
       ,CASE sekyu.moshu_kankei_kbn WHEN 1 THEN 
                '本人' 
           WHEN 2 THEN 
                '本人以外'
           END                      AS moshu_kankei_kbn
       ,CASE WHEN sekyu.moshu_kankei IS NULL THEN 
                '' 
           ELSE 
               '（' || sekyu.moshu_kankei || '）' 
            END                     AS moshu_kankei
      ,sekyu.yubin_no   AS sekyu_yubin_no
      ,sekyu.addr1      AS sekyu_addr1
      ,sekyu.addr2      AS sekyu_addr2
      ,sekyu.tel        AS sekyu_tel
      ,sekyu.mobile_tel       AS sekyu_mobile_tel -- 請求先 携帯番号 2016/07/19 ADD Kayo           
      ,sekyu.biko1      AS sekyu_biko1
      ,sekyu.biko2      AS sekyu_biko2
      ,syoukai.kbn_value_lnm    AS syoukai_kbn            -- 2016/10/27 ADD Kobayashi 紹介の有無
      ,free.v_free5             AS renraku_tel_ceremo     -- 2016/10/27 ADD Kobayashi セレモ専用
      ,s.v_free11               AS syokai_riyu   
      ,CASE WHEN nti.basho_kbn = 0 THEN
            '自宅'
          ELSE     
            nti.basho_nm
          END                         AS shibo_basho_nm   -- 死亡場所名 2016/06/09 ADD Kayo          
      ,CASE WHEN free.ts_free2 IS NULL AND free.ts_free3 IS NULL AND free.ts_free4 IS NULL THEN 
                '未搬送' 
           ELSE 
               '搬送済'
           END                         AS hs_kbn_sunmenber  -- 2016/06/09 ADD Kayo サンメンバーズ専用
  FROM seko_kihon_info s
  LEFT JOIN code_nm_mst kaiin 
		ON kaiin.code_kbn = '0030' 
		AND s.kaiin_cd = kaiin.kbn_value_cd 
		AND kaiin.delete_flg = 0
  LEFT JOIN seko_nitei nti      -- 2016/06/09 ADD Kayo
       ON s.seko_no = nti.seko_no
       AND 1        = nti.nitei_kbn
       AND 0        = nti.delete_flg  
  LEFT JOIN seko_kihon_all_free free    -- 2016/06/09 ADD Kayo
		ON s.seko_no = free.seko_no 
		AND free.delete_flg = 0              
  LEFT JOIN code_nm_mst keishiki 
		ON keishiki.code_kbn = '0040' 
		AND s.keishiki_cd = keishiki.kbn_value_cd 
		AND keishiki.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
		ON syushi.code_kbn = '0240' 
		AND s.syushi_cd = syushi.kbn_value_cd 
		AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst syuha 
		ON syuha.code_kbn = '0200' 
		AND s.syuha_cd = syuha.kbn_value_cd 
		AND syuha.delete_flg = 0
  LEFT JOIN nm_jyusho_mst jiin 
		ON jiin.jyusho_kbn = 1 
		AND s.jyusho_cd = jiin.jyusho_cd 
		AND jiin.delete_flg = 0
  LEFT JOIN code_nm_mst k_sex 
		ON k_sex.code_kbn = '0080' 
		AND s.k_sex_cd = k_sex.kbn_value_cd 
		AND k_sex.delete_flg = 0
  LEFT JOIN code_nm_mst kg_setai 
		ON kg_setai.code_kbn = '0060' 
		AND s.kg_setai_cd = kg_setai.kbn_value_cd 
		AND kg_setai.delete_flg = 0
  LEFT JOIN code_nm_mst kj_setai 
		ON kj_setai.code_kbn = '0060' 
		AND s.kj_setai_cd = kj_setai.kbn_value_cd 
		AND kj_setai.delete_flg = 0
  LEFT JOIN code_nm_mst kh_hito 
		ON kh_hito.code_kbn = '0050' 
		AND s.kh_hito_cd = kh_hito.kbn_value_cd 
		AND kh_hito.delete_flg = 0
  LEFT JOIN code_nm_mst m_zoku 
		ON m_zoku.code_kbn = '0190' 
		AND s.m_zoku_cd = m_zoku.kbn_value_cd 
		AND m_zoku.delete_flg = 0
  LEFT JOIN code_nm_mst m_zoku2 
		ON m_zoku2.code_kbn = '0190' 
		AND s.m_zoku_cd2 = m_zoku2.kbn_value_cd 
		AND m_zoku2.delete_flg = 0
  LEFT JOIN code_nm_mst hs_gyomu 
		ON hs_gyomu.code_kbn = '0220' 
		AND s.hs_gyomu_cd = hs_gyomu.kbn_value_cd 
		AND hs_gyomu.delete_flg = 0
  LEFT JOIN code_nm_mst hs_gyomu2 
		ON hs_gyomu2.code_kbn = '0600' 
		AND s.hs_gyomu_cd2 = hs_gyomu2.kbn_value_cd 
		AND hs_gyomu2.delete_flg = 0
  LEFT JOIN sekyu_saki_info sekyu 
		ON s.seko_no = sekyu.seko_no 
		AND s.sekyu_cd = sekyu.sekyu_cd 
		AND sekyu.delete_flg = 0
  LEFT JOIN code_nm_mst syoukai 
		ON syoukai.code_kbn = '1830' 
		AND s.free1_code_cd = syoukai.kbn_value_cd 
		AND syoukai.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }
    
    /**
     * PDF出力 御葬儀打合せ事項 データ 取得(彩苑用)
     *
     * <AUTHOR> Sato
     * @since      2014/02/17
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT 
	 s.seko_no
	,u.uri_den_no						-- 売上伝票№	2016/03/27 ADD Kayo
	,bm.bumon_lnm		AS	bumon_nm	-- 正式部門名	2016/03/27 ADD Kayo
	,s.uketuke_tanto_cd					-- プランナー
	,tpm.tanto_nm		AS	p_tanto_nm	-- 担当者名(プランナー)	2016/03/27 ADD Kayo
	,s.seko_tanto_cd					-- コーディネター
	,tcm.tanto_nm		AS	c_tanto_nm	-- 担当者名(コーディネター)	2016/03/27 ADD Kayo
	,s.jichu_kakute_ymd					-- 受注確定日				2016/03/27 ADD Kayo
	,daicho_no_eria
	,daicho_no_mm
	,daicho_no_seq
	,CASE WHEN kaiin_sonota IS NULL THEN 
		NULL 
	 ELSE 
		'（' || kaiin_sonota || '）' 
	 END AS kaiin_sonota
	,souke_nm
	,souke_tel
	,s.biko1
	,s.biko2
	,k_nm
	,k_knm
	,k_gengo
	,k_seinengappi_ymd
	,k_nenrei_man
	,k_nenrei_kyounen
	,kg_yubin_no
	,kg_addr1
	,kg_addr2
	,kg_tel
	,kj_yubin_no
	,kj_addr1
	,kj_addr2
	,kj_tel
	,kh_yubin_no
	,kh_addr1
	,kh_addr2
	,CASE kk_kinmusaki_kbn WHEN 1 THEN 
			'元' 
		WHEN 2 THEN 
			'現' 
		END				AS kk_kinmusaki_kbn
	,kk_kinmusaki_nm
	,kk_yakusyoku_nm
	,kk_tel
	,kk_fax
	,m_nm
	,m_knm
	,m_gengo
	,m_seinengappi_ymd
	,m_nenrei_man
	,mg_kbn
	,mg_yubin_no
	,mg_addr1
	,mg_addr2
	,mg_tel
	,mg_m_tel			-- 携帯番号	2016/03/27 ADD Kayo
	,mj_kbn
,kj_kbn -- 2016/12/15 add mihara
,kh_kbn -- 2016/12/15 add mihara
	,mj_yubin_no
	,mj_addr1
	,mj_addr2
	,mj_tel
	,mh_kbn
	,mh_yubin_no
	,mh_addr1
	,mh_addr2
	,CASE mk_kinmusaki_kbn WHEN 1 THEN 
			'元' 
		WHEN 2 THEN
			'現' 
		END				AS mk_kinmusaki_kbn
	,mk_kinmusaki_nm
	,mk_yakusyoku_nm
	,mk_tel
	,mk_fax
	,sekyu_kbn
	,CASE sd_hakko_kbn WHEN 0 THEN 
			'未発行' 
		WHEN 1 THEN 
			'発行済' 
		END				AS sd_hakko_kbn
	,sd_yotei_ymd
	,CASE sd_hakko_kbn WHEN 0 THEN 
			sd_yotei_ymd 
		WHEN 1 THEN 
			null 
		END				AS sd_yotei_ymd2
	,CASE sd_step_kbn WHEN 1 THEN 
			'葬家' 
		WHEN 2 THEN 
			'当社' 
		WHEN 3 THEN 
			'隣組' 
		END				AS sd_step_kbn
	,sd_copy_cnt
	,CASE hs_kbn WHEN 0 THEN
			'未搬送'
		WHEN 1 THEN 
			'搬送済' 
		END				AS hs_kbn
	,COALESCE(hs_spot_nm, hs_gyomu.kbn_value_lnm)	AS	hs_spot_nm
	,COALESCE(hs_anchi_nm, hs_gyomu2.kbn_value_lnm) AS	hs_anchi_nm
	,kasoba_nm
	,az_death_cnt
	,az_inkan_kbn
	,az_photo_cnt
	,az_gojokai_nm
	,tk_cyonaikai_nm
	,tk_kumicyo_nm
	,tk_house_cnt
	,tk_person_cnt
	,kaiin.kbn_value_lnm	AS kaiin_nm
	,keishiki.kbn_value_lnm AS keishiki_nm
	,syushi.kbn_value_lnm	AS syushi_nm
	,syuha_nm				AS syuha_nm
	,jiin.jyusho_lnm		AS jiin_nm
	,jiin.tel				AS jiin_tel		-- 寺院電話番号	2016/03/27 ADD Kayo		
	,k_sex.kbn_value_lnm	AS k_sex_nm
    ,free.v_free11			AS kg_setai_nm
	,kj_setai.kbn_value_lnm AS kj_setai_nm
	,free.v_free12			AS kh_hito_nm
	,m_zoku.kbn_value_lnm	AS m_zoku_nm
	,sekyu.sekyu_nm
	,sekyu.sekyu_knm
	,CASE sekyu.moshu_kankei_kbn WHEN 1 THEN 
			'本人' 
		WHEN 2 THEN
			'本人以外'
		END					AS moshu_kankei_kbn
	,CASE WHEN sekyu.moshu_kankei IS NULL THEN 
			'' 
		ELSE 
			'（' || sekyu.moshu_kankei || '）' 
		END					AS moshu_kankei
	,sekyu.yubin_no			AS sekyu_yubin_no
	,sekyu.addr1			AS sekyu_addr1
	,sekyu.addr2			AS sekyu_addr2
	,sekyu.tel				AS sekyu_tel
	,sekyu.biko1			AS sekyu_biko1
	,sekyu.biko2			AS sekyu_biko2
	,free.biko1				AS free_biko1
	,s.n_free1
	,s.n_free2
	,s.free2_kbn
    ,free.v_free3			AS byoto_nm					--病棟		2016/03/27 ADD Kayo
    ,free.v_free7			AS todoke_nm				--届出人氏名
    ,free.v_free8			AS todoke_knm				--届出人氏名カナ
    ,t_zoku.kbn_value_lnm	AS todoke_zoku_nm			--届出人続柄
    ,free.free5_code_cd		AS todoke_gengo				--届出人生年月日元号
    ,free.v_free9			AS todoke_seinengappi_ymd   --届出人生年月日
    ,free.i_free3			AS todoke_nenrei			--届出人年齢
    ,free.zip_no2			AS todoke_zip				--届出人現住所郵便番号
    ,free.addr2_1			AS todoke_addr1				--届出人現住所1
    ,free.addr2_2			AS todoke_addr2				--届出人現住所2
    ,free.tel_no2			AS todoke_tel1				--届出人電話番号 
    ,free.as_kbn2										--故人に同じ区分(届出人)
	,free.free_kbn3										--記入シートの火葬場控室（彩苑）
    ,s.v_free6              AS tochoku_tanto1           --斎場当直者1(彩苑)
    ,s.v_free7              AS tochoku_tanto2			--斎場当直者2(彩苑)
  FROM seko_kihon_info s
  LEFT JOIN uriage_denpyo	u			-- 2016/03/27 ADD Kayo
		ON	s.seko_no	=	u.seko_no
		AND	u.data_kbn	IN	(1,2)
		AND u.delete_flg = 0
  LEFT JOIN bumon_mst	bm				-- 2016/03/27 ADD Kayo
		ON	s.bumon_cd	=	bm.bumon_cd
		AND	0			=	bm.delete_flg
  LEFT JOIN tanto_mst	tpm				-- プランナー 2016/03/27 ADD Kayo
		ON	s.uketuke_tanto_cd	=	tpm.tanto_cd
		AND	0					=	tpm.delete_flg
  LEFT JOIN tanto_mst	tcm				-- コーディネーター 2016/03/27 ADD Kayo
		ON	s.seko_tanto_cd		=	tcm.tanto_cd
		AND	0					=	tcm.delete_flg
  LEFT JOIN code_nm_mst kaiin 
		ON kaiin.code_kbn = '0030' 
		AND s.kaiin_cd = kaiin.kbn_value_cd 
		AND kaiin.delete_flg = 0
  LEFT JOIN code_nm_mst keishiki 
		ON keishiki.code_kbn = '0040'
		AND s.keishiki_cd = keishiki.kbn_value_cd 
		AND keishiki.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
		ON syushi.code_kbn = '0240' 
		AND s.syushi_cd = syushi.kbn_value_cd 
		AND syushi.delete_flg = 0
  LEFT JOIN code_nm_mst syuha 
		ON syuha.code_kbn = '0200' 
		AND s.syuha_cd = syuha.kbn_value_cd 
		AND syuha.delete_flg = 0
  LEFT JOIN nm_jyusho_mst jiin 
		ON jiin.jyusho_kbn = 1 
		AND s.jyusho_cd = jiin.jyusho_cd 
		AND jiin.delete_flg = 0
  LEFT JOIN code_nm_mst k_sex 
		ON k_sex.code_kbn = '0080' 
		AND s.k_sex_cd = k_sex.kbn_value_cd 
		AND k_sex.delete_flg = 0
  LEFT JOIN code_nm_mst kg_setai 
		ON kg_setai.code_kbn = '0060' 
		AND s.kg_setai_cd = kg_setai.kbn_value_cd 
		AND kg_setai.delete_flg = 0
  LEFT JOIN code_nm_mst kj_setai 
		ON kj_setai.code_kbn = '0060' 
		AND s.kj_setai_cd = kj_setai.kbn_value_cd 
		AND kj_setai.delete_flg = 0
  LEFT JOIN code_nm_mst kh_hito 
		ON kh_hito.code_kbn = '0050' 
		AND s.kh_hito_cd = kh_hito.kbn_value_cd 
		AND kh_hito.delete_flg = 0
  LEFT JOIN code_nm_mst m_zoku 
		ON m_zoku.code_kbn = '0190' 
		AND s.m_zoku_cd = m_zoku.kbn_value_cd 
		AND m_zoku.delete_flg = 0
  LEFT JOIN code_nm_mst hs_gyomu 
		ON hs_gyomu.code_kbn = '0220' 
		AND s.hs_gyomu_cd = hs_gyomu.kbn_value_cd 
		AND hs_gyomu.delete_flg = 0
  LEFT JOIN code_nm_mst hs_gyomu2 
		ON hs_gyomu2.code_kbn = '0600' 
		AND s.hs_gyomu_cd2 = hs_gyomu2.kbn_value_cd 
		AND hs_gyomu2.delete_flg = 0
  LEFT JOIN sekyu_saki_info sekyu 
		ON s.seko_no = sekyu.seko_no 
		AND s.sekyu_cd = sekyu.sekyu_cd 
		AND sekyu.delete_flg = 0
  LEFT JOIN seko_kihon_all_free free 
		ON s.seko_no = free.seko_no 
		AND free.delete_flg = 0              
  LEFT JOIN code_nm_mst t_zoku 
		ON t_zoku.code_kbn = '0190' 
		AND free.free4_code_cd = t_zoku.kbn_value_cd 
		AND t_zoku.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }
    
    /**
     * PDF出力 御葬儀打合せ事項 会員情報 取得(サンメンバー用)
     *
     * <AUTHOR> Kobayashi
     * @since      2016/09/07
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
         public static function find3($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.kanyu_dt ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT gojokai.seko_no
       ,kanyu_dt
       ,yoto_kbn
       ,kaiin.kbn_value_lnm      AS kaiin_nm
       ,kanyu_nm
       ,keiyaku_gaku
       ,kain_no
       ,harai_no
       ,meigi_chg_cost
       ,early_use_cost
  FROM seko_gojokai_member gojokai
  LEFT JOIN seko_kihon_info s
                ON gojokai.seko_no = s.seko_no
                AND gojokai.delete_flg = 0
  LEFT JOIN code_nm_mst kaiin 
                ON kaiin.code_kbn = '1610' 
                AND gojokai.course_snm_cd = kaiin.kbn_value_cd
                AND kaiin.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }    

    /**
     * PDF出力 御葬儀打合せ事項 施行関係者情報 取得(セレモ用)
     *
     * <AUTHOR> Kobayashi
     * @since      2016/10/26
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
         public static function find4($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT kankeisya.seko_no
       ,kankei_nm               --氏名
       ,kankei_age              --年齢
       ,zoku_nm                 --続柄
       ,kankei_kaisya           --勤務先
       ,kankei_yakushoku        --役職
       ,kankei_addr1            --住所
       ,kankei_tel              --電話番号       
       ,kankei_m_tel            --携帯番号
  FROM seko_relationship kankeisya
  LEFT JOIN seko_kihon_info s
                ON  s.seko_no = kankeisya.seko_no
                AND s.delete_flg = 0              
 WHERE kankeisya.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }    

}
