/* 
 * 決済取消参照画面
 */
var apps  = apps || {};
var cancelDlg = cancelDlg || {};
$(function () {
    
    "use strict";
    // 明細ダイアロール閉じる処理
    $('#refer_dialog .p_close').click(function () {
        $('#refer_dialog, #p_shade').fadeOut(200);
    });
//    // ESC押下時に明細ダイアロール閉じる処理
//    $(document).bind("keydown", function (e) {
//        if (e.keyCode === 27) {
//            $('#refer_dialog .p_close').click();
//        }
//    });

    cancelDlg.pro = {
    };
    // 決済取消画面表示
    cancelDlg.cancelConf = function (opt) {
        var front_den_no = opt.front_den_no;
        var defaults = {
            onSelect: function () {
            },
            onClose: function () {
            },
            fail: function () {
            }
        };
        $.ajax({
            url: $.msiJqlib.baseUrl() + '/saiken/frontsyunou/cancelconf',
            data: {
                front_den_no: front_den_no,
            },
            type: 'post',
            success: function (mydata) {
                if (mydata.status === 'OK') {
                    _resetData(mydata);
                    $('#refer_dialog, #p_shade').fadeIn(200);
                }else{
                    $.msiJqlib.showErr(mydata.msg);
                }
            }
        });
    };
    // 決済取消画面表示
    cancelDlg.cancelConfDaikin = function (opt) {
        var front_den_no  = opt.front_den_no;
        var nyukin_den_no = opt.nyukin_den_no;
        var register_no   = opt.register_no;
        var defaults = {
            onSelect: function () {
            },
            onClose: function () {
            },
            fail: function () {
            }
        };
        $.ajax({
            url: $.msiJqlib.baseUrl() + '/saiken/frontsyunou/cancelconfdaikin',
            data: {
                front_den_no : front_den_no ,
                nyukin_den_no: nyukin_den_no,
                register_no  : register_no,
            },
            type: 'post',
            success: function (mydata) {
                if (mydata.status === 'OK') {
                    _resetDataDaikin(mydata);
                    $('#refer_dialog, #p_shade').fadeIn(200);
                }else{
                    $.msiJqlib.showErr(mydata.msg);
                }
            }
        });
    };
    var ContentModel = Backbone.Model.extend({
        defaults: function () {
            return {
                azukari_prc : null,
                cupon_prc   : null,
                nebiki_prc  : null,
                sum_prc     : null,
                sekyu_nm    : null,
            };
        },
        validation: {
        },
        labels: {
        },
    });
    var ContentView = Backbone.View.extend({
        el: '#refer_dialog',
        events: {
            'click #p_btn_set'   :'doSave',    // 取消確定
            'click #p_btn_canel' :'doCancel',  // 閉じる
        },
        // 取消確定
        doSave: function () {
            $.msiJqlib.clearAlert();
            var bumonCd = $('#hall_cd').val();
            apps.hederModel.set("bumon_cd",bumonCd);
            var front_den_no = apps.hederModel.get("front_den_no");
            if($.msiJqlib.isNullEx(front_den_no)){
                $.msiJqlib.showErr("伝票No.が設定されていない為、決算取消を行えません。");
                return ;
            }
            $('#refer_dialog, #p_shade').fadeOut(0);
            var dataAppJson = JSON.stringify(apps.hederModel.toJSON());
            var dataColJson = JSON.stringify(apps.meisaiConfCol.toJSON());
            var that = this;
            // フロント伝票作成処理実行
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/frontsyunou/hanbaidelete',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
//                        $('.p_close').click();
                        location.reload();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        // 閉じる
        doCancel: function () {
            $('.p_close').click();
        },
        bindings: {
            '#dlg_azukari_prc' : {
                observe : 'azukari_prc',
                onSet : 'commaOmit',
                onGet : 'commaAdd',
            },
            '#dlg_cupon_prc' : {
                observe : 'cupon_prc',
                onSet : 'commaOmit',
                onGet : 'commaAdd',
            },
            '#dlg_nebiki_prc' : {
                observe : 'nebiki_prc',
                onSet : 'commaOmit',
                onGet : 'commaAdd',
            },
            '#dlg_sum_prc' : 'sum_prc',
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(cancelDlg.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();
            this.setSelect2();
            return this;
        },
        setSelect2: function () {
        },
        commaOmit: function (val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val) {
            return $.msiJqlib.commaAdd(val);
        },         
    });
    var Content2View = Backbone.View.extend({
        el: '#refer_dialog',
        events: {
            'click #p_btn_set'   :'doSave',    // 取消確定
            'click #p_btn_canel' :'doCancel',  // 閉じる
        },
        // 取消確定
        doSave: function () {
            $.msiJqlib.clearAlert();
            var bumonCd = $('#hall_cd').val();
            apps.hederModel.set("bumon_cd",bumonCd);
            var front_den_no = apps.hederModel.get("front_den_no");
//            if($.msiJqlib.isNullEx(front_den_no)){
//                $.msiJqlib.showErr("伝票No.が設定されていない為、決算取消を行えません。");
//                return ;
//            }
            $('#refer_dialog, #p_shade').fadeOut(0);
            var dataAppJson = JSON.stringify(apps.hederModel.toJSON());
            //var dataColJson = JSON.stringify(apps.meisaiCol.toJSON());
            var dataColJson = JSON.stringify(apps.hederModel.get('seikyu_array_selected'));
            var that = this;
            // フロント伝票作成処理実行
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/frontsyunou/daikincancel',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
//                        $('.p_close').click();
                        $('#btn_search').click();
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        // 閉じる
        doCancel: function () {
            $('.p_close').click();
        },
        bindings: {
            '#dlg_azukari_prc' : {
                observe : 'azukari_prc',
                onSet : 'commaOmit',
                onGet : 'commaAdd',
            },                
            '#dlg_cupon_prc' : {
                observe : 'cupon_prc',
                onSet : 'commaOmit',
                onGet : 'commaAdd',
            },
            '#dlg_sum_prc' : 'sum_prc',
            '#sekyu_nm'    : 'sekyu_nm',
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(cancelDlg.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();
            this.setSelect2();
            return this;
        },
        setSelect2: function () {
        },
        commaOmit: function (val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val) {
            return $.msiJqlib.commaAdd(val);
        },         
    });
    // 初期化
    var _resetData = function (mydata) {
        if($.msiJqlib.isNullEx(cancelDlg.contentModel)){
            cancelDlg.contentModel = new ContentModel();
            cancelDlg.contentView  = new ContentView({model: cancelDlg.contentModel});
        }
        cancelDlg.mydata = mydata;
        cancelDlg.contentView.model.set(mydata.data.dataApp);
    };
    var _resetDataDaikin = function (mydata) {
        if($.msiJqlib.isNullEx(cancelDlg.contentModel)){
            cancelDlg.contentModel = new ContentModel();
            cancelDlg.content2View = new Content2View({model: cancelDlg.contentModel});
        }
        cancelDlg.mydata = mydata;
        cancelDlg.content2View.model.set(mydata.data.dataApp);
    };    
});