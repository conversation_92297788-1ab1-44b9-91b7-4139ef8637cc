<?php
/**
 * 一般化商品(商品選択,返礼品,料理)購入
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR> Mihara
 * @since      2014/12/xx
 * @version    2015/05/29 Mihara CAT_LVL0_OPTION(オプション)は dataSbt を 1 => 10 に変更
 * @version    2015/06/29 Mihara @n(商品選択数量展開)
 * @version    2015/10/29 Mihara 拡張版択一選択(selone-ex)対応
 * @version    2015/11/02 Mihara 更新項目に shohin_tkiyo_nm2(商品摘要名2) を追加
 * @version    2016/03/04 Mihara タブ内の shohin_cd は通常の商品CDでなく shohin_cd+shohin_kbn で管理する
 * @version    2019/07/xx Mihara 軽減税率対応 keigen
 * @filesource 
 */

/**
 * 一般化商品(商品選択,返礼品,料理)購入
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR> Mihara
 * @since      2014/12/xx
 */
abstract class Juchu_JuchuItemex00 extends Juchu_JuchuItemexAbstract
{
    /* レベル０ カテゴリ */
    const CAT_LVL0_HENREI =  9;
    const CAT_LVL0_RYORI  = 10;
    const CAT_LVL0_SHOHIN = 11;
    const CAT_LVL0_OPTION = 13;
    const CAT_LVL0_FLOWER   = 14;
    const CAT_LVL0_OPTION15 = 15;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION16 = 16;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION17 = 17;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION18 = 18;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION19 = 19;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION20 = 20;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION21 = 21;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION22 = 22;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION23 = 23;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION24 = 24;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION25 = 25;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION26 = 26;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION27 = 27;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION28 = 28;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION29 = 29;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION30 = 30;    // 2017/03/16 ADD Kayo
    const CAT_LVL0_OPTION31 = 31;    // 2017/03/28 ADD Kayo
    const CAT_LVL0_OPTION32 = 32;    // 2017/03/28 ADD Kayo
    const CAT_LVL0_OPTION33 = 33;    // 2017/03/28 ADD Kayo
    const CAT_LVL0_OPTION34 = 34;    // 2017/04/18 ADD Kayo

    const CODE_KBN_5390 = '5390';   // 祭壇パターン
    const CODE_KBN_5400 = '5400';   // 祭壇色
    const CODE_KBN_5410 = '5410';   // 業務内容(洗体)
    const CODE_KBN_5420 = '5420';   // 業務内容(洗髪)
    const CODE_KBN_5430 = '5430';   // 業務内容(古式)
    const CODE_KBN_5440 = '5440';   // 死因
    const CODE_KBN_5450 = '5450';   // 棺種類
    const CODE_KBN_5460 = '5460';   // 棺サイズ
    const CODE_KBN_5470 = '5470';   // 旅支度
    const CODE_KBN_5480 = '5480';   // 仏衣
    const CODE_KBN_5490 = '5490';   // ケアサービスより連絡
    const CODE_KBN_5495 = '5495';   // 湯灌時ドライ
    const CODE_KBN_5500 = '5500';   // 業務内容自社(洗体)
    const CODE_KBN_5510 = '5510';   // 業務内容自社(洗髪)
    const CODE_KBN_5520 = '5520';   // 業務内容自社(古式)
    const CODE_KBN_5530 = '5530';   // 寝台霊柩車出発地
    const CODE_KBN_5540 = '5540';   // 寝台霊柩車行先
    const CODE_KBN_5550 = '5550';   // 寝台霊柩車棺サイズ
    const CODE_KBN_5560 = '5560';   // ストレッチャー
    const CODE_KBN_5570 = '5570';   // 搬送布団
    const CODE_KBN_5580 = '5580';   // 後付車
    const CODE_KBN_5590 = '5590';   // 中送り車種
    const CODE_KBN_5600 = '5600';   // マイク
    const CODE_KBN_5610 = '5610';   // 当社係員立会
    const CODE_KBN_5620 = '5620';   // 寝台霊柩車請求先
    const CODE_KBN_5630 = '5630';   // 待機
    const CODE_KBN_5640 = '5640';   // 礼服スタイル
    const CODE_KBN_5650 = '5650';   // 礼服届け先
    const CODE_KBN_5660 = '5660';   // 紋付届け先
    const CODE_KBN_5670 = '5670';   // 喪服身幅
    const CODE_KBN_5680 = '5680';   // 喪服届け先
    const CODE_KBN_5690 = '5690';   // フォーマル届け先
    const CODE_KBN_5700 = '5700';   // 着付場所
    const CODE_KBN_5710 = '5710';   // ヘアセット実施場所
    const CODE_KBN_5770 = '5770';   // 熨斗紙
    const CODE_KBN_5780 = '5780';   // 熨斗紙(蓮)
    const CODE_KBN_5790 = '5790';   // 水引
    const CODE_KBN_5800 = '5800';   // 袋
    const CODE_KBN_2500 = '2500';   // 会場名
    const CODE_KBN_6280 = '6280';   // 羽織
    const CODE_KBN_6760 = '6760';   // メモリアルＢＧＭ
    
    protected $_catLvl0 = null;        // lvl0 現在値
    protected $_catLvl1 = null;        // lvl1 現在値
    protected $_catLvl2 = null;        // lvl2 現在値
    protected $_itemCatTbl = array();  // カテゴリ情報テーブル
    protected $_itemCatTblMsi = array();  // カテゴリ明細テーブル
    protected $_itemCatTblLvl1 = null; // lvl1 カテゴリ情報
    protected $_itemCatTblLvl2 = null; // lvl2 カテゴリ情報
    protected $_isListCtxt = false;    // リスト表示コンテキスト
    protected $_noDfltCookie = false; // cookie による省略値補完をしない
    protected $_isCompatCtxt = false;  // save() 時等に getCategoryKbn() を fake するため
    protected $_arrCtxt = null;        // コンストラクタの arrCtxt 引数

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param array  arrCtxt   array('lvl0'=>.., 'lvl1'=>..) レベル０商品カテゴリ
     * @param string $sekoNo   施行番号
     */
    public function __construct($arrCtxt, $sekoNo = null)
    {
        parent::__construct($sekoNo);

        $this->_arrCtxt = $arrCtxt;

        $this->_catLvl0 = $arrCtxt['lvl0'];
        
        // リスト表示の場合
        if ( isset($arrCtxt['isListCtxt']) && $arrCtxt['isListCtxt'] ) {
            $this->_isListCtxt = true;
        }

        // Cookie省略値補完をしない
        if ( isset($arrCtxt['noDfltCookie']) && $arrCtxt['noDfltCookie'] ) {
            $this->_noDfltCookie = true;
        }

        $db = Msi_Sys_DbManager::getMyDb();
        if ( App_Utils::isFukusuKaisyaKanri() ) {   // 複数会社対応
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $this->_bumonCd));
            if ($bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                $bumonCd = $this->_bumonCd;
            } else {
                $bumonCd = '00000';
            }
        } else {
            $bumonCd = null;
        }

        $this->_itemCatTbl = DataMapper_CategoryTabInfo::findTop( $db, $this->_catLvl0,$bumonCd, $this->_oyaBumonCd );
        $this->_itemCatTblMsi = DataMapper_CategoryTabMsi::findTop($db, $this->_catLvl0, $bumonCd, $this->_sekoNo);

        $this->_adjItemCatTbl();

        $lvl1 = $arrCtxt['lvl1'];
        $this->setCatLvl1( $lvl1 );

        $lvl2 = $arrCtxt['lvl2'];
        if ( $lvl2 ) {
            $this->setCatLvl2( $lvl2 );
        } else {
            $this->setDfltLvl2();
        }
    }

    /**
     * カテゴリ別タブ管理情報 調整
     * $this->_itemCatTbl の調整(絞込み等)を行います
     *
     * <AUTHOR> Mihara
     * @since 2015/05/xx
     * @return void
     */
    protected function _adjItemCatTbl()
    {
    }

    /**
     * 前回値保持用 cookie からデータ取得
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param  string     $key      キー(<lvl0>.<lvl1> | <lvl0> | <lvl0>.bol )
     * @return string|null(設定なし)
     */
    static public function getCookieForPref($key)
    {
        $ck_fdn_itemex = Msi_Sys_Utils::getCookie('fdn_itemex');
        foreach ( explode(',', $ck_fdn_itemex) as $item ) {
            @ list($k, $v) = explode(':', $item);
            if ( (string)$k === (string)$key ) {
                return $v;
            }
        }
        return null;
    }

    /**
     * レベル１商品カテゴリを設定する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param $lvl1       string|null    レベル１商品カテゴリ. null の場合は default or 先頭要素を設定する
     * @return void
     */
    public function setCatLvl1($lvl1)
    {
        $_lvl1 = null;
        if ( $lvl1 !== null ) {
            $rec = $this->tblInfoCatLvl1($lvl1);
            if ( $rec ) {
                $_lvl1 = $rec['key'];
                $this->_itemCatTblLvl1 = $rec;
            }
        }

        if ( $_lvl1 === null ) {
            if ( ! $this->_noDfltCookie ) {
                // まずは、Cookie 情報から設定を試みる
                $lvlKey = $this->curCatLvl0();
                $ckVal = static::getCookieForPref( $lvlKey );
                if ( $ckVal ) {
                    $rec = $this->tblInfoCatLvl1($ckVal);
                    if ( $rec ) {
                        $_lvl1 = $rec['key'];
                        $this->_itemCatTblLvl1 = $rec;
                        $this->_catLvl1 = $_lvl1;
                        return;
                    }
                }
            }

            // 先頭要素
            if ( isset($this->_itemCatTbl[0]) && isset($this->_itemCatTbl[0]['key']) ) {
                $_lvl1 = $this->_itemCatTbl[0]['key'];
            }
        }
        if ( $_lvl1 === null ) {
            throw new Exception( "no level1 item category: $lvl1" );
        }

        $this->_catLvl1 = $_lvl1;
    }

    /**
     * レベル１商品カテゴリの情報を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param $lvl1       string    レベル１商品カテゴリ
     * @return array|null(not found)
     */
    public function tblInfoCatLvl1($lvl1)
    {
        foreach ( $this->_itemCatTbl as $rec ) {
            if ( (integer)$rec['key'] === (integer)$lvl1 ) {
                return $rec;
            }
        }
        return null;
    }

    /**
     * レベル２商品カテゴリを設定する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param $lvl2       string|null    レベル２商品カテゴリ. null の場合は設定しない
     * @return void
     */
    public function setCatLvl2($lvl2)
    {
        $_lvl2 = null;
        if ( $lvl2 != null ) {
            $rec = $this->tblInfoCatLvl2($lvl2);
            if ( $rec ) {
                $_lvl2 = $rec['key'];
                $this->_itemCatTblLvl2 = $rec;
            }
        }

        $this->_catLvl2 = $_lvl2;
    }

    /**
     * レベル２商品カテゴリの情報を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param $lvl2       string    レベル２商品カテゴリ
     * @param $lvl1       string    レベル１商品カテゴリ
     * @return array|null(not found)
     */
    public function tblInfoCatLvl2($lvl2, $lvl1=null)
    {
        if ( $lvl1 === null ) {
            $lvl1 = $this->curCatLvl1();
        }

        $catLvl1s = $this->tblInfoCatLvl1($lvl1);
        if ( $catLvl1s === null ) {
            return null;
        }

        foreach ( $catLvl1s['_child'] as $rec ) {
            if ( (integer)$rec['key'] === (integer)$lvl2 ) {
                return $rec;
            }
        }
        return null;
    }

    /**
     * レベル２商品カテゴリ 省略時設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return void
     */
    public function setDfltLvl2()
    {
        $curLvl2 = $this->curCatLvl2();

        // 設定されていない場合
        if ( ! $curLvl2 ) {
            if ( ! $this->_noDfltCookie ) {
                // まずは、Cookie 情報から設定を試みる
                $lvlKey = $this->curCatLvl0() .'.'. $this->curCatLvl1();
                $ckVal = static::getCookieForPref($lvlKey);
                if ( $ckVal ) {
                    $this->setCatLvl2( $ckVal );
                }
                if ( $this->curCatLvl2() ) { // 設定されていれば抜ける
                    return;
                }
            }

            // 次に、lvl1 情報から lvl2 を適当に設定する
            $curLvl1 = $this->curCatLvl1();
            $lvl1Tbl = $this->tblInfoCatLvl1( $curLvl1 );
            // ひとまず先頭要素とする
            if ( count($lvl1Tbl['_child']) > 0 ) {
                $lvl2Tbl = $lvl1Tbl['_child'][0];
                $this->setCatLvl2( $lvl2Tbl['key'] );
            }
        }

    }

    /**
     * 現コンテキストの商品カテゴリ情報を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array|null(not found)
     */
    public function getItemCatInfo()
    {
        $lvl1 = $this->curCatLvl1();
        $lvl2 = $this->curCatLvl2();

        $tbl = null;
        if ( $lvl2 ) {
            $tbl = $this->tblInfoCatLvl2($lvl2, $lvl1);
        } else {
            $tbl = $this->tblInfoCatLvl1($lvl1);
        }

        return $tbl;
    }

    /**
     * 選択されているレベル０商品カテゴリを返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return string    レベル０商品カテゴリ
     */
    public function curCatLvl0()
    {
        return $this->_catLvl0;
    }

    /**
     * 選択されているレベル１商品カテゴリを返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return string    レベル１商品カテゴリ
     */
    public function curCatLvl1()
    {
        return $this->_catLvl1;
    }

    /**
     * 選択されているレベル２商品カテゴリを返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return string    レベル２商品カテゴリ
     */
    public function curCatLvl2()
    {
        return $this->_catLvl2;
    }

    /**
     * 商品カテゴリの配列を返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array
     */
    public function getItemCatTbl()
    {
        return $this->_itemCatTbl;
    }

    /**
     * 選択可能なレベル１商品カテゴリの配列を返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array      レベル１商品カテゴリ
     */
    public function optCatLvl1()
    {
        $arr = array();
        foreach ( $this->_itemCatTbl as &$rec ) {
            $arr[] = $rec;
        }
        return $arr;
    }

    /**
     * 選択可能なレベル２商品カテゴリの配列を返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array      レベル２商品カテゴリ
     */
    public function DELoptCatLvl2($lvl1=null) // $lvl0?
    {
        // return $this->_optCatLvl2;
    }

    /**
     * 目的区分の取得
     *   cf. Juchu_JuchuItemexInfoAdapter へのプロバイダ
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string 目的区分
     */
    public function getMokutekiKbn()
    {
        // 目的区分 特別パッチ
        if ( isset($this->_mokuteki_kbn_super) ) {
            return $this->_mokuteki_kbn_super;
        }

        $mokutekiKbn = null;

        // マスタデータに基づく設定
        $catInfo = $this->getItemCatInfo();
        // Msi_Sys_Utils::debug( '*** @@@ ### GGG =>' . Msi_Sys_Utils::dump($catInfo) );
        if ( isset($catInfo['mkt_code_kbn']) && isset($catInfo['mkt_kbn']) ) {
            $mokutekiKbn = $catInfo['mkt_kbn'];
            if ( strlen($mokutekiKbn) > 0 ) {
                // Msi_Sys_Utils::debug( '*** returns mokutekiKbn =>' . $mokutekiKbn );
                return $mokutekiKbn;
            }
        }

        // old version ...
        switch( $this->curCatLvl0() ) {
        case static::CAT_LVL0_SHOHIN:
            $mokutekiKbn = 0; // //目的区分：商品                const MOKUTEKI_KBN_ITEM = 0;
            break;
        case static::CAT_LVL0_OPTION15:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION16:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION17:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION18:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION19:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION20:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION21:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION22:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION23:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION24:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION25:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION26:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION27:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION28:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION29:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION30:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION31:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION32:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION33:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION34:    // 2017/04/18 ADD Kayo
            $mokutekiKbn = 0; // //目的区分：商品                const MOKUTEKI_KBN_ITEM = 0;
            break;
         case static::CAT_LVL0_HENREI:
            switch( $this->curCatLvl1() ) {
            case '会葬品':
            case '返礼品':
                $mokutekiKbn = 3; // //目的区分：忌中引物 const MOKUTEKI_KBN_KICHU = 3;
                break;
            default: // $this->curCatLvl1() は文字列で無いので現状すべてこの場合にマッチ
                $mokutekiKbn = 1; // //目的区分：会葬     const MOKUTEKI_KBN_KAISO = 1;
            }
            break;
        case static::CAT_LVL0_RYORI:
            switch( $this->curCatLvl1() ) {
            case '通夜':
                $mokutekiKbn = 10; // //目的区分：通夜  const MOKUTEKI_KBN_TSUYA = 10;
                break;
            case '壇払い':
                $mokutekiKbn = 30; // //目的区分：壇払い  const MOKUTEKI_KBN_DANBARAI = 30;
                break;
            case 'その他':
            default: // $this->curCatLvl1() は文字列で無いので現状すべてこの場合にマッチ
                $mokutekiKbn = 20; // //目的区分：その他  const MOKUTEKI_KBN_SONOTA = 20;
                break;
            }
            // $mokutekiKbn = $this->getCategoryKbnRaw(); // XXX
            break;
        default:
            throw new Exception( 'unknown _catLvl0 in getMokutekiKbn()' );
        }

        return $mokutekiKbn;
    }

	/**
     * データ種別の取得
     *   cf. Juchu_JuchuItemexInfoAdapter へのプロバイダ
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @version    2015/05/29 Mihara CAT_LVL0_OPTION(オプション)は dataSbt を 1 => 10 に変更
     * @return  string データ種別
     */
	public function getDataSbt()
    {
        $dataSbt = null;

        // XXX
        switch( $this->curCatLvl0() ) {
        case static::CAT_LVL0_SHOHIN:   //
        case static::CAT_LVL0_FLOWER:   // 14
            $dataSbt = 1; //  //データ種別：商品   const DATA_SBT_ITEM = 1;
            break;
        case static::CAT_LVL0_OPTION:   // 13   
            $dataSbt = 10;
            break;
        case static::CAT_LVL0_OPTION15:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION16:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION17:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION18:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION19:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION20:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION21:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION22:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION23:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION24:    // 2017/03/16 ADD Kayo
        case static::CAT_LVL0_OPTION25:    // 2017/03/27 ADD Kayo
            $dataSbt = 1; //  //データ種別：商品   const DATA_SBT_ITEM = 1;
            break;
        case static::CAT_LVL0_OPTION26:    // 2017/07/04 ADD Kayo
            $dataSbt = 8; //  //データ種別：商品   const DATA_SBT_ITEM = 1;
            break;
        case static::CAT_LVL0_OPTION27:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION28:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION29:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION30:    // 2017/03/27 ADD Kayo
        case static::CAT_LVL0_OPTION31:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION32:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION33:    // 2017/03/28 ADD Kayo
        case static::CAT_LVL0_OPTION34:    // 2017/04/18 ADD Kayo
            $dataSbt = 1; //  //データ種別：商品   const DATA_SBT_ITEM = 1;
            break;
        case static::CAT_LVL0_HENREI:
            $dataSbt = 2; // //データ種別： 返礼品 const DATA_SBT_HENREIHIN = 2;
            break;
        case static::CAT_LVL0_RYORI:
            switch( $this->curCatLvl1() ) {
            case '通夜':
                $dataSbt = 3; // //データ種別：料理 const DATA_SBT_TSUYA = 3;
                break;
            case '壇払い':
                $dataSbt = 4; // //データ種別：壇払い const DATA_SBT_DANBARAI = 4;
                break;
            case 'その他':
            default: // $this->curCatLvl1() は文字列で無いので現状すべてこの場合にマッチ
                $dataSbt = 3; // //データ種別：料理 const DATA_SBT_TSUYA = 3;
                break;
            }
            break;
        default:
            throw new Exception( 'unknown _catLvl0 in getDataSbt()' );
        }

        return $dataSbt;
	}

	/**
     * 現コンテキストのカテゴリー区分を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  integer カテゴリー区分
    */
	public function getCategoryKbn()
    {
        // 旧来の getCategoryKbn() を模倣
        // cf. Juchu_JuchuinfoSubAbstract::saveJuchudenpyoMsi
        if ( $this->_isCompatCtxt ) {
            $lvl0 = $this->curCatLvl0();
            if ( $lvl0 == 9 ) { // 返礼品
                return 6; // 返礼品
            } else if ( $lvl0 == 10 ) { // 料理
                return 7; // 料理
            }
            // 商品選択はそのまま返す
        }

        return $this->getCategoryKbnRaw();
	}

	/**
     * カテゴリー区分を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  integer カテゴリー区分
    */
	public function getCategoryKbnRaw()
    {
        $icatTbl = $this->getItemCatInfo();
        if ( $icatTbl === null ) {
            throw new Exception( "商品カテゴリ情報が存在しません(カテゴリー区分)" );
        }
        $kbn = $icatTbl['category_kbn'];
        return (int)$kbn;
	}

	/**
     * 現コンテキストの申込区分を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  integer 申込区分
    */
	public function getMoushikomiKbn()
    {
        $icatTbl = $this->getItemCatInfo();
        if ( $icatTbl === null ) {
            throw new Exception( "商品カテゴリ情報が存在しません(申込区分)" );
        }
        $kbn = $icatTbl['moushikomi_kbn'];
        return (int)$kbn;
	}

	/**
     * 現コンテキストのタブ順(選択のためのキーの一種)を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  integer タブ順
    */
	public function getTabNo()
    {
        if ( $this->_isListCtxt ) { // リスト表示の場合は null を返す
            return null;
        }

        $icatTbl = $this->getItemCatInfo();
        if ( $icatTbl === null ) {
            throw new Exception( "商品カテゴリ情報が存在しません(タブ順)" );
        }
        // Msi_Sys_Utils::debug( '*** getTabNo=>' . Msi_Sys_Utils::dump($icatTbl) );

        $kbn = $icatTbl['tab_no'];

        return (int)$kbn;
	}

	/**
     *  商品選択用 category_kbn, kbn1, kbn2 取得
     *   cf. Juchu_JuchuinfoSubAbstract#getItem()
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  array($category_kbn, $kbn1, $kbn2)
    */
	public function getItemLegacyParams()
    {
        $category_kbn = $this->getCategoryKbn();
        $kbn1 = 0;
        $kbn2 = 0;

        // XXX
        switch( $this->curCatLvl0() ) {
        case static::CAT_LVL0_SHOHIN:
            switch( $this->curCatLvl1() ) { // 
            case '式場祭壇':
                //                $category_kbn = 8; // self::CATEGORY_KBN_JITAKU_SAIDAN;   //カテゴリ区分
                break;
            case 'その他':
            default:
                //                $category_kbn = 15; // //カテゴリー区分：その他必要なもの  const CATEGORY_KBN_OTHER = 15;
                //                $category_kbn = 12;
	//カテゴリー区分：自宅祭壇	const CATEGORY_KBN_JITAKU_SAIDAN = 8;
	//カテゴリー区分：関前飾り	const CATEGORY_KBN_SEKIZEN_KAZARI = 9;
	//カテゴリー区分：式場祭壇	const CATEGORY_KBN_SIKIJO_SAIDAN = 10;
	//カテゴリー区分：棺	const CATEGORY_KBN_HITUGI = 11;
	//カテゴリー区分：霊柩車	const CATEGORY_KBN_HEARSE = 12;
	//カテゴリー区分：湯灌	const CATEGORY_KBN_YUKAN = 13;
	//カテゴリー区分：式場準備品	const CATEGORY_KBN_SOSYOKU = 14;
	//カテゴリー区分：その他必要なもの	const CATEGORY_KBN_OTHER = 15;
                break;
            }
            break;
        case static::CAT_LVL0_HENREI:
            switch( $this->curCatLvl1() ) {
            case '忌中':
                $kbn1 = 1; // //web_disp_shohin_mst kbn1の値：忌中 const WEB_DISP_SHOHIN_MST_KBN1_KICHU = 1;
                break;
            case 'その他':
            default:
                $kbn1 = 0; // //web_disp_shohin_mst kbn1の値：香典返し const WEB_DISP_SHOHIN_MST_KBN1_KODEN = 0;
                break;
            }
            break;
        case static::CAT_LVL0_RYORI:
            switch( $this->curCatLvl1() ) {
            case '通夜':
                $kbn1 = 0; // //web_disp_shohin_mst kbn1の値：通夜 const WEB_DISP_SHOHIN_MST_KBN1_TSUYA = 0;
                break;
            case '壇払い':
                $kbn1 = 2; // //web_disp_shohin_mst kbn1の値：壇払い const WEB_DISP_SHOHIN_MST_KBN1_DANBARAI = 2;
                break;
            case 'その他':
            default:
                $kbn1 = 2; // //web_disp_shohin_mst kbn1の値：壇払い const WEB_DISP_SHOHIN_MST_KBN1_DANBARAI = 2;
                //                $kbn1 = 3; // //web_disp_shohin_mst kbn1の値：その他 const WEB_DISP_SHOHIN_MST_KBN1_SONOTA = 3;
                break;
            }
            break;
        default:
            throw new Exception( 'unknown _catLvl0 in getItemLegacyParams()' );
        }

        return array($category_kbn, $kbn1, $kbn2);
	}

	/**
     * 表示スタイル種別を取得する
     *
     * <AUTHOR> Mihara
     * @since 2015/06/30
     * @return  integer 表示スタイル種別
    */
	public function getDispStyleType()
    {
        $icatTbl = $this->getItemCatInfo();
        if ( $icatTbl === null ) {
            throw new Exception( "商品カテゴリ情報が存在しません(表示スタイル種別)" );
        }
        if ( isset($icatTbl['disp_style_type']) ) {
            $type = $icatTbl['disp_style_type'];
        } else {
            $type = 0;
        }
        return (int)$type;
	}

    /**
     * 初期情報取得処理(JSON)
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array jsonData
     */
    public function getInitData()
    {
        $data = $this->getData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     * 初期情報取得処理(JSONでない)
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array data
     */
    public function getInitDataRaw()
    {
        $data = $this->getData();
        return $data;
    }

    /**
     * 初期情報データを取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array 初期情報データ
     */
    private function getData()
    {
        return array();

        // constructor で実行済み
        // $this->_sekoNo = $this->getReqSekoNo();
        // $this->setInitParam();

        $select = $this->selectSekoKihon();

        $db = Msi_Sys_DbManager::getMyDb();

        // APPデータを取得する
        // $dataApp = $this->getAppData();

        $data = array(
                      'catLvl0' => $this->curCatLvl0(),
                      'catLvl1' => $this->curCatLvl1(),
                      // 'catLvl2' => $this->curCatLvl2(),
                      'optLvl1' => $this->optCatLvl1(),
                      );
        return $data;
    }

    /**
     * 受注データのうち、レベル０カテゴリに属する商品を抜き出して返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param  string|null   レベル０  nullの場合は現在値
     * @return array         登録データ
     */
    public function DELgetRegItemData($lvl0=null)
    {
        if ( $lvl0 === null ) {
            $lvl0 = $this->curCatLvl0();
        }

        $db = Msi_Sys_DbManager::getMyDb();

        $data = array();

        switch( $lvl0 ) {
        case static::CAT_LVL0_SHOHIN:
        case static::CAT_LVL0_HENREI:
        case static::CAT_LVL0_RYORI:
        }

        return $data;
    }

    protected $_juchuItemexInfoAdapter = null;

    /**
     * Juchu_JuchuItemexInfoAdapter オブジェクトを返す
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array Collectionデータ
     */
    protected function getJuchuItemexInfoAdapter()
    {
        if ( $this->_juchuItemexInfoAdapter === null ) {
            $this->_juchuItemexInfoAdapter = new Juchu_JuchuItemexInfoAdapter($this, $this->_sekoNo);
        }
        return $this->_juchuItemexInfoAdapter;
    }

    /**
     * APPデータ(基本トランデータ)を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array APPデータ
     */
    public function getAppData()
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $data = array();
        $data['seko_no'] = $this->_sekoNo;
        
        $kihon = array();
        $select = $this->getSekoKihon();
        $kihon = $select; // XXX

        // $data['kihon'] = $kihon;
        // $this->_appKihon = $kihon;

        $data['lvl0'] = $this->curCatLvl0();
        $data['lvl1'] = $this->curCatLvl1();
        $data['lvl2'] = $this->curCatLvl2();
        $data['is_kakutei'] = $this->isMitsuKakutei();
        $data['cat_tab_info'] = $this->_itemCatTbl;
        $data['cat_tab_msi'] = $this->_itemCatTblMsi;
        $data['m_shohin_kbn'] = DataMapper_ShohinKbnMst::find($db);

        return $data;
    }

    /**
     * Collection データ(受注明細トランデータ)を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return array Collectionデータ
     */
    public function getColData()
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $data = array();

        // $legacyIteminfo = $this->getJuchuItemexInfoAdapter();
        // $denpyoMsi = $legacyIteminfo->getJuchuDenpyoMsi($db);

        $denpyoMsi = $this->getJuchuDenpyoMsi($db);

        $data = $denpyoMsi; // ['denpyoMsi'] = $denpyoMsi;

        return $data;
    }

	/**
     * 受注明細を取得
     *   org: Juchu_JuchuinfoSubAbstract#getJuchuDenpyoMsi()
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  array $select 受注伝票明細
     */
	public function getJuchuDenpyoMsi($db)
    {
         // 複数会社対応 2016/10/07 ADD Kayo 
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
 		$select = $db->easySelect(<<< END_OF_SQL
	SELECT	j.*
			,s.sagaku_keisan_grp 
	FROM	juchu_denpyo_msi	j
	LEFT JOIN	juchu_denpyo		jh
	ON		j.denpyo_no			=	jh.denpyo_no
	AND		0					=	jh.delete_flg		
	LEFT JOIN  shohin_mst		s
	ON		j.shohin_cd		=	s.shohin_cd
    AND     s.kaisya_cd     =   :kaisya_cd -- 2016/10/07 ADD Kayo            
	AND		0				=	s.delete_flg			
	WHERE	j.seko_no		=	:seko_no 
	AND		jh.seko_no	=	:seko_no
        AND		j.seko_no_sub	=	:seko_no_sub 
	AND		j.data_sbt		=	:data_sbt
	AND		jh.data_kbn		=	1			
	AND		j.mokuteki_kbn	=	:mokuteki_kbn
	AND		j.delete_flg	=	0
END_OF_SQL
		,array(
			'seko_no'		=> $this->_sekoNo,
			'seko_no_sub'	=> $this->_sekoNoSub,
            'data_sbt'		=> $this->getDataSbt(),
            'mokuteki_kbn'	=> $this->getMokutekiKbn(),
            'kaisya_cd'     => $curKaisyaCd             // 2016/10/07 ADD Kayo    
		));		

		return $select;
	}

    /* /\** */
    /*  * マスタデータを取得する */
    /*  * */
    /*  * <AUTHOR> Mihara */
    /*  * @since 2014/12/xx */
    /*  * @return array Master データ */
    /*  *\/ */
    /* public function getMstrData() */
    /* { */
    /*     $legacyIteminfo = $this->getJuchuItemexInfoAdapter(); // new Juchu_JuchuItemexInfoAdapter($this, $this->_sekoNo); */
    /*     list($category_kbn, $kbn1, $kbn2) = $this->getItemLegacyParams(); */
    /*     $item_list = $legacyIteminfo->getItem($category_kbn, $kbn1, $kbn2); */

    /*     $data = array( */
    /*                   'itemCatTbl' => $this->getItemCatTbl(), */
    /*                   'legacy_item_list' => $item_list, */
    /*                   ); */
    /*     return $data; */
    /* } */

    /**
     * 商品選択サブ画面要素生成
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    public function genItemPicker()
    {
        $renderer = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');

        $legacyIteminfo = $this->getJuchuItemexInfoAdapter();
        // list($category_kbn, $kbn1, $kbn2) = $this->getItemLegacyParams();
        // $item_list = $legacyIteminfo->getItem($category_kbn, $kbn1, $kbn2);

        $renderer->view->base_category_kbn = $this->getCategoryKbnRaw();
        $renderer->view->gazo_shohin_zei_kbn= $this->getGazoShohinZeiKbn(); // 画像商品税込表示区分 2015/04/30 ADD Kayo
        $renderer->view->gazo_shohin_prc_kbn= $this->getGazoShohinPrcKbn(); // 画像商品金額表示区分 2017/03/15 ADD Kayo
        $renderer->view->gazo_kain_prc_kbn= $this->getGazoKainPrcKbn(); // 画像商品会員価格表示区分

        $item_list = $legacyIteminfo->getTargetedItems();

        $catInfo = $this->getItemCatInfo();
        if ( isset($catInfo['kaijo_sansho_kbn']) && $catInfo['kaijo_sansho_kbn'] ) {
            App_Utils2::adjHallPrice($this->_sekoNo, $item_list);
        }

        // 明細に互助会値引き項目設定処理
        App_Utils2::adjGojokaiNebiki4($this->_sekoNo, $item_list);
        // Msi_Sys_Utils::debug( '*** item_list=>' . Msi_Sys_Utils::dump($item_list) );

        // 未登録分の反映
        $newData = array();
        if ( isset($this->_arrCtxt['dataCol']) && isset($this->_arrCtxt['dataCol']['new']) ) {
            $newData = $this->_arrCtxt['dataCol']['new'];
        }
        // Msi_Sys_Utils::debug( '*** newData=>' . Msi_Sys_Utils::dump($newData) );

        // 拡張版択一選択のデータ調整。現登録データを反映させる
        if ( isset($this->_arrCtxt['dataEx']) && isset($this->_arrCtxt['dataEx']['ex_type']) &&
             $this->_arrCtxt['dataEx']['ex_type'] == 'selone-ex') {
            $newData = $this->seloneExProc( $newData, $this->_arrCtxt['dataEx'] );

            // クライアントにデータを送る
            $jsonNewData = Msi_Sys_Utils::json_encode($newData);
            $renderer->view->mydata_ex_picker_id = $jsonNewData;
        }
        
        $curLvl2 = $this->curCatLvl2();
        $item_list2 = array();
        foreach ( $item_list as $rec ) {
            if ( true ) { // $curLvl2 == $rec['tab_no'] ) {
                $shohin_cd = $rec['shohin_cd'];
                $rec['disp_shohin_cd'] = $rec['shohin_cd'];
                
                // 2016/03/04 mihara タブを超えて一意の商品CD
                $shohin_cd = preg_replace( '/--\d*$/', '', $shohin_cd );
                $shohin_cd = $shohin_cd ."--". $rec['shohin_kbn'];
                $rec['shohin_cd'] = $shohin_cd;
                
                if ( array_key_exists($shohin_cd, $newData) ) { // 未登録データあり
                    $rec = array_merge($rec, $newData[$shohin_cd]);
                    $rec['hanbai_tnk'] = $rec['price'];
                    if ( isset($rec['shohintkiyonm']) ) { // 2015/05/22
                        $rec['shohin_tkiyo_nm'] = $rec['shohintkiyonm'];
                    }
                    if ( isset($rec['shohintkiyonm2']) ) { // 2015/11/02 mihara
                        $rec['shohin_tkiyo_nm2'] = $rec['shohintkiyonm2'];
                    }
                    if ( isset($rec['shohinnm']) ) {
                        $rec['shohin_nm'] = $rec['shohinnm'];
                    }
                }
                $item_list2[] = $rec;
            }
        }

        // 指定されているなら、購入数の大きい順にソート
        if ( $this->isItemListDispByQuantityOrder() ) {
            $rtn = usort( $item_list2, function($a, $b) {
                    return $a['quantity'] == $b['quantity'] ? ( $a['disp_no'] > $b['disp_no'] ? +1 : -1 )
                        : ($a['quantity'] > $b['quantity'] ? -1 : +1); } );
        }

        $renderer->view->item_list = $item_list2;

        $cond2 = array();
        $lvl1 = $this->curCatLvl1();
        $lvl1Tbl = $this->tblInfoCatLvl1( $lvl1 );
        foreach ( $lvl1Tbl['_child'] as $rec ) {
            $cond2[] = array( 'cap'=>$rec['cap'], 'val'=>$rec['key'] );
        }
        if ( count($cond2) > 0 ) {
            $renderer->view->is_lvl2_cond = true;
            $renderer->view->lvl2_ui_style = $this->lvl2UiType();
            if ( $renderer->view->lvl2_ui_style == 'tabBtn' ) {
                $this->lvl2UiTabBtn($lvl1Tbl['_child'], $renderer);
            }
            $renderer->view->cond2_list = $cond2;
            $renderer->view->cond2_cur = $this->curCatLvl2();
            // Msi_Sys_Utils::debug( '*** cond2_cur=>' . $this->curCatLvl2() );
        }

        $dataCat['cat_tab_info'] = $this->_itemCatTbl;
        $dataCat['cat_tab_msi'] = $this->_itemCatTblMsi;
        $dataCat['lvl0'] = $this->curCatLvl0();
        $dataCat['lvl1'] = $this->curCatLvl1();
        $dataCat['lvl2'] = $this->curCatLvl2();
        $dataCat['child'] = $lvl1Tbl['_child'];
        
        $jsonNewDataCat = Msi_Sys_Utils::json_encode($dataCat);
        $renderer->view->mydata_ex_cat_data = $jsonNewDataCat;
        
        $this->genItemPickerPreHook();

        $this->setItemPickerTpl($renderer);

        $this->genItemPickerPostHook();

        // 最終調整
        $this->_fixup($renderer);
    }

    /**
     * 最終調整
     *
     * <AUTHOR> Mihara
     * @since 2015/03/xx
     * @param Zend_Controller_Action_Helper_ViewRenderer  $renderer
     */
    protected function _fixup($renderer)
    {
        $renderer->view->houshi_service_nm = $this->_get_service_nm();
        $renderer->view->itemex_mokuteki_kbn = $this->getMokutekiKbn();
        // Msi_Sys_Utils::debug( '*@*@*@*@*@*@ getMokutekiKbn ===>' . $this->getMokutekiKbn() . " lvl0,1,2==>" 
        //                      . implode(",", [ $this->curCatLvl0(), $this->curCatLvl1(), $this->curCatLvl2() ]) );

        $catInfo = $this->getItemCatInfo();
        // Msi_Sys_Utils::debug( '*** @@@@@@@@@@@@ catInfo=>' . Msi_Sys_Utils::dump($catInfo) );
        if ( isset($catInfo) ) {
            $renderer->view->itemex_catInfo = $catInfo;
        }

        $renderer->view->isItemListDispByQuantityOrder = $this->isItemListDispByQuantityOrder() ? '1' : '0';

        if ( $this->getDispStyleType() == 1 ) { // 摘要を表示しない
            $renderer->view->flg_no_disp_tkiyo = true;
        }

        // $renderer->view->flg_no_input_shohin_nm = true;
    }

    /**
     * サービス料表記 取得
     *
     * <AUTHOR> Mihara
     * @since 2015/03/xx
     */
    protected function _get_service_nm()
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $service_nm = '奉仕料';
        $rec = DataMapper_KaisyaInfo::findOne($db);
        if ( $rec && strlen($rec['service_nm']) > 0 ) {
            $service_nm = $rec['service_nm'];
        }
        return $service_nm;
    }
    
    /**
     * 商品選択サブ画面要素生成 Tpl 前処理
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    protected function genItemPickerPreHook()
    {
    }

    /**
     * 商品選択サブ画面要素生成 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param Zend_Controller_Action_Helper_ViewRenderer  $renderer
     */
    protected function setItemPickerTpl($renderer)
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        $tpl = 'picker-base';
        $path = sprintf("juchu/itemex/%s.tpl", $tpl);
        $renderer->setViewScriptPathSpec($path);

        $renderer->view->picker_item_tpl = $this->itemPickerCont();

        $lvl2OptTab = $this->lvl2OptTab();
        if ( $lvl2OptTab ) {
            $renderer->view->is_lvl2_opt_tab = true;
            $renderer->view->picker_lvl2_opt_tpl = $lvl2OptTab;
            $renderer->view->is_lvl2_opt_tab_1_on = false;
            $renderer->view->picker_lvl2_opt_cap = $this->lvl2OptTabCap();
        }

        $cmt_tpl = $this->itemPickerCmtTpl();
        if ( $cmt_tpl ) {
            $renderer->view->picker_cmt_tpl = $cmt_tpl;
        }

        $footer_tpl = $this->itemPickerFooterTpl();
        if ( $footer_tpl ) {
            $renderer->view->picker_footer_tpl = $footer_tpl;
        }
    }

    /**
     * 表示区分(カテゴリ区分)のキャプションを取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param   integer   $tab_no  タブ番号
     * @return  string
     */
    protected function getCatCapByTabNo($tab_no)
    {
        $curLvl1 = $this->curCatLvl1();
        $lvl1Tbl = $this->tblInfoCatLvl1( $curLvl1 );
        // Msi_Sys_Utils::debug( '*** tblInfoCatLvl1=>' . Msi_Sys_Utils::dump($lvl1Tbl) );

        if ( isset($lvl1Tbl['_child']) ) {
            foreach ( $lvl1Tbl['_child'] as $rec ) {
                if ( (int)$rec['tab_no'] === (int)$tab_no ) {
                    return $rec['cap'];
                }
            }
        }
        return '';
    }

    /**
     * 付加情報タブ tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string
     */
    protected function lvl2OptTab()
    {
        return '';
    }

    /**
     * 付加情報タブ キャプション 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string
     */
    protected function lvl2OptTabCap()
    {
        return '追加情報';
    }

    /**
     * 商品選択サブ画面要素生成 コンテンツ tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string
     */
    protected function itemPickerCont()
    {
        return 'picker-item-multi.tpl';
    }

    /**
     * 商品選択サブ画面要素生成 コメント表示 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string
     */
    protected function itemPickerCmtTpl()
    {
        return '';
    }

    /**
     * 商品選択サブ画面要素生成 フッタ表示 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @return  string
     */
    protected function itemPickerFooterTpl()
    {
        return '';
    }

    /**
     * 商品選択サブ画面要素生成 Tpl 後処理
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    protected function genItemPickerPostHook()
    {
        if ( $this->curCatLvl0() == static::CAT_LVL0_RYORI ) {
            // $this->pickerPostHookRyori();
            // ryoriFooterAdj() にて処理
        }
    }

    /**
     * 選択商品リスト画面要素生成
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    public function genItemList()
    {
        $renderer = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');

        $legacyIteminfo = $this->getJuchuItemexInfoAdapter();
        // list($category_kbn, $kbn1, $kbn2) = $this->getItemLegacyParams();
        // $item_list = $legacyIteminfo->getItem($category_kbn, $kbn1, $kbn2);

        $item_list = $legacyIteminfo->getTargetedItems(null, 'ORDER BY tab_no, disp_no, shohin_cd'); // kbn2 等も含む

        // Msi_Sys_Utils::debug( '*** item_list=>' . Msi_Sys_Utils::dump($item_list) );

        // 未登録分の反映
        $newData = array();
        if ( isset($this->_arrCtxt['dataCol']) && isset($this->_arrCtxt['dataCol']['new']) ) {
            $newData = $this->_arrCtxt['dataCol']['new'];
        }
        // Msi_Sys_Utils::debug( '*** newData=>' . Msi_Sys_Utils::dump($newData) );

        $item_shohin_cds = array(); // 重複した shohin_cd を弾く XXX?
        $totals = array( 'total'=>0 );
        $item_list2 = array();
        foreach ( $item_list as $rec ) {
            $shohin_cd = $rec['shohin_cd'];

            // 2016/03/04 mihara タブを超えて一意の商品CD
            $shohin_cd = preg_replace( '/--\d*$/', '', $shohin_cd );
            $shohin_cd = $shohin_cd ."--". $rec['shohin_kbn'];
            $rec['shohin_cd'] = $shohin_cd;

            if ( $rec['quantity'] > 0 ) {
                if ( array_key_exists($shohin_cd, $item_shohin_cds) ) {
                    continue;
                }
                if ( ! array_key_exists($shohin_cd, $newData) ) { // 未登録データなし
                    $item_shohin_cds[ $rec['shohin_cd'] ] = $rec; // XXX?
                    $rec['_price'] = $rec['quantity'] * $rec['hanbai_tnk'];
                    if ( $rec['hoshi_prc'] ) $rec['_price'] += $rec['hoshi_prc'];

                    $totals['total'] += $rec['_price'];
                    $rec['_tab_cap'] = $this->getCatCapByTabNo($rec['tab_no']);
                    // 料理で飲物等の場合は区分を表示しない   2015/10/23 mihara
                    if ( $rec['dai_bunrui_cd'] == '0030' && ($rec['kbn2'] == 1 || $rec['kbn2'] == 2) ) {
                        $rec['_tab_cap'] = '';
                    }
                    $rec['shohin_tkiyo_nm'] = App_Utils2::expandShohinTkiyoEasy($rec['shohin_tkiyo_nm'], $rec); // 2015/06/29 mihara
                    $item_list2[] = $rec;
                    continue;
                }
            }
            if ( array_key_exists($shohin_cd, $item_shohin_cds) ) { // 2015/10/23 mihara
                continue;
            }
            if ( array_key_exists($shohin_cd, $newData) ) { // 未登録データあり
                $rec = array_merge($rec, $newData[$shohin_cd]);
                $item_shohin_cds[ $rec['shohin_cd'] ] = $rec; // XXX?
                $rec['hanbai_tnk'] = $rec['price'];
                $rec['_price'] = $rec['quantity'] * $rec['hanbai_tnk'];
                if ( isset($rec['shohintkiyonm']) ) { // 2015/05/22
                    $rec['shohin_tkiyo_nm'] = $rec['shohintkiyonm'];
                }
                if ( isset($rec['shohintkiyonm2']) ) { // 2015/11/02
                    $rec['shohin_tkiyo_nm2'] = $rec['shohintkiyonm2'];
                }
                if ( isset($rec['shohinnm']) ) {
                    $rec['shohin_nm'] = $rec['shohinnm'];
                }
                if ( $rec['chargekbn'] ) {
                    $hosi_ritu = $this->getHosiritu(); // cf. Juchu_JuchuAbstract::getHosiritu
                    // Msi_Sys_Utils::debug( '*** hosi_ritu=>' . Msi_Sys_Utils::dump($hosi_ritu) );
                    // cf. Juchu_JuchuAbstract
                    $hoshiprc = App_ClsTaxLib::CalcHosiPrc( $rec['_price'], $hosi_ritu["hasu_kbn"], $hosi_ritu["zei_rtu"], 1 );
                    $rec['hoshi_prc'] = $hoshiprc;
                    $rec['_price'] += $rec['hoshi_prc'];
                } else {
                    $rec['hoshi_prc'] = 0;
                }
                $rec['_etc'] = '変更あり';
                $totals['total'] += $rec['_price'];
                $rec['_tab_cap'] = $this->getCatCapByTabNo($rec['tab_no']);
                // 料理で飲物等の場合は区分を表示しない   2015/10/23 mihara
                if ( $rec['dai_bunrui_cd'] == '0030' && ($rec['kbn2'] == 1 || $rec['kbn2'] == 2) ) {
                    $rec['_tab_cap'] = '';
                }
                $rec['shohin_tkiyo_nm'] = App_Utils2::expandShohinTkiyoEasy($rec['shohin_tkiyo_nm'], $rec); // 2015/06/29 mihara
                $item_list2[] = $rec;
            }
        }
        // Msi_Sys_Utils::debug( '*** item_list2=>' . Msi_Sys_Utils::dump($item_list2) );

        // 指定されているなら、購入数の大きい順にソート
        if ( $this->isItemListDispByQuantityOrder() ) {
            $rtn = usort( $item_list2, function($a, $b) {
                    return $a['quantity'] == $b['quantity'] ? ( $a['disp_no'] > $b['disp_no'] ? +1 : -1 )
                        : ($a['quantity'] > $b['quantity'] ? -1 : +1); } );
        }

        $renderer->view->list_data = $item_list2;
        if ( count($item_list2) > 0 ) {
           $renderer->view->totals = $totals;
        }

        $this->genItemListPreHook();

        $this->setItemListTpl($renderer);

        $this->genItemListPostHook();

        // 最終調整
        $this->_fixup($renderer);
    }

    /**
     * 拡張版択一選択において、現登録データから同じカテゴリの商品選択個数をゼロにしたデータを追加する
     *
     * <AUTHOR> Mihara
     * @since 2015/10/xx
     * @param  array  $newData    更新予定データ
     * @param  array  $dataEx     追加パラメタ
     * @return array              現登録データを反映した更新予定データ
     */
    protected function seloneExProc($newData, $dataEx)
    {
        $chu_bunrui_cd = $dataEx['chu_bunrui_cd'];
        $shohin_cd     = $dataEx['shohin_cd'];

        // Msi_Sys_Utils::debug( 'seloneExProc==> (chu_bunrui_cd,shohin_cd)=>' . Msi_Sys_Utils::dump([$chu_bunrui_cd,$shohin_cd]) );
        // Msi_Sys_Utils::debug( '@#$% seloneExProc newData==>' . Msi_Sys_Utils::dump($newData) );

        $db = Msi_Sys_DbManager::getMyDb();
        $denpyoMsi = $this->getJuchuDenpyoMsi($db);
        // Msi_Sys_Utils::debug( '@#$% seloneExProc denpyoMsi==>' . Msi_Sys_Utils::dump($denpyoMsi) );

        $newDataRtn = $newData;

        // newData で現在変更された以外の shohin_cd データの個数はゼロとする.
        foreach ( $newDataRtn as &$rec00 ) {
            if ( $rec00['chubunruicd'] == $chu_bunrui_cd && $rec00['shohincd'] != $shohin_cd ) {
                $rec00['quantity'] = 0;
            }
        }

        // テンプレートして現設定データを１つ取り出す。１つもない場合(変更がないと言える)はここで抜けてよい
        // テンプレートは対応する項目がない場合にこの項目値を設定する
        $tmplData = null;
        if ( array_key_exists($shohin_cd, $newDataRtn) ) {
            $tmplData = $newDataRtn[ $shohin_cd ]; 
        } else {
            foreach ( $newDataRtn as $rec11 ) {
                if ( $rec11['chubunruicd'] == $chu_bunrui_cd ) {
                    $tmplData = $rec11;
                    break;
                }
            }
        }
        if ( $tmplData === null ) {
            return $newDataRtn;
        }

        // 現受注明細データから同じ chu_bunrui_cd のデータを抜き出す
        foreach ( $denpyoMsi as $rec ) {
            if ( $rec['chu_bunrui_cd'] == $chu_bunrui_cd ) {
                $shohin_cd00 = $rec['shohin_cd'];
                if ( !array_key_exists($shohin_cd00, $newDataRtn) ) { // データがない場合
                    if ( $rec['juchu_tnk'] < 0 ) { // 値引きは対象としない
                        continue;
                    }
                    $data = array(
                                  "shohincd"        => $shohin_cd00,
                                  "quantity"        => 0, // !!
                                  "daibunruicd"     => $rec['dai_bunrui_cd'],
                                  "chubunruicd"     => $rec['chu_bunrui_cd'],
                                  "shohinkbn"       => $rec['shohin_kbn'],
                                  "referuchiwkno"   => $rec['refer_uchiwk_no'],
                                  "sagakukeisangrp" => $rec['sagaku_keisan_grp'],
                                  "price"           => $rec['juchu_tnk'],
                                  "shohinnm"        => $rec['shohin_nm'],
                                  "chargekbn"       => $rec['hoshi_umu_kbn'],
                                  "category_kbn"    => $tmplData['category_kbn'],
                                  "shohintkiyonm"   => $rec['shohin_tkiyo_nm'],
                                  "shohintkiyonm2"  => isset($rec['shohintkiyonm2']) ? $rec['shohintkiyonm2'] : '',
                                  "mokuteki_kbn"    => $rec['mokuteki_kbn'],
                                  );
                    $newDataRtn[ $shohin_cd00 ] = $data;
                    // Msi_Sys_Utils::debug( '@#$% seloneExProc added data @@@@@@==>' . Msi_Sys_Utils::dump($data) );
                }
            }                

        }

        // Msi_Sys_Utils::debug( '@#$% seloneExProc newData==>' . Msi_Sys_Utils::dump($newData) );
        // Msi_Sys_Utils::debug( '@#$% seloneExProc newDataRtn==>' . Msi_Sys_Utils::dump($newDataRtn) );

        return $newDataRtn;
    }

    /**
     * 選択商品リスト画面要素生成 Tpl 前処理
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    protected function genItemListPreHook()
    {
    }

    /**
     * 商品表示を数量順にするか
     *
     * <AUTHOR> Mihara
     * @since 2015/06/xx
     */
    protected function isItemListDispByQuantityOrder()
    {
        return true;
    }

    /**
     * 選択商品リスト画面要素生成 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param Zend_Controller_Action_Helper_ViewRenderer  $renderer
     */
    protected function setItemListTpl($renderer)
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        $tpl = 'list-base';
        $path = sprintf("juchu/itemex/%s.tpl", $tpl);
        $renderer->setViewScriptPathSpec($path);

        $renderer->view->picker_list_tpl = $this->itemListCont();
    }

    /**
     * 選択商品リスト画面要素生成 コンテンツ tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     * @param string
     */
    protected function itemListCont()
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            return 'list-item-houshi.tpl';
        }

        return 'list-item-std.tpl';
    }

    /**
     * 選択商品リスト画面要素生成 Tpl 後処理
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    protected function genItemListPostHook()
    {
    }

    /**
     * 保存処理
     *   cf. Juchu_JuchuinfoSubAbstract::save()
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    public function save()
    {
        $this->_isCompatCtxt = true; // getCategoryKbn() を fake するため

        $req = Msi_Sys_Utils::getRequestObject();

        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
        $dataDecoCol = Msi_Sys_Utils::json_decode($req->getPost('dataDecoColJson'));
        $dataDecoCol2 = Msi_Sys_Utils::json_decode($req->getPost('dataDecoColJson2'));
        // Msi_Sys_Utils::debug( '*** JuchuItemex00::save() dataApp=>' . Msi_Sys_Utils::dump($dataApp) );
        // Msi_Sys_Utils::debug( '*** JuchuItemex00::save() dataCol=>' . Msi_Sys_Utils::dump($dataCol) );
        $db = Msi_Sys_DbManager::getMyDb();
        // 選択された商品が発注済みか確認する
        $chkMsg = App_HachuLib::chkItemexHachuiOrder($db, $this->_sekoNo, $dataCol); 
        if(strlen($chkMsg) > 0){
            $data = array(
                'status'    => 'ERR',
                'msg'       => $chkMsg,
            );
            Msi_Sys_Utils::outJson($data);
            return ;
        }
        $this->saveCatTabMsi($db, $dataDecoCol);
        foreach ($dataDecoCol as $rec) {
            if (isset($rec['child_data'])) {
                $this->saveCatTabMsi($db, $rec['child_data']);
            }
        }
        // 目的区分ごとに管理、登録する
        // $dataAppForIteminfo = array();
        $dataAppForIteminfoMoku = array();
        foreach ( $dataCol['new'] as $_shohinCd => $rec ) {
            $shohinCd = preg_replace( '/--\d*$/', '', $_shohinCd ); // 2016/03/04 mihara shohin_kbn カット
            
            $mokuteki_kbn = $rec['mokuteki_kbn'];
            // 軽減税率区分  keigen mihara begin
            $zei_cd = $rec["zei_cd"];
            list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd); // keigen mihara begin

            $newRec = array(
                            'item'            => $shohinCd,
                            'daibunruicd'     => $rec['daibunruicd'],
                            'chubunruicd'     => $rec['chubunruicd'],
                            'shohinkbn'       => $rec['shohinkbn'],
                            'shohinbumoncd'   => $rec['shohinbumoncd'],
                            'quantity'        => $rec['quantity'],
                            'price'           => $rec['price'],
                            'category_kbn'    => $rec['category_kbn'],
                            'shohinnm'        => $rec['shohinnm'],
                            'shohintkiyonm'   => '',
                            'shohintkiyonm2'  => isset($rec['shohintkiyonm2']) ? $rec['shohintkiyonm2'] : '',
                            'referuchiwkno'   => '',
                            'sagakukeisangrp' => '',
                            'kbn1'            => 0,
                            'kbn2'            => 0,
                            'charge'          => 0,
                            'delItem'         => $rec['quantity'] != 0 ? 1 : 0,
                            'mokuteki_kbn'    => $mokuteki_kbn,
                            'zei_cd'            => $zei_cd, // 軽減税率対応  keigen mihara
                            'zei_rtu'           => $zei_rtu, // 軽減税率対応  keigen mihara
                            'zei_hasu_kbn'      => $zei_hasu_kbn, // 軽減税率対応  keigen mihara
                            'reduced_tax_rate'  => $reduced_tax_rate, // 軽減税率対応  keigen mihara
                            '_raw'            => $rec,
                            );
            if ( isset($rec['shohintkiyonm']) ) {
                $newRec['shohintkiyonm'] = App_Utils2::expandShohinTkiyoEasy( $rec['shohintkiyonm'], $rec,
                                                                              array('shohin_cd'     => 'shohincd',
                                                                                    'dai_bunrui_cd' => 'daibunruicd',
                                                                                    'chu_bunrui_cd' => 'chubunruicd',
                                                                                    'shohin_kbn'    => 'shohinkbn') );
            }
            if ( isset($rec['chargekbn']) && $rec['chargekbn'] ) {
                $newRec['charge'] = $rec['chargekbn'] == 1 ? 1 : 0; // 2015/06/22 org:   $newRec['charge'] = 1;
            }
            if ( isset($rec['referuchiwkno']) && $rec['referuchiwkno'] ) {
                $newRec['referuchiwkno'] = $rec['referuchiwkno'];
            }
            if ( isset($rec['sagakukeisangrp']) && $rec['sagakukeisangrp'] ) {
                $newRec['sagakukeisangrp'] = $rec['sagakukeisangrp'];
            }
            if ( isset($rec['kbn2']) && $rec['kbn2'] ) { // 飲物（概算）等
                $newRec['kbn2'] = $rec['kbn2'];
            }
            if ( !array_key_exists($mokuteki_kbn, $dataAppForIteminfoMoku) ) {
                $dataAppForIteminfoMoku[$mokuteki_kbn] = array();
            }
            $dataAppForIteminfoMoku[$mokuteki_kbn][] = $newRec;
            // $dataAppForIteminfo[] = $newRec;
        }
        // Msi_Sys_Utils::debug( '*** JuchuItemex00::save() dataAppForIteminfo=>' . Msi_Sys_Utils::dump($dataAppForIteminfo) );

        $legacyIteminfo = $this->getJuchuItemexInfoAdapter();

        // 目的区分ごとに登録
        foreach ( $dataAppForIteminfoMoku as $_mokuteki_kbn => $dataAppForIteminfo00 ) {
            $this->_mokuteki_kbn_super = $_mokuteki_kbn;
            App_Utils2::adjGojokaiNebiki2( $this->_sekoNo, $dataAppForIteminfo00 ); // 値引額調整
            App_Utils2::adjGojokaiNebiki3( $this->_sekoNo, $dataAppForIteminfo00 ); // 値引額調整
            // Msi_Sys_Utils::debug( '*** dataAppForIteminfo00 =>' . Msi_Sys_Utils::dump($dataAppForIteminfo00) );
            $data = $legacyIteminfo->save( $dataAppForIteminfo00 ); // $data は上書きされる
            unset($this->_mokuteki_kbn_super);
            if ( ! (isset($data['status']) && $data['status'] == 'OK') ) {
                break; // エラーの場合は抜ける
            }
        }

        $this->savePostHook($dataApp, $dataCol);
        $db->commit();
        // 登録データがない場合(アレルギー確認登録だけなど)、正常終了として $data を作成するパッチ
        // cf. Juchu_JuchuinfoSubAbstract::save()
        if ( !isset($data) ) {
            $sekoNo = $this->_sekoNo;
            // サイドメニューデータを取得する
            if ($this->getCategoryKbn() == 6) { //返礼品を更新
                $sideMenuData = Juchu_Utils::getSideMenuData('gift', $sekoNo);
            } elseif ($this->getCategoryKbn() == 7) { //料理を更新
                $sideMenuData = Juchu_Utils::getSideMenuData('food', $sekoNo);
            } else { //選択商品を更新
                $sideMenuData = Juchu_Utils::getSideMenuData('item', $sekoNo);
            }
            $data = array(
                          'status'       => 'OK',
                          'dataSideMenu' => $sideMenuData,
                          'msg'          => '更新しました',
                          'ret'          => 0, // $ret,
                          );
        }

        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 保存 追加処理
     *
     * <AUTHOR> Mihara
     * @since 2014/12/xx
     */
    protected function savePostHook($dataApp, $dataCol)
    {
    }

	/**
     * 画像商品税込表示区分を取得する
     *
     * <AUTHOR> Kayo
     * @since 2015/05/01
     * @return  string $ret	画像商品税込表示区分
     */
	public function getGazoShohinZeiKbn( )
	{
		$db = Msi_Sys_DbManager::getMyDb();
        
        if (!App_Utils::isFukusuKaisyaKanri()) { // 複数会社管理なし 
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_shohin_zei_kbn  -- 画像商品税込表示区分
FROM	kaisya_info					
WHERE   delete_flg = 0    -- 2016/10/07 ADD Kayo
END_OF_SQL
            );
        } else {
            // 複数会社管理なし
            // 複数会社対応 2016/09/06 ADD Kayo 
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_shohin_zei_kbn  -- 画像商品税込表示区分
FROM	kaisya_info
WHERE kaisya_cd = :kaisya_cd
AND delete_flg = 0    
END_OF_SQL
         ,array('kaisya_cd' => $curKaisyaCd));
        }    
        $gazo_shohin_zei_kbn = 0;
        if (count($select) > 0) {
            $gazo_shohin_zei_kbn = $select[0]['gazo_shohin_zei_kbn'];
        }
		return $gazo_shohin_zei_kbn;
	}

	/**
     * 画像商品税込表示区分を取得する
     *
     * <AUTHOR> Kayo
     * @since 2017/03/15
     * @return  string $ret 画像商品金額表示区分
     */
	public function getGazoShohinPrcKbn( )
	{
		$db = Msi_Sys_DbManager::getMyDb();
        
        if (!App_Utils::isFukusuKaisyaKanri()) { // 複数会社管理なし 
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_shohin_prc_kbn  -- 画像商品金額表示区分
FROM	kaisya_info					
WHERE   delete_flg = 0
END_OF_SQL
            );
        } else {
            // 複数会社管理なし
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_shohin_prc_kbn  -- 画像商品金額表示区分
FROM	kaisya_info
WHERE kaisya_cd = :kaisya_cd
AND delete_flg = 0    
END_OF_SQL
         ,array('kaisya_cd' => $curKaisyaCd));
        }    
        $gazo_shohin_prc_kbn = 0;
        if (count($select) > 0) {
            $gazo_shohin_prc_kbn = $select[0]['gazo_shohin_prc_kbn'];
        }
		return $gazo_shohin_prc_kbn;
	}
    
    /**
     * 画像商品会員価格表示区分を取得する
     *
     * <AUTHOR> Sai
     * @since 2017/03/29
     * @return  string $ret 画像商品会員価格表示区分
     */
    public function getGazoKainPrcKbn() {
        $db = Msi_Sys_DbManager::getMyDb();

        if (!App_Utils::isFukusuKaisyaKanri()) { // 複数会社管理なし 
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_kain_prc_kbn 
FROM	kaisya_info					
WHERE   delete_flg = 0
END_OF_SQL
            );
        } else {
            // 複数会社管理なし
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $select = $db->easySelect(<<< END_OF_SQL
SELECT gazo_kain_prc_kbn
FROM	kaisya_info
WHERE kaisya_cd = :kaisya_cd
AND delete_flg = 0    
END_OF_SQL
                    , array('kaisya_cd' => $curKaisyaCd));
        }
        $gazo_kain_prc_kbn = 0;
        if (count($select) > 0) {
            $gazo_kain_prc_kbn = $select[0]['gazo_kain_prc_kbn'];
        }
        return $gazo_kain_prc_kbn;
    }

    /**
     * lvl2 U/I のタイプを返す
     *
     * <AUTHOR> Mihara
     * @since 2015/07/xx
     * @return  string sel
     */
	public function lvl2UiType()
    {
        return 'dropDown';
        // return 'tabBtn';
    }

	/**
     * lvl2 U/I をタブボタンで提供する
     *   現状、default はドロップダウン(select2利用)
     *
     * <AUTHOR> Mihara
     * @since 2015/07/xx
     * @param  array   $lvl2  lvl2 選択要素
     * @param  Zend_Controller_Action_Helper_ViewRenderer  $renderer
     * @return void
     */
	public function lvl2UiTabBtn($lvl2, $renderer)
    {
        $view = $renderer->view;

        // Msi_Sys_Utils::debug( 'lvl2 =>' . Msi_Sys_Utils::dump($lvl2) );

        // cf. Juchu_MitsuController#itemexAction()
        // レベル２タブ幅の設定
        $isAbleMultiLine = true;
        $maxTabOneLine = 8; // 6,7,8,99
        $cntOfTabItems = count($lvl2); // optCatLvl2);
        if ($cntOfTabItems <= 4) {
            $view->cat_lvl2_width = '20%';
        } else if ($cntOfTabItems <= $maxTabOneLine || !$isAbleMultiLine) {
            $view->cat_lvl2_width = sprintf("%0.1f%%", 85 / $cntOfTabItems); // 15% for list/block buttons. 均等割
        } else {
            $view->is_lvl2_tab_arrow = true;
            $view->lvl2_tab_width = "90%"; // for arrow etc. not .lvl2_tab height: 28px 
            if ($maxTabOneLine == 8) { // 7 tabs
                $view->cat_lvl2_width = "12.7%";
            } else if ($maxTabOneLine == 7) { // 6 tabs
                $view->cat_lvl2_width = "15%";
            } else { // 5 tabs
                $view->cat_lvl2_width = "17%";
            }
        }


    }

    /**
     * 商品選択装飾保存処理 
     *
     * <AUTHOR> Sai
     * @since  2017/11/14
     * @param Msi_Sys_Db $db db
     * @param array $dataDecoCol 商品選択タブデータ
     * @return int 更新件数
     */
    private function saveCatTabMsi($db, $dataDecoCol) {
        $cnt = 0;
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'child_data');
        foreach ($dataDecoCol as $rec) {
            // カテゴリ別タブ管理明細存在チェック
            $sqlSelect = "
            SELECT
                seko_no          -- 施行番号
            FROM
                category_tab_msi 
            WHERE
                    seko_no = :seko_no
                AND kaisya_cd = :kaisya_cd
                AND bumon_cd = :bumon_cd
                AND tab_gamen_kbn = :tab_gamen_kbn
                AND item_no = :item_no
                AND upper_tab_no = :upper_tab_no
                AND tab_no = :tab_no
                AND delete_flg = 0
                ";
            $params = array('seko_no' => $this->_sekoNo, 'kaisya_cd' => $rec['kaisya_cd'], 'bumon_cd' => $rec['bumon_cd']
                    , 'tab_gamen_kbn' => $rec['tab_gamen_kbn'], 'item_no' => $rec['item_no'], 'upper_tab_no' => $rec['upper_tab_no'], 'tab_no' => $rec['tab_no']);
            $select = $db->easySelect($sqlSelect, $params);
            if (count($select) === 0) {
                $insert = $params;
                $insert['item_deco_kbn'] = $rec['item_deco_kbn'];
                $insert['moushikomi_kbn'] = $this->_moushiKbn;
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("category_tab_msi", $insert, $except);
            } else {
                $except = array_merge($except, $params);
                // 条件部
                $where = $params;
                $where['delete_flg'] = 0;  
                $update['item_deco_kbn'] = $rec['item_deco_kbn'];
                $update['moushikomi_kbn'] = $this->_moushiKbn;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("category_tab_msi", $update, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }
    
    /**
     * 商品拡張区分情報取得処理 
     *
     * <AUTHOR> Sai
     * @since  2017/12/12
     * @param array $dataExt 商品データ
     * @return array 商品拡張区分情報
     */
    public function getItemDialogAppData($dataExt) {
        $extKbn = array('s_free_kbn1' => array(),'s_free_kbn2' => array(),'s_free_kbn3' => array(),'s_free_kbn4' => array()
            ,'s_free_kbn5' => array(),'s_free_kbn6' => array(),'s_free_kbn7' => array(),'s_free_kbn8' => array()
            ,'s_free_kbn9' => array(),'s_free_kbn10' => array(),'s_free_kbn11' => array(),'s_free_kbn12' => array()
            ,'s_free_kbn13' => array(),'s_free_kbn14' => array(),'s_free_kbn15' => array(),'s_free_kbn16' => array()
            ,'s_free_kbn17' => array(),'s_free_kbn18' => array(),'s_free_kbn19' => array(),'s_free_kbn20' => array()
            );
        $db = Msi_Sys_DbManager::getMyDb();
        $params['__x1'] = array( 'x', '(
                                        code_kbn = :x1_5390 OR code_kbn =  :x1_5400 
                                        OR code_kbn =  :x1_5410 OR code_kbn =  :x1_5420 
                                        OR code_kbn =  :x1_5430 OR code_kbn =  :x1_5440 
                                        OR code_kbn =  :x1_5450 OR code_kbn =  :x1_5460 
                                        OR code_kbn =  :x1_5470 OR code_kbn =  :x1_5480 
                                        OR code_kbn =  :x1_5490 OR code_kbn =  :x1_5495 OR code_kbn =  :x1_5500 
                                        OR code_kbn =  :x1_5510 OR code_kbn =  :x1_5520 
                                        OR code_kbn =  :x1_5530 OR code_kbn =  :x1_5540 
                                        OR code_kbn =  :x1_5550 OR code_kbn =  :x1_5560 
                                        OR code_kbn =  :x1_5570 OR code_kbn =  :x1_5580 
                                        OR code_kbn =  :x1_5590 OR code_kbn =  :x1_5600 
                                        OR code_kbn =  :x1_5610 OR code_kbn =  :x1_5620 
                                        OR code_kbn =  :x1_5630 OR code_kbn =  :x1_5640 
                                        OR code_kbn =  :x1_5650 OR code_kbn =  :x1_5660 
                                        OR code_kbn =  :x1_5670 OR code_kbn =  :x1_5680 
                                        OR code_kbn =  :x1_5690 OR code_kbn =  :x1_5700 
                                        OR code_kbn =  :x1_5710
                                        OR code_kbn =  :x1_5770 OR code_kbn =  :x1_5780
                                        OR code_kbn =  :x1_5790 OR code_kbn =  :x1_5800
                                        OR code_kbn =  :x1_2500
                                        OR code_kbn =  :x1_6280 OR code_kbn =  :x1_6760
                                        )'
                            , array(
                                    'x1_5390'=>self::CODE_KBN_5390, 'x1_5400'=>self::CODE_KBN_5400
                                    ,'x1_5410'=>self::CODE_KBN_5410, 'x1_5420'=>self::CODE_KBN_5420
                                    ,'x1_5430'=>self::CODE_KBN_5430, 'x1_5440'=>self::CODE_KBN_5440
                                    ,'x1_5450'=>self::CODE_KBN_5450, 'x1_5460'=>self::CODE_KBN_5460
                                    ,'x1_5470'=>self::CODE_KBN_5470, 'x1_5480'=>self::CODE_KBN_5480
                                    ,'x1_5490'=>self::CODE_KBN_5490, 'x1_5495'=>self::CODE_KBN_5495, 'x1_5500'=>self::CODE_KBN_5500
                                    ,'x1_5510'=>self::CODE_KBN_5510, 'x1_5520'=>self::CODE_KBN_5520
                                    ,'x1_5530'=>self::CODE_KBN_5530, 'x1_5540'=>self::CODE_KBN_5540
                                    ,'x1_5550'=>self::CODE_KBN_5550, 'x1_5560'=>self::CODE_KBN_5560
                                    ,'x1_5570'=>self::CODE_KBN_5570, 'x1_5580'=>self::CODE_KBN_5580
                                    ,'x1_5590'=>self::CODE_KBN_5590, 'x1_5600'=>self::CODE_KBN_5600
                                    ,'x1_5610'=>self::CODE_KBN_5610, 'x1_5620'=>self::CODE_KBN_5620
                                    ,'x1_5630'=>self::CODE_KBN_5630, 'x1_5640'=>self::CODE_KBN_5640
                                    ,'x1_5650'=>self::CODE_KBN_5650, 'x1_5660'=>self::CODE_KBN_5660
                                    ,'x1_5670'=>self::CODE_KBN_5670, 'x1_5680'=>self::CODE_KBN_5680
                                    ,'x1_5690'=>self::CODE_KBN_5690, 'x1_5700'=>self::CODE_KBN_5700
                                    ,'x1_5710'=>self::CODE_KBN_5710
                                    ,'x1_5770'=>self::CODE_KBN_5770, 'x1_5780'=>self::CODE_KBN_5780
                                    ,'x1_5790'=>self::CODE_KBN_5790, 'x1_5800'=>self::CODE_KBN_5800
                                    ,'x1_2500'=>self::CODE_KBN_2500
                                    ,'x1_6280'=>self::CODE_KBN_6280, 'x1_6760'=>self::CODE_KBN_6760
                                   ) );
        $params['__etc_orderby'] = array('code_kbn', 'disp_nox', 'kbn_value_cd_num');
        $codeNameMst = DataMapper_CodeNmMst::find($db, $params);
        
        //1：祭壇	
        //2：湯灌（他社）	
        //3：湯灌（自社）	
        //4：ドライアイス	
        //5：霊柩車	
        //6：バス	
        //7：新聞掲載	
        //8：男性用礼服	
        //9：紋付	
        //10：女性用喪服	
        //11：女性フォーマル	
        //12：着付	
        //13：ヘアセット	
        //14：着付出張料
        //15：返礼品	
        //16：後付車	
        //17：式場安置料
        $dialog_sbt = $dataExt['dialog_sbt'];
        if ($dialog_sbt === '1') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5390);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5400);
        } else if ($dialog_sbt === '2') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5410);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5420);
            $extKbn['s_free_kbn3'] = $this->filter($codeNameMst, self::CODE_KBN_5430);
            $extKbn['s_free_kbn4'] = $this->filter($codeNameMst, self::CODE_KBN_5440);
            $extKbn['s_free_kbn5'] = $this->filter($codeNameMst, self::CODE_KBN_5450);
            $extKbn['s_free_kbn6'] = $this->filter($codeNameMst, self::CODE_KBN_5460);
            $extKbn['s_free_kbn7'] = $this->filter($codeNameMst, self::CODE_KBN_5470);
            $extKbn['s_free_kbn8'] = $this->filter($codeNameMst, self::CODE_KBN_5480);
            $extKbn['s_free_kbn9'] = $this->filter($codeNameMst, self::CODE_KBN_5490);
            $extKbn['s_free_kbn10'] = $this->filter($codeNameMst, self::CODE_KBN_6280);
        } else if ($dialog_sbt === '3') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5500);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5510);
            $extKbn['s_free_kbn3'] = $this->filter($codeNameMst, self::CODE_KBN_5520);
            $extKbn['s_free_kbn4'] = $this->filter($codeNameMst, self::CODE_KBN_5440);
            $extKbn['s_free_kbn5'] = $this->filter($codeNameMst, self::CODE_KBN_5450);
            $extKbn['s_free_kbn6'] = $this->filter($codeNameMst, self::CODE_KBN_5460);
            $extKbn['s_free_kbn7'] = $this->filter($codeNameMst, self::CODE_KBN_5470);
            $extKbn['s_free_kbn10'] = $this->filter($codeNameMst, self::CODE_KBN_5495);
        } else if ($dialog_sbt === '4') {
        } else if ($dialog_sbt === '5') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5530);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5540);
            $extKbn['s_free_kbn3'] = $this->filter($codeNameMst, self::CODE_KBN_5560);
            $extKbn['s_free_kbn4'] = $this->filter($codeNameMst, self::CODE_KBN_5570);
            $extKbn['s_free_kbn5'] = $this->filter($codeNameMst, self::CODE_KBN_5580);
            $extKbn['s_free_kbn6'] = $this->filter($codeNameMst, self::CODE_KBN_5590);
            $extKbn['s_free_kbn7'] = $this->filter($codeNameMst, self::CODE_KBN_5600);
            $extKbn['s_free_kbn8'] = $this->filter($codeNameMst, self::CODE_KBN_5550);
            $extKbn['s_free_kbn9'] = $this->filter($codeNameMst, self::CODE_KBN_5610);
            $extKbn['s_free_kbn10'] = $this->filter($codeNameMst, self::CODE_KBN_5620);
            $extKbn['s_free_kbn1_dtl'] = $this->getBashoName($extKbn['s_free_kbn1']);
            $extKbn['s_free_kbn2_dtl'] = $this->getBashoName($extKbn['s_free_kbn2']);
        } else if ($dialog_sbt === '6') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5630);
            $extKbn['s_free_kbn2'] = $extKbn['s_free_kbn1'];
            $extKbn['s_free_kbn3'] = $extKbn['s_free_kbn1'];
            $extKbn['s_free_kbn4'] = $extKbn['s_free_kbn1'];
            $extKbn['s_free_kbn5'] = $extKbn['s_free_kbn1'];
        } else if ($dialog_sbt === '7') {
        } else if ($dialog_sbt === '8') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5640);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5650);
        } else if ($dialog_sbt === '9') {
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5660);
        } else if ($dialog_sbt === '10') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5670);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5680);
        } else if ($dialog_sbt === '11') {
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5690);
        } else if ($dialog_sbt === '12') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5700);
            $extKbn['s_free_kbn2'] = $extKbn['s_free_kbn1'];
        } else if ($dialog_sbt === '13') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5710);
            $extKbn['s_free_kbn2'] = $extKbn['s_free_kbn1'];
        } else if ($dialog_sbt === '14') {
        } else if ($dialog_sbt === '15') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5770);
            $extKbn['s_free_kbn2'] = $this->filter($codeNameMst, self::CODE_KBN_5780);
            $extKbn['s_free_kbn3'] = $this->filter($codeNameMst, self::CODE_KBN_5790);
            $extKbn['s_free_kbn4'] = $this->filter($codeNameMst, self::CODE_KBN_5800);
        } else if ($dialog_sbt === '16') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5630);
        } else if ($dialog_sbt === '17') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_2500);
        } else if ($dialog_sbt === '18') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_6760);
        } else if ($dialog_sbt === '19') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5630);
        } else if ($dialog_sbt === '20') {
            $extKbn['s_free_kbn1'] = $this->filter($codeNameMst, self::CODE_KBN_5630);
        }
        return $extKbn;
    }
    

    /**
     * コード名称マスタフィルター処理
     * 
     * <AUTHOR> Sai
     * @since      2014/02/14
     * @since      2017/12/12 Juchu_JuchuCustomerinfoComよりコピー
     * @param $dataCodeNameMst コード名称マスタ配列
     * @param $kbn 区分値コード数値
     * @param $key 通常はkbn_value_cd_numをキーにtureの場合はkbn_value_cdをキーに、
     * @return  array コード名称マスタ
     */
    private function filter($dataCodeNameMst, $kbn) {
        $this->_codeKbn = $kbn;
        $codeNames = array_filter($dataCodeNameMst, function ($item) {
            return $item['code_kbn'] === $this->_codeKbn;
        }
        );
        return $codeNames;
    }
    /**
     * 商品拡張情報取得処理 
     *
     * <AUTHOR> Sai
     * @since  2017/11/29
     * @param array $dataExt 商品データ
     * @return array 商品拡張情報
     */
    public function getItemDialogData($dataExt) {
        $whereAdd = '';
        if ($dataExt['shohin_ref_kbn'] === '2') { // 商品コードまで参照
            $shohin_cd = preg_replace('/--\d*$/', '', $dataExt['item']);
            $whereAdd = "AND sei.shohin_cd  = '" . $shohin_cd . "'";
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sei.kaisya_cd
                ,sei.bumon_cd
                ,sei.seko_no
                ,sei.dai_bunrui_cd
                ,sei.chu_bunrui_cd
                ,sei.shohin_kbn
                ,sei.shohin_cd
                ,skb.shohin_ref_kbn
                ,skb.dialog_sbt
                ,sei.k_free1
                ,sei.k_free2
                ,sei.k_free3
                ,sei.k_free4
                ,sei.k_free5
                ,sei.k_free6
                ,sei.k_free7
                ,sei.k_free8
                ,sei.k_free9
                ,sei.k_free10
                ,sei.k_free11
                ,sei.k_free12
                ,sei.k_free13
                ,sei.k_free14
                ,sei.k_free15
                ,sei.k_free16
                ,sei.k_free17
                ,sei.k_free18
                ,sei.k_free19
                ,sei.k_free20
                ,sei.s_free1
                ,sei.s_free2
                ,sei.s_free3
                ,sei.s_free4
                ,sei.s_free5
                ,sei.s_free6
                ,sei.s_free7
                ,sei.s_free8
                ,sei.s_free9
                ,sei.s_free10
                ,sei.s_free11
                ,sei.s_free12
                ,sei.s_free13
                ,sei.s_free14
                ,sei.s_free15
                ,sei.s_free16
                ,sei.s_free17
                ,sei.s_free18
                ,sei.s_free19
                ,sei.s_free20
                ,sei.v_free1
                ,sei.v_free2
                ,sei.v_free3
                ,sei.v_free4
                ,sei.v_free5
                ,sei.v_free6
                ,sei.v_free7
                ,sei.v_free8
                ,sei.v_free9
                ,sei.v_free10
                ,sei.v_free11
                ,sei.v_free12
                ,sei.v_free13
                ,sei.v_free14
                ,sei.v_free15
                ,sei.v_free16
                ,sei.v_free17
                ,sei.v_free18
                ,sei.v_free19
                ,sei.v_free20
                ,sei.text_free1
                ,sei.text_free2
                ,sei.text_free3
                ,sei.text_free4
                ,sei.text_free5
                ,sei.n_free1
                ,sei.n_free2
                ,sei.n_free3
                ,sei.n_free4
                ,sei.n_free5
                ,sei.n_free6
                ,sei.n_free7
                ,sei.n_free8
                ,sei.n_free9
                ,sei.n_free10
                ,TO_CHAR(sei.d_free1, 'YYYY/MM/DD') AS d_free1
                ,TO_CHAR(sei.d_free2, 'YYYY/MM/DD') AS d_free2
                ,TO_CHAR(sei.d_free3, 'YYYY/MM/DD') AS d_free3
                ,TO_CHAR(sei.d_free4, 'YYYY/MM/DD') AS d_free4
                ,TO_CHAR(sei.d_free5, 'YYYY/MM/DD') AS d_free5
                ,TO_CHAR(sei.d_free6, 'YYYY/MM/DD') AS d_free6
                ,TO_CHAR(sei.d_free7, 'YYYY/MM/DD') AS d_free7
                ,TO_CHAR(sei.d_free8, 'YYYY/MM/DD') AS d_free8
                ,TO_CHAR(sei.d_free9, 'YYYY/MM/DD') AS d_free9
                ,TO_CHAR(sei.d_free10, 'YYYY/MM/DD') AS d_free10
                ,TO_CHAR(sei.t_free1, 'HH24:MI') AS t_free1
                ,TO_CHAR(sei.t_free2, 'HH24:MI') AS t_free2
                ,TO_CHAR(sei.t_free3, 'HH24:MI') AS t_free3
                ,TO_CHAR(sei.t_free4, 'HH24:MI') AS t_free4
                ,TO_CHAR(sei.t_free5, 'HH24:MI') AS t_free5
                ,TO_CHAR(sei.t_free6, 'HH24:MI') AS t_free6
                ,TO_CHAR(sei.t_free7, 'HH24:MI') AS t_free7
                ,TO_CHAR(sei.t_free8, 'HH24:MI') AS t_free8
                ,TO_CHAR(sei.t_free9, 'HH24:MI') AS t_free9
                ,TO_CHAR(sei.t_free10, 'HH24:MI') AS t_free10
                ,TO_CHAR(sei.ts_free1, 'YYYY/MM/DD HH24:MI') AS ts_free1
                ,TO_CHAR(sei.ts_free2, 'YYYY/MM/DD HH24:MI') AS ts_free2
                ,TO_CHAR(sei.ts_free3, 'YYYY/MM/DD HH24:MI') AS ts_free3
                ,TO_CHAR(sei.ts_free4, 'YYYY/MM/DD HH24:MI') AS ts_free4
                ,TO_CHAR(sei.ts_free5, 'YYYY/MM/DD HH24:MI') AS ts_free5
                ,TO_CHAR(sei.ts_free6, 'YYYY/MM/DD HH24:MI') AS ts_free6
                ,TO_CHAR(sei.ts_free7, 'YYYY/MM/DD HH24:MI') AS ts_free7
                ,TO_CHAR(sei.ts_free8, 'YYYY/MM/DD HH24:MI') AS ts_free8
                ,TO_CHAR(sei.ts_free9, 'YYYY/MM/DD HH24:MI') AS ts_free9
                ,TO_CHAR(sei.ts_free10, 'YYYY/MM/DD HH24:MI') AS ts_free10
                ,sei.dl_free1
                ,sei.dl_free2
                ,sei.dln_free1
                ,sei.dln_free2
                ,CASE WHEN sei.seko_no IS NULL THEN '0' ELSE '1' END data_flg
            FROM
                shohin_kbn_mst skb
            LEFT JOIN
                shohin_ext_info sei
            ON  skb.shohin_ref_kbn = sei.shohin_ref_kbn
            AND skb.dialog_sbt = skb.dialog_sbt
            AND sei.delete_flg = 0
            AND sei.dai_bunrui_cd  = :dai_bunrui_cd
            AND sei.chu_bunrui_cd   = :chu_bunrui_cd
            AND sei.shohin_kbn  = :shohin_kbn
            AND sei.seko_no  = :seko_no
            {$whereAdd}
            WHERE
                skb.delete_flg = 0
            AND skb.shohin_ref_kbn  = :shohin_ref_kbn
            AND skb.dialog_sbt  = :dialog_sbt
                ";
        $select = $db->easySelOne($sql, array('shohin_ref_kbn' => $dataExt['shohin_ref_kbn'], 'dialog_sbt' => $dataExt['dialog_sbt']
            , 'dai_bunrui_cd' => $dataExt['dai_bunrui_cd'], 'chu_bunrui_cd' => $dataExt['chu_bunrui_cd']
            , 'shohin_kbn' => $dataExt['shohin_kbn'], 'seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 商品拡張情報保存処理 
     *
     * <AUTHOR> Sai
     * @since  2017/11/29
     * @param array $dataItem 商品データ
     * @param array $dataDialoContent 商品ダイアログデータ
     * @return int 更新件数
     */
    public function saveItemDialogData($dataItem, $dataDialoContent) {
        $db = Msi_Sys_DbManager::getMyDb();
        $this->saveShohinExtData($db, $dataItem, $dataDialoContent);
        $shohin_cd = preg_replace('/--\d*$/', '', $dataItem['item']);       // 商品CD取得
        if ($dataItem['dialog_sbt'] == '6') {
            $dataApp['denpyo_no'] = $this->getJuchudenpyoNo();
            $dataHachu = $this->getHachuInfoOne($shohin_cd);
            $this->saveHachuInfo($db, $dataApp, array($dataHachu));
        }
        $db->commit();
    }

    /**
     * 商品拡張情報保存処理 
     *
     * <AUTHOR> Sai
     * @since  2017/11/29
     * @param Msi_Sys_Db $db db
     * @param array $dataItem 商品データ
     * @param array $dataDialoContent 商品ダイアログデータ
     * @return int 更新件数
     */
    private function saveShohinExtData($db, $dataItem, $dataDialoContent) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataDialoContent, 'seko_no dai_bunrui_cd chu_bunrui_cd shohin_kbn shohin_ref_kbn dialog_sbt 
                                                            k_free1 k_free2 k_free3 k_free4 k_free5 k_free6 k_free7 k_free8 k_free9 k_free10 
                                                            k_free11 k_free12 k_free13 k_free14 k_free15 k_free16 k_free17 k_free18 k_free19 k_free20 
                                                            s_free1 s_free2 s_free3 s_free4 s_free5 s_free6 s_free7 s_free8 s_free9 s_free10 
                                                            s_free11 s_free12 s_free13 s_free14 s_free15 s_free16 s_free17 s_free18 s_free19 s_free20 
                                                            v_free1 v_free2 v_free3 v_free4 v_free5 v_free6 v_free7 v_free8 v_free9 v_free10 
                                                            v_free11 v_free12 v_free13 v_free14 v_free15 v_free16 v_free17 v_free18 v_free19 v_free20 
                                                            text_free1 text_free2 text_free3 text_free4 text_free5
                                                             n_free1 n_free2 n_free3 n_free4 n_free5 n_free6 n_free7 n_free8 n_free9 n_free10 
                                                            d_free1 d_free2 d_free3 d_free4 d_free5 d_free6 d_free7 d_free8 d_free9 d_free10 
                                                            t_free1 t_free2 t_free3 t_free4 t_free5 t_free6 t_free7 t_free8 t_free9 t_free10 
                                                            ts_free1 ts_free2 ts_free3 ts_free4 ts_free5 ts_free6 ts_free7 ts_free8 ts_free9 ts_free10 
                                                            dl_free1 dl_free2 dln_free1 dln_free2');
        if (empty($oneRow['dai_bunrui_cd'])) {
            $oneRow['dai_bunrui_cd'] = $dataItem['dai_bunrui_cd'];
        }
        if (empty($oneRow['chu_bunrui_cd'])) {
            $oneRow['chu_bunrui_cd'] = $dataItem['chu_bunrui_cd'];
        }
        if (empty($oneRow['shohin_kbn'])) {
            $oneRow['shohin_kbn'] = $dataItem['shohin_kbn'];
        }
        foreach ($oneRow as $key => $value) {
            $oneRow[$key] = Msi_Sys_Utils::emptyToNull($value);
        }
        $except = array();
        $select = $this->getItemDialogData($dataItem);
        if (count($select) === 0 || $select['data_flg'] === '0') {
            $oneRow['seko_no'] = $this->_sekoNo;
            if ($dataItem['shohin_ref_kbn'] === '2') { // 商品コードまで参照
                $oneRow['shohin_cd'] = preg_replace('/--\d*$/', '', $dataItem['item']);
            }
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("shohin_ext_info", $oneRow);
        } else {
            $oneRow['shohin_cd'] = $select['shohin_cd'];
            array_push($except, 'kaisya_cd');
            array_push($except, 'bumon_cd');
            array_push($except, 'dai_bunrui_cd');
            array_push($except, 'chu_bunrui_cd');
            array_push($except, 'shohin_kbn');
            array_push($except, 'shohin_cd');
            array_push($except, 'seko_no');
            // 条件部
            $where['dai_bunrui_cd'] = $select['dai_bunrui_cd'];
            $where['chu_bunrui_cd'] = $select['chu_bunrui_cd'];
            $where['shohin_kbn'] = $select['shohin_kbn'];
            $where['shohin_cd'] = $select['shohin_cd'];
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("shohin_ext_info", $oneRow, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 搬送・霊柩車の出発地、行先の区分による場所名を取得する
     *
     * <AUTHOR> Sai
     * @since  2018/06/29
     * @param array $cdNmKbns コート名称データ
     * @return array $dataBashoName 場所データ
     */
    private function getBashoName($cdNmKbns) {
        $dataBashoName = array();
        $bashoData = $this->getBashoData();
        if (count($bashoData) > 0) {
            foreach ($cdNmKbns as $value) {
                $basho_nm = '';
                switch ($value['kbn_value_cd']) {
                    case '11': // 死亡場所
                        $basho_nm = $bashoData['death_place'];
                        break;
                    case '12': // 安置先
                        $basho_nm = $bashoData['hs_anchi_nm'];
                        break;
                    case '13': // 通夜式場
                        $basho_nm = $bashoData['tsu_basho_nm'];
                        break;
                    case '14': // 葬儀式場
                        $basho_nm = $bashoData['so_basho_nm'];
                        break;
                    case '15': // 故人宅
                    case '16': // 喪主宅
                        $basho_nm = $value['kbn_value_lnm'];
                        break;
                    case '17': // 斎場
                        $basho_nm = $bashoData['ka_basho_nm'];
                        break;
                    default:
                        break;
                }
                $dataBashoName[$value['kbn_value_cd']] = $basho_nm;
            }
        }
        return $dataBashoName;
    }

    /**
     * 搬送・霊柩車の出発地、行先の区分による場所データを取得する
     *
     * <AUTHOR> Sai
     * @since  2018/06/29
     * @return array $dataBasho 場所データ
     */
    private function getBashoData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sk.seko_no -- 施行番号
            ,skf.free1_kbn AS death_place_kbn -- 死亡場所区分
            ,skf.free1_place_nm AS death_place -- 死亡場所
            ,sk.hs_gyomu_kbn2 -- 安置先区分
            ,sk.hs_anchi_nm -- 安置先
            ,tsu.basho_kbn AS tsu_basho_kbn -- 通夜式場区分
            ,tsu.basho_nm AS tsu_basho_nm -- 通夜式場
            ,so.basho_kbn AS so_basho_kbn -- 葬儀式場区分
            ,so.basho_nm AS so_basho_nm -- 葬儀式場
            ,ka.basho_nm AS ka_basho_nm -- 斎場
        FROM
            seko_kihon_info sk
            LEFT JOIN seko_kihon_all_free skf
            ON  (
                    sk.seko_no = skf.seko_no
                AND skf.seq_no = 0
                AND skf.delete_flg = 0
                )
            LEFT JOIN seko_nitei tsu
            ON  (
                    sk.seko_no = tsu.seko_no
                AND tsu.nitei_kbn = 4 -- 通夜
                AND tsu.delete_flg = 0
                )
            LEFT JOIN seko_nitei so
            ON  (
                    sk.seko_no = so.seko_no
                AND so.nitei_kbn = 7 -- 葬儀
                AND so.delete_flg = 0
                )
            LEFT JOIN seko_nitei ka
            ON  (
                    sk.seko_no = ka.seko_no
                AND ka.nitei_kbn = 6 -- 火葬
                AND ka.delete_flg = 0
                )
        WHERE
            sk.seko_no = :seko_no
        AND sk.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
        
    }

}
