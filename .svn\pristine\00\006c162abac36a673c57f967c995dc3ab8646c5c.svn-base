var apppho=apppho||{};
$(function(){apppho.pro={s_output_dt:"#print_date, #print_time",p_limit_dt:"#panel_deliv_date, #panel_deliv_time",s_han_kbn:"#s2id_photo_copy_1 .select-container",s_han_cnt:"#s2id_photo_size_1 .select-container",s_cab_kbn:"#s2id_photo_copy_2 .select-container",s_cab_cnt:"#s2id_photo_size_2 .select-container",s_sitei_yoko1:"#photo_size_3_width",s_sitei_tate1:"#photo_size_3_height",s_sitei_yoko2:"#photo_size_4_width",s_sitei_tate2:"#photo_size_4_height",v_free5:"#phone_no"};var g=[{id:"1",text:"1"},
{id:"2",text:"2"},{id:"3",text:"3"},{id:"4",text:"4"},{id:"5",text:"5"}],l=[{id:"0",text:"\u65b0\u898f"},{id:"1",text:"\u713c\u5897"}],h=function(a,b,c){var d=a.model;a.$el.msiPickHelper({action:"gazo",onSelect:function(a){a.gazo_cd?(d.set(c.oid,a.gazo_img),d.set(c.code,a.code),d.set(c.name,a.gazo_nm)):(d.set(c.oid,null),d.set(c.code,null),d.set(c.name,null))},onClear:function(){d.set(c.oid,null);d.set(c.code,null);d.set(c.name,null)},hookSetData:function(){return b}})},m=Backbone.Model.extend({defaults:function(){return{seko_no:null,
sougi_ymd:null,panel_size_info_1:null,panel_size_info_2:null,panel_size_info_3:null,panel_size_info_4:null,panel_size_info_5:null,panel_price_1:null,panel_price_2:null,panel_price_3:null,panel_price_4:null,panel_price_5:null}},validation:{}}),p=Backbone.View.extend({el:$("#detail"),events:{"change #print_set input":"clearPrintClass","click #print_set input":"doPrintClick","click #btn_save":"doSave","click #btn_delete":"doDelete","click #btn_cancel":"doCancel","click .tab li a":"changeTab","click .dlg_date, .dlg_time":"setDatePickerFocus",
"change #photo_copy_1,#photo_copy_2,#photo_size_1,#photo_size_2, input[name=panel_type], input[name=color_type]":function(){apppho.dpCreFlg=!0}},bindings:{},initialize:function(){this.render()},render:function(){this.stickit();return this},clearPrintClass:function(){this.$("input[name=print]").removeClass("onCheck")},doPrintClick:function(a){this.setPrinButton($(a.currentTarget));apppho.printIdx=$("#"+a.currentTarget.id+":checked").val();this.setPhotoView()},setPrinButton:function(a){a.hasClass("onCheck")?
(a.attr("checked",!1),a.button("refresh"),a.removeClass("onCheck")):a.toggleClass("onCheck")},setPhotoView:function(){this.$("#photo-tab div").remove();if(0<=apppho.printIdx){var a=String(apppho.printIdx),b=!1;_.each(apppho.photoCol.models,function(c){c.get("yakimashi_kbn")===a&&(apppho.photoModel=c,b=!0)});b||(apppho.photoModel=new k({yakimashi_kbn:a}),apppho.photoModel.set("s_output_bumon_cd",apppho.appModel.get("bumon_cd")),apppho.photoCol.add(apppho.photoModel));apppho.photoModel.set(apppho.appModel.toJSON());
apppho.photoView=new n({model:apppho.photoModel});this.$("#photo-tab").append(apppho.photoView.render().el);this.$(".radio_set").buttonset();apppho.appModel.get("juchu_kakute_ymd")&&(this.$("#photo_copy_1,#photo_copy_2,#photo_size_1,#photo_size_2,#photo_size_3,#photo_size_4").attr("disabled","disabled"),this.$("input[name=panel_type]").button("disable"))}this.setButtonsStatus()},setPhotoCheckedButton:function(){$("#print_set .lbl_print").removeClass("checked");_.each(apppho.photoCol.models,function(a){a=
a.get("yakimashi_kbn");$("#print_set .lbl_print").eq(a).addClass("checked")})},setButtonsStatus:function(){var a=this.$("#btn_save"),b=this.$("#btn_delete"),c=this.$(".tab li").index(this.$(".tab li span").closest("li"));0===c?(b.show(),0<=apppho.printIdx?$.msiJqlib.setBtnEnable(a):$.msiJqlib.setBtnDisable(a),this.$("#print_set .lbl_print").eq(apppho.printIdx).hasClass("checked")?$.msiJqlib.setBtnEnable(b):$.msiJqlib.setBtnDisable(b)):1===c&&($.msiJqlib.setBtnEnable(a),b.hide())},changeTab:function(a){$.msiJqlib.changeTab(this.$(".tab-contents"),
this.$(".tab li"),$(a.currentTarget));this.setButtonsStatus()},isInputOk:function(){var a=[];if(0<=apppho.printIdx){var b=apppho.photoModel.validate();b&&_.each(b,function(c,b){a.push(c)})}if(0<a.length)return $.msiJqlib.showErr(a.join(", ")),!1;$.msiJqlib.clearAlert();return!0},doSave:function(){$.msiJqlib.clearAlert();if(this.isInputOk()){var a=$.msiJqlib.isEqual(apppho.photoModel.toJSON(),apppho.orgDataPhoto),a=$.msiJqlib.isNullEx2(apppho.printIdx)||a,b=$.msiJqlib.isEqual(apppho.monitorModel.toJSON(),
apppho.orgDataMonitor);if(a&&b)$.msiJqlib.showInfo("\u30c7\u30fc\u30bf\u306e\u5909\u66f4\u304c\u3042\u308a\u307e\u305b\u3093");else{var c=JSON.stringify(apppho.photoModel.toJSON()),d=JSON.stringify(apppho.monitorModel.toJSON()),a=JSON.stringify({photoChangeFlg:!a,monitorChangeFlg:!b});$.ajax({url:$.msiJqlib.baseUrl()+"/juchu/mitsu/photoexsave",data:{dataApp:JSON.stringify(apppho.appModel.toJSON()),dataPhoto:c,dataMonitor:d,changeFlg:a,dpCreFlg:apppho.dpCreFlg},type:"POST",success:function(a){"OK"===
a.status?($.msiSideMenuLib.setSideMenu({data:a.dataSideMenu}),apppho.resetData(a.dataApp,a.dataPhotoCol,a.dataMonitor),$.msiJqlib.showInfo(a.msg)):"NG"===a.status?$.msiJqlib.showWarn(a.msg):$.msiJqlib.showErr(a.msg)}})}}},doDelete:function(){var a=this.$("#print_set .lbl_print").eq(apppho.printIdx),b=a.text();!$.msiJqlib.isNullEx2(b)&&a.hasClass("checked")&&confirm("\u300c"+b+"\u300d\u3092\u524a\u9664\u3057\u307e\u3059\u3002\u3088\u308d\u3057\u3044\u3067\u3059\u304b\uff1f")&&(a=JSON.stringify(apppho.photoModel.toJSON()),
$.ajax({url:$.msiJqlib.baseUrl()+"/juchu/mitsu/photoexdelete",data:{dataApp:JSON.stringify(apppho.appModel.toJSON()),dataPhoto:a},type:"POST",success:function(a){"OK"===a.status?(apppho.appView.setPrinButton($("input[name=print]").eq(apppho.printIdx)),apppho.printIdx=-1,$.msiSideMenuLib.setSideMenu({data:a.dataSideMenu}),apppho.resetData(a.dataApp,a.dataPhotoCol,a.dataMonitor),$.msiJqlib.showInfo(a.msg)):$.msiJqlib.showWarn(a.msg)}}))},doCancel:function(){confirm("\u521d\u671f\u72b6\u614b\u306b\u623b\u3057\u3066\u3088\u308d\u3057\u3044\u3067\u3059\u304b\uff1f")&&
window.location.reload()},setDatePickerFocus:function(a){$(a.currentTarget).prev("input").datepicker("show")}}),k=Backbone.Model.extend({defaults:function(){return{seko_no:null,yakimashi_kbn:null,siire_cd:null,s_output_date:null,s_output_time:null,s_input_bumon_cd:null,s_output_bumon_cd:null,s_output_dt:null,s_kisekae_kbn:"1",s_huku_kbn:"0",s_huku_oid:null,s_huku_type:null,s_huku_nm:null,s_haikei_kbn:"0",s_haikei_oid:null,s_haikei_type:null,s_haikei_nm:null,s_kamon_kbn:"0",gazo_oid:null,gazo_nm:null,
s_kamon_nm:null,s_jotai_kbn:"0",size_kbn:"0",s_kako_kbn:"0",s_han_kbn:"0",s_han_cnt:"1",s_cab_kbn:"0",s_cab_cnt:"2",s_sitei_cnt1:null,s_sitei_yoko1:null,s_sitei_tate1:null,s_sitei_cnt2:null,s_sitei_yoko2:null,s_sitei_tate2:null,p_size_kbn:"0",p_limit_dt:null,p_limit_date:null,p_limit_time:null,biko1:null,sougi_ymd:null,tuya_ymd:null,free_kbn1:"0",v_free1:null,v_free2:null,v_free3:null,free_kbn2:null,free_kbn3:null,free_kbn4:null,free_kbn5:null,free_kbn6:null,free_kbn7:null,free_kbn8:null,free_kbn9:null,
free_kbn10:null,free_kbn11:null,v_free4:null,tanto_nm:null,v_free5:null,free_kbn12:"0",free_kbn13:"0"}},validation:{s_output_dt:{required:!1,pattern:"timestamp"},p_limit_dt:{required:!1,pattern:"timestamp"},s_han_kbn:{required:function(){return this.kbnCntCheck("s_han_cnt")}},s_han_cnt:{required:function(){return this.kbnCntCheck("s_han_kbn")}},s_cab_kbn:{required:function(){return this.kbnCntCheck("s_cab_cnt")}},s_cab_cnt:{required:function(){return this.kbnCntCheck("s_cab_kbn")}},s_sitei_yoko1:{required:function(){return this.shiteiCheck(1)},
pattern:"signedfloat1",max:100},s_sitei_tate1:{required:function(){return this.shiteiCheck(1)},pattern:"signedfloat1",max:100},s_sitei_yoko2:{required:function(){return this.shiteiCheck(2)},pattern:"signedfloat1",max:100},s_sitei_tate2:{required:function(){return this.shiteiCheck(2)},pattern:"signedfloat1",max:100},v_free5:{required:!1,pattern:"tel"}},labels:{s_output_dt:"\u51fa\u529b\u5e0c\u671b\u6642\u9593",p_limit_dt:"\u30d1\u30cd\u30eb\u5230\u7740\u671f\u9650",s_han_kbn:"\u5199\u771f\u534a\u5207\u533a\u5206",
s_han_cnt:"\u5199\u771f\u534a\u5207\u679a\u6570",s_cab_kbn:"\u5199\u771f\u30ad\u30e3\u30d3\u30cd\u533a\u5206",s_cab_cnt:"\u5199\u771f\u30ad\u30e3\u30d3\u30cd\u679a\u6570",s_sitei_yoko1:"\u6307\u5b9a1\u30b5\u30a4\u30ba\u6a2a",s_sitei_tate1:"\u6307\u5b9a1\u30b5\u30a4\u30ba\u7e26",s_sitei_yoko2:"\u6307\u5b9a2\u30b5\u30a4\u30ba\u6a2a",s_sitei_tate2:"\u6307\u5b9a2\u30b5\u30a4\u30ba\u7e26",v_free5:"\u96fb\u8a71\u756a\u53f7"},shiteiCheck:function(a){var b=this.get("s_sitei_yoko"+a);a=this.get("s_sitei_tate"+
a);b=$.msiJqlib.isNullEx2(b);a=$.msiJqlib.isNullEx2(a);return b&&a||!b&&!a?!1:!0},kbnCntCheck:function(a){return $.msiJqlib.isNullEx2(this.get(a))?!1:!0}}),q=Backbone.Collection.extend({model:k}),n=Backbone.View.extend({tagName:"div",template:_.template($("#tmpl-photo").html()),events:{"click #lbl_dlg_crest":"kamonHelper","click .dlg_cloth":"hukuHelper","click #lbl_dlg_bg":"haikeiHelper","click #staff, .label.dlg_staff":"tantoHelper","click input[name='tel_kbn']":function(a){this.setModel(a,"free_kbn12")},
"click input[name='color_type']":function(a){this.setModel(a,"s_kako_kbn")},"click input[name='trim_type']":function(a){this.setModel(a,"size_kbn")},"click input[name='change_cloth']":function(a){this.setModel(a,"s_kisekae_kbn");"0"==this.model.get("s_kisekae_kbn")?(this.$("#cloth_name").attr("readonly","readonly"),this.$("#cloth_name").val(null),this.model.set("s_huku_nm",null),this.model.set("s_huku_type",null)):this.$("#cloth_name").removeAttr("readonly")},"click input[name='bg_setting']":function(a){this.setModel(a,
"s_haikei_kbn");"1"==this.model.get("s_haikei_kbn")?(this.$("#bg_name").attr("readonly","readonly"),this.$("#bg_name").val(null),this.model.set("s_haikei_nm",null),this.model.set("s_haikei_type",null)):this.$("#bg_name").removeAttr("readonly")},"click input[name='cloth_type']":function(a){this.setModel(a,"s_huku_kbn")},"click input[name='crest_setting']":function(a){this.setModel(a,"s_kamon_kbn")},"click input[name='panel_type']":function(a){this.setModel(a,"p_size_kbn")},"click input[name='answer']":function(a){this.setModel(a,
"s_jotai_kbn")},"click input[name='import_type']":function(a){this.setModel(a,"free_kbn1")},"click input[name='portrait']":function(a){this.setModel(a,"free_kbn13")},"change #neck":function(a){this.setCheckBox(a,"free_kbn2","#neck")},"change #body":function(a){this.setCheckBox(a,"free_kbn3","#body")},"change #hair":function(a){this.setCheckBox(a,"free_kbn4","#hair")},"change #clothes":function(a){this.setCheckBox(a,"free_kbn5","#clothes")},"change #neckband":function(a){this.setCheckBox(a,"free_kbn6",
"#neckband")},"change #arm":function(a){this.setCheckBox(a,"free_kbn7","#arm")},"change #all_ac":function(a){this.setCheckBox(a,"free_kbn8","#all_ac")},"change #pocket":function(a){this.setCheckBox(a,"free_kbn9","#pocket")},"change #earphone":function(a){this.setCheckBox(a,"free_kbn10","#earphone")},"change #devices":function(a){this.setCheckBox(a,"free_kbn11","#devices")}},bindings:{"#input_bumon_cd":{observe:"s_input_bumon_cd",onSet:"empty2null"},"#output_bumon_cd":{observe:"s_output_bumon_cd",
onSet:"empty2null"},"#print_date":{observe:"s_output_date",getVal:function(a,b,c){this.setDate(a.val(),this.model.get("s_output_time"),"s_output_dt",this.model);return a.val()}},"#print_time":{observe:"s_output_time",getVal:function(a,b,c){this.setDate(this.model.get("s_output_date"),a.val(),"s_output_dt",this.model);return a.val()}},"#panel_deliv_date":{observe:"p_limit_date",getVal:function(a,b,c){this.setDate(a.val(),this.model.get("p_limit_time"),"p_limit_dt",this.model);return a.val()}},"#panel_deliv_time":{observe:"p_limit_time",
getVal:function(a,b,c){this.setDate(this.model.get("p_limit_date"),a.val(),"p_limit_dt",this.model);return a.val()}},"#print_date_2":"tuya_ymd","#photo_copy_1":{observe:"s_han_kbn",onSet:"empty2null"},"#photo_size_1":{observe:"s_han_cnt",onSet:"empty2null"},"#photo_copy_2":{observe:"s_cab_kbn",onSet:"empty2null"},"#photo_size_2":{observe:"s_cab_cnt",onSet:"empty2null"},"#photo_size_3":{observe:"s_sitei_cnt1",onSet:"empty2null"},"#photo_size_4":{observe:"s_sitei_cnt2",onSet:"empty2null"},"#photo_size_3_width":{observe:"s_sitei_yoko1",
onSet:"empty2null"},"#photo_size_3_height":{observe:"s_sitei_tate1",onSet:"empty2null"},"#photo_size_4_width":{observe:"s_sitei_yoko2",onSet:"empty2null"},"#photo_size_4_height":{observe:"s_sitei_tate2",onSet:"empty2null"},"#lbl_panel_info_1":"panel_size_info_1","#lbl_panel_info_2":"panel_size_info_2","#lbl_panel_info_3":"panel_size_info_3","#lbl_panel_info_4":"panel_size_info_4","#lbl_panel_info_5":"panel_size_info_5","#lbl_panel_price_1":{observe:"panel_price_1",onGet:"commaAdd"},"#lbl_panel_price_2":{observe:"panel_price_2",
onGet:"commaAdd"},"#lbl_panel_price_3":{observe:"panel_price_3",onGet:"commaAdd"},"#lbl_panel_price_4":{observe:"panel_price_4",onGet:"commaAdd"},"#lbl_panel_price_5":{observe:"panel_price_5",onGet:"commaAdd"},"#cloth_crest_name":"s_kamon_nm","#cloth_name":"s_huku_nm","#bg_name":"s_haikei_nm","#photo_memo":"biko1","#import_other":"v_free1","#modify_other":"v_free2","#delete_other":"v_free3","#staff":"tanto_nm","#phone_no":"v_free5"},commaAdd:function(a,b){if(!$.msiJqlib.isNullEx2(a))return $.msiJqlib.commaAdd(a)+
"\u5186"},empty2null:function(a){return $.msiJqlib.isNullEx2(a)?null:a},initialize:function(){this.listenTo(this.model,"change:gazo_oid change:s_haikei_oid change:s_huku_oid",this.setGazo);Backbone.Validation.bind(this,Backbone.Validation.msi_v_iv_callback(apppho.pro,"error1"))},render:function(){this.$el.html(this.template({baseUrl:$.msiJqlib.baseUrl()}));this.stickit();this.$(".photo_date").datetimepicker($.extend({},$.msiJqlib.datePickerDefault,{showTimepicker:!1}));this.$(".time").timepicker($.extend({},
$.msiJqlib.datetimePickerDefault));$.msiJqlib.setSelect2Com1(this.$("#input_bumon_cd"),$.extend({data:$.msiJqlib.objToArray2(f.bumonkbn)},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#output_bumon_cd"),$.extend({data:$.msiJqlib.objToArray2(f.bumonkbn)},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#photo_copy_1"),$.extend({data:l},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#photo_size_1"),$.extend({data:g},$.msiJqlib.setSelect2Default1));
$.msiJqlib.setSelect2Com1(this.$("#photo_copy_2"),$.extend({data:l},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#photo_size_2"),$.extend({data:g},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#photo_size_3"),$.extend({data:g},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#photo_size_4"),$.extend({data:g},$.msiJqlib.setSelect2Default1));this.setRadioBoxIni();this.setCheckBoxIni();this.addGazo(this.$("#crest_pic_cloth"),this.model.get("gazo_oid"));
this.addGazo(this.$("#photo_pic"),this.model.get("s_huku_oid"));this.addGazo(this.$("#photo_pic_back"),this.model.get("s_haikei_oid"));$.msiJqlib.isNullEx2(this.model.get("v_free4"))&&(this.model.set("v_free4",apppho.appModel.get("tan_cd")),this.model.set("tanto_nm",apppho.appModel.get("tan_nm")));return this},setRadioBoxIni:function(){this.setRadioBox("free_kbn12",["#company_tel","#moblie_tel"]);this.setRadioBox("s_kako_kbn",["#color","#mono"]);this.setRadioBox("size_kbn",["#trim_a","#trim_b","#trim_c"]);
this.setRadioBox("s_kisekae_kbn",["#nochange","#change"]);this.setRadioBox("s_haikei_kbn",["#bg_hide","#bg_show"]);this.setRadioBox("s_huku_kbn",["#japanese_cloth","#foreign_cloth"]);this.setRadioBox("s_kamon_kbn",["#crest_show","#crest_shade","#crest_hide"]);this.setRadioBox("p_size_kbn","#no_panel #panel_size_1 #panel_size_2 #panel_size_3 #panel_size_4 #panel_size_5".split(" "));this.setRadioBox("s_jotai_kbn",["#answer_1","#answer_2"]);this.setRadioBox("free_kbn1",["#scanner","#usb","#desktop"]);
this.setRadioBox("free_kbn13",["#portrait1","#portrait2"])},setRadioBox:function(a,b){var c=this;_.each(b,function(b,e){c.model.get(a)===String(e)&&c.$(b).attr("checked","checked")})},setModel:function(a,b){var c=$("#"+a.currentTarget.id+":checked").val();this.model.set(b,c)},setCheckBoxIni:function(){this.setCheck("free_kbn2","#neck");this.setCheck("free_kbn3","#body");this.setCheck("free_kbn4","#hair");this.setCheck("free_kbn5","#clothes");this.setCheck("free_kbn6","#neckband");this.setCheck("free_kbn7",
"#arm");this.setCheck("free_kbn8","#all_ac");this.setCheck("free_kbn9","#pocket");this.setCheck("free_kbn10","#earphone");this.setCheck("free_kbn11","#devices")},setCheck:function(a,b){"1"===this.model.get(a)&&this.$(b).attr("checked","checked")},setCheckBox:function(a,b,c){a=$(c+":checked").val();this.model.set(b,"1"===a?"1":"0")},setDate:function(a,b,c,d){var e=null;$.msiJqlib.isNullEx2(a)||$.msiJqlib.chkDate(a)&&(e=a);$.msiJqlib.isNullEx2(b)||($.msiJqlib.isNullEx2(a)||(e+=" "),e+=b);d.set(c,e)},
setGazo:function(a,b){var c=a.changed;if(_.has(c,"gazo_oid"))c="#crest_pic_cloth";else if(_.has(c,"s_huku_oid"))c="#photo_pic";else if(_.has(c,"s_haikei_oid"))c="#photo_pic_back";else return;this.addGazo(this.$(c),b)},addGazo:function(a,b){a.find("img").remove();$.msiJqlib.isNullEx2(b)||$("<img>").appendTo(a).attr("src",$.msiJqlib.baseUrl()+"/mref/gazodlg/img/imgid/"+b)},tantoHelper:function(){var a=this.model;this.$el.msiPickHelper({action:"tanto",onSelect:function(b){a.set({v_free4:b.code,tanto_nm:b.name})},
onClear:function(){a.set({v_free4:null,tanto_nm:null})}})},kamonHelper:function(){h(this,{s_gazo_kbn:1,limit:20},{oid:"gazo_oid",code:"gazo_nm",name:"s_kamon_nm"})},hukuHelper:function(){if("0"!==this.model.get("s_kisekae_kbn")){var a=apppho.appModel.get("k_sex_kbn");h(this,{s_gazo_kbn:5,limit:20,s_k_sex_kbn:"1"===a?"0":"2"===a?"1":"9"},{oid:"s_huku_oid",code:"s_huku_type",name:"s_huku_nm"})}},haikeiHelper:function(){"1"!==this.model.get("s_haikei_kbn")&&(apppho.appModel.get("k_sex_kbn"),h(this,{s_gazo_kbn:4,
limit:20,s_k_sex_kbn:"9"},{oid:"s_haikei_oid",code:"s_haikei_type",name:"s_haikei_nm"}))},copyCheck:function(a){var b,c=$(a.currentTarget);b=c.is("#photo_copy_1")?this.$("#photo_copy_2"):this.$("#photo_copy_1");var d=a.val;b=b.select2("data");$.msiJqlib.isNullEx2(d)||$.msiJqlib.isNullEx2(b)||d!==b.id||($.msiJqlib.showWarn("\u65b0\u898f\u307e\u305f\u306f\u713c\u5897\u3092\u8907\u6570\u9078\u629e\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u305b\u3093"),a.preventDefault(),c.select2("close"))}}),
r=Backbone.Model.extend({defaults:function(){return{seko_no:null,m_view_shot_kbn:"0",m_kbn:"0",m_input_bumon_cd:null,m_output_bumon_cd:null,m_muki_kbn:null,m_haikei_kbn:null,gazo_oid2:null,gazo_cd2:null,gazo_cd2_nm:null,biko:null}}}),s=Backbone.View.extend({el:$("#monitor-tab"),events:{"change #layout_set input":"clearLayoutClass","click #layout_set input":"doLayoutClick","click #monitor_list .sample .title, #monitor_list .sample .pic":function(a){a=$(a.currentTarget).parent("li");var b=this.$("#monitor_list li.sample").index(a);
a.hasClass("selected")?(a.removeClass("selected"),this.model.set("m_kbn","0"),this.model.set("m_haikei_kbn",null)):($("#monitor .sample").removeClass("selected"),a.addClass("selected"),this.model.set("m_kbn","1"),this.model.set("m_haikei_kbn",String(b)))},"change #view_shot_kbn":function(a){this.setCheckBox(a,"m_view_shot_kbn","#view_shot_kbn")}},bindings:{"#memo":"biko","#input_bumon_cd_2":{observe:"m_input_bumon_cd",afterUpdate:function(a,b,c){$.msiJqlib.setSelect2Val(a,a.val())},onSet:"empty2null"},
"#output_bumon_cd_2":{observe:"m_output_bumon_cd",afterUpdate:function(a,b,c){$.msiJqlib.setSelect2Val(a,a.val())},onSet:"empty2null"},"#monitor_bg_name":"gazo_cd2"},empty2null:function(a){return $.msiJqlib.isNullEx2(a)?null:a},setCheckBox:function(a,b,c){a=$(c+":checked").val();this.model.set(b,"1"===a?"1":"0")},initialize:function(){Backbone.Validation.bind(this,Backbone.Validation.msi_v_iv_callback(apppho.pro,"error1"));this.listenTo(this.model,"change:m_haikei_kbn",this.setHaikei);this.render()},
render:function(){this.stickit();$.msiJqlib.setSelect2Com1(this.$("#input_bumon_cd_2"),$.extend({data:$.msiJqlib.objToArray2(f.bumonkbn)},$.msiJqlib.setSelect2Default1));$.msiJqlib.setSelect2Com1(this.$("#output_bumon_cd_2"),$.extend({data:$.msiJqlib.objToArray2(f.bumonkbn)},$.msiJqlib.setSelect2Default1));return this},setHaikei:function(){"3"===this.model.get("m_haikei_kbn")?this.$("#monitor_bg_name").removeAttr("disabled"):(this.$("#monitor_bg_name").attr("disabled","disabled"),this.model.set("gazo_cd2",
null))},haikeiHelper:function(){"3"===this.model.get("m_haikei_kbn")&&h(this,{s_gazo_kbn:3,limit:20},{oid:"gazo_oid2",code:"gazo_cd2",name:"gazo_cd2_nm"})},clearLayoutClass:function(){this.$("input[name=layout]").removeClass("onCheck")},doLayoutClick:function(a){a=$(a.currentTarget);a.hasClass("onCheck")?(a.attr("checked",!1),a.button("refresh"),a.removeClass("onCheck"),this.model.set("m_muki_kbn",null)):(a.toggleClass("onCheck"),this.model.set("m_muki_kbn",a.val()))}}),f=msiLib2.getJsonFromHtml($("#data-photo"));
apppho.appModel=new m;apppho.photoModel=new k;apppho.photoCol=new q;apppho.monitorModel=new r;apppho.appView=new p({model:apppho.appModel});apppho.monitorView=new s({model:apppho.monitorModel});$.msiSideMenuLib.setSideMenu({showFooter:!0});apppho.resetData=function(a,b,c){apppho.dpCreFlg=!1;apppho.appModel.set(a);apppho.photoCol.reset(b);apppho.appView.setPhotoCheckedButton();apppho.appView.setPhotoView();apppho.monitorModel.set(c);c?("1"===c.m_kbn&&$("#monitor_list li.sample").eq(c.m_haikei_kbn).addClass("selected"),
"1"===c.m_muki_kbn?$("#layout_right").attr("checked",!0).button("refresh").addClass("onCheck"):"0"===c.m_muki_kbn&&$("#layout_left").attr("checked",!0).button("refresh").addClass("onCheck"),"1"===c.m_view_shot_kbn&&"1"!==$("#view_shot_kbn:checked").val()&&$("#view_shot_kbn").click()):apppho.monitorModel.set("m_output_bumon_cd",apppho.appModel.get("bumon_cd"));apppho.orgDataPhoto=apppho.photoModel.toJSON();apppho.orgDataPhotoCol=apppho.photoCol.toJSON();apppho.orgDataMonitor=apppho.monitorModel.toJSON()};
apppho.resetData(f.dataApp,f.dataPhotoCol,f.dataMonitor);$("#photo-div-wrapper").show()});
