{include file="fdn_head_std.tpl"}
{include file="fdn_header_2.tpl"}

<form id="my-form-id" method="post" class="">
    <div id="main">
        <div id="order" style="display:none; overflow: hidden">
            <div class="page-title"><span>売掛金残高一覧表</span></div>
            {include file="fdn_print_1.tpl"}
            <div id ="jyoken_siji" class="">
                <fieldset2 class="base_1">
                    <label for="nyukin_st_ymd" class="lbl_nyukin_ymd my-require">入金日</label>
                    <input name="nyukin_st_ymd" id="nyukin_st_ymd" type="text" class="txt my-type-date date_auto_slash to_alpha_num" value="" />
                    <div class="label dlg_date my-type-date9-trg" data-ref-rel="#nyukin_st_ymd"></div>
                    <label class="lbl_taisho_kikan my-require">～</label>
                    <input name="nyukin_ed_ymd" id="nyukin_ed_ymd" type="text" class="txt my-type-date date_auto_slash to_alpha_num" value="" />
                    <div class="label dlg_date my-type-date9-trg" data-ref-rel="#nyukin_ed_ymd"></div>
                </fieldset2>
                <fieldset2 class="base_2">
                    <label for="taisho_kikan_kbn">対象期間区分</label>
                    <input type="hidden" name="taisho_kikan_kbn" id="taisho_kikan_kbn" class="zandaka_kbn-cls" />
                </fieldset2>
                <fieldset2 class="base_2">
                    <label for="report_kbn" class="lbl_report_kbn my-require">帳票区分</label>
                    <!--
                    <input type="hidden" id="report_kbn" name="report_kbn"
                           class="msi-picker report_kbn-cls" data-picker-kind="cdNm"
                           data-picker-param="kind2:'0710',placeholder:''" />
                    -->
                    <input type="hidden" name="report_kbn" id="report_kbn" class="report_kbn-cls" />
                </fieldset2>
<!--	互助会は無い
                <fieldset2 class="base_2">
                    <label for="gojokai_use">{$kain_nm}利用</label>
                    <input type="hidden" name="gojokai_use" id="gojokai_use" class="gojokai_use-cls" />
                </fieldset2>
-->
                <fieldset2 class="base_2">
                    <label for="zandaka_kbn">残高区分</label>
                    <input type="hidden" name="zandaka_kbn" id="zandaka_kbn" class="zandaka_kbn-cls" />
                </fieldset2>
                <fieldset2 class="base_2">
                    <label for="order_kbn">出力順</label>
                    <input type="hidden" name="order_kbn" id="order_kbn" class="order_kbn-cls" />
                </fieldset2>
                <fieldset2 class="base_3">
                    <label for="tani_kbn" class="lbl_tani_kbn my-require">単位選択</label>
                    <input type="hidden" id="tani_kbn" name="tani_kbn"
                           class="msi-picker tani_kbn-cls" data-picker-kind="cdNm"
                           data-picker-param="kind2:'0700',placeholder:''" />
                </fieldset2>
            </div>	 
            <!-- 処理ボタン -->
            <div class="buttons">
                <input type="button" name="btn_print" id="btn_print" value="印刷" />
                <input type="button" name="btn_csvout" id="btn_csvout" value="CSV" />
            </div>
        </div><!-- /.order -->
    </div><!-- /#main -->
</form><!-- /#my-form-id -->
{include file="fdn_print_2.tpl"}
