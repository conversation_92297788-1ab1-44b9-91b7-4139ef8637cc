<?php

/**
 * Mref_SiiredlgController
 *
 * 仕入先 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 * @version    2025/03/xx Tosaka ベースよりコピー
 * @filesource 
 */

/**
 * 仕入先 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 */
class Mref_SiiredlgController extends Mref_AbstractdlgController {

    /**
     * 検索処理
     *
     * @return array($data, $hash)
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
    protected function _doSearch() {
        $params = $this->_params;
        $data = array();
        $hash = array();
        $isMoreData = false;

        // Msi_Sys_Utils::debug( ' _doSearch: _params==>' . Msi_Sys_Utils::dump($params) );

        $db = Msi_Sys_DbManager::getMyDb();

        $this->_params['limit'] = $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 300);
        $this->_params['offset'] = $offset = Msi_Sys_Utils::easyGetVar($params, 'offset', 'DIGIT', 0);

        $cond = array(
            '__etc_limit' => $limit + 1,
            '__etc_offset' => $offset,
        );

        //ADD 2014/08/07 MSI Sugiyama
        $no_date = Msi_Sys_Utils::easyGetVar($params, 'no_date', null, 0);

        $code = Msi_Sys_Utils::easyGetVar($params, 's_code_ime-off');
        $name = Msi_Sys_Utils::easyGetVar($params, 's_name');
        $kana = Msi_Sys_Utils::easyGetVar($params, 's_kana');
        $siire_kbn = Msi_Sys_Utils::easyGetVar($params, 's_siire_kbn');
        $transfer_kbn = Msi_Sys_Utils::easyGetVar($params, 's_transfer_kbn');
        $tel = Msi_Sys_Utils::easyGetVar($params, 's_tel'); // 2016/02/13 ADD Kayo
        $fax = Msi_Sys_Utils::easyGetVar($params, 's_fax'); // 2016/02/13 ADD Kayo
        $toriatsukaisya_flg = Msi_Sys_Utils::easyGetVar($params, 's_toriatsukaisya_flg'); // 取扱者フラグ 2022/07/28 ADD mihara
        $gyosya_flg = Msi_Sys_Utils::easyGetVar($params, 's_gyosya_flg'); // 業者フラグ 2022/08/10 ADD Tosaka
        $siire_flg = Msi_Sys_Utils::easyGetVar($params, 's_siire_flg'); // 仕入れ先フラグ 2022/09/09 ADD Tosaka
        $bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_bumon_cd');

        if (strlen($code) > 0) {
            $cond['siire_cd'] = array('~', $code);
        }
        if (strlen($transfer_kbn) > 0) {
            $cond['transfer_kbn'] = DataMapper_Utils::condOneOf('transfer_kbn', $transfer_kbn, 'transfer_kbn_');
        }
        if (strlen($name) > 0) {
            $cond['__x1'] = array('x', "(siire_lnm ~ :x1_1 OR siire_snm ~ :x1_2)",
                array('x1_1' => $name, 'x1_2' => $name));
        }
        if (strlen($kana) > 0) {
            $cond['__x2'] = array('x', "(siire_knm ~ :x2_1)",
                array('x2_1' => $kana));
        }
        if (strlen($siire_kbn) > 0) {
            // $cond['__x3'] = array( 'x', "(siire_kbn = :siire_kbn)", array('siire_kbn' => $siire_kbn) );
            $cond['__x3'] = DataMapper_Utils::condOneOf('siire_kbn', $siire_kbn, 'sikbn_');
        }
        if (strlen($tel) > 0) {  // 2016/02/13 ADD Kayo
            $cond['__x4'] = array('x', "(tel ~ :x4_1)",
                array('x4_1' => $tel));
        }
        if (strlen($fax) > 0) {  // 2016/02/13 ADD Kayo
            $cond['__x5'] = array('x', "(fax ~ :x5_1)",
                array('x5_1' => $fax));
        }
        if (strlen($toriatsukaisya_flg) > 0) { // 2022/07/28 ADD mihara
            $cond['toriatsukaisya_flg'] = +$toriatsukaisya_flg;
        }
        if (strlen($gyosya_flg) > 0) { // 2022/07/28 ADD mihara
            $cond['gyosya_flg'] = +$gyosya_flg;
        }
        if (strlen($siire_flg) > 0) { // 2022/09/09 ADD Tosaka
            $cond['siire_flg'] = +$siire_flg;
        }
        // 部門から仕入先部門マスタで対象の仕入先を絞る
        if (strlen($bumon_cd) > 0) { // 2025/04/30 ADD Kobayashi
            $siire = self::getSiireBumon($db, $bumon_cd);
            $cond['__raw_1'] = "siire_cd IN ('".$siire."')";
        }

        //MOD Start 2014/08/07 MSI Sugiyama
        //$select = DataMapper_Siire::find( $db, $cond );
        if (intval($no_date) === 1) {
            //「現在日付が適用期間内」の条件をセットしない
            $select = DataMapper_Siire::find($db, $cond, false);
        } else {
            //「現在日付が適用期間内」の条件をセットする
            $select = DataMapper_Siire::find($db, $cond);
        }
        //MOD End 2014/08/07 MSI Sugiyama

        $count = 0;
        foreach ($select as $rec) {
            $count++;
            if ($count > $limit) {
                $isMoreData = true;
                break;
            }
            $myid = $rec['myid'] = $rec['siire_cd'];
            $rec['code'] = $rec['siire_cd'];
            $rec['name'] = $rec['siire_lnm'];
            $rec['kana'] = $rec['addr1_nm'] . $rec['addr2_nm'] . $rec['addr3_nm'] . $rec['addr4_nm'];
            $data[] = $rec;
            $hash[$myid] = $rec;
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );

        return array($data, $hash, $isMoreData);
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @return void
     */
    protected function _setScriptVar() {
        $this->view->dlg_title = '仕入先検索';
        if (Msi_Sys_Utils::arrIsSet($this->_params, 'mytitle')) { // 2022/07/28 ADD mihara
            $this->view->dlg_title = $this->_params['mytitle'];
        }
        // $this->view->dlg_tpl_search = 'search-f3.tpl';
        $this->view->dlg_tpl_search = 'search-siire.tpl';
        if (Msi_Sys_Utils::arrIsSet($this->_params, 'spe_search_kind2')) { // 2022/07/28 ADD mihara
            $this->view->dlg_tpl_search = 'search-siire-k2.tpl';
        }
        $this->view->dlg_tpl_head = 'head-siire.tpl';
        $this->view->dlg_tpl_list = 'list-siire.tpl';
        $this->view->l2_kana = '住所';

        // ひらがな対応 2018/05/25 ADD Kayo
        $getCstmKey = Msi_Sys_Utils::getCstmKeyArr();
        foreach ($getCstmKey as $rec) {
            if ($rec == 'hiragana') {
                $this->view->l1_kana = 'ひらがな';
                break;
            }
        }

        foreach (Msi_Sys_Utils::strArrayify_qw('s_code s_name s_kana s_siire_kbn') as $k) {
            @ $this->view->$k = $this->_params[$k];
        }
    }
    
    /**
     * 仕入先部門マスタで部門から仕入先を取得
     *
     * <AUTHOR> Kobayashi
     * @since 2025/04/30
     * @param $db db
     * @param $bumon_cd 部門コード
     * @return $siire 仕入先
     */
    private function getSiireBumon($db, $bumon_cd) {
        $sql = "
        SELECT 
            sbm.siire_cd
        FROM 
            siire_bumon_mst sbm
        WHERE 
            sbm.bumon_cd = '$bumon_cd'
            AND sbm.delete_flg = 0";
        $select = $db->easySelect($sql);
        
        $siire = null;
        if(Msi_Sys_Utils::myCount($select) > 0){
            $siire = implode("','",array_column($select,'siire_cd'));
        }
        return $siire;
    }

}
