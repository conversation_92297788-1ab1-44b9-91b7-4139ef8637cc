{include file="fdn_head_std.tpl"}
{include file="fdn_header_hachu2.tpl"}
<form action="#" id="customerinfo-form-id" ondragover="return false"  onMouseMove = "return false;">
    <div id="main">
        {include file="header_info.tpl"} 
        <!-- サイドメニュー開始 -->
        {include file="side_menu.tpl"}
        <!-- サイドメニュー終了 -->
        <!-- 本体開始 -->
        <div id="detail">
            <div id="estimate">
                <div class="mylist">
                    <table>
                        <tbody>
                            <tr>
                                <td rowspan="2" class="chk_select">選択</td>
                                <td rowspan="2" class="w15 hachusaki" >発注先</td>
                                <td style="border-right:1px solid #88B1D1;" class="nouhinbasho">納品場所</td>
                                <td class="t_kmk nouhinkbn">項目</td>
                                <td class="w10 hinpin">品目</td>
                                <td class="w20 tekiyo">摘要</td>
                                <td class="w5 tankaTitle" rowspan="2">単価</td>
                                <td class="w5 suriyoTitle" rowspan="2">数量</td>
                                <td rowspan="2" class="hachuStatus">発注</td>
                            </tr>
                            <tr>                               
                                <td>納品日時</td>
                                <td colspan ="3" style="border-top: 1px solid #88B1D1;">備考</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                {literal}
                <div id ="dataContent">
                </div>
                 <!-- 明細データ行 テンプレート -->
                <script type="text/template" id="item-template">
                        <dl>
                            <dt class="field_left">
                            <span class="radio_set">
                                <label for="i_chk" class="lbl_stamp_check mycheckBox"></label>
                                <input type="checkbox"  id="i_chk" name="i_chk" />
                                </label>
                            </span>
                            </dt>
                            <dt>
                            <input type="text"  name="hinmk" class="txt hinmkLabel" readonly = "readonly" />                       
                            <div class="label dlg_date hachusk_button"></div>
                            <input type ="hidden" name ="siire_cd" class="siire_cd"/>
                            </dt>
                            <dt>
                                <div class="product_detail">
                                <select class="komokku" data-id="">
                                </select>
                                <input type="text" name="n_place" class="txt i_n_place"  readonly="readonly" />
                                <div class="label dlg_place"></div>
                                <input type="text"  name="date" class="txt i_date date_auto_slash to_alpha_num" />
                                <div class="label dlg_date ymdcalendar"></div>
                                <input type="text"  name="time"  class="txt i_time time_auto_colon to_alpha_num" />
                                <div class="label dlg_time"></div>
                                <label class="labeltime">～</label>
                                <input type="text"  name="time"  class="txt i_time2 time_auto_colon to_alpha_num" />
                              <div class="label dlg_time"></div>
                                   </div>
                            </dt>
                            <dt>     
                                <input type="text" name="shohin_kbn" class= "txt shohin_kbn" readonly = "readonly">
                                <input type="text" name="hin_pin" class="txt i_hinpin" readonly = "readony" />
                                <input type="text" name="tekiyo" class="txt i_tekiyo" readonly="readonly" />
                                <input type="text" name="bikou" class="txt i_bikou"  maxlength="30"/>
                            </dt>
                            <dt>
                            <input type="text" name="tanka" class="txt i_tanka" readonly="readonly"/>
                            <input type="text" id="hachu_suryo" name="hachu_suryo"  class="txt i_amount" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                            <input type="text" id="hachu" name="hachu" class="txt i_stats" readonly="readonly"/>
                            </dt>
                        </dl>                                               
                </script>
                {/literal}

                <!-- 本体終了 -->
            </div>

            <div class="function">
                {*<input type="button" name="btn_add" id="btn_add" value="行コピー" />
                <input type="button" name="btn_del" id="btn_del" value="削除" />*}
                <label for="memo" class="lbl_memo option">備考</label>
                <textarea name="memo" id="memo" class="txt" cols="1" rows="10" maxlength="300"></textarea>
            </div>

            {include file="hachu/hachushori/hachu_footer.tpl"}

        </div>
    </div>
</form>
<script id="my-data-init-id" type="application/json">
    {$mydata_json|smarty:nodefaults}
</script>                
<script id="my-data-init-seko" type="application/json">
    {$mydata_seko|smarty:nodefaults}                
</script>
{include file="fdn_footer_std.tpl"}
