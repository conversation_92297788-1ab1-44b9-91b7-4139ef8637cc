<?php

/**
 * ロール権限対応表（二世会）
 * 
 * @category   config
 * @package    App
 * <AUTHOR> Sugiyama
 * @since      2018/05/15
 * @filesource
 */
return array(
	'system' => array(
		'funcs' => array(
			  'default.'
			, 'system.'
			, 'dev.'
			, 'mstr.'
			, 'saime.'
		)
	),
	
	// 管理者(マスタ使用可)
	'sysman' => array('specs' => '-ALL-', 'ngfuncs' => array('dev.', 'sample.')),

	// <editor-fold defaultstate="collapsed" desc="'manager'">
	'manager' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.customerinfo.'
			, 'juchu.mitsu.'
			, 'juchu.houji.'
			, 'hachu.'
			, 'juchu.denpyo.'
			, 'juchu.etc'
			, 'saiken.'
			, 'kanri.'
			, 'juchu.juchuhenko.'
			, 'kanri.pdf0602.'
			, 'juchu.juchuhenkoh.'
			, 'kanri.bechulist.'
			, 'juchu.bechuprn.'
			, 'kanri.sekolist'
			, 'kanri.shohinkbnuriprn.'
			, 'kanri.tantouriprn.'
			, 'kanri.zeibetuuriprn.'
			, 'kanri.uriprn.'
			, 'kanri.gojokaisekoprn.'
			, 'kanri.nyukinnikkiprn.'
			, 'kanri.urizanprn.'
			, 'kanri.urimikaishuprn.'
			, 'kanri.kaizanprn.'
			, 'kanri.siireshohinprn.'
			, 'kanri.dmdownload.'
			, 'juchu.bechuprn.'
			, 'kanri.kaishuprn.'
			, 'juchu.sekoinfocsv.'
			, 'zaiko.zaikokanriprn.'
			, 'zaiko.togetuzaikoiprn.'
			, 'kanri.shohinmotoprn.'
			, 'gaiji.'
			, 'sync.'
			, 'cale.'
			, 'juchu.juchuhenko.customerinfosave'
			, 'juchu.juchuhenko.customerinfodelete'
			, 'juchu.juchuhenko.tschange'
			, 'juchu.juchuhenko.tssave'
			, 'juchu.excel.index'
			, 'juchu.pdf0131.'
			, 'juchu.pdf0113.'
			, 'kanri.pdf0602.'
			, 'juchu.juchuhenko.checkgetujifixmitsu'
			, 'kanri.Pdf0601.'
			, 'juchu.pdf0115.'
			, 'kanri.pdf0601.'
			, 'juchu.juchuhenko.checkgetujifix'
			, 'kanri.pdf0707.'
			, 'kanri.pdf0704.'
			, 'kanri.pdf0708.'
			, 'kanri.pdf0709.'
			, 'kanri.pdf0717.'
			, 'kanri.pdf0701.'
			, 'kanri.pdf0702.'
			, 'kanri.pdf0705.'
			, 'kanri.pdf0706.'
			, 'kanri.pdf0711.'
			, 'juchu.pdf0104.'
			, 'kanri.urihojitantoprn.'
		),
		'readonly' => array(
			'juchu.bechudenpyo.'
			, 'juchu.denpyodlg.'
			, 'juchu.denpyosekyudlg.'
			, 'juchu.denpyononyudlg.'
			, 'juchu.pdf0101.'
		)
	), // </editor-fold>

	// <editor-fold defaultstate="collapsed" desc="'keiri'">
	'keiri' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.customerinfo.'  //  01.新規施行受付
			, 'juchu.mitsu.'   //  02.施行打合せ
			, 'juchu.bechulist.'  //  03.別注品一覧
			, 'juchu.houji.'   //  04.法事打合せ
			, 'juchu.denpyo.'   //  05.受注伝票（単品）
			, 'juchu.bechuprn.'   //  06.別注品一覧表
			, 'cale.'	 //  07.施行カレンダー
			, 'hachu.hachulist12.'  //  01.発注一覧
			, 'hachu.hachulistchklist.' //  02.発注一覧ChkList
			, 'saiken.seikyu1.'   //  01.葬儀請求書承認
			, 'saiken.seikyu3.'   //  02.別注品・単品請求書
			, 'saiken.nyukindenpyo.' //  03.入金入力
			, 'kanri.nyukinnikkiprn.' //  04.入金日記帳
			, 'kanri.urizanprn.'  //  05.売掛金残高一覧表
			, 'kanri.urimikaishuprn.' //  06.売掛金未回収一覧表
			, 'kanri.kaishuprn.'  //  07.回収報告書
			, 'saimu.hachudenpyo.'  //  01.発注伝票
			, 'saimu.hachudenchklist.' //  02.発注伝票ChkList
			, 'saimu.siiredenpyo.'  //  03.仕入伝票
			, 'saimu.kenpinsiire.'  //  04.検品・仕入確定
			, 'saimu.siiredenchklist.' //  05.仕入伝票ChkList
			, 'kanri.sekolist.'   //  01.施行一覧
			, 'kanri.hachulist22.'  //  02.発注管理
			, 'juchu.juchuhenko.'  //  03.受注内容変更(葬儀)
			, 'juchu.juchuhenkoh.'  //  04.受注内容変更(法事)
			, 'kanri.bechulist.'  //  05.別注品一覧
			, 'kanri.denpyo.'   //  06.受注伝票（単品）
			, 'kanri.bechuprn.'   //  07.別注品一覧表
			, 'kanri.zaimurendo.'  //  01.財務連動
			, 'kanri.shohinkbnuriprn.' //  02.商品区分売上一覧
			, 'kanri.tantouriprn.'  //  03.担当者売上集計表
			, 'kanri.siireshohinprn.' //  04.仕入商品一覧表
			, 'juchu.sekoinfocsv.'  //  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	// <editor-fold defaultstate="collapsed" desc="'somu'">
	'somu' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.customerinfo.'  //  01.新規施行受付
			, 'juchu.mitsu.'   //  02.施行打合せ
			, 'juchu.bechulist.'  //  03.別注品一覧
			, 'juchu.houji.'   //  04.法事打合せ
			, 'juchu.denpyo.'   //  05.受注伝票（単品）
			, 'juchu.bechuprn.'   //  06.別注品一覧表
			, 'cale.'	 //  07.施行カレンダー
			, 'hachu.hachulist12.'  //  01.発注一覧
			, 'hachu.hachulistchklist.' //  02.発注一覧ChkList
			, 'saiken.seikyu1.'   //  01.葬儀請求書承認
			, 'saiken.seikyu3.'   //  02.別注品・単品請求書
//			, 'saiken.nyukindenpyo.'	//  03.入金入力
//			, 'kanri.nyukinnikkiprn.'	//  04.入金日記帳
			, 'kanri.urizanprn.'  //  05.売掛金残高一覧表
			, 'kanri.urimikaishuprn.' //  06.売掛金未回収一覧表
			, 'kanri.kaishuprn.'  //  07.回収報告書
			, 'saimu.hachudenpyo.'  //  01.発注伝票
			, 'saimu.hachudenchklist.' //  02.発注伝票ChkList
			, 'saimu.siiredenpyo.'  //  03.仕入伝票
			, 'saimu.kenpinsiire.'  //  04.検品・仕入確定
			, 'saimu.siiredenchklist.' //  05.仕入伝票ChkList
			, 'kanri.sekolist.'   //  01.施行一覧
			, 'kanri.hachulist22.'  //  02.発注管理
			, 'juchu.juchuhenko.'  //  03.受注内容変更(葬儀)
			, 'juchu.juchuhenkoh.'  //  04.受注内容変更(法事)
			, 'kanri.bechulist.'  //  05.別注品一覧
			, 'kanri.denpyo.'   //  06.受注伝票（単品）
			, 'kanri.bechuprn.'   //  07.別注品一覧表
			, 'kanri.zaimurendo.'  //  01.財務連動
			, 'kanri.shohinkbnuriprn.' //  02.商品区分売上一覧
			, 'kanri.tantouriprn.'  //  03.担当者売上集計表
			, 'kanri.siireshohinprn.' //  04.仕入商品一覧表
			, 'juchu.sekoinfocsv.'  //  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	// 参照のみ
	// <editor-fold defaultstate="collapsed" desc="'sansho'">
	'sansho' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
//			, 'juchu.customerinfo.'		//  01.新規施行受付
//			, 'juchu.mitsu.'			//  02.施行打合せ(見積確定使用不可)
//			, 'juchu.bechulist.'		//  03.別注品一覧
//			, 'juchu.houji.'			//  04.法事打合せ(見積確定使用不可)
//			, 'juchu.denpyo.'			//  05.受注伝票（単品）
//			, 'juchu.bechuprn.'			//  06.別注品一覧表
			, 'cale.'					//  07.施行カレンダー
//			, 'hachu.hachulist12.'		//  01.発注一覧
//			, 'hachu.hachulistchklist.'	//  02.発注一覧ChkList
//			, 'saiken.seikyu1.'			//  01.葬儀請求書承認
//			, 'saiken.seikyu3.'			//  02.別注品・単品請求書
//			, 'saiken.nyukindenpyo.'	//  03.入金入力
//			, 'kanri.nyukinnikkiprn.'	//  04.入金日記帳
			, 'kanri.urizanprn.'		//  05.売掛金残高一覧表
			, 'kanri.urimikaishuprn.'	//  06.売掛金未回収一覧表
			, 'kanri.kaishuprn.'		//  07.回収報告書
//			, 'saimu.hachudenpyo.'		//  01.発注伝票
//			, 'saimu.hachudenchklist.'	//  02.発注伝票ChkList
//			, 'saimu.siiredenpyo.'		//  03.仕入伝票
//			, 'saimu.kenpinsiire.'		//  04.検品・仕入確定
//			, 'saimu.siiredenchklist.'	//  05.仕入伝票ChkList
			, 'kanri.sekolist.'			//  01.施行一覧
//			, 'kanri.hachulist22.'		//  02.発注管理
//			, 'juchu.juchuhenko.'		//  03.受注内容変更(葬儀)
//			, 'juchu.juchuhenkoh.'		//  04.受注内容変更(法事)
//			, 'kanri.bechulist.'		//  05.別注品一覧
//			, 'kanri.denpyo.'			//  06.受注伝票（単品）
//			, 'kanri.bechuprn.'			//  07.別注品一覧表
//			, 'kanri.zaimurendo.'		//  01.財務連動
			, 'kanri.shohinkbnuriprn.'	//  02.商品区分売上一覧
			, 'kanri.tantouriprn.'		//  03.担当者売上集計表
			, 'kanri.siireshohinprn.'	//  04.仕入商品一覧表
			, 'juchu.sekoinfocsv.'		//  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	// 事務
	// <editor-fold defaultstate="collapsed" desc="'jimu'">
	'jimu' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.'
			, 'cale.'
			, 'hachu.'
			, 'saiken.'
			, 'kanri.'
			, 'saimu.'
                        , 'gaiji.'
                        , 'analy.'
                        , 'cust.'
			, 'mstr.seikyukaiininfo.'	//会員情報登録
		)
	), // </editor-fold>
        
	// 事務2（顧客管理のみ可）
	// <editor-fold defaultstate="collapsed" desc="'jimu2'">
	'jimu2' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.'
			, 'cale.'
			, 'hachu.'
			, 'saiken.'
			, 'kanri.'
			, 'saimu.'
                        , 'gaiji.'
                        , 'analy.'
                        , 'cust.custform2.'             // 09.顧客管理 03.顧客管理
			, 'mstr.seikyukaiininfo.'	//会員情報登録
		)
	), // </editor-fold>

	// 見積のみ
	// <editor-fold defaultstate="collapsed" desc="'tanto'">
	'tanto' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.customerinfo.'  //  01.新規施行受付
			, 'juchu.mitsu.'   //  02.施行打合せ(見積確定使用不可)
			, 'juchu.bechulist.'  //  03.別注品一覧
			, 'juchu.houji.'   //  04.法事打合せ(見積確定使用不可)
			, 'juchu.denpyo.'			//  05.受注伝票（単品）
			, 'juchu.bechuprn.'   //  06.別注品一覧表
			, 'cale.'	 //  07.施行カレンダー
//			, 'hachu.hachulist12.'		//  01.発注一覧
//			, 'hachu.hachulistchklist.'	//  02.発注一覧ChkList
			, 'saiken.seikyu1.'			//  01.葬儀請求書承認
			, 'saiken.seikyu3.'   //  02.別注品・単品請求書
			, 'saiken.nyukindenpyo.'	//  03.入金入力
			, 'kanri.nyukinnikkiprn.'	//  04.入金日記帳
//			, 'kanri.urizanprn.'		//  05.売掛金残高一覧表
//			, 'kanri.urimikaishuprn.'	//  06.売掛金未回収一覧表
//			, 'kanri.kaishuprn.'		//  07.回収報告書
//			, 'saimu.hachudenpyo.'		//  01.発注伝票
//			, 'saimu.hachudenchklist.'	//  02.発注伝票ChkList
//			, 'saimu.siiredenpyo.'		//  03.仕入伝票
//			, 'saimu.kenpinsiire.'		//  04.検品・仕入確定
//			, 'saimu.siiredenchklist.'	//  05.仕入伝票ChkList
			, 'kanri.sekolist.'   //  01.施行一覧
//			, 'kanri.hachulist22.'		//  02.発注管理
//			, 'juchu.juchuhenko.'		//  03.受注内容変更(葬儀)
//			, 'juchu.juchuhenkoh.'		//  04.受注内容変更(法事)
			, 'kanri.bechulist.'		//  05.別注品一覧
			, 'kanri.denpyo.'			//  06.受注伝票（単品）
			, 'kanri.bechuprn.'   //  07.別注品一覧表
//			, 'kanri.zaimurendo.'		//  01.財務連動
//			, 'kanri.shohinkbnuriprn.'	//  02.商品区分売上一覧
//			, 'kanri.tantouriprn.'		//  03.担当者売上集計表
//			, 'kanri.siireshohinprn.'	//  04.仕入商品一覧表
//			, 'juchu.sekoinfocsv.'		//  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	// 見積(確定可)＆発注
	// <editor-fold defaultstate="collapsed" desc="'tantoh'">
	'tantoh' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.customerinfo.'  //  01.新規施行受付
			, 'juchu.mitsu.'   //  02.施行打合せ
			, 'juchu.bechulist.'  //  03.別注品一覧
			, 'juchu.houji.'   //  04.法事打合せ
			, 'juchu.denpyo.'   //  05.受注伝票（単品）
			, 'juchu.bechuprn.'   //  06.別注品一覧表
			, 'cale.'	 //  07.施行カレンダー
			, 'hachu.hachulist12.'  //  01.発注一覧
			, 'hachu.hachulistchklist.' //  02.発注一覧ChkList
			, 'saiken.seikyu1.'			//  01.葬儀請求書承認
			, 'saiken.seikyu3.'   //  02.別注品・単品請求書
			, 'saiken.nyukindenpyo.'	//  03.入金入力
			, 'kanri.nyukinnikkiprn.'	//  04.入金日記帳
//			, 'kanri.urizanprn.'		//  05.売掛金残高一覧表
//			, 'kanri.urimikaishuprn.'	//  06.売掛金未回収一覧表
//			, 'kanri.kaishuprn.'		//  07.回収報告書
			, 'saimu.hachudenpyo.'		//  01.発注伝票
//			, 'saimu.hachudenchklist.'	//  02.発注伝票ChkList
//			, 'saimu.siiredenpyo.'		//  03.仕入伝票
//			, 'saimu.kenpinsiire.'		//  04.検品・仕入確定
//			, 'saimu.siiredenchklist.'	//  05.仕入伝票ChkList
			, 'kanri.sekolist.'   //  01.施行一覧
//			, 'kanri.hachulist22.'		//  02.発注管理
			, 'juchu.juchuhenko.'  //  03.受注内容変更(葬儀)
			, 'juchu.juchuhenkoh.'  //  04.受注内容変更(法事)
			, 'kanri.bechulist.'  //  05.別注品一覧
			, 'kanri.denpyo.'			//  06.受注伝票（単品）
			, 'kanri.bechuprn.'   //  07.別注品一覧表
//			, 'kanri.zaimurendo.'		//  01.財務連動
//			, 'kanri.shohinkbnuriprn.'	//  02.商品区分売上一覧
//			, 'kanri.tantouriprn.'		//  03.担当者売上集計表
//			, 'kanri.siireshohinprn.'	//  04.仕入商品一覧表
//			, 'juchu.sekoinfocsv.'		//  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	// 一般
	// <editor-fold defaultstate="collapsed" desc="'ipan'">
	'ipan' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
//			, 'juchu.customerinfo.'		//  01.新規施行受付
//			, 'juchu.mitsu.'			//  02.施行打合せ(見積確定使用不可)
			, 'juchu.bechulist.'		//  03.別注品一覧
//			, 'juchu.houji.'			//  04.法事打合せ(見積確定使用不可)
//			, 'juchu.denpyo.'			//  05.受注伝票（単品）
			, 'juchu.bechuprn.'			//  06.別注品一覧表
//			, 'cale.'					//  07.施行カレンダー
//			, 'hachu.hachulist12.'		//  01.発注一覧
//			, 'hachu.hachulistchklist.'	//  02.発注一覧ChkList
			, 'saiken.seikyu1.'			//  01.葬儀請求書承認
			, 'saiken.seikyu3.'			//  02.別注品・単品請求書
//			, 'saiken.nyukindenpyo.'	//  03.入金入力
//			, 'kanri.nyukinnikkiprn.'	//  04.入金日記帳
//			, 'kanri.urizanprn.'		//  05.売掛金残高一覧表
//			, 'kanri.urimikaishuprn.'	//  06.売掛金未回収一覧表
//			, 'kanri.kaishuprn.'		//  07.回収報告書
//			, 'saimu.hachudenpyo.'		//  01.発注伝票
//			, 'saimu.hachudenchklist.'	//  02.発注伝票ChkList
//			, 'saimu.siiredenpyo.'		//  03.仕入伝票
//			, 'saimu.kenpinsiire.'		//  04.検品・仕入確定
//			, 'saimu.siiredenchklist.'	//  05.仕入伝票ChkList
//			, 'kanri.sekolist.'			//  01.施行一覧
//			, 'kanri.hachulist22.'		//  02.発注管理
//			, 'juchu.juchuhenko.'		//  03.受注内容変更(葬儀)
//			, 'juchu.juchuhenkoh.'		//  04.受注内容変更(法事)
//			, 'kanri.bechulist.'		//  05.別注品一覧
//			, 'kanri.denpyo.'			//  06.受注伝票（単品）
//			, 'kanri.bechuprn.'			//  07.別注品一覧表
//			, 'kanri.zaimurendo.'		//  01.財務連動
//			, 'kanri.shohinkbnuriprn.'	//  02.商品区分売上一覧
//			, 'kanri.tantouriprn.'		//  03.担当者売上集計表
//			, 'kanri.siireshohinprn.'	//  04.仕入商品一覧表
//			, 'juchu.sekoinfocsv.'		//  05.施行情報ダウンロード
//			, 'kanri.getujifix.'		//  06.月次確定
//			, 'gaiji.gaiji,'			//  07.外字登録
//			, 'kanri.importkaiin.'		//  08.会員情報取り込み
		)
	), // </editor-fold>

	/*
	'jimu' => array(
		'funcs' => array(
			  'default.'
			, 'mref.'
			, 'juchu.'
			, 'hachu.'
			, 'kanri.'
			, 'saiken.'
			, 'saimu.'
			, 'zaiko.'
			, 'sukko.'
			, 'gaiji.'
			, 'sync.'
			, 'cale.'
		)
	),
*/
	// <editor-fold defaultstate="collapsed" desc="'hachuinp'">
	'hachuinp' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'hachu.'
			, 'juchu.etc'
			, 'saimu.'
			, 'zaiko.'
			, 'kanri.shohinkbnuriprn.'
			, 'kanri.tantouriprn.'
			, 'kanri.zeibetuuriprn.'
			, 'kanri.uriprn.'
			, 'kanri.gojokaisekoprn.'
			, 'kanri.nyukinnikkiprn.'
			, 'kanri.urizanprn.'
			, 'kanri.urimikaishuprn.'
			, 'kanri.kaizanprn.'
			, 'kanri.siireshohinprn.'
			, 'zaiko.zaikokanriprn.'
			, 'zaiko.togetuzaikoiprn.'
			, 'kanri.shohinmotoprn.'
			, 'juchu.bechuprn'
			, 'kanri.kaishuprn'
			, 'kanri.idodenpyo.'
			, 'gaiji.'
			, 'cale.'
			, 'juchu.pdf0101.'
			, 'juchu.excel.'
			, 'kanri.Pdf0601.'
			, 'juchu.pdf0115.'
			, 'juchu.pdf0113.'
			, 'kanri.pdf0601.'
			, 'kanri.pdf0707.'
			, 'kanri.pdf0704.'
			, 'kanri.pdf0708.'
			, 'kanri.pdf0709.'
			, 'kanri.pdf0717.'
			, 'juchu.juchuhenko.checkgetujifixsiire'
			, 'juchu.juchuhenko.checkgetujifixkenpin'
			, 'kanri.pdf0712.'
			, 'kanri.pdf0701.'
			, 'kanri.pdf0702.'
			, 'kanri.pdf0705.'
			, 'kanri.pdf0706.'
			, 'kanri.pdf0711.'
			, 'juchu.pdf0104.'
		),
		'readonly' => array(
			  'juchu.mitsu.'
			, 'juchu.bechulist'
			, 'juchu.houji.'
			, 'juchu.denpyo'
			, 'saiken.pdf1102.'
			, 'juchu.bechudenpyo.'
			, 'juchu.denpyodlg.'
			, 'juchu.denpyosekyudlg.'
			, 'juchu.denpyononyudlg.'
		)
	), // </editor-fold>

	// <editor-fold defaultstate="collapsed" desc="'ro'">
	'ro' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'juchu.pdf0101.'
			, 'juchu.juchuhenko.tschange'
			, 'juchu.excel.'
			, 'juchu.pdf0113.'
			, 'kanri.pdf0602.'
			, 'kanri.Pdf0601.'
			, 'juchu.pdf0115.'
			, 'saiken.pdf1102.'
			, 'kanri.pdf0601.'
			, 'juchu.pdf0104.s'
		),
		'readonly' => array(
			'juchu.mitsu.'
			, 'juchu.bechulist'
			, 'juchu.houji.'
			, 'juchu.denpyo'
			, 'juchu.bechudenpyo.'
			, 'juchu.bechuprn.'
			, 'juchu.denpyodlg.'
			, 'juchu.denpyosekyudlg.'
			, 'juchu.denpyononyudlg.'
			, 'cale.'
		)
	), // </editor-fold>

	// <editor-fold defaultstate="collapsed" desc="'zaikoinp'">
	'zaikoinp' => array(
		'funcs' => array(
			'default.'
			, 'mref.'
			, 'zaiko.'
			, 'kanri.idodenpyo.'
			, 'juchu.pdf0101.'
			, 'juchu.juchuhenko.tschange'
			, 'juchu.pdf0104.'
			, 'juchu.pdf0113.'
			, 'kanri.pdf0602.'
			, 'kanri.Pdf0601.'
			, 'juchu.pdf0115.'
		),
		'readonly' => array(
			'juchu.customerinfo.'
			, 'juchu.mitsu.'
			, 'juchu.bechulist'
			, 'juchu.houji.'
			, 'juchu.denpyo'
			, 'saiken.pdf1102.'
			, 'juchu.bechudenpyo.'
			, 'juchu.denpyodlg.'
			, 'juchu.denpyosekyudlg.'
			, 'juchu.denpyononyudlg.'
			, 'hachu.'
			, 'juchu.etc'
			, 'saimu.'
		)
	), // </editor-fold>

	// <editor-fold defaultstate="collapsed" desc="'local'">
	'local' => array('funcs' => array('default.'
			, 'mref.'
			, 'juchu.'
			, 'kanri.pdf'
			, 'sync.'
			, 'gaiji.'
		),
		'ngfuncs' => array(
			'dev.'
			, 'sample.' // for safety
		)
	), // </editor-fold>

    	// 施行担当者
	// <editor-fold defaultstate="collapsed" desc="'sekou'">
        'sekou' => array(
                'funcs' => array(
                        'default.'
                        , 'mref.'
                        //  01.受注業務
                        , 'juchu.customerinfo.'     //  01.新規施行受付
                        , 'juchu.mitsu.'            //  02.施行打合せ
                        , 'juchu.bechulist.'        //  03.別注品一覧
                        , 'juchu.houji.'            //  04.法事打合せ
                        , 'juchu.denpyo.'           //  05.受注伝票（単品）
                        , 'juchu.bechuprn.'         //  06.別注品一覧表
                        , 'cale.'                   //  07.施行カレンダー
                        //  02.発注業務
                        , 'hachu.hachulist12.'      //  01.発注一覧
                        , 'hachu.hachulistchklist.' //  02.発注一覧ChkList
                        //  03.債権管理
                        , 'saiken.seikyu1.'         //  01.葬儀請求書承認
                        , 'saiken.seikyu3.'         //  02.別注品・単品請求書
                        , 'saiken.ryoshu.'          //     領収書発行
                        , 'saiken.nyukindenpyo.'    //  03.入金入力
                        , 'kanri.nyukinnikkiprn.'   //  04.入金日記帳
                        , 'kanri.urizanprn.'        //  05.売掛金残高一覧表
                        , 'kanri.urimikaishuprn.'   //  06.売掛金未回収一覧表
                        , 'kanri.kaishuprn.'        //  07.回収報告書
                        // 06.管理アシスト業務
                        , 'kanri.sekolist.'         //  01.施行一覧
                        , 'kanri.hachulist22.'      //  02.発注管理
                        , 'juchu.juchuhenko.'       //  03.受注内容変更(葬儀)
                        , 'juchu.juchuhenkoh.'      //  04.受注内容変更(法事)
                        , 'kanri.bechulist.'        //  05.別注品一覧
                        , 'kanri.denpyo.'           //  06.受注伝票（単品）
                        , 'kanri.bechuprn.'         //  07.別注品一覧表
		)
	), // </editor-fold>
    
    	// 施行担当者2
	// <editor-fold defaultstate="collapsed" desc="'sekou2'">
        'sekou2' => array(
                'funcs' => array(
                        'default.'
                        , 'mref.'
                        //  01.受注業務
                        , 'juchu.customerinfo.'     //  01.新規施行受付
                        , 'juchu.mitsu.'            //  02.施行打合せ
                        , 'juchu.bechulist.'        //  03.別注品一覧
                        , 'juchu.houji.'            //  04.法事打合せ
                        , 'juchu.denpyo.'           //  05.受注伝票（単品）
                        , 'juchu.bechuprn.'         //  06.別注品一覧表
                        , 'cale.'                   //  07.施行カレンダー
                        //  02.発注業務
                        , 'hachu.hachulist12.'      //  01.発注一覧
                        , 'hachu.hachulistchklist.' //  02.発注一覧ChkList
                        // 06.管理アシスト業務
                        , 'kanri.sekolist.'         //  01.施行一覧
                        , 'kanri.hachulist22.'      //  02.発注管理
                        , 'juchu.juchuhenko.'       //  03.受注内容変更(葬儀)
                        , 'juchu.juchuhenkoh.'      //  04.受注内容変更(法事)
                        , 'kanri.bechulist.'        //  05.別注品一覧
                        , 'kanri.denpyo.'           //  06.受注伝票（単品）
                        , 'kanri.bechuprn.'         //  07.別注品一覧表
		)
	), // </editor-fold>
    
    	// 受注担当者
	// <editor-fold defaultstate="collapsed" desc="'juchu'">
        'juchu' => array(
                'funcs' => array(
                        'default.'
                        , 'mref.'
                        //  01.受注業務
                        , 'juchu.customerinfo.'     //  01.新規施行受付
                        , 'juchu.mitsu.'            //  02.施行打合せ
                        , 'juchu.bechulist.'        //  03.別注品一覧
                        , 'juchu.houji.'            //  04.法事打合せ
                        , 'juchu.denpyo.'           //  05.受注伝票（単品）
                        , 'juchu.bechuprn.'         //  06.別注品一覧表
                        , 'cale.'                   //  07.施行カレンダー
		)
	), // </editor-fold>
    
        // 営業担当者
        // <editor-fold defaultstate="collapsed" desc="'eigyo'">
        'eigyo' => array(
                'funcs' => array(
                    // 01.受注業務
                    'juchu.',
                    'cale.',                    //  07.施行カレンダー
                    // 02.発注業務
                    'hachu.',
                    // 08.マスタ管理
                    'mstr.seikyukaiininfo.',    // 30.会員情報登録
		),
		'ngfuncs' => array(
                    'juchu.juchuhenko.',        // 03.受注内容変更(葬儀)
                    'juchu.juchuhenkoh.',       // 04.受注内容変更(法事)
                    'juchu.sekoinfocsv.',       // 07.管理業務 05.施行情報ダウンロード
		)
	), // </editor-fold>
    
        // 葬祭営業担当者
        // <editor-fold defaultstate="collapsed" desc="'eigyo2'">
        'eigyo2' => array(
                'funcs' => array(
                        'default.'
                        , 'mref.'
                        //  01.受注業務
                        , 'juchu.customerinfo.'     //  01.新規施行受付
                        , 'juchu.mitsu.'            //  02.施行打合せ
                        , 'juchu.bechulist.'        //  03.別注品一覧
                        , 'juchu.houji.'            //  04.法事打合せ
                        , 'juchu.denpyo.'           //  05.受注伝票（単品）
                        , 'juchu.bechuprn.'         //  06.別注品一覧表
                        , 'cale.'                   //  07.施行カレンダー
                        //  02.発注業務
                        , 'hachu.hachulist12.'      //  01.発注一覧
                        , 'hachu.hachulistchklist.' //  02.発注一覧ChkList
                        // 06.管理アシスト業務
                        , 'kanri.sekolist.'         //  01.施行一覧
                        , 'kanri.hachulist22.'      //  02.発注管理
                        , 'juchu.juchuhenko.'       //  03.受注内容変更(葬儀)
                        , 'juchu.juchuhenkoh.'      //  04.受注内容変更(法事)
                        , 'kanri.bechulist.'        //  05.別注品一覧
                        , 'kanri.denpyo.'           //  06.受注伝票（単品）
                        , 'kanri.bechuprn.'         //  07.別注品一覧表
                        // 08.マスタ管理
                        , 'mstr.seikyukaiininfo.'   // 30.会員情報登録
                        // 09.顧客管理
                        , 'cust.'
		),
		'ngfuncs' => array(
                    'juchu.sekoinfocsv.',       // 07.管理業務 05.施行情報ダウンロード
		)
	), // </editor-fold>

        // 受注入力支援者
        // <editor-fold defaultstate="collapsed" desc="'juchu2'">
        'juchu2' => array(
                'funcs' => array(
                    // 01.受注業務
                    'juchu.',
                    'cale.',                    //  07.施行カレンダー
                    // 02.発注業務
                    'hachu.',
                    // 06.管理アシスト業務
                    'kanri.bechulist.',         //  05.別注品一覧
                    // 08.マスタ管理
                    'mstr.seikyukaiininfo.',    // 30.会員情報登録
		),
		'ngfuncs' => array(
                    'juchu.juchuhenko.',        // 03.受注内容変更(葬儀)
                    'juchu.juchuhenkoh.',       // 04.受注内容変更(法事)
                    'juchu.sekoinfocsv.',       // 07.管理業務 05.施行情報ダウンロード
		)
	), // </editor-fold>    
);
