<?php
  /**
   * DataMapper_ShohinEx
   *
   * 商品 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2013/xx/xx
   * @version    2014/03/27 MSI Mihara  dai_bunrui_cd,chu_bunrui_cd,shohin_kbn 項目を shohin_bunrui_mst と join して再度追加
   * @filesource 
   */

  /**
   * 商品 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2013/xx/xx
   * @version    2014/03/27 MSI Mihara  dai_bunrui_cd,chu_bunrui_cd,shohin_kbn 項目を shohin_bunrui_mst と join して再度追加
   */
class DataMapper_ShohinEx extends DataMapper_Abstract
{
    /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2013/xx/xx
     * @version    2014/03/27 MSI Mihara  dai_bunrui_cd,chu_bunrui_cd,shohin_kbn 項目を shohin_bunrui_mst と join して再度追加
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array(), $isDateEffective=true)
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.shohin_cd ';
        }

        if ( $isDateEffective ) {
            $dateWhere     = " AND m.hanbai_st_ymd <= CURRENT_DATE AND m.hanbai_end_ymd >= CURRENT_DATE ";
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
  SELECT 
	  m.shohin_cd,
	  m.shohin_nm,
	  m.kani_shohin_nm,
	  m.shohin_knm,
	  m.shohin_tkiyo_nm,
	  m.tani_cd,
	  m.irisu,
	  m.tani_ovr_kbn,
	  m.setshohin_kbn,
	  m.zaiko_knri_kbn,
	  m.hachu_kbn,
	  m.kashidashi_kbn,
	  m.size_nm,
	  m.uri_zei_kbn,
	  m.shiire_zei_kbn,
	  m.genka_hyoka_kbn,
	  m.siire_cd,
	  m.hyojn_nyuko_readtime,
	  m.maker_hinban,
	  m.nm_input_kbn,
	  m.siiretnk_input_kbn,
	  m.hihyoji_kbn,
	  m.teryo_hachu_ten,
	  m.teryo_hachu_suryo,
	  m.hanbai_st_ymd,
	  m.hanbai_end_ymd,
	  m.siire_st_ymd,
	  m.siire_end_ymd,
	  m.uri_kamoku_cd,
	  m.siire_kamoku_cd,
	  m.shohin_naiyo,
	  m.henpin_kahi_kbn,
	  m.memo,
	  m.tnk_chg_kbn,
	  m.hoshi_umu_kbn,
	  m.gazo_file_nm,
	  m.gazo_img,
	  m.gazo_tn_data,
	  m.delete_flg,
	  m._req_id,
	  tk.tekiyo_st_date as tanka_st_date,
	  tk.tekiyo_ed_date as tanka_ed_date,
	  tk.hanbai_tnk,
	  tk.siire_tnk,
      tk._req_id as tanka__req_id,
      br.dai_bunrui_cd  AS dai_bunrui_cd,
      br.chu_bunrui_cd  AS chu_bunrui_cd,
      br.shohin_kbn  AS shohin_kbn
  FROM shohin_mst m
  LEFT JOIN shohin_tanka_mst tk
    ON m.shohin_cd = tk.shohin_cd
   AND tk.delete_flg=0
   AND ( tk.tekiyo_st_date <= CURRENT_DATE AND tk.tekiyo_ed_date >= CURRENT_DATE )
  LEFT JOIN shohin_bunrui_mst br
    ON m.shohin_cd = br.shohin_cd
   AND br.delete_flg=0
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
}
