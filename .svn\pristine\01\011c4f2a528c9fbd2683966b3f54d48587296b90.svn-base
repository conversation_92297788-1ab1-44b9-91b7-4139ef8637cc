<?php

/**
 * 商品アップグレードマスタマッピング 処理
 * application/models/{DataMapper,Logic} に置いたほうがよいが、
 * 個別対応やパッチが見込まれるので、こちらに置いた。
 *
 * @category   App
 * @package    controllers\Mstr2
 * <AUTHOR> Mogi
 * @since      2020/10/xx
 * @filesource 
 */

/**
 * 商品アップグレードマスタマッピング 処理
 *
 * @category   App
 * @package    controllers\Mstr2
 * <AUTHOR> Mogi
 * @since      2020/10/xx
 */
abstract class Mstr2_ShohinUpgradeMstLogicAbst {

    /**
     * @ignore
     * 内部データ交換用
     */
    protected static $_dataExchange = null;

    /**
     * 商品アップグレードマスタ の登録処理
     *
     * <AUTHOR> Mogi
     * @since      2020/10/xx
     * @param      array  $data データ  個別行データは _dtl
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function doUpdate($data) {
        static::$_dataExchange = array('data' => $data);

        /* -- shohin_upgrade_mst --
          (*:primary key)
          ----------------------------------------------------------------------
         * id                   | ID               
          gojokai_kbn           | 互助会区分
          plan_bumon_cd         | 施行プラン部門コード
          plan_shohin_cd        | 施行プラン商品コード
          plan_dai_bunrui_cd    | 施行プラン大分類コード
          plan_chu_bunrui_cd    | 施行プラン中分類コード
          plan_shohin_kbn       | 施行プラン商品区分
          plan_suryo            | 施行プラン数量    (※1で固定)
          bumon_cd              | 部門コード
          shohin_cd             | 商品コード
          dai_bunrui_cd         | 大分類コード
          chu_bunrui_cd         | 中分類コード
          shohin_kbn            | 商品区分
         */

        $db = Msi_Sys_DbManager::getMyDb();

        // Msi_Sys_Utils::debug( '** doUpdate() data=>' . Msi_Sys_Utils::dump($data) );
        $gojokai_kbn = $data['gojokai_kbn'];
        $plan_bumon_cd = $data['plan_bumon_cd'];
        $plan_shohin_cd = $data['plan_shohin_cd'];
        $plan_dai_bunrui_cd = $data['plan_dai_bunrui_cd'];
        $plan_chu_bunrui_cd = $data['plan_chu_bunrui_cd'];
        $plan_shohin_kbn = $data['plan_shohin_kbn'];

        // 1:レコードの並び替え・重複チェック（javascript側で制御されているが念のため）
        $dataCol = $data['_dtl'];
        array_multisort(array_column($dataCol, 'myid'), SORT_ASC, $dataCol);
        $duplicate = self::checkDuplicate($dataCol);
        if ($duplicate === false) {
            $this->addErrorMsg('レコードが重複しています。');
            return false;
        }
//        Msi_Sys_Utils::debug( 'dataCol=>' . Msi_Sys_Utils::dump($dataCol) );
        // 2:既存のデータでdataColに含まれないものを削除
        $dbparamPre = array(
            'gojokai_kbn' => $gojokai_kbn,
            'plan_bumon_cd' => $plan_bumon_cd,
            'plan_shohin_cd' => $plan_shohin_cd,
            'plan_dai_bunrui_cd' => $plan_dai_bunrui_cd,
            'plan_chu_bunrui_cd' => $plan_chu_bunrui_cd,
            'plan_shohin_kbn' => $plan_shohin_kbn
        );
        $oldMyIds = self::getOldMyid($db, $dbparamPre);
//        Msi_Sys_Utils::debug( 'oldMyIds=>' . Msi_Sys_Utils::dump($oldMyIds) );
        $cnt = static::_pre_proc($db, $dbparamPre, $dataCol, $oldMyIds);

        // テーブルからMAX(id)を取得
        $id = self::getMaxId($db);
        // 3:データ登録
        foreach ($dataCol as $line) {

            if (!in_array($line['myid'], $oldMyIds)) { // 既存データに無い場合のみ登録を行う
                list($bumon_cd, $dai_bunrui_cd, $chu_bunrui_cd, $shohin_kbn, $shohin_cd) = preg_split('|_|', $line['myid']);
                $id++;
                $dbparam = array(
                    'id' => $id,
                    'gojokai_kbn' => $gojokai_kbn,
                    'plan_bumon_cd' => $plan_bumon_cd,
                    'plan_shohin_cd' => $plan_shohin_cd,
                    'plan_dai_bunrui_cd' => $plan_dai_bunrui_cd,
                    'plan_chu_bunrui_cd' => $plan_chu_bunrui_cd,
                    'plan_shohin_kbn' => $plan_shohin_kbn,
                    'plan_suryo' => 1,
                    'bumon_cd' => $bumon_cd,
                    'shohin_cd' => $shohin_cd,
                    'dai_bunrui_cd' => $dai_bunrui_cd,
                    'chu_bunrui_cd' => $chu_bunrui_cd,
                    'shohin_kbn' => $shohin_kbn,
                );

                $cnt += static::_main_proc($db, $dbparam);
            }
        }
    }

    /**
     * 前処理
     * 既存データで削除対象のものを論理削除
     *
     * <AUTHOR> Mogi
     * @since      2020/11/xx
     * @param      Zend_Db_Adapter_Abstract $db
     * @param      $_data  array
     * @param      $dataCol  array
     * @param      $oldDataCol  array
     * @return     integer 対象処理行数
     */
    protected static function _pre_proc($db, $_data, $dataCol, $oldDataCol) {
        $data = static::_pre_proc_data_fixup($_data);

        // dataColのmyidを配列にする
        $dataColMyid = array();
        foreach ($dataCol as $onerow) {
            $dataColMyid[] = $onerow['myid'];
        }

        // 既存データをループして、dataColに無いものを論理削除する
        foreach ($oldDataCol as $oldMyid) {

            if (!in_array($oldMyid, $dataColMyid)) {

                list($data['bumon_cd'], $data['dai_bunrui_cd'], $data['chu_bunrui_cd'], $data['shohin_kbn'], $data['shohin_cd']) = preg_split('|_|', $oldMyid);

//              Msi_Sys_Utils::debug( 'delete=>' . Msi_Sys_Utils::dump($select) );
                // 既存のデータを削除
                $cnt = $db->easyExecute(<<< END_OF_SQL
                UPDATE shohin_upgrade_mst SET delete_flg = 1
                WHERE gojokai_kbn=:gojokai_kbn
                    AND plan_bumon_cd=:plan_bumon_cd
                    AND plan_shohin_cd=:plan_shohin_cd
                    AND plan_dai_bunrui_cd=:plan_dai_bunrui_cd
                    AND plan_chu_bunrui_cd=:plan_chu_bunrui_cd
                    AND plan_shohin_kbn=:plan_shohin_kbn
                    AND bumon_cd=:bumon_cd
                    AND dai_bunrui_cd=:dai_bunrui_cd
                    AND chu_bunrui_cd=:chu_bunrui_cd
                    AND shohin_kbn=:shohin_kbn
                    AND shohin_cd=:shohin_cd
END_OF_SQL
                        , $data);
            }
        }

        return;
    }

    /**
     * 前処理
     * データ直前変換
     *
     * <AUTHOR> Mihara
     * @since      2016/05/xx
     * @param      $data  array
     * @return     array   変換済データ
     */
    protected static function _pre_proc_data_fixup($data) {
        return $data;
    }

    /**
     * 本処理
     * web_disp_shohin_mst 表に登録
     *
     * <AUTHOR> Mihara
     * @since      2016/05/xx
     * @param      Zend_Db_Adapter_Abstract $db
     * @param      $_data   array
     * @return     integer 対象処理行数
     */
    protected static function _main_proc($db, $_data) {
        static $stmt = null;

        $sql = <<< END_OF_SQL
        INSERT INTO shohin_upgrade_mst 
        (
            id, gojokai_kbn, plan_bumon_cd, plan_shohin_cd, plan_dai_bunrui_cd,
            plan_chu_bunrui_cd, plan_shohin_kbn, plan_suryo, bumon_cd, shohin_cd, 
            dai_bunrui_cd, chu_bunrui_cd, shohin_kbn
        ) VALUES (
            :id, :gojokai_kbn, :plan_bumon_cd, :plan_shohin_cd, :plan_dai_bunrui_cd,
            :plan_chu_bunrui_cd, :plan_shohin_kbn, :plan_suryo, :bumon_cd, :shohin_cd,
            :dai_bunrui_cd, :chu_bunrui_cd, :shohin_kbn
        )
END_OF_SQL;

        if ($stmt === null) {
            $stmt = $db->execPrepared($sql);
        }

        $data = static::_main_proc_data_fixup($_data);

        $cnt = $db->stmtExecute($stmt, $data);

        return $cnt;
    }

    /**
     * 本処理
     * データ直前変換
     *
     * <AUTHOR> Mihara
     * @since      2016/05/xx
     * @param      $data   array
     * @return     array   変換済データ
     */
    protected static function _main_proc_data_fixup($data) {
        return $data;
    }

    /**
     * 登録前 重複チェック処理
     * <AUTHOR> Mogi
     * @since      2020/11/04
     * @param array $dataCol	
     * @return true or false
     */
    private function checkDuplicate($dataCol) {

        $oldMyId = null;
        $my_id = array();

        foreach ($dataCol as $row) {
            // IDが同じ（部門・大分類・中分類・商品区分・商品コードが重複）の場合はreturn
            $my_id[] = $row['myid'];
            if ($oldMyId === $row['myid']) {
                return false;
            }
            $oldMyId = $row['myid'];
        }
//        Msi_Sys_Utils::debug( 'my_id=>' . Msi_Sys_Utils::dump($my_id) );
        return;
    }

    /**
     * 既存のテーブルに保存された対象データのmyidを配列で返す
     * <AUTHOR> Mogi
     * @since      2020/11/04
     * @param array $db	
     * @param array $params	
     * @return array $select
     */
    private function getOldMyid($db, $params) {

        $select = $db->getOneValues(<<< END_OF_SQL
        SELECT 
            bumon_cd || '_' || dai_bunrui_cd || '_' || chu_bunrui_cd || '_' || shohin_kbn || '_' || shohin_cd AS myid
        FROM shohin_upgrade_mst
        WHERE gojokai_kbn=:gojokai_kbn
            AND plan_bumon_cd=:plan_bumon_cd
            AND plan_shohin_cd=:plan_shohin_cd
            AND plan_dai_bunrui_cd=:plan_dai_bunrui_cd
            AND plan_chu_bunrui_cd=:plan_chu_bunrui_cd
            AND plan_shohin_kbn=:plan_shohin_kbn
            AND delete_flg = 0
END_OF_SQL
                , $params);
        return $select;
    }

    /**
     * 既存のテーブルに保存されているidの最大値を返す
     * <AUTHOR> Mogi
     * @since      2020/11/04
     * @param array $db	
     * @return array $maxid
     */
    private function getMaxId($db) {
        $maxid = $db->getOneVal(<<< END_OF_SQL
        SELECT 
            MAX(id)
        FROM shohin_upgrade_mst
END_OF_SQL
        );
        return $maxid;
    }

}
