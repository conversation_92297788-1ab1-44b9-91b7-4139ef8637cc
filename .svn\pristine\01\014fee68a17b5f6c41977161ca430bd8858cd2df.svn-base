/* 
 * 見積書処理
 * 共通処理はlib.mitsu.seikyuに記述している
 */
var appmtsk = appmtsk || {};
$(function() {
    "use strict";

    appmtsk = {
        o_price: '御見積り金額',
        printURI: '/juchu/pdf0113',
        viewKbn: '1', // 画面区分 1:見積書 2：請求書
        setSideMenu: function() {
            var optsEst = {
                //refModel: appsimu.appModel,
                showFooter: true
            };
            $.msiSideMenuLib.setSideMenu(optsEst);
        },
        initializeView: function(dataApp) {
// 2017.04.18 それぞれの部門に計上する
//葬儀・法事は計上部門は「00010：塩釜本社」のみなので、変更不可にする
//		    $("#hall_cd").attr("disabled", "disabled");
//			$('#hall_cd').select2('val', '00010');

//            if ($.msiJqlib.isNullEx(dataApp.seko_plan_cd)) {
//                // 基本プラン未選択
//                $("#estimate-div-wrapper").hide();
//                $(".buttons input").hide();// 保存・印刷・取消ボタン
//                $.msiJqlib.showWarn(dataApp.no_seko_plan_msg);
//                return false;
//            }
            if (dataApp.moushi_kbn === '19') { // オーダーメイド
                applms.printURI = '/juchu/pdf0140';
            } 
            if (!$.msiJqlib.isNullEx(dataApp.jichu_kakute_ymd)) {
                // 確定済み
                $(".function #btn_kakutei").hide(); // 見積確定ボタン
//権限により見積取消ボタン使用可不可を設定
//                $(".function #btn_torikesi").show();// 見積取消ボタン
				if (dataApp.login_role === "sysman" || dataApp.login_role === "jimu" || dataApp.login_role === "tantoh") {
	                $(".function #btn_torikesi").show(); // 見積取消ボタン
					$(".function #btn_saimitsu").show(); // 再見積ボタン
                } else {
	                $(".function #btn_torikesi").hide(); // 見積取消ボタン
					$(".function #btn_saimitsu").hide(); // 再見積ボタン
                }
                $(".function #btn_add").hide();// 追加ボタン
                $(".function #btn_del").hide();// 削除ボタン
                $(".buttons #btn_save").hide();// 保存ボタン
                $(".buttons #btn_cancel").hide();// 取消ボタン
//                $(".function #btn_saimitsu").show();// 再見積ボタン
                $(".buttons #btn_allDelete").hide();// 一括削除ボタン
            } else {
//                $(".function #btn_kakutei").show(); // 見積確定ボタン
//権限により見積確定ボタン使用可不可を設定
                if (dataApp.login_role === "sysman" || dataApp.login_role === "jimu" || dataApp.login_role === "tantoh") {
	                $(".function #btn_kakutei").show(); // 見積確定ボタン
                } else {
	                $(".function #btn_kakutei").hide(); // 見積確定ボタン
                }
                $(".function #btn_torikesi").hide();// 見積取消ボタン
                $(".function #btn_add").show();// 追加ボタン
                $(".function #btn_del").show();// 削除ボタン
                $(".buttons #btn_save").show();// 保存ボタン
                $(".buttons #btn_cancel").show();// 取消ボタン
                $(".function #btn_saimitsu").hide();// 再見積ボタン
//権限により見積確定ボタン使用可不可を設定
                if (dataApp.login_role === "sysman" || data.dataApp.login_role === "jimu" || dataApp.login_role === "tantoh") {
                    $(".buttons #btn_allDelete").show();// 一括削除ボタン
                } else {
                    $(".buttons #btn_allDelete").hide();// 一括削除ボタン
                }
                
                // 一旦追加ボタンを活性化
                applms.clearDisabledButtonStyle($(".function #btn_add"));
                // 一旦削除ボタンを非活性化
                applms.setDisabledButtonStyle($(".function #btn_del"));
            }
            // 承認されたら見積取消しボタン非表示
            if (dataApp.shonin.hasShonin) {
                $(".function #btn_torikesi").hide();// 見積取消ボタン
                $(".function #btn_saimitsu").hide();// 再見積ボタン
            }

            return true;
        },
        doCancel: function() {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        mitsukakutei: function(that) {
            if (that.isChangeShowWarn()) {
                return;
            }
            if (!confirm('見積を確定します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/estimatekakutei',
                data: {
                    dataAppJson:  JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'WARN') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        mitsutorikesi: function() {
            if (!confirm('見積確定を取り消します。よろしいですか？')) {
                return;
            }
            if (!confirm('確定後に変更されたデータはすべて削除されます。よろしいですか？')) {
                return;
            }
            $.msiJqlib.setProgressing( true );
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/estimatetorikesi',
                data: {
                    dataAppJson:  JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'WARN') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doAllDelete: function () {
            var sekoNo = applms.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('見積もりを一括削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/estimatedelete',
                data: {
                    dataAppJson: JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        window.location.reload();
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        }
    };
    var data = JSON.parse(_.unescape($('#data-estimate').text()));

});