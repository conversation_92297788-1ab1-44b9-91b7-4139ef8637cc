@charset "UTF-8";
/* CSS Document */

#info {
	position: absolute;
	width: 80%;
	top: 40px;
	left: 20%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0 15px;
	font-size: 12px;
	color: #369;
	z-index: 3;
}
#info table {
	width: 100%;
	border-collapse: collapse;
	border: 10px solid #369;
	border-radius: 10px;
	opacity: 0.95;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
#info th,
#info td {
	background-color: #799CBF;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	border-right: 1px solid #369;
	padding: 3px 0;
	color: #FFF;
}
#info td {
	background-color: #FFF;
	color: #369;
	padding: 5px;
}
#info .date,
#info .code {
	width: 10%;
	padding: 5px 0;
	text-align: center;
}
#info .name,
#info .mourner,
#info .staff {
	width:12%;
}
#info .member {
	width: 14%;	
}
#info .address {
	width: 30%;	
}
.plan_detail  {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 100;
	display: none;
}
.plan_detail .shade {
	position: absolute;
	width: 100%;
	left: 100%;
	top: 0;
	left: 0;
}
.plan_detail .body {
	position: absolute;
	top: 100px;
	left: 50%;
	width: 600px;
	height: 450px;
	margin-left: -300px;
	background-color: #296FA7;
	background-color: rgba(41,111,167,0.9);
	box-shadow: 0 2px 10px rgba(0,0,0,0.7);
	border-radius: 12px;
}
.plan_detail .body .close {
	height: 30px;
	border-radius: 12px 12px 0 0;
	background-image: url(../../img-sss/ico_close_on.png);
	background-repeat: no-repeat;
	background-position: 98% center;
	background-size: 13px 13px;	
	text-indent: -9999px;
}
.plan_detail .body .item_next,
.plan_detail .body .item_prev {
	position: absolute;
	width: 25px;
	height: 120px;
	top: 330px;
	left: 0;
	background-image: url(../../img-sss/arrow_3_l.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 10px 20px;	
	text-indent: -9999px;
	cursor: pointer;
}
.plan_detail .body .item_next {
	left: auto;
	right: 0;
	background-image: url(../../img-sss/arrow_3_r.png);
}
.plan_detail .body .item_list {
	width: 550px;
	height: 100px;
	margin: 10px 25px;
	overflow: hidden;
}
.plan_detail .body .item_list ul {
	display: block;
	position: absolute;
	width: 540px;
	height: 100px;
	top: 0;
	left: 0;
}
.plan_detail .body .item_list ul li {
	display: block;
	width: 125px;
	height: 100px;
	margin-left: 10px;
	cursor: pointer;
	float: left;
}
.plan_detail .body .item_list ul li img {
	width: 125px;
	height: 100px;
}
#item_pop {
	position: fixed;
	width: 500px;
	height: 400px;
	top: 30px;
	left: 50%;
	margin-left: -250px;
	background-color: #000;
	box-shadow: 0 0 50px rgba(0,0,0,0.8);
	z-index: 101;
	display: none;
}
#order {
	position: fixed;
	width: 100%;
	height: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 10px 25px;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	top: 40px;
	left: 0;
	background-color: #D4E5F4;
	box-shadow: -2px 0 2px rgba(0,0,0,0.2);
	line-height: 1;
	z-index: 2;
}
#order select {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
	width: 100px;
	height: 32px;
	text-indent: 5px;
	text-overflow: "";
	line-height: 1.5;
	margin-top: 0;
	border: none;
	border-radius: 0;
	font-size: inherit;
	background-image: url(../../img-sss/arrow.png);
	background-repeat: no-repeat;
	background-position: 93% center;
	background-size: 9px 8px;
}
#order input.txt {
	height: 32px;
	padding: 8px;
	line-height: 1;
	border: none;
	font-size: inherit;
}
#order h2 {
	position: absolute;
	top: 20px;
	left: 25px;
	font-size: 15px;
	color: #266CA5;
}
#order .label,
#order label,
#order .subtitle {
	display: block;
	width: 12%;
	height: 32px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	border-right: 3px solid #A1D7F4;
	border-bottom: 1px solid #CCC;
	background-color: #E9F3FB;
	padding: 10px 1%;
	font-size: 13px;
	font-weight: bold;
	color: #296FA7;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	float: left;
}
#order .label.require,
#order label.require,
#order .subtitle.require {
	border-right-color: #F60;	
}
#order .label.option.done,
#order label.option.done,
#order .subtitle.option.done,
#order .label.require.done,
#order label.require.done,
#order .subtitle.require.done {
	border-right-color: #6C0;	
}
#order .dlg_note,
#order .dlg_date,
#order .dlg_deliv,
#order .dlg_order_no,
#order .dlg_bill,
#order .dlg_staff,
#order .dlg_item,
#order .dlg_summary {
	width: 3%;
	border-right: 1px solid #CCC;
	background-image: url(../../img-sss/ico_dialog.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 12px 10px;
}
#order input.txt,
#order textarea.txt,
#order select {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	border: none;
	border-right: 1px solid #CCC;
	border-bottom: 1px solid #CCC;
	box-shadow: none;
	background-color: #FFF;
	float: left;
}
#order .buttons {
	width: 100%;
	left: 0;	
}
#order fieldset {
	clear: both;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative;
	display: block;
	width: 100%;
	overflow: hidden;
}
#order fieldset.type_1,
#order fieldset.base_2 {
	margin-bottom: 10px;	
}
#order .header table {
	width: 100%;
	border-bottom: 1px solid #88B1D1;
	border-collapse: collapse;
	background: linear-gradient(to bottom, #E8F3FB 0%, #E8F3FB 49%, #D5EBF9 50%, #D5EBF9 100%);
	background: -moz-linear-gradient(top, #E8F3FB 0%, #E8F3FB 49%, #D5EBF9 50%, #D5EBF9 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #E8F3FB), color-stop(49%, #E8F3FB), color-stop(50%, #D5EBF9), color-stop(100%, #D5EBF9));
	background: -webkit-linear-gradient(top, #E8F3FB 0%, #E8F3FB 49%, #D5EBF9 50%, #D5EBF9 100%);
	background: -o-linear-gradient(top, #E8F3FB 0%, #E8F3FB 49%, #D5EBF9 50%, #D5EBF9 100%);
	background: -ms-linear-gradient(top, #E8F3FB 0%, #E8F3FB 49%, #D5EBF9 50%, #D5EBF9 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFE8F3FB', endColorstr='#FFD5EBF9',GradientType=0 );
}
#order .header table td {
	width: 5%;
	height: 30px;
	background-color: transparent;
	text-align: center;
	color: #286EA6;
	font-weight: bold;
	border-right: 1px solid #88B1D1;
}
#order .header .type {
	width: 7%;
}
#order .header .item_id {
	width: 13%;
}
#order .header .item {
	width: 25%;
}
#order .header .price,
#order .header .cost {
	width: 10%;
}
#order .header .memo {
	width: 20%;
	border-right: none;
}
#order .items {
	height: 58%;
	overflow: auto;
	border: 1px solid #88B1D1;
	background-color: #EBF3FA;
	-webkit-overflow-scrolling: touch;
}
#order .items #add {
	width: 100%;
	height: 60px;
	background-image: url(../../img-sss/plus_1.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 26px 26px;
	text-indent: -999px;
}
#order .items #add:active {
	background-image: url(../../img-sss/plus_1_on.png);
}
#order .list fieldset {
	height: 61px;
	border-bottom: 1px solid #88B1D1;
	background: linear-gradient(to bottom, #FFF 0%, #FFF 49%, #F5F9FD 50%, #F5F9FD 100%);
	background: -moz-linear-gradient(top, #FFF 0%, #FFF 49%, #F5F9FD 50%, #F5F9FD 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #FFF), color-stop(49%, #FFF), color-stop(50%, #F5F9FD), color-stop(100%, #F5F9FD));
	background: -webkit-linear-gradient(top, #FFF 0%, #FFF 49%, #F5F9FD 50%, #F5F9FD 100%);
	background: -o-linear-gradient(top, #FFF 0%, #FFF 49%, #F5F9FD 50%, #F5F9FD 100%);
	background: -ms-linear-gradient(top, #FFF 0%, #FFF 49%, #F5F9FD 50%, #F5F9FD 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFFFFFFF', endColorstr='#FFF5F9FD',GradientType=0 );
}
#order input {
	width: 10%;
}
#order label,
#order select {
	width: 8%;
}
#order .label {
	background-color: #FFF;	
}
#order .dlg_staff {
	width: 3%;
}
#order .lbl_collect_type {
	margin-left: 84%;	
}
#order .lbl_order_no,
#order .lbl_order_date,
#order .lbl_bill_id,
#order .lbl_deliv_id,
#order .lbl_tax,
#order .lbl_staff {
	width: 7%;	
}
#order .lbl_deliv_date,
#order .lbl_collect_date {
	width: 9%;
}
#order .note_1 .lbl_note,
#order .note_2 .lbl_note {
	width: 7%;	
	border-bottom: 1px solid #88B1D1;
	border-left: 1px solid #88B1D1;
}
#order .note_1 input,
#order .note_2 input,
#order .total_title,
#order .total_price,
#order .dlg_note {
	border-bottom: 1px solid #88B1D1;
	border-right: 1px solid #88B1D1;	
}
#order .total_title {
	border-left: 1px solid #88B1D1;
}
#order .total_title {
	width: 10%;
	text-align: center;
	padding: 10px 0;
	background-color: #E8F3FB;
}
#order .note_3 .total_title {
	margin-left: 50%;	
}
#order .total_price {
	width: 10%;
	text-align: right;
	font-weight: normal;
	font-size: inherit;
	color: inherit;
}
#order .staff {
	width: 13%;	
}
#order #note_id_1,
#order #note_id_2,
#order #order_date,
#order #deliv_date,
#order #collect_date,
#order #order_no,
#order #bill_id,
#order #deliv_id,
#order #staff_1 {
	border-right: none;	
}
#order #bill_name,
#order #deliv_name {
	width: 15%;	
}
#order #note_txt_1,
#order #note_txt_2 {
	width: 30%;	
	border-right: none;
}
#order .buttons {
	position: fixed;
	width: 80%;
	height: 40px;
	left: 20%;
	bottom: 0;
	text-align: center;
	border-top: 1px solid #FFF;
	background: linear-gradient(to bottom, #E5EFF7 0%, #CADDED 100%);
	background: -moz-linear-gradient(top, #E5EFF7 0%, #CADDED 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #E5EFF7), color-stop(100%, #CADDED));
	background: -webkit-linear-gradient(top, #E5EFF7 0%, #CADDED 100%);
	background: -o-linear-gradient(top, #E5EFF7 0%, #CADDED 100%);
	background: -ms-linear-gradient(top, #E5EFF7 0%, #CADDED 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFE5EFF7', endColorstr='#FFCADDED',GradientType=0 );
	z-index: 5;
}
#order .buttons input {
	width: 120px;
	height: 32px;
	font-weight: bold;
	padding-top: 2px;
	color: #296FA7;
	letter-spacing: 0.1em;
	text-indent: 15px;
	background: none;
	border: 1px solid #94B9D6;
	border-radius: 3px;
	box-shadow: 1px 1px 0 #FFF;
	margin: 3px 5px 0 5px;
}
#order .buttons input:active {
	background-color: #369;
	border: 1px solid #FFF;
	box-shadow: none;
	box-shadow: inset 1px 1px 1px rgba(0,0,0,0.2);
	color: #FFF;
}
#order #btn_save {
	background-image: url(../../img-sss/ico_save.png);
	background-repeat: no-repeat;
	background-position: 31px 8px;
	background-size: 14px 13px;	
}
#order #btn_save:active {
	background-image: url(../../img-sss/ico_save_on.png);
}
#order #btn_print {
	background-image: url(../../img-sss/ico_print.png);
	background-repeat: no-repeat;
	background-position: 26px 8px;
	background-size: 18px 15px;
}
#order #btn_print:active {
	background-image: url(../../img-sss/ico_print_on.png);
}
#order #btn_delete {
	background-image: url(../../img-sss/ico_trash.png);
	background-repeat: no-repeat;
	background-position: 34px 8px;
	background-size: 11px 15px;
}
#order #btn_delete:active {
	background-image: url(../../img-sss/ico_trash_on.png);
}
#order #btn_cancel {
	background-image: url(../../img-sss/ico_close.png);
	background-repeat: no-repeat;
	background-position: 33px 9px;
	background-size: 13px 13px;	
}
#order #btn_cancel:active {
	background-image: url(../../img-sss/ico_close_on.png);
}
#order .list fieldset input,
#order .list fieldset div,
#order .list fieldset select,
#order .list fieldset textarea {
	position: absolute;
	width: 10%;
	height: 60px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	top: 0;
	left: 0;
	border-right: 1px solid #88B1D1;
	border-bottom: none;
	color: inherit;
	font-size: inherit;
	font-weight: normal;
}
#order .list .row {
	width: 5%;
	text-align: center;
}
#order .list .type {
	width: 7%;
	left: 5%;
}
#order .list .item_id {
	left: 12%;
	border-right: none;
}
#order .list .dlg_item {
	width: 3%;
	left: 22%;
}
#order .list .item {
	width: 25%;
	left: 25%;
}
#order .list .item input,
#order .list .item div {
	position: absolute;
	top: 0;
	left: 0;
	height: 30px;
	border-right: none;	
}
#order .list .item .item_name {
	width: 100%;
	border-bottom: 1px solid #88B1D1;
}
#order .list .item .summary_id {
	width: 40%;
	top: 50%;
}
#order .list .item .dlg_summary {
	position: absolute;
	width: 10%;
	top: 50%;
	left: 40%;
	border-right: 1px solid #88B1D1;
}
#order .list .item .summary {
	width: 50%;
	top: 50%;
	left: 50%;
}
#order .list .quantity,
#order .list .unit {
	width: 5%;
	left: 50%;
}
#order .list .unit {
	left: 55%;	
}
#order .list .price {
	left: 60%;
}
#order .list .cost {
	left: 70%;
}
#order .list .price input,
#order .list .cost input,
#order .list .price div,
#order .list .cost div {
	width: 100%;
	height: 30px;
	text-align: right;
	border-right: none;
}
#order .list .price div,
#order .list .cost div {
	top: 50%;
	padding-right: 9px;
	border-top: 1px solid #88B1D1;
}
#order .list .memo {
	width: 20%;
	left: 80%;
	padding-left: 10px;
	line-height: 30px;
	border-right: none;
}
.dialog {
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 100;
	display: none;
}
.dialog .close {
	position: absolute;
	width: 22px;
	height: 22px;
	padding: 5px;
	top: 0;
	right: 2px;
	text-align: center;
	font-size: 22px;
	font-weight: normal;
	color: #FFF;
	line-height: 1;
	cursor: pointer;
	z-index: 1;
}
#celemony_dialog .close {
	top: -3px;
	right: 8px;
}
.shade {
	position: relative;
	width: 100%;
	height: 100%;
	background-color: rgba(0,0,0,0.5);
}
.dialog .dialog_body {
	position: fixed;
	width: 600px;
	height: auto;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	top: 130px;
	left: 50%;
	margin-left: -300px;
	padding: 20px 10px 10px 10px;
	border-radius: 9px;
	background-color: rgba(41,111,167,0.85);
	box-shadow: 0 2px 10px rgba(0,0,0,0.7);
}
#hall_dialog .dialog_body {
	padding-top: 30px;
}
#zip_dialog .dialog_body {
	width: 700px;
	margin-left: -350px;
	padding-top: 10px;
}
#press_dialog .dialog_body {
	width: 400px;
	margin-left: -200px;
	padding-top: 30px;
}
.dialog .dialog_body .note {
	width: 80%;
	margin: 0 auto 10px auto;
	text-align: center;
	font-size: 12px;
	font-weight: bold;
	color: #FFF;	
}
.dialog .dialog_body .head,
.dialog .dialog_body .list {
	width: 98%;
	margin: 0 auto;	
}
.dialog .dialog_body .list {
	height: 350px;
	overflow: auto;
	background-color: #DFEDFA;
	-webkit-overflow-scrolling: touch;
}
#press_dialog .dialog_body .list {
	height: 200px;	
}
.dialog .dialog_body table {
	width: 100%;
	margin: 0;
    table-layout: fixed;
	border-collapse: collapse;
	box-shadow: 0 2px 2px rgba(0,0,0,0.2);
	font-size: 13px;
	font-weight: bold;
	color: #369;
}
.dialog .dialog_body table .option.on {
	background-color: #F90;
	color: #FFF;	
}
.dialog .dialog_body table .option.on .code,
.dialog .dialog_body table .option.on .zip {
	background-image: url(../../img-sss/check_1.png);
	background-repeat: no-repeat;
	background-position: 4% center;
	background-size: 16px 12px;
}
.dialog .dialog_body table th,
.dialog .dialog_body table td {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 4px 1%;
	border-right: 1px solid #5692B7;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.dialog .dialog_body table th {
	color: #FFF;
}
.dialog .dialog_body .head tr {
	background-color: #709EC4;
}
.dialog .dialog_body table th:last-child,
.dialog .dialog_body table td:last-child {
	border-right: none;	
}
.dialog .dialog_body .list tr {
	background-color: #F5F8FB;
	border-bottom: 1px solid #5692B7;
}
.dialog .dialog_body table .code,
.dialog .dialog_body table .zip {
	width: 16%;
	text-align: center;
}
.dialog .dialog_body table .name,
.dialog .dialog_body table .kana,
.dialog .dialog_body table .address {
	width: 44%;
}
#press_dialog .dialog_body table .name,
#press_dialog .dialog_body table .price {
	width: 50%;	
}
#press_dialog .dialog_body table td.price {
	text-align: right;
}
.dialog .select {
	width: 158px;
	height: 30px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	margin: 10px auto 0 auto;
	background-color: #99BAD5;
	border: 1px solid transparent;
	border-radius: 3px;
	color: #FFF;
	font-size: 16px;
	font-weight: bold;
	line-height: 30px;
	text-align: center;
}
.dialog .select:active {
	background-color: rgba(0,50,100,0.2);
	border: 1px solid #FFF;
}
.dialog .search fieldset {
	width: 100%;
	margin-bottom: 10px;
}
.dialog .search fieldset.f_button {
	text-align: center;	
}
.dialog .search label {
	display: inline-block;
	width: 10%;
	text-align: right;
	font-size: 12px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #FFF;
	font-weight: bold;
}
#zip_dialog .search label {
	width: 8%;
}
.dialog .search input.txt {
	display: inline-block;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 35%;
	height: 28px;
	padding: 2px 5px;
	margin-right: 0.5%;
	border: none;
	border-radius: 3px;
	box-shadow: inset 1px 1px 3px rgba(0,0,0,0.3);
	font-weight: normal;
}
#zip_dialog .search input#s_zip {
	width: 13%;	
}
#zip_dialog .search input#s_address,
#zip_dialog .search input#s_kana {
	width: 27%;
}
.dialog .search #btn_save,
.dialog .search #btn_search {
	display: inline-block;
	width: 158px;
	height: 30px;
	border: 1px solid #97B9D5;
	border-radius: 5px;
	line-height: 1.2;
	letter-spacing: 0.5em;
	text-indent: 15px;
	margin: 0 15px;
	background-color: transparent;
	background-image: url(../../img-sss/ico_search_2.png);
	background-repeat: no-repeat;
	background-position: 30% center;
	background-size: 14px 13px;
	color: #FFF;
	font-weight: bold;
	cursor: pointer;
}
.dialog .search #btn_save {
	background-image: url(../../img-sss/ico_save_on.png);
}
.dialog .search #btn_save:active,
.dialog .search #btn_search:active {
	background-color: rgba(0,50,100,0.2);
	border: 1px solid #FFF;
}
#time_dialog .shade {
	background-color: rgba(0,0,0,0);	
}
#time_dialog .dialog_body {
	width: 35%;
	overflow: hidden;
	padding: 10px;
	margin-left: -190px;
	left: 0;
	margin-left: 0;
	background-color: #F2F5F7;
	background-image: url(images/ui-bg_highlight-hard_100_f2f5f7_1x100.png);
	background-position: 50% top;
	background-repeat: repeat-x;
	box-shadow: 0 1px 5px rgba(0,0,0,0.5);
}
#time_dialog .hour,
#time_dialog .minute {
	display: block;
	width: 500px;
	width: 100%;
	margin: 0 auto;
	overflow: hidden;	
}
#time_dialog .minute {
	margin: 5px 0;
}
#time_dialog .hour li,
#time_dialog .minute li {
	display: block;
	width: 6.9%;
	height: 28px;
	margin: 2px 0.3%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	border-radius: 1px;
	border: 1px solid #AED0EA;
	background-color: #D7EBF9;
	background-image: url(images/ui-bg_glass_80_d7ebf9_1x400.png);
	background-position: 50% 50%;
	background-repeat: repeat-x;
	color: #39C;
	font-size: 16px;
	font-weight: bold;
	line-height: 28px;
	text-align: center;
	float: left;
}
#time_dialog .hour li:hover,
#time_dialog .minute li:hover {
	border: 1px solid #74B2E2;
	background: #E4F1FB url(images/ui-bg_glass_100_e4f1fb_1x400.png) 50% 50% repeat-x;
	font-weight: bold;
	color: #0070A3;
}
#time_dialog .hour li.label,
#time_dialog .minute li.label {
	width: 9%;
	margin: 2px 0.5%;
	background-image: none;
	background-color: transparent;
	border: 1px solid #DDD;
	border: none;
	font-size: 13px;
	color: #444;
}
#time_dialog .hour li.on,
#time_dialog .minute li.on {
	border: 1px solid #3399CC;
	background-color: #3BAAE3;
	background-image: url(images/ui-bg_glass_50_3baae3_1x400.png);
	background-position: 50% 50%;
	background-repeat: repeat-x;
	color: #FFF;
}
#time_dialog .select {
	width: 29%;
	border: 1px solid #AED0EA;
	background-color: #D7EBF9;
	background-image: url(images/ui-bg_glass_80_d7ebf9_1x400.png);
	background-position: 50% 50%;
	background-repeat: repeat-x;
	color: #39C;
	font-size: 14px;
}
#time_dialog .select:hover {
	border: 1px solid #74B2E2;
	background-image: url(images/ui-bg_glass_100_e4f1fb_1x400.png);
	background-position: 50% 50%;
	background-repeat: repeat-x;
	color: #0070A3;
}
#time_dialog .select:active {
	border: 1px solid #3399CC;
	background-color: #3BAAE3;
	background-image: url(images/ui-bg_glass_50_3baae3_1x400.png);
	background-position: 50% 50%;
	background-repeat: repeat-x;
	color: #FFF;
}
#celemony_dialog .dialog_body {
	position: fixed;
	width: 94%;
	height: 94%;
	box-sizing: border-box;
	top: 3%;
	left: 3%;
	margin: 0;
	padding: 150px 0 0 0;
	background: none;
	box-shadow: none;
}
#celemony_dialog .search,
#celemony_dialog .result {
	width: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 10px;
	border-radius: 5px;
	background-color: rgba(62,192,216,0.85);
	box-shadow: 0 2px 10px rgba(0,0,0,0.7);
}
#celemony_dialog .search {
	position: absolute;
	top: 0;
	left: 0;
	color: #FFF;
	font-weight: bold;	
}
#celemony_dialog .search fieldset {
	width: 100%;
	margin-top: 10px;	
}
#celemony_dialog .search label {
	display: inline-block;
	width: 5.5%;
	text-align: right;
	font-size: 12px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
#celemony_dialog .search .lbl_s_ontime {
	width: 7%;
	padding: 4px 0 4px 10px;
	box-sizing: border-box;
	background: transparent;
	border: none;
	border-radius: 3px;
	text-align: center;
	color: #FFF;
}
#celemony_dialog .search .lbl_s_ontime  .ui-button-text {
	background: transparent;
}
#celemony_dialog .search .lbl_s_ontime:after {
	content: '';
	position: absolute;
	display: block;	
	width: 14px;
	height: 10px;
	top: 6px;
	left: 3px;
	border-left: 3px solid rgba(255,255,255,0.6);
	border-bottom: 3px solid rgba(255,255,255,0.6);
	transform: rotate(315deg);
}
#celemony_dialog .search .lbl_s_ontime.ui-state-active {
	background-color: #0CF;
	color: #FFF;
}
#celemony_dialog .search .lbl_s_ontime.ui-state-active:after {
	border-color: #FFF;	
}
#celemony_dialog .search input.txt {
	display: inline-block;
	box-sizing: border-box;
	width: 11.5%;
	height: 28px;
	padding: 2px 5px;
	margin-right: 0.5%;
	border: none;
	border-radius: 3px;
	box-shadow: inset 1px 1px 1px rgba(0,0,0,0.1);
	font-weight: normal;
}
#celemony_dialog .search select {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
	text-overflow: "";
	width: 7%;
	height: 28px;
	text-indent: 3px;
	text-align: left;
	font-size: inherit;
	line-height: 1;
	margin-top: 0;
	border: none;
	border-radius: 3px;
	background-image: url(../../img-sss/arrow.png);
	background-repeat: no-repeat;
	background-position: 90% center;
	background-size: 9px 8px;
}
#celemony_dialog .search select#s_month {
	width: 4%;
}

#celemony_dialog .search input.check {
	margin-right: 6%;	
}
#celemony_dialog .search input#s_code_1,
#celemony_dialog .search input#s_code_2,
#celemony_dialog .search input#s_code_3 {
	width: 3.5%;
}
#celemony_dialog .search #btn_search {
	display: block;
	width: 160px;
	height: 30px;
	border: 1px solid #FFF;
	border-radius: 5px;
	line-height: 1.2;
	letter-spacing: 0.5em;
	text-indent: 15px;
	margin: 15px auto 0 auto;
	background-color: transparent;
	background-image: url(../../img-sss/head_ico_search.png);
	background-repeat: no-repeat;
	background-position: 30% center;
	background-size: 15px 15px;
	color: #FFF;
	font-weight: bold;
	cursor: pointer;
}
#celemony_dialog .search #btn_search:active {
	background-color: rgba(0,50,100,0.2);
	border: 1px solid #FFF;
}
#celemony_dialog #open_search {
	position: absolute;
	display: block;
	width: 40px;
	height: 40px;
	top: 0;
	right: 0;
	border-radius: 50%;
	background-color: #296FA7;
	background-color: rgba(62,192,216,0.85);
	background-image: url(../../img-sss/head_ico_search.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 30px 30px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.5);
	cursor: pointer;
	display: none;
}
#celemony_dialog #open_search:active {
	background-color: rgba(0,50,100,0.2);
	box-shadow: none;
}
#celemony_dialog .result {
	position: relative;
	width: 100%;
	height: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding-top: 30px;
	padding-bottom: 20px;
}
#celemony_dialog .result table {
	width: 100%;
	margin: 0;
    table-layout: fixed;
	border-collapse: collapse;
}
#celemony_dialog .result .head {
	position: absolute;
	width: 100%;
	top: 30px;
	left: 0;
	padding: 0 10px;
	z-index: 1;
}
#celemony_dialog .result .head th {
	width: 10%;
	height: 14px;
	box-sizing: border-box;
	padding: 5px 0;
	background-color: #2C91A3;
	border-right: 1px solid #dbe1f7;
	font-size: 12px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-weight: normal;
	color: #FFF;	
}
#celemony_dialog .result .list {
	position: relative;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	margin: 0;
	padding: 0;
	overflow: auto;
	background-color: #FBFEFE;
	-webkit-overflow-scrolling: touch;	
}
#celemony_dialog .result .list table {
	width: 100%;
	margin: 0;
	margin-top: 28px;
	box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}
#celemony_dialog .result .list tr {
	border-bottom: 1px solid #EEE;	
}
#celemony_dialog .result .list td {
	width: 10%;
	box-sizing: border-box;
	border-right: 1px solid #DDD;
	background-color: #FFF;
	padding: 5px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 12px;
	color: #369;
}
#celemony_dialog .result .head th:last-child,
#celemony_dialog .result .list td:last-child {
	border-right: none;	
}
#celemony_dialog .result .list .even td {
	background-color: #FBFEFE;
}
#celemony_dialog .result .head .address,
#celemony_dialog .result .list .address {
	width: 20%;
}
#celemony_dialog .result .list .estimate,
#celemony_dialog .result .list .bill {
	text-align: right;	
}
#celemony_dialog .result .head .member,
#celemony_dialog .result .list .member,
#celemony_dialog .result .head .hall,
#celemony_dialog .result .list .hall,
#celemony_dialog .result .head .staff,
#celemony_dialog .result .list .staff,
#celemony_dialog .result .head .name,
#celemony_dialog .result .list .name {
	width: 12.5%;	
}
#celemony_dialog .result .head .estimate,
#celemony_dialog .result .list .estimate,
#celemony_dialog .result .head .bill,
#celemony_dialog .result .list .bill,
#celemony_dialog .result .head .type,
#celemony_dialog .result .list .type,
#celemony_dialog .result .head .status,
#celemony_dialog .result .list .status {
	width: 7.5%;	
}
#celemony_dialog .result .list .name.chief .tel,
#celemony_dialog .result .list .name.chief .address {
	display: none;
}
#celemony_dialog .result .list .name.chief {
	cursor: pointer;
}
#celemony_dialog .result .list .name.chief:after {
	content: 'i';
	position: absolute;
	display: block;
	width: 16px;
	height: 16px;
	top: 6px;
	right: 3px;
	border-radius: 50%;
	background-color: #9EDFEB;
	text-align: center;
	line-height: 16px;
	font-size: 12px;
	font-family: serif;
	color: #FFF;	
	z-index: 1;
}
#celemony_dialog .result .head .detail {
	width: 5%;
	background: linear-gradient(to bottom, #83ACCD 0%, #83ACCD 49%, #709EC4 50%, #709EC4 100%);
	background: -moz-linear-gradient(top, #83ACCD 0%, #83ACCD 49%, #709EC4 50%, #709EC4 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #83ACCD), color-stop(49%, #83ACCD), color-stop(50%, #709EC4), color-stop(100%, #709EC4));
	background: -webkit-linear-gradient(top, #83ACCD 0%, #83ACCD 49%, #709EC4 50%, #709EC4 100%);
	background: -o-linear-gradient(top, #83ACCD 0%, #83ACCD 49%, #709EC4 50%, #709EC4 100%);
	background: -ms-linear-gradient(top, #83ACCD 0%, #83ACCD 49%, #709EC4 50%, #709EC4 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FF83ACCD', endColorstr='#FF709EC4',GradientType=0 );
}
#celemony_dialog .result .list .detail {
	width: 5%;
	background: linear-gradient(to bottom, #F5F8FB 0%, #F5F8FB 49%, #D6E7F6 50%, #D6E7F6 100%);
	background: -moz-linear-gradient(top, #F5F8FB 0%, #F5F8FB 49%, #D6E7F6 50%, #D6E7F6 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #F5F8FB), color-stop(49%, #F5F8FB), color-stop(50%, #D6E7F6), color-stop(100%, #D6E7F6));
	background: -webkit-linear-gradient(top, #F5F8FB 0%, #F5F8FB 49%, #D6E7F6 50%, #D6E7F6 100%);
	background: -o-linear-gradient(top, #F5F8FB 0%, #F5F8FB 49%, #D6E7F6 50%, #D6E7F6 100%);
	background: -ms-linear-gradient(top, #F5F8FB 0%, #F5F8FB 49%, #D6E7F6 50%, #D6E7F6 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFF5F8FB', endColorstr='#FFD6E7F6',GradientType=0 );
}
#celemony_dialog .result .list .detail a {
	display: block;
	height: 46px;
	background-image: url(../../img-sss/arrow_4_r.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 14px 26px;
	text-decoration: none;
}
#celemony_dialog .result .list .code,
#celemony_dialog .result .list .tel,
#celemony_dialog .result .list .date,
#celemony_dialog .result .list .date_of_death,
#celemony_dialog .result .list .type,
#celemony_dialog .result .list .status {
	text-align: center;	
}
#celemony_dialog .popup {
	position: absolute;
	width: 300px;
	height: auto;
	box-sizing: border-box;
	padding: 10px;
	margin-top: -10px;
	border-radius: 5px;
	background-color: rgba(255,255,255,0.98);
	box-shadow: 1px 0 4px rgba(0,0,0,0.2);
	font-size: 14px;
	color: #369;
	z-index: 110;
	display: none;
}
#celemony_dialog .popup > span {
	display: block;
}
#celemony_dialog .popup .close {
	position: absolute;
	width: 20px;
	height: 20px;
	top: 5px;
	right: 5px;
	cursor: pointer;
}
#celemony_dialog .popup .close:before,
#celemony_dialog .popup .close:after {
	content: '';
	position: absolute;
	display: block;
	width: 16px;
	height: 1px;
	top: 10px;
	left: 4px;
	transform-origin: center center;
	background-color: #369;	
	z-index: 1;
}
#celemony_dialog .popup .close:before {
	transform: rotate(45deg);	
}
#celemony_dialog .popup .close:after {
	transform: rotate(135deg);	
}
#celemony_dialog .popup:before,
#celemony_dialog .popup:after {
	content: '';
	position: absolute;
	display: block;
	box-sizing: content-box;
	width: 0;
	height: 0;
	top: 100%;
	left: 50%;
	margin-left: -10px;
	border: 10px solid transparent;
}
#celemony_dialog .popup:before {
	margin-top: 1px;
	margin-left: -11px;
	border: 11px solid transparent;
	border-top: 10px solid #DDD;
	z-index: 1;
}
#celemony_dialog .popup:after {
	border-top: 10px solid #FFF;
	z-index: 2;
}
#calendar_dialog {
	position: absolute;
	width: 300px;
	height: auto;
	top: 52px;
	right: 10px;
	padding: 10px;
	background-color: rgba(0,20,25,0.85);
	border-radius: 5px;
	box-shadow: 0 1px 5px rgba(0,0,0,0.5);
	color: #FFF;
	z-index: 120;
	display: none;
}
#calendar_dialog:after {
	content: '';
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	box-sizing: content-box;
	bottom: 100%;
	right: 115px;
	margin: -10px -10px 0 0;
	border: 10px solid transparent;
	border-bottom: 10px solid rgba(0,20,25,0.85);
	z-index: 1;
}
#calendar_dialog .title {
	height: 30px;
	text-align: center;
	font-size: 12px;
	line-height: 30px;
}
#calendar_dialog .title .num {
	font-size: 18px;
	padding: 0 2px;
}
#calendar_dialog #cal_next,
#calendar_dialog #cal_prev {
	position: absolute;
	display: block;
	width: 20px;
	height: 40px;
	top: 5px;
	left: 5px;
	overflow: hidden;
	white-space: nowrap;
	text-indent: 100%;
	z-index: 1;
}
#calendar_dialog #cal_next {
	left: auto;
	right: 5px;
}
#calendar_dialog #cal_next:after,
#calendar_dialog #cal_prev:after {
	content: '';
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	box-sizing: content-box;
	top: 50%;
	margin-top: -8px;
	border: 8px solid transparent;
	z-index: 1;
}
#calendar_dialog #cal_next:after {
	left: 0;
	border-left: 14px solid #FFF;	
}
#calendar_dialog #cal_prev:after {
	right: 0;
	border-right: 14px solid #FFF;	
}
#calendar_dialog .week {
	clear: both;	
}
#calendar_dialog .week li {
	display: block;
	width: 28px;
	height: 28px;
	margin: 6px;
	border: 1px solid transparent;
	border-radius: 50%;
	text-align: center;
	font-size: 16px;
	line-height: 28px;
	color: #FFF;
	float: left;
}
#calendar_dialog .week li.prev,
#calendar_dialog .week li.next,
#calendar_dialog .week li.prev.sat,
#calendar_dialog .week li.next.sat,
#calendar_dialog .week li.prev.sun,
#calendar_dialog .week li.next.sun {
	color: #578;
}
#calendar_dialog .week li.sat {
	color: #9EF;
}
#calendar_dialog .week li.sun {
	color: #F9A;	
}
#calendar_dialog .week li.tomo {
	border: 1px solid rgba(255,255,255,0.8);
}
#calendar_dialog .week li.tai {
	border: 1px solid rgba(255,122,122,0.8);
}
#calendar_dialog .week li.today {
	background-color: #dbe1f7;
	border: 1px solid transparent;
	color: #FFF !important;
}

.ui-datepicker {
	width: 35%;
	box-shadow: 0 1px 5px rgba(0,0,0,0.5);
}
.ui-datepicker td {
	font-size: 16px;
}