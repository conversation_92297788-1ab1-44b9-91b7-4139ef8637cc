<?php
  /**
   * Logic_JuchuSeikaHenrei
   *
   * 生花返礼品受注 処理
   *
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Kayo
   * @since      2017/10/27
   * @version    2019/06/07 tosaka 軽減税率対応 keigen
   * @filesource 
   */

  /**
   * 生花返礼品受注 処理
   * 
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Kayo
   * @since      2017/10/27
   */
class Logic_JuchuSeikaHenrei
{
    /**
     * 生花返礼品受注 の更新・登録
     *
     * <AUTHOR> Kayo
     * @since      2017/10/27
     * @param      array  $data データ. 明細は $data['_dtl_']に含まれる
     * @return     array        登録データ
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function upsert($data)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $seko_no = Msi_Sys_Utils::emptyToNull($data['seko_no']);
        if ( $seko_no === null ) {
            throw new Msi_Sys_Exception_InputException( "施行番号が指定されていません" );
        }
        $cnt		= 0;
		$uri_den_no = null;
		$denpyo_no	= null;
		$data_kbn	= 1;
        $count      = 0;    // 生花返礼品表示順
        // 受注伝票明細を更新
        foreach ( $data['_dtl_'] as &$dtl ) {
            $dtl['seko_no'] = $seko_no;
            ++$count;
            $dtl['seika_henrei_disp_no'] = $count;    // 生花返礼品表示順
            if (strlen($dtl['j_shohin_cd']) > 0) {
                // 別注品で入力されたものは、受注伝票を更新しない
                $denpyoMsi = array();
                if (strlen($dtl['shohin_cd']) < 1) {
                    // 商品コードが削除された場合
                    $denpyoMsi['seika_henrei_shohin_cd']			=	NULL;		// 生花返礼品商品コード
                    $denpyoMsi['seika_henrei_shohin_nm']			=	NULL;		// 生花返礼品商品名
                    $denpyoMsi['seika_henrei_shohin_tkiyo_nm']		=	NULL;       // 生花返礼品商品摘要名
                    $denpyoMsi['seika_henrei_shohin_tkiyo_nm2']		=	NULL;		// 生花返礼品商品摘要名2
                    $denpyoMsi['seika_henrei_juchu_suryo']			=	0;          // 生花返礼品数量
                    $denpyoMsi['seika_henrei_tani_cd']				=	99;			// 生花返礼品単位コード
                    $denpyoMsi['seika_henrei_juchu_tnk']			=	0;			// 生花返礼品単価
                    $denpyoMsi['seika_henrei_juchu_prc']			=	0;			// 生花返礼品受注金額
                    $denpyoMsi['seika_henrei_gojokai_nebiki_prc']	=	0;			// 生花返礼品互助会値引額
                    $denpyoMsi['seika_henrei_nebiki_prc']			=	0;			// 生花返礼品値引額
                    $denpyoMsi['nonyu_yubin_no']                    =	$dtl['nonyu_yubin_no'];	// 納入先郵便番号
                    $denpyoMsi['nonyu_addr1']                       =	$dtl['nonyu_addr1'];	// 納入先住所1
                    $denpyoMsi['nonyu_addr2']                       =	$dtl['nonyu_addr2'];	// 納入先住所2
                    $denpyoMsi['nonyu_tel']                         =	$dtl['nonyu_tel'];		// 納入先電話番号
                    $denpyoMsi['k_free4']							=   NULL;		// 弔電cd
                    $denpyoMsi['v_free4']							=   NULL;		// 弔電nm
                    $denpyoMsi['v_free2']							=   NULL;		// 名札 1
                    $denpyoMsi['v_free3']							=   NULL;		// 名札 2
                    $denpyoMsi['seika_henrei_disp_no']              =   $dtl['seika_henrei_disp_no'];          // 生花返礼品表示順
                } else {
                    $denpyoMsi['seika_henrei_shohin_cd']			=	$dtl['shohin_cd'];			// 生花返礼品商品コード
                    $denpyoMsi['seika_henrei_shohin_nm']			=	$dtl['shohin_nm'];			// 生花返礼品商品名
                    $denpyoMsi['seika_henrei_shohin_tkiyo_nm']		=	$dtl['shohin_tekiyo'];		// 生花返礼品商品摘要名
                    $denpyoMsi['seika_henrei_shohin_tkiyo_nm2']		=	(isset($dtl['shohin_tekiyo2'])) ? $dtl['shohin_tekiyo2'] : NULL; // 生花返礼品商品摘要名2
                    $denpyoMsi['seika_henrei_juchu_suryo']			=	$dtl['juchu_suryo'];		// 生花返礼品数量
                    $denpyoMsi['seika_henrei_tani_cd']				=	99;							// 生花返礼品単位コード
                    $denpyoMsi['seika_henrei_juchu_tnk']			=	$dtl['juchu_tnk'];			// 生花返礼品単価
                    $denpyoMsi['seika_henrei_juchu_prc']			=	$dtl['juchu_suryo'] * $dtl['juchu_tnk'];			// 生花返礼品受注金額
                    $denpyoMsi['seika_henrei_gojokai_nebiki_prc']	=	0;							// 生花返礼品互助会値引額
                    $denpyoMsi['seika_henrei_nebiki_prc']			=	$dtl['nebiki_prc'];			// 生花返礼品値引額
                    $denpyoMsi['nonyu_yubin_no']                    =	$dtl['nonyu_yubin_no'];	// 納入先郵便番号
                    $denpyoMsi['nonyu_addr1']                       =	$dtl['nonyu_addr1'];	// 納入先住所1
                    $denpyoMsi['nonyu_addr2']                       =	$dtl['nonyu_addr2'];	// 納入先住所2
                    $denpyoMsi['nonyu_tel']                         =	$dtl['nonyu_tel'];		// 納入先電話番号
                    $denpyoMsi['k_free4']							=	(isset($dtl['chouden'])) ? $dtl['chouden'] : NULL;// 弔電cd
                    $denpyoMsi['v_free4']							=	(isset($dtl['chouden_sonota'])) ? $dtl['chouden_sonota'] : NULL;// 弔電nm
                    $denpyoMsi['v_free2']							=	(isset($dtl['j_nafuda_nm'])) ? $dtl['j_nafuda_nm'] : NULL;// 名札1
                    $denpyoMsi['v_free3']							=	(isset($dtl['j_msi_biko1'])) ? $dtl['j_msi_biko1'] : NULL;// 名札2
                    $denpyoMsi['seika_henrei_disp_no']              =   $dtl['seika_henrei_disp_no'];          // 生花返礼品表示順
                }
                $where  = array();
                $where['denpyo_no'] = $dtl['denpyo_no'];
                $where['msi_no']    = $dtl['msi_no'];
                $where['delete_flg']= 0;
                list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $denpyoMsi, $where);
                $cnt += $db->easyExecute($sqlUp, $param);
            }    
			$cnt += static::makeUriageDenpyoMsi($db, $dtl);
			if (isset($dtl['denpyo_no_seko']))	{
				$uri_den_no	= $dtl['uri_den_no'];
				$denpyo_no	= $dtl['denpyo_no_seko'];
				$data_kbn	= $dtl['data_kbn'];
			}
        }
        // 行削除された場合の処理
        foreach ( $data['_dtl_org_'] as &$dtl_org ) {
            $find_flg = 0;
            foreach ( $data['_dtl_'] as $dtl2 ) {
                // 生花返礼品売上明細№の存在チェック
                if ($dtl_org['seika_henrei_msi_no'] == $dtl2['seika_henrei_msi_no']) {
                    $find_flg = 1;
                    break;
                }
            }
            // 存在しなくなった場合
            if ($find_flg == 0) {
                // 売上伝票を削除する
                $cnt += static::deleteUriageDenpyoMsi($db, $dtl_org);
                if (isset($dtl_org['denpyo_no_seko']))	{
                    $uri_den_no	= $dtl_org['uri_den_no'];
                    $denpyo_no	= $dtl_org['denpyo_no_seko'];
                    $data_kbn	= $dtl_org['data_kbn'];
                }
            }
        }
		// 売上伝票を再集計する(消費税等)
		if (strlen($uri_den_no) > 0)	{
			$cnt += static::sumUriageDenpyo($db, $uri_den_no);
            // 発注管理情報を作成する
            //$cnt += Logic_JuchuSekoHachuInfoMake::upsertHachuInfo($db, $uri_den_no, '0025');
		}
        // 集計処理
		if (strlen($denpyo_no) > 0)	{
			if ( Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyo_no, $data_kbn) !== true ) {
				throw new Msi_Sys_Exception_LogicException( "集計処理でエラーが発生しました" );
			}
		}	
        // データ確定
        $db->commit();
        $dataNew = DataMapper_SeikaHenrei::findOne( $db, array('seko_no'=>$seko_no,'data_kbn' => 4));

        return $dataNew;
    }
	
    /**
     *
     * 売上伝票明細を作成する
     *
     * <AUTHOR> Kayo
     * @since 2017/10/28
     * @param      Msi_Sys_Db $db
     * @param      array  $dtl 生花返礼品レコード
     * @return int 作成件数
     */
    protected static function makeUriageDenpyoMsi($db, &$dtl) {
		$cnt = 0;
        if (strlen($dtl['j_shohin_cd']) > 0) {
            // 別注品で入力されたもの
            // 受注伝票明細が存在チェック
            $jchudata = $db->easySelOne( <<< END_OF_SQL
SELECT *
FROM juchu_denpyo_msi
WHERE delete_flg = 0
AND	denpyo_no    = :denpyo_no
AND	msi_no       = :msi_no
AND seika_henrei_shohin_cd IS NOT NULL			
END_OF_SQL
            , array('denpyo_no' => $dtl['denpyo_no']
                   ,'msi_no'	=> $dtl['msi_no']));
            if (Msi_Sys_Utils::myCount($jchudata) <= 0)	{
                // 削除されていたら
                if (strlen($dtl['seika_henrei_msi_no']) > 0)	{
                    // 売上伝票を削除する
                    $cnt += static::deleteUriageDenpyoMsi($db, $dtl);
                    // 受注伝票明細の生花返礼品売上明細№をクリアする
                    $cnt += static::updateJuchuDenpyoMsi($db, $dtl, 0);
                    return	$cnt;
                }
            }
        }    
		$upd_flg = 0;	// 新規 
        // 売上伝票明細が存在チェック
        $select = $db->easySelOne( <<< END_OF_SQL
SELECT 
	 u.uri_den_no
	,m.msi_no
	,m.shohin_nm		
FROM uriage_denpyo	u
LEFT JOIN uriage_denpyo_msi m
ON	u.uri_den_no	=	m.uri_den_no
AND 0				=	m.delete_flg			
WHERE u.delete_flg = 0
AND	u.seko_no   = :seko_no
AND u.data_kbn	   IN (1,2)
AND m.msi_no	= :msi_no	
END_OF_SQL
        , array('seko_no' => $dtl['seko_no']
               ,'msi_no'  => $dtl['seika_henrei_msi_no']));
        if (Msi_Sys_Utils::myCount($select) > 0)	{
            $upd_flg = 1;	// 更新 
        }
		// 売上伝票明細を作成する(サブ処理)
		$cnt += static::makeUriageDenpyoMsiSub($db, $dtl, $upd_flg);

        return $cnt;
    }
	
    /**
     *
     * 売上伝票を削除する
     *
     * <AUTHOR> Kayo
     * @since 2017/10/28
     * @param      Msi_Sys_Db $db
     * @param      array  $dtl 生花返礼品レコード
     * @return int 作成件数
     */
    protected static function deleteUriageDenpyoMsi($db, &$dtl) 
	{
		$cnt = 0;
		// 売上伝票明細が存在チェック
		$select = $db->easySelOne( <<< END_OF_SQL
SELECT 
	 u.uri_den_no
    ,u.denpyo_no            
    ,u.data_kbn            
	,m.msi_no
	,m.shohin_nm		
FROM uriage_denpyo	u
LEFT JOIN uriage_denpyo_msi m
ON	u.uri_den_no	=	m.uri_den_no
AND 0				=	m.delete_flg			
WHERE u.delete_flg = 0
AND	u.seko_no   = :seko_no
AND u.data_kbn	   IN (1,2)
AND m.msi_no	= :msi_no	
END_OF_SQL
        , array('seko_no' => $dtl['seko_no']
			   ,'msi_no'  => $dtl['seika_henrei_msi_no']));
		if (count($select) <= 0)	{
			return	0;
		}
		$dtl['uri_den_no']		= $select['uri_den_no'];
		$dtl['denpyo_no_seko']  = $select['denpyo_no'];
		$dtl['data_kbn']		= $select['data_kbn'];
		$denpyoMsi  = array();
		$denpyoMsi['delete_flg']	=	1;
		$where  = array();
		$where['uri_den_no'] = $select['uri_den_no'];
		$where['msi_no']	 = $select['msi_no'];
		list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $denpyoMsi, $where);
 		$cnt += $db->easyExecute($sqlUp, $param);
		return $cnt;
    }
	
    /**
     *
     * 受注伝票明細の生花返礼品売上明細№を更新する
     *
     * <AUTHOR> Kayo
     * @since 2017/10/28
     * @param      Msi_Sys_Db $db
     * @param      array  $dtl 生花返礼品レコード
	 * @param      string $set_flg 0:クリア 1:設定
     * @return int 作成件数
     */
    protected static function updateJuchuDenpyoMsi($db, &$dtl, $set_flg) 
	{
		$cnt = 0;
		$denpyoMsi  = array();
		if ($set_flg == 0)	{
			$denpyoMsi['seika_henrei_msi_no']	=	null;	// 生花返礼品売上明細№
		} else {
			$denpyoMsi['seika_henrei_msi_no']	=	$dtl['seika_henrei_msi_no'];	// 生花返礼品売上明細№
		}	
		$where  = array();
		$where['denpyo_no'] = $dtl['denpyo_no'];
		$where['msi_no']    = $dtl['msi_no'];
		$where['delete_flg']= 0;
		list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $denpyoMsi, $where);
 		$cnt += $db->easyExecute($sqlUp, $param);
        return $cnt;
    }
	
    /**
     *
     * 売上伝票明細を作成する(サブ処理)
     *
     * <AUTHOR> Kayo
     * @since 2017/10/28
     * @param      Msi_Sys_Db $db
     * @param      array  $dtl 生花返礼品レコード
     * @param      string  $upd_flg 更新フラグ 0:新規 1:更新
     * @return int 作成件数
     */
    protected static function makeUriageDenpyoMsiSub($db, &$dtl, $upd_flg) {
		// 売上伝票の存在チェック
		$uridata = $db->easySelOne( <<< END_OF_SQL
SELECT u.*
	,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD') AS juchu_ymd_ex		
FROM uriage_denpyo	u
WHERE u.delete_flg = 0
AND	u.seko_no   = :seko_no
AND u.data_kbn	   IN (1,2)
END_OF_SQL
        , array('seko_no' => $dtl['seko_no']));
		if (count($uridata) <= 0)	{
			return	0;
		}
		$dtl['uri_den_no']		= $uridata['uri_den_no'];
		$dtl['denpyo_no_seko']  = $uridata['denpyo_no'];
		$dtl['data_kbn']		= $uridata['data_kbn'];
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); 
		// 商品マスタの存在チェック
		$shodata = $db->easySelOne( <<< END_OF_SQL
SELECT 
	 sm.tani_cd
	,sm.siire_cd
	,sm.bumon_cd		
	,sm.uri_zei_kbn		
	,bm.dai_bunrui_cd
	,bm.chu_bunrui_cd
	,bm.shohin_kbn
	,sim.siire_lnm	AS	siire_nm		
FROM shohin_mst	sm
LEFT JOIN shohin_bunrui_mst bm
  ON	sm.kaisya_cd	= bm.kaisya_cd 
  AND	sm.bumon_cd		= bm.bumon_cd
  AND	sm.shohin_cd	= bm.shohin_cd
  AND	0				= bm.delete_flg
LEFT JOIN siire_mst sim
  ON	sm.kaisya_cd	= sim.kaisya_cd 
  AND	sm.siire_cd		= sim.siire_cd
  AND	0				= sim.delete_flg
WHERE sm.delete_flg		= 0
AND	sm.shohin_cd		= :shohin_cd
AND sm.kaisya_cd		= :kaisya_cd			
AND bm.dai_bunrui_cd	= '0025'
END_OF_SQL
        , array('shohin_cd' => $dtl['shohin_cd'], 'kaisya_cd' => $kaisya_cd));
		if (Msi_Sys_Utils::myCount($shodata) <= 0)	{
			return	0;
		}
		$cnt = 0;
		$data = array();
		if ($upd_flg == 0)	{
			//  売上明細№ 最大値
			$maxMsiNo = static::_getMaxdenpyoMsiNo($db, $uridata['uri_den_no']);
			// 新規
			$data['uri_den_no']					=	$uridata['uri_den_no'];		// 売上伝票№
			$data['msi_no']						=	$maxMsiNo;					// 売上明細№
			$data['disp_no']					=	1;							// 表示順
			$data['data_sbt']					=	1;							// データ種別
			$data['bumon_cd']					=	$uridata['bumon_cd'];		// 売上部門コード
			$data['seko_no']					=	$uridata['seko_no'];		// 施行番号
			$data['seko_no_sub']				=	$uridata['seko_no_sub'];	// 施行番号（枝番）
			$data['juchu_ymd']					=	$dtl['juchu_ymd'];		// 受注日
			$data['denpyo_kbn']					=	$dtl['denpyo_kbnx'];		// 伝票区分
			$data['mokuteki_kbn']				=	2;							// 使用目的区分
			$data['dai_bunrui_cd']				=	$shodata['dai_bunrui_cd'];	// 大分類コード
			$data['chu_bunrui_cd']				=	$shodata['chu_bunrui_cd'];	// 中分類コード
			$data['shohin_kbn']					=	$shodata['shohin_kbn'];		// 商品区分
			$data['shohin_cd']					=	$dtl['shohin_cd'];			// 商品コード
			$data['shohin_nm']					=	$dtl['shohin_nm']; // 商品名
			$data['shohin_tkiyo_nm']			=	$dtl['shohin_tekiyo'];		// 商品摘要名
			$data['shohin_tkiyo_nm2']			=	(isset($dtl['shohin_tekiyo2'])) ? $dtl['shohin_tekiyo2'] : NULL; // 商品摘要名2
			$data['juchu_suryo']				=	$dtl['juchu_suryo'];		// 数量
			$data['tani_cd']					=	$shodata['dai_bunrui_cd'];	// 単位コード
			$data['uri_tnk']					=	$dtl['juchu_tnk'];			// 単価
			$data['uri_prc']					=	$dtl['juchu_suryo'] * $dtl['juchu_tnk'];			// 受注金額
			$data['nebiki_prc']					=	$dtl['nebiki_prc'];			// 値引額
			$data['gen_tnk']					=	0;							// 原価単価
			$data['gen_gaku']					=	0;							// 原価金額
			$data['arari_gaku']					=	0;							// 粗利益額
			// 消費税取得処理
			$uri_prc = $data['uri_prc'] + $data['nebiki_prc'];
			$data['zei_kbn']					=	$dtl['zei_kbn'];	// 消費税区分
			$data['zei_cd']						=	$dtl['zei_cd'];			// 消費税コード
                        $data['reduced_tax_rate'] = $dtl['reduced_tax_rate'];
                        list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate00) = DataMapper_ZeiMstEasy::getRtuAndEtc($dtl['zei_cd']);
                        if ( $data['reduced_tax_rate'] == 2 && $reduced_tax_rate00 != $data['reduced_tax_rate'] ) {
                            throw new Msi_Sys_Exception_InputException( "(d8fbf2e8)軽減税率区分が不正です(zei_cd=%s, reduced_tax_rate=%s",
                                                                        $dtl['zei_cd'], $data['reduced_tax_rate'] );
                        }
                        // 消費税額
                        // 軽減税率対応 keigen  cf. Juchu_JuchuAbstract::setZeiInfo()
                        // 後ほど伝票ベースで計算しなおし、再設定される
                        $zei = App_ClsTaxLib::CalcTax($uri_prc, $dtl['zei_kbn'], $zei_rtu, $zei_hasu_kbn);
                        if ($dtl['zei_kbn'] == 1) {
                            $data["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
                            $data['out_zei_prc'] = 0;    // 行外税消費税額
                        } else if ($dtl['zei_kbn'] == 2) {
                            $data['in_zei_prc'] = 0;     // 行内税消費税額
                            $data["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
                        } else {
                            $data['in_zei_prc'] = 0;
                            $data["out_zei_prc"] = 0;
                        }
			$data['nafuda_nm']					=	null;					// 名札
			$data['hoshi_umu_kbn']				=	0;						// 奉仕料有無区分
			$data['hoshi_ritu_cd']				=	1;						// 奉仕料率コード
			$data['hoshi_prc']					=	0;						// 奉仕料金額
            $data['nonyu_cd']					=	null;					// 納入先コード
			$data['nonyu_nm']					=	null;					// 納入先名
			$data['nonyu_knm']					=	null;					// 納入先名カナ
			$data['nonyu_yubin_no']				=	$dtl['nonyu_yubin_no'];	// 納入先郵便番号
			$data['nonyu_addr1']				=	$dtl['nonyu_addr1'];	// 納入先住所1
			$data['nonyu_addr2']				=	$dtl['nonyu_addr2'];	// 納入先住所2
			$data['nonyu_tel']					=	$dtl['nonyu_tel'];		// 納入先電話番号
			$data['nonyu_fax']					=	null;					// 納入先FAX
			$data['nonyu_dt']					=	null;					// 納入予定日
			$data['msi_biko1']					=	null;					// 明細備考１
			$data['msi_biko2']					=	null;					// 明細備考２
			$data['siire_cd']					=	$shodata['siire_cd'];	// 仕入先コード
			$data['siire_lnm']					=	$shodata['siire_nm'];	// 仕入先名
			$data['uri_kamoku_cd']				=	null;					// 科目コード
			$data['add_kbn']					=	0;						// 追加区分
			$data['refer_uchiwk_no']			=	null;					// 参照先明細№
			$data['select_shohin_cd']			=	null;					// 選択商品コード
			$data['keijo_ymd']					=	$uridata['keijo_ymd'];	// 売上計上日
			$data['shuko_status_kbn']			=	0;						// 出庫状態区分
			$data['soko_cd']					=	null;					// 倉庫コード
			$data['gojokai_nebiki_prc']			=	0;						// 互助会値引額
			$data['add_henpin_ymd']				=	Msi_Sys_Utils::emptyToNull($dtl['add_henpin_ymdx']);    // 追加・返品日
			$data['kaisya_cd']					=	$uridata['kaisya_cd'];	// 会社コード
			$data['shohin_bumon_cd']			=	$shodata['bumon_cd'];	// 商品部門コード
			$data['nonyu_time']					=	null;					// 納入予定時間
			$data['nonyu_status_kbn']			=	null;					// 納入状態区分
			$data['gs_nouki_dt']				=	null;					// 業者納期日
			$data['gs_nouki_time']				=	null;					// 業者納期時間
			$data['gs_nohinsaki_kbn']			=	null;					// 業者納品先区分
			$data['gs_nohinsaki_nm']			=	null;					// 業者納品先名
			$data['noshi_kbn']					=	null;					// のし紙
			$data['convert_kbn']				=	0;						// コンバート区分
			$data['v_free1']					=	null;					// 文字フリー項目１
			$data['v_free2']					=	null;					// 文字フリー項目２
			$data['v_free3']					=	null;					// 文字フリー項目３
			$data['v_free4']					=	null;					// 文字フリー項目４
			$data['v_free5']					=	$dtl['v_free5'];                        // 文字フリー項目５(受注担当者コード)
			$data['k_free1']					=	null;					// 区分フリー項目１
			$data['k_free2']					=	null;					// 区分フリー項目２
			$data['k_free3']					=	null;					// 区分フリー項目３
			$data['k_free4']					=	null;					// 区分フリー項目４
			$data['k_free5']					=	null;					// 区分フリー項目５
			$data['n_free1']					=	null;					// 数字フリー項目１
			$data['n_free2']					=	null;					// 数字フリー項目２
			$data['n_free3']					=	null;					// 数字フリー項目３
			$data['n_free4']				=	Msi_Sys_Utils::emptyToNull($dtl['n_free4']);    // 数字フリー項目４(コピー元の明細番号)
			$data['n_free5']					=	null;					// 数字フリー項目５
			$data['d_free1']					=	null;					// 日付フリー項目１
			$data['d_free2']					=	null;					// 日付フリー項目２
			$data['d_free3']					=	null;					// 日付フリー項目３
			$data['ts_free1']					=	null;					// タイムスタンプフリー項目１
			$data['ts_free2']					=	null;					// タイムスタンプフリー項目２
			$data['ts_free3']					=	null;					// タイムスタンプフリー項目３
			$data['zaimu_rendo_kbn']			=	0;						// 財務連動区分
			$data['zaimu_rendo_date']			=	null;					// 財務連動日付
			$data['zaimu_tanto_cd']				=	null;					// 財務連動担当者コード
			$data['zaimu_rendo_approval_kbn']	=	null;					// 財務連動承認区分
			$data['zaimu_rendo_approval_date']	=	null;					// 財務連動承認日付
			$data['zaimu_tanto_approval_cd']	=	null;					// 財務連動承認担当者コード
			$data['zaimu_rendo_denno']			=	null;					// 財務連動伝票番号
			$data['nyukin_den_no']				=	null;					// 入金伝票№
			$data['nyukin_msi_no']				=	null;					// 入金明細№
			$data['nyukin_kbn']					=	null;					// 入金区分
            $data['seika_henrei_disp_no']		=	$dtl['seika_henrei_disp_no'];   // 生花返礼品表示順
            $data['k_free4']					=	(isset($dtl['chouden'])) ? $dtl['chouden'] : NULL;// 弔電cd
            $data['v_free4']					=	(isset($dtl['chouden_sonota'])) ? $dtl['chouden_sonota'] : NULL;// 弔電nm
            $data['v_free2']					=	(isset($dtl['j_nafuda_nm'])) ? $dtl['j_nafuda_nm'] : NULL;// 名札1
            $data['v_free3']					=	(isset($dtl['j_msi_biko1'])) ? $dtl['j_msi_biko1'] : NULL;// 名札2
            $data['delete_flg']					=	0;						// 削除フラグ
			list($sql, $param) = DataMapper_Utils::makeInsertSQL( 'uriage_denpyo_msi', $data );
			$cnt += $db->easyExecute($sql, $param);
            if (strlen($dtl['j_shohin_cd']) > 0) {
                // 別注品で入力されたもの
                $dtl['seika_henrei_msi_no']			=  $maxMsiNo;
                $cnt += static::updateJuchuDenpyoMsi($db, $dtl, 1);
            }    
		} else {
			// 更新
			$data['juchu_ymd']					=	$dtl['juchu_ymd'];		// 受注日
			$data['denpyo_kbn']					=	$dtl['denpyo_kbnx'];		// 伝票区分
			$data['mokuteki_kbn']				=	2;							// 使用目的区分
			$data['dai_bunrui_cd']				=	$shodata['dai_bunrui_cd'];	// 大分類コード
			$data['chu_bunrui_cd']				=	$shodata['chu_bunrui_cd'];	// 中分類コード
			$data['shohin_kbn']					=	$shodata['shohin_kbn'];		// 商品区分
			$data['shohin_cd']					=	$dtl['shohin_cd'];			// 商品コード
			$data['shohin_nm']					=	$dtl['shohin_nm'];			// 商品名
			$data['shohin_tkiyo_nm']			=	$dtl['shohin_tekiyo'];		// 商品摘要名
			$data['shohin_tkiyo_nm2']			=	(isset($dtl['shohin_tekiyo2'])) ? $dtl['shohin_tekiyo2'] : NULL;		// 商品摘要名2
			$data['juchu_suryo']				=	$dtl['juchu_suryo'];		// 数量
			$data['tani_cd']					=	$shodata['dai_bunrui_cd'];	// 単位コード
			$data['uri_tnk']					=	$dtl['juchu_tnk'];			// 単価
			$data['uri_prc']					=	$dtl['juchu_suryo'] * $dtl['juchu_tnk'];			// 受注金額
			$data['nebiki_prc']					=	$dtl['nebiki_prc'];			// 値引額
			$data['gen_tnk']					=	0;							// 原価単価
			$data['gen_gaku']					=	0;							// 原価金額
			$data['arari_gaku']					=	0;							// 粗利益額
			$data['add_henpin_ymd']				=	Msi_Sys_Utils::emptyToNull($dtl['add_henpin_ymdx']);    // 追加・返品日
			// 消費税取得処理
			$uri_prc = $data['uri_prc'] + $data['nebiki_prc'];
			$data['zei_kbn']					=	$dtl['zei_kbn'];	// 消費税区分
			$data['zei_cd']						=	$dtl['zei_cd'];			// 消費税コード
                        $data['reduced_tax_rate'] = $dtl['reduced_tax_rate'];
                        list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate00) = DataMapper_ZeiMstEasy::getRtuAndEtc($dtl['zei_cd']);
                        if ( $data['reduced_tax_rate'] == 2 && $reduced_tax_rate00 != $data['reduced_tax_rate'] ) {
                            throw new Msi_Sys_Exception_InputException( "(d8fbf2e8)軽減税率区分が不正です(zei_cd=%s, reduced_tax_rate=%s",
                                                                        $dtl['zei_cd'], $data['reduced_tax_rate'] );
                        }
                        // 消費税額
                        // 軽減税率対応 keigen  cf. Juchu_JuchuAbstract::setZeiInfo()
                        // 後ほど伝票ベースで計算しなおし、再設定される
                        $zei = App_ClsTaxLib::CalcTax($uri_prc, $dtl['zei_kbn'], $zei_rtu, $zei_hasu_kbn);
                        if ($dtl['zei_kbn'] == 1) {
                            $data["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
                            $data['out_zei_prc'] = 0;    // 行外税消費税額
                        } else if ($dtl['zei_kbn'] == 2) {
                            $data['in_zei_prc'] = 0;     // 行内税消費税額
                            $data["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
                        } else {
                            $data['in_zei_prc'] = 0;
                            $data["out_zei_prc"] = 0;
                        }
			$data['nafuda_nm']					=	null;					// 名札
			$data['siire_cd']					=	$shodata['siire_cd'];	// 仕入先コード
			$data['siire_lnm']					=	$shodata['siire_nm'];	// 仕入先名
			$data['shohin_bumon_cd']			=	$shodata['bumon_cd'];	// 商品部門コード
            $data['seika_henrei_disp_no']		=	$dtl['seika_henrei_disp_no'];   // 生花返礼品表示順
			$data['nonyu_yubin_no']				=	$dtl['nonyu_yubin_no'];	// 納入先郵便番号
			$data['nonyu_addr1']				=	$dtl['nonyu_addr1'];	// 納入先住所1
			$data['nonyu_addr2']				=	$dtl['nonyu_addr2'];	// 納入先住所2
			$data['nonyu_tel']					=	$dtl['nonyu_tel'];		// 納入先電話番号
            $data['k_free4']					=	(isset($dtl['chouden'])) ? $dtl['chouden'] : NULL;// 弔電cd
            $data['v_free4']					=	(isset($dtl['chouden_sonota'])) ? $dtl['chouden_sonota'] : NULL;// 弔電nm
            $data['v_free2']					=	(isset($dtl['j_nafuda_nm'])) ? $dtl['j_nafuda_nm'] : NULL;// 名札1
            $data['v_free3']					=	(isset($dtl['j_msi_biko1'])) ? $dtl['j_msi_biko1'] : NULL;// 名札2
			$data['v_free5']					=	$dtl['v_free5'];                        // 文字フリー項目５(受注担当者コード)
			$data['n_free4']				=	Msi_Sys_Utils::emptyToNull($dtl['n_free4']);    // 数字フリー項目４(コピー元の明細番号)
			$where  = array();
			$where['uri_den_no'] = $uridata['uri_den_no'];
			$where['msi_no']    = $dtl['seika_henrei_msi_no'];
			$where['delete_flg']= 0;
			list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $data, $where);
			$cnt += $db->easyExecute($sqlUp, $param);
		}
	}

    /**
     *
     * 最大受注明細Noを取得する
     *
     * <AUTHOR> Kayo
     * @since 2017/10/29
     * @param      Msi_Sys_Db $db
     * @param      string  $uri_den_no 売上伝票№
     * @return int 最大売上明細№
     */
    protected static function _getMaxdenpyoMsiNo($db, $uri_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT COALESCE(MAX(msi_no),0) AS msi_no
FROM	uriage_denpyo_msi
WHERE   uri_den_no = :uri_den_no
END_OF_SQL;
        $max = $db->getOneVal($sql, array('uri_den_no' => $uri_den_no));
        return $max + 1;
    }

    /**
     *
     * 売上伝票を再集計する(消費税等)
     *
     * <AUTHOR> Kayo
     * @since 2017/10/29
     * @param      Msi_Sys_Db $db
     * @param      string  $uri_den_no 売上伝票№
     * @return int $cnt 更新件数
     */
    protected static function sumUriageDenpyo($db, $uri_den_no) {
        $cnt = 0;
        $juchu_prc_sum = 0;         // 受注金額合計
        $genka_prc_sum = 0;         // 原価金額合計
        $juchu_hepn_sum = 0;         // 受注返品合計
        $juchu_nebk_sum = 0;         // 受注値引合計
        $hoshi_prc_sum = 0;         // 奉仕料合計
        $szei_katax_taisho_prc = 0; // 外税課税対象額
        $uzei_katax_taisho_prc = 0; // 内税課税対象額
        $hitax_katax_taisho_prc = 0; // 非税課税対象額
        $out_zei_prc = 0;            // 外税消費税額
        $in_zei_prc = 0;            // 内税消費税額
        $arari_prc = 0;            // 粗利益額
        $pBaika = 0;                 // 売価
        $pZeiRt = null;              // OUT 消費税率
        $pShouhiZeiPrc = null;       // OUT 消費税額
        $pRuikeiBaikaPrc = 0;        // REF 累計売価額
        $pRuikeiShohiZeiPrc = 0;     // REF 累計消費税額
        $pChoseiPrc = 0;             // OUT 調整額
        // 売上伝票明細を取得
        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
	,TO_CHAR(juchu_ymd,'YYYY/MM/DD') AS juchu_ymd_ex		
FROM uriage_denpyo_msi
WHERE delete_flg = 0
AND	uri_den_no   = :uri_den_no
ORDER BY msi_no			
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
        $juchu_ymd = null;
        $count = 0;
        foreach ($select as $rec) {
            ++$count;
            if ($count == 1) {
                $juchu_ymd = $rec['juchu_ymd_ex'];
            }
            $pBaika = $rec['uri_prc'] + $rec['hoshi_prc'] + $rec['nebiki_prc'] + $rec['gojokai_nebiki_prc'];
            $outZei = App_ClsTaxLib::GetCalcTax($db, $rec['juchu_ymd_ex'], $pBaika, $rec['zei_kbn']);
            $pZeiRt = $outZei['zei_rtu'];
            if ($rec['zei_kbn'] == 1) { // 1(内税)
                if (App_ClsTaxLib::GetTaxMsiInfoUchiZei($db
                                , $rec['juchu_ymd_ex']
                                , $pBaika
                                , $rec['zei_kbn']
                                , $rec['zei_cd']
                                , $pZeiRt
                                , $pShouhiZeiPrc
                                , $pRuikeiBaikaPrc
                                , $pRuikeiShohiZeiPrc
                                , $pChoseiPrc) !== true) {
                    $errMsg = sprintf("%d行目: 消費税計算エラー", $rec['msi_no']);
                    throw new Msi_Sys_Exception_InputException($errMsg);
                }
                $uzei_katax_taisho_prc += $pBaika;
            } else if ($rec['zei_kbn'] == 2) { // 2(外税)
                if (App_ClsTaxLib::GetTaxMsiInfo($db
                                , $rec['juchu_ymd_ex']
                                , $pBaika
                                , $rec['zei_kbn']
                                , $rec['zei_cd']
                                , $pZeiRt
                                , $pShouhiZeiPrc
                                , $pRuikeiBaikaPrc
                                , $pRuikeiShohiZeiPrc
                                , $pChoseiPrc) !== true) {
                    $errMsg = sprintf("%d行目: 消費税計算エラー", $rec['msi_no']);
                    throw new Msi_Sys_Exception_InputException($errMsg);
                }
                $szei_katax_taisho_prc += $pBaika;
            } else {
                if (App_ClsTaxLib::GetTaxMsiInfo($db
                                , $rec['juchu_ymd_ex']
                                , $pBaika
                                , $rec['zei_kbn']
                                , $rec['zei_cd']
                                , $pZeiRt
                                , $pShouhiZeiPrc
                                , $pRuikeiBaikaPrc
                                , $pRuikeiShohiZeiPrc
                                , $pChoseiPrc) !== true) {
                    $errMsg = sprintf("%d行目: 消費税計算エラー", $rec['msi_no']);
                    throw new Msi_Sys_Exception_InputException($errMsg);
                }
                $hitax_katax_taisho_prc += $pBaika;
            }
            if ($rec['denpyo_kbn'] == 2) { // 2:返品
                $juchu_hepn_sum += $rec['uri_prc'];
            } else {
                $juchu_prc_sum += $rec['uri_prc'];
            }
            $genka_prc_sum += $rec['gen_gaku'];
            $juchu_nebk_sum += $rec['nebiki_prc'] + $rec['gojokai_nebiki_prc'];
            $arari_prc += $rec['arari_gaku'];
            $hoshi_prc_sum += $rec['hoshi_prc'];  // 奉仕料合計
        }
        // 消費税総額設定
        $zeiData = App_KeigenUtils::calcUriageZeiDenpyo($db, $uri_den_no);
        $out_zei_prc = $zeiData['out_zei_prc'];
        $in_zei_prc = $zeiData['in_zei_prc'];

        // 消費税端数調整 tosaka keigen();
        $cnt += App_KeigenUtils::setUriageDenpyoAdjTax($db, $uri_den_no);
        $updData = array('uri_den_no' => $uri_den_no,
            'uri_prc_sum' => $juchu_prc_sum,
            'genka_prc_sum' => $genka_prc_sum,
            'uri_hepn_sum' => $juchu_hepn_sum,
            'uri_nebk_sum' => $juchu_nebk_sum,
            'hoshi_prc_sum' => $hoshi_prc_sum,
            'arari_prc' => $arari_prc,
            'szei_katax_taisho_prc' => $szei_katax_taisho_prc,
            'uzei_katax_taisho_prc' => $uzei_katax_taisho_prc,
            'hitax_katax_taisho_prc' => $hitax_katax_taisho_prc,
            'out_zei_prc' => $out_zei_prc,
            'in_zei_prc' => $in_zei_prc,
        );
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL2('uriage_denpyo', $updData, array('uri_den_no'));
        $cnt += $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 生花返礼品受注の削除（別注品が削除されたら）
     *
     * <AUTHOR> Kayo
     * @since      2017/10/27
     * @param      string  $denpyo_no 受注伝票番号
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function Delete($denpyo_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();
		// 受注伝票明細が存在チェック
		$select = $db->easySelect( <<< END_OF_SQL
SELECT *
FROM juchu_denpyo_msi
WHERE denpyo_no   = :denpyo_no
AND seika_henrei_shohin_cd IS NOT NULL			
END_OF_SQL
        , array('denpyo_no' => $denpyo_no));
        $cnt		= 0;
		$uri_den_no = null;
		$denpyo_no	= null;
		$data_kbn	= 1;
        // 受注伝票明細を更新
        foreach ( $select as $rec ) {
			// 売上伝票を削除する
			$cnt += static::deleteUriageDenpyoMsi($db, $rec);
			if (isset($rec['denpyo_no_seko']))	{
				$uri_den_no	= $rec['uri_den_no'];
				$denpyo_no	= $rec['denpyo_no_seko'];
				$data_kbn	= $rec['data_kbn'];
			}
        }
		// 売上伝票を再集計する(消費税等)
		if (strlen($uri_den_no) > 0)	{
            // 発注管理情報を作成する
            $cnt += Logic_JuchuSekoHachuInfoMake::upsertHachuInfo($db, $uri_den_no, '0025');
		}
        // 集計処理
		if (strlen($denpyo_no) > 0)	{
			if ( Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyo_no, $data_kbn) !== true ) {
				throw new Msi_Sys_Exception_LogicException( "集計処理でエラーが発生しました" );
			}
		}	
        return $cnt;
    }
	
	/**
	 * 依頼主や請求先のデータを作成
	 * 
	 * <AUTHOR> Chan Aung
	 * @since 2017/11/21
	 * @param object $db database オブジェクト
	 * @param array $data 保存するデータ
	 * @param string $seko_no [condition]
	 */
	public static function createIraiNushiAndSekyuSaki($db, $data) {
		$updData = array(
			'v_free28' => empty($data['v_free28']) ? NULL : $data['v_free28'], //依頼主名
			'v_free29' => $data['v_free29'], //依頼主郵便
			'v_free30' => $data['v_free30'], //依頼主住所１
			'v_free31' => $data['v_free31'], //依頼主住所２
			'v_free32' => $data['v_free32'], //依頼主電話番号
			'v_free33' => $data['v_free33'], //請求先名
			'v_free34' => $data['v_free34'], //請求先郵便
			'v_free35' => $data['v_free35'], //請求先住所１
			'v_free36' => $data['v_free36'], //請求先住所２
			'v_free37' => $data['v_free37'], //請求先電話番号
			'tanto_cd5' => $data['tanto_cd5'], //セレモ担当コード
			'tanto_nm5' => $data['tanto_nm5'] //セレモ担当名
		);
		list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_kihon_all_free', $updData, array('seko_no' => $data['seko_no']));
		$db->easyExecute($sql, $param);
	}

}
