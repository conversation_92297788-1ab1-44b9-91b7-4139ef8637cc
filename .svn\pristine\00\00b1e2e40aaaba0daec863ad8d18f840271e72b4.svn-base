<?php

/**
 * 会員情報登録リスト（CSV出力のみ）
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Okuyama
 * @since 	 2017/05/26
 * @filesource 
 */

/**
 * 会員情報登録リスト
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Okuyama
 * @since 	 2017/05/26
 */
class Mstr_Pdf1421Controller extends Zend_Controller_Action {

	private static $title1 = '会員および家族情報登録リスト';
	private static $title2 = '会員情報登録リスト';

	/*****************************************
	 * アクション（会員および家族情報抽出）
     *
     * <AUTHOR> Okuyama
     * @since 2017/05/26
     */
    public function indexAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $db = Msi_Sys_DbManager::getMyDb();
        $title = self::$title1;

        $csvData = $this->getCsv($db);
        if (!isset($csvData)) {
            App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
            return;
        }
        $buf = Msi_Sys_Utils::csvOutString($csvData);
        Msi_Sys_Utils::out2way1($buf, $title . '.csv', 'text/csv');
    }
    /**
     * 会員情報SELECT
     *
     * <AUTHOR> Okuyama
     * @since 2017/05/26
     * @param Msi_Sys_Db $db	データベース
     * @return	array	$set_ary	設定配列
     */
    private function kaiin_mst_select($db) {
        $select = $db->easySelect(<<< END_OF_SQL
SELECT
        SSI.sekyu_cd                AS  会員コード
        ,SSI.sekyu_nm               AS  会員名
        ,SSI.sekyu_knm              AS  会員カナ名
        ,SSI.yubin_no               AS  郵便番号
        ,SSI.addr1                  AS  住所１
        ,SSI.addr2                  AS  住所２
        ,SSI.tel                    AS  電話番号
        ,SSI.mobile_tel             AS  携帯番号
        ,SKI.kaiin_kbn              AS  会員区分
        ,NM_KaiinKbn.kbn_value_lnm  AS  会員区分名
        ,SKI.kaiin_shubetsu         AS  会員種別
        ,NM_KaiinSbt.kbn_value_lnm  AS  会員種別名
        ,SKI.dm_soufu_kbn           AS  DM送付区分
        ,NM_DMsofu.kbn_value_lnm    AS  DM送付区分名
        ,SKI.birthday               AS  生年月日　
        ,SKI.k_sex_code_kbn         AS  性別区分
        ,NM_Seibetsu.kbn_value_lnm  AS  性別
        ,SKI.nyukaibi               AS  入会日
        ,SKI.tanto_nm               AS  担当者名
        ,SKI.biko1                  AS  会員備考１
        ,SKI.biko2                  AS  会員備考２
        ,SSI.fax                    AS  FAX番号
        ,SKI.mail_addr1             AS  メールアドレス１
        ,SKI.mail_addr2             AS  メールアドレス２
        ,SUBSTR(
            SKI._cre_user,                  -- 抽出文字列　（会社コード.ユーザーコード）
            STRPOS(SKI._cre_user, '.') + 1  -- 抽出位置開始位置
        )                                   AS  作成者
        ,TO_CHAR(SKI._cre_ts, 'YYYY/MM/DD') AS  作成日
        ,SUBSTR(
            SKI._mod_user,                  -- 抽出文字列　（会社コード.ユーザーコード）
            STRPOS(SKI._mod_user, '.') + 1  -- 抽出位置開始位置
        )                                   AS  最終更新者
        ,TO_CHAR(SKI._mod_ts, 'YYYY/MM/DD') AS  最終更新日
        ,SZI.sekyu_cd               AS  会員コード２
        ,SZI.msi_no                 AS  会員コード連番
        ,SZI.kokyaku_nm             AS  家族名
        ,SZI.kokyaku_knm            AS  家族名カナ
        ,SZI.m_zoku_cd              AS  続柄コード
        ,SZI.m_zoku_nm              AS  続柄	
        ,SZI.birthday               AS  生年月日
        ,SZI.dokyo_kbn              AS  同居区分
        ,NM_Dokyo.kbn_value_lnm     AS  同居区分名
        ,SZI.biko1                  AS  家族備考
        ,SZI.zip_no                 AS  家族郵便番号
        ,SZI.addr1                  AS	家族住所１
        ,SZI.addr2                  AS	家族住所２
        ,SUBSTR(
            SZI._cre_user,                  -- 抽出文字列　（会社コード.ユーザーコード）
            STRPOS(SZI._cre_user, '.') + 1  -- 抽出位置開始位置
        )                                   AS  作成者２
        ,TO_CHAR(SZI._cre_ts, 'YYYY/MM/DD') AS  作成日２
        ,SUBSTR(
            SZI._mod_user,                  -- 抽出文字列　（会社コード.ユーザーコード）
            STRPOS(SZI._mod_user, '.') + 1  -- 抽出位置開始位置
        )                                   AS  最終更新者２
        ,TO_CHAR(SZI._mod_ts, 'YYYY/MM/DD') AS  最終更新日２
FROM 
    sekyu_saki_info         AS  SSI
INNER JOIN sekyu_kaiin_info AS  SKI 
    ON  SSI.seko_no     =   SKI.seko_no
    AND SSI.sekyu_cd    =   SKI.sekyu_cd
    AND SKI.delete_flg  =   0
LEFT JOIN sekyu_kazoku_info AS SZI
    ON SSI.seko_no      =   SZI.seko_no
    AND SSI.sekyu_cd    =   SZI.sekyu_cd
    AND SZI.delete_flg  =   0
LEFT JOIN code_nm_mst       AS  NM_KaiinKbn
    ON NM_KaiinKbn.code_kbn     =   '3760'
    AND NM_KaiinKbn.delete_flg  =   0
    AND SKI.kaiin_kbn           =   NM_KaiinKbn.kbn_value_cd_num
LEFT JOIN code_nm_mst       AS  NM_KaiinSbt
    ON NM_KaiinSbt.code_kbn     =   '3770'
    AND NM_KaiinSbt.delete_flg  =   0
    AND SKI.kaiin_shubetsu      =   NM_KaiinSbt.kbn_value_cd_num
LEFT JOIN code_nm_mst       AS  NM_DMsofu   
    ON NM_DMsofu.code_kbn       =   '3780'
    AND NM_DMsofu.delete_flg    =   0
    AND SKI.dm_soufu_kbn        =   NM_DMsofu.kbn_value_cd_num
LEFT JOIN code_nm_mst       AS  NM_Seibetsu
    ON NM_Seibetsu.code_kbn     =   '0080'
    AND NM_Seibetsu.delete_flg  =   0
    AND SKI.k_sex_code_kbn      =   NM_Seibetsu.kbn_value_cd
LEFT JOIN code_nm_mst       AS  NM_Dokyo
    ON NM_Dokyo.code_kbn        =   '3790'
    AND NM_Dokyo.delete_flg     =   0
    AND SZI.dokyo_kbn           =   NM_Dokyo.kbn_value_cd_num
WHERE
    SSI.delete_flg  =   0
ORDER BY
    SSI.sekyu_cd
    ,SZI.msi_no
END_OF_SQL
        );
        return $select;
    }
    /**
     * CSV出力メイン
     *
     * <AUTHOR> Okuyama
     * @since 2017/05/26
     * @param Msi_Sys_Db $db	データベース
     * @return	   viod
     */
    private function getCsv($db) {
        // 会員情報リスト
        $select = $this->kaiin_mst_select($db);
        if (count($select) == 0) {
            return null;
        }
        // CSVを編集
        $csvData = App_ClsMsterCsvEdit::EditCsv($select);
        return $csvData;
    }

    /*****************************************
     * アクション（会員情報抽出）
     *
     * <AUTHOR> Okuyama
     * @since 2018/08/19
     * ****************************************/
    public function index2Action() {
        $params = Msi_Sys_Utils::webInputs();
        $db = Msi_Sys_DbManager::getMyDb();
        $title = self::$title2;
        $csvData = $this->getCsv_Kaiin($db);
        if (!isset($csvData)) {
            App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
            return;
        }
        $buf = Msi_Sys_Utils::csvOutString($csvData);
        Msi_Sys_Utils::out2way1( $buf, $title.'.csv', 'text/csv' );
    }

    /**
     * 会員情報SELECT
     *
     * <AUTHOR> Okuyama
     * @since 2018/08/19
     * @param Msi_Sys_Db $db	データベース
     * @return	array	$set_ary	設定配列
     */
    private function kaiin_mst_select2($db) {

        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         SSI.sekyu_cd                                     AS 会員コード
        ,SSI.sekyu_nm                                     AS 会員名
        ,SSI.sekyu_knm                                    AS 会員カナ名
        ,SSI.yubin_no                                     AS 郵便番号
        ,SSI.addr1                                        AS 住所１
        ,SSI.addr2                                        AS 住所２
        ,SSI.tel                                          AS 電話番号
        ,SSI.mobile_tel                                   AS 携帯番号
        ,SKI.kaiin_kbn                                    AS 会員区分
        ,NM_KaiinKbn.kbn_value_lnm                        AS 会員区分名
        ,SKI.kaiin_shubetsu                               AS 会員種別
        ,NM_KaiinSbt.kbn_value_lnm                        AS 会員種別名
        ,SKI.dm_soufu_kbn                                 AS DM送付区分
        ,NM_DMsofu.kbn_value_lnm                          AS DM送付区分名
        ,SKI.birthday                                     AS 生年月日
        ,SKI.k_sex_code_kbn                               AS 性別区分
        ,NM_Seibetsu.kbn_value_lnm                        AS 性別
        ,SKI.nyukaibi                                     AS 入会日
        ,SKI.tanto_nm                                     AS 担当者名
        ,SKI.biko1                                        AS 会員備考１
        ,SKI.biko2                                        AS 会員備考２
        ,SSI.fax                                          AS FAX番号
        ,SKI.mail_addr1                                   AS メールアドレス1
        ,SKI.mail_addr2                                   AS メールアドレス2
        ,cre_tanto.tanto_nm                               AS 登録者
        ,TO_CHAR(SKI._cre_ts, 'YYYY/MM/DD HH24:MI')       AS 登録日時
        ,mod_tanto.tanto_nm                               AS 更新者
        ,TO_CHAR(SKI._mod_ts, 'YYYY/MM/DD HH24:MI')       AS 更新日時
    FROM sekyu_saki_info AS SSI
    INNER JOIN sekyu_kaiin_info	AS SKI 
    ON  SSI.seko_no            = SKI.seko_no
    AND SSI.sekyu_cd           = SKI.sekyu_cd
    AND SKI.delete_flg         = 0
    LEFT JOIN code_nm_mst AS NM_KaiinKbn 
    ON  NM_KaiinKbn.code_kbn   = '3760'
    AND NM_KaiinKbn.delete_flg = 0
    AND SKI.kaiin_kbn          = NM_KaiinKbn.kbn_value_cd_num
    LEFT JOIN code_nm_mst AS NM_KaiinSbt 
    ON  NM_KaiinSbt.code_kbn   = '3770'
    AND NM_KaiinSbt.delete_flg = 0
    AND SKI.kaiin_shubetsu     = NM_KaiinSbt.kbn_value_cd_num
    LEFT JOIN code_nm_mst AS NM_DMsofu 
    ON  NM_DMsofu.code_kbn     = '3780'
    AND NM_DMsofu.delete_flg   = 0
    AND SKI.dm_soufu_kbn       = NM_DMsofu.kbn_value_cd_num
    LEFT JOIN code_nm_mst AS NM_Seibetsu 
    ON  NM_Seibetsu.code_kbn   = '0080'
    AND NM_Seibetsu.delete_flg = 0
    AND SKI.k_sex_code_kbn     = NM_Seibetsu.kbn_value_cd
    LEFT JOIN login_mst cre_login -- ログイン者マスタ（登録者）
    ON  cre_login.login_cd     = SUBSTR(SKI._cre_user, STRPOS(SKI._cre_user,'.')+1, CHAR_LENGTH(SKI._cre_user))
    AND cre_login.delete_flg   = 0
    LEFT JOIN tanto_mst cre_tanto -- 担当者マスタ（登録者）
    ON  cre_tanto.tanto_cd     = cre_login.tanto_cd
    AND cre_tanto.delete_flg   = 0
    LEFT JOIN login_mst mod_login -- ログイン者マスタ（更新者）
    ON  mod_login.login_cd     = SUBSTR(SKI._mod_user, STRPOS(SKI._mod_user,'.')+1, CHAR_LENGTH(SKI._mod_user))
    AND mod_login.delete_flg   = 0
    LEFT JOIN tanto_mst mod_tanto -- 担当者マスタ（更新者）
    ON  mod_tanto.tanto_cd   = mod_login.tanto_cd
    AND mod_tanto.delete_flg = 0
    WHERE 
        SSI.delete_flg = 0
    ORDER BY
        SSI.sekyu_cd
END_OF_SQL
        );
        return $select;
    }

    /**
     * CSV出力メイン（会員情報抽出）
     *
     * <AUTHOR> Okuyama
     * @since 2018/08/19
     * @param Msi_Sys_Db $db	データベース
     * @return	   viod
     */
    private function getCsv_Kaiin($db) {
        // 会員情報リスト
        $select = $this->kaiin_mst_select2($db);
        if (count($select) == 0) {
            return null;
        }
        // CSVを編集
        $csvData = App_ClsMsterCsvEdit::EditCsv($select);

        return $csvData;
    }
}
