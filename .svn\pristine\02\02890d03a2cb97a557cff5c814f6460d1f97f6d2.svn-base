<?php
/**
 * 法事 一般化商品(商品選択,返礼品,料理)購入 
 *   org: 佐野商店(Juchu_JuchuItemhj.sano.php)
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR> Arai
 * @since      2020/11/09 sanmen_kを元にコピー
 * @filesource 
 */

/**
 * 法事 一般化商品(商品選択,返礼品,料理)購入
 *   org: 佐野商店(Juchu_JuchuItemhj.sano.php)
 *
 * @category   App
 * @package    controllers\Juchu
 * <AUTHOR> Mihara
 * @since      2015/04/xx
 */
class Juchu_JuchuItemhj10 extends Juchu_JuchuItemhj05
{
    /**
     * 保存 追加処理
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     */
    protected function savePostHook($dataApp, $dataCol)
    {
        if ( $this->curCatLvl0() == static::CAT_LVL0_RYORI ) {
            $this->savePostHookRyori($dataApp, $dataCol);
        }
    }

    /**
     * 保存 追加処理 料理
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     */
    protected function savePostHookRyori($dataApp, $dataCol)
    {
        // Msi_Sys_Utils::debug( '*** savePostHookRyori=>' . Msi_Sys_Utils::dump($dataCol) );

        $allergy = null;
        if ( isset($dataCol['etcNew']) && isset($dataCol['etcNew']['allergy']) ) {
            $db = Msi_Sys_DbManager::getMyDb();
            foreach ( $dataCol['etcNew']['allergy'] as $curLvl1 => $rec ) {
                $allergy = $rec['txt'];
                $category_kbn = $rec['category_kbn'];
                // Msi_Sys_Utils::debug( '*** curlvl1=>' . $curLvl1 . ' allergy=>' . $allergy . ' kbn=>' . $category_kbn );
                DataMapper_SekoKihonAllFree::easyReg( $db, $this->_sekoNo, $category_kbn,
                                                      '10', // class_kbn＝10：料理関係
                                                      array('v_free1' => $allergy) );
            }
            $db->commit();
        }
    }

    /**
     * 商品選択サブ画面要素生成 コンテンツ tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function itemPickerCont()
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        if ( $lvl0 == static::CAT_LVL0_HENREI ) { // || $lvl0 == static::CAT_LVL0_RYORI ) {
            return 'picker-item-multi.tpl';
        } else if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            return 'picker-item-food.tpl';
        /* } else if ( $lvl0 == static::CAT_LVL0_SHOHIN ) { */
        /*     if ( $lvl1 == 6 ) { // 湯灌 */
        /*         // return 'picker-item-wash.tpl'; */
        /*         return 'picker-item-one.tpl'; */
        /*     } if ( $lvl1 == 1 || $lvl1 == 4 ) { // $lvl1 == 2 庭飾り */
        /*         return 'picker-item-one.tpl'; */
        /*     } else { */
        /*         return 'picker-item-plmi.tpl'; */
        /*     } */
        } else {
            throw new Exception( "lvl0: $lvl0?" );
        }
    }

    /**
     * 商品選択サブ画面要素生成 コメント表示 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function itemPickerCmtTpl()
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        if ( $lvl0 == static::CAT_LVL0_HENREI ) {
            if ( $lvl1 == 1 ) {
                // return 'picker-cmt-henrei.tpl';
            } else if ( $lvl1 == 2 ) {
                // return 'picker-cmt-henrei2.tpl';
            }
        } else if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            $this->ryoriFooterAdj();
            return 'picker-cmt-food.tpl';
        }

        $catInfo = $this->getItemCatInfo();
        if ( isset($catInfo['ryoukin_kbn']) && $catInfo['ryoukin_kbn'] > 0 ) {
            //         Msi_Sys_Utils::debug( '*** catInfo=>' . Msi_Sys_Utils::dump($catInfo) );
            return 'picker-cmt-ryoukin.tpl';
        }

        return '';
    }

    /**
     * 商品選択サブ画面要素生成 フッタ表示 tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function itemPickerFooterTpl()
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            // return $this->ryoriFooterAdj();
        }
        return '';
    }

    /**
     * アレルギー確認を取得する
     *
     * <AUTHOR> Mihara
     * @since 2014/05/xx
     * @return  string  アレルギー確認
     */
    public function get_allergy_memo()
    {
        $category_kbn = $this->getCategoryKbnRaw();

        // 未登録分があればこちらを優先
        $etcNewAllergy = array();
        if ( isset($this->_arrCtxt['dataCol']) && isset($this->_arrCtxt['dataCol']['etcNew']) 
             && isset($this->_arrCtxt['dataCol']['etcNew']['allergy']) ) {
            $etcNewAllergy = $this->_arrCtxt['dataCol']['etcNew']['allergy'];
        }
        foreach ( $etcNewAllergy as $recOw ) {
            if ( (int)$recOw['category_kbn'] === (int)$category_kbn ) { // 未登録データとして存在した
                $txt = $recOw['txt'];
                return $txt;
            }
        }

        // DB から取得
		$db = Msi_Sys_DbManager::getMyDb();
        $rec = DataMapper_SekoKihonAllFree::easyFind( $db, $this->_sekoNo, $this->getCategoryKbnRaw(),
                                                      '10', // class_kbn＝10：料理関係
                                                      array('v_free1') );
        if ( $rec ) {
            return $rec['v_free1'];
        }
        return '';
    }

    /**
     * 料理 フッタ表示 調整処理
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function ryoriFooterAdj()
    {
        $renderer = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');
        $view = $renderer->view;

        $category_kbn = $this->getCategoryKbn();

        // Msi_Sys_Utils::debug( '@@@@@@@@@@@@ category_kbn=>' . $category_kbn );

		// 料理概算、席料を取得
		$food_gaisan = $this->_getFoodGaisanData($category_kbn);

        // Msi_Sys_Utils::debug( '@@@@@@@@@@@@ food_gaisan=>' . Msi_Sys_Utils::dump($food_gaisan) );

        // 未登録分の反映
        $newData = array();
        if ( isset($this->_arrCtxt['dataCol']) && isset($this->_arrCtxt['dataCol']['new']) ) {
            $newData = $this->_arrCtxt['dataCol']['new'];
        }
        // Msi_Sys_Utils::debug( '@@@@@@@@@@@@@@ newData=>' . Msi_Sys_Utils::dump($newData) );

        $footer_height = 0; // 表示部の高さ
        $food_drint_etc_cap = []; // 表示キャプション文字列

        if ( strlen($food_gaisan['shohin_cd1']) > 0 ) { // 料理概算 商品コードが存在する
            $shohin_cd = $food_gaisan['shohin_cd1'];
            if ( array_key_exists($shohin_cd, $newData) ) { // 未登録データありの場合は上書き
                $recOw = $newData[$shohin_cd];
                $food_gaisan['quantity1'] = number_format($recOw['quantity']);
                $food_gaisan['hanbai_tnk1'] = number_format($recOw['price']);
                $food_gaisan['prc1'] = number_format($recOw['quantity'] * $recOw['price']);
                $food_gaisan['chargekbn1'] = $recOw['chargekbn'];
            }

            $footer_height += 35; // 料理概算分 高さ調整
            $food_drint_etc_cap[] = '飲物(概算)';
            $view->is_footer_drink  = true; // 料理概算 表示する
            $view->drink_shohin_cd	= $food_gaisan['shohin_cd1'];	// 料理概算 商品コード
            $view->drink_count		= $food_gaisan['quantity1'];	// 料理概算人数
            $view->drink_quantity	= $food_gaisan['hanbai_tnk1'];	// 料理概算単価
            $view->drink_prc		= $food_gaisan['prc1'];			// 料理概算金額
            $view->chargekbn1		= $food_gaisan['chargekbn1'];	// 料理概算奉仕料有無区分 
            $rec1 = $food_gaisan['_rec1'];
            $view->drink_shohin_nm       = $rec1['shohin_nm'];
            $view->drink_shohin_tkiyo_nm = $rec1['shohin_tkiyo_nm'];
            $view->drink_hanbai_tnk      = $rec1['hanbai_tnk'];
            $view->drink_dai_bunrui_cd  = $rec1['dai_bunrui_cd'];
            $view->drink_chu_bunrui_cd  = $rec1['chu_bunrui_cd'];
            $view->drink_shohin_kbn     = $rec1['shohin_kbn'];
            $view->drink_category_kbn   = $rec1['category_kbn'];
            $view->drink_zei_cd         = $rec1['zei_cd'];
            $view->drink_org_hanbai_tnk = $rec1['hanbai_tnk'];
            $view->drink_org_quantity   = $rec1['quantity'];
            $view->drink_org_chargekbn  = $rec1['chargekbn'];
        }

        if ( strlen($food_gaisan['shohin_cd2']) > 0 ) { // 席料 商品コードが存在する
            $shohin_cd = $food_gaisan['shohin_cd2'];
            if ( array_key_exists($shohin_cd, $newData) ) { // 未登録データありの場合は上書き
                $recOw = $newData[$shohin_cd];
                $food_gaisan['quantity2'] = number_format($recOw['quantity']);
                $food_gaisan['hanbai_tnk2'] = number_format($recOw['price']);
                $food_gaisan['prc2'] = number_format($recOw['quantity'] * $recOw['price']);
                $food_gaisan['chargekbn2'] = $recOw['chargekbn'];
            }

            $footer_height += 35; // 席料分 高さ調整
            $food_drint_etc_cap[] = '席料';
            $view->is_footer_charge  = true; // 席料 表示する
            $view->charge_shohin_cd	= $food_gaisan['shohin_cd2'];	// 席料 商品コード
            $view->charge_count		= $food_gaisan['quantity2'];	// 席料人数
            $view->charge_quantity	= $food_gaisan['hanbai_tnk2'];  // 席料単価
            $view->charge_prc		= $food_gaisan['prc2'];			// 席料金額
            $view->chargekbn2		= $food_gaisan['chargekbn2'];	// 席料奉仕料有無区分 
            $rec2 = $food_gaisan['_rec2'];
            $view->charge_shohin_nm       = $rec2['shohin_nm'];
            $view->charge_shohin_tkiyo_nm = $rec2['shohin_tkiyo_nm'];
            $view->charge_hanbai_tnk      = $rec2['hanbai_tnk'];
            $view->charge_dai_bunrui_cd  = $rec2['dai_bunrui_cd'];
            $view->charge_chu_bunrui_cd  = $rec2['chu_bunrui_cd'];
            $view->charge_shohin_kbn     = $rec2['shohin_kbn'];
            $view->charge_category_kbn   = $rec2['category_kbn'];
            $view->charge_org_hanbai_tnk = $rec2['hanbai_tnk'];
            $view->charge_org_quantity   = $rec2['quantity'];
            $view->charge_org_chargekbn  = $rec2['chargekbn'];
        }

        // アレルギー確認分
        $footer_height += 70; // 表示部の高さ  アレルギー確認分
        $food_drint_etc_cap[] = 'アレルギー確認';
        $view->allergy_memo	= $this->get_allergy_memo(); // アレルギー確認を取得
        $view->allergy_category_kbn = $this->getCategoryKbnRaw();

        $view->footer_height = $footer_height;
        $view->food_drint_etc_cap = implode($food_drint_etc_cap, '、');

        // 表示制御
        $lvlKey = $this->curCatLvl0() .'.'. $this->curCatLvl1() . '.tg1';
        $ckVal = static::getCookieForPref($lvlKey);

        // Msi_Sys_Utils::debug( '&^&^&^&& ckVal=>' . $ckVal );        
        // if ( strlen($ckVal) <= 0 || $ckVal ) {
        if ( strlen($ckVal) > 0 && $ckVal ) {
            $view->is_food_etc_added_show = true;
            $view->food_etc_added_class = 'st-open';
        } else {
            $view->is_food_etc_added_show = false;
            $view->food_etc_added_class = 'st-close';
        }

        return 'picker-footer-drink.tpl';
    }
    
	/**
     * 料理概算と席料を取得
     * org: Juchu_JuchuCustomerinfo::getFoodGaisanData()
     *
     * <AUTHOR>
     * @since 2015/01/29
     * @param $category_kbn
     * @return  array 商品情報
    */
	public function _getFoodGaisanData($category_kbn)
	{
		$db = Msi_Sys_DbManager::getMyDb();

		$shohincd1		= '';		//料理概算の商品コード
		$quantity1		= 0;		//人数
		$hanbai_tnk1	= 0;		//単価
		$prc1			= 0;		//金額
		$chargekbn1		= 0;		//奉仕料有無区分 
		$shohincd2		= '';		//席料の商品コード
		$quantity2		= 0;		//人数
		$hanbai_tnk1	= 0;		//単価
		$prc2			= 0;		//金額
		$chargekbn2		= 0;		//奉仕料有無区分 

		$kbn2	 = 1; // 1：飲物（概算）
        $legacyIteminfo = $this->getJuchuItemhjInfoAdapter();
        $legacyIteminfo->_juchuItemex_getItem_no_kbn1 = true; // kbn1 条件は使わない
        $kbn1 = null; // 気にしない
		$select  = $legacyIteminfo->getItem($category_kbn, $kbn1, $kbn2);
        $select_1 = null;
		if (count($select) > 0)
		{
			$shohincd1	 = $select[0]['shohin_cd'];
			$quantity1	 = (float)$select[0]['quantity'];
			$hanbai_tnk1 = (float)$select[0]['hanbai_tnk'];
			$chargekbn1	 = $select[0]['chargekbn'];
            $select_1 = $select[0];
		}

		// 席料の商品コードを取得
		$kbn2	 = 2; // 2：席料
		$select  = $legacyIteminfo->getItem($category_kbn, $kbn1, $kbn2);
        $select_2 = null;
		$hanbai_tnk2 = null;
		if (count($select) > 0)
		{
			$shohincd2	 = $select[0]['shohin_cd'];
			$quantity2	 = (float)$select[0]['quantity'];
			$hanbai_tnk2 = (float)$select[0]['hanbai_tnk'];
			$chargekbn2	 = $select[0]['chargekbn'];
            $select_2 = $select[0];
		}
		if ($quantity1 === null)
		{
			$quantity1 = 0;
		}	
		if ($hanbai_tnk1 === null)
		{
			$hanbai_tnk1 = 0;
		}
		$prc1 = $quantity1 * $hanbai_tnk1;
		if ($quantity2 === null)
		{
			$quantity2 = 0;
		}	
		if ($hanbai_tnk2 === null)
		{
			$hanbai_tnk2 = 0;
		}	
		$prc2 = $quantity2 * $hanbai_tnk2;

		$data = array(
			'shohin_cd1'	=> $shohincd1,
			'quantity1'		=> number_format($quantity1),
			'hanbai_tnk1'	=> number_format($hanbai_tnk1),
			'prc1'			=> number_format( $prc1),
			'chargekbn1'	=> $chargekbn1,
			'shohin_cd2'	=> $shohincd2,
			'quantity2'		=> number_format($quantity2),
			'hanbai_tnk2'	=> number_format($hanbai_tnk2),
			'prc2'			=> number_format($prc2),
			'chargekbn2'	=> $chargekbn2,
            '_rec1'         => $select_1,
            '_rec2'         => $select_2,
		);
	
        unset( $legacyIteminfo->_juchuItemex_getItem_no_kbn1 ); // kbn1 条件は使わないの解除

		return $data;
	}

    /**
     * 付加情報タブ tpl 設定
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function lvl2OptTab()
    {
        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            // return 'picker-lvl2-opt-drink.tpl';
            // $picker_footer_tpl として設定
        }

        return '';
    }

    /**
     * 付加情報タブ キャプション 設定
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     * @return  string
     */
    protected function lvl2OptTabCap()
    {
        $lvl0 = $this->curCatLvl0();
        if ( $lvl0 == static::CAT_LVL0_RYORI ) {
            // return '飲物(概算)・アレルギー確認';
        }

        return parent::lvl2OptTabCap();
    }

    /**
     * 商品選択サブ画面要素生成 Tpl 後処理
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     */
    protected function genItemPickerPostHook()
    {
        parent::genItemPickerPostHook();

        $lvl2 = $this->curCatLvl2();
        $lvl1 = $this->curCatLvl1();
        $lvl0 = $this->curCatLvl0();

        /* if ( $lvl0 == static::CAT_LVL0_SHOHIN ) { */
        /*     if ( $lvl1 == 6 ) { // 湯灌 */
        /*         // $this->_myYukanFunc(); */
        /*     } */
        /* } */
    }

    /**
     * 湯灌 用 特別処理
     *
     * <AUTHOR> Mihara
     * @since 2015/04/xx
     */
    protected function _myYukanFunc()
    {
        $renderer = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');
        $view = $renderer->view;

        $item_memo = $this->getWashMemo();

        // $view->item_list = $item_list;
        $view->categorykbn = $this->getCategoryKbn();

        $view->title = $item_memo[0]['disp_title'];
        $view->memo1 = $item_memo[0]['disp_memo1'];
        $view->memo2 = $item_memo[0]['disp_memo2'];
        $view->memo3 = $item_memo[0]['disp_memo3'];
        $view->memo4 = $item_memo[0]['disp_memo4'];
        $view->memo5 = $item_memo[0]['disp_memo5'];
        $view->memo6 = $item_memo[0]['disp_memo6'];
        $view->memo7 = $item_memo[0]['disp_memo7'];
        $view->memo8 = $item_memo[0]['disp_memo8'];
        $view->memo9 = $item_memo[0]['disp_memo9'];
        $view->memo10 = $item_memo[0]['disp_memo10'];
        $view->gazo_img1 = $item_memo[0]['gazo_img1'];
        $view->gazo_img2 = $item_memo[0]['gazo_img2'];
        $view->gazo_memo1 = $item_memo[0]['gazo_memo1'];
        $view->gazo_memo2 = $item_memo[0]['gazo_memo2'];

    }

	/**
     * 湯灌の説明文を取得
     *  cf. JuchuIteminfo.php
     *
     * <AUTHOR> Kayo
     * @since 2014/05/25
     * @return  array 商品情報
    */
	protected function getWashMemo()
    {
		$db = Msi_Sys_DbManager::getMyDb();

        // $moushikomi_kbn =	$this->_moushiKbn;		// 申込区分
        $moushikomi_kbn = $this->getMoushikomiKbn();

		$category_kbn	=	13; // //カテゴリー区分：湯灌 const CATEGORY_KBN_YUKAN = 13;
		$select = $db->easySelect( <<< END_OF_SQL
	SELECT *
	FROM category_web_memo m
WHERE delete_flg=0
	AND	moushikomi_kbn	= :moushikomi_kbn
	AND category_kbn	= :category_kbn			
	AND tekiyo_st_date <= CURRENT_DATE AND tekiyo_ed_date >= CURRENT_DATE
END_OF_SQL
		,array(
			'moushikomi_kbn' => $moushikomi_kbn,
			'category_kbn'	 => $category_kbn,
		));
		return	$select;
	}



}
