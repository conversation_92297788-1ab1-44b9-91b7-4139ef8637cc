<?php
  /**
   * PDF test
   *
   * @category   App
   * @package    controllers\Dev
   * <AUTHOR> Mihara
   * @since      2013/xx/xx
   * @filesource 
   */

// require_once APPLICATION_PATH . '/../library/composer_root/vendor/autoload.php';

class Dev_Pdf3_Test22Controller extends Zend_Controller_Action
{
    /**
     * アクション
     *
     * <AUTHOR> Mihara
     * @since 2013/xx/xx
     */
	public function test22Action()
    {
        $bk_img_file = 'file://' . APPLICATION_PATH . '/modules/dev/controllers/Pdf3/SekoDisplay_mod.jpg';
        // $bk_img_file = sprintf("%s/img/etc/SekoDisplay_mod.jpg", Msi_Sys_Utils::getBaseUrl());

        $aBun = array( mb_convert_encoding(pack("H*", "E001E002E003"), "UTF-8", "UTF-16"),
                       mb_convert_encoding(pack("H*", "9AD89AD95D0EFA11"), "UTF-8", "UTF-16"), // hashigo-taka, saki
                       mb_convert_encoding(pack("H*", "5409D842DFB7"), "UTF-8", "UTF-16"), // yoshi, tsuchi-yoshi. surrogate
                       mb_convert_encoding(pack("H*", "53F1D842DF9F"), "UTF-8", "UTF-16"), // shikaru. surrogate
                       'ABabＡＢ、ａｂ12１２。',
                       'あいう「アイウ」ｱｲｳ' );

        $staff1 = array( 'あいう　えお',
                         'かきく　けこ',
                         'さしす　せそ' );
        $staff2 = array( 'あいう　えお',
                         'かきく　けこ' );
        $staff3 = array( 'あいう　えお',
                         'かきく　けこ',
                         'さしす　せそ' );
        $staff4 = array( 'あいう　えお',
                         'かきく　けこ' );
        $staff4 = $aBun;

        $param = compact( Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
bk_img_file
staff1 staff2 staff3 staff4
END_OF_TXT
        ) );

        $tplFile = APPLICATION_PATH . '/modules/dev/controllers/Pdf3/test22-sample.tpl';

        // if ( true ) {
        //     $html = App_PdfViaHtmlUtils::genHtml( $tplFile, $param );
        //     $html = App_PdfViaHtmlUtils::gaijiPatchHtml( $html );
        //     Msi_Sys_Utils::outHtml($html);
        //     return;
        // }

        $pdf = App_PdfViaHtmlUtils::easyGenPdf( $tplFile, $param ); // , array('page-size'=>'B5') );

        if ( false ) { // pdf
            Msi_Sys_Utils::outBinary($pdf);
        }
        else { // jpg
            $tmpFile = Msi_Sys_Utils::tempnam() . '.pdf';
            if ( Msi_Sys_Utils::put_contents($tmpFile, $pdf) === false ) {
                throw new Exception("(55c92a86)file write error");
            }
            $tmpFile2 = Msi_Sys_Utils::tempnam() . '.jpg';

            // '/usr/bin/pdftocairo -jpeg', // '/usr/bin/convert -density 300', // MSI_CMD_PDFTK,
            $system_cmd = sprintf( "%s %s %s", '/usr/bin/convert -density 300',
                                   $tmpFile, $tmpFile2 );
            Msi_Sys_Utils::info( '======> ' . $system_cmd );
        
            // list($rtn_val, $output) = Msi_Sys_CmdMan::do_exec_p( $system_cmd, true );
            list($rtn_val, $output) = Msi_Sys_CmdMan::do_cmd_input( $system_cmd );
            if ( $rtn_val !== 0 ) {
                throw new Exception( '処理できません' );
            }

            $jpg = Msi_Sys_Utils::get_contents($tmpFile2);

            Msi_Sys_Utils::outAttachedFile($jpg, 'test22.jpg', 'image/jpeg');

            unlink($tmpFile);
            unlink($tmpFile2);
        }
	}

}
