/** 
 * @fileoverview 検品仕入確定
 * @version 2019/06/10 消費税軽減率対応 tosaka
 */
var apphh = apphh || {};
$(function () {
	"use strict";

	var utils = window.msiBbUtils;
	var search_flg;
	// 明細部
	var MeisaiModel = Backbone.Model.extend({
		defaults: function () {
			return {
				shohin_cd: null, //商品コード
				shohin_nm: null, //商品名称
				hachu_suryo: 0,
				nyuka_suryo: 0,
				hachu_tnk: 0,
				hachu_prc: 0,
				kepin_suryo: 0,
				henpin_suryo: 0,
				msi_biko2: null,
				siiretnk_input_kbn: 0,
				msi_no: null, //発注明細No
				nm_input_kbn: null,
				tnk_chg_kbn: null,
				line_no: -1,
				is_checked: 0,
				tanto_nm: null,
				zeikomi: 0,
				m_soko_cd: null,            //倉庫コード 2015/01/03 ADD Kayo
                                zei_cd: null,               // tosaka 軽減税率対応 keigen
                                reduced_tax_rate: 1,        // tosaka 軽減税率対応  軽減税率区分: 1：対象外 2：軽減8%
                                zei_kbn: -1                 // tosaka 軽減税率対応  税区分   -1: dummy
                                
			};
		},
		// 入力チェック
		validation: {
			hachu_suryo: [
				{
					maxLength: 8,
					msg: '発注数量は8桁までです。'
				},
				{
					pattern: 'number',
					msg: '発注数量に数値以外が入力されています。'
				}
			],
			nyuka_suryo: [
				{
					required: function () {
						return this.nyukaRequired();
					},
					msg: '入荷数量の入力に誤りがあります。'
				},
				{
					maxLength: 8,
					msg: '入荷数量は8桁までです。'
				},
				{
					pattern: 'number',
					msg: '入荷数量に数値以外が入力されています。'
				}
			],
			kepin_suryo: [
				{
					maxLength: 8,
					msg: '欠品数量は8桁までです。'
				},
				{
					pattern: 'number',
					msg: '欠品数量に数値以外が入力されています。'
				}
			],
			henpin_suryo: [
				{
					maxLength: 8,
					msg: '返品数量は8桁までです。'
				},
				{
					pattern: 'number',
					msg: '返品数量に数値以外が入力されています。'
				}
			],
			hachu_tnk: [
				{
					maxLength: 10,
					msg: '仕入単価は10桁までです。'
				},
				{
					pattern: 'signedfloat2',
					msg: '仕入単価に数値以外が入力されています。'
				}
			],
			hachu_prc: [
				{
					maxLength: 12,
					msg: '金額は12桁までです。'
				},
				{
					pattern: 'number',
					msg: '金額に数値以外が入力されています。'
				}
			],
			msi_biko2: {
				required: false,
				maxLength: 30,
				msg: '備考は30文字以内です。'
			},
            m_soko_cd: [
				{
					required: function () {
						return this.sokoRequired();
					},
					msg: '在庫品の場合、倉庫は必須です。'
				}
			],	
			kenpin_dt: [
				{
					required: function () {
						return this.dateRequired();
					},
					msg: '検品日は必須入力です。'
				},
				{
					pattern: 'ymd',
					msg: '検品日の入力に誤りがあります。'
				}
			],
			out_zei_hasu_prc: [
				{
					maxLength: 12,
					msg: '金額は12桁までです。'
				},
				{
					pattern: 'number',
					msg: '金額に数値以外が入力されています。'
				}
			]
		},
		dateRequired: function () {
			if (meisaiList.length > 0
					&& (this.get("is_checked") == "1")
					&& (this.get('kenpin_dt') === null
							|| this.get('kenpin_dt') == '')) {
				return true;
			} else {
				return false;
			}
		},
		nyukaRequired: function () {
			if (meisaiList.length > 0
					&& (this.get('nyuka_suryo') == '0')) {
				return true;
			} else {
				return false;
			}
		},
		sokoRequired: function () {
			if (meisaiList.length > 0) {
				if 	(this.get("is_checked") == "1") {
					if (this.get('zaiko_knri_kbn') == 1 || this.get('zaiko_knri_kbn') == 2) {	//1:在庫単価管理 2:在庫金額管理 3:在庫管理不要 4：預り在庫品
						return true;
					} else {
						return false;
					}
				} else {	
					return false;
				}	
			} else {
				return false;
			}
		},
		labels: {
			shohin_cd: '商品コード',
			shohin_nm: '商品名称',
			suryo: '数量',
			msi_biko: '明細備考'
		}
	}); // MeisaiModel

	var MeisaiCollection = Backbone.Collection.extend({
		model: MeisaiModel,
	}); // MeisaiCollection

	var meisaiList = new MeisaiCollection;

	// ヘッダ・フッタ部
	var AppModel = Backbone.Model.extend({
		defaults: function () {
			return {
				denpyo_no: null, // 伝票No                            
				seko_no: null,
				// 仕入先
				siire_cd: null, // 仕入先コード
				siire_nm: null, // 仕入先名
				siire_lnm: null,
				souke_nm: null,
				tanto_cd: null,
				tanto_nm: null,
				hachudate_s: null,
				hachudate_e: null,
                                data_kbn: null,
				daicho_no_eria:null,		//受付番号
			};
		},
		validation: {
		},
		labels: {
		}

	}); // AppModel

	var MeisaiView = Backbone.View.extend({
		tagName: "tbody",
		template: _.template($('#item-template').html()),
		events: {
			"change .nyuka_suryo, .hachu_tnk, .out_zei_hasu_prc": "cahgeNyuka",
			"change .henpin_suryo": "cahgeHenpin",
			"keypress .nyuka_suryo, .henpin_suryo, .out_zei_hasu_prc": "preventEimoji",
			'change .shiharai_kbn': 'cahgeshiharai',
			'click .lbl_stamp_check': 'calcprice_bef',
			"click .dlg_date": "setDatePicker",		// 2015/01/04 ADD Kayo
                        "click .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen tosaka
                        "keyup .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen tosaka
		},
        // 軽減税率対応 消費税選択 tosaka keigen
        showKeigenSel2: function(ev) {
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            if ( this.model.get('shohin_cd') ) { // 商品が指定されている場合だけ表示
                msiGlobalObj.keigenLib.f07ShowKeigenSel2.apply(this, [ev]);
            }
        },
        // 横アイコンクリック時ピッカー表示	2015/01/04 ADD Kayo
        setDatePicker: function(e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
		initialize: function () {
			Backbone.Validation.bind(this);

            this.listenTo(this.model, 'change:hachu_suryo change:hachu_prc change:nyuka_suryo change:zeikomi  change:out_zei_hasu_prc', function() {
                // 赤字表示
                utils.decoMinus( this, 'hachu_suryo hachu_prc nyuka_suryo zeikomi out_zei_hasu_prc' );
            });

            this.listenTo(this.model, 'change:zei_cd', this.calcprice);  // 2019/06/11 ADD keigen
		},
		render: function (e) {

			Backbone.Validation.bind(this, {
				valid: function (view, attr, selector) {
					var $el = view.$('.' + attr),
							$group = $el;

					$el.removeClass('my-error');
					$el.attr('title', '');
					$group.removeClass('has-error');
				},
				invalid: function (view, attr, error, selector) {
					var $el = view.$('.' + attr),
							$group = $el;

					$el.addClass('my-error');
					$el.attr('title', error);
					$group.addClass('has-error');
				}
			});

			if (this.template === null) {
				this.template = _.template($('#item-template').html());
			}
			this.$el.html(this.template(this.model.toJSON()));

			var currAttr = $(this.$el).find(".lbl_stamp_check").attr("for");
			var newAttr = currAttr + this.model.attributes.denpyo_no + '_' + this.model.attributes.msi_no;
			$(this.$el).find(".lbl_stamp_check").attr("for", newAttr);
			$(this.$el).find(".lbl_stamp_check").next().attr("id", newAttr);

			this.$(".kenpin_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));

//			$('.msi-picker', this.$el).each(msiLib2.msiPickerBinder);

			this.stickit();
            // 赤字表示
            utils.decoMinus( this, 'hachu_suryo hachu_prc nyuka_suryo zeikomi out_zei_hasu_prc' );

			var listModel = this.model;
			var kenpin_kbn = $('#kenpin_kbn').val();
			//「検品」クリック時
			$(this.$el).find('.lbl_stamp_check').on('click', function (e) {
				if (typeof $(e.currentTarget).attr("aria-pressed") == "undefined" || $(e.currentTarget).attr("aria-pressed") == "false") {
					$(e.currentTarget).next().val("1");
					listModel.set('is_checked', "1");
					$(e.currentTarget).addClass("ui-state-active");
					$(e.currentTarget).attr("aria-pressed", "true");
					//未検品画面の場合のみ処理が動くように
					if (kenpin_kbn == "1") {
						//if ((listModel.get('nonyu_dt_msi') == null) || (listModel.get('nonyu_dt_msi') == '')) {
							if ((listModel.get('juchu_sougi_ymd') == null) || (listModel.get('juchu_sougi_ymd') == '')) {
								//システム日付
								var date = new Date();
								var year = date.getFullYear();
								var month = date.getMonth() + 1;
								var day = date.getDate();
								if (month < 10) {
									month = '0' + month;
								}
								if (day < 10) {
									day = '0' + day;
								}
								var str = year + '/' + month + '/' + day;
								listModel.set('kenpin_dt', str);
								listModel.set('kenpin_dt2', str);
							} else {
								listModel.set('kenpin_dt', listModel.get('juchu_sougi_ymd'));
								listModel.set('kenpin_dt2', listModel.get('juchu_sougi_ymd'));
							}
						//} else {
						//	listModel.set('kenpin_dt', listModel.get('nonyu_dt_msi'));
						//	listModel.set('kenpin_dt2', listModel.get('nonyu_dt_msi'));
						//}

						var hachu_suryo = listModel.get('hachu_suryo');
						var hachu_tnk = listModel.get('hachu_tnk');
						var hachu_prc = String(Math.floor(hachu_suryo * hachu_tnk));

						//2016/10/10 DEL Kayo listModel.set('nyuka_suryo', hachu_suryo);
						listModel.set('hachu_prc', hachu_prc);
						//2016/10/10 DEL Kayo listModel.set('kepin_suryo', "0");
						//2016/10/10 DEL Kayo listModel.set('henpin_suryo', "0");
					} else {
						listModel.set('kenpin_dt', listModel.get('kenpin_dt2'));
					}
				}
				else
				{
					$(e.currentTarget).next().val("0");
					listModel.set('kenpin_dt', '');
					listModel.set('is_checked', "0");
					$(e.currentTarget).removeClass("ui-state-active");
					$(e.currentTarget).attr("aria-pressed", "false");
				}
			});
            // 仕入単価変更区分    
            if (this.model.get("siiretnk_input_kbn") == 0) {            // 2015/03/02 ADD Kayo
                this.$('.hachu_tnk').attr("disabled", "disabled");      // 2015/03/02 ADD Kayo
            }                                                           // 2015/03/02 ADD Kayo
			return this;
		},
		bindings: {
			'.denpyono': 'denpyo_no',
			'.hachusaki': 'siire_lnm',
			'.hachu_suryo': 'hachu_suryo',
			'.nyuka_suryo': 'nyuka_suryo',
			'.hachu_tnk': {
				observe: 'hachu_tnk',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},
			'.hachu_prc': {
				observe: 'hachu_prc',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},
			'.keppin_suryo': 'kepin_suryo',
			'.henpin_suryo': 'henpin_suryo',
			'.hachu_date': 'order_ymd',
			'.nohin_date': 'nonyu_dt_msi',
			'.kenpin_date': 'kenpin_dt',
			'.kenpin_date2': 'kenpin_dt2',
			'.biko_text': 'msi_biko1',
			'.shohin_cd': 'shohin_cd',
			'.shohin_nm': 'shohin_nm',
			'.is_checked': 'kenpin_flg',
			//   '.shiharai_kbn' : 'shiharai_kbn',
			'select.shiharai_kbn': {
				observe: 'shiharai_kbn',
				selectOptions: {
					collection: function () {
						if (typeof apphh.siire_kbn_arr != "undefined") {
							return apphh.siire_kbn_arr;
						}
						else {
							return false;
						}
					},
				}
			},
			'.tantonm': 'tanto_nm',
			'.bumonnm': 'bumon_snm',
			'.jodai_tnk': {
				observe: 'jyodai_tnk',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},
			'.moushinm': 'kbn_value_snm',
			'.soukenm': 'souke_nm',
			'.tekiyonm': 'shohin_tkiyo_nm',
			'.out_zei_hasu_prc': 'out_zei_hasu_prc',
			'.zeikomi': {
				observe: 'zeikomi',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
			},
			'select.kamoku_text': {
				observe: 'siire_kamoku_cd',
				selectOptions: {
					collection: function () {
						if (typeof apphh.kamoku_kbn_arr != "undefined") {
							return apphh.kamoku_kbn_arr;
						}
						else {
							return false;
						}
					},
				}
			},
			'.seikyuno': 'uri_den_no',
			'.seikyuusakinm': 'sekyu_nm',
//            '.m_soko_cd': {						//倉庫コード
//                observe: 'm_soko_cd',
//                events: ['change'],
//                getVal: utils.getValSel2,
//                update: utils.updateSel2
//            },
            'select.m_soko_cd': {
                    observe: 'm_soko_cd',
                    selectOptions: {
                            collection: function () {
                                if (typeof apphh.soko_arr != "undefined") {
                                        return apphh.soko_arr;
                                }
                                else {
                                        return false;
                                }
                            },        
                            defaultOption: {
                                label: '',
                                value: null
                            }
                    }
            },
            // 軽減税率対応 keigen Kayo
            '.zei_cd': {
                observe: 'zei_cd',
                onGet: function(val, options) {
                    msiGlobalObj.keigenLib.f07UpdKeigenZeiCd(val, this);
                    return val;
                },
                afterUpdate: function($el, val, options) {
                    msiGlobalObj.keigenLib.f07UpdKeigenZeiCd(val, this);
                },
            },
		},
		cahgeNyuka: function (e) {
			var ha_suryo = this.model.get('hachu_suryo');
			var ny_suryo = this.model.get('nyuka_suryo');
			var hachu_tnk = this.model.get('hachu_tnk');
			ny_suryo = ny_suryo.split(",").join("");
			hachu_tnk = hachu_tnk.split(",").join("");
			var hachu_prc = Math.floor(ny_suryo * hachu_tnk);

			var ke_suryo = 0;

			if (isNaN(parseInt(ny_suryo))) {
				ny_suryo = 0;
			}

			ke_suryo = parseInt(ha_suryo) - parseInt(ny_suryo);

			this.model.set({kepin_suryo: ke_suryo.toString(),
				nyuka_suryo: ny_suryo.toString(),
				hachu_prc: hachu_prc.toString()});

			this.calcprice();
		},
		cahgeHenpin: function (e) {
			var ha_suryo = this.model.get('hachu_suryo');
			var ny_suryo = this.model.get('nyuka_suryo');
			var he_suryo = this.model.get('henpin_suryo');
			var hachu_tnk = this.model.get('hachu_tnk');
			var new_suryo = 0;
			//「発注数量」と「入荷数量」が一致する場合のみ
			if (ha_suryo == ny_suryo) {
				if (isNaN(parseInt(he_suryo))) {
					he_suryo = 0;
				}

				new_suryo = parseInt(ny_suryo) - parseInt(he_suryo);
				var hachu_prc = Math.floor(new_suryo * hachu_tnk);
				this.model.set({henpin_suryo: he_suryo.toString(),
					nyuka_suryo: new_suryo.toString(),
					hachu_prc: hachu_prc.toString()});
				this.calcprice();
			} else
			{
				if (isNaN(parseInt(he_suryo))) {
					he_suryo = 0;
				}
				this.model.set({henpin_suryo: he_suryo.toString()});
			}

		},
		calcprice_bef: function (e) {
			if (typeof $(e.currentTarget).attr("aria-pressed") == "undefined" || $(e.currentTarget).attr("aria-pressed") == "false") {
				this.calcprice();
			} else {
			}
		},
		// 端数処理関数     2019/06/11 DEL Kayo keigen
		//     端数区分  0:切捨て 1:四捨五入 2:切上げ
//		_getZeiHasuFunc: function (zei_hasu_kbn) {
//			var hasu = 0
//			hasu = zei_hasu_kbn;
//
//			if (hasu == 1)
//				return Math.round;
//			else if (hasu == 2)
//				return Math.ceil;
//			else
//				return Math.floor;
//		},
        // 端数処理関数 マイナス金額対応版  2019/06/10 ADD tosaka keign
        //     端数区分  0:切捨て 1:四捨五入 2:切上げ
        _getZeiHasuFunc: function (zei_cd, prc) {
            var hasu = 0;
            var d = _.findWhere(_zeiMst, {id: "" + zei_cd});
            if (d) {
                hasu = d.zei_hasu_kbn
            }
            if (prc >= 0) {
                if (hasu == 1) {
                    return Math.round;
                } else if (hasu == 2) {
                    return Math.ceil;
                } else {
                    return Math.floor;
                }
            } else {
                if (hasu == 1) {
                    return Math.round;
                } else if (hasu == 2) {
                    return Math.floor;
                } else {
                    return Math.ceil;
                }
            }
        },
		calcprice: function (e) {
			var bbm = this.model;
			var zei_kbn;     //課税区分
			//var kijun_date;  //消費税計算基準日
			var hachu_price;
			zei_kbn = bbm.get('shiire_zei_kbn');
			hachu_price = bbm.get('hachu_prc');

			var sagaku = parseInt(bbm.get('out_zei_hasu_prc'));

			// var ritu = bbm.get('zei_rtu');                   // 2019/06/11 DEL tosaka keigen
			// var zei_hasu_kbn = bbm.get('zei_hasu_kbn');      // 2019/06/11 DEL tosaka keigen
                        var zei_cd =     bbm.get('zei_cd');                 // 2019/06/11 ADD tosaka keigen
                        var d = _.findWhere(_zeiMst, {id: "" + zei_cd});    // 2019/06/11 ADD tosaka keigen
                        var ritu = d.zei_rtu;                               // 2019/06/11 ADD tosaka keigen
                        bbm.set('reduced_tax_rate', d.reduced_tax_rate);    // 2019/06/11 ADD tosaka keigen
			var hasuFunc = this._getZeiHasuFunc(zei_cd, hachu_price);

			//課税区分が外税の場合のみ
			if (zei_kbn == 2) {
				var zeikomi = 0
				/*
				 if ((bbm.get('nonyu_dt_msi') == null) || (bbm.get('nonyu_dt_msi') == '')) {
				 //システム日付
				 var date = new Date();
				 var year = date.getFullYear();
				 var month = date.getMonth() + 1;
				 var day = date.getDate();
				 if (month < 10) {
				 month = '0' + month;
				 }
				 if (day < 10) {
				 day = '0' + day;
				 }
				 var str = year + '/' + month + '/' + day;
				 kijun_date = str;
				 } else {
				 kijun_date = bbm.get('nonyu_dt_msi');
				 }
				 */

				zeikomi = parseInt(hachu_price) + parseInt(hasuFunc(hachu_price * ritu / 100)) + parseInt(sagaku)
				bbm.set({zeikomi: zeikomi});
				/*
				 $.ajax({
				 url: $.msiJqlib.baseUrl() + '/saimu/Kenpinsiire/calcprice',
				 type: 'POST',
				 data: {
				 chk_data: kijun_date,
				 hachu_prc: hachu_price
				 },
				 success: function (respData) {
				 bbm.set({zeikomi: respData.zeikomi + sagaku});
				 }
				 });
				 */
			}else{
				zeikomi = parseInt(hachu_price);
				bbm.set({zeikomi: zeikomi});
                        }
		},
		cahgeshiharai: function (e) {
			var shiharaKbn = $(e.currentTarget).val();
			this.model.set({shiharai_kbn: shiharaKbn});
		},
		preventEimoji: function (e) {
			$.msiJqlib.preventEimoji(e);
		},
		commaOmit: function (val, options) {
			var num = val.replace(/,/g, '');
			return num;
		},
		commaAdd: function (val, options) {
			return $.msiJqlib.commaAdd(val);
		}
	}); // MeisaiView

	var AppView = Backbone.View.extend({
		el: document, // '#my-form-id', // for #hall_cd

		events: {
			"click #bumon_nm,.bumon_cd-ref": "bumonHelper",
			"click .denpyo_no-ref": "denpyoHelper",
			"click .seko_no-ref": "sekoHelper",
			"click #siire_cd,#siire_nm,.siire_cd-ref": "siireHelper",
			"click #tanto_nm,.tanto_cd-ref": "tantoHelper",
			"click #kamoku_nm,.kamoku_cd-ref": "kamokuHelper",
			"click #btn_save": "doSave",
			"click #btn_check": "doCheck",
			"change #kenpin_kbn": "changeJokyo",
			"click #btn_search_k": "doSearch",
			"click #btn_clear_k": "doClear"
		},
		// 発注伝票検索ダイアログ
		denpyoHelper: function () {
			var bbm = this.model;
			this.$el.msiPickHelper({
				action: 'saimu.hachudenpyo',
				onSelect: function (data) {
					bbm.set({'denpyo_no': data.code,
						'siire_cd': data.siire_cd,
						'siire_nm': data.siire_nm});
				},
				onClear: function () {
					bbm.set('denpyo_no', null);
				},
				hookSetData: function () {
					return {init_search: 0
						, no_cond: 0
						, no_kanri_info: 0	// 発注管理情報には存在しないレコード
						, no_link_siire: 0	// 発注伝票に仕入伝票が紐付いていないレコード
						, data_kbn: null
					};
				}
			});
		},
		// 仕入先 pickup
		siireHelper: function () {
			var bbm = this.model;
			this.$el.msiPickHelper({
				action: 'siire',
				onSelect: function (data) {
					// console.log( JSON.stringify(data) );
					bbm.set({'siire_cd': data.code,
						'siire_nm': data.name});
				},
				onClear: function () {
					bbm.set('siire_cd', null);
					bbm.set('siire_nm', null);
				},
				hookSetData: function () {
					return {init_search: 1
						, no_cond: 0
					};
				}
			});
		},
		//施行検索ダイアログ
		sekoHelper: function () {
			var bbm = this.model;
			msiLib2.celemonyDialog(
					function (data) {
						bbm.set({'seko_no': data.seko_no});
					});
		},
		//担当者
		tantoHelper: function () {
			var bbm = this.model;
			this.$el.msiPickHelper({
				action: 'tanto',
				onSelect: function (data) {
					bbm.set({'tanto_cd': data.code,
						'tanto_nm': data.name});
				},
				onClear: function () {
					bbm.set('tanto_cd', null);
					bbm.set('tanto_nm', null);
				},
				hookSetData: function () {
					return {init_search: 1,
						no_cond: 0,
					};
				}
			});
		},
		//部門選択
		bumonHelper: function () {
			var bbm = this.model;
			this.$el.msiPickHelper({
				action: 'bumon',
				onSelect: function (data) {
					var bumon_nm;
					bumon_nm = data.name;
					bumon_nm = bumon_nm.replace(/(^\s+)|(\s+$)/g, "");
					bumon_nm = bumon_nm.replace(/(^[\s　]+)|([\s　]+$)/g, "");
					bbm.set({'bumon_cd': data.code,
						'bumon_nm': bumon_nm});
				},
				onClear: function () {
					bbm.set('bumon_cd', null);
					bbm.set('bumon_nm', null);
				},
				hookSetData: function () {
					return {init_search: 1,
						no_cond: 0,
					};
				}
			});
		},
		//科目検索ダイアログ
		kamokuHelper: function () {
			var bbm = this.model;
			this.$el.msiPickHelper({
				action: 'kamoku',
				onSelect: function (data) {
					bbm.set({'kamoku_cd': data.code,
						'kamoku_nm': data.name});
				},
				onClear: function () {
					bbm.set({kamoku_cd: null, // 科目コード
						kamoku_nm: null, // 科目名
					});
				},
				hookSetData: function () {
					var param = {
						init_search: 0,
						no_cond: 0,
						disp_mode: 1   // null:入金 1:仕入
					}
					return param;
				}
			});
		},
		ckDate: function (datestr) {
			// 正規表現による書式チェック 
			if (!datestr.match(/^\d{4}\/\d{2}\/\d{2}$/)) {
				return false;
			}
			var vYear = datestr.substr(0, 4) - 0;
			var vMonth = datestr.substr(5, 2) - 1; // Javascriptは、0-11で表現 
			var vDay = datestr.substr(8, 2) - 0;
			// 月,日の妥当性チェック 
			if (vMonth >= 0 && vMonth <= 11 && vDay >= 1 && vDay <= 31) {
				var vDt = new Date(vYear, vMonth, vDay);
				if (isNaN(vDt)) {
					return false;
				} else if (vDt.getFullYear() == vYear && vDt.getMonth() == vMonth && vDt.getDate() == vDay) {
					return true;
				} else {
					return false;
				}
			} else {
				return false;
			}
		},
		doSearch: function () {

			var hachudate_s = $('#hachudate_s').val();
			var kenpin_kbn = $('#kenpin_kbn').val();	// 2015/01/04 ADD Kayo
			if (hachudate_s != '') {
				if (this.ckDate(hachudate_s) == false) {
					if (kenpin_kbn == 1) {	// 2015/01/04 ADD Kayo
						msiLib2.showErr("発注日（自）の入力内容に誤りがあります。");
					} else {
						msiLib2.showErr("検品日（自）の入力内容に誤りがあります。");
					}	
					return false;
				}
			}

			var hachudate_e = $('#hachudate_e').val();
			if (hachudate_e != '') {
				if (this.ckDate(hachudate_e) == false) {
					if (kenpin_kbn == 1) {	// 2015/01/04 ADD Kayo
						msiLib2.showErr("発注日（至）の入力内容に誤りがあります。");
					} else {
						msiLib2.showErr("検品日（至）の入力内容に誤りがあります。");
					}	
					return false;
				}
			}

			if ((hachudate_s != '') && (hachudate_e != '')) {
				if (hachudate_s > hachudate_e) {
					if (kenpin_kbn == 1) {	// 2015/01/04 ADD Kayo
						msiLib2.showErr("発注日の大小関係に誤りがあります。");
					} else {
						msiLib2.showErr("検品日の大小関係に誤りがあります。");
					}	
					return false;
				}
			}

			var souke_nm = $('#souke_nm').val();
			var bumon_cd = $('#bumon_cd').val();
			var denpyo_no = $('#denpyo_no').val();
			var seko_no = $('#seko_no').val();
			var siire_cd = $('#siire_cd').val();
			var tanto_cd = $('#tanto_cd').val();
			var seikyu_nm = $('#seikyu_nm').val();
			var kamoku_cd = $('#kamoku_cd').val();
			var data_kbn = $('#data_kbn').val();    // 2015/12/15 ADD Kayo
			var daicho_no_eria = $('#code_1').val();  

			var kenpin_kbn = $('#kenpin_kbn').val();
			$("#kenpin_kbn2").val(kenpin_kbn);

			var formJson = {
				hachudate_s: hachudate_s,
				hachudate_e: hachudate_e,
				souke_nm: souke_nm,
				bumon_cd: bumon_cd,
				denpyo_no: denpyo_no,
				seko_no: seko_no,
				siire_cd: siire_cd,
				tanto_cd: tanto_cd,
				seikyu_nm: seikyu_nm,
				kamoku_cd: kamoku_cd,
                                data_kbn: data_kbn,      // 2015/12/15 ADD Kayo
                                daicho_no_eria: daicho_no_eria,    
                
			};
			$.ajax({// url: location.href,
				data: {
					form_data: formJson,
					kenpin_kbn: kenpin_kbn,
					action: '表示'//,
							//  mode:'seko',                                                  
				},
				type: 'POST',
				dataType: 'json',
				success: function (mydata) {
					if (mydata.status == 'OK') {
						_resetData(mydata.dataApp, mydata.dataCol, mydata.zeimst);  // 2019/06/11 ADD tosaka keigen
						//   that.setSouke(mydata.dataApp, bbm);
					} else {
						msiLib2.showErr(mydata.msg);
					}
				}
			});
		},
		doClear: function () {
			/* 2015/01/03 DEL Kayo 計算ミスが多すぎるので
			 var date = new Date();
			 //月初
			 date.setDate(date.getDate() - 3);
			 var year  = date.getFullYear();
			 var month = date.getMonth() + 1;
			 var day = date.getDate();
			 if (month < 10) {
			 month = '0' + month;
			 }
			 if (day < 10) {
			 day = '0' + day;
			 }
			 var str1 = year + '/' + month + '/' + day;
			 
			 //月末
			 date = new Date();
			 var date2 = new Date(date.getFullYear(), date.getMonth(), date.getDate());
			 month = date2.getMonth() + 1;
			 day = date2.getDate();
			 if (month < 10) {
			 month = '0' + month;
			 }
			 if (day < 10) {
			 day = '0' + day;
			 }
			 var str2 = year + '/' + month + '/' + day;
			 
			 $("#hachudate_s").val(str1);
			 $("#hachudate_e").val(str2);
			 */
			$('#souke_nm').val("");
			$('#bumon_cd').val("");
			$('#bumon_nm').val("");
			$('#denpyo_no').val("");
			$('#seko_no').val("");
			$('#siire_cd').val("");
			$('#siire_nm').val("");
			$('#tanto_cd').val("");
			$('#tanto_nm').val("");
			$('#seikyu_nm').val("");
			$('#kamoku_cd').val("");
			$('#kamoku_nm').val("");
			//$('#kenpin_kbn').val("1");
			$('#data_kbn').val("");    // 2015/12/15 ADD Kayo

			var $table = this.$("#dtl-table-id");
			$table.find('tbody').remove();

			var bbm = this.model;
			bbm.set({'denpyo_no': "",
				'siire_cd': "",
				'siire_nm': "",
				'seko_no': "",
				'souke_nm': "",
				'tanto_cd': "",
				'tanto_nm': "",
				'bumon_cd': "",
				'bumon_nm': "",
				'seikyu_nm': "",
				'kamoku_cd': "",
				'kamoku_nm': "",
                'data_kbn': "" 
            });
		},
		setSouke: function (data, model) {
			$('#souke_nm').val(data[0]["souke_nm"]);
			model.set({'souke_nm': data[0]["souke_nm"]});
		},
		changeJokyo: function () {
			var bbm = this.model;
			var denpyo_no = bbm.get("denpyo_no");
			var siire_cd = bbm.get("siire_cd");
			var seko_no = bbm.get("seko_no");
			var kenpin_kbn = $('#kenpin_kbn').val();
			if (kenpin_kbn == 1) {
				$('.lbl_hachudate').text('発注日');
			} else {
				$('.lbl_hachudate').text('検品日');
			}

			var param_no = "";
			var modekbn = "";

			if (denpyo_no == null) {
			} else if (denpyo_no != "") {
				param_no = denpyo_no;
				modekbn = "denpyo";
			} else if (siire_cd != "") {
				param_no = siire_cd;
				modekbn = "siire";
			} else if (seko_no != "") {
				param_no = seko_no;
				modekbn = "seko";
			}
		},
		initialize: function () {

			Backbone.Validation.bind(this);
			this.listenTo(this.collection, 'change remove add', this.render);
			this.listenTo(this.collection, 'reset', this.resetCol);

			this.listenTo(Backbone, 'update.siire', this.updateSiire);

			this.render();
		},
		updateSiire: function (siireData) {
			// console.log( 'updatesiire called' + JSON.stringify(siireData) );
			var m = this.model;
			_.map(('siire_cd  siire_nm  siire_knm  siire_yubin_no  siire_addr1 ' +
					' siire_addr2  siire_tel  siire_fax  siire_biko1  siire_biko2 ' +
					'').split(/\s+/),
					function (k) {
						m.set(k, siireData[ k ]);
					});
			$('#siire_cd, #siire_nm').msiErrClear();
		},
		resetCol: function (collection, options) {
			var $table = this.$("#dtl-table-id");
			$table.find('tbody').remove();
			_.each(collection.models, function (m) {
				var v = new MeisaiView({model: m});
				$table.append(v.render().el);
			});
			var kenpin_kbn = $('#kenpin_kbn').val();
			$('.nyuka_suryo').attr("disabled", false);
			$('.henpin_suryo').attr("disabled", false);
			$('.biko_text').attr("disabled", false);
			//2015/03/02 Kayo DEL $('.hachu_tnk').attr("disabled", false);
			$('.out_zei_hasu_prc').attr("disabled", false);
			$('.tekiyonm').attr("disabled", false);
			$('.kamoku_text').attr("disabled", false);

			_.each(collection.models, function (m) {
//2015/03/02 Kayo DEL 				if (m.attributes.siiretnk_input_kbn == 0) {
//2015/03/02 Kayo DEL 					this.$('.hachu_tnk').attr("disabled", "disabled");
//2015/03/02 Kayo DEL 				}
				if (kenpin_kbn == 2) {
					m.attributes.is_checked = "1";
				}
			});

			//初期表示時のチェックあり
			if (kenpin_kbn == 2) {
				//_.each(collection.models, function (m) {
				//$('#i_chk' + m.attributes.denpyo_no + '_' + m.attributes.msi_no).click();
				$('.lbl_stamp_check').attr("aria-pressed", "true");
				$('.lbl_stamp_check').addClass("ui-state-active");
				//m.attributes.is_checked = "1";
				$('.nyuka_suryo').attr("disabled", "disabled");
				$('.henpin_suryo').attr("disabled", "disabled");
				$('.biko_text').attr("disabled", "disabled");
				$('.hachu_tnk').attr("disabled", "disabled");
				$('.kenpin_date').attr("disabled", "disabled");
				$('.shiharai_kbn').attr("disabled", "disabled");
				$('.out_zei_hasu_prc').attr("disabled", "disabled");
				$('.tekiyonm').attr("disabled", "disabled");
				$('.kamoku_text').attr("disabled", "disabled");
				//});
			}

			this.render();
		},
		createMeisai: function (e) {
			this.collection.add(new MeisaiModel);
		},
		render: function () {
			// console.log( 'AppView render called.' );
			//$("#order").find('.radio_set').buttonset();

			Backbone.Validation.bind(this, {
				valid: function (view, attr, selector) {
					var $el = view.$('#' + attr),
							$group = $el.closest('.input-group');
					$group = $group || $el;

					$el.removeClass('my-error');
					$el.attr('title', '');
					$group.removeClass('has-error');
				},
				invalid: function (view, attr, error, selector) {
					var $el = view.$('#' + attr),
							$group = $el.closest('.input-group');
					$group = $group || $el;

					$el.addClass('my-error');
					$el.attr('title', error);
					$group.addClass('has-error');
				}
			});

			if (search_flg != true) {
				/* 2015/01/03 DEL Kayo 計算ミスが多すぎるので
				 
				 var date = new Date();
				 //月初
				 date.setDate(date.getDate() - 3);
				 var year  = date.getFullYear();
				 var month = date.getMonth() + 1;
				 var day = date.getDate();
				 if (month < 10) {
				 month = '0' + month;
				 }
				 if (day < 10) {
				 day = '0' + day;
				 }
				 var str1 = year + '/' + month + '/' + day;
				 
				 //月末
				 date = new Date();
				 var date2 = new Date(date.getFullYear(), date.getMonth(), date.getDate());
				 month = date2.getMonth() + 1;
				 day = date2.getDate();
				 if (month < 10) {
				 month = '0' + month;
				 }
				 if (day < 10) {
				 day = '0' + day;
				 }
				 var str2 = year + '/' + month + '/' + day;
				 
				 this.$("#hachudate_s").val(str1);
				 this.$("#hachudate_e").val(str2);
				 */
			}
			search_flg = true;

			this.$("#hachudate_s").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
			this.$("#hachudate_e").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
			this.stickit();

			// スクロール調整
			this.scrollAdj();

			// select2 調整。XXX 
			this.select2Adj(); // patch

			return this;
		},
		// スクロールバー表示調整
		scrollAdj: function () {
			var $list = this.$('.items .list'),
					$header = this.$('.items .header'),
					sc_of,
					sc_w,
					hh;
			// console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
			if ($list[0].scrollHeight === $list[0].clientHeight) {
				sc_of = 'scroll'; // not 'hidden'. hide for 'auto' in Chrome.
				sc_w = '44.4%';
				$list.css("overflow-y", sc_of);
				//     $header.css("overflow-x", sc_of);
				$('#denpyo_biko1').css('width', sc_w);
				$('#denpyo_biko2').css('width', sc_w);
				$('#note_txt_x').css('width', sc_w);
			} else {
				sc_of = 'scroll';
				sc_w = '43.3%';
				hh = $header.height();
				$list.css("overflow-y", sc_of);
				//       $header.css("overflow-x", sc_of);
				// console.log( 'hh=>' + hh + ' height=>' + $header.height() );
				$header.height(hh); // for Chrome. XXX
				$('#denpyo_biko1').css('width', sc_w);
				$('#denpyo_biko2').css('width', sc_w);
				$('#note_txt_x').css('width', sc_w);
			}
		},
		// select2 調整
		select2Adj: function () {
			var bbv = this;
			_.each('kenpin_selected'.split(/\s+/),
					function (k) {
						var $el = bbv.$('#' + k);
						var $s2el = bbv.$('#s2id_' + k);
						if ($s2el.length) {
							$el.select2("val", bbv.model.get(k));
						}
					});
		},
		btnDisabled: function (elem) {
			// $(elem).hide();
			$(elem).attr("disabled", "disabled");
		},
		btnEnabled: function (elem) {
			// $(elem).show();
			$(elem).removeAttr("disabled");
		},
		isInputOk: function () {
			var aMsg = [], line;
			var result = this.model.validate();
			if (result) {
				_.each(result, function (v, k) {
					aMsg.push(v);
					// console.log( '*** err ' + k + ': ' + v );
				});
			}
			this.collection.each(function (m, i) {
				var resLine = m.validate();
				if (resLine) {
					line = i + 1;
					_.each(resLine, function (v, k) {
						aMsg.push('明細' + line + '行目 ' + v);
						console.log('*** err ' + '明細' + line + '行目 ' + k + ': ' + v);
					});
				}
			});

			// NG
			if (aMsg.length > 0) {
				msiLib2.showErr(aMsg.join(', '));
				return false;
			}

			// OK
			msiLib2.clearAlert();
			// console.log( 'valid OK' );
			return true;
		},
		doCheck: function (ev) {
			if (this.isInputOk()) {
				if (ev)
					ev.stopPropagation();
				msiLib2.showInfo('入力チェック OK');
			}
		},
		doSave: function (ev) {
			if (nodata == true) {
				return;
			}
			if (!this.isInputOk()) {
				return;
			}

			var dataAppJson = JSON.stringify(this.model.toJSON());
			var dataColJson = JSON.stringify(this.collection.toJSON());
			var kenpin_kbn = $('#kenpin_kbn2').val();
			this.exeCheck(dataAppJson, dataColJson, kenpin_kbn);
		},
		exeCheck: function (dataAppJson, dataColJson, kenpin_kbn) {
			var that = this;
			$.ajax({
				url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkgetujifixkenpin',
				data: {
					dataAppJson: dataAppJson,
					dataColJson: dataColJson,
					kenpin_kbn: kenpin_kbn
				},
				type: 'POST',
				success: function (mydata) {
					if (mydata.status === 'OK') {
						that.exeSave(dataAppJson, dataColJson, kenpin_kbn);
					} else if (mydata.status === 'INFO') {
						$.msiJqlib.setProgressing(false);
						if (!confirm(mydata.msg)) {
							return;
						}
						$.msiJqlib.setProgressing(true);
						that.exeSave(dataAppJson, dataColJson, kenpin_kbn);
					} else {
						$.msiJqlib.showWarn(mydata.msg);
					}
				}
			});
		},
		exeSave: function (dataAppJson, dataColJson, kenpin_kbn) {
			var $this = this;

			$.ajax({// url: location.href,
				data: {
					dataAppJson: dataAppJson,
					dataColJson: dataColJson,
					action: '保存',
					kenpin_kbn: kenpin_kbn,
				},
				type: 'POST',
				success: function (mydata) {
					if (mydata.status == 'OK') {
						msiLib2.showInfo(mydata.msg);
						$this.doSearch();
					} else {
						msiLib2.showErr(mydata.msg);
					}
					console.log('ajax res msg==>' + mydata.msg);
				}
			});
		},
		// 選択モード(更新モード)か否か
		isSelectedCtxt: function () { // aka. isUpdCtxt
			var denpyo_no = this.model.get('denpyo_no');
			if (denpyo_no) {
				return true;
			}
			return false;
		},
		// 初期状態から変更されているか
		isChanged: function () {
			if (!orgDataApp || !orgDataCol ||
					($.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) &&
							$.msiJqlib.isEqual(orgDataCol, this.collection.toJSON()))) {
				return false;
			}
			return true;
		},
		bindings: {
			'#denpyo_no': 'denpyo_no',
			'#siire_cd': 'siire_cd',
			'#siire_nm': 'siire_nm',
			'#seko_no': 'seko_no',
			'#souke_nm': 'souke_nm',
			'#tanto_cd': 'tanto_cd',
			'#tanto_nm': 'tanto_nm',
			'#bumon_cd': 'bumon_cd',
			'#bumon_nm': 'bumon_nm',
			'#seikyu_no': 'seikyu_nm',
			'#kamoku_cd': 'kamoku_cd',
			'#kamoku_nm': 'kamoku_nm',
            '#data_kbn': {							//伝区
                observe: 'data_kbn',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
		},
	}); // AppView

	var app;
	var orgDataApp, orgDataCol;
	var _resetData;
	var minDataApp, minDataCol, min2DataApp, min2DataCol;
	var _setInitData;
	//var shohinSearchCondData = {}; // 前回商品検索大域用
        var _zeiMst = [];   // 2019/06/10 ADD Kayo keigen   

	app = new AppView({model: new AppModel, collection: meisaiList});
	var nodata = false;
	_resetData = function (myApp, myCol, myZeimst) {
		if (myCol.length == 0) {
			msiLib2.showInfo("データが存在しません。");
			$("body").find('#btn_save').addClass('disable_btn');
			nodata = true;
		} else {
			$("body").find('#btn_save').removeClass('disable_btn');
			nodata = false;
		}

		$("body").find(".lbl_stamp_check").unbind("click");
		$("body").find(".mycheckBox").unbind("click");

		if (myCol.length > 0) {
			apphh.siire_kbn_arr = $.msiJqlib.objToArray(myCol[0].siire_kbn);
			apphh.kamoku_kbn_arr = $.msiJqlib.objToArray(myCol[0].kamoku_nm);
			apphh.soko_arr = $.msiJqlib.objToArray(myCol[0].soko);
			//console.log( apphh.delivery_kbn_arr);
		}
                _zeiMst = myZeimst.zei;    // 2019/06/10 ADD tosaka keigen

		app.model.set(myApp);
		app.collection.reset(myCol);
		orgDataApp = app.model.toJSON(); // not JSON, but Object
		orgDataCol = app.collection.toJSON(); // not JSON, but Object

		app.model.trigger('change'); // patch
	};

	_setInitData = function () {
		nodata = true;
		$("body").find("#kenpin_kbn2").val($("body").find("#kenpin_kbn").val());
		$("body").find('#btn_save').addClass('disable_btn');
	};

	// ページ遷移前の確認
	$(window).on('beforeunload', function () {
		if (app.isChanged()) {
			return "保存されていないデータがあります.";
		}
	});

	// リサイズ処理
	$(window).on('resize', function () {
		app.render();
	});

	// 参照専用
	if ($('#my-form-id').hasClass('my-ctxt-readonly')) {
		var $form = $('#my-form-id');
		$form.msiInputReadonly()
				.msiCalReadonly()
				.find('.my-readonly-hidden').hide();

		$form.find('.my-readonly-visible').show();

		utils.setReadOnlyCtxt();
	}

	// 付加的な disabled 付与
	$('#order').find('.label[data-ref-rel]').each(function () {
		// console.log( '.label[data-ref-rel]->' + $(this).attr('class') );
		var $this = $(this),
				ref = $this.attr('data-ref-rel'),
				$el = $(ref, $this.parent());
		// console.log( '.label[data-ref-rel]->' + $(this).attr('class') + ' ref=>' + $el.attr('class') + ' disabled=>' + $el.attr('disabled'));
		if ($el.attr('disabled')) { // || $el.attr('readonly') ) {
			$(this).addClass("my-disabled").attr("disabled", "disabled");
		} else {
			$(this).removeClass("my-disabled").removeAttr("disabled");
			;
		}
	});

	$.msiJqlib.initDone(function () {
		_setInitData();
	}); // 処理完了

	$('#order').fadeIn('fast'); // ちらつきのごまかし

});
