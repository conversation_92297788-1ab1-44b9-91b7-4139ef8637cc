(function(d){var g=!1,h=Backbone.Model.extend({defaults:function(){return{comp:"",usr:"",pass:"",save:0}},validation:{comp:{required:!0},usr:{required:!0},pass:{required:!0},save:{required:!0}},labels:{comp:"\u4f1a\u793e\u30b3\u30fc\u30c9",usr:"\u30ed\u30b0\u30a4\u30f3ID",pass:"\u30d1\u30b9\u30ef\u30fc\u30c9"}}),k=Backbone.View.extend({el:"#form-id",events:{"click #btn_login":"doUpdate","click #btn-clear":"doClear","dblclick #lbl_save_1":"dblSaveOn","dblclick #lbl_save_2":"dblSaveOff","keypress #login-input-form":"onEnter"},
initialize:function(){Backbone.Validation.bind(this,{valid:function(a,b,e){a=a.$("#"+b);b=(b=a.closest(".input-group"))||a;a.removeClass("my-error");a.attr("title","");b.removeClass("has-error")},invalid:function(a,b,e,c){a=a.$("#"+b);b=(b=a.closest(".input-group"))||a;a.addClass("my-error");a.attr("title",e);b.addClass("has-error")}});this.stickit()},_initFocus:function(){d("#"+_.find(["comp","usr","pass"],function(a){return""===d("#"+a).val()})).focus()},setInitData:function(){var a="",b="",e=0,
c=msiLib2.uiPrefCookie("fdn_lu"),d=msiLib2.uiPrefCookie("fdn_crt_lu");c&&(c=c.split(":"),2==c.length&&(a=c[0],b=c[1],e=1));d&&(c=d.split(":"),3==c.length&&(a=c[0],c[1].match(/^-ALL-$/)||(b=c[1]),g=c[2]&&"1"===c[2]));this.model.set("comp",a);this.model.set("usr",b);this.model.set("save",e);this.model.set("pass","");this._initFocus()},checkAutoLogin:function(){var a=this;g&&(g=!1,a.model.set("pass","dummyPass"),setTimeout(function(){a.doUpdate()},0))},isInputOk:function(){this.$el.addClass("my-ctxt-show-error");
var a=[],b=this.model.validate();b&&_.each(b,function(b,c){a.push(b)});if(0<a.length)return msiLib2.showLoginErrRes(),this._initFocus(),!1;msiLib2.clearAlert();return!0},doClear:function(a){msiLib2.clearAlert();d(".my-error").removeClass("my-error").attr("title","");d(".has-error").removeClass("has-error");this.setInitData()},dblSaveOn:function(a){this.setInitData()},dblSaveOff:function(a){this.model.set("comp","");this.model.set("usr","");this.model.set("pass","")},doUpdate:function(a){if(this.isInputOk()){this.$el.removeClass("my-ctxt-show-error");
a=JSON.stringify(this.model.toJSON());var b=this;d.ajax({url:msiLib2.baseUrl()+"/index/login2chk",data:a,type:"POST",contentType:"application/json",dataType:"json",success:function(a){"OK"==a.status?d("#login").fadeOut("fast",function(){location.href=msiLib2.baseUrl()+"/index/login2ok"}):(a.msgSpe?msiLib2.showWarn(a.msgSpe):a.msg&&msiLib2.showErr(a.msg),msiLib2.showLoginErrRes(),b.model.set("pass",""),b._initFocus())}})}},onEnter:function(a){13==a.keyCode&&this.doUpdate()},bindings:{"#comp":{observe:"comp",
onSet:function(a,b){return a=a.trim()}},"#usr":"usr","#pass":"pass",'input[name="save"]':"save"}}),f;d(function(){f=new k({model:new h});d.msiJqlib.initDone(function(){f.setInitData();f.checkAutoLogin()});d("#login").fadeIn("slow");setTimeout(function(){f._initFocus()},1200);if(-1!=window.navigator.userAgent.toLowerCase().indexOf("chrome")){var a=d("input");a.off("click.trick");sessionStorage.fixedChromeFocus||(sessionStorage.fixedChromeFocus="true",a.on("click.trick",function(){var b=window.open("/",
"_blank");setTimeout(function(){b.close()},1);a.off("click.trick")}))}})})(jQuery);
