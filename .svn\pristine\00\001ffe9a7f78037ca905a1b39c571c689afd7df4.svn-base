/* 
 * 請求書処理
 * 共通処理はlib.mitsu.seikyuに記述している
 */
var appmtsk = appmtsk || {};
$(function () {
    "use strict";

    /** 請求書の行区分：1=>ヘッダー行 */
    var ROW_KBN_H = '1';
    /** 請求書の行区分：3=>明細行 */
    var ROW_KBN_D = '3';
    /** 見積書または請求書の行区分：9=>合計行 */
    var ROW_KBN_G = '9';
    /** 申込区分：法事 */
    var MOUSHI_KBN_HOJI = '2';

    appmtsk = {
        o_price: '御請求金額',
        printURI: '/juchu/pdf0113',
        shoninURL: '/saiken/seikyusyo/shoninexe',
        shoninCancelURL: '/saiken/seikyusyo/shonintorikesi',
        viewKbn: '2', // 画面区分 1:見積書 2：請求書
        initializeView: function (dataApp) {
            $('#detail, #info').addClass("seikyu-layout");
            $('#detail .buttons').addClass("seikyu-button");
            if ($.msiJqlib.isNullEx(dataApp.seko_no)) {
                this.hideView();
                $.msiJqlib.showWarn("施行番号が選択されていません。");
                return false;
            }
            if ($.msiJqlib.isNullEx(dataApp.jichu_kakute_ymd)) {
                this.hideView();
                $.msiJqlib.showWarn("見積が確定されていないため、処理を続行することができません。");
                return false;
            }
            if (dataApp.is_konbini_hako_kbn) {
                $("#btn_print_konbini").show();
            }
            // 請求情報を設定する
            _setSeikyuView(dataApp.seikyu);

            // 権限によってボタン制御を行う
            if (dataApp.shonin_role_kbn == '1') {
                // 承認が全くない場合(葬儀のみの処理)
                if (dataApp.moushi_kbn != MOUSHI_KBN_HOJI) {
                    if (!dataApp.shonin.hasShonin) {
                        applms.setDisabledButtonStyle($(".buttons #btn_cancel"));
                        applms.setDisabledButtonStyle($(".buttons #btn_print"));
                        applms.setDisabledButtonStyle($(".buttons #btn_print_pre"));
                    } else {
                        applms.clearDisabledButtonStyle($(".buttons #btn_cancel"));
                        applms.clearDisabledButtonStyle($(".buttons #btn_print"));
                        applms.clearDisabledButtonStyle($(".buttons #btn_print_pre"));
                    }
                }
                // すべて承認された場合
                if (dataApp.shonin.hasShoninAll) {
                    applms.setDisabledButtonStyle($(".buttons #btn_shonin"));
                } else {
                    applms.clearDisabledButtonStyle($(".buttons #btn_shonin"));
                }
            } else {   
                applms.setDisabledButtonStyle($(".buttons #btn_shonin"));
                applms.setDisabledButtonStyle($(".buttons #btn_cancel"));
                // 承認が全くない場合(葬儀のみの処理)
                if (dataApp.moushi_kbn != MOUSHI_KBN_HOJI) {
                    if (!dataApp.shonin.hasShonin) {
                        applms.setDisabledButtonStyle($(".buttons #btn_print"));
                        applms.setDisabledButtonStyle($(".buttons #btn_print_pre"));
                    } else {
                        applms.clearDisabledButtonStyle($(".buttons #btn_print"));
                        applms.clearDisabledButtonStyle($(".buttons #btn_print_pre"));
                    }
                }
            }
            // 施行担当印鑑設定
            this.setInkanInfo($("#shonin-info #tanto"), dataApp.shonin.inkan_oid, dataApp.shonin.juchukakutei_dt);
            // 事務担当印鑑設定
            this.setInkanInfo($("#shonin-info #jimu"), dataApp.shonin.inkan_img1, dataApp.shonin.shonin_dt1);
            // 所属長印鑑設定
            this.setInkanInfo($("#shonin-info #shozoku"), dataApp.shonin.inkan_img2, dataApp.shonin.shonin_dt2);
            return true;
        },
        // エレメント非表示処理
        hideView: function () {
            $("#estimate-div-wrapper").hide();
            $(".buttons #btn_shonin").hide();
            $(".buttons #btn_print").hide();
            $(".buttons #btn_print_pre").hide();
            $(".buttons #btn_cancel").hide();
        },
        setInkanInfo: function ($target, inkanOid, shoninDt) {
            if ($.msiJqlib.isNullEx(inkanOid)) {
                $target.find("img").remove();
                $target.find("div").text("");
            } else if ($target.find("img").length === 0) {
                // 印鑑イメージ設定
                this.setInkanImg($target, inkanOid);
                // 承認日設定
                $target.find("div").text(shoninDt);
                $target.find("div,img").hide().delay(600).fadeIn(0);
            }
        },
        // 印鑑イメージ設定処理
        setInkanImg: function ($target, oid) {
            var imgURI = $.msiJqlib.baseUrl() + '/saiken/seikyusyo/img/imgid/' + oid;
            $target.prepend('<img src="' + imgURI + '"/>');
        },
        // MYTODO 入金行追加処理
        setNyukin: function (m) {
            var nyukin = 0;
            if (nyukin > 0) {
                m.set('title', '【Ｈ】御入金');
                m.set('sub', null);
                m.set('price', nyukin);
                m.set('row_kbn', ROW_KBN_D);
                applms.summaryCol.add(m);
                applms.prcSum -= nyukin;
            }
        },
        setAdditional: function (m, m2) {
            var bechu_prc = m.get('price');
            var kariuke_prc = m2.get('price');
            var cm = m.clone();
            var cm2 = m2.clone();
            // 葬儀施行金額
            m.set('title', '葬儀施行金額');
            m.set('sub', null);
            m.set('price', applms.prcSum);
            m.set('row_kbn', ROW_KBN_G);
            applms.summaryCol.add(m);

            // 供花・供物/単品金額
            applms.prcSum += Number(bechu_prc);
            cm.set('price', bechu_prc);
            cm.set('title', '供花・供物/単品未清算');
            cm.set('sub', null);
            cm.set('row_kbn', ROW_KBN_G);
            applms.summaryCol.add(cm);
            $("#estimate .all").css({'border-bottom': '1px solid #A9C5DC'});
            // 仮受商品金額
            applms.prcSum += Number(kariuke_prc);
            cm2.set('price', kariuke_prc);
            cm2.set('title', '仮受商品');
            cm2.set('sub', null);
            cm2.set('row_kbn', ROW_KBN_G);
            applms.summaryCol.add(cm2);
            $("#estimate .all").css({'border-bottom': '1px solid #A9C5DC'});

        },
        // 法事用
        setAdditionalh: function (m) {
            var kariuke_prc = m.get('price');
            var cm = m.clone();
            // 葬儀施行金額
            m.set('title', '葬儀施行金額');
            m.set('sub', null);
            m.set('price', applms.prcSum);
            m.set('row_kbn', ROW_KBN_G);
            applms.summaryCol.add(m);

            // 仮受商品金額
            applms.prcSum += Number(kariuke_prc);
            cm.set('price', kariuke_prc);
            cm.set('title', '仮受商品');
            cm.set('sub', null);
            cm.set('row_kbn', ROW_KBN_G);
            applms.summaryCol.add(cm);
            $("#estimate .all").css({'border-bottom': '1px solid #A9C5DC'});

        },
        // 承認処理
        doShonin: function () {
            if (!confirm('承認します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + appmtsk.shoninURL,
                data: {
                    dataAppJson: JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                        window.location.reload();
                    }
                }
            });
        },
        // 承認処理
        doCancel: function () {
            if (!confirm('承認を取り消します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + appmtsk.shoninCancelURL,
                data: {
                    dataAppJson: JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                        window.location.reload();
                    }
                }
            });
        },
        doPrint_konbini: function () {
//            if (!confirm("コンビニ請求書を発行します.\nよろしいですか？")) {
//                return;
//            }
            s_print.showPrinter({
                onSelect: function (pm) {
                    var uri_den_no_arr = applms.appModel.get("uri_den_no");
                    msiLib2.fileDlAjax({
                        url: $.msiJqlib.baseUrl() + '/saiken/seikyu3/konbinihakko',
                        data: {
                            printer_cd: pm.get("printer_cd"),
                            preview: 'off',
                            uri_den_no: uri_den_no_arr,
                            out_type: 1
                        }
                    });
                }
            });
        }
    };

    // 請求情報設定する
    var _setSeikyuView = function (dataSeikyu) {
        var m = new SeikyuModel();
        var v = new SeikyuView({model: m});
        v.model.set(dataSeikyu);
    };

    // 請求情報モデル
    var SeikyuModel = Backbone.Model.extend({
        defaults: function () {
            return {
                sekyu_nm: null, // 請求先名
                tel: null, // 請求先TEL
                addr1: null, // 請求先住所１
                addr2: null // 請求先住所２
            };
        }
    });
    // 請求情報ビュー
    var SeikyuView = Backbone.View.extend({
        el: $("#seikyu-info"),
        bindings: {
            '#sekyu_nm': 'sekyu_nm',
            '#tel': 'tel',
            '#addr1': 'addr1',
            '#addr2': 'addr2'
        },
        initialize: function () {
            this.render();
        },
        render: function () {
            this.stickit();
            return this;
        }
    });
    
});