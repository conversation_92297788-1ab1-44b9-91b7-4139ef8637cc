<?php
  /**
   * DataMapper_Saiken_RelicsRyosyushoHistory
   *
   * 領収書発行履歴（遺品回収） データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Kayo
   * @since      2020/06/17
   * @filesource 
   */

  /**
   * 領収書発行履歴（遺品回収） データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Kayo
   * @since      2020/06/17
   */
class DataMapper_Saiken_RelicsRyosyushoHistory extends DataMapper_Abstract
{
    /**
     * データ 取得
     *
     * <AUTHOR> Kayo
     * @since      2020/06/17
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.relics_uketuke_no, T.msi_no, T.hako_count ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi') AS hako_date_disp
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi') AS haki_date_disp
	   ,ryosyusho_no || '-' || ryosyusho_eda_no	AS ryosyusho_no_ceremo	-- 領収書番号	CEREMO
  FROM (
SELECT  m.*
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
FROM relics_ryosyusho_history m
LEFT JOIN tanto_mst t1
   ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
LEFT JOIN tanto_mst t2
   ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm 
        WHERE lm.login_cd = substr(m._mod_user, 10)
        AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
        AND t3.delete_flg=0
        AND m.haki_date IS NOT NULL
 WHERE m.delete_flg=0
    $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }

    /**
     * データ 取得
     *
     * <AUTHOR> Kayo
     * @since      2020/06/17
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findRyoshuNo($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.relics_uketuke_no, T.msi_no, T.hako_count ';
        }
        $dateWhere = ' WHERE relics_uketuke_no = '."'".$keyHash['relics_uketuke_no']."' AND msi_no = ".$keyHash['msi_no'];
     

        $select = $db->easySelect( <<< END_OF_SQL
  SELECT *
      FROM(
            SELECT *
            FROM   relics_ryosyusho_history  
            WHERE  hako_count = (SELECT MAX(hako_count) FROM relics_ryosyusho_history
            $dateWhere    
        )
    ) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }
}
