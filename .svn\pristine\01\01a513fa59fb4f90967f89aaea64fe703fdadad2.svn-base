#!/bin/env php
<?php
   /**
    *
    * 画像ファイル登録<br>
    * （施行プランマスタの画像２のみ）<br>
    * ※ ごんきやのカスタマイズリリース用<br>
    *
    * テーブル内のファイル名を元に画像ファイルを blob として登録します<br>
    * usage: reg_img_files.php -c kaisya_cd<br>
    *    ex. reg_img_files.php -c 01001000<br>
    *
    * @category   batch
    * @package    App
    * <AUTHOR> Okuyama
    * @since      2017/09/26
    * @version    2017/xx/xx xxxx  ｘｘｘｘ
    * @filesource
    */

require_once realpath(dirname(__FILE__) . '/../../library/Msi/batch_env.php');

$_cmd_name = '画像ファイル登録';

$_path_psql = Msi_Sys_Utils::getConfigByKeyDefault( 'msi.path.command.psql', 'psql' );
$_path_php  = Msi_Sys_Utils::getConfigByKeyDefault( 'msi.path.command.php', 'php' );


/**
 * 画面にメッセージを出力する
 *
 * <AUTHOR> Okuyama
 * @since      2017/09/26
 * @param      string   $msg   メッセージ
 * @return     void
 */
function _console( $msg ) {
    $msg = rtrim($msg) . "\n";
    print $msg;
}

/**
 * メッセージ出力を標準化する
 *
 * <AUTHOR> Okuyama
 * @since      2017/09/26
 * @param      string   $msg   メッセージ
 * @return     string
 */
function _msg( $msg ) {
    global $_cmd_name;
    $_msg = $_cmd_name . ': ' . $msg;
    return $_msg;
}

/**
 * エラー出力を標準化し、Exception を送出する
 *
 * <AUTHOR> Okuyama
 * @since      2017/09/26
 * @param      string   $msg   メッセージ
 * @return     void
 * @throws     Exception
 */
function _err( $msg ) {
    throw new Exception( _msg($msg) );
}

/**
 * コマンド説明を表示し終了する
 *
 * <AUTHOR> Okuyama
 * @since      2017/09/26
 * @param      int      $exitval  終了値
 * @param      string   $errMsg   エラーメッセージ
 * @return     void
 */
function _usage_exit( $exitval=1, $errMsg='' ) {
    _console( $errMsg );
    $mycmd = basename( __FILE__, '' );
    _console( <<< END_USAGE
テーブル内のファイル名を元に画像ファイルを blob として登録します
usage: $mycmd -c <kaisya_cd> [-d <data_dir>]
   ex. $mycmd -c 01001000 -d ../../data/initdata/01001000
   -c: 会社コード
   -d: ファイル格納フォルダ

END_USAGE
              );

    exit( $exitval );
}

$opts = getopt("c:d:h");
// var_dump($opts);

if ( isset($opts['h']) ) _usage_exit(0);

if ( ! isset($opts['c']) ) _usage_exit(1, '-c オプションは必須です');
$kaisya_cd = $opts['c'];

if ( ! isset($opts['d']) ) {
    $dirdata = APPLICATION_PATH . '/../data/initdata/' . $kaisya_cd;
} else {
    $dirdata = $opts['d'];
}
if ( ! is_dir($dirdata) ) {
    _usage_exit(1, "-d のディレクトリ($dirdata)が不正です");
}

try {
    $cmd_path = APPLICATION_PATH . '/../scripts/jobs';
    $data_path =  $dirdata . '/img_files';
    $reg_img_univ_cmd = "$_path_php {$cmd_path}/reg_img_univ.php -c $kaisya_cd -d $data_path";

    $cmd = <<< END_OF_TXT
$reg_img_univ_cmd -m '施行プラン' seko_plan_mst.gazo_img1_flie_nm.gazo_img1 seko_plan_mst.gazo_img2_flie_nm.gazo_img2
## $reg_img_univ_cmd -m '会場マスタ' kaijyo_mst.excel_file_nm.excel_img
END_OF_TXT;

    $cmd_input = '';
    // Msi_Sys_Utils::debug( 'cmd=>' . $cmd );

    list($rtn, $output, $errstr) = Msi_Sys_CmdMan::do_cmd_input_check_err( $cmd, $cmd_input, 'ERR' );

    // Msi_Sys_Utils::debug( 'rtn=>' . $rtn );
    // Msi_Sys_Utils::debug( 'output=>' . $output );
    // Msi_Sys_Utils::debug( 'err=>' . $errstr );

    if ( $rtn != 0 || strlen($errstr) > 0 ) {
        _err( $errstr );
    }

    Msi_Sys_Utils::info( _msg(sprintf('画像ファイル登録(会社コード:%s)を正常終了しました', $kaisya_cd)) );

}
catch ( Exception $e ){
    $msg = $e->getMessage();
    _console( $msg );
    Msi_Sys_Utils::err( $msg );
    exit( 1 );
}

exit(0);
