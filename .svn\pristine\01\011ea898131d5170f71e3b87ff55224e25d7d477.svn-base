<?php
/**
 * セレモニー(07121270) 施行ボード連携用データ(葬儀)
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2017/09/14
 * @verion     2017/10/03 住所表示(県名をトル、番地を表示しない)。ホール名を短縮形で表示
 * @filesource
 */

/**
 * セレモニー(07121270) 施行ボード連携用データ(葬儀)
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2017/09/14
 */
class Logic_Exdata_Send07121270SekoSogi
{
    /**
     * 出力CSVファイルの設定
     *
     * <AUTHOR> Mihara
     * @since      2017/09/19
     * @return     array
     */
    public static function getCsvOutRule($outEnc='UTF-8')
    {
        // 出力CSVファイルの設定
        $outRule = array(
                         'file_prefix'   => 'fdn_s', // 'Sekou', // '施行ボード連携' 
                         'isTitleOut'    => true, // タイトル行を設定するか
                         'outEnc'        => $outEnc, // 出力文字コード
                         'output_fields' =>  Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
施行番号       葬区            霊              火                  火案                バス
バス（人数）   地名            式場            葬家                祭壇
担当者         スタッフ        通夜            告別式              納棺
棺             移送            看              骨                  許
写             安              宿              礼状                会葬
備考           打合担当者ＩＤ  施行担当者ＩＤ  施行管轄ホール番号  施行管轄ホール名
式場ＩＤ       告別式日時      寺院名           菩提寺名            故人名
END_OF_TXT
           // 式場ＩＤ      施行番号
                         ),
                         // key_title_map: この対応情報があればタイトル項目名を置き換えます
                         // 対応がない場合は、タイトル項目名として項目キー名をそのまま使います
                         'key_title_map' => array(),
        );

        return $outRule;
    }

    /**
     * データを取得する
     *
     * <AUTHOR> Mihara
     * @since      2017/09/14
     * @param      Msi_Sys_Db   $db
     * @param      string       $seko_no
     * @return     mixed        array(array())(OK)|string(err msg)|false(出力しない)
     */
    public static function getData($db, $seko_no)
    {
        $obj = new static($db, $seko_no);
        $data = $obj->_getDataOne();
        if ( $data === false ) {
            return false;
        }
        if ( is_string($data) ) {
            return $data;
        }
        $arrData = array( $data );
        return $arrData;
    }

    /**
     * @ignore
     */
    protected $_db = null;
    protected $_seko_no = null;

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2017/09/14
     */
    protected function __construct($db, $seko_no)
    {
        $this->_db = $db;
        $this->_seko_no = $seko_no;
    }

    /**
     * データを１件取得する
     *
     * <AUTHOR> Mihara
     * @since      2017/09/14
     * @return     mixed        array(array())(OK)|string(err msg)
     */
    protected function _getDataOne()
    {
        $db = $this->_db;
        $seko_no = $this->_seko_no;

        //        $niteiTuyaRec = $this->getNitei(4); // 通夜
        //        $niteiKokuRec = $this->getNitei(11); // 告別式
        //        // if ( ($niteiTuyaRec === null || !isset($niteiTuyaRec['nitei_ymd'])) &&
        //        //      ($niteiKokuRec === null || !isset($niteiKokuRec['nitei_ymd'])) ) {
        //        if ( $niteiTuyaRec === null && $niteiKokuRec === null ) {
        //            return false; // 日程が決まっていないなら出力対象としない
        //        }

        $dataRtn = array(); // 返却するデータ
        $dataWk  = array(); // 作業用
        try {
            // 施行基本データ
            $sekoKihon = $this->getSekoKihon();
            if ( $sekoKihon === null ) {
                throw new Exception("施行基本情報(施行No:$seko_no)が存在しません");
            }

            // Msi_Sys_Utils::debug( "#### sekoKihon => " . Msi_Sys_Utils::dump($sekoKihon) );

            $dataWk = array_merge($dataWk, $sekoKihon);

            // 葬区  葬儀区分
            // （セレモニーの葬儀）
            // １．施行データの場合は”施行”とする
            // ２．１で”単発施行”が選択されている場合は”単発”とする
            // ３．法事データの場合は”法事”とする
            // （家族葬の葬儀）
            // １．施行区分が「家族葬」の場合は”家族葬”とする
            // ２．施行区分が「イオンの葬儀」の場合は”イオン”とする
            // ３．施行区分が「その他」の場合は”家族葬（その他）”とする
            // ４．可能であれば、家族葬、イオンのプラン名を付加する
            // 　　例：イオン（家族葬）、家族葬（火葬式セットプラン） など
            // $kbn = $dataWk['申込区分名'];
            // if ( strlen($dataWk['葬儀区分名']) > 0 ) {
            //     $kbn .= '／' . $dataWk['葬儀区分名'];
            // }
            // if ( strlen($dataWk['施行区分名']) > 0 ) {
            //     $kbn .= '／' . $dataWk['施行区分名'];
            // }
            // if ( strlen($dataWk['プラン名']) > 0 ) {
            //     $kbn .= '／' . $dataWk['プラン名'];
            // }
            if ( $dataWk['kaisya_cd'] == '07121270' ) { // セレモニー
                $kbn = $dataWk['申込区分名'];
                if ( $dataWk['施行区分名'] == '一般' && $dataWk['seko_kbn_yippan'] == 2 ) { // 単発施行
                    $kbn = '単発';
                } else if ( $dataWk['施行区分名'] == '福祉(生保)' ) { // 福祉(生保)施行
                    $kbn = '福祉(生保)';
                }
            } else if ( $dataWk['kaisya_cd'] == '07121271' ) { // 家族葬
                $kbn = $dataWk['申込区分名'];
                if ( $dataWk['kaiin_cd'] == 60 ) { // $dataWk['施行区分名'] == '家族葬' )
                    $kbn = '家族葬';
                } else if ( $dataWk['kaiin_cd'] == 70 ) { // $dataWk['施行区分名'] == 'イオンのお葬式'
                    $kbn = 'イオン';
                } else if ( $dataWk['kaiin_cd'] == 99 ) { // $dataWk['施行区分名'] == 'その他'
                    $kbn = '家族葬（その他）';
                }
                if ( strlen($dataWk['プラン名']) > 0 && preg_match('/^(家族葬|イオン)$/u', $kbn) ) {
                    $planNm = preg_replace('/（イオン）/u', '', $dataWk['プラン名']);
                    $kbn = sprintf("%s（%s）", $kbn, $planNm);
                }
            } else { // never
                $kbn = $dataWk['申込区分名'];
            }
            $dataRtn['葬区'] = $kbn;

            // 霊  霊柩車
            $dataRtn['霊'] = null; // '-霊-'; // null; // 手入力

            // 火  火葬場
            $niteiKaso = null;
            $niteiKasoRec = $this->getNitei(6); // 火葬
            if ( $niteiKasoRec !== null ) {
                $niteiKaso = sprintf("%s %s 炉前（%s）", $niteiKasoRec['nitei_time'], $niteiKasoRec['basho_nm'],
                                     $niteiKasoRec['kaso_kyo'] ? '有' : '無'); 
            }
            $dataRtn['火'] = $niteiKaso;

            // 火案  火葬場案内のスタッフ
            $dataRtn['火案'] = null; // '-火案-'; // null;

            // バス  マイクロバスの運行者、通夜、告別の台数
            // XXX ex. （自社Or他社、通夜・告別、台数、乗車人数）
            /*
 0780       | リムジン
 0790       | クラウン・セルシオ・寝台車
 0800       | 霊柩車
 0803       | マイクロバス（通夜）
 0805       | マイクロバス（告別式）
 0810       | マイクロバス（通常）
 0820       | マイクロバス（A）
 0830       | マイクロバス（Ｂ）
 0833       | タクシー（通夜）
 0835       | タクシー（告別式）
            */
            $buses = null;
            /*
            $shohinRec = $this->getJuchuMsiByKbn('0803,0805,0810,0820,0830'); // XXX
            // Msi_Sys_Utils::debug( "マイクロバ => " . Msi_Sys_Utils::dump($shohinRec) );
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('／', array_map(function($v){return $v['shohin_nm'];}, $shohinRec));
                $buses = $_shohinNm;
            }
            */
            $shohinRec = $this->getHachuInfoByKbn('0803,0805,0810,0820,0830');
            // Msi_Sys_Utils::debug( "マイクロバス => " . Msi_Sys_Utils::dump($shohinRec) );
            $dispRecs = array();
            // ex. 葵観光 通x2台、告x1台
            if ( count($shohinRec) > 0 ) {
                $recBuses = array();
                foreach ( $shohinRec as $rec00 ) {
                    $siire_nm = $rec00['仕入先名'];
                    $shohin_nm = $rec00['shohin_nm'];
                    $hachu_suryo = +$rec00['hachu_suryo'];
                    if ( strlen($siire_nm) <= 0 || strlen($shohin_nm) <= 0 || $hachu_suryo <= 0 ) continue;
                    if ( !array_key_exists($siire_nm, $recBuses) ) {
                        $recBuses[$siire_nm] = array( '通'=>0, '告'=>0, '市民葬'=>0, 'ほか'=>0, 'total'=>0 );
                    }
                    if ( preg_match('/(火葬場|告別式)/u', $shohin_nm) ) {
                        $recBuses[$siire_nm]['告'] += $hachu_suryo;
                        $recBuses[$siire_nm]['total'] += $hachu_suryo;
                    } else if ( preg_match('/通夜/u', $shohin_nm) ) {
                        $recBuses[$siire_nm]['通'] += $hachu_suryo;
                        $recBuses[$siire_nm]['total'] += $hachu_suryo;
                    } else if ( preg_match('/市民葬/u', $shohin_nm) ) {
                        $recBuses[$siire_nm]['市民葬'] += $hachu_suryo;
                        $recBuses[$siire_nm]['total'] += $hachu_suryo;
                    } else {
                        $recBuses[$siire_nm]['ほか'] += $hachu_suryo;
                    }
                }
                // Msi_Sys_Utils::debug( "recBuses=> " . Msi_Sys_Utils::dump($recBuses) );
                foreach ( $recBuses as $cap => $rec111 ) {
                    // if ( $rec111['total'] == 0 ) continue;
                    $arrDisp2 = array();
                    foreach ( $rec111 as $k => $v ) {
                        if ( preg_match('/(通|告|市民葬)/u', $k) ) {
                            if ( $v > 0 ) {
                                $arrDisp2[] = sprintf("%sx%d台", $k, $v);
                            }
                        }
                    }
                    if ( count($arrDisp2) > 0 ) {
                        $dispRecs[] = sprintf("%s %s", $cap, implode('、', $arrDisp2));
                    }
                }
                $_shohinNm = implode('／', $dispRecs);
                $buses = $_shohinNm;
            }
            $dataRtn['バス'] = $buses; // '-バス-'; // null; // 

            // バス（人数）
            $dataRtn['バス（人数）'] = null; // '-バス（人数）-'; // null; // 

            // 地名  故人住居地名
            $addr = $sekoKihon['故人住所1'];
            // if ( strlen($sekoKihon['故人住所2']) > 0 ) {
            //     $addr .= $sekoKihon['故人住所2'];
            // }
            $addr = App_Utils2::addrCutter($addr); // 住所の前後をカット
            $dataRtn['地名'] = $addr;

            // 式場
            $niteiTuyaRec = $this->getNitei(4); // 通夜
            $niteiKokuRec = $this->getNitei(11); // 告別式
            $sikijyo = null;
            if ( $niteiTuyaRec && strlen($niteiTuyaRec['basho_nm']) > 0 ) {
                $sikijyo = $niteiTuyaRec['basho_nm'];
            }
            if ( $niteiKokuRec && strlen($niteiKokuRec['basho_nm']) > 0 ) {
                $kokuBashoNm = $niteiKokuRec['basho_nm'];
                if ( strlen($kokuBashoNm) > 0 && $sikijyo != $kokuBashoNm ) {
                    if ( strlen($sikijyo) > 0 ) {
                        $sikijyo .= '／';
                    }
                    $sikijyo .= $kokuBashoNm;
                }
            }
            $dataRtn['式場'] = App_Utils2::hallnmShrinker($sikijyo);

            // 葬家
            $souke = null;
            if ( strlen($dataWk['葬家名']) > 0 ) {
                $_soukeNm = $dataWk['葬家名'];
                $souke = sprintf("%s家", $_soukeNm);

            }
            @ list($_moshuNm, $firstNm) = preg_split('/[ 　]/u', $dataWk['喪主名'], 2);
            if ( strlen($_moshuNm) > 0 && // $_soukeNm != $_moshuNm   // 完全一致
                 !Msi_Sys_Utils::isBeginsWithOrMatch($_moshuNm, $_soukeNm) ) { // 前方一致
                $souke = sprintf("%s（%s）", $souke, $_moshuNm);
            }
            $dataRtn['葬家'] = $souke;

            // 祭壇
            $saidan = null;
            $shohinRec = $this->getJuchuMsi('0010'); // 0010(祭壇)
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('／', array_map(function($v){return $v['shohin_nm'];}, $shohinRec));
                $saidan = $_shohinNm;
            }
            $dataRtn['祭壇'] = $saidan;

            // 担当者
            $tanto = null;
            $_uchiTanto = preg_replace('/[ 　]/u', '', $dataWk['打合担当者名']); // 必須項目
            $_sekoTanto = preg_replace('/[ 　]/u', '', $dataWk['施行担当者名']);
            if ( strlen($_sekoTanto) > 0 ) {
                if ( $_uchiTanto != $_sekoTanto ) {
                    $tanto = sprintf("（打）%s（施）%s", $_uchiTanto, $_sekoTanto);
                }
            }
            if ( $tanto === null ) {
                $tanto = $_uchiTanto;
            }
            $dataRtn['担当者'] = $tanto;

            // スタッフ
            $dataRtn['スタッフ'] = null; // '-スタッフ-'; // null;

            // 通夜
            $tuya = null;
            $niteiTuyaRec = $this->getNitei(4); // 通夜
            if ( $niteiTuyaRec ) {
                $_tuya_bgn = preg_replace('/^20\\d{2}[-\/]/', '', $niteiTuyaRec['nitei_ymd']);
                if ( strlen($_tuya_bgn) > 0 ) {
                    $tuya = sprintf("%s～", $_tuya_bgn);
                }
                $_tuya_end = $niteiTuyaRec['nitei_ed_time'];
                if ( strlen($_tuya_end) > 0 ) {
                    $tuya .= $_tuya_end;
                }
            }
            $dataRtn['通夜'] = $tuya;

            // 告別式
            $koku = null;
            $niteiKokuRec = $this->getNitei(11); // 告別式
            if ( $niteiKokuRec ) {
                $_koku_bgn = preg_replace('/^20\\d{2}[-\/]/', '', $niteiKokuRec['nitei_ymd']);
                if ( strlen($_koku_bgn) > 0 ) {
                    $koku = sprintf("%s～", $_koku_bgn);
                }
                $_koku_end = $niteiKokuRec['nitei_ed_time'];
                if ( strlen($_koku_end) > 0 ) {
                    $koku .= $_koku_end;
                }
            }
            $dataRtn['告別式'] = $koku;

            // 納棺
            $niteiNoukanRec = $this->getNitei(3); // 納棺
            $noukan = null;
            if ( $niteiNoukanRec ) {
                $_noukan_ts = preg_replace('/^20\\d{2}[-\/]/', '', $niteiNoukanRec['nitei_ymd']);
                if ( strlen($_noukan_ts) > 0 ) {
                    $noukan = $_noukan_ts;
                }
                if ( strlen($niteiNoukanRec['basho_nm']) > 0 ) {
                    $noukan .= ' ' . $niteiNoukanRec['basho_nm'];
                }
            }
            $shohinRec = $this->getJuchuMsi('0310'); // 0310(湯灌式・納棺式)
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('／', array_map(function($v){return $v['shohin_nm'];}, $shohinRec));
                // $_shohinNm = preg_replace('/[ 　]/u', '', $_shohinNm);
                $noukan .= ' ' . $_shohinNm;
            }
            $dataRtn['納棺'] = App_Utils2::hallnmShrinker($noukan);

            // 棺
            $hitugi = null;
            $shohinRec = $this->getJuchuMsi('0030'); // 0030(棺)
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('／', array_map(function($v){return $v['shohin_nm'];}, $shohinRec));
                $hitugi = $_shohinNm;
            }
            $dataRtn['棺'] = $hitugi;

            // 移送
            $dataRtn['移送'] = null; // '-移送-'; // null;

            // 看  看板の種類
            $kanban = null;
            $shohinRec = $this->getJuchuMsi('0250,0260'); // 0250(看板（ホール・外）), 0260(案内看板)
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('／', array_map(function($v){return $v['shohin_nm'];}, $shohinRec));
                $kanban = $_shohinNm;
            }
            $dataRtn['看'] = $kanban;

            // 骨  骨壷、骨覆い
            $hone = null;
            $shohinRec = $this->getJuchuMsi('0050'); // 0050(骨壷)
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('、', array_map(function($v){return $v['shohin_nm'];}, $shohinRec)); // 、delimiter?
                $hone = $_shohinNm;
            }
            $dataRtn['骨'] = $hone;

            // 許  許可証提出場所
            $dataRtn['許'] = null; // '-許-'; // null;

            // 写  遺影写真種類
            $sha = null;
            // 0060(写真額付き・セット), 0070(四切写真), 0080(キャビネ), 0090(造花額), 0100(写真飾額)
            $shohinRec = $this->getJuchuMsi('0060,0070,0080,0100');
            if ( count($shohinRec) > 0 ) {
                $_shohinNm = implode('+', array_map(function($v){return $v['shohin_nm'];}, $shohinRec)); // + delimiter?
                $sha = $_shohinNm;
            }
            $dataRtn['写'] = $sha;

            // 安  安置場所
            $anchi = null;
            if ( strlen($dataWk['安置先名']) > 0 ) {
                $anchi = $dataWk['安置先名'];
            }
            if ( $anchi === null && strlen($dataWk['安置先区分名']) > 0 ) {
                $anchi = $dataWk['安置先区分名'];
            }
            $dataRtn['安'] = App_Utils2::hallnmShrinker($anchi);
            

            // 宿  ホールまたは外現場の宿泊施設の利用有無を表示
            $shukuhaku = null;
            $shohinRec = $this->getJuchuMsi('0360'); // 0360(寝具)
            if ( count($shohinRec) > 0 ) {
                $shohinRec000 = array();
                $_singuHall = array_filter($shohinRec,
                                           function($v){return preg_match('/寝具.*（ホール）.*一式/u', $v['shohin_nm']);});
                if ( count($_singuHall) > 0 ) {
                    $shohinRec000[] = '有（ホール）';
                }
                $_singuGai = array_filter($shohinRec,
                                           function($v){return preg_match('/寝具.*（外現場）.*一式/u', $v['shohin_nm']);});
                if ( count($_singuGai) > 0 ) {
                    $shohinRec000[] = '有（外現場）';
                }
                $_shohinNm = implode('／', $shohinRec000);
                if ( $_shohinNm ) {
                    $shukuhaku = $_shohinNm;
                }
            }
            $dataRtn['宿'] = $shukuhaku; // '-宿-'; // null;

            // 礼状  礼状枚数
            $reijyo = null;
            $shohinRec = $this->getJuchuMsi('0120,0121'); // 0120(会葬礼状),0121(会葬礼状二つ折) cf. 0130(清め塩)
            $reijyoMaisu = 0;
            if ( count($shohinRec) > 0 ) {
                foreach ( $shohinRec as $rec00 ) {
                    $shohin_nm = preg_replace('/\*.*/u', '', $rec00['shohin_nm']);
                    $maisu = 0;
                    if ( preg_match('/(\\d+)\\s*枚/u', $shohin_nm, $m) ) {
                        $maisu = +$m[1];
                    }
                    $juchu_suryo = +$rec00['juchu_suryo'];
                    // Msi_Sys_Utils::debug( "#### @@@ 礼状  礼状枚数=> " . join(",", [$shohin_nm, $maisu, $juchu_suryo]) );
                    if ( $maisu > 0 && $juchu_suryo > 0 ) {
                        $reijyoMaisu += $maisu*$juchu_suryo;
                    }
                }
            }
            if ( $reijyoMaisu > 0 ) {
                $reijyo = sprintf("%d枚", $reijyoMaisu);
            }
            $dataRtn['礼状'] = $reijyo; // '-礼状-'; // null;

            // 会葬  会葬者数
            $dataRtn['会葬'] = null; // '-会葬-'; // null;

            // 備考
            $dataRtn['備考'] = null; // '-備考-'; // null;

            // 打合担当者ＩＤ
            $dataRtn['打合担当者ＩＤ'] = $dataWk['打合担当者コード'];

            // 施行担当者ＩＤ
            $dataRtn['施行担当者ＩＤ'] = $dataWk['施行担当者コード'];

            $tantoBumons = App_Utils::getTantoBumons();

            // 施行管轄ホール番号
            // 施行管轄ホール名
            $dataRtn['施行管轄ホール番号'] = $dataWk['売上部門コード'];
            $dataRtn['施行管轄ホール名']   = $dataWk['売上部門名'];

            // 式場ＩＤ
            $sikijyo_id = null;
            $niteiTuyaRec = $this->getNitei(4); // 通夜
            $niteiKokuRec = $this->getNitei(11); // 告別式
            if ( $niteiKokuRec ) {
                $sikijyo_id = $niteiKokuRec['basho_cd'];
            } else if ( $niteiTuyaRec ) {
                $sikijyo_id = $niteiTuyaRec['basho_cd'];
            }
            $dataRtn['式場ＩＤ'] = $sikijyo_id;

            // 施行番号 (追加)
            $dataRtn['施行番号'] = $seko_no;
            
            // 告別式日時
            $kokunichiji = null;
            $niteiKokuRec = $this->getNitei(11); // 告別式
            if ( $niteiKokuRec ) {
                $kokunichiji = str_replace('/', '-', $niteiKokuRec['nitei_ymd']);
            }
            $dataRtn['告別式日時'] = $kokunichiji;
            
            // 寺院名
            $dataRtn['寺院名'] = $dataWk['寺院名'];
            
            // 菩提寺名
            $dataRtn['菩提寺名'] = $dataWk['菩提寺名'];
            
            // 故人名
            $dataRtn['故人名'] = $dataWk['故人名'];
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            Msi_Sys_Utils::warn( $err );
            return $err;
        }

        return $dataRtn;
    }

    /**
     * 施行基本情報を取得する
     */
    protected function getSekoKihon() {
        $db = $this->_db;
        $seko_no = $this->_seko_no;

        $sekoKihon = $db->easySelOne( <<< END_OF_SQL
SELECT
    k.seko_no as "施行番号"
   ,k.moushi_kbn       -- 申込区分
   ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
            AND delete_flg=0 limit 1) as "申込区分名"
   ,k.sougi_cd         -- 葬儀区分
   ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
            AND delete_flg=0 limit 1) as "葬儀区分名"
   ,k.kaiin_cd        -- 施行区分(会員コード)
   ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.kaiin_cd AND code_kbn='0030'
            AND delete_flg=0 limit 1) as "施行区分名"
   ,k.kaiin_sonota     -- 会員区分その他
   ,k.uketuke_tanto_cd as "打合担当者コード"
   ,t1.tanto_nm        as "打合担当者名"
   ,k.seko_tanto_cd    as "施行担当者コード"
   ,t2.tanto_nm        as "施行担当者名"
   ,k.k_nm             as "故人名"
   ,k.k_knm            as "故人名カナ"
   ,k.kg_addr1         as "故人住所1"
   ,k.kg_addr2         as "故人住所2"
   ,k.m_nm             as "喪主名"
   ,k.m_knm            as "喪主カナ"
   ,k.souke_nm         as "葬家名"
   ,k.hs_anchi_nm      as "安置先名"
   ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.hs_anchi_kbn AND code_kbn='0600'
            AND delete_flg=0 limit 1) as "安置先区分名"
   ,k.hs_anchi_cd
   ,k.hs_anchi_kbn
   ,k.seko_plan_cd
   ,plan.seko_plan_nm  as "プラン名"
   ,k.bumon_cd as "売上部門コード"
   ,(SELECT bumon_lnm FROM bumon_mst WHERE bumon_cd=k.bumon_cd AND delete_flg=0) as "売上部門名"
   ,k.jyusho_nm         as "寺院名"
   ,CASE WHEN k.free7_kbn = 1  THEN '菩提寺'
         WHEN k.free7_kbn = 2  THEN '紹介'
                                ELSE ''
         END            as  "菩提寺名"
   ,k.kaisya_cd
   ,k.free9_kbn as seko_kbn_yippan
   ,cm4.kbn_value_lnm AS seko_kbn_yippan_nm
 FROM seko_kihon_info k
   LEFT OUTER JOIN tanto_mst t1
   ON  (
           k.uketuke_tanto_cd = t1.tanto_cd
       AND t1.delete_flg = 0
       )
   LEFT OUTER JOIN tanto_mst t2
   ON  (
           k.seko_tanto_cd = t2.tanto_cd
       AND t2.delete_flg = 0
       )
   LEFT OUTER JOIN seko_plan_mst  plan
   ON  (
           k.seko_plan_cd=plan.seko_plan_cd
       AND k.kaisya_cd=plan.kaisya_cd
--       AND k.gojokai_kbn=plan.gojokai_kbn   -- セレモニー様では無視
       AND ( (k.sougi_ymd BETWEEN plan.tekiyo_st_date AND plan.tekiyo_ed_date) OR k.sougi_ymd IS NULL )
       AND plan.delete_flg = 0
       )
    LEFT JOIN code_nm_mst cm4
        ON cm4.kbn_value_cd_num = k.free9_kbn
        AND cm4.code_kbn='3410'
        AND cm4.delete_flg=0
 WHERE k.seko_no = :seko_no
   AND k.delete_flg = 0
END_OF_SQL
                                      , array('seko_no' => $seko_no) );

        return $sekoKihon;
    }

    /**
     * 施行日程情報を取得する
     *  cf. JuchuCustomerinfo.ceremony.php getNitei()
     *
     * @param      integer  $nitei_kbn
     * @return     mixed    array(OK)|null
     */
    protected function getNitei($nitei_kbn) {
        // 読み込んでいなければデータ取得
        if ( !isset($this->_dataNitei) ) {
            $db = $this->_db;
            $seko_no = $this->_seko_no;

            $dataNitei = array();
            $sql = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分
                ,COALESCE(sn.ts_based_nm,tsi.ts_based_nm)  AS ts_based_nm  -- 日程基本名 
                ,tsi.ts_based_nm2   -- 日程基本名2 
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ymd-- 日程タイムスタンプ
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,CASE 
                    WHEN sn.free3_kbn = 1
                    THEN NULL
                    ELSE TO_CHAR(sn.nitei_ymd ,'HH24:MI')
                END nitei_time -- 日程時刻のみ
                ,TO_CHAR(sn.nitei_ed_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ed_ymd-- 日程終了タイムスタンプ
                ,TO_CHAR(sn.nitei_ed_ymd ,'HH24:MI') AS nitei_ed_time -- 日程終了時刻のみ
                ,sn.spot_cd       -- 場所区分コード
                ,sn.basho_kbn       -- 場所区分
                ,sn.basho_cd        -- 場所コード
                ,sn.basho_nm        -- 場所名
                ,sn.nyukan_kyo      -- 入棺経
                ,sn.syukan_kyo      -- 出棺経
                ,sn.kaso_kyo        -- 火葬経    
                ,sn.shishikisha_ninzu-- 司式者人数    
                ,sn.free1_kbn        -- 式場区分    
                ,sn.free2_kbn        -- 初七日    
                ,sn.v_free3          -- 初七日    
                ,sn.v_free4          -- 式場名    
            FROM
                seko_nitei sn
                INNER JOIN 
                    tm_schedule_info tsi
                    ON
                    (
                         sn.nitei_kbn = tsi.date_kbn
                    )
            WHERE
                    sn.seko_no = :seko_no
                AND sn.delete_flg = 0
            ORDER BY
                tsi.disp_no
                ";
            $select = $db->easySelect($sql, array('seko_no' => $seko_no));

            /* // タイムスケジュール情報マスタ取得SQL */
            /* $sql2 = " */
            /* SELECT */
            /*     tsi.ts_based_nm     -- 日程基本名 */
            /*     ,tsi.ts_based_nm2   -- 日程基本名2  */
            /*     ,tsi.date_kbn       -- 日付区分  */
            /*     ,tsi.disp_no        -- 表示順 */
            /* FROM */
            /*     tm_schedule_info tsi */
            /* WHERE */
            /*        tsi.delete_flg = 0 */
            /* ORDER BY */
            /*     tsi.disp_no */
            /*     "; */
            /* $select2 = $db->easySelect($sql2); */
            /* foreach ($select2 as &$value) { */
            /*     $value['nitei_kbn'] = (int) $value['date_kbn']; */
            /* } */
            // 施行日程に存在するとき
            if (count($select) > 0) {
                for ($i = 0; $i < count($select); $i++) {
                    $niteiOneRowData = array();
                    $niteiOneRowData['seko_no'] = $select[$i]['seko_no'];
                    $niteiOneRowData['nitei_kbn'] = (int) $select[$i]['nitei_kbn'];
                    $niteiOneRowData['ts_based_nm'] = $select[$i]['ts_based_nm'];
                    $niteiOneRowData['ts_based_nm2'] = $select[$i]['ts_based_nm2'];
                    $niteiOneRowData['nitei_ymd'] = $select[$i]['nitei_ymd'];
                    $niteiOneRowData['nitei_date'] = $select[$i]['nitei_date'];
                    $niteiOneRowData['nitei_time'] = $select[$i]['nitei_time'];
                    $niteiOneRowData['nitei_ed_ymd'] = $select[$i]['nitei_ed_ymd'];
                    $niteiOneRowData['nitei_ed_time'] = $select[$i]['nitei_ed_time'];
                    $niteiOneRowData['spot_cd'] = $select[$i]['spot_cd'];
                    $niteiOneRowData['basho_kbn'] = $select[$i]['basho_kbn'];
                    $niteiOneRowData['basho_cd'] = $select[$i]['basho_cd'];
                    $niteiOneRowData['basho_nm'] = $select[$i]['basho_nm'];
                    $niteiOneRowData['nyukan_kyo'] = $select[$i]['nyukan_kyo'];
                    $niteiOneRowData['syukan_kyo'] = $select[$i]['syukan_kyo'];
                    $niteiOneRowData['kaso_kyo'] = $select[$i]['kaso_kyo'];
                    $niteiOneRowData['shishikisha_ninzu'] = $select[$i]['shishikisha_ninzu'];
                    $niteiOneRowData['free1_kbn'] = $select[$i]['free1_kbn'];
                    $niteiOneRowData['free2_kbn'] = $select[$i]['free2_kbn'];
                    $niteiOneRowData['v_free3'] = $select[$i]['v_free3'];
                    $niteiOneRowData['v_free4'] = $select[$i]['v_free4'];
                    $dataNitei[$i] = $niteiOneRowData;
                }
            } else {
                $dataNitei = array(); // $select2;
            }

            $this->_dataNitei = $dataNitei;

            // Msi_Sys_Utils::debug( "#### _dataNitei=> " . Msi_Sys_Utils::dump($dataNitei) );
        }

        $dataNitei = $this->_dataNitei;

        $niteiRec = array();
        foreach ( $dataNitei as $rec00 ) {
            if ( (integer)$nitei_kbn === (integer)$rec00['nitei_kbn'] ) {
                $niteiRec = $rec00;
                return $niteiRec;
            }
        }

        return null;
    }

	/**
	 * 受注伝票明細取得
     * @return     array        該当がない場合は array() を返す
	 */
	protected function _readJuchuMsi() {
        // 読み込んでいなければデータ取得
        if ( !isset($this->_dataJuchuMsi) ) {
            $db = $this->_db;
            $seko_no = $this->_seko_no;

            $dataMsi = array();
            $sql = "
        SELECT
            *
,( SELECT chu_bunrui_nm FROM shohin_chu_bunrui_mst WHERE msi.chu_bunrui_cd=chu_bunrui_cd) AS chu_bunrui_nm
,( SELECT dai_bunrui_nm FROM shohin_dai_bunrui_mst WHERE msi.dai_bunrui_cd=dai_bunrui_cd) AS dai_bunrui_nm
        FROM
            juchu_denpyo_msi  msi
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
ORDER BY disp_no ASC, msi_no ASC
                ";
            $select = $db->easySelect($sql, array('seko_no' => $seko_no));

            $this->_dataJuchuMsi = $select;

            // Msi_Sys_Utils::debug( "#### _juchuMsi=> " . Msi_Sys_Utils::dump($select) );
        }

        return $this->_dataJuchuMsi;
    }

	/**
	 * 受注伝票明細取得(by chu_bunrui)
     *
     * @param      $chu_bunrui  string  複数の場合は ,区切りで渡す
     * @return     array        該当がない場合は array() を返す
	 */
	protected function getJuchuMsi($chu_bunrui) {
        $arrChuBunrui = Msi_Sys_Utils::strArrayify_qw($chu_bunrui);

        $dataJuchuMsi = $this->_readJuchuMsi();

        $juchuRecs = array();
        foreach ( $dataJuchuMsi as $rec00 ) {
            $_rec_chu_bunrui_cd = (string)$rec00['chu_bunrui_cd'];
            if ( in_array($_rec_chu_bunrui_cd, $arrChuBunrui) ) {
                $juchuRecs[] = $rec00;
            }
        }

        return $juchuRecs;
	}

	/**
	 * 受注伝票明細取得(by shohin_kbn)
     *
     * @param      $shohin_kbn  string  複数の場合は ,区切りで渡す
     * @return     array        該当がない場合は array() を返す
	 */
	protected function getJuchuMsiByKbn($shohin_kbn) {
        $arrShohinKbn = Msi_Sys_Utils::strArrayify_qw($shohin_kbn);

        $dataJuchuMsi = $this->_readJuchuMsi();

        $juchuRecs = array();
        foreach ( $dataJuchuMsi as $rec00 ) {
            $_rec_shohin_kbn_cd = (string)$rec00['shohin_kbn'];
            if ( in_array($_rec_shohin_kbn_cd, $arrShohinKbn) ) {
                $juchuRecs[] = $rec00;
            }
        }

        return $juchuRecs;
	}

	/**
	 * 施行発注管理情報取得
     * @return     array        該当がない場合は array() を返す
	 */
	protected function _readHachuInfo() {
        // 読み込んでいなければデータ取得
        if ( !isset($this->_dataHachuInfo) ) {
            $db = $this->_db;
            $seko_no = $this->_seko_no;

            $dataMsi = array();
            $sql = <<< END_OF_SQL
        SELECT
            h.*
,siire.siire_lnm as "仕入先名"
,siire.jigyosya_nm as "事業者名"
,siire.syaryo_tein1 as "車両定員（正座席）"
,siire.syaryo_tein2 as "車両定員（補助席）"
,( SELECT chu_bunrui_nm FROM shohin_chu_bunrui_mst WHERE h.chu_bunrui_cd=chu_bunrui_cd) AS chu_bunrui_nm
,( SELECT dai_bunrui_nm FROM shohin_dai_bunrui_mst WHERE h.dai_bunrui_cd=dai_bunrui_cd) AS dai_bunrui_nm
        FROM
            seko_hachu_info h
  LEFT JOIN siire_mst siire 
         ON siire.kaisya_cd=h.kaisya_cd
        AND siire.siire_cd=h.siire_cd
        AND siire.delete_flg=0
 WHERE h.seko_no = :seko_no
   AND h.delete_flg = 0
ORDER BY seko_no ASC, hachu_no ASC
END_OF_SQL;
            $select = $db->easySelect($sql, array('seko_no' => $seko_no));

            $this->_dataHachuInfo = $select;

            // Msi_Sys_Utils::debug( "#### _hachuInfo=> " . Msi_Sys_Utils::dump($select) );
        }

        return $this->_dataHachuInfo;
    }

	/**
	 * 施行発注管理情報取得(by shohin_kbn)
     *
     * @param      $shohin_kbn  string  複数の場合は ,区切りで渡す
     * @return     array        該当がない場合は array() を返す
	 */
	protected function getHachuInfoByKbn($shohin_kbn) {
        $arrShohinKbn = Msi_Sys_Utils::strArrayify_qw($shohin_kbn);

        $dataHachuInfo = $this->_readHachuInfo();

        $hachuRecs = array();
        foreach ( $dataHachuInfo as $rec00 ) {
            $_rec_shohin_kbn_cd = (string)$rec00['shohin_kbn'];
            if ( in_array($_rec_shohin_kbn_cd, $arrShohinKbn) ) {
                $hachuRecs[] = $rec00;
            }
        }

        return $hachuRecs;
	}

}
