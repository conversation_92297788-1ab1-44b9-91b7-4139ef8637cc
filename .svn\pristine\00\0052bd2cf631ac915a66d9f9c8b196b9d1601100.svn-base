/** 
 * @fileoverview 受注伝票入力（単品）
 */
msiGlobalObj.doReadonlyByMyself = true; // 参照専用は独自処理
$( function() {
    "use strict";
    
    /** 支払方法区分: 社内 */
    var PAY_KBN_SHANAI = '4';
    /** 支払方法区分: 持込 */
    var PAY_KBN_MOTIKOMI = '5';
    /** 支払方法区分: キャンセル */
    var PAY_KBN_CANCEL = '6';
    /** 単価補正対象配列 */
    var HOSEI_TAISHO_LIST = new Array(PAY_KBN_SHANAI    // 社内
                                    , PAY_KBN_MOTIKOMI  // 持込
                                    , PAY_KBN_CANCEL    // キャンセル
                                     );

    var msg01 = "データが変更されています. \nこのデータを保存せず、ページ移動してよろしいですか？";
    
    var utils = window.msiBbUtils;

    var MeisaiModel = Backbone.Model.extend({

        defaults: function() {
            return {
                denku: 1, // null
                shohin_cd: null,
                shohin_nm: null,
                suryo: 1,
                tani_cd: 99, // null,
                bf_juchu_tanka: null, // データ補正前の単価
                juchu_tanka: null,
                genka_tanka: null,
                nebiki_gaku: null,
                msi_biko1: null,
                msi_biko2: null,
                shohin_tekiyo: null,
                juchu_gaku: null,
                juchu_gaku_cal: null, // 値引額も調整した値
                genka_gaku: null,
                arari: null,
                msi_no: null, // 受注明細No
                nm_input_kbn: null,
                tnk_chg_kbn: null,
                tani_ovr_kbn: null,
                seq_no: meisaiList.nextSeqNo(),
                line_no: -1,
                _s_search_cond: {}, // 前回商品検索条件データ
                zaiko_knri_kbn: null, // 在庫管理区分
                soko_cd: null, // 倉庫コード
                soko_nm: null, // 倉庫名
                zei_cd: null, // sugiyama 軽減税率対応 keigen
                reduced_tax_rate: null, // sugiyama 軽減税率対応  軽減税率区分: 1：対象外 2：軽減8%
                zei_kbn: -1, // sugiyama 軽減税率対応  税区分   -1: dummy
                selected: 0,
            };
        },

        validation: {
            denku: {
                required: true,
            },
            tani_cd: {
                required: true,
            },
            suryo: {
                required: true,
                pattern: 'number',
            },
            juchu_tanka: {
                required: true,
                pattern: 'number',
            },
            genka_tanka: {
                required: false,
                pattern: 'number',
            },
            nebiki_gaku: {
                required: false,
                pattern: 'number',
            },
            shohin_cd: {
                required: true,
            },
            shohin_nm: {
                required: true,
                maxLength: 40,
            },
            shohin_tekiyo: {
                required: false,
                maxLength: 40,
            },
            msi_biko1: {
                required: false,
                maxLength: 30,
            },
            msi_biko2: {
                required: false,
                maxLength: 30,
            },
            soko_cd: {
                required: function() {
                    var zaiko_knri_kbn = this.get("zaiko_knri_kbn");
                    return zaiko_knri_kbn == '1' || zaiko_knri_kbn == '2';
                }
            },
        },

        labels: {
            denku: '伝区',
            shohin_cd: '商品コード',
            shohin_nm: '商品名称',
            suryo: '数量',
            tani_cd: '単位',
            juchu_tanka: '受注単価',
            genka_tanka: '原価単価',
            nebiki_gaku: '値引額',
            msi_biko1: '明細備考１',
            msi_biko2: '明細備考２',
            shohin_tekiyo: '商品摘要',
            soko_cd: '倉庫コード',
        },

    }); // MeisaiModel

    var MeisaiCollection = Backbone.Collection.extend({

        model: MeisaiModel,

        nextSeqNo: function() {
            ++ MeisaiCollection.seq_cnt;
            return MeisaiCollection.seq_cnt;
        },

        resetLineNo: function() {
            var i, max, m;
            for ( i = 0, max=this.length ; i < max ; i++ ) {
                m = this.at(i);
                m.set( 'line_no', i+1 );
                // console.log( 'seq_no=>' + m.get('seq_no') + ' line_no=>' + m.get('line_no') + ' ' + m.get('msi_biko2') );
            }
        },
    },
    { // classProperties
        seq_cnt: 0
    }
    ); // MeisaiCollection

    var meisaiList = new MeisaiCollection;

    var AppModel = Backbone.Model.extend({

        defaults: function() {
            return {
                kaishu_kbn: null,
                juchusaki_kbn: null,
                bf_pay_method_cd: null, // 前回選択した支払い方法コード
                pay_method_cd: null,
                tax_kbn: null,
                zei_cd: null,
                juchu_ymd: null,
                keijo_ymd: null,
                denpyo_no: null,
                kaishu_ymd: null,
                tanto_cd: null,
                tanto_nm: null,
                bumon_cd: null,
                denpyo_biko1: null,
                denpyo_biko2: null,
                total_kingaku: null,
                total_genka: null,
                total_zei: null,
                total_zeikomi: null,
                // 請求先
                seko_no: null, // 施行No 2015/01/18 ADD Kayo
                sekyu_cd: null,
                sekyu_nm: null,
                sekyu_knm: null,		// 請求先名カナ
                sekyu_yubin_no: null,	// 請求先郵便番号
                sekyu_addr1: null,		// 請求先住所1
                sekyu_addr2: null,		// 請求先住所2
                sekyu_tel: null,		// 請求先電話番号
                sekyu_fax: null,		// 請求先FAX
                sekyu_biko1: null,		// 請求先備考１
                sekyu_biko2: null,		// 請求先備考２
				sekyu_soufu_nm:null,	// 請求書送付先 2015/01/18 ADD Kayo 
                kaiin_kbn_nm: null,     // 2016/03/17 ADD Kayo
                kaiin_sbt_nm: null,     // 2016/03/17 ADD Kayo
                v_free5: null, // 振込先
				// 納品先
                nonyu_cd: null,
                nonyu_nm: null,
                nonyu_dt: null,
                nonyu_knm: null, // 納品先名カナ
                nonyu_yubin_no: null, // 納品先郵便番号
                nonyu_addr1: null, // 納品先住所1
                nonyu_addr2: null, // 納品先住所2
                nonyu_tel: null, // 納品先電話番号
                nonyu_fax: null, // 納品先FAX
                nonyu_biko1: null, // 納品先備考１
                nonyu_biko2: null, // 納品先備考２
                nonyu_delivery_kbn: null, // 納品場所区分
                cre_user:null,            // 作成者 日時    2016/06/16 ADD Kayo
                mod_user:null,            // 更新者 日時    2016/06/16 ADD Kayo
                seikyu_user:null,         // 請求者 日時    2016/07/01 ADD Kayo
                zaimu_rendo_user:null,    // 財務連動 日時    2020/08/21 ADD Kayo
                nohinsho_pirint_kbn: 0,   // 納品書発行区分 2016/07/07 ADD Kayo
                uketsuke_no: null,        // 受付番号 v_free6
            };
        },

        validation: {
            kaishu_kbn: {
                required: true,
            },
            juchusaki_kbn: {
                required: true,
            },
            pay_method_cd: {
                required: true,
            },
            tax_kbn: {
                required: true,
            },
            zei_cd: {
                required: true,
            },
            denpyo_no: {
                required: false,
            },
            juchu_ymd: {
                required: true,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            keijo_ymd: {
                required: true,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            kaishu_ymd: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            tanto_cd: {
                required: true,
            },
            tanto_nm: {
                required: true,
            },
            denpyo_biko1: {
                required: false,
                maxLength: 30,
            },
            denpyo_biko2: {
                required: false,
                maxLength: 30,
            },

            // 請求先
            sekyu_nm: {
                required: true,
                maxLength: 30
            },
            sekyu_knm: {
                required: false,
                maxLength: 30
            },
            sekyu_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            sekyu_addr1: {
                required: false,
                maxLength: 30
            },
            sekyu_addr2: {
                required: false,
                maxLength: 30
            },
            sekyu_tel: {
                required: false,
                pattern: 'tel'
            },
            sekyu_fax: {
                required: false,
                pattern: 'tel'
            },
            sekyu_biko1: {
                required: false,
                maxLength: 30
            },
            sekyu_biko2: {
                required: false,
                maxLength: 30
            },
            kaiin_kbn_nm: {     // 2016/03/17 ADD Kayo
                required: false,
            },
            kaiin_sbt_nm: {     // 2016/03/17 ADD Kayo
                required: false,
            },

            // 納入先
            nonyu_nm: {
                required: false,
                maxLength: 30
            },
            nonyu_dt: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            nonyu_knm: {
                required: false,
                maxLength: 30
            },
            nonyu_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            nonyu_addr1: {
                required: false,
                maxLength: 30
            },
            nonyu_addr2: {
                required: false,
                maxLength: 30
            },
            nonyu_tel: {
                required: false,
                pattern: 'tel'
            },
            nonyu_fax: {
                required: false,
                pattern: 'tel'
            },
            nonyu_biko1: {
                required: false,
                maxLength: 30
            },
            nonyu_biko2: {
                required: false,
                maxLength: 30
            },
            nonyu_delivery_kbn: {
                required: false,
            },
            nohinsho_pirint_kbn: {  // 納品書発行区分 2016/07/07 ADD Kayo 
                required: false,
            },
            uketsuke_no: {
                required: false,
                maxLength: 7
            },
            _all: function(val, attr, computed) {
                var zei_cds = app.collection.reduce(function(accum, m) {
                    var zei_cd = +m.get('zei_cd');
                    var zei_kbn = +m.get('zei_kbn');
                    if ( zei_kbn > 0 && zei_cd > 0 ) { accum.push( zei_cd ); }
                    return accum;
                }, []);
                var errMsg = msiLib2.isZeiCdsOk(zei_cds);
                if ( errMsg ) {
                    return errMsg;
                }
                return;
            },
        },

        labels: {
            kaishu_kbn: '回収区分',
            juchusaki_kbn: '受注先区分',
            pay_method_cd: '支払方法',
            tax_kbn: '税区分',
            zei_cd: '税コード',
            juchu_ymd: '受注日',
            keijo_ymd: '売上計上日',
            denpyo_no: '伝票NO',
            seko_no:'施行番号',
            sekyu_cd: '請求先',
            sekyu_nm: '請求先',
            kaiin_kbn_nm: '会員区分',     // 2016/03/17 ADD Kayo
            kaiin_sbt_nm: '会員種別',     // 2016/03/17 ADD Kayo
            nonyu_cd: '納品先',
            nonyu_nm: '納品先',
            nonyu_dt: '納入予定日',
            kaishu_ymd: '回収予定日',
            tanto_cd: '担当者',
            tanto_nm: '担当者',
            bumon_cd: '部門',
            denpyo_biko1: '備考１',
            denpyo_biko2: '備考２',
            nohinsho_pirint_kbn: '納品書発行区分', // 2016/07/07 ADD Kayo 
            uketsuke_no: '受付番号'
        }

    }); // AppModel

    var MeisaiView = Backbone.View.extend({

        tagName:  "tbody",

        template: _.template($('#item-template').html()),

        events: {
            "click a.destroy" : "clear",
            "click a.add" : "add",
            "click .shohin_cd,.shohin-ref" : 'shohinHelper',
            "click .soko_cd,.dlg_soko": "sokoHelper",
            "click .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen sugiyama
            "keyup .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen sugiyama
            "click tr": "setSelected",
        },
        // 行選択
        setSelected: function (e) {
            var selected = this.model.get('selected');
            var className = e.target.className;
            var target = className.match('line_no|row');
            
            if ($.msiJqlib.isNullEx2(target)) {
                return;
            }
            if (selected === '1') {
                this.$('.row').removeClass('row-selected');
                this.model.set('selected', '0')
            } else {
                this.$('.row').removeClass('row-selected');
                //_.each(meisaiList.models, function (m) {
                //    m.set('selected', '0');
                //});
                this.$('.row').addClass('row-selected');
                this.model.set('selected', '1');
            }
        },
        // 軽減税率対応 消費税選択 sugiyama keigen
        showKeigenSel2: function(ev) {
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            if ( this.model.get('shohin_cd') ) { // 商品が指定されている場合だけ表示
                msiGlobalObj.keigenLib.f03ShowKeigenSel2.apply(this, [ev]);
            }
        },

        shohinHelper: function() {
            // this は Backbone.View
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model;
            var bbv = this;
            var bumonCd = orgDataApp.bumon_cd;
            this.$el.msiPickHelper({
                action: 'shohin.nokijunbi', // org keigen  'shohin', // 'shohin', 'shohin2'
                data: {bumon_cd: bumonCd},
                onSelect: function(data) {
                    delete data['gazo_tn_data'];
                    delete data['img'];
                    // console.log( 'shohinHelper data=>', data );
                    // console.log( '### shohinHelper' + data.nm_input_kbn +
                    //             ' tnk_chg_kbn=>' + data.tnk_chg_kbn + ' tani_ovr_kbn=>' + data.tani_ovr_kbn );
                    var dataZei = msiLib2.chkZeiEtcOrDflt(data.uri_zei_cd, data.uri_reduced_tax_rate); // , '2019/10/01');
                    var uri_zei_cd = _.has(dataZei, 'zei_cd') ? dataZei.zei_cd : null;
                    var uri_reduced_tax_rate = _.has(dataZei, 'reduced_tax_rate') ? dataZei.reduced_tax_rate : null;
                    bbm.set( {shohin_cd: data.code,
                              shohin_nm: data.name,
                              juchu_tanka: data.tanka.replace(/,/g, ''),
                              genka_tanka: data.genka.replace(/,/g, ''),
                              nebiki_gaku: null,
                              shohin_tekiyo: data.shohin_tkiyo_nm,
                              tani_cd: data.tani_cd,
                              nm_input_kbn: data.nm_input_kbn,
                              tnk_chg_kbn: data.tnk_chg_kbn,
                              tani_ovr_kbn: data.tani_ovr_kbn,
                              zaiko_knri_kbn: data.zaiko_knri_kbn,
                              dai_bunrui_cd: data.dai_bunrui_cd,
                              chu_bunrui_cd: data.chu_bunrui_cd,
                              shohin_kbn: data.shohin_kbn,
                              zei_cd: uri_zei_cd,  // keigen
                              reduced_tax_rate: uri_reduced_tax_rate, // keigen
                              zei_kbn: data.uri_zei_kbn, // keigen
                             } );
                    // 商品検索条件の保存
                    bbv.shohinSearchCondSave();
                    // 支払方法区分に応じてデータを補正
                    _hoseiData(app.model.get('pay_method_cd'));
                    bbv.adjShohinInput();
                },
                onClear: function() {
                    bbm.set( {shohin_cd: null,
                              shohin_nm: null,
                              juchu_tanka: null,
                              genka_tanka: null,
                              nebiki_gaku: null,
                              shohin_tekiyo: null,
                              tani_cd: null,
                              nm_input_kbn: null,
                              tnk_chg_kbn: null,
                              tani_ovr_kbn: null,
                              zaiko_knri_kbn: null,
                              dai_bunrui_cd: null,
                              chu_bunrui_cd: null,
                              shohin_kbn: null,
                              zei_cd: null,  // keigen
                              reduced_tax_rate: null, // keigen
                              zei_kbn: -1, // keigen
                             } );
                    bbv.adjShohinInput();
                },
                hookSetData: function() {
                    var param = {
                        init_search: 0,
                        no_cond: 0,
                        // s_uri_zei_kbn: app.model.get('tax_kbn'), 伝票内で税区分(内税、外税)を混在できるようにコメント化
                        // s_shohin_kbn: null,
                        // s_dai_bunrui_cd: '0050,0030', // '0050,0040,0060,0080,0030', XXX
                        // s_chu_bunrui_cd: '0070,0040', // '0010,0040',
                        // tpl_search: 'nobunrui',
                    },
                    // 商品検索条件の復旧
                    param = _.extend(param, bbv.shohinSearchCondGet());
                    // console.log( 'resotre=>' + JSON.stringify(param) );
                    return param;
                }
            });
        },
        sokoHelper: function() {
            // this は Backbone.View
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'soko', //
                onSelect: function(data) {
                    bbm.set( {soko_cd: data.code,
                              soko_nm: data.name,
                             } );
                },
                onClear: function() {
                    bbm.set( {soko_cd: null,
                              soko_nm: null,
                             } );
                },
            });
        },

        // 前回商品検索条件データ
        // _shohinSearchCondData: {},

        // 商品検索条件の保存
        shohinSearchCondSave: function() {
            var p = {};
            if ( $('#s_shohin_kbn').select2('val') ) {
                p['s_shohin_kbn'] = $('#s_shohin_kbn').select2('val');
            }
            if ( $('#s_dai_bunrui_cd').select2('val') ) {
                p['s_dai_bunrui_cd'] = $('#s_dai_bunrui_cd').select2('val').join(',');
            }
            if ( $('#s_chu_bunrui_cd').select2('val') ) {
                p['s_chu_bunrui_cd'] = $('#s_chu_bunrui_cd').select2('val').join(',');
            }
            this.model.set('_s_search_cond', p);
            shohinSearchCondData = p;
            // console.log( 'sav=>' + JSON.stringify(p) );
        },

        // 商品検索条件の復旧
        shohinSearchCondGet: function() {
            // 商品CDが設定されている場合は、自分の直前データ、未設定時は大域の直前データ
            var condData = this.model.get('shohin_cd') ? this.model.get('_s_search_cond') : shohinSearchCondData;
            var p = _.pick( condData, 's_shohin_kbn', 's_dai_bunrui_cd', 's_chu_bunrui_cd' );
            return p;
        },

        /**
         * 商品関連入力制御
         * cf. juchu.juchuhenko.js
         * (1)名称入力区分による制御
         * @param {string} nmInputKbn 名称入力区分
         * 1：名称変更不可 摘要入力不可
         * 2：名称変更可   摘要入力不可
         * 3：名称変更不可 摘要入力可
         * 4：名称変更可   摘要入力可
         * (2) 売上単価変更区分による制御
         * 0：変更不可, 1：変更可
         * (3) 単位変更不可区分
         * 0：変更不可, 1：変更可
         */
        adjShohinInput: function() {
            var data = this.model.toJSON();
            // console.log( '^^^^^^^^^^^^^^^' + JSON.stringify(data) );
            var nm_input_kbn=4, tani_ovr_kbn=1, tnk_chg_kbn=1;
            if ( _.has(data, 'nm_input_kbn') ) nm_input_kbn = data.nm_input_kbn;
            if ( _.has(data, 'tnk_chg_kbn')  ) tnk_chg_kbn = data.tnk_chg_kbn;
            if ( _.has(data, 'tani_ovr_kbn') ) tani_ovr_kbn = data.tani_ovr_kbn;
            // console.log( '### nm_input_kbn=>' + nm_input_kbn + ' tnk_chg_kbn=>' + tnk_chg_kbn + ' tani_ovr_kbn=>' + tani_ovr_kbn );
            // 名称入力区分
            switch ( +nm_input_kbn ) {
            case 2:
                this.$('.shohin_nm').removeAttr('readonly');
                this.$('.shohin_tekiyo').attr('readonly', 'readonly');
                break;
            case 3:
                this.$('.shohin_nm').attr('readonly', 'readonly');
                this.$('.shohin_tekiyo').removeAttr('readonly');
                break;
            case 4:
                this.$('.shohin_nm').removeAttr('readonly');
                this.$('.shohin_tekiyo').removeAttr('readonly');
                break;
            case 1:
            default:
                this.$('.shohin_nm').attr('readonly', 'readonly');
                this.$('.shohin_tekiyo').attr('readonly', 'readonly');
            }
            // 売上単価変更区分
            if (tnk_chg_kbn == 1) {
                this.$('.juchu_tanka').removeAttr('readonly');
            } else {
                this.$('.juchu_tanka').attr('readonly', 'readonly');
            }
            // 単位変更不可区分
            if (tani_ovr_kbn == 1) {
                this.$('.tani_cd').removeAttr('readonly');
            } else {
                this.$('.tani_cd').attr('readonly', 'readonly');
            }
        },        

        initialize: function() {
            Backbone.Validation.bind(this);

            this.listenTo(this.model, 'change', this.recalcAndRender);
            this.listenTo(this.model, 'destroy', this.remove);
        },

        recalcAndRender: function() {
            this.recalc();
            //this.render();
        },

        recalc: function() {
            var m = this.model,
                denku = m.get('denku'), // 伝票区分  1：受注、2：返品 // 3:値引き 5：破損返品、90：記事
                sign = 1,
                nebiki_gaku = 0,
                suryo = m.get('suryo');
            // if ( denku == 2 ) { sign = -1; }  伝区は単なる集計属性
            if ( !$.msiJqlib.isNullEx2( m.get('nebiki_gaku') ) ) {
                nebiki_gaku = (+1) * m.get('nebiki_gaku'); // マイナス値が明示的に設定される
            }

            // if ( _.isNumber( m.get('juchu_tanka') ) && _.isNumber( m.get('suryo') ) ) {
            if ( !$.msiJqlib.isNullEx2( m.get('juchu_tanka') ) && !$.msiJqlib.isNullEx2( m.get('suryo') ) ) {
                m.set('juchu_gaku', m.get('juchu_tanka') * m.get('suryo') * sign);
                m.set('juchu_gaku_cal', m.get('juchu_tanka') * m.get('suryo') * sign + nebiki_gaku);
            } else {
                m.unset('juchu_gaku');
                m.unset('juchu_gaku_cal');
            }
            if ( !$.msiJqlib.isNullEx2( m.get('genka_tanka') ) && !$.msiJqlib.isNullEx2( m.get('suryo') ) ) {
                m.set('genka_gaku', m.get('genka_tanka') * m.get('suryo') * sign);
            } else {
                m.unset('genka_gaku');
            }
            if ( !$.msiJqlib.isNullEx2( m.get('juchu_gaku') ) && !$.msiJqlib.isNullEx2( m.get('genka_gaku') ) ) {
                m.set('arari',  m.get('juchu_gaku') - m.get('genka_gaku'));
            } else {
                m.unset('arari');
            }
        },

        render: function() {

            Backbone.Validation.bind( this, Backbone.Validation.msi_err_setting_std_2() );

            if ( this.template === null ) {
                this.template = _.template($('#item-template').html());
            }
            this.$el.html(this.template(this.model.toJSON()));

            // 参照専用で表示しない項目を消す
            if ( utils.isReadOnlyCtxt() ) {
                this.$('td.control').remove();
                $(document).find('.header table td.control').remove();
            }

            $('.msi-picker', this.$el).each( msiLib2.msiPickerBinder );

            // if ( this.model.get('line_no') % 2 == 0 ) {
            //     this.$el.find('td').addClass('my-table-row-bg');
            //     this.$el.addClass( 'striped2' );
            // } else {
            //     this.$el.find('td').removeClass('my-table-row-bg');
            //     this.$el.addClass( 'striped1' );
            // }

            this.stickit();

            utils.decoMinus( this, 'juchu_tanka genka_tanka suryo juchu_gaku juchu_gaku_cal genka_gaku nebiki_gaku' );

            //            this.model.validate(); // XXX

            this.adjShohinInput();

            if ( utils.isReadOnlyCtxt() ) {
                this.$el.find(':input').attr('disabled', 'disabled')
                    .end().find('.my-readonly-hidden').hide();
            }

            return this;
        },

        // // Close the `"editing"` mode, saving changes to the todo.
        // DELclose: function() {
        //     var value = this.input.val();
        //     if (!value) {
        //         this.clear();
        //     } else {
        //         this.model.save({title: value});
        //         this.$el.removeClass("editing");
        //     }
        // },

        // Remove the item, destroy the model.
        clear: function() {
            minDataApp = app.model.toJSON(); // not JSON, but Object
            minDataCol = app.collection.toJSON(); // not JSON, but Object
            this.model.destroy();
            min2DataApp = app.model.toJSON(); // not JSON, but Object
            min2DataCol = app.collection.toJSON(); // not JSON, but Object
        },

        // Create a item. ugly...
        add: function(e) {
            // shift を押した場合は上に追加
            var isUpside = e.shiftKey ? true : false;
            // Ctrl を押した場合はコピー
            var isCopy = (e.ctrlKey || e.metaKey) ? true : false;

            var off = this.model.get('line_no');
            var newModel, orgModel;
            if ( isCopy ) {
                orgModel = meisaiList.get( this.model );
                newModel = orgModel.clone(); // shallow copy
                newModel.set( 'msi_no', null );
            } else {
                newModel = new MeisaiModel;
            }

            // console.log( 'add line_no=>' + off + ' shift=>' + isUpside + ' isCopy=>' + isCopy );
            // newModel.set( 'msi_biko2',  ""+jQuery.format.date(new Date(), 'MM/dd hh:mm:ss') ); // XXX

            if ( isUpside ) off = off -1;
            meisaiList.add( newModel, {at: off, silent:true} ); // add event を挙げない
            meisaiList.resetLineNo(); // line_no を再設定
            meisaiList.trigger( 'add', newModel, meisaiList, {at: off} ); // , options) // 改めて add event を挙げる
        },

        bindings: {
            '.line_no': 'line_no',
            '.denku': {
                observe: 'denku',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '.tani_cd': {
                observe: 'tani_cd',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '.shohin_cd': {
                observe: 'shohin_cd',
                events: ['change'], // , 'input', 'propertychange'],
            },
            '.shohin_nm': {
                observe: 'shohin_nm',
                events: ['change'],
            },
            '.shohin_tekiyo': {
                observe: 'shohin_tekiyo',
                events: ['change'],
            },
            '.msi_biko1': {
                observe: 'msi_biko1',
                events: ['change'],
            },
            '.msi_biko2': {
                observe: 'msi_biko2',
                events: ['change'],
            },
            '.suryo': {
                observe: 'suryo',
                events: ['change'],
            },
            '.juchu_tanka': {
                observe: 'juchu_tanka',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.genka_tanka': {
                observe: 'genka_tanka',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.nebiki_gaku': {
                observe: 'nebiki_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.juchu_gaku': {
                observe: 'juchu_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.juchu_gaku_cal': {
                observe: 'juchu_gaku_cal',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.genka_gaku': {
                observe: 'genka_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.arari': {
                observe: 'arari',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.soko_cd': {
                observe: 'soko_cd',
                events: ['change'],
            },
            '.soko_nm': {
                observe: 'soko_nm',
                events: ['change'],
            },
            // sugiyama 軽減税率対応
            // 軽減税率対応 keigen sugiyama
            '.zei_cd': {
                observe: 'zei_cd',
                onGet: function(val, options) {
                    msiGlobalObj.keigenLib.f03UpdKeigenZeiCd(val, this);
                    return val;
                },
                afterUpdate: function($el, val, options) {
                    msiGlobalObj.keigenLib.f03UpdKeigenZeiCd(val, this);
                },
            },
        },
    }); // MeisaiView

    var AppView = Backbone.View.extend({

        el: document, // '#my-form-id', // for #hall_cd

        events: {
            "click #add-meisai": "createMeisai",
            "click #denpyo_no,.denpyo_no-ref": "denpyoHelper",
            "click #tanto_nm,.tanto-ref": "tantoHelper",
            "click #sekyu_cd,#sekyu_nm,.sekyu_cd-ref": "sekyuHelper",
            "click #nonyu_cd,#nonyu_nm,.nonyu_cd-ref": "nonyuHelper",
            "click #btn_save": "doSave",
            "click #btn_copy": "doCopy", // XXX
            "click #btn_delete": "doDelete",
            "click #btn_new": "doNew",
            "click #btn_cancel": "doCancel",
            "click #btn_check": "doCheck",
            "click #btn_clear_err": "doClearErr",
            "click #btn_print": "doPrint",
            "click #btn_print_nohin": "doPrint_nohin",
            "click #btn_print_sei": "doPrint_sei",
            "click #btn_print_nyu_ryo_sei": "doPrint_nyu_ryo_sei",
            "click #btn_print_konbini": "doPrint_konbini",
            "change #pay_method_cd" : "changePayMethodCd",
            "click .chkAllToggle" : "toggleChkAll",     // 全選択ボタン
            "click #btn_csv": "doCsv",                  // CSV出力
        },
        // 受注伝票検索ダイアログ	
        denpyoHelper: function() {
            // this は Backbone.View
            // 参照専用モードでも伝票検索は抑制しない 
            // if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model,
                bbv = this;
            this.$el.msiPickHelper({
                action: 'juchu.denpyo',
                onSelect: function(data) {
                    if ( bbv.isChanged() ) {
                        if ( ! confirm(msg01) ) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', data.code);
                    var denpyo_no = data.code;
                    $.ajax({ // url: location.href,
                        data: {
                            denpyo_no: denpyo_no,
                            action: '表示',
                        },
                        type: 'POST',
                        success: function( mydata ) {
                            if ( mydata.status == 'OK' ) {
                                // msiLib2.showInfo( mydata.msg );
                                _resetData( mydata.dataApp, mydata.dataCol );
                                // console.log( '* * * bumon_cd=>' + mydata.dataApp.bumon_cd );
                                msiLib2.setHallCd( mydata.dataApp.bumon_cd ); // XXX auto?
                            } else {
                                msiLib2.showErr( mydata.msg );
                                // if ( orgDataApp && orgDataCol ) {
                                //    _resetData( orgDataApp, orgDataCol );
                                // }
                            }
                        }
                    });
                },
                onClear: function() {
                    if ( bbv.isChanged() ) {
                        if ( ! confirm(msg01) ) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', null);
                    $.ajax({ // url: location.href,
                        data: {
                            action: '初期化',
                        },
                        type: 'POST',
                        success: function( mydata ) {
                            if ( mydata.status == 'OK' ) {
                                _resetData( mydata.dataApp, mydata.dataCol );
                            } else {
                                msiLib2.showErr( mydata.msg );
                            }
                        }
                    });
                },
                hookSetData: function() {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return { init_search: 0, // XXX
                             no_cond: 0,
                             s_seko_no: '0000000000', // 単品
                             // 's_juchu_ymd_from': $.msiJqlib.getStdDate(),
                             // 's_juchu_ymd_to': $.msiJqlib.getStdDate(),
                             // ,s_bumon: bumon_cd
                           };
                }
            });
        },

        // 担当pickup
        tantoHelper: function() {
            // this は Backbone.View
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function(data) {
                    bbm.set('tanto_cd', data.code);
                    bbm.set('tanto_nm', data.name);
                },
                onClear: function() {
                    bbm.set('tanto_cd', null);
                    bbm.set('tanto_nm', null);
                },
                hookSetData: function() {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return { init_search: 1,
                             no_cond: 0,
                             s_bumon: bumon_cd
                           };
                }
            });
        },

        // 請求先pickup
        sekyuHelper: function() {
            // this は Backbone.View
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model;
            msiLib2.setPickHelper( 'juchu.denpyo.sekyu', '#sekyu_cd'
                                   ,function(data) { // onSelect
                                       // console.log( JSON.stringify(data) );
                                       Backbone.trigger( 'pick.sekyu.select', data ); // catched in juchu.denpyo.order.sekyu.js
                                   }
                                   ,function() { // onClear
                                       // nop.  cf. juchu.denpyo.order.sekyu.js
                                       // Backbone.trigger( 'pick.sekyu.clear' );
                                   }
                                   ,function() { // hookSetData
                                       var p = { init_search: 0,
                                                 no_cond: 0,
                                                 s_seko_no: null, // 2015/01/18 UPD Kayo
                                               };
                                       _(msiGlobalObj.bbExports.SekyuModel.myKeys).each( function(v) { p[v] = bbm.get(v); } );
                                       return p;
                                   }
                                 );
        },

        // 納品先pickup
        nonyuHelper: function() {
            // this は Backbone.View
            if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model;
            msiLib2.setPickHelper( 'juchu.denpyo.nonyu', '#nonyu_cd'
                                   ,function(data) { // onSelect
                                       // console.log( JSON.stringify(data) );
                                       Backbone.trigger( 'pick.nonyu.select', data ); // catched in juchu.denpyo.order.nonyu.js
                                   }
                                   ,function() { // onClear
                                       // nop.  cf. juchu.denpyo.order.nonyu.js
                                       // Backbone.trigger( 'pick.nonyu.clear' );
                                   }
                                   ,function() { // hookSetData
                                       var p = { init_search: 0,
                                                 no_cond: 0,
                                                 s_seko_no: '0000000000',
                                               };
                                       _(msiGlobalObj.bbExports.NonyuModel.myKeys).each( function(v) { p[v] = bbm.get(v); } );
                                       return p;
                                   }
                                 );
        },
        
        //  支払方法区分変更時の処理 2016/09/06 ADD Matsuyama
        changePayMethodCd: function(ev) {
            app.model.set('pay_method_cd', ev.val);
            _hoseiData(ev.val);
        },

        initialize: function() {

            Backbone.Validation.bind(this);

            this.listenTo(this.model, 'change', this.recalcAndRender); // for zei calc and cancel button

            this.listenTo(this.collection, 'add', this.addOne);
            this.listenTo(this.collection, 'change remove add', this.recalc);
            this.listenTo(this.collection, 'change remove add', this.render);
            this.listenTo(this.collection, 'reset', this.resetCol);

            this.listenTo(Backbone, 'update.sekyu', this.updateSekyu);
            this.listenTo(Backbone, 'update.sekyu.zip', this.updateSekyuZip);

            this.listenTo(Backbone, 'update.nonyu', this.updateNonyu);
            this.listenTo(Backbone, 'update.nonyu.zip', this.updateNonyuZip);

            // this.dtlFooter = this.$('#dtl-table-id tfoot');
            // this.dtlFooter.hide();

            this.recalc();
            this.render();

            // 参照専用モード
            if ( utils.isReadOnlyCtxt() ) {
                if ( this.isSelectedCtxt() ) {
                    this.btnEnabled('#btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
                } else {
                    this.btnDisabled('#btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
                }
                return this;
            }

            // 更新か新規か
            if ( this.isSelectedCtxt() ) { // 更新の場合
                // 受注日変更不可
                //$('#juchu_ymd').attr('disabled', 'disabled').datepicker("option", "disabled", true).end();
                //$('.label[data-ref-rel="#juchu_ymd"]').addClass("my-disabled").attr("disabled", "disabled");
                // 受注日変更可
                //$('#juchu_ymd').removeAttr('disabled').datepicker("option", "disabled", false).end();
                //$('.label[data-ref-rel="#juchu_ymd"]').removeClass("my-disabled").removeAttr("disabled");;

                this.btnEnabled('#btn_save, #btn_copy, #btn_delete');
                if ( this.isChanged() ) {
                    this.btnDisabled('#btn_print, #btn_new, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
                } else {
                    this.btnEnabled('#btn_print, #btn_new, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
                }
            }
            else { // 新規の場合
                // 受注日変更可
                //$('#juchu_ymd').removeAttr('disabled').datepicker("option", "disabled", false).end();
                //$('.label[data-ref-rel="#juchu_ymd"]').removeClass("my-disabled").removeAttr("disabled");;

                this.btnDisabled('#btn_copy, #btn_delete, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
                this.btnEnabled('#btn_save, #btn_new');
            }

            // 変更されているか
            if ( this.isChanged() ) {
                this.btnEnabled('#btn_cancel');
            } else {
                this.btnDisabled('#btn_cancel');
            }
        },

        updateSekyu: function(sekyuData) {
            // console.log( 'updateSekyu called' + JSON.stringify(sekyuData) );
            var m = this.model;
            _.map( ( 'sekyu_cd  sekyu_nm  sekyu_knm  sekyu_yubin_no  sekyu_addr1 1 kaiin_kbn_nm kaiin_sbt_nm ' + 
                     ' sekyu_addr2  sekyu_tel  sekyu_fax  sekyu_biko1  sekyu_biko2  seko_no sekyu_soufu_nm ' +
                     '' ).split(/\s+/),
                   function(k) { 
                       m.set( k, sekyuData[ k ] );
                   } );
            $('#sekyu_cd, #sekyu_nm').msiErrClear();
        },

        updateSekyuZip: function(data) {
            if ( data === null ) { return; } // only closing, not setting.
            // console.log( 'updateSekyuZip called' + JSON.stringify(data) );
            var m = this.model;
            _.map( 'yubin_no  addr1'.split(/\s+/),
                   function(k) { m.set( 'sekyu_' + k, data[ k ] ); } );
            // $('#sekyu_cd, #sekyu_nm').msiErrClear();
            $.msiJqlib.msiPickupNewEndDialog();
        },

        updateNonyu: function(nonyuData) {
            // console.log( 'updateNonyu called' + JSON.stringify(nonyuData) );
            var m = this.model;
            _.map( ( 'nonyu_cd  nonyu_nm  nonyu_knm  nonyu_yubin_no  nonyu_addr1 ' + 
                     ' nonyu_addr2  nonyu_tel  nonyu_fax  nonyu_biko1  nonyu_biko2 nonyu_delivery_kbn ' +
                     '' ).split(/\s+/),
                   function(k) { 
                       m.set( k, nonyuData[ k ] );
                   } );
            $('#nonyu_cd, #nonyu_nm').msiErrClear();
        },

        updateNonyuZip: function(data) {
            if ( data === null ) { return; } // only closing, not setting.
            // console.log( 'updateNonyuZip called' + JSON.stringify(data) );
            var m = this.model;
            _.map( 'yubin_no  addr1'.split(/\s+/),
                   function(k) { m.set( 'nonyu_' + k, data[ k ] ); } );
            // $('#nonyu_cd, #nonyu_nm').msiErrClear();
            $.msiJqlib.msiPickupNewEndDialog();
        },

        resetCol: function(collection, options) {
            var $table = this.$("#dtl-table-id");
            $table.find('tbody').remove();
            _.each( collection.models, function(m) {
                var v = new MeisaiView({model: m});
                $table.append(v.render().el);
                if(m.get('line_no') > -1){
                    if(!$.msiJqlib.isNullEx2(m.get('selected')) && m.get('selected') == '1'){
                        var row = $table.find('tbody tr .row');
                        $(row[m.get('line_no') -1]).addClass('row-selected');
                    }
                }
            } );

            this.recalc();
            this.render();
        },

        createMeisai: function(e) {
            this.collection.add( new MeisaiModel );
        },

        addOne: function(meisai, list, options) {
            var v = new MeisaiView({model: meisai});

            var line_no = meisai.get('line_no'), off;
            if ( _.has(options, 'at') ) {
                off = options['at'];
            }
            
            // console.log( 'line_no=>' + line_no + ' of=>' + off + ' addOne *** => ' + JSON.stringify(meisai) );

            if ( off === undefined ) {
                this.$("#dtl-table-id").append( v.render().el );
            } else if ( off > 0 ) {
                this.$("#dtl-table-id").find('tbody').eq( off -1 ).after( v.render().el );
            } else { // off === 0
                this.$("#dtl-table-id").prepend( v.render().el );
            }
        },

        recalcAndRender: function() {
            this.recalc();
            this.render();
        },

        _getZeiRitu: function(zei_cd) {
            var ritu = 0,
                d = _.findWhere( _zeiMst, { id: ""+zei_cd } );
            if ( d ) ritu = parseInt(d.text, 10); // x% OK
            // console.log( 'zei_cd=>' + zei_cd + ' ritu=>' + ritu + '  mydata.mst.zei=>' + JSON.stringify(_zeiMst) );
            return ritu;
        },

        // // 端数処理関数
        // //     端数区分  0:切捨て 1:四捨五入 2:切上げ
        // _getZeiHasuFunc: function(zei_cd) {
        //     var hasu = 0,
        //         d = _.findWhere( _zeiMst, { id: ""+zei_cd } );
        //     if ( d ) hasu = d.zei_hasu_kbn;
        //     // console.log( 'zei_cd=>' + zei_cd + ' hasu=>' + hasu + '  mydata.mst.zei=>' + JSON.stringify(_zeiMst) );
        //     if      ( hasu == 1 ) return Math.round;
        //     else if ( hasu == 2 ) return Math.ceil;
        //     else                  return Math.floor;
        // },
        // 端数処理関数 マイナス金額対応版
        //     端数区分  0:切捨て 1:四捨五入 2:切上げ
        _getZeiHasuFunc2: function (zei_cd, prc) {
            var hasu = 0;
            var d = _.findWhere(_zeiMst, {id: "" + zei_cd});
            if (d) {
                hasu = d.zei_hasu_kbn
            }
            if (prc >= 0) {
                if (hasu == 1) {
                    return Math.round;
                } else if (hasu == 2) {
                    return Math.ceil;
                } else {
                    return Math.floor;
                }
            } else {
                if (hasu == 1) {
                    return Math.round;
                } else if (hasu == 2) {
                    return Math.floor;
                } else {
                    return Math.ceil;
                }
            }
        },

        // recalc 新バージョン 軽減税率対応 keigen
        recalc: function() {
            var totals = {};
            var _zei_meisai_sum = 0;
            var t = this;
            var isSetGaku = false; // 金額が設定されたら真にするフラグ
            var hTotalGakuSoto = {};
            var hTotalGakuUchi = {};
            var totalGakuHi = 0;
            // 税CDごとに集計  total_genka,total_arari 処理はそのまま残す
            this.collection.each( function(m, i) {
                m.set( 'line_no', i+1 );
                var zei_cd = +m.get('zei_cd');
                var zei_kbn = +m.get('zei_kbn');
                var ritu = m.get('zei_kbn') == 0 ? 0 : +t._getZeiRitu( zei_cd );
                if ( ! $.msiJqlib.isNullEx2(m.get('juchu_gaku_cal')) ) {
                    if ( zei_kbn == 2 ) { // 外税
                        if ( !_.has(hTotalGakuSoto, zei_cd) ) { hTotalGakuSoto[zei_cd] = 0; }
                        hTotalGakuSoto[zei_cd] += $.msiJqlib.bbModelToNum(m, 'juchu_gaku_cal');
                    } else if ( zei_kbn == 1 ) { // 内税
                        if ( !_.has(hTotalGakuUchi, zei_cd) ) { hTotalGakuUchi[zei_cd] = 0; }
                        hTotalGakuUchi[zei_cd] += $.msiJqlib.bbModelToNum(m, 'juchu_gaku_cal');
                    } else { // 非課税
                        totalGakuHi += $.msiJqlib.bbModelToNum(m, 'juchu_gaku_cal');
                    }
                    isSetGaku = true;
                }
                if ( ! $.msiJqlib.isNullEx2(m.get('genka_gaku')) ) {
                    if ( $.msiJqlib.isNullEx2(totals.total_genka) ) totals.total_genka = 0;
                    totals.total_genka   += $.msiJqlib.bbModelToNum(m, 'genka_gaku');
                }
                if ( ! $.msiJqlib.isNullEx2(m.get('arari')) ) {
                    if ( $.msiJqlib.isNullEx2(totals.total_arari) ) totals.total_arari = 0;
                    totals.total_arari   += $.msiJqlib.bbModelToNum(m, 'arari');
                }
            } );

            var hasuFunc;
            if ( isSetGaku ) { // 金額が設定されていれば
                var myTotalKingaku = 0;
                var myTotalZei     = 0;
                var myTotalZeiKomi = 0;
                // 外税分
                _.each( hTotalGakuSoto, function(val, zei_cd) {
                    hasuFunc = function(prc) { return t._getZeiHasuFunc2(zei_cd, prc)(prc); };
                    var ritu = t._getZeiRitu( zei_cd );
                    var zei = +hasuFunc( val * ritu / 100 );
                    myTotalZei += zei;
                    myTotalKingaku += val;
                    myTotalZeiKomi += val + zei;
                } );
                // 内税分
                _.each( hTotalGakuUchi, function(val, zei_cd) {
                    hasuFunc = function(prc) { return t._getZeiHasuFunc2(zei_cd, prc)(prc); };
                    var ritu = t._getZeiRitu( zei_cd );
                    var zei = +hasuFunc( val * ritu /(100+ritu) );
                    myTotalZei += zei;
                    myTotalKingaku += val;
                    myTotalZeiKomi += val;
                } );
                // 非課税分
                myTotalKingaku += totalGakuHi;
                myTotalZeiKomi += totalGakuHi;

                totals.total_kingaku = myTotalKingaku;
                totals.total_zei     = myTotalZei;
                totals.total_zeikomi = myTotalZeiKomi;
            }

            var $m = this.model;
            'total_kingaku total_genka total_arari total_zei total_zeikomi'.split(/\s+/).forEach(
                function(k) {
                    if ( ! $.msiJqlib.isNullEx2(totals[k]) ) {
                        $m.set(k, totals[k]);
                    } else {
                        $m.unset(k);
                    }
                    // console.log( '**** ' + k + '==>' + $m.get(k) );
                } );

        }, // recalc
        
        render: function() {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind( this, Backbone.Validation.msi_err_setting_std() );

            if (this.collection.length) {
                // this.dtlFooter.show();
            } else {
                this.collection.add( new MeisaiModel ); // １行は表示する
                // this.dtlFooter.hide();
            }

            this.stickit();

            utils.decoMinus( this, 'total_kingaku total_genka total_zei total_zeikomi' );
            
            // スクロール調整
            this.scrollAdj();

            // select2 調整。XXX 
            this.select2Adj(); // patch

            return this;
        },

        // スクロールバー表示調整
        scrollAdj: function() {
            var $list = this.$('.items .list'),
                $header = this.$('.items .header'),
                sc_of,
                sc_w,
                hh;
            // console.log( '$list.scrollHeight=>' + $list[0].scrollHeight + ' $list.clientHeight=>' +  $list[0].clientHeight );
            if ( $list[0].scrollHeight === $list[0].clientHeight ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                sc_w = '44.4%';
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            } else {
                sc_of = 'scroll';
                sc_w = '43.3%';
                hh = $header.height();
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                // console.log( 'hh=>' + hh + ' height=>' + $header.height() );
                $header.height(hh); // for Chrome. XXX
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            }
        },

        // select2 調整
        select2Adj: function() {
            return; // XXX
            var bbv = this;
            _.each( 'kaishu_kbn tax_kbn zei_cd'.split(/\s+/),
                    function(k) {
                        var $el = bbv.$('#' + k);
                        var $s2el = bbv.$('#s2id_' + k);
                        if ( $s2el.length ) {
                            $el.select2("val", bbv.model.get(k));
                        }
                    } );
        },

        btnDisabled: function(elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },

        btnEnabled: function(elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },

        isInputOk: function() {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if ( result ) {
                _.each( result, function(v, k) {
                    aMsg.push( v );
                    // console.log( '*** err ' + k + ': ' + v );
                } );
            }
            this.collection.each( function(m, i) {
                var resLine = m.validate();
                if ( resLine ) {
                    line = i + 1;
                    _.each( resLine, function(v, k) {
                        aMsg.push( '明細' + line + '行目 ' + v );
                        // console.log( '*** err ' + '明細' + line + '行目 ' + k + ': ' + v );
                    } );
                }
            } );

            // NG
            if ( aMsg.length > 0 ) {
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },

        doCheck: function(ev) {
            if ( this.isInputOk() ) {
                if (ev) ev.stopPropagation();
                msiLib2.showInfo( '入力チェック OK' );
            }
        },

        doClearErr: function() {
            this.clearErr();
        },

        clearErr: function() {
            this.$el.msiErrClearAll();
        },

        doCopy: function(ev) {
            // console.log( 'doCopy called' );
            if ( this.isChanged() ) {
                if ( ! confirm(msg01) ) {
                    return;
                }
            }

            var newDataApp = app.model.toJSON(); // not JSON, but Object
            var newDataCol = app.collection.toJSON(); // not JSON, but Object
            _.each( newDataCol, function(m) { m.msi_no = null; } ); // 明細番号は全クリア
            newDataApp.denpyo_no = null; // 伝票番号はクリア
            newDataApp.juchu_ymd = msiLib2.getStdDate(); // 受注日は現在日
            $.msiJqlib.getZeiCdFromDate().then( function(cd) {
                // console.log( 'juchu.denpyo.order.js.. cd=>' + cd );
                newDataApp.zei_cd = cd; // 現在日の税コード
                _resetData( newDataApp, newDataCol );
            } );
        },

        doSave: function(mode) {
            if ( ! this.isInputOk() ) {
                return;
            }

//            if ( this.isSelectedCtxt() && !this.isChanged() ) { // 更新で変更なし
//                if ( ! confirm('データが変更されていませんが、更新しますか？') ) {
//                    return;
//                }
//            }

            this.clearErr();

            this.model.set('moushi_kbn',3); // 単品
            this.model.set('mode',1);       // モード 1:更新 2:削除
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());
            this.btnEnabled('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
            // 売上計上日
            if ($.msiJqlib.isNullEx2(this.model.get("keijo_ymd"))) {
               this.exeSave(dataAppJson, dataColJson, mode);
            } else {
               this.exeCheck(dataAppJson, dataColJson,mode);
            }
            this.btnDisabled('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
        },
        exeCheck: function(dataAppJson, dataColJson, mode) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkgetujifix',
                data: {
                    dataAppJson: dataAppJson
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        that.exeSave(dataAppJson, dataColJson, mode);
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing( false );
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing( true );
                        that.exeSave(dataAppJson, dataColJson, mode);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        exeSave: function(dataAppJson, dataColJson, mode) {
            var $this = this;

            $.ajax({ // url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '保存',
                },
                type: 'POST',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        _.each(meisaiList.models, function(v,k){
                            var line_no1 = v.get('line_no');
                            var selected = v.get('selected');
                            _.each( mydata.dataCol, function(v2, k2) {
                                var line_no2 = v2.line_no;
                                if(line_no1 == line_no2){
                                    mydata.dataCol[k2].selected = selected;
                                }
                            });
                        });
                        // 登録の場合は、新規画面へ推移する
                        if ( /新規登録/.test(mydata.msg) ) {
 							if (mode == "1") {  // 納品書印刷
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doPrint_sub(mydata.dataApp);
                                return;
							}	
 							if (mode == "2") {  // 請求書
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doPrint_sei_sub(mydata.dataApp);
                                return;
							}
 							if (mode == "3") {  // 入金・領収・請求書印刷
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doPrint_nyu_ryo_sei_sub(mydata.dataApp);
                                return;
							}
                            if (mode == "4") {          // 入金・領収・請求書印刷
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doPrint_konbini_sub(mydata.dataApp);
                                return;
                            }
                            if (mode == "5") {          // 納品書（サンメンバーズ）
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doPrint_nohin_sub(mydata.dataApp);
                                return;
                            }
                            if (mode == "6") {          // CSV出力
                                _resetData( mydata.dataApp, mydata.dataCol );
                                $this.doCsv_sub();
                                return;
                            }
                            $this._doNew( mydata.msg );
                        }
                        // console.log( 'dump dataApp *** ==>' + JSON.stringify(mydata.dataApp) );
                        // console.log( 'dump dataCol *** ==>' + JSON.stringify(mydata.dataCol) );
						msiLib2.showInfo( mydata.msg );
                        _resetData( mydata.dataApp, mydata.dataCol );
                        //msiLib2.showInfo( mydata.msg );
                        if (mode == "1") {          // 納品書印刷
							$this.doPrint_sub(mydata.dataApp);
                            return;
						}	
                        if (mode == "2") {          // 請求書
                            $this.doPrint_sei_sub(mydata.dataApp);
                            return;
                        }	
                        if (mode == "3") {          // 入金・領収・請求書印刷
                            $this.doPrint_nyu_ryo_sei_sub(mydata.dataApp);
                            return;
                        }
                        if (mode == "4") {          // 入金・領収・請求書印刷
                            $this.doPrint_konbini_sub(mydata.dataApp);
                            return;
                        }
                        if (mode == "5") {          // 納品書（サンメンバーズ）
                            $this.doPrint_nohin_sub(mydata.dataApp);
                            return;
                        }
                        if (mode == "6") {          // CSV出力
                            $this.doCsv_sub();
                            return;
                        }
                        $this._doNew( mydata.msg );
                    } else {
                        msiLib2.showErr( mydata.msg );
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                }
            });
        },
        doPrint: function(ev) {
            // console.log( 'doPrint() called' );
            if (this.doSave("1") === false) {
                return false; 
            }; 
            return;
        },
        doPrint_sub: function() {
            var url = '/saiken/pdf1102',
                bbv = this,
                denpyo_no = this.model.get('denpyo_no');

            this.clearErr();

            // console.log( 'uri_den_no_arr=> ' + JSON.stringify(uri_den_no_arr) );
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + url,
                data: {
                    denpyo_no: denpyo_no,
                    preview: 'off',
                    out_type: 1,
                },
            });

            return;
        },
        // 印刷処理（サンメンバーズ）
        doPrint_nohin: function(ev) {
            // console.log( 'doPrint() called' );
            if (this.doSave("5") === false) {
                return false; 
            }; 
            return;
        },
        doPrint_nohin_sub: function() {
            var url = '/saiken/pdf1102',
                bbv = this,
                denpyo_no = this.model.get('denpyo_no');

            this.clearErr();

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + url,
                data: {
                    denpyo_no: denpyo_no,
                    preview: 'off',
                    out_type: 1,
                },
            });

            return;
        },

		// 印刷(請求書) 2016/05/04 ADD Kayo
        doPrint_sei: function() {
            if (this.doSave("2") === false) {
                return false; 
            }; 
            return;
        },    
        doPrint_sei_sub: function(dataApp) {
            var bbv = this,
                denpyo_no = dataApp.denpyo_no;

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/saiken/Pdf1101/index',
                data: {
                    preview: 'off',
                    uri_den_no: denpyo_no,
                    out_type: 1,
                    output_kbn: 1
                },
            });
            return;
        },

        // 入金・領収・請求書印刷
        doPrint_nyu_ryo_sei: function() {
            if (this.doSave("3") === false) {
                return false; 
            }; 
            return;
        },
        doPrint_nyu_ryo_sei_sub: function(dataApp) {
            var  denpyo_no = dataApp.denpyo_no;
            var  nohinsho_pirint_kbn = dataApp.nohinsho_pirint_kbn;

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl()  + '/saiken/seikyu3/nyuryoseihakko',
                data: {
                    preview: 'off',
                    uri_den_no: denpyo_no,
                    out_type: 3,
                    nohinsho_pirint_kbn:nohinsho_pirint_kbn
                },
            });
            return;
        },
        // コンビニ請求書印刷
        doPrint_konbini: function() {
            if (this.doSave("4") === false) {
                return false; 
            }; 
            return;
        },
        doPrint_konbini_sub: function(dataApp) {
            s_print.showPrinter({
                onSelect: function (pm) {
                    var denpyo_no = dataApp.denpyo_no;
                    msiLib2.fileDlAjax({
                        url: $.msiJqlib.baseUrl() + '/saiken/seikyu3/konbinihakko',
                        data: {
                            printer_cd: pm.get("printer_cd"),
                            preview: 'off',
                            uri_den_no: denpyo_no,
                            out_type: 3,
                        },
                    });
                }
            });
            return;
        },

        doDelete: function(ev) {
            // console.log( 'doDelete called' );
            if ( false &&  ! this.isInputOk() ) {
                return;
            }

            this.clearErr();

            if ( ! confirm('削除してよろしいですか？') ) {
                return;
            }

            this.model.set('moushi_kbn',3); // 単品
            this.model.set('mode',2);       // モード 1:更新 2:削除
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());
            this.btnEnabled('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');
            this.exeDelCheck(dataAppJson, dataColJson);
            this.btnDisabled('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini, #btn_print_nohin');


        },
        exeDelCheck: function(dataAppJson, dataColJson) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkgetujifix',
                data: {
                    dataAppJson: dataAppJson
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        that.exeDelete(dataAppJson, dataColJson);
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing( false );
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing( true );
                        that.exeDelete(dataAppJson, dataColJson);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        exeDelete: function(dataAppJson, dataColJson) {
            var $this = this;
            $.ajax({ // url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '削除',
                },
                type: 'POST',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        _resetData( mydata.dataApp, mydata.dataCol );
                        msiLib2.showInfo( mydata.msg );
                    } else {
                        msiLib2.showErr( mydata.msg );
                    }
                    // console.log( 'ajax res msg==>' + mydata.msg );
                }
            });
        },

        _doNew: function(msg) {
            var _msg = msg;
            $.ajax({
                data: {
                    action: '新規',
                },
                type: 'POST',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        _resetData( mydata.dataApp, mydata.dataCol );
                        // msiLib2.showInfo( mydata.msg );
                        if ( _msg ) {
                            msiLib2.showInfo( _msg );
                        }
                    } else {
                        msiLib2.showErr( mydata.msg );
                    }
                }
            });
        },

        doNew: function(ev) {
            if ( ! confirm('新規作成画面へ移行します. よろしいですか？') ) {
                return;
            }
            this._doNew();
        },

        doCancel: function(ev) {
            // console.log( 'doCancel called' );

            if ( ! confirm('初期状態に戻してよろしいですか？') ) {
                return;
            }

            if (ev) ev.stopPropagation();

            if ( orgDataApp && orgDataCol ) {
                _resetData( orgDataApp, orgDataCol );
            }

            msiLib2.showInfo( '初期状態に戻しました' );
        },

        // 選択モード(更新モード)か否か
        isSelectedCtxt: function() { // aka. isUpdCtxt
            var denpyo_no = this.model.get('denpyo_no');
            if ( denpyo_no ) {
                return true;
            }
            return false;
        },

        // 初期状態から変更されているか
        isChanged: function() {
            if ( !orgDataApp || !orgDataCol ||
                 ( $.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) &&
                   $.msiJqlib.isEqual(orgDataCol, this.collection.toJSON()) ) ) {
                return false;
            }
            return true;
        },
        // check トグル
        toggleChkAll: function() {
            var _chkAll = !this.model.get('_chkAll');
            this.model.set('_chkAll', _chkAll);
            if ( _chkAll ) {
                this.$('.chkAllToggle').addClass('selected');
                this.checkAll();
            } else {
                this.$('.chkAllToggle').removeClass('selected');
                this.uncheckAll();
            }
        },
        // 行全選択
        checkAll: function() {
            this._checkAll(true);
        },
        // 行全選択解除
        uncheckAll: function() {
            this._checkAll(false);
        },
        // 行全設定
        _checkAll: function(isSet) {
            if(isSet){
                _.each(meisaiList.models, function(v, k){
                    meisaiList.models[k].set("selected", "1");
                });
                $('.list .row').addClass('row-selected');
            }else{
                _.each(meisaiList.models, function(v, k){
                    meisaiList.models[k].set("selected", "0");
                });
                $('.list .row').removeClass('row-selected');
            }
            this.model.trigger('change');
        },
        doCsv: function() {
            if (this.doSave("6") === false) {
                return false; 
            };
            return;
        },
        doCsv_sub: function() {
            var dataAppJson = JSON.stringify(app.model);
            var dataColJson = JSON.stringify(meisaiList);
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/denpyo/csv',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson
                },
            });
        },
        bindings: {
            '#kaishu_kbn': {
                observe: 'kaishu_kbn',
                afterUpdate: utils.afterUpdateSel2,
            },
            '#furikomi_kbn': {
                observe: 'v_free5',
                afterUpdate: utils.afterUpdateSel2
            },
            '#juchusaki_kbn': {
                observe: 'juchusaki_kbn',
                afterUpdate: utils.afterUpdateSel2,
            },
            '#pay_method_cd': {
                observe: 'pay_method_cd',
                afterUpdate: utils.afterUpdateSel2,
                onSet: function(value, options) {
                    if ( value == '10' || value == '20' ) { // 葬家(10),当日(20)
                        app.model.set('kaishu_kbn', 1); // 現売(1)
                    } else if ( value == '30' ) { // 振込(30)
                        app.model.set('kaishu_kbn', 0); // 売掛(0)
                    } else if ( value == '40' ) { // クレジット(40)
                        app.model.set('kaishu_kbn', 2); // クレジット(2)
                    } else if ( value == '50' || value == '60' ) { // グループ(50),社内(60)
                        app.model.set('kaishu_kbn', 9); // その他(9)
                    } else if ( value == '99' ) { // その他(99)
                        app.model.set('kaishu_kbn', 9); // その他(9)
                    }
                    return value;
                },
            },
            '#tax_kbn': {
                observe: 'tax_kbn',
                afterUpdate: utils.afterUpdateSel2,
                updateModel: function(val, event, options) {
                    // 行データが入力されていたら、税区分変更は不可
                    if ( app.collection.length > 1 || app.collection.at(0).get('shohin_cd') ) {
                        msiLib2.showErr( '既存データがあるので税区分変更はできません' );
                        $('#tax_kbn').select2("val", app.model.get('tax_kbn'));
                        return false;
                    }
                    return val;
                }
            },
            '#zei_cd': {
                observe: 'zei_cd',
                afterUpdate: utils.afterUpdateSel2
            },
            '#denpyo_no': 'denpyo_no',
            '#juchu_ymd': {
                observe: 'juchu_ymd',
                events: ['change']
            },
            '#keijo_ymd': {
                observe: 'keijo_ymd',
                events: ['change']
            },
            '#sekyu_cd': 'sekyu_cd',
            '#sekyu_nm': 'sekyu_nm',
            '#kaiin_kbn_nm': 'kaiin_kbn_nm',
            '#kaiin_sbt_nm': 'kaiin_sbt_nm',
            '#nonyu_cd': 'nonyu_cd',
            '#nonyu_nm': 'nonyu_nm',
            '#nonyu_dt': {                  // 2015/07/31 UPD Kayo
                observe: 'nonyu_dt',
                events: ['change']
            },
            '#kaishu_ymd': {               // 2015/07/31 UPD Kayo
                observe: 'kaishu_ymd',
                events: ['change']
            },
            '#tanto_cd': 'tanto_cd',
            '#tanto_nm': 'tanto_nm',
            '#hall_cd': {
                observe:'bumon_cd',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '#denpyo_biko1': {
                observe: 'denpyo_biko1',
                events: ['change']
            },
            '#denpyo_biko2': {
                observe: 'denpyo_biko2',
                events: ['change']
            },
            '#total_kingaku': {
                observe: 'total_kingaku',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#total_genka': {
                observe: 'total_genka',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#total_zei': {
                observe: 'total_zei',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#total_zeikomi': {
                observe: 'total_zeikomi',
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '#cre_user':    'cre_user',           // 作成者 日時    2016/06/16 ADD Kayo
            '#mod_user':    'mod_user',           // 更新者 日時    2016/06/16 ADD Kayo
            '#seikyu_user': 'seikyu_user',        // 請求者 日時    2016/07/01 ADD Kayo
            '#zaimu_rendo_user':'zaimu_rendo_user', // 財務連動 日時    2020/08/21 ADD Kayo
            '#nohinsho_pirint_kbn': {           // 納品書発行区分 2016/07/07 ADD Kayo                                  
                observe: 'nohinsho_pirint_kbn',
                afterUpdate: utils.afterUpdateSel2,
                onSet: function(value, options) {
                    if ( value == '0' ) { // 発行なし
                        $("#btn_print_nohin").hide();
                    } else {              // 発行あり
                        $("#btn_print_nohin").hide();
                        $("#btn_print_nohin").show();
                    }
                    return value;
                }    
            },
            '#uketsuke_no': 'uketsuke_no',
       },
    }); // AppView

    var app;
    var orgDataApp, orgDataCol;
    var _resetData;
    var minDataApp, minDataCol, min2DataApp, min2DataCol;
    var _setInitData;
    var shohinSearchCondData = {}; // 前回商品検索大域用
    var _zeiMst = [];
    var orgData = null;
    var _hoseiData;

    app = new AppView( { model: new AppModel, collection: meisaiList } );

    _resetData = function( myApp, myCol ) {
        _(msiGlobalObj.bbExports.SekyuModel.myKeys).each( function(k) { app.model.set(k, null); } );
        _(msiGlobalObj.bbExports.NonyuModel.myKeys).each( function(k) { app.model.set(k, null); } );
        // console.log( '_resetData myApp=>' + JSON.stringify(myApp) );
        app.model.set( myApp );
        if ( _.has(orgData, 'df_kouza_no') && $.msiJqlib.isNullEx2(app.model.get("v_free5"))) {
            app.model.set("v_free5",orgData.df_kouza_no);
        }
        app.collection.reset( myCol );
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        orgDataCol = app.collection.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();
        if ( myApp.nohinsho_pirint_kbn == '0' ) {   // 発行なし
            $("#btn_print_nohin").hide();
        } else {                                    // 発行あり
            $("#btn_print_nohin").hide();
            $("#btn_print_nohin").show();
        }
        app.model.trigger('change'); // patch
    };
    
    //  データ補正 2016/09/26 ADD Matsuyama
    _hoseiData = function(selectCd) {
        var bf_pay_method_cd = app.model.get('bf_pay_method_cd');
        // 補正対象の場合は単価を補正する
        if ($.inArray(selectCd, HOSEI_TAISHO_LIST) > -1) {
            _.each(app.collection.models, function (m) {
                var tanka = m.get('juchu_tanka');
                m.set('juchu_tanka', 0);
                if (tanka > 0) {
                    m.set('bf_juchu_tanka', tanka);
                }
                if (!$.msiJqlib.isNullEx2(m.get('nebiki_gaku'))) {
                    m.set('nebiki_gaku', 0);
                }
            });
            $('.nebiki_gaku').prop('disabled', true);
        } else {
            // 前回の選択で補正を行っていた場合は元のデータに戻す
            if ($.inArray(bf_pay_method_cd, HOSEI_TAISHO_LIST) > -1) {
                _.each(app.collection.models, function (m) {
                    m.set('juchu_tanka', m.get('bf_juchu_tanka'));
                    m.set('bf_juchu_tanka', 0);
                });
                $('.nebiki_gaku').prop('disabled', false);
            }
        }
        app.model.set('bf_pay_method_cd', selectCd);
    };

    _setInitData = function() {
        var mydata = msiLib2.getJsonFromHtml( $('#my-data-init-id') );
        _zeiMst = mydata.mst.zei;
        orgData = mydata;
        // 振込先設定
        if ( _.has(mydata, 'furikomi_kbn') ) {
            $.msiJqlib.setSelect2Com1($("#furikomi_kbn"), ({data: mydata.furikomi_kbn}));
        }
        // console.log( 'mydata.mst.zei=>' + JSON.stringify(_zeiMst) );

        if ( _.has(mydata.dataApp, '_reload_denpyo_no') ) { // cf. denpyoHelper()
            // 更新や参照で denpyo_no が指定されている場合
            $.ajax({ 
                data: {
                    denpyo_no: mydata.dataApp['_reload_denpyo_no'],
                    action: '表示',
                },
                type: 'POST',
                success: function( mydata ) {
                    if ( mydata.status == 'OK' ) {
                        _resetData( mydata.dataApp, mydata.dataCol );
                        msiLib2.setHallCd( mydata.dataApp.bumon_cd ); // XXX auto?
                        app.select2Adj(); // patch
                    } else {
                        msiLib2.showErr( mydata.msg );
                    }
                }
            });
            return;
        } else {
            // 参照専用時は pickup 画面を表示する
            if ( utils.isReadOnlyCtxt() ) {
                setTimeout( function() { app.denpyoHelper(); }, 1500 );
            }

           _resetData( mydata.dataApp, mydata.dataCol );
       }
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function() {
        if ( app.isChanged() ) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on( 'resize', function() { app.render(); } );

    // 参照専用
    if ( $('#my-form-id').hasClass('my-ctxt-readonly') ) {
        var $form = $('#my-form-id');
        $form.msiInputReadonly()
            .msiCalReadonly()
            .find('.my-readonly-hidden').hide();

        $form.find('.my-readonly-visible').show();

        $('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_cancel, #btn_print, #btn_print_sei, #btn_print_nyu_ryo_sei, #btn_print_konbini').hide();
        // .attr("disabled", "disabled");

        utils.setReadOnlyCtxt();
    }

    // 付加的な disabled 付与
    $('#order').find('.label[data-ref-rel]').each( function() {
        // console.log( '.label[data-ref-rel]->' + $(this).attr('class') );
        var $this = $(this),
            ref = $this.attr('data-ref-rel'),
            $el = $( ref, $this.parent() );
        // console.log( '.label[data-ref-rel]->' + $(this).attr('class') + ' ref=>' + $el.attr('class') + ' disabled=>' + $el.attr('disabled'));
        if ( $el.attr('disabled') ) { // || $el.attr('readonly') ) {
            $(this).addClass("my-disabled").attr("disabled", "disabled");
        } else {
            $(this).removeClass("my-disabled").removeAttr("disabled");;
        }
    } );

    $.msiJqlib.initDone( function() { _setInitData(); } ); // 処理完了

    $('#order').fadeIn('fast'); // ちらつきのごまかし

} );
