<?php
  /**
   * PDF タイムスケジュール登録リスト
   *
   * @category	 App
   * @package	 controller
   * <AUTHOR> Kayo
   * @since 	 2016/02/07
   * @filesource 
   */

  /**
   * PDF タイムスケジュール登録リスト
   *
   * @category	 App
   * @package	 controller
   * <AUTHOR> Kayo
   * @since 	 2016/02/07
   */
class Mstr_Pdf1415Controller extends Zend_Controller_Action
{
	private static $title1 = 'タイムスケジュール登録リスト';
	private static $sourceFileName = 'pdf_tmpl/1415.pdf';
	private static $row_height = 11.28;
	private static $row_count = 43;
    
	/**
	 * アクション
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 */
	public function indexAction()
	{
		$params  = Msi_Sys_Utils::webInputs();
		$dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

		$csv = false;
		// CSV出力
		if (array_key_exists('csv', $dataAppAry))	{
			$csv = $dataAppAry['csv'];
		}

		$db = Msi_Sys_DbManager::getMyDb();
        $title = self::$title1;

		if ($csv) {
			$csvData = $this->getCsv($db);
			if (!isset($csvData)) {
                App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
				return;
			}
			$buf = Msi_Sys_Utils::csvOutString($csvData);
			Msi_Sys_Utils::out2way1( $buf, $title.'.csv', 'text/csv' );
		} else {
			$pdfObj = new App_Pdf( $title, 'A4', 'L' );
			$ret = $this->outData($pdfObj, $db, $prn_ymd, $printkbn);
			if ($ret != App_PdfKanriLib::STATUS_OK) {
				App_PdfKanriLib::err($ret);
				return;
			}
			$pdfObj->download();
		}
	}
    
	/**
	 * PDF出力メイン
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db	データベース
	 * @param string $prn_ymd	印刷年月日
	 * @return	   viod
	 */
	private function outData($pdfObj, $db, $prn_ymd)
	{
		$pdfObj->set_default_font_size(8);
		//マイナス値は赤字出力を設定
		$pdfObj->set_default_minus_font_color('red');

        $rec = $this->shohin_mst_select($db);
		if (count($rec) == 0) {
			return App_PdfKanriLib::STATUS_NODATA;
		}

        $this->outDataMsi($pdfObj, $rec, $rep_kbn);

		if ($pdfObj->getNumPages() > 0) {
			$pdfObj->setPage(1);
		} else {
			$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName);
		}

		$kaisya = DataMapper_PdfCommon::getKaisya( $db, array('kaisya_cd', 'kaisya_lnm') );
		if (!isset($kaisya)) { return; }

		// ページ等を出力
		$ymd = null;
		if (isset($ymd_st) && isset($ymd_ed)) {
			$ymd = date('Y年m月d日', strtotime($ymd_st)).'　～　'.date('Y年m月d日', strtotime($ymd_ed));
		} else if (isset($ymd_st)) {
			$ymd = date('Y年m月d日', strtotime($ymd_st)).'　～　';
		} else if (isset($ymd_ed)) {
			$ymd = '　～　'.date('Y年m月d日', strtotime($ymd_ed));
		}
		$pdfObj->header_out(array(
			array('x' =>  45, 'y' =>  30, 'width' => 160, 'height' => 15, 'value' => $kaisya['kaisya_cd'].'　'.$kaisya['kaisya_lnm']), // 会社
			array('x' => 712, 'y' =>  26, 'width' =>  55, 'height' => 15, 'value' => $prn_ymd, 'format' => 'Y年m月d日', 'type' => 'ymd'), // 作成日
			array('x' =>  45, 'y' => 576, 'width' => 780, 'height' => 15, 'type' => 'page', 'align' => 'C') // ページ
		  ));
	}
    
	/**
	 * タイムスケジュール基本 SELECT
	 *
	 * <AUTHOR> Kayo
	 * @since 2016/02/07
	 * @param Msi_Sys_Db $db	データベース
	 * @return	array	$set_ary	設定配列
	 */
	private function tm_schedule_kihon_select($db) {
        
        $select = $db->easySelect( <<< END_OF_SQL
SELECT
	tm.schedule_cd			AS	"スケジュールコード"
   ,tm.schedule_nm			AS	"スケジュール名"
   ,tm.disp_date_kbn1	|| ':' || cd1.kbn_value_lnm	AS	"表示日付区分１"
   ,tm.schedule_title_nm1	AS	"スケジュール１タイトル"
   ,tm.schedule_line_su1	AS	"スケジュール１行数"
   ,tm.disp_date_kbn2	|| ':' || cd2.kbn_value_lnm	AS	"表示日付区分２"
   ,tm.schedule_title_nm2	AS	"スケジュール２タイトル"
   ,tm.schedule_line_su2	AS	"スケジュール２行数"
   ,tm.disp_date_kbn3	|| ':' || cd3.kbn_value_lnm	AS	"表示日付区分３"
   ,tm.schedule_title_nm3	AS	"スケジュール３タイトル"
   ,tm.schedule_line_su3	AS	"スケジュール３行数"
   ,tm.template_flie_nm		AS	"Excleテンプレートファイル名"
FROM tm_schedule_kihon tm
LEFT JOIN code_nm_mst cd1	-- 表示日付区分１
    ON  '0460'              =   cd1.code_kbn
    AND tm.disp_date_kbn1		=   cd1.kbn_value_cd_num
    AND 0                   =   cd1.delete_flg
LEFT JOIN code_nm_mst cd2	-- 表示日付区分２
    ON  '0460'              =   cd2.code_kbn
    AND tm.disp_date_kbn2	=   cd2.kbn_value_cd_num
    AND 0                   =   cd2.delete_flg
LEFT JOIN code_nm_mst cd3	-- 表示日付区分３
    ON  '0460'              =   cd3.code_kbn
    AND tm.disp_date_kbn3	=   cd3.kbn_value_cd_num
    AND 0                   =   cd3.delete_flg
WHERE tm.delete_flg = 0
ORDER BY tm.schedule_cd
END_OF_SQL
        );
        return  $select;
    }
    
	/**
	 * タイムスケジュール施行 SELECT
	 *
	 * <AUTHOR> Kayo
	 * @since 2016/02/07
	 * @param Msi_Sys_Db $db	データベース
	 * @param string $schedule_cd	スケジュールコード
	 * @return	array	$set_ary	設定配列
	 */
	private function tm_schedule_seko_select($db, $schedule_cd) {
        
        $select = $db->easySelect( <<< END_OF_SQL
SELECT
	ts.schedule_cd		AS	"スケジュールコード"
   ,tm.schedule_nm		AS	"スケジュール名"
   ,ts.disp_no			AS	"表示順"
   ,ts.seko_kbn			|| ':' || cd1.kbn_value_lnm		AS	"施行区分"
   ,CASE WHEN ts.saiyo_kbn = 0 THEN
		'0:採用なし'
	ELSE		
	    '1:採用あり'
	END					AS	"採用区分"
   --,ts.dialog_disp_kbn	AS	"ダイアログ選択区分"
FROM tm_schedule_seko ts
LEFT JOIN tm_schedule_kihon tm
	ON	ts.schedule_cd	=	tm.schedule_cd
	AND	0				=	tm.delete_flg
LEFT JOIN code_nm_mst cd1	-- 表示日付区分１
    ON  '0460'              =   cd1.code_kbn
    AND ts.seko_kbn			=   cd1.kbn_value_cd_num
    AND 0                   =   cd1.delete_flg
WHERE	ts.delete_flg = 0
AND		ts.schedule_cd = :schedule_cd			
ORDER BY ts.schedule_cd, ts.disp_no
END_OF_SQL
		,array('schedule_cd' => $schedule_cd));
        return  $select;
    }
    
	/**
	 * タイムスケジュール内訳 SELECT
	 *
	 * <AUTHOR> Kayo
	 * @since 2016/02/07
	 * @param Msi_Sys_Db $db	データベース
	 * @param string $schedule_cd	スケジュールコード
	 * @return	array	$set_ary	設定配列
	 */
	private function tm_schedule_uchiwk_select($db, $schedule_cd) {
        
        $select = $db->easySelect( <<< END_OF_SQL
SELECT
	 tu.schedule_cd		AS	"スケジュールコード"
	,tm.schedule_nm		AS	"スケジュール名"
	,tu.line_no			AS	"行番号"
	,CASE WHEN tu.page_kbn = 1	THEN
			'1頁目'
		WHEN tu.page_kbn = 2	THEN
			'2頁目'
		WHEN tu.page_kbn = 3	THEN
			'3頁目'
		ELSE
			null
		END				AS	"ページ区分"
	,tu.jikou_sabun		AS	"時刻差分"
	,tu.kijyun_kbn		|| ':' || cd1.kbn_value_lnm		AS	"基準区分"
--	,tu.orikaeshi_kbn	AS	"折り返し区分"
	,CASE	WHEN	tu.line_su = 1 THEN
				'1行'
			WHEN	tu.line_su = 2 THEN	
				'2行'
			WHEN	tu.line_su = 3 THEN
				'3行'
			ELSE
				null
			END			AS	"行数"
	,tu.nitei_line1		AS	"日程(1行目)"
	,tu.biko_line1		AS	"備考(1行目)"
	,tu.nitei_line2		AS	"日程(2行目)"
	,tu.biko_line2		AS	"備考(2行目)"
	,tu.nitei_line3		AS	"日程(3行目)"
	,tu.biko_line3		AS	"備考(3行目)"
FROM tm_schedule_uchiwk tu
LEFT JOIN tm_schedule_kihon tm
	ON	tu.schedule_cd	=	tm.schedule_cd
	AND	0				=	tm.delete_flg
LEFT JOIN code_nm_mst cd1	-- 表示日付区分１
    ON  '0460'              =   cd1.code_kbn
    AND tu.kijyun_kbn		=   cd1.kbn_value_cd_num
    AND 0                   =   cd1.delete_flg
WHERE	tu.delete_flg = 0
AND		tu.schedule_cd = :schedule_cd			
ORDER BY tu.schedule_cd, tu.line_no, tu.page_kbn
END_OF_SQL
		,array('schedule_cd' => $schedule_cd));
        return  $select;
    }
    
	/**
	 * カラム位置の設定
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 * @return	array	$set_ary	設定配列
	 */
	private function getSetAry() {
		static $row_top = 90;
		static $y1 = 0;
		static $yM = 4;

		$set_ary[] = array('x' =>  45, 'y' => $row_top+$y1, 'width' => 48, 'height' => 11); 					// 施行番号
		$set_ary[] = array('x' => 100, 'y' => $row_top+$y1, 'width' => 48, 'height' => 11); 					// 日付
		$set_ary[] = array('x' => 149, 'y' => $row_top+$y1, 'width' => 52, 'height' => 11); 					// 伝票区分
		$set_ary[] = array('x' => 169, 'y' => $row_top+$y1, 'width' => 52, 'height' => 11); 					// 商品コード
		$set_ary[] = array('x' => 212, 'y' => $row_top+$y1, 'width' =>136, 'height' => 11); 					// 商品名
		$set_ary[] = array('x' => 390, 'y' => $row_top+$y1, 'width' => 30, 'height' => 11, 'type' => 'num');	// 数量
		$set_ary[] = array('x' => 418, 'y' => $row_top+$y1, 'width' => 15, 'height' => 11); 					// 単位名
		$set_ary[] = array('x' => 440, 'y' => $row_top+$y1, 'width' => 42, 'height' => 11, 'type' => 'num', 'precision' => 2);	// 出庫単価
		$set_ary[] = array('x' => 480, 'y' => $row_top+$y1, 'width' => 47, 'height' => 11, 'type' => 'num');	// 出庫金額
		$set_ary[] = array('x' => 528, 'y' => $row_top+$y1, 'width' => 40, 'height' => 11); 					// 倉庫コード
		$set_ary[] = array('x' => 562, 'y' => $row_top+$y1, 'width' => 96, 'height' => 11); 					// 倉庫名
		$set_ary[] = array('x' => 657, 'y' => $row_top+$y1, 'width' => 70, 'height' => 11); 					// 出庫日時
		$set_ary[] = array('x' => 730, 'y' => $row_top+$y1, 'width' => 90, 'height' => 11); 					// 出庫担当者
		$set_ary[] = array('x' => 212, 'y' => $row_top+$y1, 'width' =>136, 'height' => 11, 'align' => 'R'); 	// 合計名
		return $set_ary;
	}

	/**
	 * 葬儀在庫引落チェックリストを出力(明細)
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 * @param pdfObj $pdfObj PDFオブジェクト
	 * @param type $rec レコード
	 * @param string $rep_kbn	帳票区分
	 * @return	   viod
	 */
	private function outDataMsi($pdfObj, $rec, $rep_kbn)
	{
        // 最終レコード
        $head_ary = array(array('x' => 45, 'y' => 62, 'width' =>160, 'height' => 15, 'value' => $bumon));
        $pdfObj->write_table($this->getSetAry(), self::$row_height, $row_ary, self::$row_count, __DIR__ . '/' . self::$sourceFileName, 1, $head_ary);
	}
    
	/**
	 * ＰＤＦ出力処理
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 * @return	   array
	 */
	private function setPdfArray(
				$h_siire_ymd,
				$h_denpyo_no,
				$m_denpyo_kbn_nm,
				$m_shohin_cd,
				$m_shohin_nm,
				$m_suryo,
				$m_tani_nm,
				$m_tnk,
				$m_prc,
				$m_soko_cd,
				$m_soko_nm,
				$m_shuko_dt,
				$h_tanto_nm,
				$gokei_title
		)
	{
		$row_ary = array(
			$h_siire_ymd,
			$h_denpyo_no,
			$m_denpyo_kbn_nm,
			$m_shohin_cd,
			$m_shohin_nm,
			$m_suryo,
			$m_tani_nm,
			$m_tnk,
			$m_prc,
			$m_soko_cd,
			$m_soko_nm,
			$m_shuko_dt,
			$h_tanto_nm,
			$gokei_title
		);
		return $row_ary;
	}
    
	/**
	 * CSV出力メイン
	 *
	 * <AUTHOR> Kayo
	 * @since 2015/10/11
	 * @param Msi_Sys_Db $db	データベース
	 * @return	   viod
	 */
	private function getCsv($db)
	{
		// タイムスケジュール基本 SELECT カクテル
		$select = $this->tm_schedule_kihon_select($db);
        if (count($select) == 0) {
            return null;
        }
		$csvData = array();
        foreach($select as $rec) {
			// タイムスケジュール基本 CSVを編集
			$csvData = array_merge($csvData, App_ClsMsterCsvEdit::EditCsvHead($select));
			$csvData = array_merge($csvData, App_ClsMsterCsvEdit::EditCsvMsi($rec));
			// タイムスケジュール施行 CSVを編集
			$ts_select = $this->tm_schedule_seko_select($db, $rec['スケジュールコード']);
			$csvData =  array_merge($csvData,App_ClsMsterCsvEdit::EditCsv($ts_select));
			// タイムスケジュール内訳 CSVを編集
			$tu_select = $this->tm_schedule_uchiwk_select($db, $rec['スケジュールコード']);
			$csvData =  array_merge($csvData,App_ClsMsterCsvEdit::EditCsv($tu_select));
		}	
        
        return $csvData;
	}
}
