<?php

/**
 * Excel 事前相談管理報告
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Sai.tk
 * @since      2022/11/22
 * @filesource 
 */

/**
 * Excel 事前相談管理報告
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Sai.tk
 * @since      2022/11/22
 */
class Kanri_ExceljizensoudankanriController extends Zend_Controller_Action {
    
    private static $title = '事前相談管理報告';
    private static $sourceFileName1 = 'excel_tmpl/jizensoudankanri_tmpl.xlsx';
    
    /**
     * 事前相談管理報告出力アクション
     *
     * <AUTHOR> Sai.tk
     * @since      2022/11/22
     */
    public function indexAction() {
        $params  = Msi_Sys_Utils::webInputs();
        $taisho_year = htmlspecialchars($params['taisho_year']);          // 対象年度
        $db = Msi_Sys_DbManager::getMyDb();
        $month_arr = array(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
        $nendo_matsu = $taisho_year.'/08';
        
        $excel = App_ExcelOut2::LoadExcel( __DIR__.'/'.self::$sourceFileName1);
        
        //テンプレートにするシートの設定
        $excel->setActiveSheetIndex(0);
        $sheet = $excel->getActiveSheet();
        
        $sheet->setCellValue('A1', $taisho_year.'年度　事前相談管理報告');
        foreach ($month_arr as $month) {
            $sheet->setCellValue('C'.(2 + $month * 2), '0');
            $sheet->setCellValue('C'.(3 + $month * 2), '0');
            $sheet->setCellValue('D'.(2 + $month * 2), '0');
            $sheet->setCellValue('D'.(3 + $month * 2), '0');
            $sheet->setCellValue('E'.(2 + $month * 2), '0');
            $sheet->setCellValue('E'.(3 + $month * 2), '0');
            $sheet->setCellValue('G'.(2 + $month * 2), '0');
            $sheet->setCellValue('G'.(3 + $month * 2), '0');
            $sheet->setCellValue('H'.(2 + $month * 2), '0');
            $sheet->setCellValue('H'.(3 + $month * 2), '0');
            // 当月施行件数
            $rec = DataMapper_Exceljizensoudankanri::findSekoCount( $db, '1',  null, $nendo_matsu, $month, $month + 1);
            foreach ($rec as $row) {
                if ($row['kaiin_cd'] == '1') {
                    $sheet->setCellValue('C'.(2 + $month * 2), $row['count']);
                } else {
                    $sheet->setCellValue('C'.(3 + $month * 2), $row['count']);
                }
            }
            // 当月相談件数
            $rec = DataMapper_Exceljizensoudankanri::findSekoCount( $db, '5',  null, $nendo_matsu, $month, $month + 1, $nendo_matsu, $month, $month + 1);
            foreach ($rec as $row) {
                if ($row['kaiin_cd'] == '1') {
                    $sheet->setCellValue('D'.(2 + $month * 2), $row['count']);
                    $sheet->setCellValue('E'.(2 + $month * 2), $row['count2']);
                } else {
                    $sheet->setCellValue('D'.(3 + $month * 2), $row['count']);
                    $sheet->setCellValue('E'.(3 + $month * 2), $row['count2']);
                }
            }
            // 直近3か月相談件数
            $rec = DataMapper_Exceljizensoudankanri::findSekoCount( $db, '5',  null, $nendo_matsu, $month - 2, $month + 1, $nendo_matsu, $month, $month + 1);
            foreach ($rec as $row) {
                if ($row['kaiin_cd'] == '1') {
                    $sheet->setCellValue('G'.(2 + $month * 2), $row['count']);
                    $sheet->setCellValue('H'.(2 + $month * 2), $row['count2']);
                } else {
                    $sheet->setCellValue('G'.(3 + $month * 2), $row['count']);
                    $sheet->setCellValue('H'.(3 + $month * 2), $row['count2']);
                }
            }
        }

        // 印刷エリアの指定
        $sheet->getPageSetup()->setPrintArea('A1:I27');
        // Excelファイルをダウンロード
        App_ExcelOut2::OutputExcel($excel, self::$title.'.xlsx' );
    }
}
