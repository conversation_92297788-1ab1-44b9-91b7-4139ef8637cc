<?php

/**
 * PDF 契約先登録リスト
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Mogi
 * @since 	 2020/11/xx
 * @filesource 
 */

/**
 * PDF 契約先登録リスト
 *
 * @category	 App
 * @package	 controller
 * <AUTHOR> Mogi
 * @since 	 2020/11/xx
 */
class Mstr_Pdf1441Controller extends Zend_Controller_Action {

    private static $title = '契約先登録リスト';

    /**
     * アクション
     *
     * <AUTHOR> Mogi
     * @since 2020/11/20
     */
    public function indexAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);

        $csv = false;
        // CSV出力
        if (array_key_exists('csv', $dataAppAry)) {
            $csv = $dataAppAry['csv'];
        }

        $db = Msi_Sys_DbManager::getMyDb();
        $title = self::$title;
        if ($csv) {
            $csvData = $this->getCsv($db);
            if (!isset($csvData)) {
                App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
                return;
            }
            $buf = Msi_Sys_Utils::csvOutString($csvData);
            Msi_Sys_Utils::out2way1($buf, $title . '.csv', 'text/csv');
        }
    }

    /**
     * 契約先登録CSVデータ取得
     *
     * <AUTHOR> Mogi
     * @since 	 2020/11/xx
     * @param Msi_Sys_Db $db	データベース
     * @return	array	$set_ary	設定配列
     */
    private function keiyakusaki_mst_select($db) {

        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
          k.partner_cd                                                  AS 契約先コード
        , k.partner_lnm                                                 AS 契約先名称
        , k.parftner_knm                                                AS 契約先カナ名
        , k.partner_snm                                                 AS 契約先簡略名
        , k.partner_ksnm                                                AS 契約先カナ簡略名
        , k.company_kbn || ':' || cnm_company.kbn_value_lnm             AS 法人区分
        , k.sekyu_soufu_tel                                             AS 電話番号
        , k.sekyu_soufu_zip                                             AS 郵便番号
        , k.sekyu_soufu_addr1                                           AS 住所1
        , k.sekyu_soufu_addr2                                           AS 住所2
        , TO_CHAR(k.tekiyo_st_date,'YYYY/MM/DD')                        AS 適用開始日
        , TO_CHAR(k.tekiyo_ed_date,'YYYY/MM/DD')                        AS 適用終了日
        , cnm_wari_pattern.kbn_value_lnm                                AS 割引率パターン
        , k.tel1                                                        AS "TEL1"
        , k.tel2                                                        AS "TEL2"
        , k.tel3                                                        AS "TEL3"
        , CASE k.shokuchi_kbn 
               WHEN 0 THEN '通常' 
               WHEN 1 THEN '諸口' 
               ELSE NULL 
          END                                                           AS 諸口区分
        , CASE k.sakujo_kbn
               WHEN 0 THEN '通常' 
               WHEN 1 THEN '削除' 
               ELSE NULL 
          END                                                           AS 削除区分
        , tanto1.tanto_nm                                               AS 社内担当者1
        , tanto2.tanto_nm                                               AS 社内担当者2
        , tanto3.tanto_nm                                               AS 社内担当者3
        , k.aite_tanto_nm                                               AS 相手先担当者
        , CASE k.oya_dantai_kbn
               WHEN 0 THEN '親団体ではない'
               WHEN 1 THEN '親団体'
               ELSE NULL
          END                                                           AS 親団体区分
        , k.oya_dantai_cd                                               AS 親団体コード
        , eigyo1.tanto_nm                                               AS 営業担当1
        , eigyo2.tanto_nm                                               AS 営業担当2
        , eigyo3.tanto_nm                                               AS 営業担当3
        , eigyo4.tanto_nm                                               AS 営業担当4
        , k.nyukai_prc                                                  AS 入会金
        , k.file_no                                                     AS "ファイルNo."
        , cnm_keiyaku.kbn_value_lnm                                     AS 契約内容
        , TO_CHAR(k.keiyaku_date, 'YYYY/MM/DD')                         AS 契約日
        , TO_CHAR(k.update_date, 'YYYY/MM/DD')                          AS 更新日
        , k.biko                                                        AS 備考欄
    FROM keiyakusaki_mst k -- 契約先マスタ
    LEFT JOIN partner_wari_kbn_mst partner -- 契約先割引区分マスタ
        ON partner.partner_cd = k.partner_cd
        AND partner.delete_flg = 0
    LEFT JOIN code_nm_mst cnm_company -- コード名称（法人区分）
        ON cnm_company.code_kbn = '8280'
        AND cnm_company.kbn_value_cd_num = k.company_kbn
        AND cnm_company.delete_flg = 0
    LEFT JOIN code_nm_mst cnm_wari_pattern -- コード名称（割引率パターン）
        ON cnm_wari_pattern.code_kbn    = '7880'
        AND cnm_wari_pattern.kbn_value_cd_num = partner.sougi_wari_kbn
        AND cnm_wari_pattern.delete_flg = 0
    LEFT JOIN tanto_mst tanto1 -- 担当者マスタ（社内担当者1）
        ON tanto1.tanto_cd = k.tanto_cd1
        AND tanto1.delete_flg = 0
    LEFT JOIN tanto_mst tanto2 -- 担当者マスタ（社内担当者2）
        ON tanto2.tanto_cd = k.tanto_cd2
        AND tanto2.delete_flg = 0
    LEFT JOIN tanto_mst tanto3 -- 担当者マスタ（社内担当者3）
        ON tanto3.tanto_cd = k.tanto_cd3
        AND tanto3.delete_flg = 0
    LEFT JOIN tanto_mst eigyo1
        ON eigyo1.tanto_cd = k.tanto_eigyo_cd1
        AND eigyo1.delete_flg = 0
    LEFT JOIN tanto_mst eigyo2
        ON eigyo2.tanto_cd = k.tanto_eigyo_cd2
        AND eigyo2.delete_flg = 0
    LEFT JOIN tanto_mst eigyo3
        ON eigyo3.tanto_cd = k.tanto_eigyo_cd3
        AND eigyo3.delete_flg = 0
    LEFT JOIN tanto_mst eigyo4
        ON eigyo4.tanto_cd = k.tanto_eigyo_cd4
        AND eigyo4.delete_flg = 0
    LEFT JOIN code_nm_mst cnm_keiyaku
        ON cnm_keiyaku.code_kbn = '9607'
        AND cnm_keiyaku.kbn_value_cd = k.keiyaku_naiyou_cd
        AND cnm_keiyaku.delete_flg = 0
    ORDER BY k.partner_cd
END_OF_SQL
        );
        return $select;
    }

    /**
     * CSV出力メイン
     *
     * <AUTHOR> Mogi
     * @since 	 2020/09/xx
     * @param Msi_Sys_Db $db	データベース
     * @return	   viod
     */
    private function getCsv($db) {
        $select = $this->keiyakusaki_mst_select($db);
        if (count($select) == 0) {
            return null;
        }
        // CSVを編集
        $csvData = App_ClsMsterCsvEdit::EditCsv($select);

        return $csvData;
    }

}
