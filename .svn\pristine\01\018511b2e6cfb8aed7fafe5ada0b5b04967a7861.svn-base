<?php

/**
 * Seikyu_SeikyuShonin
 *
 * 請求承認クラス
 *
 * @category   App
 * @package    controllers\Seikyu\SeikyuShonin
 * <AUTHOR> Sai
 * @since      2014/04/15
 * @version 2019/06/20 tosaka 軽減税率対応
 * @filesource 
 */

/**
 * 請求承認クラス
 *
 * @category   App
 * @package    controllers\Seikyu\SeikyuShonin
 * <AUTHOR>
 * @since      2014/04/15
 */
class Saiken_SeikyuShonin extends Juchu_JuchuEstimate {

    /**
     *
     * 葬送儀礼～その他の明細データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/16
     * @return array 明細データ
     */
    protected function getDetailData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            dm.msi_no -- 明細番号
            ,dm.dai_bunrui_cd -- 大分類コード
            ,dm.chu_bunrui_cd -- 中分類コード
            ,cbm.chu_bunrui_nm -- 中分類名
            ,dm.shohin_kbn -- 商品区分
            ,dm.shohin_kbn_nm-- 商品区分名
            ,dm.shohin_cd -- 商品コード
            ,dm.shohin_nm -- 商品名
            ,dm.shohin_tkiyo_nm -- 商品摘要名
            ,dm.mokuteki_kbn -- 目的区分
            ,dm.juchu_suryo -- 数量
            ,dm.uri_tnk AS juchu_tnk-- 単価
            ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
            ,dm.gojokai_nebiki_prc -- 付帯値引き
            ,dm.nebiki_prc -- 値引き
            ,(dm.juchu_suryo * uri_tnk) + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
            ,0 AS ekimu_sagaku_kei -- セット内差額計
            ,dm.hoshi_umu_kbn -- 奉仕料有無区分
            ,dm.hoshi_prc -- 奉仕料
            ,sm.tnk_chg_kbn-- 売上単価変更区分
            ,sm.nm_input_kbn-- 名称入力区分
            ,sbm.hachu_kbn -- 発注書区分
            ,sm.tani_cd-- 単位コード
            ,sm.uri_kamoku_cd-- 科目コード
            ,dm.zei_kbn -- 消費税区分
            ,dm.gen_tnk-- 原価単価
            ,CASE  -- タブ区分 1:葬送儀礼, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ 6 式場関係費
                WHEN dm.dai_bunrui_cd = '0010' AND sbm.syoukei_group_cd NOT IN ('20', '70')  AND sbm.chu_bunrui_cd NOT IN ('2000') THEN '1'
                WHEN dm.dai_bunrui_cd = '0010' AND sbm.syoukei_group_cd = '20' THEN '6'
                WHEN dm.dai_bunrui_cd = '0020' THEN '2'
                WHEN dm.dai_bunrui_cd = '0030' THEN '3'
                WHEN dm.dai_bunrui_cd = '0040' THEN '3'
                WHEN dm.dai_bunrui_cd = '0060' THEN '4'
                WHEN dm.dai_bunrui_cd = '0010' AND sbm.syoukei_group_cd = '70' THEN '5'
                WHEN dm.dai_bunrui_cd = '0010' AND sbm.chu_bunrui_cd IN ('2000') THEN '8'
            END tab_kbn
            ,CASE 
                spsm.service_kbn 
                WHEN 1 THEN '1' 
                WHEN 2 THEN '2' 
                ELSE '0' 
            END nebiki_kbn -- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
            ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
            ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                WHEN '0020' THEN '0'
                ELSE '1'
            END add_down_kbn
            ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                WHEN '0020' THEN '0'
                ELSE '1'
            END del_kbn
            ,dm.add_kbn -- 追加区分 0:トランデータ
            ,sbm.print_group_cd
            ,sbm.syoukei_group_cd
            ,dm.out_zei_prc
            ,dm.in_zei_prc
            ,dm.k_free3
            ,dm.k_free4
            ,dm.in_zei_prc
            ,dm.add_cost
            ,0 AS suryo_update_kbn -- 数量変更区分
            ,0 AS add_flg  -- プラン商品かどうかのフラグ
            ,dm.plan_shohin_cd
            ,dm.plan_shohin_suryo
            ,dm.plan_shohin_tnk
            ,dm.upgrade_kbn
            ,0 AS sagaku -- 差額の初期値保持用
            ,tk.hanbai_tnk AS shohin_tnk
            ,spsm.kaisya_cd
            ,dm.zei_cd  -- 2019/06/20 tosaka keigen
            ,dm.reduced_tax_rate --  2019/06/20 tosaka keigen
        FROM
            (
                SELECT
                    m1.msi_no
                    ,m1.denpyo_kbn
                    ,m1.disp_no
                    ,m1.add_kbn
                    ,m1.dai_bunrui_cd
                    ,m1.chu_bunrui_cd
                    ,m1.shohin_kbn
                    ,skm.shohin_kbn_nm
                    ,m1.shohin_cd
                    ,m1.shohin_nm
                    ,m1.shohin_tkiyo_nm
                    ,m1.juchu_suryo
                    ,m1.uri_tnk
                    --,m1.juchu_suryo * m1.uri_tnk AS m1.juchu_prc
                    ,m1.hoshi_umu_kbn
                    ,m1.hoshi_prc
                    ,m1.gojokai_nebiki_prc
                    ,m1.nebiki_prc
                    ,m1.zei_kbn        
                    ,m1.gen_tnk
                    ,m1.mokuteki_kbn
                    ,m1.out_zei_prc
                    ,m1.in_zei_prc
                    ,m1.k_free3
                    ,m1.k_free4
                    ,COALESCE(m1.add_cost, 0) AS add_cost
                    ,m1.plan_shohin_cd
                    ,m1.plan_shohin_tnk
                    ,m1.plan_shohin_suryo
                    ,m1.upgrade_kbn
                    ,m1.zei_cd  -- 2019/06/20 tosaka keigen
                    ,m1.reduced_tax_rate -- 2019/06/20 tosaka keigen
                FROM
                    uriage_denpyo d
                LEFT JOIN
                    uriage_denpyo_msi m1
                    ON d.uri_den_no = m1.uri_den_no
                    AND m1.delete_flg = 0
                    AND m1.seko_no_sub = :seko_no_sub
                    AND m1.data_sbt IN (1, 2, 3, 4, 5, 6, 7) --データ種別 1：葬送儀礼 2：返礼品　3：料理 4：壇払い 5：別途費用 6：立替金 7：値引き
                INNER JOIN
                    shohin_kbn_mst skm
                    ON  (m1.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0)
                WHERE
                    d.seko_no = :seko_no
                    AND d.delete_flg = 0
            ) dm
            INNER JOIN
                shohin_chu_bunrui_mst cbm
            ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                AND cbm.delete_flg = 0)
            INNER JOIN
                shohin_mst sm
            ON  (
                    dm.shohin_cd = sm.shohin_cd
                AND  sm.hihyoji_kbn = 0
                AND sm.delete_flg = 0
                )
            LEFT OUTER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                AND dm.shohin_kbn  = sbm.shohin_kbn
                AND dm.shohin_cd  = sbm.shohin_cd
                AND sbm.delete_flg = 0
                )
            LEFT JOIN shohin_tanka_mst tk
                ON sm.shohin_cd = tk.shohin_cd
                AND sm.kaisya_cd  = tk.kaisya_cd                
                AND sm.bumon_cd  = tk.bumon_cd                
                AND tk.delete_flg=0                
                AND (TO_CHAR(tk.tekiyo_st_date, 'YYYY/MM/DD') <= '$this->_zeiKijunYmd' AND TO_CHAR(tk.tekiyo_ed_date, 'YYYY/MM/DD') >= '$this->_zeiKijunYmd' )
            LEFT OUTER JOIN
                seko_plan_smsi_mst spsm
            ON  (
                    dm.dai_bunrui_cd = spsm.dai_bunrui_cd
                AND dm.chu_bunrui_cd  = spsm.chu_bunrui_cd
                AND dm.shohin_kbn  = spsm.shohin_kbn
                AND dm.shohin_cd  = spsm.shohin_cd
                AND spsm.seko_plan_cd = :seko_plan_cd
                AND spsm.gojokai_kbn = :gojokai_kbn
                AND (TO_CHAR(spsm.tekiyo_st_date, 'YYYY/MM/DD') <= '$this->_zeiKijunYmd' AND TO_CHAR(spsm.tekiyo_ed_date, 'YYYY/MM/DD') >= '$this->_zeiKijunYmd' )
                AND spsm.delete_flg = 0
                )
        ORDER BY
            sbm.mitumori_print_seq
            ,dm.dai_bunrui_cd
            ,dm.disp_no
            ,dm.msi_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo, 
            'seko_no_sub' => $this->_sekoNoSub,
            'seko_plan_cd' => $this->_sekoPlanCd,
            'gojokai_kbn' => $this->_gojokaiKbn,
            ));
        // 葬儀基本費用のタブのみ特殊処理
        foreach ($select as &$value) {
            if ($value['tab_kbn'] === '1' ) {
                switch ($value['upgrade_kbn']) {
                    case '1':   // プラン商品
                        $planMsi = DataMapper_SekoPlanSmsi::find($db, array('seko_plan_cd' => $this->_sekoPlanCd, 'shohin_cd' => $value['shohin_cd'], 'kaisya_cd' => $value['kaisya_cd']));
                        if ($value['juchu_suryo'] == $value['plan_shohin_suryo']) {
                            $value['suryo_update_kbn'] = $planMsi[0]['suryo_update_kbn'];
                        }
                    case '2':   // アップグレード商品
                    case '3':   // セット外商品
                        $value['juchu_prc'] = $value['plan_shohin_tnk'] * $value['plan_shohin_suryo'];
                        $value['ekimu_sagaku_kei'] = $value['juchu_prc'] + $value['gojokai_nebiki_prc'];
                        $value['sagaku'] = $value['add_cost'];
                        $value['juchu_prc_kei'] = $value['ekimu_sagaku_kei'] + $value['sagaku'];
                        break;
                }
            }
        }
        return $select;
    }
    
    /**
     *
     * 別注品（喪家分で未入金かつ自社請求のもの）のデータを取得
     *
     * <AUTHOR> Tosaka
     * @since 2018/4/20
     * @return array 明細データ
     */
    protected function getBechuData($uri_den_no) {
        
        $db = Msi_Sys_DbManager::getMyDb();
        // 伝票の状態を調べる
        $cond['uri_den_no'] = $uri_den_no;
        $select = DataMapper_UriageDenpyo::findUriageDenpyo($db, $cond);
        if (count($select) === 0) {
            return;
        }
        $uri_data = $select[0];
        // 伝票が分割・合算されていない場合は合算予定のものを取得する
        if ($uri_data['bun_gas_kbn_num'] === '0') {
            $sql = "
            SELECT
                dm.msi_no -- 明細番号
                ,dm.dai_bunrui_cd -- 大分類コード
                ,dm.chu_bunrui_cd -- 中分類コード
                ,cbm.chu_bunrui_nm -- 中分類名
                ,dm.shohin_kbn -- 商品区分
                ,dm.shohin_kbn_nm-- 商品区分名
                ,dm.shohin_cd -- 商品コード
                ,dm.shohin_nm -- 商品名
                ,dm.shohin_tkiyo_nm -- 商品摘要名
                ,dm.mokuteki_kbn -- 目的区分
                ,dm.juchu_suryo -- 数量
                ,dm.uri_tnk AS juchu_tnk-- 単価
                ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
                ,dm.gojokai_nebiki_prc -- 付帯値引き
                ,dm.nebiki_prc -- 値引き
                ,(dm.juchu_suryo * uri_tnk) + dm.gojokai_nebiki_prc + dm.nebiki_prc + hoshi_prc AS juchu_prc_kei -- 受注金額差引計
                ,0 AS ekimu_sagaku_kei -- セット内差額計
                ,dm.hoshi_umu_kbn -- 奉仕料有無区分
                ,dm.hoshi_prc -- 奉仕料
                ,sm.tnk_chg_kbn-- 売上単価変更区分
                ,sm.nm_input_kbn-- 名称入力区分
                ,sbm.hachu_kbn -- 発注書区分
                ,sm.tani_cd-- 単位コード
                ,sm.uri_kamoku_cd-- 科目コード
                ,dm.zei_kbn -- 消費税区分
                ,dm.gen_tnk-- 原価単価
                ,'7' AS tab_kbn
                ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
                ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                    WHEN '0020' THEN '0'
                    ELSE '1'
                END add_down_kbn
                ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                    WHEN '0020' THEN '0'
                    ELSE '1'
                END del_kbn
                ,dm.add_kbn -- 追加区分 0:トランデータ
                ,sbm.print_group_cd
                ,sbm.syoukei_group_cd
                ,dm.out_zei_prc
                ,dm.in_zei_prc
                ,dm.k_free3
                ,dm.k_free4
                ,dm.in_zei_prc
                ,dm.add_cost
                ,0 AS suryo_update_kbn -- 数量変更区分
                ,0 AS add_flg  -- プラン商品かどうかのフラグ
                ,dm.plan_shohin_cd
                ,dm.plan_shohin_tnk
                ,0 AS sagaku -- 差額の初期値保持用
                ,dm.zei_cd  -- 2019/06/20 tosaka keigen
                ,dm.reduced_tax_rate --  2019/06/20 tosaka keigen
            FROM
                (
                    SELECT
                        m1.msi_no
                        ,m1.denpyo_kbn
                        ,m1.disp_no
                        ,m1.add_kbn
                        ,m1.dai_bunrui_cd
                        ,m1.chu_bunrui_cd
                        ,m1.shohin_kbn
                        ,skm.shohin_kbn_nm
                        ,m1.shohin_cd
                        ,m1.shohin_nm
                        ,m1.shohin_tkiyo_nm
                        ,m1.juchu_suryo
                        ,m1.uri_tnk
                        --,m1.juchu_suryo * m1.uri_tnk AS m1.juchu_prc
                        ,m1.hoshi_umu_kbn
                        ,m1.hoshi_prc
                        ,m1.gojokai_nebiki_prc
                        ,m1.nebiki_prc
                        ,m1.zei_kbn        
                        ,m1.gen_tnk
                        ,m1.mokuteki_kbn
                        ,m1.out_zei_prc
                        ,m1.in_zei_prc
                        ,m1.k_free3
                        ,m1.k_free4
                        ,COALESCE(m1.add_cost, 0) AS add_cost
                        ,m1.plan_shohin_cd
                        ,m1.plan_shohin_tnk
                        ,m1.zei_cd  -- 2019/06/20 tosaka keigen
                        ,m1.reduced_tax_rate -- 2019/06/20 tosaka keigen
                    FROM
                        uriage_denpyo d1
                    LEFT JOIN uriage_denpyo_msi m1
                    ON m1.uri_den_no = d1.uri_den_no
                    AND m1.delete_flg = 0
                    INNER JOIN shohin_kbn_mst skm
                    ON  (m1.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0)
                    WHERE d1.n_free1 = :n_free1
                    AND d1.pay_method_cd = '$this->_pay_method_cd_moke'
                    AND d1.kaishu_kbn = '$this->_kaishu_kbn_jisya'
                    AND d1.nyukin_prc = 0
                    AND d1.bun_gas_kbn_num IN (0)
                    AND d1.data_kbn IN (3,4)
                    AND CASE WHEN d1.data_kbn = 3 THEN d1.k_free1 = 0 ELSE d1.k_free1 is null END
                ) dm
            INNER JOIN
                shohin_chu_bunrui_mst cbm
            ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                AND cbm.delete_flg = 0)
            INNER JOIN
                shohin_mst sm
            ON  (
                    dm.shohin_cd = sm.shohin_cd
                AND  sm.hihyoji_kbn = 0
                AND sm.delete_flg = 0
                )
            LEFT OUTER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                    AND dm.shohin_kbn  = sbm.shohin_kbn
                    AND dm.shohin_cd  = sbm.shohin_cd
                    AND sbm.delete_flg = 0
                )
            ORDER BY
                sbm.mitumori_print_seq
                ,dm.dai_bunrui_cd
                ,dm.disp_no
                ,dm.msi_no
                    ";
            $msiData = $db->easySelect($sql, array(
                'n_free1' => $this->_mokeNo, 
                ));
        } else if ($uri_data['bun_gas_kbn_num'] === '10') { // 合算元になっている場合は既に合算されているものと合算予定のもの（上長承認がされていなければ）を取得する
            if ($this->_statusKbn !== '3') {
                $sql = "
                SELECT
                    dm.msi_no -- 明細番号
                    ,dm.dai_bunrui_cd -- 大分類コード
                    ,dm.chu_bunrui_cd -- 中分類コード
                    ,cbm.chu_bunrui_nm -- 中分類名
                    ,dm.shohin_kbn -- 商品区分
                    ,dm.shohin_kbn_nm-- 商品区分名
                    ,dm.shohin_cd -- 商品コード
                    ,dm.shohin_nm -- 商品名
                    ,dm.shohin_tkiyo_nm -- 商品摘要名
                    ,dm.mokuteki_kbn -- 目的区分
                    ,dm.juchu_suryo -- 数量
                    ,dm.uri_tnk AS juchu_tnk-- 単価
                    ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
                    ,dm.gojokai_nebiki_prc -- 付帯値引き
                    ,dm.nebiki_prc -- 値引き
                    ,(dm.juchu_suryo * uri_tnk) + dm.gojokai_nebiki_prc + dm.nebiki_prc + hoshi_prc AS juchu_prc_kei -- 受注金額差引計
                    ,0 AS ekimu_sagaku_kei -- セット内差額計
                    ,dm.hoshi_umu_kbn -- 奉仕料有無区分
                    ,dm.hoshi_prc -- 奉仕料
                    ,sm.tnk_chg_kbn-- 売上単価変更区分
                    ,sm.nm_input_kbn-- 名称入力区分
                    ,sbm.hachu_kbn -- 発注書区分
                    ,sm.tani_cd-- 単位コード
                    ,sm.uri_kamoku_cd-- 科目コード
                    ,dm.zei_kbn -- 消費税区分
                    ,dm.gen_tnk-- 原価単価
                    ,'7' AS tab_kbn
                    ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
                    ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                        WHEN '0020' THEN '0'
                        ELSE '1'
                    END add_down_kbn
                    ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                        WHEN '0020' THEN '0'
                        ELSE '1'
                    END del_kbn
                    ,dm.add_kbn -- 追加区分 0:トランデータ
                    ,sbm.print_group_cd
                    ,sbm.syoukei_group_cd
                    ,dm.out_zei_prc
                    ,dm.in_zei_prc
                    ,dm.k_free3
                    ,dm.k_free4
                    ,dm.in_zei_prc
                    ,dm.add_cost
                    ,0 AS suryo_update_kbn -- 数量変更区分
                    ,0 AS add_flg  -- プラン商品かどうかのフラグ
                    ,dm.plan_shohin_cd
                    ,dm.plan_shohin_tnk
                    ,0 AS sagaku -- 差額の初期値保持用
                    ,dm.zei_cd  -- 2019/06/20 tosaka keigen
                    ,dm.reduced_tax_rate --  2019/06/20 tosaka keigen
                FROM
                    (
                        SELECT
                            m1.msi_no
                            ,m1.denpyo_kbn
                            ,m1.disp_no
                            ,m1.add_kbn
                            ,m1.dai_bunrui_cd
                            ,m1.chu_bunrui_cd
                            ,m1.shohin_kbn
                            ,skm.shohin_kbn_nm
                            ,m1.shohin_cd
                            ,m1.shohin_nm
                            ,m1.shohin_tkiyo_nm
                            ,m1.juchu_suryo
                            ,m1.uri_tnk
                            --,m1.juchu_suryo * m1.uri_tnk AS m1.juchu_prc
                            ,m1.hoshi_umu_kbn
                            ,m1.hoshi_prc
                            ,m1.gojokai_nebiki_prc
                            ,m1.nebiki_prc
                            ,m1.zei_kbn        
                            ,m1.gen_tnk
                            ,m1.mokuteki_kbn
                            ,m1.out_zei_prc
                            ,m1.in_zei_prc
                            ,m1.k_free3
                            ,m1.k_free4
                            ,COALESCE(m1.add_cost, 0) AS add_cost
                            ,m1.plan_shohin_cd
                            ,m1.plan_shohin_tnk
                            ,m1.zei_cd  -- 2019/06/20 tosaka keigen
                            ,m1.reduced_tax_rate -- 2019/06/20 tosaka keigen
                        FROM
                            uriage_denpyo d1
                        LEFT JOIN uriage_denpyo_msi m1
                        ON m1.uri_den_no = d1.uri_den_no
                        AND m1.delete_flg = 0
                        INNER JOIN shohin_kbn_mst skm
                        ON  (m1.shohin_kbn = skm.shohin_kbn
                        AND skm.delete_flg = 0)
                        WHERE d1.n_free1 = :n_free1
                        AND d1.pay_method_cd = '$this->_pay_method_cd_moke'
                        AND d1.kaishu_kbn = '$this->_kaishu_kbn_jisya'
                        AND d1.nyukin_prc = 0
                        AND d1.bun_gas_kbn_num IN (0)
                        AND d1.data_kbn IN (3,4)
                        AND CASE WHEN d1.data_kbn = 3 THEN d1.k_free1 = 0 ELSE d1.k_free1 is null END
                    ) dm
                INNER JOIN
                    shohin_chu_bunrui_mst cbm
                ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                    AND cbm.delete_flg = 0)
                INNER JOIN
                    shohin_mst sm
                ON  (
                        dm.shohin_cd = sm.shohin_cd
                    AND  sm.hihyoji_kbn = 0
                    AND sm.delete_flg = 0
                    )
                LEFT OUTER JOIN
                    shohin_bunrui_mst sbm
                ON  (
                        dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                        AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                        AND dm.shohin_kbn  = sbm.shohin_kbn
                        AND dm.shohin_cd  = sbm.shohin_cd
                        AND sbm.delete_flg = 0
                    )
                ORDER BY
                    sbm.mitumori_print_seq
                    ,dm.dai_bunrui_cd
                    ,dm.disp_no
                    ,dm.msi_no
                        ";
                $msiData1 = $db->easySelect($sql, array(
                    'n_free1' => $this->_mokeNo, 
                    ));
            } else {
                $msiData1 = array();
            }
            $sql2 = "
            SELECT
                dm.msi_no -- 明細番号
                ,dm.dai_bunrui_cd -- 大分類コード
                ,dm.chu_bunrui_cd -- 中分類コード
                ,cbm.chu_bunrui_nm -- 中分類名
                ,dm.shohin_kbn -- 商品区分
                ,dm.shohin_kbn_nm-- 商品区分名
                ,dm.shohin_cd -- 商品コード
                ,dm.shohin_nm -- 商品名
                ,dm.shohin_tkiyo_nm -- 商品摘要名
                ,dm.mokuteki_kbn -- 目的区分
                ,dm.juchu_suryo -- 数量
                ,dm.uri_tnk AS juchu_tnk-- 単価
                ,dm.juchu_suryo * uri_tnk AS juchu_prc -- 受注金額
                ,dm.gojokai_nebiki_prc -- 付帯値引き
                ,dm.nebiki_prc -- 値引き
                ,(dm.juchu_suryo * uri_tnk) + dm.gojokai_nebiki_prc + dm.nebiki_prc + hoshi_prc AS juchu_prc_kei -- 受注金額差引計
                ,0 AS ekimu_sagaku_kei -- セット内差額計
                ,dm.hoshi_umu_kbn -- 奉仕料有無区分
                ,dm.hoshi_prc -- 奉仕料
                ,sm.tnk_chg_kbn-- 売上単価変更区分
                ,sm.nm_input_kbn-- 名称入力区分
                ,sbm.hachu_kbn -- 発注書区分
                ,sm.tani_cd-- 単位コード
                ,sm.uri_kamoku_cd-- 科目コード
                ,dm.zei_kbn -- 消費税区分
                ,dm.gen_tnk-- 原価単価
                ,'7' AS tab_kbn
                ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
                ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                    WHEN '0020' THEN '0'
                    ELSE '1'
                END add_down_kbn
                ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                    WHEN '0020' THEN '0'
                    ELSE '1'
                END del_kbn
                ,dm.add_kbn -- 追加区分 0:トランデータ
                ,sbm.print_group_cd
                ,sbm.syoukei_group_cd
                ,dm.out_zei_prc
                ,dm.in_zei_prc
                ,dm.k_free3
                ,dm.k_free4
                ,dm.in_zei_prc
                ,dm.add_cost
                ,0 AS suryo_update_kbn -- 数量変更区分
                ,0 AS add_flg  -- プラン商品かどうかのフラグ
                ,dm.plan_shohin_cd
                ,dm.plan_shohin_tnk
                ,0 AS sagaku -- 差額の初期値保持用
                ,dm.zei_cd  -- 2019/06/20 tosaka keigen
                ,dm.reduced_tax_rate --  2019/06/20 tosaka keigen
            FROM
                (
                    SELECT
                        m1.msi_no
                        ,m1.denpyo_kbn
                        ,m1.disp_no
                        ,m1.add_kbn
                        ,m1.dai_bunrui_cd
                        ,m1.chu_bunrui_cd
                        ,m1.shohin_kbn
                        ,skm.shohin_kbn_nm
                        ,m1.shohin_cd
                        ,m1.shohin_nm
                        ,m1.shohin_tkiyo_nm
                        ,m1.juchu_suryo
                        ,m1.uri_tnk
                        --,m1.juchu_suryo * m1.uri_tnk AS m1.juchu_prc
                        ,m1.hoshi_umu_kbn
                        ,m1.hoshi_prc
                        ,m1.gojokai_nebiki_prc
                        ,m1.nebiki_prc
                        ,m1.zei_kbn        
                        ,m1.gen_tnk
                        ,m1.mokuteki_kbn
                        ,m1.out_zei_prc
                        ,m1.in_zei_prc
                        ,m1.k_free3
                        ,m1.k_free4
                        ,COALESCE(m1.add_cost, 0) AS add_cost
                        ,m1.plan_shohin_cd
                        ,m1.plan_shohin_tnk
                        ,m1.zei_cd  -- 2019/06/20 tosaka keigen
                        ,m1.reduced_tax_rate -- 2019/06/20 tosaka keigen
                    FROM
                        uriage_denpyo d1
                    LEFT JOIN uriage_denpyo_msi m1
                    ON m1.uri_den_no = d1.uri_den_no
                    AND m1.delete_flg = 0
                    INNER JOIN shohin_kbn_mst skm
                    ON  (m1.shohin_kbn = skm.shohin_kbn
                    AND skm.delete_flg = 0)
                    WHERE d1.n_free1 = :n_free1
                    AND d1.bun_gas_kbn_num IN (10)
                    AND d1.data_kbn IN (3,4)
                ) dm
            INNER JOIN
                shohin_chu_bunrui_mst cbm
            ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                AND cbm.delete_flg = 0)
            INNER JOIN
                shohin_mst sm
            ON  (
                    dm.shohin_cd = sm.shohin_cd
                AND  sm.hihyoji_kbn = 0
                AND sm.delete_flg = 0
                )
            LEFT OUTER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                    AND dm.shohin_kbn  = sbm.shohin_kbn
                    AND dm.shohin_cd  = sbm.shohin_cd
                    AND sbm.delete_flg = 0
                )
            ORDER BY
                sbm.mitumori_print_seq
                ,dm.dai_bunrui_cd
                ,dm.disp_no
                ,dm.msi_no
                    ";
            $msiData2 = $db->easySelect($sql2, array(
                'n_free1' => $this->_mokeNo, 
                ));
            $msiData = array_merge($msiData1, $msiData2);
        }
        return $msiData;
    }

    /**
     * 承認実行処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/16
     */
    public function shoninexe() {
        $db = Msi_Sys_DbManager::getMyDb();
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        $inkan_img = null;
        $shonin_dt = null;

        // 売上伝票番号を取得する
        $uri_den_no = $this->getUriagedenpyoNo();
        // 売上伝票の承認情報を取得する
        $dataUriShonin = $this->getUriageShoninInfo();

        // 事務→所属長の順番に承認する
        $status_kbn = 0;    // 2015/03/02 ADD Kayo
        if (!isset($dataUriShonin['inkan_img1'])) {
            $inkan_img = 'inkan_img1';
            $shonin_dt = 'shonin_dt1';
            // 分割されずかつ未入金かつ月次確定されていなければ合算処理を行う
            $fix_flg = $this->checkDataFix($db, $dataUriShonin['keijo_ymd']);
            if (!$fix_flg && $dataUriShonin['bun_gas_kbn_num'] === '0' && $dataUriShonin['nyukin_prc'] === '0') {
                Logic_JuchuDenpyoGasan::upsertFromShonin($this->_sekoNo);
            }
        } else if (!isset($dataUriShonin['inkan_img2'])) {
            $inkan_img = 'inkan_img2';
            $shonin_dt = 'shonin_dt2';
            $status_kbn = 1;    // 2015/03/02 ADD Kayo
            // 分割されずかつ未入金かつ月次確定されていなければ合算処理を行う
//            $fix_flg = $this->checkDataFix($db, $dataUriShonin['keijo_ymd']);
//            if (!$fix_flg && ($dataUriShonin['bun_gas_kbn_num'] === '0' || $dataUriShonin['bun_gas_kbn_num'] === '10') && $dataUriShonin['nyukin_prc'] === '0') {
//                Logic_JuchuDenpyoGasan::upsertFromShonin($this->_sekoNo);
//            }
        } else {
            // 承認済み
            $data['status'] = 'NG';
            $data['msg'] = '承認済みです。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $sql = "
        UPDATE
            uriage_denpyo
        SET 
            {$inkan_img} = :inkan_img
            ,{$shonin_dt} = :shonin_dt
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array(
            'uri_den_no' => $uri_den_no
            , 'inkan_img' => $db->copyBlob(App_Utils::getTantoInkanOid())
            , 'shonin_dt' => Msi_Sys_Utils::getDatetimeStd()
        ));
        // 施行基本情報のステータスを変更 2015/03/02 ADD Kayo
        if ($status_kbn == 1) {   
            $db->easyExecute(<<< END_OF_SQL
UPDATE	seko_kihon_info
SET		status_kbn	= 3     -- 3：請求済み
WHERE   delete_flg  = 0
AND		seko_no		= :seko_no
AND    not status_kbn IN (4,9)   -- 2016/03/02 ADD Kayo                   
END_OF_SQL
			,array(  'seko_no'=> $dataApp['seko_no']));
        }
        
        $db->commit();
        $data = $this->getData();

        // 画面データを設定する
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '承認しました';
        Msi_Sys_Utils::outJson($data);
        return $cnt;
    }

    
    /**
     *
     * 請求情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/18
     * @return array 請求情報
     */
    protected function getSeikyuData() {
        $dataSeikyu = App_Utils::getSekoSekyuInfo($this->_sekoNo);
        return $dataSeikyu;
    }
    /**
     * 承認取消処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/16
     */
    public function shonintorikesi() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 施行番号を設定する
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();
        // 売上伝票番号を取得する
        $uri_den_no = $this->getUriagedenpyoNo();
        // 合算元でかつ合算先が未入金の場合合算削除処理(所属長承認の取消の場合は合算削除はしない)
        if ($dateUriShonin['bun_gas_kbn_num'] === '10' && !isset($dateUriShonin['inkan_img2'])) {
            // 合算元の売上伝票から合算先の伝票データを取得する
            $select = DataMapper_UriageDenpyo::find($db, array('uri_den_no' => $uri_den_no));
            $select2 = DataMapper_UriageDenpyo::find($db, array('uri_den_no' => $select[0]['bun_gas_uri_den_no']));
            if (count($select2) > 0 && $select2[0]['nyukin_prc'] == 0) {
                Logic_JuchuDenpyoGasan::clearAllFromShonin($select2[0]);
                if ( Logic_SyukeiTblUpdate::SyukeiMain($db, $select[0]['denpyo_no'], $select[0]['data_kbn']) !== true ) {	// 2014/07/06 UPD Kayo
                    throw new Msi_Sys_Exception_LogicException( "集計処理でエラーが発生しました" );
                }
            }
        }
        // 所属長→事務の順番に承認取消する
        if (isset($dateUriShonin['inkan_img2'])) {
            $inkan_img = 'inkan_img2';
            $shonin_dt = 'shonin_dt2';
        } else if (isset($dateUriShonin['inkan_img1'])) {
            $inkan_img = 'inkan_img1';
            $shonin_dt = 'shonin_dt1';
        } else {
            // 承認済み
            $data['status'] = 'NG';
            $data['msg'] = '承認取消済みです。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $sql = "
        UPDATE
            uriage_denpyo
        SET 
            {$inkan_img} = NULL
            ,{$shonin_dt} = NULL
            ,bechu_uri_prc_sum = 0
            ,bechu_szei_katax_taisho_prc = 0
            ,bechu_uzei_katax_taisho_prc = 0
            ,bechu_hitax_katax_taisho_prc = 0
            ,bechu_out_zei_prc = 0
            ,bechu_in_zei_prc = 0
            ,tan_uri_prc_sum = 0
            ,tan_szei_katax_taisho_prc = 0
            ,tan_uzei_katax_taisho_prc = 0
            ,tan_hitax_katax_taisho_prc = 0
            ,tan_out_zei_prc = 0
            ,tan_in_zei_prc = 0
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array(
            'uri_den_no' => $uri_den_no
        ));
        // 施行基本情報のステータスを変更 2015/03/02 ADD Kayo
        $db->easyExecute(<<< END_OF_SQL
UPDATE	seko_kihon_info
SET		status_kbn	= 2     -- 2：施工中
WHERE   delete_flg  = 0
AND		seko_no		= :seko_no
AND    not status_kbn IN (4,9)   -- 2016/03/02 ADD Kayo                   
END_OF_SQL
		,array(  'seko_no'=> $dataApp['seko_no']));

        $db->commit();
        $data = $this->getData();

        // 画面データを設定する
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '承認を取り消しました';
        Msi_Sys_Utils::outJson($data);
        return $cnt;
    }
    
    /**
     * 月次確定チェック処理
     *
     * <AUTHOR> Sai
     * @since 2018/04/xx
     * @param Msi_Sys_Db $db
     * @param string $keijo_ymd 売上計上日
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     * @return 
     */
    private static function checkDataFix($db, $keijo_ymd) {
        // 月次確定チェック
        $flg = false;
        $fixInfo = DataMapper_DataFixTable::findOne($db, array('fix_kbn' => 1));
        if (count($fixInfo) > 0) {
            if ($keijo_ymd <= $fixInfo['fix_date_ymd']) {
                $flg = true;
            }
        }
        return $flg;
    }

}
