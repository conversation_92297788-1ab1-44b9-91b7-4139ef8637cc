{include file="fdn_head_std.tpl"}
{include file="fdn_header_1.tpl"}
<div class="container div-fixed-for-spin" id="main-container">
    <form  id="customerinfo-form-id">
        <div id="main">
            {include file="header_info.tpl"}
            {include file="side_menu.tpl"}
            <div id="detail">
                <div id = "customer-div-wrapper" style="display: none">
                    <fieldset class="base_1">
                        <label for="apply_type" class="lbl_apply_type require done">申込区分</label>
                        <input type="hidden" name="apply_type" id="apply_type" class="cls_apply_type"/>
                        <label for="funeral_type" class="lbl_funeral_type require done">葬儀区分</label>
                        <input type="hidden" name="funeral_type" id="funeral_type" class="cls_funeral_type"/>
                        <label for="code" class="lbl_code require done">台帳番号</label>
                        <input name="code_1" id="code_1" type="text" class="txt to_alpha_num" value="" maxlength = "2"/>
                        <div class="label code">-</div>
                        <input name="code_2" id="code_2" type="text" class="txt to_alpha_num" value="" maxlength = "2"/>
                        <div class="label code">-</div>
                        <input name="code_3" id="code_3" type="text" class="txt to_alpha_num" value="" maxlength = "4"/>
                        <label for="personal_info" class="lbl_personal_info require done">個人情報保護</label>
                        <input type="hidden" name="personal_info" id="personal_info" class="cls_personal_info"/>
                    </fieldset>
                    <fieldset class="base_2">
                        <label for="member" class="lbl_member require done">会員</label>
                        <input type="hidden" name="member" id="member" class="cls_member"/>
                        <input name="member_detail" id="member_detail" type="text" class="txt" value="" maxlength = "20"/>
                        <label for="staff_1" class="lbl_staff require">受付担当者</label>
                        <input name="staff_1" id="staff_1" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                        <div class="label dlg_staff dlg_staff1 cursor-pointer"></div>
                        <label for="staff_2" class="lbl_staff require done">施行担当者</label>
                        <input name="staff_2" id="staff_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                        <div class="label dlg_staff dlg_staff2 cursor-pointer"></div>
                    </fieldset>
                    <ul class="tab">
                        <li><span>基本情報</span></li>
                        <li><a href="javascript:void(0)">日程</a></li>
                        <li><a href="javascript:void(0)">喪主・御請求情報</a></li>
                        <li><a href="javascript:void(0)">{$kain_nm}確認</a></li>
                        <li><a href="javascript:void(0)">その他</a></li>
                        <li><a href="javascript:void(0)">貸出備品</a></li>
                        <li><a href="javascript:void(0)">報告書</a></li>
                    </ul>
                    <div id = "customer-tab">
                        {*基本情報タブ*}
                        <div id = "input-tab" class = "tab-contents">
                            <fieldset class="person_1 radio_set">
                                <label for="name" class="lbl_name require lbl_replace_kojin">故人お名前</label>
                                <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <input id = "name_clip" data-clip ="1" style="display: none" type=file accept="image/*">
                                <div class="label dlg_clip cursor-pointer"></div>
                                <div id = "name_clip_link" class="label"></div>
                                <div id = "name_clip_clear" class = ""></div>
                                <div class="lbl_sex subtitle require done">性別</div>
                                <span id="sex_set" class="radio_set">
                                    <label for="male" class="lbl_male">男性</label><input name="sex" class="radio_sex" id="male" type="radio" value="1" checked="checked" />
                                    <label for="female" class="lbl_female">女性</label><input name="sex" class="radio_sex" id="female" type="radio" value="2" />
                                </span>
                                <label for="spouse" class="lbl_spouse require done">配偶者</label>
                                <input type="hidden" name="spouse" id="spouse" class="cls_spouse"/>
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="kana" class="lbl_kana require done">フリガナ</label>
                                <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                                <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
                                <input name="birthday_date" id="birthday_date" type="text" class="txt date_auto_slash_s to_alpha_num" value="" maxlength = "8" {*placeholder="YYYY/MM/DD"*} />
                                <div class="label" id="age"></div>
                                <div class="label" id="age_at_death_pref"></div>
                                <input name="age_at_death" id="age_at_death" type="text" class="txt" value="" maxlength = "3"  />
                                <div class="label" id="age_at_death_suf"></div>
                            </fieldset>
                            <fieldset class="address_1">
                                <div class="lbl_address subtitle require done">現住所</div>
                                <label for="zip_1" class="lbl_zip done">〒</label>
                                <input name="zip_1" id="zip_1" type="text" data-zip = "k1" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_1_1" id="address_1_1" type="text" class="txt" value=""  maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="tel_1" class="require done">TEL</label>
                                <input name="tel_1" id="tel_1" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_1_2" id="address_1_2" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="head_1" class="require done">世帯主</label>
                                <input type="hidden" name="head_1" id="head_1" class="cls_head_1"/>
                            </fieldset>
                            <fieldset class="address_2">
                                <div class="lbl_address subtitle option">
                                    住民登録住所<br />
                                    <span class="radio_set">
                                        <input name="as_address_2" id="as_address_2" type="checkbox" value="1" />
                                        <label for="as_address_2" class="lbl_as_address">現住所に同じ</label>
                                    </span>
                                </div>
                                <label for="zip_2" class="lbl_zip done">〒</label>
                                <input name="zip_2" id="zip_2" type="text" data-zip = "k2" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_2_1" id="address_2_1" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="tel_2" class="option">TEL</label>
                                <input name="tel_2" id="tel_2" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_2_2" id="address_2_2" type="text" class="txt" value="" maxlength = "30"/>
                                <label for="head_2" class="option">世帯主</label>
                                <input type="hidden" name="head_2" id="head_2" class="cls_head_2"/>
                            </fieldset>
                            <fieldset class="address_3">
                                <div class="lbl_address subtitle option">本籍<br />
                                    <span class="radio_set">
                                        <input name="as_address_3" id="as_address_3" type="checkbox" value="1" />
                                        <label for="as_address_3" class="lbl_as_address">現住所に同じ</label>
                                    </span>
                                </div>
                                <label for="zip_3" class="lbl_zip done">〒</label>
                                <input name="zip_3" id="zip_3" type="text" data-zip = "k3" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_3_1" id="address_3_1" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="head_3" class="option">筆頭者</label>
                                <input type="hidden" name="head_3" id="head_3" class="cls_head_3"/>
                                <input name="address_3_2" id="address_3_2" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="business_1">
                                <label for="company" class="option">勤務先</label>

                                <input type="hidden" name="employee" id="employee" class="cls_employee"/>
                                <input name="company" id="company" type="text" class="txt" value="" maxlength = "30"/>
                                <label for="company_tel" class="option">TEL</label>
                                <input name="company_tel" id="company_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                            </fieldset>
                            <fieldset class="business_2">
                                <label for="position" class="option">役職／職種</label>
                                <input name="position" id="position" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_position"></div>
                                <label for="company_fax" class="option">FAX</label>
                                <input name="company_fax" id="company_fax" type="text" class="txt to_alpha_num" value=""  maxlength = "15" placeholder="************"/>
                            </fieldset>
                            <fieldset class="funeral_1">
                                <label for="family_name" class="lbl_family_name require done">葬家</label>
                                <input name="family_name" id="family_name" type="text" class="txt" value=""  maxlength = "10"/>
                                <div class="label" id="lbl_family_name2">家</div>
                                <label for="family_name_kana" class="lbl_family_name_soke option require done">フリガナ</label>
                                <input name="family_name_kana" id="family_name_kana" type="text" class="txt" value="" maxlength = "12"/>
                                <label for="keishiki_cd" class="lbl_keishiki_cd option require done">住所</label>
                                <input type="hidden" id="keishiki_cd" name="keishiki_cd" class="cls_keishiki_cd"/>
                                <label for="family_tel" class="lbl_family_tel require done">TEL</label>
                                <input name="family_tel" id="family_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                            </fieldset>
                            <fieldset class="funeral_2" style="margin-bottom: 10px;">
                                <label for="funeral_style" class="lbl_funeral_style require done">形式</label>
                                <input type="hidden" name="funeral_style" id="funeral_style" class="cls_funeral_style"/>
                                <label for="syushi_cd" class="lbl_religion option require done">宗旨</label>
                                <input type="hidden" id="syushi_cd" name="syuha_cd" class="cls_syushi_cd"/>
                                <input type="hidden" id="syushi_cd" name="syushi_cd" class="cls_syushi_cd"/>
                                <label for="syuha_cd" class="lbl_denomination require done">宗派</label>
                                <input type="hidden" id="syuha_cd" name="syuha_cd" class="cls_syuha_cd"/>
                                <label for="temple" class="lbl_temple require done">寺院名</label>
                                <input name="temple" id="temple" type="text" class="txt" value="" maxlength = "30" class="cls_temple" data-kind2 = "1" data-code = "jyusho_cd" data-name = "jyusho_nm" readonly="readonly"/>
                                <div class="label dlg_temple cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="funeral_3">
                                <label for="funeral_scale" class="lbl_funeral_scale require">葬儀規模</label>
                                <input type="hidden" name="funeral_scale" id="funeral_scale" class="cls_funeral_scale"/>
								<span class="radio_set">
									<input name="as_hatubutu" id="as_hatubutu" type="checkbox" value="1" />
									<label for="as_hatubutu" class="lbl_as_hatubutu">初仏</label>
								</span>
								<span class="radio_set">
									<input name="as_butudan" id="as_butudan" type="checkbox" value="1" />
									<label for="as_butudan" class="lbl_as_butudan">仏壇</label>
								</span>
								<span class="radio_set">
									<input name="as_boseki" id="as_boseki" type="checkbox" value="1" />
									<label for="as_boseki" class="lbl_as_boseki">墓石</label>
								</span>
								<span class="radio_set">
									<input name="as_ihai" id="as_ihai" type="checkbox" value="1" />
									<label for="as_ihai" class="lbl_as_ihai">位牌</label>
								</span>
								<span class="radio_set">
									<input name="as_kaimyo" id="as_kaimyo" type="checkbox" value="1" />
									<label for="as_kaimyo" class="lbl_as_kaimyo">戒名彫</label>
								</span>
								<span class="radio_set">
									<input name="as_boseki_rehome" id="as_boseki_rehome" type="checkbox" value="1" />
									<label for="as_boseki_rehome" class="lbl_as_boseki_rehome">墓石リフォーム</label>
								</span>
								<span class="radio_set">
									<input name="as_butudan_rehome" id="as_butudan_rehome" type="checkbox" value="1" />
									<label for="as_butudan_rehome" class="lbl_as_butudan_rehome">仏壇リフォーム</label>
								</span>
								<span class="radio_set">
									<input name="as_sonota" id="as_sonota" type="checkbox" value="1" />
									<label for="as_sonota" class="lbl_as_sonota">その他</label>
								</span>
                                <input name="info_sonota" id="info_sonota" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="memo">
                                <label for="memo" class="lbl_memo option">メモ（出棺経路・納骨・壇払など）</label>
                                <textarea name="memo" id="memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                            </fieldset>
                            <fieldset class="info_memo">
                                <label for="info_memo" class="lbl_info_memo option">情報内容（詳細・備考）</label>
                                <textarea name="info_memo" id="info_memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                            </fieldset>
                        </div>
                        {*日程タブ*}
                        <div id = "infodate-tab" class = "tab-contents off">
                            <div id="infodate">
                            </div>
                            <div id="add_charge">
                                <h3>別途費用</h3>
                                <fieldset class="add_charge">
                                    <label for="add_place_1" class="lbl_add_place require done">式場使用料</label>
                                    <input name="add_place_1" id="add_place_1" type="text" class="txt place to_alpha_num" value=""  maxlength = "30" readonly="readonly"/>
                                    <div class="label dlg_place add_place_1 cursor-pointer"></div>
                                    <input name="total_1" id="total_1" type="text" class="txt add_total to_alpha_num" value=""  maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                                </fieldset>
                                <fieldset class="add_charge">
                                    <label for="add_place_2" class="lbl_place require done">通夜会場使用料</label>
                                    <input name="add_place_2" id="add_place_2" type="text" class="txt place to_alpha_num" value=""  maxlength = "30" readonly="readonly"/>
                                    <div class="label dlg_place add_place_2 cursor-pointer"></div>
                                    <input name="nights" id="nights" type="text" class="txt nights to_alpha_num" value="" maxlength = "3" readonly="readonly"/>
                                    <div class="label lbl_nights">泊</div>
                                    <input name="price_2" id="price_2" type="text" class="txt add_price to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                                    <div class="label lbl_add_eq">＝</div>
                                    <input name="total_2" id="total_2" type="text" class="txt add_total" value="" readonly="readonly" />
                                </fieldset>
                                <fieldset class="add_charge">
                                    <input name="nights2" id="nights2" type="text" class="txt nights to_alpha_num" value="" maxlength = "3" readonly="readonly"/>
                                    <div class="label lbl_nights">泊</div>
                                    <input name="price_3" id="price_3" type="text" class="txt add_price to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this));" readonly="readonly"/>
                                    <div class="label lbl_add_eq">＝</div>
                                    <input name="total_3" id="total_3" type="text" class="txt add_total" value="" readonly="readonly" />
                                </fieldset>
                            </div>
                        </div>
                        {*喪主タブ*}
                        <div id = "infochief-tab" class = "tab-contents off">
                            <div id="chief" class="info_area">
                                <h3 class = "lbl_replace_moshu">喪主</h3>
                                <fieldset class="person_1 radio_set">
                                    <label for="name" class="lbl_name require lbl_replace_moshu">喪主お名前</label>
                                    <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label lbl_name_sama">様</div>
                                    <input id = "name_clip"  data-clip ="2" style="display: none" type=file accept="image/*">
                                    <div class="label dlg_clip cursor-pointer"></div>
                                    <div id = "name_clip_link" class="label"></div>
                                    <div id = "name_clip_clear" class = ""></div>
                                    <label for="chief_relationship" class="lbl_relationship require done">続柄</label>
                                    <input type="hidden" name="s_chief_relationship" id="s_chief_relationship" class="cls_s_chief_relationship"/>
                                    <label for="chief_relationship2" class="lbl_relationship require done">喪主様からみた続柄</label>
                                    <input type="hidden" name="s_chief_relationship2" id="s_chief_relationship2" class="cls_s_chief_relationship"/>
                                </fieldset>
                                <fieldset class="person_2">
                                    <label for="kana" class="lbl_kana require done">フリガナ</label>
                                    <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                                    <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
                                    <input name="birthday_date" id="birthday_date" type="text" class="txt date_auto_slash_s to_alpha_num" value="" maxlength = "8"/>
                                    <div class="label" id="age"></div>
                                </fieldset>
                                <fieldset class="address_1">
                                    <div class="lbl_address subtitle option">現住所<br />
                                        <span class="radio_set">
                                            <input name="as_address_4" id="as_address_4" type="checkbox" value="1" />
                                            <label for="as_address_4" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                        </span>
                                    </div>
                                    <label for="zip_1" class="lbl_zip done">〒</label>
                                    <input name="zip_1" id="zip_1" type="text" data-zip = "m1" class="txt zip_helper to_alpha_num" value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_1_1" id="address_1_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_1" class="option require">TEL</label>
                                    <input name="tel_1" id="tel_1" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_1_2" id="address_1_2" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="mobile_tel_1" class="option">携帯</label>
                                    <input name="mobile_tel_1" id="mobile_tel_1" type="text" class="txt ime-off to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                                </fieldset>
                                <fieldset class="address_2">
                                    <div class="lbl_address subtitle option">住民登録住所<br />
                                        <span class="radio_set">
                                            <input name="as_address_5" id="as_address_5" type="checkbox" value="1" />
                                            <label for="as_address_5" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                        </span>
                                    </div>
                                    <label for="zip_2" class="lbl_zip done">〒</label>
                                    <input name="zip_2" id="zip_2" type="text" data-zip = "m2" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_2_1" id="address_2_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_2" class="option">TEL</label>
                                    <input name="tel_2" id="tel_2" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_2_2" id="address_2_2" type="text" class="txt" value="" maxlength = "30"/>
                                </fieldset>
                                <fieldset class="address_3">
                                    <div class="lbl_address subtitle option">本籍<br />
                                        <span class="radio_set">
                                            <input name="as_address_6" id="as_address_6" type="checkbox" value="1" />
                                            <label for="as_address_6" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                        </span>
                                    </div>
                                    <label for="zip_3" class="lbl_zip done">〒</label>
                                    <input name="zip_3" id="zip_3" type="text" data-zip = "m3" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_3_1" id="address_3_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <input name="address_3_2" id="address_3_2" type="text" class="txt" value="" maxlength = "30"/>
                                </fieldset>
                                <fieldset class="business_1">
                                    <label for="company" class="option">勤務先</label>
                                    <input type="hidden" name="employee" id="employee" class="cls_employee"/>
                                    <input name="company" id="company" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="company_tel" class="option">TEL</label>
                                    <input name="company_tel" id="company_tel" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                </fieldset>
                                <fieldset class="business_2">
                                    <label for="position" class="option">役職／職種</label>
                                    <input name="position" id="position" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_position"></div>
                                    <label for="company_fax" class="option">FAX</label>
                                    <input name="company_fax" id="company_fax" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                </fieldset>
                            </div>
                            <div id="bill" class="info_area">
                                <h3>請求先</h3>
                                <span class="radio_set">
                                    <input name="as_chief" id="as_chief" type="checkbox" value="1" />
                                    <label for="as_chief" class="lbl_as_chief lbl_replace_moshu">喪主に同じ</label>
                                </span>
                                <fieldset class="person_1 radio_set">
                                    <label for="name" class="lbl_name require done">御請求先名</label>
                                    <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label lbl_name_sama">様</div>
                                    <input id = "name_clip"  data-clip ="3" style="display: none" type=file accept="image/*">
                                    <div class="label dlg_clip cursor-pointer"></div>
                                    <div id = "name_clip_link" class="label"></div>
                                    <div id = "name_clip_clear" class = ""></div>
                                    <label for="bill_relationship" class="lbl_bill_relationship require done lbl_replace_moshu">喪主との関係</label>
                                    <input type="hidden" name="s_bill_relationship" id="s_bill_relationship" class="cls_s_bill_relationship"/>
                                    <input name="bill_relationship_name" id="bill_relationship_name" type="text" class="txt" value="" maxlength = "10"/>
                                    <div class="label dlg_relationship"></div>
                                </fieldset>
                                <fieldset class="person_2">
                                    <label for="kana" class="lbl_kana require done">フリガナ</label>
                                    <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                </fieldset>
                                <fieldset class="bill_address">
                                    <div class="lbl_address subtitle require done">現住所</div>
                                    <label for="zip_4" class="lbl_zip done">〒</label>
                                    <input name="zip_4" id="zip_4" type="text" data-zip = "s1" class="txt zip_helper to_alpha_num"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_4_1" id="address_4_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_4" class="require done">TEL</label>
                                    <input name="tel_4" id="tel_4" type="text" class="txt to_alpha_num" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_4_2" id="address_4_2" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="mobile_tel_2" class="option done">携帯</label>
                                    <input name="mobile_tel_2" id="mobile_tel_2" type="text" class="txt ime-off to_alpha_num" value="" maxlength = "15" placeholder="000-0000-0000"/>
                                </fieldset>
                                <fieldset class="memo">
                                    <label for="memo" class="lbl_memo option">備考</label>
                                    <input name="memo" id="memo" type="text" class="txt" value="" maxlength = "60"/>
                                </fieldset>
                            </div>
                        </div>
                        {*互助会タブ*}
                        {include file="juchu/customerinfo/gojokai_kanyu_tmpl.tpl"}
                        {*その他タブ*}
                        <div id = "infomisc-tab" class = "tab-contents off">
                            <div id="misc_1" class="misc">
                                <h3>診断書</h3>
                                <fieldset class="karte_1 radio_set">
                                    <div class="lbl_publish subtitle">発行</div>
                                    <span id="publish_set">
                                        <label for="finished" class="lbl_finished">済</label><input name="publish" id="finished" type="radio" value="1"  />
                                        <label for="unfinished" class="lbl_unfinished">未</label><input name="publish" id="unfinished" type="radio" value="2" checked="checked"/>
                                    </span>
                                    <label for="steps" class="lbl_steps">手続き</label>
                                    <input type="hidden" name="steps" id="steps" class="cls_steps"/>
                                </fieldset>
                                <fieldset class="karte_2">
                                    <label for="datetime" class="lbl_datetime option">発行予定日時</label>
                                    <input name="date" id="date" type="text" class="txt date_auto_slash to_alpha_num" value="" maxlength = "10"/>
                                    <div class="label dlg_date"></div>
                                    <input name="time" id="time" type="text" class="txt time_auto_colon to_alpha_num" value="" maxlength = "5"/>
                                    <div class="label dlg_time"></div>
                                    <label for="copy" class="lbl_copy option">コピー</label>
                                    <input type="hidden" name="copys" id="copys" class="cls_copys"/>
                                </fieldset>
                            </div>
                            <div id="misc_2" class="misc">
                                <h3>搬送業務</h3>
                                <fieldset class="transport_1">
                                    <div class="lbl_transport subtitle option">搬送</div>
                                    <span id="transport_set" class="radio_set">
                                        <label for="transport_yes" class="lbl_transport_check">有</label><input name="transport" id="transport_yes" type="radio" value="1" />
                                        <label for="transport_no" class="lbl_transport_check">無</label><input name="transport" id="transport_no" type="radio" value="2" checked="checked" />
                                    </span>
                                </fieldset>
                                <fieldset class="transport_2">
                                    <label for="pickup" class="lbl_pickup">お伺い先</label>
                                    <input type="hidden" name="pickup_type" id="pickup_type" class="cls_pickup_type"/>
                                    <input name="pickup_name" id="pickup_name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_pickup_name cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="transport_3">
                                    <label for="dropoff" class="lbl_dropoff">安置先</label>
                                    <input type="hidden" name="dropoff_type" id="dropoff_type" class="cls_dropoff_type"/>
                                    <input name="dropoff_name" id="dropoff_name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_dropoff_name cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="transport_4">
                                    <label for="crematorium" class="lbl_crematorium">火葬場</label>
                                    <input name="crematorium" id="crematorium" type="text" class="txt" value="" maxlength = "30" data-kind2 = "3" data-code = "kasoba_cd" data-name = "kasoba_nm" readonly="readonly"/>
                                    <div class="label dlg_crematorium cursor-pointer"></div>
                                </fieldset>
                            </div>
                            <div id="misc_3" class="misc">
                                <h3>お預り品</h3>
                                <fieldset class="deposit_1">
                                    <label for="certificate" class="lbl_certificate option">死亡診断書</label>
                                    <input type="hidden" name="certificate" id="certificate" class="cls_certificate"/>
                                    <div class="lbl_stamp subtitle option">印鑑</div>
                                    <span id="stamp_set" class="radio_set">
                                        <input name="stamp" id="stamp" type="checkbox" value="1" /><label for="stamp" class="lbl_stamp_check"></label>
                                    </span>
                                    <label for="portrait" class="lbl_portrait option">御写真</label>
                                    <input type="hidden" name="portrait" id="portrait" class="cls_portrait"/>
                                </fieldset>
                                <fieldset class="deposit_4">
                                    <label for="membership" class="lbl_membership option">その他</label>
                                    <input name="membership" id="membership" type="text" class="txt" value="" maxlength = "30" />
                                </fieldset>
                            </div>
                            <div id="misc_4" class="misc">
                                <h3>隣組</h3>
                                <fieldset class="neighborhood_1">
                                    <label for="association" class="lbl_association option">町内会名</label>
                                    <input name="association" id="association" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_association"></div>
                                </fieldset>
                                <fieldset class="neighborhood_2">
                                    <label for="leader" class="lbl_leader option">隣組長様</label>
                                    <input name="leader" id="leader" type="text" class="txt" value="" maxlength = "30"/>
                                    <input type="text" name="doors" id="doors" class="txt cls_doors to_alpha_num" maxlength = "2"/>
                                    <div class="label lbl_name_sama">軒</div>
                                    <input type="text" name="persons" id="persons" class="txt cls_persons to_alpha_num" maxlength = "2"/>
                                    <div class="label lbl_name_sama">名</div>
                                </fieldset>
                                <fieldset class="neighborhood_3">
                                    <label for="area" id="lbl_area" class="lbl_area option">地区</label>
                                    <input type="hidden" id="area" class="cls_area"/>
                                    <label for="group" id="lbl_group" class="lbl_group option">組</label>
                                    <input type="hidden" id="group" class="cls_group"/>
                                </fieldset>
                            </div>
                            <div id="misc_5" class="misc">
                                <h3>目録手配</h3>
                                <fieldset class="catalog_1 radio_set">
                                    <div class="lbl_catalog subtitle option">依頼</div>
                                    <span id="catalog_set">
                                        <label for="catalog_yes" class="lbl_catalog_check">有</label><input name="catalog" id="catalog_yes" type="radio" value="1"  />
                                        <label for="catalog_no" class="lbl_catalog_check">無</label><input name="catalog" id="catalog_no" type="radio" value="2" checked="checked" />
                                    </span>
                                    <input type="hidden" name="catalog_bumon" id="catalog_bumon" class="cls_catalog_bumon"/>
                                </fieldset>
                                <fieldset class="catalog_2">
                                    <label for="catalog_area" class="lbl_catalog_area option">対象エリア</label>
                                    <input type="hidden" name="catalog_area" id="catalog_area" class="cls_catalog_area"/>
                                </fieldset>
                                <fieldset class="catalog_3">
                                    <label for="catalog_item" class="lbl_catalog_item option">手配品目</label>
                                    <input type="hidden" name="catalog_item" id="catalog_item" class="cls_catalog_item"/>
                                </fieldset>
                                <fieldset class="catalog_4">
                                    <label for="catalog_datetime" class="lbl_datetime option">搬入日時</label>
                                    <input name="catalog_date" id="catalog_date" type="text" class="txt ime-off date_auto_slash to_alpha_num" value="" />
                                    <div class="label dlg_date"></div>
                                    <input name="catalog_time_from" id="catalog_time_from" type="text" class="txt ime-off time_auto_colon to_alpha_num" value="" />
                                    <div class="label dlg_time" id="lbl_dlg_time_from"></div>
                                    <div class="label lbl_catalog_time">〜</div>
                                    <input name="catalog_time_to" id="catalog_time_to" type="text" class="txt ime-off time_auto_colon to_alpha_num" value="" />
                                    <div class="label dlg_time"></div>
                                </fieldset>
                                <fieldset class="catalog_5">
                                    <label for="delivery" class="lbl_catalog_place">搬入場所</label>
                                    <input type="hidden" id="delivery" class="cls_delivery"/>
                                    <input name="catalog_place" id="catalog_place" type="text" class="txt" value="" maxlength="30"/>
                                    {*                                    <div class="label dlg_catalog_place cursor-pointer"></div>*}
                                </fieldset>
                                <fieldset class="catalog_6">
                                    <label for="delivery_memo_cd" class="lbl_catalog_memo">備考</label>
                                    <input type="hidden" id="delivery_memo_cd" class="cls_delivery_memo_cd"/>
                                </fieldset>
                                <fieldset class="catalog_7 foot-margin-bottom">
                                    <textarea name="delivery_memo" id="delivery_memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                                </fieldset>
                            </div>
                        </div>
                        {*貸出備品タブ*}
                        <div id = "inforental-tab" class = "tab-contents off">
                            <div id="items">
                                <ul>
                                    <div id="kashidasi-bihin">
                                    </div>
                                    <li class="add"><a href="javascript:void(0)" id = "item_add" class="item_ad">+</a></li>
                                </ul>
                            </div>
                        </div>

                        {*報告書タブ*}
                        {include file="juchu/customerinfo/report_tmpl.tpl"}
                    </div>
                    <div class="buttons">
                        <input type="button" name="btn_save" id="btn_save" value="保存" />
                        <input type="button" name="btn_print" id="btn_print" value="印刷" />
                        <input type="button" name="btn_appform_ykn" id="btn_appform_ykn" value="申請書(湯)" style="display: none;"/>
                        <input type="button" name="btn_appform_sik" id="btn_appform_sik" value="申請書(生)" style="display: none;"/>
                        <input type="button" name="btn_appform_uktk" id="btn_appform_uktk" value="申請書(受)" style="display: none;"/>
                        <!-- <input type="button" name="btn_print_info" id="btn_print_info" value="情報提供" /> -->
                        <input type="button" name="btn_delete" id="btn_delete" value="削除" />
                        <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
                        <input type="button" name="btn_consult" id="btn_consult" value="相談履歴へ" />
                        <input type="button" name="btn_seko_copy" id="btn_seko_copy" value="葬儀施行コピー" />
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{literal}
    <!-- 日程タブ 亡日テンプレート -->
    <script type="text/template" id="tmpl-nitei-1">
        <label class="lbl_date done">亡日</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" data-kind2 = "5" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 湯灌テンプレート -->
    <script type="text/template" id="tmpl-nitei-2">
        <label class="lbl_date done">湯灌</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm" value="" maxlength = "30" />
        <div class="label dlg_place cursor-pointer disabled"></div>
    </script>
    <!-- 日程タブ 入棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-3">
        <label class="lbl_date done">入棺</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value=""  maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">入棺経</label>
        <input type="text" class="sutra nyukan_kyo"/>
    </script>
    <!-- 日程タブ 通夜テンプレート -->
    <script type="text/template" id="tmpl-nitei-4">
        <label class="lbl_date done">通夜</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm" value="" data-kind2 = "7" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 出棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-5">
        <label class="lbl_date done">出棺</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">出棺経</label>
        <input type="hidden" class="sutra syukan_kyo"/>
    </script>
    <!-- 日程タブ 火葬テンプレート -->
    <script type="text/template" id="tmpl-nitei-6">
        <label class="lbl_date done">火葬</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm" value="" data-kind2 = "3" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">火葬経</label>
        <input type="hidden" class="sutra kaso_kyo"/>
    </script>
    <!-- 日程タブ 葬儀テンプレート -->
    <script type="text/template" id="tmpl-nitei-7">
        <label class="lbl_date done">葬儀</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value=""  data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra ">司式者</label>
        <input type="hidden" class="sutra shishikisha_ninzu"/>
    </script>
    <!-- 日程タブ 壇払いテンプレート -->
    <script type="text/template" id="tmpl-nitei-8">
        <label class="lbl_date done">壇払</label>
        <input type="text" class="txt date nitei_date date_auto_slash" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time time_auto_colon" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 貸出備品テンプレート -->
    <script type="text/template" id="tmpl-kashidasi-bihin">
        <div class = "delete"></div>
        <% if (nm_input_kbn == '2' || nm_input_kbn == '4') { %>
        <input class="name nm_input" maxlength = "30"></input>
        <% } else { %>
        <span class="name"></span>
        <% }%>
        <span class="num">0</span>
        <span class="minus">-</span>
        <span class="plus">+</span>
        <fieldset class="f_return">
        <label class="lbl_return">回収<br />予定</label>
        <input class="return txt date_auto_slash to_alpha_num" />
        </fieldset>
    </script>
{/literal}

<script id="data-customerinfo" type="application/json">
    {$customerinfo_json|smarty:nodefaults}
</script>
{include file="fdn_footer_std.tpl"}
