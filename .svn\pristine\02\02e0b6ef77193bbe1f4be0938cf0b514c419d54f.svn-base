{include file="fdn_head_std.tpl"}
{include file="fdn_header_0.tpl"}

{*include file="header_info.tpl"*}

<form id="my-form-id" method="post" class="{$ctxt_readonly}">
    <div id="main">
        <div id="order" >
            {include file="saiken/seikyusyo/printer.tpl"}

            <div class="page-title"><span>{$page_title}</span></div>
            <div id="searchbtnarea">
                <input name="btn_search" id="btn_search" type="button" class="my-no-readonly" value="検索" />
                <input name="btn_clear" id="btn_clear" type="button" class="my-no-readonly" value="クリア" />
            </div>

            <div class="search">
                <!-- 検索条件 -->
                <fieldset class="base_1">
                    <label for="seko_no" class="lbl_seko_no option">施行No</label>
                    <input name="seko_no" id="seko_no" type="text" class="txt cursor-pointer my-no-readonly tanto" value="" style="ime-mode: disabled"/>
                    <div class="label dlg_common seko_no-ref lbl_dlg_seko_no"></div>
                    <label for="r_sum_sekyu_prc" class="lbl_sum_sekyu_prc option">合計金額</label>
                    <input name="r_sum_sekyu_prc" id="r_sum_sekyu_prc" type="text" class="txt" value="" readonly />
                    <!--
                    <label class="lbl_staff require">請求書発行担当者</label>
                    <input name="tanto_cd" id="tanto_cd" type="text" class="txt staff_cd" value="">
                    <input name="tanto_nm" id="tanto_nm" type="text" class="txt staff_nm" value="" readonly="">
                    <div id="tanto_dlg" class="label dlg_staff dlg_staff1 cursor-pointer"></div>
                    -->
                    <div class="dummy_51"></div>
                    <label for="r_shiharai_kbn" class="lbl_r_shiharai_kbn" style="margin-left:5px;">支払方法</label>
                    <input type="hidden" id="r_shiharai_kbn" name="r_shiharai_kbn" value=""
                           class="msi-picker s_issue_st-cls my-no-readonly" data-picker-kind="cdNm"
                           data-picker-param="kind2:'1130',placeholder:' ',fmt:'tags2'" />
                    <label for="r_nafuda" class="lbl_r_nafuda option">名札</label>
                    <input name="r_nafuda" id="r_nafuda" type="text" class="txt my-no-readonly" value="" />
                </fieldset>

                <fieldset class="base_2">
                    <label for="r_name_2" class="lbl_r_name_2">故人名</label>
                    <input type="text" name="r_name_2" id="r_name_2" class="txt" value="" readonly />

                    <label for="r_addr" class="lbl_r_addr option" id="r_addr_label">喪主住所</label>
                    <input type="text" name="r_addr" id="r_addr" class="txt" value="" style="width:28%" readonly />

                    <label for="r_seikyu_saki" class="lbl_r_seikyu_saki option" style="margin-left:5px;">請求先</label>
                    <input name="r_seikyu_saki" id="r_seikyu_saki" type="text" class="txt my-no-readonly" value="" />

                    <label for="r_seikyu_tel" class="lbl_r_seikyu_tel option">電話</label>
                    <input name="r_seikyu_tel" id="r_seikyu_tel" type="text" class="txt  my-no-readonly" value="" />

                    <div id="searchbtnareax">
                        <input name="btn_select" id="btn_select" type="button" class="my-no-readonly" value="絞込み" />
                    </div>
                </fieldset>

                <fieldset class="base_3">
                    <label for="r_name_3" class="lbl_r_name_3 option"  id="r_name_label">喪主名</label>
                    <input type="text" name="r_name_3" id="r_name_3" class="txt" value="" readonly />

                    <label for="r_sougi_bi" class="lbl_sougi_bi option" id="r_sougi_bi_label">葬儀日</label>
                    <input name="r_sougi_bi" id="r_sougi_bi" type="text" class="txt" value="" maxlength="10" readonly />

                    <label for="r_staff_2" class="lbl_r_staff_2 option">施行担当</label>
                    <input name="r_staff_2" id="r_staff_2" type="text" class="txt" value="" readonly />

                    <label for="r_seikyu_addr" class="lbl_r_seikyu_addr option" style="margin-left:5px;">請求先住所</label>
                    <input name="r_seikyu_addr" id="r_seikyu_addr" type="text" class="txt my-no-readonly" value="" />
                </fieldset>

            </div><!-- /.search -->

            {include file="juchu/bechulist/result-list.tpl"}

            <!-- 処理ボタン -->
            <div class="buttons sekolist-cmd-buttons">
                <input type="button" name="btn_cre" id="btn_cre" value="新規登録" />
                <input type="button" name="btn_upd" id="btn_upd" value="修正・削除" />
                <!-- input type="button" name="btn_del" id="btn_del" value="削除" / -->
                {if $is_gohoumei_kbn}<input type="button" name="btn_print_gohonm" id="btn_print_gohonm" value="御芳名" />{/if}
                <input type="button" name="btn_print" id="btn_print" value="印刷" />
                <input type="button" name="btn_csvout" id="btn_csvout" value="CSV" />
                {if $is_seikyu_hako_kbn_nrs}<input type="button" name="btn_print_nyu_ryo_sei" id="btn_print_nyu_ryo_sei" value="入金・領収・請求書" />{/if}
                {if $is_seikyu_hako_kbn}<input type="button" name="btn_print_sei" id="btn_print_sei" value="請求書" />{/if}
                {if $is_konbini_hako_kbn}<input type="button" name="btn_print_konbini" id="btn_print_konbini" value="コンビニ請求書" />{/if}
                {if $is_ryoshu_btn}<input type="button" name="btn_ryoshu_collective" id="btn_ryoshu_collective" value="領収書"/>{/if}
                {if $is_ryoshu_btn}<input type="button" name="btn_re_ryoshu_collective" id="btn_re_ryoshu_collective" value="領収書再発行"/>{/if}

            </div><!-- /.buttons -->

        </div><!-- /#order -->

    </div><!-- /#main -->
</form><!-- /#my-form-id -->


<script id="my-data-init-id" type="application/json">
    {$mydata_json|smarty:nodefaults}
</script>


{include file="fdn_footer_std.tpl"}
