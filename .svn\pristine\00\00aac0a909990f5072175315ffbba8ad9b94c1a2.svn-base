/**
 * @fileoverview googlemap連動施行情報画面
 */
var _map = null; // Mapオブジェクト
$(function () {
    "use strict";

    var AppModel = Backbone.Model.extend({

        defaults: function () {
            return {
                taisho_st_ymd: null,
                taisho_ed_ymd: null,
                key: null,
            };
        },

        validation: {
            taisho_st_ymd: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            taisho_ed_ymd: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
        },

        labels: {
            taisho_st_ymd: '対象年月日(自)',
            taisho_ed_ymd: '対象年月日(至)',
        }

    }); // AppModel

    var AppView = Backbone.View.extend({

        el: document, // '#my-form-id', // for #hall_cd

        events: {
            "click #btn_display_map": "showMap",
        },

        initialize: function () {
            Backbone.Validation.bind(this);
            this.render();
        },

        render: function () {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind(this, {
                valid: function (view, attr, selector) {
                    var $el = view.$('#' + attr),
                            $group = $el.closest('.input-group');
                    $group = $group || $el;

                    $el.removeClass('my-error');
                    $el.attr('title', '');
                    $group.removeClass('has-error');
                },
                invalid: function (view, attr, error, selector) {
                    var $el = view.$('#' + attr),
                            $group = $el.closest('.input-group');
                    $group = $group || $el;

                    $el.addClass('my-error');
                    $el.attr('title', error);
                    $group.addClass('has-error');
                }
            });

            this.stickit();


            this.btnEnabled('#btn_display');
            return this;
        },

        btnDisabled: function (elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },

        btnEnabled: function (elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },

        isInputOk: function () {
            this.clearErr();

            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // NG
            if (aMsg.length > 0) {
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },

        clearErr: function () {
            this.$el.msiErrClearAll();
        },
        // 施行情報取得処理
        showMap: function () {
            if ($.msiJqlib.isNullEx2(this.model.get('key'))) {
                msiLib2.showErr('APIが利用できません。');
            }
            var that = this;
            // 最初に地図を非表示設定する
            that.destroyMap();
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/kanri/googlemapsekoinfo/getsekoinfo',
                data: {
                    dataAppJson: JSON.stringify(this.model.toJSON()),
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.initMap(mydata.dataCol);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },
        initMap: function(dataCol) {
            if (typeof google === "undefined") {
                return;
            }
            if (dataCol.length == 0) {
                $.msiJqlib.showErr("データがありません");
                return;
            }

            // 地図表示エリアの制御
            $(".map_area_disappear").addClass("map_area_appear");
            $(".map_area_disappear").removeClass("map_area_disappear");
            var mapdiv = $("#map_area")[0];
            //　データ成型
            var latlng = []; //緯度経度の値をセット
            var marker = []; //マーカーの位置情報をセット
            var myLatLng; //地図の中心点をセット用
            var geocoder;
            var infoWindow = [];
            geocoder = new google.maps.Geocoder();
            var map = new google.maps.Map(mapdiv);//地図を作成する
            geo(aftergeo);
            function geo(callback) {
                var cRef = dataCol.length;
                for (var i = 0; i < dataCol.length; i++) {
                    (function (i) {
                        geocoder.geocode({'address': dataCol[i]['addr']},
                                function (results, status) { // 結果
                                    if (status === google.maps.GeocoderStatus.OK) { // ステータスがOKの場合
                                        latlng[i] = results[0].geometry.location;// マーカーを立てる位置をセット
                                        marker[i] = new google.maps.Marker({
                                            position: results[0].geometry.location, // マーカーを立てる位置を指定
                                            map: map // マーカーを立てる地図を指定
                                        });
                                        infoWindow[i] = new google.maps.InfoWindow({ // 吹き出しの追加
                                            content: '<div class="sample">'+dataCol[i]['name']+'<br>'+dataCol[i]['addr']+'</div>' // 吹き出しに表示する内容
                                          });
                                        markerEvent(i); // マーカーにクリックイベントを追加
                                    }
                                    if (--cRef <= 0) {
                                        callback();//全て取得できたらaftergeo実行
                                    }
                                }
                        );
                    })(i);
                }
            }

            function aftergeo() {
                var idx = _.keys(latlng)[0];
                myLatLng = latlng[idx];//最初の住所を地図の中心点に設定
                var opt = {
                    center: myLatLng, // 地図の中心を指定
                    zoom: 16 // 地図のズームを指定
                };//地図作成のオプションのうちcenterとzoomは必須
                map.setOptions(opt);//オプションをmapにセット
            };
            // マーカーにクリックイベントを追加
            function markerEvent(i) {
                marker[i].addListener('click', function() { // マーカーをクリックしたとき
                  infoWindow[i].open(map, marker[i]); // 吹き出しの表示
              });
            };

        },
        destroyMap: function(event) {
            // 地図オブジェクトを破棄し、表示済みの地図は非表示にする。
            _map = null;
            $(".map_area_appear").addClass("map_area_disappear");
            $(".map_area_appear").removeClass("map_area_appear");
            $(".mb").removeClass("map_off");
            $(".mb").addClass("map_btn");
        },

        bindings: {
            '#taisho_st_ymd': 'taisho_st_ymd',
            '#taisho_ed_ymd': 'taisho_ed_ymd',
        },
    }); // AppView

    var app = new AppView({model: new AppModel});

    var _resetData = function (myApp) {
        app.model.set(myApp);
        $(document).msiErrClearAll();
        app.model.trigger('change'); // patch
    };

    var _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
        _resetData(mydata.dataApp);
    };

    // リサイズ処理
    $(window).on('resize', function () {
        app.render();
    });

    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    // ラジオボタン・チェックボックスのボタン化
    $('.radio_set, .checkbox_set').buttonset();
    $('#order').fadeIn('fast'); // ちらつきのごまかし
});
