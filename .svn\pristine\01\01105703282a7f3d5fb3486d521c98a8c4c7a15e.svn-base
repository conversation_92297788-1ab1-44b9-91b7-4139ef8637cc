<?php
  /**
   * Datmig_DatmigAbstController
   *
   * データ移行 コントローラクラス
   *
   * @category   App
   * @package    controllers\Datmig
   * <AUTHOR> Mihara
   * @since      2018/05/xx
   * @filesource 
   */
abstract class Datmig_DatmigAbstController extends Msi_Zend_Controller_Action
{
    static protected $_page_title = 'データ移行(ツール)';

    /**
     * init ファンクション
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
    public function init()
    {
        ini_set("max_execution_time", 0); // unlimited.
        ini_set('memory_limit', '1536M');

        // view tpl path を datmig00 に設定
        $this->_helper->viewRenderer->setViewScriptPathSpec(':module/datmig00/:action.:suffix');

        // サーチパス設定
        Logic_Datmig_DatmigEnv::setSearchPath();
    }

    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2018/05/xx
     */
	public function indexAction()
    {
        $this->_forward( 'main' );
	}

    /**
     * main アクション
     *
     * <AUTHOR> Mihara
     * @since  2018/05/xx
     */
    public function mainAction()
    {
        $req = $this->getRequest();

        $params  = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        App_Smarty::pushCssFile( ['app/datmig.datmig00.css'] );
        App_Smarty::pushJsFile( ['app/datmig.datmig00.js'] );

        // 初期値
        $data = $this->_initData();

        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        $this->view->page_title = static::$_page_title;
    }

    /**
     * 初期設定
     *
     * <AUTHOR> Mihara
     * @since  2018/05/xx
     */
    protected function _initData()
    {
        // 初期値
        $data = array( 
                'dataApp' => array(
                                   's_name' => '',
                                   's_name2' => '',
                                   's_order_by_1'  => 'cre_ts_asc',
                                   's_order_by_2'  => '',
                                   's_order_by_3'  => '',
                                   's_limit_cnt'   => 100, //300,
                                   'ope_type_list' => '', // 'ope10_w',
                                   'upf_type_list' => '', // 'csv00',
                                   'init_search'   => true,
                                   ),
                'mstrData' => $this->_getMstrData(),
                       );

        return $data;
    }

    /**
     * @ignore
     * 検索条件パラメタ
     */
    protected $_params = null;

    /**
     * 検索結果表示 アクション
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @params boolean $isRtnJson   JSON 形式で返すか
     */
	public function searchAction($isRtnJson=false)
    {
        try {
            if ( ! Logic_Datmig_DatmigEnv::isExistsEnv() ) { // スキーマが存在しない
                throw new Msi_Sys_Exception_LogicException('データ移行環境が存在しません');
            }

            if ( isset($this->_params) ) {
                $params  = $this->_params;
            } else {
                $this->_params = $params = Msi_Sys_Utils::webInputs();
            }
            // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );

            @ list($data, $hash, $isMoreData) = $this->_doSearch();
        }
        catch ( Msi_Sys_Exception_LogicException $e ) { 
            $err = $e->getMessage();
            $msg = $err;
        }
        catch ( Msi_Sys_Exception_InputException $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::debug( '検索条件エラー=>' . $err );
            $msg = '検索条件エラーです';
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $msg = '内部エラーです';
        }

        // $limit  = Msi_Sys_Utils::easyGetVar($this->_params, 'limit', 'DIGIT', 20);
        $limit  = Msi_Sys_Utils::easyGetVar($this->_params, 's_limit_cnt', 'DIGIT', 20);
        $offset = Msi_Sys_Utils::easyGetVar($this->_params, 'offset', 'DIGIT', 0);

        $next_offset = -1;
        $prev_offset = -1;

        if ( isset($data) ) {
            $count = count($data);
            $this->view->list_count = $count;
            $this->view->list_data = $data;
            $next_offset = -1;
            $prev_offset = -1;
            if ( $count <= 0 ) {
                $msg = '該当するデータはありません';
            } else if ( $isMoreData ) {
                $msg = '該当するデータが他に存在します';
                $next_offset = $offset + $count; // 次ページ  org
                // $next_offset = $offset + $count_org; // 次ページ
            }
            if ( $offset > 0 ) { // 前ページ
                $prev_offset = $offset - $limit; // 重複があるので正確ではない
                if ( $prev_offset < 0 || $prev_offset == $offset ) $prev_offset = 0;
            }

            $ctlData = array( 'next_offset' => $next_offset,
                              'prev_offset' => $prev_offset );
            $ctlJson = Msi_Sys_Utils::json_encode( $ctlData );
            $this->view->myctldata_json = $ctlJson;

            // $json = Msi_Sys_Utils::json_encode( $hash );
            // $this->view->mydata_json = $json;
        }

        // JSON で返す場合
        if ( $isRtnJson ) {
            if ( isset($msg) ) {
                $rtnData = array(
                              'status' => 'NG',
                              'msg'    => $msg,
                              );
            } else {
                $rtnData = array(
                                 'status' => 'OK',
                                 'msg' => '',
                                 'data' => $data,
                                 );
            }
            Msi_Sys_Utils::outJson( $rtnData );
            return;
        }

        if ( isset($msg) ) {
            $this->view->my_msg = $msg;
        }

        if ( Msi_Sys_Utils::globalGet('_set_tpl') ) {
            // 特別の tpl を使う場合. cf. Kanri_SekolistController::searchAction()
            list($action, $controller, $module) = Msi_Sys_Utils::globalGet('_set_tpl');

            Msi_Sys_Utils::setScriptPath( $action, $controller, $module );
        } else {
            $this->_helper->viewRenderer->setScriptAction( 'result-list' );
        }

        $this->_setScriptVar();
    }

    /**
     * 検索処理
     *
     * @return array($data, $hash)
     */
	protected function _doSearch()
    {
        $params = $this->_params;
        $data = array();
        $hash = array();
        $isMoreData = false;

        // Msi_Sys_Utils::debug( 'shohin params=>' . Msi_Sys_Utils::dump($params) );

        $db = Msi_Sys_DbManager::getMyDb();

        $this->_params['limit']  = $limit  = Msi_Sys_Utils::easyGetVar($params, 's_limit_cnt', 'DIGIT', 20);
        $this->_params['offset'] = $offset = Msi_Sys_Utils::easyGetVar($params, 'offset', 'DIGIT', 0);
        // no_date_check(有効日付)チェック: 0(default(チェックする)), 1(無視), 2(販売日付だけ無視), 3(仕入日付だけ無視), ...

        $a_order_by = array();
        foreach ( array(1, 2, 3) as $seq ) {
            $order_by = Msi_Sys_Utils::easyGetVar($params, 's_order_by_'.$seq, null, 0);
            $order_by_str = $this->_getSQLOrderByKey($order_by);
            if ( $order_by_str != null ) {
                $a_order_by[] = $order_by_str;
            }
        }
        $a_order_by[] = 'name1 ASC';

        $cond = array(
                      '__etc_orderby' => $a_order_by, // array('rc_shohin_cd ASC'),
                      '__etc_limit'   => $limit +1,
                      '__etc_offset'  => $offset,
                      );

        $name1    = Msi_Sys_Utils::easyGetVar($params, 's_name1');
        $name2    = Msi_Sys_Utils::easyGetVar($params, 's_name2');
        $memo     = Msi_Sys_Utils::easyGetVar($params, 's_memo');

        if ( strlen($name1) > 0 ) {
            $cond['name1'] = array( '~', $name1 );
        }
        if ( strlen($name2) > 0 ) {
            $cond['name2'] = array( '~', $name2 );
        }
        if ( strlen($memo) > 0 ) {
            $cond['memo'] = array( '~', $memo );
        }

        $this->_searchPreFind( $cond, array() );

        // Msi_Sys_Utils::debug( 'shohin cond=>' . Msi_Sys_Utils::dump($cond) );

        // 検索本処理
        $select = $this->_searchExecFind( $db, $cond );

        // Msi_Sys_Utils::debug( 'select =>' . Msi_Sys_Utils::dump($select) );

        if ( count($select) >= ($limit+1) ) {
            $isMoreData = true;
        }

        $count = 0;
        foreach ( $select as $rec ) {
            $count++;
            if ( $count > $limit ) {
                // $isMoreData = true;
                break;
            }
            $rec['tbl_name1'] = $rec['name1'];
            $rec['tbl_name2'] = $rec['name2'];
            $rec['off_total'] = $count + $offset;
            // $rec['_uri_prc_sum'] = Msi_Sys_Utils::filterComma( $rec['uri_prc_sum'] );
            $rec['oddEven'] = $count % 2 ? 'odd' : 'even';
            $rec['hachu_no'] = 1;
            $rec['my_id'] = $rec['name1']; // seko_no'] . '-' . $rec['hachu_no'];
            $data[] = $rec;
            // $hash[$myid] = $rec;
        }

        // 調整
        $this->_adjData($data); // , $hash);

        // $hash 調整
        foreach ( $data as &$rec ) {
            $myid = $rec['rc_shohin_cd'];
            $hash[$myid] = $rec;
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );

        return array( $data, $hash, $isMoreData );
    }

    /**
     * 検索条件調整
     *
     * @param  array  $cond
     * @param  array  $addParam
     * @return void
     */
	protected function _searchPreFind(&$cond, $addParam)
    {
    }

    /**
     * 検索本処理
     *
     * @param  DB     $db
     * @param  array  $cond
     * @param  array  $addParam
     * @return array  検索結果
     */
	protected function _searchExecFind($db, $cond, $addParam=array())
    {
        $select = Logic_Datmig_DmWk99WkTbl::find( $db, $cond );
        
        return $select;
    }

    /**
     * 調整
     *
     * <AUTHOR> Mihara
     * @since 2015/05/xx
     * @param  array  $data
     * @param  array  $hash
     * @return void
     */
	protected function _adjData(&$data)
    {
    }

    /**
     * テンプレート変数設定
     * サブクラスで実装すること
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @return void
     */
    protected function _setScriptVar()
    {
        // $this->view->dlg_title = '(タイトル)';

        foreach ( Msi_Sys_Utils::strArrayify_qw('s_number s_code_1 s_code_2 s_code_3 s_tel ' .
                                                's_staff_1 s_apply s_name1 s_name2 s_name3 s_memo s_bumon ' .
                                                's_staff_2 s_ontime' ) as $k ) {
            @ $this->view->$k = $this->_params[ $k ];
        }
    }

    /**
     * 検索前調整
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param      array      $cond  条件
     */
    protected function  _pre_search_hook(&$cond)
    {
    }

    /**
     * ファイル upload
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     */
	public function uploadAction()
    {
        try {
            $params = Msi_Sys_Utils::webInputs();

            Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

            $upfType = $params['upf_type'];
            $upfTypeStr = $params['upf_type_str'];
            $opeMethod = sprintf('_upload_method_%s', $upfType); // ex. _apply_method_ope00

            if ( !method_exists($this, $opeMethod) ) {
                $msg = sprintf("[処理]%s(%s) が存在しません", $upfTypeStr, $upfType);
                throw new Exception($msg);
            }

            $outMsg = $this->$opeMethod($params);

            $outData = array(
                          'status' => 'OK',
                          'msg' => $outMsg,
                          );
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $outData = array(
                             'status' => 'NG',
                             'msg' => $err, // $userMsg,
                             );
        }

        Msi_Sys_Utils::outJson( $outData );
    }

    /**
     * CSV出力
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     */
	public function csvoutAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( 'Abst_UtilController::csvoutAction params==>' . Msi_Sys_Utils::dump($params) );

        $isBdlStyle = isset($params['dl_type']) && $params['dl_type'] == 'bdl';
        $tbl_names = $params['tbl_names'];

        $db = Msi_Sys_DbManager::getMyDb();
        Logic_Datmig_DatmigEnv::csvOutTblsInAction($tbl_names, $isBdlStyle, $db);
    }

    /**
     * マスターデータ類を返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getMstrData()
    {
        $mstrData = array( 
                          'opeTypeList' => $this->_getOpeTypeList(),
                          'upfTypeList' => $this->_getUpfTypeList(),
                          'sortKey'     => $this->_getSortKey(),
                          'limitCnt'    => $this->_getLimitCnt(),
                           );
        return $mstrData;
    }

    static protected $_sortKey =
        array( 
              array("id" => 'cre_ts_asc',  "text" => '昇順:作成日時',       "orderBy" => '_cre_ts ASC'),   
              array("id" => 'name1_asc',   "text" => '昇順:テーブル名',     "orderBy" => 'name1 ASC'),   
              array("id" => 'name2_asc',   "text" => '昇順:論理名',         "orderBy" => 'name2 ASC'),   
              array("id" => 'memo_asc',    "text" => '昇順:備考',           "orderBy" => 'memo ASC'),
              array("id" => 'cre_ts_desc', "text" => '降順:作成日時',       "orderBy" => '_cre_ts DESC'),   
              array("id" => 'name1_desc',  "text" => '降順:テーブル名',     "orderBy" => 'name1 DESC'),   
              array("id" => 'name2_desc',  "text" => '降順:論理名',         "orderBy" => 'name2 DESC'),   
              array("id" => 'memo_desc',   "text" => '降順:備考',           "orderBy" => 'memo DESC'),   
               );

    /**
     * ソートキーを返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getSortKey()
    {
        $sortKey = Msi_Sys_Utils::remapArray(static::$_sortKey, 'id text');
        return $sortKey;
    }

    /**
     * ソートキーから SQL order by key を返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getSQLOrderByKey($key)
    {
        $order_by_str = null;
        foreach ( static::$_sortKey as $arr ) {
            if ( $arr['id'] === $key ) {
                $order_by_str = $arr['orderBy'];
                break;
            }
        }
        return $order_by_str;
    }

    /**
     * 返却行数選択肢を返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getLimitCnt()
    {
        $limitCnt = array( 
                          array("id" => 5,   "text" => '5件'),
                          array("id" => 10,  "text" => '10件'),
                          array("id" => 20,  "text" => '20件'),
                          array("id" => 50,  "text" => '50件'),
                          array("id" => 100,  "text" => '100件'),
                          array("id" => 200,  "text" => '200件'),
                          array("id" => 300,  "text" => '300件'),
                           );
        return $limitCnt;
    }

    /**
     * 処理タイプ選択肢を返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getOpeTypeList()
    {
        $opeTypeList = array( 
                             array("id" => 'ope00',    "text" => '開始(環境作成)'),
                             array("id" => 'ope10_w',  "text" => 'ファイルチェック'),
                             array("id" => 'ope20_w',  "text" => 'データ登録'),
                             array("id" => 'ope40_w',  "text" => '登録データ削除'),
                             array("id" => 'ope90_w',  "text" => '個別ファイル削除'),
                             array("id" => 'ope99',    "text" => '全終了(環境削除)'),
                           );
        return $opeTypeList;
    }

    /**
     * 取込ファイル種類選択肢を返す
     *
     * <AUTHOR> Mihara
     * @since 2017/01/xx
     * @return array
     */
    protected function _getUpfTypeList()
    {
        $opeTypeList = array( 
                             array("id" => 'csv00',  "text" => '自動判別CSVファイル'),
                           );
        return $opeTypeList;
    }

    /**
     * 処理実行 アクション
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     */
	public function applyAction()
    {
        try {
            $params = Msi_Sys_Utils::webInputs();

            Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

            $opeType = preg_replace('/(_[a-z])+$/', '', $params['ope_type']);
            $opeTypeStr = $params['ope_type_str'];
            $opeMethod = sprintf('_apply_method_%s', $opeType); // ex. _apply_method_ope00

            if ( !method_exists($this, $opeMethod) ) {
                $msg = sprintf("[処理]%s(%s) が存在しません", $opeTypeStr, $opeType);
                throw new Exception($msg);
            }

            $outMsg = $this->$opeMethod($params);

            // $outMsg = sprintf("表:%s(%s) がファイル(%s)から登録されました", $db->getTableComment($tblname), $tblname, $filename);
            $outData = array(
                          'status' => 'OK',
                          'msg' => $outMsg,
                          );
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $outData = array(
                             'status' => 'NG',
                             'msg' => $err, // $userMsg,
                             );
        }

        Msi_Sys_Utils::outJson( $outData );
    }

    /**
     * apply 実処理 ope00 (処理開始)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope00($params)
    {
        $rtn = Logic_Datmig_DatmigEnv::createEnv();

        $outMsg = '???';
        if ( $rtn === 0 ) {
            $outMsg = '既存環境が存在するので何もしません';
        } if ( $rtn === 1 ) {
            $outMsg = '新規環境を作成しました';
        }

        return $outMsg;
    }    

    /**
     * apply 実処理 ope99 (処理終了)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope99($params)
    {
        $rtn = Logic_Datmig_DatmigEnv::dropEnv();

        $outMsg = '???';
        if ( $rtn === 0 ) {
            $outMsg = '既存環境は存在しません';
        } if ( $rtn === 1 ) {
            $outMsg = '環境を削除しました';
        }

        return $outMsg;
    }

    /**
     * apply 実処理 ope90 (個別ファイル削除)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope90($params)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $tbl_names = $params['tbl_names'];

        $norm_tbl_names = $this->_norm_tbl_names($tbl_names, 'ope90');
        // Msi_Sys_Utils::debug( sprintf("org:%s => new:%s",Msi_Sys_Utils::dump($tbl_names),Msi_Sys_Utils::dump($norm_tbl_names)) );

        $rtn = Logic_Datmig_DatmigEnv::dropWkTblsWithCommit($norm_tbl_names, $db);

        $outMsg = 'ファイル削除しました';

        return $outMsg;
    }

    // ファイルチェック処理のモジュール名
    static protected $_dataChkLogicMapper = array(); // ex. array( '売上伝票' => 'Logic_Datmig_Lifesystem_UriDen', ...

    /**
     * ファイルチェック処理のモジュール名を返す
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   string $t
     * @return  mixed (string: message)
     */
	protected function _getDataChkModName($db, $tbl_nm)
    {
        $wk99Rec = Logic_Datmig_DmWk99Ctrl::findOne( $db, array('tbl_nm'=>$tbl_nm) );
        if ( $wk99Rec === null ) {
            throw new Exception("(29b377cc)テーブル? {$tbl_nm}");
        }
        $tbl_type = $wk99Rec['tbl_type'];
        if ( ! array_key_exists($tbl_type, static::$_dataChkLogicMapper) ) {
            throw new Exception("{$tbl_type} に対応する処理モジュール設定が存在しません");
        }
        $modName = static::$_dataChkLogicMapper[ $tbl_type ];
        return $modName;
    }

    // 処理タイプ処理順
    static protected $_tblTypeOrder = array();

    /**
     * 複数テーブルを処理順にして返す
     * 登録などで前後性の前提が必要な場合に備えて、処理順にして返す
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param array $tbl_names
     * @param string $ope_type
     * @return  array
     */
	protected function _norm_tbl_names($tbl_names, $ope_type)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $tblTypeOrder = array();
        // 明示的に設定されていればそれを使う
        if ( isset(static::$_tblTypeOrder) && array_key_exists($ope_type, static::$_tblTypeOrder) ) {
            $tblTypeOrder = Msi_Sys_Utils::strArrayify_qw( static::$_tblTypeOrder[ $ope_type ] );
        }
        else { // そうでなければ $_dataChkLogicMapper のキーから構成する
            $aFiles = array_keys(static::$_dataChkLogicMapper);
            $tblTypeOrder = $aFiles;
        }

        // wk99_ctrl で tbl_type を調べて
        $hTblNamesInOpeType = array();
        foreach ( $tbl_names as $tbl_nm ) {
            $wk99CtrlRec = Logic_Datmig_DmWk99Ctrl::findOne( $db, array('tbl_nm'=>$tbl_nm) );
            if ( $wk99CtrlRec === null ) {
                throw new Exception("(5c40a817)Datmig_DatmigAbstController::_norm_tbl_names no wk99CtrlRec($tbl_nm)");
            }
            $my_tbl_type = $wk99CtrlRec['tbl_type'];
            if ( ! array_key_exists($my_tbl_type, $hTblNamesInOpeType) ) {
                $hTblNamesInOpeType[ $my_tbl_type ] = array();
            }
            $hTblNamesInOpeType[ $my_tbl_type ][] = $tbl_nm;
        }

        // $tblTypeOrder 順で格納する
        $new_tbl_names = array();
        foreach ( $tblTypeOrder as $_tbl_type00 ) {
            if ( array_key_exists($_tbl_type00, $hTblNamesInOpeType) ) {
                $new_tbl_names = array_merge($new_tbl_names, $hTblNamesInOpeType[ $_tbl_type00 ]);
                unset( $hTblNamesInOpeType[ $_tbl_type00 ] );
            }
        }
        // $tblTypeOrder に登場していないのがあれば  for safety
        foreach ( $hTblNamesInOpeType as $_k => $_arr ) {
            $new_tbl_names = array_merge($new_tbl_names, $_arr);
        }

        return $new_tbl_names;
    }
    
    // アップロード許容CSVファイル名
    static protected $_regexFile = null;

    /**
     * アップロードを許可されるCSVファイル名を正規表現で返す
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @return  string
     */
	protected function _get_regex_up_file()
    {
        // 明示的に設定されていればそれを使う
        if ( isset(static::$_regexFile) && strlen(static::$_regexFile) > 0 ) {
            return static::$_regexFile;
        }

        // そうでなければ $_dataChkLogicMapper のキーから構成する
        $aFiles = array_keys(static::$_dataChkLogicMapper);
        $regexFile = '/^(.*)(' . 
            implode('|', array_map(function($v){return preg_quote($v);}, $aFiles)) .
            ')(.*)\\.csv$/ui';
        // ex. '/^(.*)(入金伝票|売上伝票|売上明細)(.*)\\.csv$/ui'

        return $regexFile;
    }

    /**
     * upload 実処理 csv10 (自動判別CSVファイル)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _upload_method_csv00($params)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        $filename = Msi_Sys_Utils::getUploadName( 'file' );
        // Msi_Sys_Utils::debug( ' **** filename==>' . $filename );

        $regexFile = $this->_get_regex_up_file();
        if ( !preg_match($regexFile, $filename, $match) ) {
            throw new Exception( "ファイル名($filename)が不正です(csvファイル?)" );
        }

        $tblname = $match[2];
        // Msi_Sys_Utils::debug( ' **** tblname==>' . $tblname );

        $rtn = Msi_Sys_Utils::getUploadFile( 'file' );
        Msi_Sys_Utils::debug( ' **** getUploadFile rtn ==>' . Msi_Sys_Utils::dump($rtn) );
        if ( is_string($rtn) ) {
            throw new Exception($rtn);
        }
        list($tempfile, $fileInfo, $mime) = $rtn;

        // Msi_Sys_Utils::debug( ' **** writeblobAction tempfile==>' . $tempfile );

        $funcName = $tblname; // $params['upf_type_str'];
        $infile = $tempfile;
        $db = Msi_Sys_DbManager::getMyDb(); // schema: wk99
        $options = array( 'inFileName' => $filename,
                          'tblNmBase' => $funcName );
        // , 'isNoCommit' => true, 'isNoCheck' => $isNoCheck);
        $rtn = Logic_Datmig_CsvToTblStd::execute( $funcName, $infile, $db, $options );

        if ( $rtn['return_code'] == 0 ) {
            $outMsg = '正常終了しました';
        } else {
            $outMsg = 'エラーが発生しました';
        }
        $outMsg .= "\n" . Msi_Sys_Utils::dump($rtn);

        return $outMsg;
    }    

    /**
     * apply 実処理 ope10 (ファイルチェック)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope10($params)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $tbl_names = $params['tbl_names'];

        $norm_tbl_names = $this->_norm_tbl_names($tbl_names, 'ope10');
        // Msi_Sys_Utils::debug( sprintf("org:%s => new:%s",Msi_Sys_Utils::dump($tbl_names),Msi_Sys_Utils::dump($norm_tbl_names)) );

        foreach ( $norm_tbl_names as $tbl ) {
            $modName = $this->_getDataChkModName($db, $tbl);
            $method = 'chkData';
            if ( !method_exists($modName, $method) ) {
                throw new Exception(sprintf("%s::%s が存在しません", $modName, $method));
            }
            $opt = $params;
            $args = array( $db, $tbl, $opt );
            list($bool, $rtnData) = call_user_func_array(array($modName, $method), $args);

            // Msi_Sys_Utils::debug( '** '.$modName.'chkData()=>' . Msi_Sys_Utils::dump([$bool, $rtnData]) );
        }

        $db->commit();

        if ( $bool ) {
            $outMsg = '正常終了しました';
        } else {
            $outMsg = 'エラー行が存在します';
        }
        $outMsg .= "\n" . Msi_Sys_Utils::dump($rtnData);
        $outMsg .= "\n" . "詳細は生成されたファイルを参照ください";

        return $outMsg;
    }

    /**
     * apply 実処理 ope20 (データ登録)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope20($params)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $tbl_names = $params['tbl_names'];

        $norm_tbl_names = $this->_norm_tbl_names($tbl_names, 'ope20');
        // Msi_Sys_Utils::debug( sprintf("org:%s => new:%s",Msi_Sys_Utils::dump($tbl_names),Msi_Sys_Utils::dump($norm_tbl_names)) );

        foreach ( $norm_tbl_names as $tbl ) {
            $modName = $this->_getDataChkModName($db, $tbl);
            $method = 'insData';
            if ( !method_exists($modName, $method) ) {
                throw new Exception(sprintf("%s::%s が存在しません", $modName, $method));
            }
            $opt = $params;
            $args = array( $db, $tbl, $opt );
            list($bool, $rtnData) = call_user_func_array(array($modName, $method), $args);

            Msi_Sys_Utils::debug( '** '.$modName.'insData()=>' . Msi_Sys_Utils::dump([$bool, $rtnData]) );

            // 特別追加処理   非課税商品用パッチのため追加
            $modNamePatch = $modName . 'Patch';
            if ( @ !method_exists($modNamePatch, $method) ) {
                continue;
            }
            list($bool, $rtnData) = call_user_func_array(array($modNamePatch, $method), $args);

            Msi_Sys_Utils::debug( '** '.$modNamePatch.'insData()=>' . Msi_Sys_Utils::dump([$bool, $rtnData]) );
        }

        $db->commit();

        if ( $bool ) {
            $outMsg = '正常終了しました';
        } else {
            $outMsg = 'エラーが存在します';
        }
        $outMsg .= "\n" . Msi_Sys_Utils::dump($rtnData);

        return $outMsg;
    }

    /**
     * apply 実処理 ope40 (登録データ削除)
     *
     * <AUTHOR> Mihara
     * @since 2018/05/xx
     * @param   array   $params
     * @return  mixed (string: message)
     */
	protected function _apply_method_ope40($params)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $tbl_names = $params['tbl_names'];

        $norm_tbl_names = $this->_norm_tbl_names($tbl_names, 'ope40');
        // Msi_Sys_Utils::debug( sprintf("org:%s => new:%s",Msi_Sys_Utils::dump($tbl_names),Msi_Sys_Utils::dump($norm_tbl_names)) );

        foreach ( $norm_tbl_names as $tbl ) {
            $modName = $this->_getDataChkModName($db, $tbl);
            $method = 'delData';
            if ( !method_exists($modName, $method) ) {
                throw new Exception(sprintf("%s::%s が存在しません", $modName, $method));
            }
            $opt = $params;
            $args = array( $db, $tbl, $opt );
            list($bool, $rtnData) = call_user_func_array(array($modName, $method), $args);

            Msi_Sys_Utils::debug( '** '.$modName.'delData()=>' . Msi_Sys_Utils::dump([$bool, $rtnData]) );
        }

        $db->commit();

        if ( is_string($bool) ) {
            $outMsg = $bool;
        } else {
            if ( $bool ) {
                $outMsg = '正常終了しました';
            } else {
                $outMsg = 'エラーが存在します';
            }
            $outMsg .= "\n" . Msi_Sys_Utils::dump($rtnData);
        }

        return $outMsg;
    }

}
