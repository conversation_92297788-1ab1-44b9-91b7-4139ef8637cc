/* 
 * 見積書と請求書の共通処理
 */
var applms = applms || {}; // 見積書と請求書共通処理
var appmtsk = appmtsk || {}; // 見積書または請求書個別処理
$(function() {
    "use strict";

    // 見積書と請求書でデフォルト
    applms = {
        o_price: '', // 金額文言
        printURI: '', // 印刷URI
        viewKbn: '', // 画面区分 1:見積書 2：請求書
        setSideMenu: function() {
        }, // サイドメニュー設定処理
        initializeView: function() {
        }, // viewの初期処理
        setNyukin: function() {
        }, // 入金行追加処理(請求書)
        doSave: function() {
        }, // 保存処理
        doShonin: function() {
        }, // 承認処理(請求書)
        doCancel: function() {
        }, // 再表示処理(見積書)と承認取消処理(請求書)
        doFixInvoice: function() {
        }, // 請求書確定処理()
        doInvoiceCancel: function() {
        }, // 請求書確定取消処理
        mitsukakutei: function() {
        }, // 見積確定処理(見積書)
        mitsutorikesi: function() {
        }, // 見積取消処理(見積書)
        // 押下不可のボタンスタイルを設定
        setDisabledButtonStyle: function($button) {
            $button.attr("disabled", "disabled").addClass("disable_btn");
        },
        // 押下不可のボタンスタイルをクリア
        clearDisabledButtonStyle: function($button) {
            $button.removeAttr("disabled").removeClass("disable_btn");
        }
    };
    // 見積書または請求書個別処理を継承する
    $.extend(applms, appmtsk);

    // 現在選択したタブのインデックス
    applms.tabindex = 0;
    // 現在選択した行のインデックス
    applms.rowindex = -1; // 選択されていないとき

    /** タブ区分：1=>葬送儀礼 */
    var TAB_KBN_S = '1';
    /** タブ区分：2=>返礼品 */
    var TAB_KBN_H = '2';
    /** タブ区分：3=>飲食費 */
    var TAB_KBN_I = '3';
    /** タブ区分：4=>立替 */
    var TAB_KBN_T = '4';
    /** タブ区分：5=>その他タブ*/
    var TAB_KBN_O = '5';

    /** タブサブ区分：1 */
    var TAB_SUB_KBN_1 = '1';
    /** タブ区分：2 */
    var TAB_SUB_KBN_2 = '2';
    /** タブ区分：3 */
    var TAB_SUB_KBN_3 = '3';

    /** 見積書または請求書の行区分：1=>ヘッダー行 */
    var ROW_KBN_H = '1';
    /** 見積書または請求書の行区分：2=>明細のタイトル行 */
    var ROW_KBN_T = '2';
    /** 見積書または請求書の行区分：3=>明細行 */
    var ROW_KBN_D = '3';
    /** 見積書または請求書の行区分：4=>消費税等行 */
    var ROW_KBN_S = '4';
    /** 見積書または請求書の行区分：9=>合計行 */
    var ROW_KBN_G = '9';


    /** レコード区分：1=>タイトル行 */
    var REC_KBN_T = '1';
    /** レコード区分：2=>明細行 */
    var REC_KBN_M = '2';
    /** レコード区分：3=>小計行 */
    var REC_KBN_S = '3';
    /** レコード区分：4=>奉仕料行 */
    var REC_KBN_H = '4';
    /** レコード区分：5=>互助会充当行 */
    var REC_KBN_J = '5';
    /** レコード区分：9=>合計行 */
    var REC_KBN_G = '9';

    /** 選択行追加可能区分 0=>追加不可 */
    var ADD_KBN_NG = '0';
    /** 選択行追加可能区分 1=>追加可 */
    var ADD_KBN_OK = '1';

    /** 受注伝票の追加区分 1=>見積入力データ */
    var ADD_KBN_MITSU = '1';

    /** 行選択可能区分 1=>選択可能 */
    var CHECK_KBN_OK = '1';

    /** 値引き区分 0=>通常商品 */
    var NEBIKI_KBN_IPPAN = '0';
    /** 値引き区分 1=>互助会 */
    var NEBIKI_KBN_GOJOKAI = '1';
    /** 値引き区分 1=>互助会値引き商品 */
    var NEBIKI_KBN_NEBIKI = '2';

    /** disabledプロパティ */
    var ATTR_NO_INPUT = 'disabled';

    /** 米印 */
    var KOME = '※';
    /** × */
    var KAKE = '×';
    /** パーセント */
    var PERCENT = '％';
    /** 葬送儀礼 */
    var SOSOGIREI = '葬送儀礼費用';
    /** 返礼品費用 */
    var HENREI = '返礼品費用';
    /** 飲食費用 */
    var INSHOKU = '飲食費用';
    /** 立替費用 */
    var TATEKAE = '立替費用';
    /** その他費用' */
    var SONOTA = 'その他費用';
    /** 【小　計】 */
    var SHOKEI = '【小　計】';
    /** 奉仕料 */
    var HOSHIRYO = '奉仕料';

    var MSG_WARN_1 = '保存されていないデータが存在します。保存後、もう一度行ってください。';

    // リサイズ処理
    $(window).resize(function() {
        // 高さ変更処理
        _responsiveResize();
        // ヘッダーのスクロール表示非表示処理
        _responsiveScroll();
    });
    // グリッド高さ変更処理
    var _responsiveResize = function() {
        var colLen = applms.appView.getMeisaiColLength();
        if (colLen === 0) {
            return;
        }
        var $target, maxheight, height;
        var dh = $("#detail").height();
        var heightOnerow = 32;
        if (applms.tabindex === 0) {
            $target = $("#detail #estimate.mitsu");
            // 非表示以外のtbodyの数
            var disLen = $target.find(".display-none").length;
            maxheight = dh - 240;
            colLen = colLen - disLen;
            height = (colLen) * heightOnerow;
        } else {
            $target = $("#detail #estimate.meisai");
            maxheight = (dh - 300);
            height = (colLen - 1) * heightOnerow;
        }
        applms.$meisai_scroll = $target;
        if (height > maxheight) {
            $target.css("height", maxheight);
            applms.meisai_scroll = true;
        } else {
            $target.css("height", "auto");
            applms.meisai_scroll = false;
        }
    };
    // ヘッダーのスクロール表示非表示処理
    var _responsiveScroll = function() {
        // 見積書または請求書のタブ以外の場合
        if (applms.tabindex === 0) {
            return;
        }
        var $targets = $("#detail #estimate.meisai");
        var $target = $targets.eq(applms.tabindex - 1);
        var $tabContents = $target.closest(".tab-contents");
        // 元の高さと表示されている高さを比較
        if (!applms.meisai_scroll) {
            $tabContents.find(".estimate-head").css("overflow-y", "auto");
            $tabContents.find(".estimate-foot").css("overflow-y", "auto");
        } else {
            $tabContents.find(".estimate-head").css("overflow-y", "scroll");
            $tabContents.find(".estimate-foot").css("overflow-y", "scroll");
        }
    };

    // 見積書または請求書設定処理 
    var _setSummary = function() {
        applms.summaryCol.reset();
        var m;
        var prc1, prc2, prc3, prc4, prc5 = 0, prc6;
        if (applms.meisaiCol1.length === 0) {
            return;
        }
        // エレメント削除する前に明細のon|off情報を退避する
        var $dspNoneIds = [];
        $('#estimate .bloc .head').each(function(index) {
            if ($(this).hasClass("display-none")) {
                $dspNoneIds.push(index);
            }
        });

        // すべてのエレメント削除
        $("#estimate .bloc,#estimate .all").remove();

        // 葬送儀礼費用
        m = _getSummaryOne(applms.meisaiCol1, {record_kbn: REC_KBN_G}); // 合計行
        _setSummaryModel(m, SOSOGIREI, '小計 ①', ROW_KBN_H);
        prc1 = m.get('price'); // 小計 ①'

        // 【Ａ】葬送儀礼費用基本プラン設定処理 START
        // 葬送儀礼用の互助会会員番号
        var kaiinNo = applms.appModel.get('kaiin_no');
		var gojokai_kbn = applms.appModel.get('gojokai_kbn');
        if (!$.msiJqlib.isNullEx2(kaiinNo)) {
            // 【Ａ】葬送儀礼費用基本プラン
            m = new SummaryModel();
			_setSummaryModel(m, applms.appModel.get('sosogorei_plan_a_nm'), null, ROW_KBN_T);

			if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
				// 互助会品目合計
				var planGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_GOJOKAI, chu_bunrui_cd: '0020'}), 'juchu_prc'); // 互助会商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: planGokei});
				_setSummaryModel(m, applms.appModel.get('kain_nm') + '品目合計', null, ROW_KBN_D);
				// 互助会会員値引き
				var nebikiGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_NEBIKI, chu_bunrui_cd: '0020'}), 'juchu_prc'); // 値引き商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: nebikiGokei});
				_setSummaryModel(m, applms.appModel.get('kain_nm') + '会員特典', null, ROW_KBN_D);
			} else {
				// ナウエルメンバーズ
				// 互助会品目合計
				var planGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_GOJOKAI}), 'juchu_prc'); // 互助会商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: planGokei});
				_setSummaryModel(m, '品目合計', null, ROW_KBN_D);
				// 互助会会員値引き
				var nebikiGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_NEBIKI, chu_bunrui_cd: '0057'}), 'juchu_prc'); // 値引き商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: nebikiGokei});
				_setSummaryModel(m, '会員特典', null, ROW_KBN_D);
			}	

            // 小計
            var shokei = planGokei + nebikiGokei;
            m = new SummaryModel({price: shokei});
            _setSummaryModel(m, '小計', null, ROW_KBN_D);
            // 互助会外品目合計
			if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
				var planGokei2 = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_IPPAN, chu_bunrui_cd: '0020'}), 'juchu_prc'); // 通常商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: planGokei2});
				_setSummaryModel(m, applms.appModel.get('kain_nm') + '外品目合計', null, ROW_KBN_D);
			} 	
        } else {
            // 【Ａ】葬送儀礼費用基本プラン
			if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
				m = _getSummaryOne(applms.meisaiCol1, {record_kbn: REC_KBN_S, tab_sub_kbn: TAB_SUB_KBN_1}); // 小計行かつ【Ａ】葬送儀礼費用基本
				_setSummaryModel(m, applms.appModel.get('sosogorei_plan_a_nm'), null, ROW_KBN_T);
			} else {
				// ナウエルメンバーズ
                m = new SummaryModel();
                _setSummaryModel(m, applms.appModel.get('sosogorei_plan_a_nm'), null, ROW_KBN_T);
				// 互助会品目合計
				var planGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_GOJOKAI}), 'juchu_prc'); // 互助会商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: planGokei});
				_setSummaryModel(m, '品目合計', null, ROW_KBN_D);
				// 互助会会員値引き
				var nebikiGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_NEBIKI, chu_bunrui_cd: '0057'}), 'juchu_prc'); // 値引き商品かつ【Ａ】葬送儀礼費用基本
				m = new SummaryModel({price: nebikiGokei});
				_setSummaryModel(m, '会員特典', null, ROW_KBN_D);
                var shokei = planGokei + nebikiGokei;
                m = new SummaryModel({price: shokei});
                _setSummaryModel(m, '小計', null, ROW_KBN_D);
                     //var planGokei = _calcSum(applms.meisaiCol1.where({nebiki_kbn: NEBIKI_KBN_GOJOKAI, chu_bunrui_cd: '0020'}), 'juchu_prc'); // 互助会商品かつ【Ａ】葬送儀礼費用基本
				//m = new SummaryModel({price: planGokei});
				//_setSummaryModel(m, applms.appModel.get('sosogorei_plan_a_nm'), null, ROW_KBN_T);
			}	
        }
        // 【Ａ】葬送儀礼費用基本プラン設定処理 END

        // 【Ｂ】葬送儀礼費用プラン外選択品目
		if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
			m = _getSummaryOne(applms.meisaiCol1, {record_kbn: REC_KBN_S, tab_sub_kbn: TAB_SUB_KBN_2}); // 小計行かつ【Ｂ】葬送儀礼費用プラン外選択品目
			_setSummaryModel(m, applms.appModel.get('sosogorei_plan_b_nm'), null, ROW_KBN_T);
		} else {
			// ナウエルメンバーズ
			m = _getSummaryOne(applms.meisaiCol1, {record_kbn: REC_KBN_S, tab_sub_kbn: TAB_SUB_KBN_2}); // 小計行かつ【Ｂ】葬送儀礼費用プラン外選択品目
			_setSummaryModel(m, applms.appModel.get('sosogorei_plan_e_nm'), null, ROW_KBN_T);
		}	
        // 返礼品モデル
        var mh = _getSummaryOne(applms.meisaiCol2, {record_kbn: REC_KBN_G}); // 合計行
        // 飲食費モデル
        var mi = _getSummaryOne(applms.meisaiCol3, {record_kbn: REC_KBN_G}); // 合計行

        // おもてなし費用
        var omtns = mh.get('price') + mi.get('price');
        m = new SummaryModel({price: omtns});
        _setSummaryModel(m, 'おもてなし費用', '小計 ②', ROW_KBN_H);
        prc2 = m.get('price'); // 小計 ②'

        // 【Ｃ】返礼品
        _setSummaryModel(mh, '【Ｃ】返礼品', null, ROW_KBN_D);

        // 【Ｄ】飲食費
        _setSummaryModel(mi, '【Ｄ】飲食費', null, ROW_KBN_D);

        // その他費用
        m = _getSummaryOne(applms.meisaiCol5, {record_kbn: REC_KBN_G}); // 合計行
        _setSummaryModel(m, SONOTA, '小計 ③', ROW_KBN_H);
        prc3 = m.get('price'); // 小計 ③'

        // 【Ｅ】別途費用
        var betto = _calcSum(applms.meisaiCol5.where({chu_bunrui_cd: '0090'}), 'juchu_prc'); // 【Ｅ】別途費用
        m = new SummaryModel({price: betto});
        _setSummaryModel(m, '【Ｅ】別途費用', null, ROW_KBN_D);

        // 【Ｆ】値引き
        var nebiki = _calcSum(applms.meisaiCol5.where({chu_bunrui_cd: '0120'}), 'juchu_prc'); // 【Ｆ】値引き
        if (nebiki !== 0) {
            m = new SummaryModel({price: nebiki});
            _setSummaryModel(m, '【Ｆ】値引き', null, ROW_KBN_D);
        }

        // 消費税等
        m = new SummaryModel();
        _setSummaryModel(m, '消費税等', null, ROW_KBN_S);

        // 合計 ④ ＝ 小計 ① ＋ 小計 ② ＋ 小計 ③
        var prc4 = prc1 + prc2 + prc3;
        m = new SummaryModel({price: prc4});
        _setSummaryModel(m, '合計 ④ ＝ 小計 ① ＋ 小計 ② ＋ 小計 ③', null, ROW_KBN_D);

        var shohizeiObj = applms.appModel.get('shohizei');

        // 合計 ④ の消費税 ⑤ 
//        var prc5 = Math.floor(prc4 * applms.appModel.get('shohi_zei_rtu') / 100);
        var prc5 = shohizeiObj.zei_prc * 1;
        m = new SummaryModel({price: prc5});
        _setSummaryModel(m, '合計 ④ の消費税 ⑤', null, ROW_KBN_D);

        var lbl_gokeigaku = '合計金額 = 合計 ④ ＋ ⑤';
        var arrNum = ['⑥', '⑦', '⑧'];
        var numShift;
        // 立替金 ⑥
        m = _getSummaryOne(applms.meisaiCol4, {record_kbn: REC_KBN_G}); // 合計行
        prc6 = m.get('price'); // 小計 ⑥'
        if (prc6 !== 0) {
            numShift = arrNum.shift();
            _setSummaryModel(m, '立替金 ' + numShift, null, ROW_KBN_D);
            lbl_gokeigaku += ' ＋ ' + numShift;
        }


        // ⑦ 預り金（壇払い）
        var prc7 = 0;
        if (!$.msiJqlib.isNullEx2(shohizeiObj.azukari_dan)) {
            prc7 = shohizeiObj.azukari_dan * 1;
        }
        if (prc7 !== 0) {
            numShift = arrNum.shift();
            m = new SummaryModel({price: prc7});
            _setSummaryModel(m, '互助会掛金（壇払い充当）' + numShift, null, ROW_KBN_D);
            lbl_gokeigaku += ' ＋ ' + numShift;
        }
        // ⑧ 預り金（返礼品）
        var prc8 = 0;
        if (!$.msiJqlib.isNullEx2(shohizeiObj.azukari_hen)) {
            prc8 = shohizeiObj.azukari_hen * 1;
        }
        if (prc8 !== 0) {
            numShift = arrNum.shift();
            m = new SummaryModel({price: prc8});
			if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
				_setSummaryModel(m, '預り金（返礼品）' + numShift, null, ROW_KBN_D);
			} else {
				// ナウエルメンバーズ
				_setSummaryModel(m, '互助会掛金（葬儀費用充当）' + numShift, null, ROW_KBN_D);
			}	
            lbl_gokeigaku += ' ＋ ' + numShift;
        }

        // 合計金額 = 合計 ④ ＋ ⑤ ＋ ⑥
        applms.prcSum = prc4 + prc5 + prc6 + prc7 + prc8;
        m = new SummaryModel({price: applms.prcSum});
        _setSummaryModel(m, lbl_gokeigaku, null, ROW_KBN_D);

        // 早期利用費
        _setShohiEtc('早期利用費', shohizeiObj.early_use);

        // 早期利用費の消費税
        _setShohiEtc('早期利用費の消費税', shohizeiObj.early_use_zei);

        // 互助会払込金額
        _setShohiEtc(applms.appModel.get('kain_nm') + '払込金額', shohizeiObj.gojo_harai);

        // 前納割引
        _setShohiEtc('前納割引', shohizeiObj.zen_wari);
        
        // 掛金消費税差額
        var zei_nm = '';
        if (applms.appModel.get('zei_flg')) {
            zei_nm = '消費税';
        } else {
            zei_nm = '掛金消費税差額';
        }
        _setShohiEtc(zei_nm, shohizeiObj.kake_zei_sagaku);

        // 請求書の入金行設定処理
        applms.setNyukin(new SummaryModel());

        // 御見積りまたは御請求金額
        m = new SummaryModel({price: applms.prcSum});
        _setSummaryModel(m, applms.o_price, '合計', ROW_KBN_G);

        // 明細のon|offを設定する
        var $target = $('#estimate .bloc .head');
        _.each($dspNoneIds, function(val) {
            $target.eq(val).trigger("click");
        });
    };
    /**
     * 見積書または請求書モデル設定処理
     * @param {SummaryModel} m 見積書または請求書モデル
     * @param {string} title タイトル
     * @param {string} sub サブタイトル
     * @param {string} rowKbn 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4=>消費税等行 9:合計行
     * @param {SummaryCollection} addCol 存在すれば、ここにモデルを追加する
     */
    var _setSummaryModel = function(m, title, sub, rowKbn, addCol) {
        if (!$.msiJqlib.isNullEx2(m)) {
            m.set('title', title);
            m.set('sub', sub);
            m.set('row_kbn', rowKbn);
            if ($.msiJqlib.isNullEx2(addCol)) {
                applms.summaryCol.add(m);
            } else {
                addCol.add(m);
            }
        }
    };
    /**
     * 見積書または請求書モデル設定処理
     * @param {SummaryModel} m 見積書または請求書モデル
     * @param {string} title タイトル
     * @param {string} item 商品名
     * @param {string} sub サブタイトル
     * @param {string} rowKbn 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4=>消費税等行 9:合計行
     * @param {SummaryCollection} addCol 存在すれば、ここにモデルを追加する
     */
    var _setSummaryModel2 = function(m, title, item, sub, rowKbn, addCol) {
        if (!$.msiJqlib.isNullEx2(m)) {
            m.set('title', title);
            m.set('shohin_tkiyo_nm', item);
            m.set('sub', sub);
            m.set('row_kbn', rowKbn);
            if ($.msiJqlib.isNullEx2(addCol)) {
                applms.summaryCol.add(m);
            } else {
                addCol.add(m);
            }
        }
    };
    /**
     * 見積書または請求書消費税等モデル設定処理
     * @param {string} title タイトル
     * @param {number} prc 金額
     */
    var _setShohiEtc = function(title, prc) {
        if (!$.msiJqlib.isNullEx2(prc)) {
            prc = prc * 1;
            if (prc !== 0) {
                var m = new SummaryModel({price: prc});
                _setSummaryModel(m, title, null, ROW_KBN_D);
                applms.prcSum += prc;
            }
        }
    };
    /**
     * 見積書または請求書一行の金額を設定して返す処理
     * @param {MeisaiCollection} meisaiCol 明細コレクション
     * @param {object} filters 絞り込み条件
     */
    var _getSummaryOne = function(meisaiCol, filters) {
        var m, refms, refm;
        refms = meisaiCol.where(filters);
        if (refms.length === 1) {
            refm = refms[0];
            m = new SummaryModel();
            m.set('price', refm.get('juchu_prc'));// 金額
        }
        return m;
    };

    // 葬送儀礼データ設定処理
    var _setSosogire = function() {
        // 葬送儀礼費用基本プランデータ
		var dataS1 = null;
		if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
			dataS1 = applms.meisaiCol.filter(function(item) {
				return item.get("tab_kbn") === TAB_KBN_S && item.get("chu_bunrui_cd") === '0020'; // 【Ａ】葬送儀礼費用基本プラン
			});
		} else {
			dataS1 = applms.meisaiCol.filter(function(item) {
				return item.get("tab_kbn") === TAB_KBN_S && item.get("chu_bunrui_cd") === '0020' && item.get("add_kbn") === '0'; // 【Ａ】葬送儀礼費用基本プラン
			});
		}	
        var dataColS1 = new MeisaiCollection();
        dataColS1.add(dataS1);
        var dataColSPx = new MeisaiCollection();
        dataColSPx.add(dataColSPx);
		var gojokai_kbn = applms.appModel.get('gojokai_kbn');
        // 葬送儀礼費用プラン外選択商品データ
		var dataS2 = null; 
		if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
			dataS2 = applms.meisaiCol.filter(function(item) {
				return item.get("tab_kbn") === TAB_KBN_S &&
						item.get("dai_bunrui_cd") === '0010' &&
						(item.get("chu_bunrui_cd") !== '0020' // 大分類が葬送儀礼かつ中分類が【Ａ】葬送儀礼費用基本プラン以外
						||item.get("add_kbn") !== '0')	
			});
		} else {
			// ナウエルメンバーズ
			dataS2 = applms.meisaiCol.filter(function(item) {
				return item.get("tab_kbn") === TAB_KBN_S &&
						item.get("dai_bunrui_cd") === '0010' &&
						item.get("chu_bunrui_cd") === '0040' &&	// 基本プラン
						item.get("add_kbn") === '0'
			});
		}	
        var dataColS2 = new MeisaiCollection();
        dataColS2.add(dataS2);

		var dataS3 = null;
		var dataColS3 = null;
		var dataS4 = null; 
		var dataColS4 = null;
		var dataS5 = null; 
		var dataColS5 = null;
		// 基本プラン
		if (gojokai_kbn >= 17 && gojokai_kbn <= 25)	{
			if (gojokai_kbn != 21)	{
				// ナウエルメンバーズ
				dataS3 = applms.meisaiCol.filter(function(item) {
					return item.get("tab_kbn") === TAB_KBN_S &&
							item.get("dai_bunrui_cd") === '0010' &&
							item.get("chu_bunrui_cd") === '0055';	// 式場使用料
				});
				dataColS3 = new MeisaiCollection();
				dataColS3.add(dataS3);
				// ナウエルメンバーズ
				dataS4 = applms.meisaiCol.filter(function(item) {
					return item.get("tab_kbn") === TAB_KBN_S &&
							item.get("dai_bunrui_cd") === '0010' &&
							item.get("chu_bunrui_cd") === '0057';// プラン割引
				});
				dataColS4 = new MeisaiCollection();
				dataColS4.add(dataS4);
				// ナウエルメンバーズ
				dataS5 = applms.meisaiCol.filter(function(item) {
					return item.get("tab_kbn") === TAB_KBN_S &&
							item.get("dai_bunrui_cd") === '0010' &&
							(item.get("add_kbn") === '1' || item.get("add_kbn") === '9' || item.get("nebiki_kbn") === '0')
						/*	&&
							(item.get("chu_bunrui_cd") !== '0020'
						  && item.get("chu_bunrui_cd") !== '0040'
						  && item.get("chu_bunrui_cd") !== '0055'
						  && item.get("chu_bunrui_cd") !== '0057')// 葬送儀礼費用プラン外選択商品
						*/  
				});
				dataColS5 = new MeisaiCollection();
				dataColS5.add(dataS5);
			}	
		}

        // 葬送儀礼費用基本ヘッダーを追加
        var mk = new MeisaiModel();
        mk.set('shohin_kbn_nm', applms.appModel.get('sosogorei_plan_a_nm'));
		var shohin_nm = null;
		if (gojokai_kbn >= 17 && gojokai_kbn <= 25)	{
			if (gojokai_kbn != 21)	{
				_.each(dataS2, function(m) {
					if (m.get('dai_bunrui_cd') == '0010' && m.get('chu_bunrui_cd') == '0020' && m.get('shohin_kbn') == '0020') {
						shohin_nm = m.get('shohin_nm');
						m.set('shohin_nm', '祭壇プラン');
					}
				});
				mk.set('shohin_nm', shohin_nm);
			}	
		}	
        mk.set('tab_kbn', TAB_KBN_S);
        mk.set('record_kbn', REC_KBN_T);
        mk.set('add_down_kbn', ADD_KBN_NG);
        dataColS1.add(mk, {at: 0});

		if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
			// 小計行を追加する
			_setShokeiRow(dataColS1, TAB_KBN_S, TAB_SUB_KBN_1);
		}	
        // 葬送儀礼費用プラン外選択商品ヘッダーを追加
        var mg = new MeisaiModel();
        mg.set('shohin_kbn_nm', applms.appModel.get('sosogorei_plan_b_nm'));
        mg.set('tab_kbn', TAB_KBN_S);
        mg.set('record_kbn', REC_KBN_T);
        mg.set('add_down_kbn', ADD_KBN_OK);
        dataColS2.add(mg, {at: 0});
        // 小計行を追加する
		if (gojokai_kbn < 17 || gojokai_kbn > 25 || gojokai_kbn == 21)	{
			_setShokeiRow(dataColS2, TAB_KBN_S, TAB_SUB_KBN_2);
		}	
        // マージする
        dataColS1.add(dataColS2.toJSON());
        if (!$.msiJqlib.isNullEx2(dataColS3) && dataColS3.length > 0) {
			//会場費ヘッダーを追加
			var mkaijyo = new MeisaiModel();
			mkaijyo.set('shohin_kbn_nm', applms.appModel.get('sosogorei_plan_c_nm'));
			mkaijyo.set('tab_kbn', TAB_KBN_S);
			mkaijyo.set('record_kbn', REC_KBN_T);
			mkaijyo.set('add_down_kbn', ADD_KBN_OK);
			dataColS3.add(mkaijyo, {at: 0});
			// マージする
			dataColS1.add(dataColS3.toJSON());
		}
        if (!$.msiJqlib.isNullEx2(dataColS4) && dataColS4.length > 0) {
			//会場費ヘッダーを追加
			var mnebiki = new MeisaiModel();
			mnebiki.set('shohin_kbn_nm', applms.appModel.get('sosogorei_plan_d_nm'));
			mnebiki.set('tab_kbn', TAB_KBN_S);
			mnebiki.set('record_kbn', REC_KBN_T);
			mnebiki.set('add_down_kbn', ADD_KBN_OK);
			dataColS4.add(mnebiki, {at: 0});
			// マージする
			dataColS1.add(dataColS4.toJSON());
		}
		// 小計行を追加する
		if (gojokai_kbn >= 17 && gojokai_kbn <= 25)	{
			if (gojokai_kbn != 21) {
				_setShokeiRow(dataColS1, TAB_KBN_S, SOSOGIREI);
			}
		}	
        if (!$.msiJqlib.isNullEx2(dataColS5) && dataColS5.length > 0) {
			//葬送儀礼費用プラン外選択商品
			var mpl = new MeisaiModel();
			mpl.set('shohin_kbn_nm', applms.appModel.get('sosogorei_plan_e_nm'));
			mpl.set('tab_kbn', TAB_KBN_S);
			mpl.set('record_kbn', REC_KBN_T);
			mpl.set('add_down_kbn', ADD_KBN_OK);
			dataColS5.add(mpl, {at: 0});
			// 小計行を追加する
			_setShokeiRow(dataColS5, TAB_KBN_S, TAB_SUB_KBN_2);
			// マージする
			dataColS1.add(dataColS5.toJSON());
		}	
		// 合計行を追加する
        _setGokeiRow(dataColS1, TAB_KBN_S, SOSOGIREI);
        applms.meisaiCol1.reset(dataColS1.toJSON());
    };

    // 返礼品データ設定処理
    var _setHenrei = function() {
        // 返礼品データ
        var dataH = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_H;
        });
        var dataColH = new MeisaiCollection();
        dataColH.add(dataH);
        // 返礼品互助会充当行を追加する
//        _setHenreiRow(dataColH, TAB_KBN_H);
        // 合計行を追加する
        _setGokeiRow(dataColH, TAB_KBN_H, HENREI);
        applms.meisaiCol2.reset(dataColH.toJSON());

    };

    // 飲食費データ設定処理
    var _setInshoku = function() {
        // 飲食費データ
        var dataI = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_I;
        });
        var dataColI = new MeisaiCollection();
        dataColI.add(dataI);
        // 奉仕料行を追加する
        _setHoshiRow(dataColI, TAB_KBN_I);
        // 合計行を追加する
        _setGokeiRow(dataColI, TAB_KBN_I, INSHOKU);
        applms.meisaiCol3.reset(dataColI.toJSON());

    };

    // 立替金データ設定処理
    var _setTatekae = function() {
        // 立替金データ
        var dataT = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_T;
        });
        var dataColT = new MeisaiCollection();
        dataColT.add(dataT);
        // 合計行を追加する
        _setGokeiRow(dataColT, TAB_KBN_T, TATEKAE);
        applms.meisaiCol4.reset(dataColT.toJSON());

    };

    // その他データ設定処理
    var _setSonota = function() {

        // その他データ
        var dataS = applms.meisaiCol.filter(function(item) {
            return item.get("tab_kbn") === TAB_KBN_O;
        });
        var dataColS = new MeisaiCollection();
        dataColS.add(dataS);
        // 合計行を追加する
        _setGokeiRow(dataColS, TAB_KBN_O, SONOTA);
        applms.meisaiCol5.reset(dataColS.toJSON());
    };

    // 小計行設定処理
    var _setShokeiRow = function(dataCol, tabKbn, tabSubKbn) {
        var shokei = 0;
        shokei = _calcSum(dataCol.models, 'juchu_prc');
        var m = new MeisaiModel();
        m.set('shohin_tkiyo_nm', SHOKEI);
        m.set('tab_kbn', tabKbn);
        m.set('tab_sub_kbn', tabSubKbn);
        m.set('record_kbn', REC_KBN_S);
        m.set('add_down_kbn', ADD_KBN_NG);
        m.set('juchu_prc', shokei);
        dataCol.add(m);
    };

//    // 返礼品互助会充当行設定処理
//    var _setHenreiRow = function(dataCol, tabKbn) {
//        var shohizeiObj = applms.appModel.get('shohizei');
//        if (!$.msiJqlib.isNullEx2(shohizeiObj.henrei)) {
//            var prc = 0;
//            _.each(shohizeiObj.henrei, function(item) {
//                prc += item.gojokai_juto * 1;
//            });
//            if (prc !== 0) {
//                var m = new MeisaiModel();
//                m.set('shohin_kbn_nm', null);
//                m.set('shohin_nm', '互助会充当');
//                m.set('shohin_tkiyo_nm', null);
//                m.set('tab_kbn', tabKbn);
//                m.set('record_kbn', REC_KBN_J);
//                m.set('add_down_kbn', ADD_KBN_NG);
//                m.set('juchu_prc', prc);
//                dataCol.add(m);
//            }
//        }
//    };

    // 奉仕料行設定処理
    var _setHoshiRow = function(dataCol, tabKbn) {
        var hoshi = 0;
        // 飲食費の奉仕料データ
        var dataH = dataCol.filter(function(item) {
            return item.get("hoshi_umu_kbn") === '1'; // 奉仕料あり
        });
        hoshi = _calcSum(dataH, 'hoshi_prc');
        var m = new MeisaiModel();
        m.set('shohin_kbn_nm', HOSHIRYO);
        m.set('shohin_tkiyo_nm', KOME + KAKE + applms.appModel.get('hoshi_ritu_org') + PERCENT);
        m.set('tab_kbn', tabKbn);
        m.set('record_kbn', REC_KBN_H);
        m.set('add_down_kbn', ADD_KBN_NG);
        m.set('juchu_prc', hoshi);
        dataCol.add(m);
    };

    // 合計行設定処理
    var _setGokeiRow = function(dataCol, tabKbn, title) {
        var gokei = 0;
        // 小計を除く
        var dataE = dataCol.filter(function(item) {
            return item.get("record_kbn") !== REC_KBN_S;
        });
        gokei = _calcSum(dataE, 'juchu_prc');
        var m = new MeisaiModel();
        m.set('shohin_kbn_nm', title);
        m.set('tab_kbn', tabKbn);
        m.set('record_kbn', REC_KBN_G);
        m.set('add_down_kbn', ADD_KBN_NG);
        m.set('juchu_prc', gokei);
        dataCol.add(m);
    };

    /**
     * 金額合計処理
     * @param {array} dataCol モデル配列
     * @param {string} tarPro 集計対象モデルプロパティ
     */
    var _calcSum = function(dataCol, tarPro) {
        var gokei = 0;
        _.each(dataCol, function(m) {
            var prc = m.get(tarPro);
            if (!$.msiJqlib.isNullEx2(prc) && !isNaN(prc)) {
                gokei += Number(prc);
            }
        });
        return gokei;
    };

    /**
     * validation valid時処理
     * @param {MeisaiView} view
     * @param {string} attr
     */
    var _valid = function(view, attr) {
        var $el = view.$("." + attr);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };
    /**
     * validation invalid時処理
     * @param {MeisaiView} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function(view, attr, error) {
        var $el = view.$("." + attr);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function() {
            return {
                jichu_kakute_ymd: null, // 受注確定日
                tanto_nm: null, // 受注確定担当者名
                kakutei_info: null, // 受注確定日 + 受注確定担当者名
                kakutei_invoice: null, // 請求書確定日 + 請求書担当者名
                hoshi_ritu_org: null, // 奉仕率
                hoshi_ritu: null, // 奉仕率 / 100
                hasu_kbn: null, // 端数区分
                shohi_zei_rtu: null, // 消費税率
                shohi_zei_cd: null, // 消費税コード
                sosogorei_plan_a_nm: null, // 【Ａ】葬送儀礼費用基本プラン
                sosogorei_plan_b_nm: null, // 【Ｂ】葬送儀礼費用プラン外選択品目
                sosogorei_plan_c_nm: null, // 会場費 
                sosogorei_plan_d_nm: null, // プラン割引
                kaiin_no: null, // 互助会会員番号
                seko_plan_cd: null, // 施行プランコード
                harai_gaku: null, // 互助会払込金額A
                no_seko_plan_msg: null, // 施行プランコードが存在しない時のメッセージ
                print_kbn: '1',			// 印刷区分
				gojokai_kbn: null		// 互助会区分
            };
        }
    }); // AppModel

    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click .tab li a": "changeTab",
            "click #estimate .closable table .head": "summaryHeaderClick",
            "click .function #btn_add": "addRow",
            "click .function #btn_del": "delRow",
            "click .function #btn_kakutei": "mitsukakutei",
            "click .function #btn_torikesi": "mitsutorikesi",
            "click .function #btn_saimitsu": "exeSaimitsu",
            "click #btn_history": "showHistory",
            "click #btn_save": "doSave",
            "click #btn_cancel": "doCancel",
            "click #btn_shonin": "doShonin",
            "click #btn_fix": "doFixInvoice",
            "click #btn_fix_cancel": "doInvoiceCancel",
            "click #btn_print": "doPrint",
            "click #btn_print_shiharai": "doPrintShiharai"
        },
        bindings: {
            '#kakutei_info': 'kakutei_info',
            '#kakutei_invoice': 'kakutei_invoice', // 請求書確定日 + 請求書担当者名
            '#print_kbn': 'print_kbn'
        },
        initialize: function() {
            this.listenTo(applms.summaryCol, 'add', this.addSummaryOne);
            this.listenTo(applms.meisaiCol1, 'reset', this.addSosogireiCol);
            this.listenTo(applms.meisaiCol2, 'reset', this.addHenreiCol);
            this.listenTo(applms.meisaiCol3, 'reset', this.addInshokuCol);
            this.listenTo(applms.meisaiCol4, 'reset', this.addTatekaeCol);
            this.listenTo(applms.meisaiCol5, 'reset', this.addSonotaCol);
            this.listenTo(applms.meisaiCol1, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol2, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol3, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol4, 'add', this.addOneAfter);
            this.listenTo(applms.meisaiCol5, 'add', this.addOneAfter);
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function() {
            Backbone.Validation.bind(this, {
                valid: function(view, attr) {
                    _valid(view, attr);
                },
                invalid: function(view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            return this;
        },
        // 見積書または請求書一行追加処理（最終行に追加）
        addSummaryOne: function(m) {
            var v = new SummaryView({model: m});
            var el = v.render().el;

            // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            var rowKbn = m.get("row_kbn");
            if (rowKbn === ROW_KBN_T || rowKbn === ROW_KBN_D) { // 2:明細のタイトル行, 3:明細行
                this.$("#estimate .bloc").last().find("table").append(el);
            } else {
                var $div;
                if (rowKbn === ROW_KBN_H) { // 1:ヘッダー行
                    $div = $('<div class="bloc closable"></div>');
                } else if (rowKbn === ROW_KBN_S) { // 4:消費税等行
                    $div = $('<div class="bloc"></div>');

                } else if (rowKbn === ROW_KBN_G) { // 9:合計行
                    $div = $('<div class="all"></div>');
                }
                var $table = $('<table/>');
                $table.append(el);
                $div.append($table);
                this.$("#estimate").append($div);
            }

        },
        // 葬送儀礼～その他タブ一行追加処理（最終行に追加）
        addOne: function(m) {
            var v = new MeisaiView({model: m});
            if (m.get('record_kbn') === REC_KBN_G) {
                // 合計行追加
                this.$tabTarget.find(".total table").append(v.render().el);
            } else {
                // 合計行以外の行追加
                this.$tabTarget.find(".meisai .list table").append(v.render().el);
            }
        },
        // 葬送儀礼～その他タブ一行追加処理（指定行の次に追加）
        addOneAfter: function(m) {
            if (applms.tabindex === 1) {
                this.$tabTarget = this.$("#estimate1-tab");
            } else if (applms.tabindex === 2) {
                this.$tabTarget = this.$("#estimate2-tab");
            } else if (applms.tabindex === 3) {
                this.$tabTarget = this.$("#estimate3-tab");
            } else if (applms.tabindex === 4) {
                this.$tabTarget = this.$("#estimate4-tab");
            } else if (applms.tabindex === 5) {
                this.$tabTarget = this.$("#estimate5-tab");
            }
            m.set('tab_kbn', String(applms.tabindex));
            var v = new MeisaiView({model: m});
            if (applms.rowindex === 0) {
                this.$tabTarget.find(".meisai .list table").prepend(v.render().el);
            } else {
                this.$tabTarget.find(".meisai .list table tbody").eq(applms.rowindex - 1).after(v.render().el);
            }
            if (applms.meisai_scroll && applms.addEndFlg) {
                applms.$meisai_scroll.scrollTop(applms.$meisai_scroll[0].scrollHeight);
//                applms.$meisai_scroll.scrollTop(applms.rowindex * 35 - applms.$meisai_scroll[0].clientHeight);
//                var t = applms.$meisai_scroll.scrollTop();
//                console.log(applms.$meisai_scroll[0].scrollHeight);
//                console.log(applms.$meisai_scroll[0].clientHeight);
//                console.log(applms.rowindex * 35);
//                console.log(t);
                
            }
        },
        addCol: function(collection) {
            this.$tabTarget.find('tbody').remove();
            collection.each(this.addOne, this);
        },
        // 葬送儀礼コレクション追加処理
        addSosogireiCol: function(collection) {
            this.$tabTarget = this.$("#estimate1-tab");
            this.addCol(collection);
        },
        // 返礼品コレクション追加処理
        addHenreiCol: function(collection) {
            this.$tabTarget = this.$("#estimate2-tab");
            this.addCol(collection);
        },
        // 飲食コレクション追加処理
        addInshokuCol: function(collection) {
            this.$tabTarget = this.$("#estimate3-tab");
            this.addCol(collection);
        },
        // 立替コレクション追加処理
        addTatekaeCol: function(collection) {
            this.$tabTarget = this.$("#estimate4-tab");
            this.addCol(collection);
        },
        // その他コレクション追加処理
        addSonotaCol: function(collection) {
            this.$tabTarget = this.$("#estimate5-tab");
            this.addCol(collection);
        },
        // タブ切り替え処理
        changeTab: function(e) {
            $.msiJqlib.clearAlert();
            var idx = $("#estimate-div-wrapper .tab li").index($(e.currentTarget).closest("li"));
            if (idx === 0 && !this.mesaiIsEq()) {
                $.msiJqlib.showWarn(MSG_WARN_1);
                return;
            }
            // タブ切り替え
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            // 選択行解除
            this.$('.list tbody tr').removeClass('active');
            // 一旦追加ボタンを活性化
            applms.clearDisabledButtonStyle(this.$(".function #btn_add"));
            // 一旦削除ボタンを非活性化
            applms.setDisabledButtonStyle(this.$(".function #btn_del"));
            // タブインデックス
            applms.tabindex = $("#estimate-div-wrapper .tab li").index($("#estimate-div-wrapper .tab li span").closest("li"));
//            // 見積書または請求書タブクリックした場合、明細変更されたら再集計
//            if (applms.tabindex === 0) {
//                // 明細イコール
//                if (!this.mesaiIsEq()) {
//                    _setSummary();
//                }
//            }
            // 行インデックスインデックス初期化
            applms.rowindex = -1;
            // リサイズ発火
            $(window).trigger("resize");
        },
        // 明細イコールが判定する
        mesaiIsEq: function() {
            // 明細イコール
            var meisaiEq1 = $.msiJqlib.isEqual(applms.meisaiCol1.toJSON(), applms.orgDataCol1);
            var meisaiEq2 = $.msiJqlib.isEqual(applms.meisaiCol2.toJSON(), applms.orgDataCol2);
            var meisaiEq3 = $.msiJqlib.isEqual(applms.meisaiCol3.toJSON(), applms.orgDataCol3);
            var meisaiEq4 = $.msiJqlib.isEqual(applms.meisaiCol4.toJSON(), applms.orgDataCol4);
            var meisaiEq5 = $.msiJqlib.isEqual(applms.meisaiCol5.toJSON(), applms.orgDataCol5);
            return meisaiEq1 && meisaiEq2 && meisaiEq3 && meisaiEq4 && meisaiEq5;
        },
        // 見積書または請求書ヘッダー行クリック
        summaryHeaderClick: function(e) {
            var $target = $(e.currentTarget);
            this.toggleView($target);
        },
        // 見積書または請求書ヘッダー行クリックにより明細表示・非表示処理
        toggleView: function($target) {
            var $shosai = $target.parent('table').find(".shosai");
            $shosai.toggle();
            $target.toggleClass("display-none");
            $shosai.toggleClass("display-none");
            // リサイズ発火
            $(window).trigger("resize");
        },
        //　行追加処理
        addRow: function() {
            if (applms.tabindex > 0) {
                var mesaiCol = this.getMeisaiCol();
                var m = new MeisaiModel();
                m.set('shohin_kbn_nm', 'New');
                m.set('add_kbn', ADD_KBN_MITSU);
                //var selectedModel = mesaiCol.at(applms.rowindex);
                applms.addEndFlg = false;
                if (applms.rowindex === -1) {
                    applms.addEndFlg = true;
                    applms.rowindex = mesaiCol.length - 1;
                    // 葬送儀礼の場合は小計飲食の場合は奉仕料があるため、マイナス１をする
                    if (applms.tabindex === 1 || applms.tabindex === 3) {
                        applms.rowindex -= 1;
                    } else if (applms.tabindex === 2) { // 返礼品の場合、互助会充当行分引く
                        var len = applms.meisaiCol2.where({record_kbn: REC_KBN_J}).length;
                        if (len > 0) {
                            applms.rowindex -= 1;
                        }
                    }
                    mesaiCol.add(m, {at: applms.rowindex});
                } else {
                    applms.rowindex += 1;
                    mesaiCol.add(m, {at: applms.rowindex});
                }
                // 追加した行を選択
                this.$tabTarget.find(".meisai .list table tbody").eq(applms.rowindex).find("tr").click();
                // スクロールが出現される可能性があるためリサイズ発火
                $(window).trigger("resize");
            }
        },
        //　行削除処理
        delRow: function() {
            if (applms.tabindex > 0 && applms.rowindex > -1) {
                var mesaiCol = this.getMeisaiCol();
                var m = mesaiCol.at(applms.rowindex);
                // 追加区分 0:トランデータ, 1:見積画面新規入力データ
                if (m.get('add_kbn') === ADD_KBN_MITSU && !$.msiJqlib.isNullEx2(m.get('msi_no'))) {
                    // データの場合は削除コレクションに追加する
                    applms.meisaiDelCol.add(m.clone());
                }
                m.destroy();
                applms.rowindex = -1;
                this.calcSum();
                // スクロールがなくなる可能性があるためリサイズ発火
                $(window).trigger("resize");
            }
            // 削除ボタンを非活性化
            applms.setDisabledButtonStyle(this.$(".function #btn_del"));
            // 追加ボタンを活性化
            applms.clearDisabledButtonStyle(this.$(".function #btn_add"));
        },
        // 明細行の金額の小計と合計計算処理
        calcSum: function() {
            if (applms.tabindex > 0) {
                var mesaiCol = applms.appView.getMeisaiCol();
                var shokei = 0;
                var gokei = 0;
                var hoshi = 0;
                _.each(mesaiCol.models, function(m) {
                    var prc = m.get('juchu_prc');
                    var hoshi_prc = m.get('hoshi_prc');
                    var recordKbn = m.get('record_kbn');// レコード区分 1:タイトル行, 2:明細行, 3:小計行,4:奉仕料行 9:合計行
                    var hoshiKbn = m.get('hoshi_umu_kbn');// 奉仕料有無区分 0：奉仕料なし 1：奉仕料あり

                    if (hoshiKbn === '1') {
                        if (!$.msiJqlib.isNullEx2(hoshi_prc) && !isNaN(hoshi_prc)) {
                            hoshi += Number(m.get('hoshi_prc'));
                        }
                    }
                    if (!$.msiJqlib.isNullEx2(prc) && !isNaN(prc)) {
                        if (recordKbn === REC_KBN_M) { // 2:明細行
                            shokei += Number(prc);
                            gokei += Number(prc);
                        } else if (recordKbn === REC_KBN_S) {// 3:小計行
                            m.set('juchu_prc', shokei);
                            shokei = 0;
                        } else if (recordKbn === REC_KBN_H) {// 4:奉仕料行
                            m.set('juchu_prc', hoshi);
                            gokei += Number(hoshi);
                        } else if (recordKbn === REC_KBN_G) {// 9:合計行
                            m.set('juchu_prc', gokei);
                        }
                    }

                });
            }
        },
        // タブインデックスによる明細コレクションを取得する
        getMeisaiCol: function() {
            switch (applms.tabindex) {
                case 0:
                    return applms.summaryCol;
                case 1:
                    return applms.meisaiCol1;
                case 2:
                    return applms.meisaiCol2;
                case 3:
                    return applms.meisaiCol3;
                case 4:
                    return applms.meisaiCol4;
                case 5:
                    return applms.meisaiCol5;
            }
        },
        // タブインデックスによる明細コレクションの件数を取得する
        getMeisaiColLength: function() {
            return this.getMeisaiCol().length;
        },
        isInputOk: function() {
            var aMsg = [];

            var meisaiCol = [applms.meisaiCol1, applms.meisaiCol2, applms.meisaiCol3, applms.meisaiCol4, applms.meisaiCol5];
            _.each(meisaiCol, function(col) {
                col.each(function(m, i) {
                    var addKbn = m.get('add_kbn');
                    if (addKbn === ADD_KBN_MITSU) {
                        var resLine = m.validate();
                        if (resLine) {
                            _.each(resLine, function(v, k) {
                                if ($.inArray(v, aMsg) < 0) {
                                    aMsg.push(v);
                                }
                                ;
                            });
                        }
                    }
                });
            });

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#estimate-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#estimate1-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#estimate2-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                } else if (this.$("#estimate3-tab").find(errClsNm).length) {
                    $li.eq(3).find("a").click();
                } else if (this.$("#estimate4-tab").find(errClsNm).length) {
                    $li.eq(4).find("a").click();
                } else if (this.$("#estimate5-tab").find(errClsNm).length) {
                    $li.eq(5).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }

            // OK 
            $.msiJqlib.clearAlert();
            return true;
        },
        mitsukakutei: function() {
            applms.mitsukakutei(this);
        },
        mitsutorikesi: function() {
            this.exeCheck();
//            applms.mitsutorikesi(this);
        },
//        saimitsu: function () {
//            this.exeCheck(this.exeSaimitsu);
//        },
        exeSaimitsu: function () {
            if (!confirm('再見積を行います。よろしいですか？')) {
                return;
            }
            $.msiJqlib.setProgressing(true);
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/reestimate',
                data: {
                    dataAppJson: JSON.stringify(applms.appModel.toJSON())
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else if (mydata.status === 'WARN') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        showHistory: function () {
            var seko_no = applms.appModel.get("seko_no");
            if (!seko_no)
                return;
            this._showNew('/juchu/mitsu/history/sn/' + seko_no);
        },
        // 他データ参照
        _showNew: function (path) {
            var url = $.msiJqlib.baseUrl() + path;
            var that = this;
            var refreshFunc = function () {
                console.log('refresh searching...');
                that.doSearch();
            };
            msiLib2.openWinSub(refreshFunc, url);
        },
        exeCheck: function() {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkgetujifixmitsu',
                data: {dataAppJson:JSON.stringify(applms.appModel.toJSON())},
                type: 'POST',
                success: function(mydata) {
                    $.msiJqlib.setProgressing( false );
                    if (mydata.status === 'OK') {
                        applms.mitsutorikesi(that);
                    } else if (mydata.status === 'INFO') {
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        applms.mitsutorikesi(that);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doSave: function() {
            $.msiJqlib.clearAlert();
            if (!this.isInputOk()) {
                return;
            }
            // 明細イコール
            var meisaiEq1 = $.msiJqlib.isEqual(applms.meisaiCol1.toJSON(), applms.orgDataCol1);
            var meisaiEq2 = $.msiJqlib.isEqual(applms.meisaiCol2.toJSON(), applms.orgDataCol2);
            var meisaiEq3 = $.msiJqlib.isEqual(applms.meisaiCol3.toJSON(), applms.orgDataCol3);
            var meisaiEq4 = $.msiJqlib.isEqual(applms.meisaiCol4.toJSON(), applms.orgDataCol4);
            var meisaiEq5 = $.msiJqlib.isEqual(applms.meisaiCol5.toJSON(), applms.orgDataCol5);

            if (meisaiEq1 && meisaiEq2 && meisaiEq3 && meisaiEq4 && meisaiEq5) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                meisaiChangeFlg1: !meisaiEq1,
                meisaiChangeFlg2: !meisaiEq2,
                meisaiChangeFlg3: !meisaiEq3,
                meisaiChangeFlg4: !meisaiEq4,
                meisaiChangeFlg5: !meisaiEq5
            });
            // app情報
            var dataAppJson = JSON.stringify(applms.appModel.toJSON());
            // 明細情報
            var dataCol1 = this.filterCol(applms.meisaiCol1); // 葬送儀礼
            var dataCol2 = this.filterCol(applms.meisaiCol2); // 返礼品
            var dataCol3_1 = this.filterCol2(applms.meisaiCol3, '0070'); // 飲食費(料理) 中分類コード:0070
            var dataCol3_2 = this.filterCol2(applms.meisaiCol3, '0080'); // 飲食費(壇払い) 中分類コード:0080
            var dataCol4 = this.filterCol(applms.meisaiCol4); // 立替金
            var dataCol5_1 = this.filterCol2(applms.meisaiCol5, '0090'); // その他(別途費用) 中分類コード:0090
            var dataCol5_2 = this.filterCol2(applms.meisaiCol5, '0120'); // その他(値引き) 中分類コード:0120

            var dataColJson1 = JSON.stringify(dataCol1);
            var dataColJson2 = JSON.stringify(dataCol2);
            var dataColJson3_1 = JSON.stringify(dataCol3_1);
            var dataColJson3_2 = JSON.stringify(dataCol3_2);
            var dataColJson4 = JSON.stringify(dataCol4);
            var dataColJson5_1 = JSON.stringify(dataCol5_1);
            var dataColJson5_2 = JSON.stringify(dataCol5_2);
            // 明細削除データ
            var dataDelColJson = JSON.stringify(applms.meisaiDelCol.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/estimatesave',
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson1: dataColJson1,
                    dataColJson2: dataColJson2,
                    dataColJson3_1: dataColJson3_1,
                    dataColJson3_2: dataColJson3_2,
                    dataColJson4: dataColJson4,
                    dataColJson5_1: dataColJson5_1,
                    dataColJson5_2: dataColJson5_2,
                    dataDelColJson: dataDelColJson,
                    changeFlg: changeFlg
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        applms.resetData(mydata.dataApp, mydata.dataCol);
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        filterCol: function(meisaiCol) {
            var dataCol = meisaiCol.filter(function(item) {
                return item.get("record_kbn") === REC_KBN_M;
            });
            return dataCol;
        },
        filterCol2: function(meisaiCol, chubunruiCd) {
            var dataCol = meisaiCol.filter(function(item) {
                return item.get("record_kbn") === REC_KBN_M && item.get("chu_bunrui_cd") === chubunruiCd;
            });
            return dataCol;
        },
        doPrint: function() {
            if (this.isChangeShowWarn()) {
                return;
            }
            var sekoNo = applms.appModel.get("seko_no");
//            var moushiKbn = applms.appModel.get("moushi_kbn");
            var printKbn = applms.appModel.get("print_kbn");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + applms.printURI,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    data_kbn: '1',
                    printKbn: printKbn,
                    output_kbn: 1
                }
            });
            return;
//            var $form = $("#estimate-form");
//            $.msiJqlib.setHiddenInput('preview', 'off', $form);
//            $.msiJqlib.setHiddenInput('seko_no', sekoNo, $form);
//            $.msiJqlib.setHiddenInput('data_kbn', moushiKbn, $form);
//            $form.attr('action', $.msiJqlib.baseUrl() + applms.printURI);
//            $form.attr('method', 'POST');
//            $form.submit();
        },
        doPrintShiharai: function() {
            if (this.isChangeShowWarn()) {
                return;
            }
            var sekoNo = applms.appModel.get("seko_no");
            var printKbn = applms.appModel.get("print_kbn");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + applms.printURI,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    data_kbn: '1',
                    printKbn: printKbn,
                    output_kbn: 1,
					output_shiharai : 1
                }
            });
            return;
        },
        doCancel: function() {
            applms.doCancel(this);
        },
        doShonin: function() {
            applms.doShonin(this);
        },
        doFixInvoice: function() {
            applms.doFixInvoice(this);
        },
        doInvoiceCancel: function() {
            applms.doInvoiceCancel(this);
        },
        isChangeShowWarn: function() {
            $.msiJqlib.clearAlert();
            if (!this.mesaiIsEq()) {
                $.msiJqlib.showWarn(MSG_WARN_1);
                return true;
            } else {
                return false;
            }
        }
    }); // AppView

    /**
     * @description見積書または請求書タブ処理
     */
    // 見積書または請求書モデル
    var SummaryModel = Backbone.Model.extend({
        defaults: function() {
            return {
                title: null, // タイトル
                sub: null, // サブタイトル
                price: null, // 金額
                row_kbn: '3' // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            };
        }
    }); // SummaryModel

    // 見積書または請求書コレクション
    var SummaryCollection = Backbone.Collection.extend({
        model: SummaryModel
    });
    // 見積書または請求書ビュー
    var SummaryView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl1: _.template($('#tmpl-mitsu-head').html()), // 見積書または請求書タブヘッダーテンプレート
        tmpl2: _.template($('#tmpl-mitsu-meisai').html()), // 見積書または請求書タブ明細テンプレート
        events: {
            "click .checkable": "setActive"
        },
        bindings: {
            '.title': 'title',
            '.sub': 'sub',
            '.price': {
                observe: 'price',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            }
        },
        commaOmit: function(val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val) {
            return $.msiJqlib.commaAdd(val);
        },
        initialize: function() {
            this.render();
        },
        render: function() {
            // 行区分 1:ヘッダー行, 2:明細のタイトル行, 3:明細行 4:消費税等行 9:合計行
            var rowKbn = this.model.get("row_kbn");
            if (rowKbn === ROW_KBN_H || rowKbn === ROW_KBN_S) { // 1:ヘッダー行 4:消費税等行
                this.$el.html(this.tmpl1(this.model.toJSON()));
                this.$el.addClass("head");
            } else if (rowKbn === ROW_KBN_G) {// 9:合計行
                this.$el.html(this.tmpl1(this.model.toJSON()));
            } else { // 明細行
                this.$el.html(this.tmpl2(this.model.toJSON()));
                this.$el.addClass("shosai");
                var $tr = this.$("tr");
                if (rowKbn === ROW_KBN_T) {
                    $tr.addClass("plan");
                } else if (rowKbn === ROW_KBN_D) {
                    $tr.addClass("detail");
                }
            }
            this.toggleClass();
            this.stickit();
            return this;
        },
        // 金額がマイナスのときに赤字クラスを追加する処理
        toggleClass: function() {
            var prc = this.model.get('price');
            if (!$.msiJqlib.isNullEx2(prc) && prc < 0) {
                this.$('.price').addClass('com-akaji');
            } else {
                this.$('.price').removeClass('com-akaji');
            }
        }
    });

    /**
     * @description 葬送儀礼費用・返礼品・飲食費・立替・その他タブ処理
     */
    // 明細モデル
    var MeisaiModel = Backbone.Model.extend({
        defaults: function() {
            return {
                dai_bunrui_cd: null, // 大分類コード
                chu_bunrui_cd: null, // 中分類コード
                chu_bunrui_nm: null, // 中分類名
                shohin_kbn: null, // 商品区分
                shohin_kbn_nm: null, // 商品区分名
                shohin_cd: null, // 商品コード
                shohin_nm: null, // 商品名称
                shohin_tkiyo_nm: '', // 商品摘要
                mokuteki_kbn: '0', // 目的区分
                juchu_suryo: null, // 数量
                juchu_tnk: null, // 単価
                juchu_prc: null, // 受注金額
                hoshi_umu_kbn: null, // 奉仕料有無区分
                hoshi_prc: null, // 奉仕料金額
                tab_kbn: null, // タブ区分 1:葬送儀礼, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ,
                tab_sub_kbn: null, // タブサブ区分 同じタブ内の複数分類がある場合1～連番
                nebiki_kbn: '0', // 値引き区分 0:通常商品, 1:互助会値引き商品
                record_kbn: '2', // レコード区分 1:タイトル行, 2:明細行, 3:小計行,4:奉仕料行 9:合計行
                checkable_kbn: '1', // 行選択可能区分 0:選択不可, 1:選択可
                add_down_kbn: '1', // 選択行下に行追加可能区分 0:追加不可, 1:追加可
                del_kbn: '1', // 行削除可能区分 0:削除不可, 1:削除可
                add_kbn: '0', // 追加区分 0:トランデータ, 1:見積画面新規入力データ 9:写真加工等で自動作成データ
//                data_up_flg: '0', // データ更新対象フラグ 0:対象外 1:対象(画面より変更)
                'zei_cd': null, // 軽減税率対応 keigen Kayo
                'reduced_tax_rate': null, // 軽減税率対応  軽減税率区分: 1：対象外 2：軽減8%  keigen Kayo
            };
        },
        validation: {
            shohin_cd: {
                required: true
            },
            shohin_nm: {
                required: true,
                maxLength: 40
            },
            shohin_tkiyo_nm: {
                required: false,
                maxLength: 40
            },
            juchu_suryo: {
                required: true,
                pattern: 'number'
            },
            juchu_tnk: {
                required: true,
                pattern: 'number'
            }
        },
        labels: {
            shohin_nm: '品目',
            shohin_tkiyo_nm: '摘要',
            juchu_suryo: '数量',
            juchu_tnk: '単価'
        }
    }); // MeisaiModel

    // 明細コレクション
    var MeisaiCollection = Backbone.Collection.extend({
        model: MeisaiModel
                //comparator: ''
    });

    // 明細ビュー
    var MeisaiView = Backbone.View.extend({
        tagName: 'tbody',
        // 1:明細行（小計含む） 2:合計行
        tmpl1: _.template($('#tmpl-meisai-1').html()),
        tmpl1k: _.template($('#tmpl-meisai-1k').html()),                // 2019/07/03 ADD Kayo
        tmpl1_hoshi: _.template($('#tmpl-meisai-1-hoshi').html()),
        tmpl1_hoshik: _.template($('#tmpl-meisai-1-hoshik').html()),    // 2019/07/03 ADD Kayo
        tmpl2: _.template($('#tmpl-meisai-2').html()),
        tmpl_add: _.template($('#tmpl-meisai-add').html()),
        tmpl_add2: _.template($('#tmpl-meisai-add-2').html()),
        events: {
            "click .checkable": "setActive",
            "click .juchu_tnk": "preventSelIfReadOnly",
            "click .juchu_suryo": "preventSelIfReadOnly",
            "click .dlg_shohin": "itemHelper",
            "change .hoshi_check": "setHoshi",
            "click .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen Kayo
            "keyup .keigen_disp:not(.no_input)": "showKeigenSel2", // 軽減税率対応 keigen Kayo
        },
        // 軽減税率対応 消費税選択 Kayo keigen
        showKeigenSel2: function(ev) {
            msiGlobalObj.keigenLib.f01ShowKeigenSel2.apply(this, [ev]);
        },
        bindings: {
            '.shohin_kbn_nm': 'shohin_kbn_nm',
            '.shohin_nm': 'shohin_nm',
            '.shohin_tkiyo_nm': {
//                observe: ['shohin_tkiyo_nm', 'hoshi_umu_kbn'],
                observe: 'shohin_tkiyo_nm',
                onGet: function(val) {
                    if (this.model.get('hoshi_umu_kbn') === '1' && this.model.get('dai_bunrui_cd') === '0040') {
                        if (!$.msiJqlib.isNullEx2(val)) {
                            return KOME + " " +  val;
                        } else {
                            return KOME;

                        }
                    } else {
                        return val;
                    }
                }
            },
            '.juchu_suryo': {
                observe: 'juchu_suryo',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.juchu_tnk': {
                observe: 'juchu_tnk',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.juchu_prc': {
                observe: 'juchu_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
			// 軽減税率対応 keigen Kayo
            '.zei_cd': {
                observe: 'zei_cd',
                onGet: function(val, options) { 
                    msiGlobalObj.keigenLib.f01UpdKeigenZeiCd(val, this);
                    return val;
                },
                afterUpdate: function($el, val, options) {
                    msiGlobalObj.keigenLib.f01UpdKeigenZeiCd(val, this);
                },
            },
		},
        commaOmit: function(val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function(val) {
            return $.msiJqlib.commaAdd(val);
        },
        initialize: function() {
            this.listenTo(this.model, 'destroy', this.remove);
            this.listenTo(this.model, 'change:juchu_tnk change:juchu_suryo', this.calcGokei);
            this.listenTo(this.model, 'change:juchu_tnk change:juchu_suryo', this.toggleClass);
//            this.listenTo(this.model, 'change', this.setUpFlg);
            Backbone.Validation.bind(this);
//            this.render();
        },
        render: function() {
            Backbone.Validation.bind(this, {
                valid: function(view, attr) {
                    _valid(view, attr);
                },
                invalid: function(view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            // レコード区分
            var recordKbn = this.model.get("record_kbn");
            if (recordKbn === REC_KBN_G) { // 合計行
                this.$el.html(this.tmpl2(this.model.toJSON()));
            } else { // 明細行(小計含む)
                // 追加区分
                var addKbn = this.model.get("add_kbn");
                // タブ区分
                var tabKbn = this.model.get("tab_kbn");
                // 発注済み 0:未発注 1:発注済み
                var orderFlg = this.model.get("order_flg");
                if ((addKbn === ADD_KBN_MITSU) && $.msiJqlib.isNullEx(applms.appModel.get('jichu_kakute_ymd')) && orderFlg !== '1') { // 画面で追加されたデータかつ未確定
                    if (tabKbn === TAB_KBN_I) {
                        this.$el.html(this.tmpl_add2(this.model.toJSON()));
                        // 大分類コード
                        var dai_bunrui_cd = this.model.get("dai_bunrui_cd");
                        if (dai_bunrui_cd === '0040') { // 壇払い
                            this.setCheckBoxStatus();
                        } else {
                            this.$(".hoshi_check").hide();
                        }
                    } else {
                        this.$el.html(this.tmpl_add(this.model.toJSON()));
                    }
                    this.afterPickUp();
                } else {
                    if (applms.controllerName == 'seikyusyo') {
                        if (applms.viewKbn === '1' && tabKbn === TAB_KBN_I) { // 見積画面かつ飲食費タブ
                            this.$el.html(this.tmpl1_hoshi(this.model.toJSON()));
                        } else {
                            this.$el.html(this.tmpl1(this.model.toJSON()));
                        }    
                    } else {    
                        if (applms.viewKbn === '1' && tabKbn === TAB_KBN_I) { // 見積画面かつ飲食費タブ
                            if (recordKbn === REC_KBN_M) { // 明細行 2019/07/03 Kayo keigen  
                                this.$el.html(this.tmpl1_hoshik(this.model.toJSON()));
                            } else {
                                this.$el.html(this.tmpl1_hoshi(this.model.toJSON()));
                            }    
                        } else {
                            if (recordKbn === REC_KBN_M) { // 明細行 2019/07/03 Kayo keigen  
                                this.$el.html(this.tmpl1k(this.model.toJSON()));
                            } else {                       // 2019/07/03 Kayo keigen  
                                this.$el.html(this.tmpl1(this.model.toJSON()));
                            }    
                        }
                    }    
                    if (orderFlg === '1') {
                        this.$el.find("tr").attr("title", "発注済みです");
                    }
                }
                this.setCssClass();
            }
            this.toggleClass();
            this.stickit();
            return this;
        },
        // 各種区分によるCSSクラスを設定する
        setCssClass: function() {
            var $tr = this.$("tr");
            // レコード区分
            var recordKbn = this.model.get("record_kbn");
            if (recordKbn === REC_KBN_T) {
                // タイトル行の場合
                $tr.addClass("plan");
            } else {
                $tr.addClass("detail");
            }
            // 値引き区分
            var nebikiKbn = this.model.get("nebiki_kbn");
            // 大分類コードが 0070:値引きも赤表示
            var daiBunruiCd = this.model.get("dai_bunrui_cd");
            if (nebikiKbn === NEBIKI_KBN_NEBIKI || daiBunruiCd === '0070') {
                $tr.addClass("minus");
            }
            // 行選択可能区分
            var checkKbn = this.model.get("checkable_kbn");
            if (checkKbn === CHECK_KBN_OK) {
                $tr.addClass("checkable");
            }
            // 奉仕料行は摘要を右寄せ
            if (recordKbn === REC_KBN_H) {
                $tr.find(".summary").css("text-align", "right");
            }

        },
        // 明細行選択処理 ORG:main.jsの /* 見積り行選択 */
        setActive: function(e) {
            var $tr = this.$("tr");
            // 選択した行のインデックス 
            applms.rowindex = $tr.closest("table").find("tbody").index($tr.closest("tbody"));

            // 入力項目の場合は選択状態を変更しない
            if ($(e.target).hasClass("input_field")) {
                $('.list tbody tr').removeClass('active');
                $tr.toggleClass('active', true);
            } else {
                if ($tr.hasClass('active')) {
                    $tr.removeClass('active');
                    // 行インデックスインデックス初期化
                    applms.rowindex = -1;
                } else {
                    $('.list tbody tr').removeClass('active');
                    $tr.addClass('active');
                }
            }
            this.setAddAndDelButton();

        },
        // 行追加と削除のボタン制御処理
        setAddAndDelButton: function() {

            var $tr = this.$("tr");
            // 親のタブ
            var $tab = $tr.closest(".tab-contents");
            // 選択行下に行追加可能区分 0:追加不可, 1:追加可
            var addDownKbn = this.model.get("add_down_kbn");
            // 行選択されていない、または追加可の場合
            if (!$tr.hasClass('active') || addDownKbn === '1') {
                applms.clearDisabledButtonStyle($tab.find("#btn_add"));
            } else {
                applms.setDisabledButtonStyle($tab.find("#btn_add"));
            }
            // 追加区分 0:トランデータ, 1:見積画面新規入力データ（トランに保存済み）, 2:見積画面新規入力データ（トランに保存前）
            var addKbn = this.model.get("add_kbn");
            // 発注済み 0:未発注 1:発注済み
            var orderFlg = this.model.get("order_flg");
            if ((addKbn === ADD_KBN_MITSU) && applms.rowindex !== -1 && orderFlg !== '1') {
                applms.clearDisabledButtonStyle($tab.find("#btn_del"));
            } else {
                applms.setDisabledButtonStyle($tab.find("#btn_del"));
            }
        },
        // 商品ヘルパー処理
        itemHelper: function() {
            var m = this.model;
            var that = this;
            var wheredata = this.getWhere();
            this.$el.msiPickHelper({
                action: 'shohin0',
                mydata: wheredata,
                onSelect: function(data) {
                    m.set({
                        'dai_bunrui_cd': data.dai_bunrui_cd
                        , 'chu_bunrui_cd': data.chu_bunrui_cd
                        , 'shohin_kbn': data.shohin_kbn
                        , 'shohin_kbn_nm': data.shohin_kbn_nm
                        , 'shohin_cd': data.code
                        , 'shohin_nm': data.name
                        , 'shohin_tkiyo_nm': data.shohin_tkiyo_nm
                        , 'juchu_suryo': 1
                        , 'juchu_tnk': that.commaOmit(data.tanka)
                        , 'juchu_prc': that.commaOmit(data.tanka)
                        , 'hoshi_umu_kbn': data.hoshi_umu_kbn
                        , 'nm_input_kbn': data.nm_input_kbn
                        , 'siire_cd': data.siire_cd
                        , 'siire_lnm': data.siire_lnm
                        , 'gen_tnk': that.commaOmit(data.genka)
                        , 'tani_cd': data.tani_cd
                        , 'zei_kbn': data.uri_zei_kbn
                        , 'uri_kamoku_cd': data.uri_kamoku_cd
                        , 'tnk_chg_kbn': data.tnk_chg_kbn
                        , 'hachu_kbn': data.hachu_kbn
                        , 'mokuteki_kbn': data.mokuteki_kbn
                        , 'zei_cd': data.uri_zei_cd  // 軽減税率対応   keigen Kayo
                        , 'reduced_tax_rate': data.uri_reduced_tax_rate_ex  // 軽減税率対応  keigen Kayo
                    });
                    that.afterPickUp();
                },
                onClear: function() {
//                    m.set({
//                        'dai_bunrui_cd': null
//                        , 'chu_bunrui_cd': null
//                        , 'shohin_kbn': null
//                        , 'shohin_kbn_nm': null
//                        , 'shohin_cd': null
//                        , 'shohin_nm': null
//                        , 'shohin_tkiyo_nm': null
//                        , 'juchu_suryo': null
//                        , 'juchu_tnk': null
//                        , 'juchu_prc': null
//                        , 'hoshi_umu_kbn': null
//                        , 'nm_input_kbn': null
//                        , 'siire_cd': null
//                        , 'siire_lnm': null
//                        , 'gen_tnk': null
//                        , 'tani_cd': null
//                        , 'zei_kbn': null
//                        , 'uri_kamoku_cd': null
//                        , 'tnk_chg_kbn': null
//                    });
//                    that.afterPickUp();
                }
            });
        },
        // 商品ピックアップ条件を取得する
        getWhere: function() {
            switch (applms.tabindex) {
                case 1:
                    // 大分類 0010:葬送儀礼 中分類 IN 0020:【Ａ】葬送儀礼費用基本プラン,0030:【Ｂ】葬送儀礼費用プラン外選択商品,0040:プラン,0050:小国町限定特典
                    return {s_dai_bunrui_cd: '0010', s_chu_bunrui_cd: '0020,0030,0040,0050'};
                case 2:
                    // 大分類 0020:会葬返礼 中分類 IN 0060:返礼品
                    return {s_dai_bunrui_cd: '0020', s_chu_bunrui_cd: '0060'};
                case 3:
                    // 大分類 0030:料理,0040:壇払 中分類 IN 0070:飲食費,0080:壇払
                    return {s_dai_bunrui_cd: '0030,0040', s_chu_bunrui_cd: '0070,0080'};
                case 4:
                    // 大分類 0060:立替費用 IN 0100:立替金
                    return {s_dai_bunrui_cd: '0060', s_chu_bunrui_cd: '0100'};
                case 5:
                    // 大分類 0050:別途費用,0070:値引 中分類 IN 0090:別途費用,0120:値引き
                    return {s_dai_bunrui_cd: '0050,0070', s_chu_bunrui_cd: '0090,0120'};
            }
        },
        // pickup終了後処理
        afterPickUp: function() {
            // 名称入力区分による制御
            this.setInputType();
            // 売上単価変更区分による制御
            this.setTankaType();
            // 受注数量制御
            if ($.msiJqlib.isNullEx2(this.model.get("juchu_suryo"))) {
                this.$('.juchu_suryo').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            } else {
                this.$('.juchu_suryo').removeAttr(ATTR_NO_INPUT);
            }
            // 奉仕料チェックボックス制御
            if (this.model.get("dai_bunrui_cd") === '0040') {
                this.$(".hoshi_check").show();
                this.setCheckBoxStatus();
            } else {
                this.$(".hoshi_check").hide();
            }
        },
        /**
         * 名称入力区分による制御
         * 1：名称変更なし（摘要も入力不可）、
         * 2：名称変更あり（摘要は入力不可）、
         * 3：摘要追加のみ（摘要入力可）、
         * 4：名称＋摘要（両方入力可）
         */
        setInputType: function() {
            var nmInputKbn = this.model.get("nm_input_kbn");
            if (nmInputKbn === '2' || nmInputKbn === '4') {
                // 商品名入力可にする
                this.$('.shohin_nm').removeAttr(ATTR_NO_INPUT);
            } else {
                // 商品名入力不可にする
                this.$('.shohin_nm').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
            if (nmInputKbn === '3' || nmInputKbn === '4') {
                // 摘要入力可にする
                this.$('.shohin_tkiyo_nm').removeAttr(ATTR_NO_INPUT);
            } else {
                // 摘要入入力不可にする
                this.$('.shohin_tkiyo_nm').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
        },
        /**
         * 売上単価変更区分による制御
         * 0：単価変更不可、1：単価変更可
         */
        setTankaType: function() {
            var taniKbn = this.model.get("tnk_chg_kbn");
            if (taniKbn === "1") {
                this.$('.juchu_tnk').removeAttr(ATTR_NO_INPUT);
            } else {
                this.$('.juchu_tnk').attr(ATTR_NO_INPUT, ATTR_NO_INPUT);
            }
        },
        // 奉仕料チェックボックスチェック処理
        setHoshi: function(e) {
//            e.stopImmediatePropagation();　// クリック処理でやる必要がある
            this.setCheckBoxModel(e, 'hoshi_umu_kbn');
            this.calcGokei();
        },
        // チェックボックス切り替え処理
        setCheckBoxModel: function(e, attr) {
            var val = this.$('.hoshi_check:checked').val();
            this.model.set(attr, val === "1" ? "1" : "0");
        },
        // チェックボックス初期設定処理
        setCheckBoxStatus: function() {
            // 奉仕料チェックボックス初期設定処理
            if (this.model.get("hoshi_umu_kbn") === "1" && this.$('.hoshi_check:checked').val() !== "1") {
                this.$('.hoshi_check').attr("checked", "checked");
            }
        },
        // 一行の合計処理
        calcGokei: function() {
            var m = this.model;
            var tnk = Number(m.get("juchu_tnk"));
            var suryo = m.get("juchu_suryo");
            if (!$.msiJqlib.isNullEx2(tnk) && !$.msiJqlib.isNullEx2(suryo) && !isNaN(tnk) && !isNaN(suryo)) {
                var hoshiPrc = 0;
                var hasuKbn = applms.appModel.get('hasu_kbn');
                var hoshiRitu = applms.appModel.get('hoshi_ritu');
                // 奉仕料有り 
                if (m.get("hoshi_umu_kbn") === "1") {
                    // 丸め処理 0:切捨て 1:四捨五入 2:切上げ
                    if (hasuKbn === "0") {
                        hoshiPrc = Math.floor(tnk * suryo * hoshiRitu);
                    } else if (hasuKbn === "1") {
                        hoshiPrc = Math.round(tnk * suryo * hoshiRitu);
                    } else if (hasuKbn === "2") {
                        hoshiPrc = Math.ceil(tnk * suryo * hoshiRitu);
                    }
                    m.set('hoshi_prc', hoshiPrc);
                } else {
                    m.set('hoshi_prc', 0);
                }
                m.set('juchu_prc', tnk * suryo);
            } else {
                m.set('juchu_prc', null);
            }
            // 小計・合計計算処理
            applms.appView.calcSum();

        },
        // 金額がマイナスのときに赤字クラスを追加する処理
        toggleClass: function() {
            var suryo = this.model.get('juchu_suryo');
            var tnk = this.model.get('juchu_tnk');
            var prc = this.model.get('juchu_prc');
            if (!$.msiJqlib.isNullEx2(suryo) && suryo < 0) {
                this.$('.juchu_suryo').addClass('com-akaji');
            } else {
                this.$('.juchu_suryo').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(tnk) && tnk < 0) {
                this.$('.juchu_tnk').addClass('com-akaji');
            } else {
                this.$('.juchu_tnk').removeClass('com-akaji');
            }
            if (!$.msiJqlib.isNullEx2(prc) && prc < 0) {
                this.$('.juchu_prc').addClass('com-akaji');
            } else {
                this.$('.juchu_prc').removeClass('com-akaji');
            }
        },
        // readonly時は自動選択しない
        preventSelIfReadOnly: function(e) {
            var $target = $(e.currentTarget);
            if ($target.attr(ATTR_NO_INPUT) !== ATTR_NO_INPUT) {
                $target.select();
            }
        }
    }); // MeisaiView

    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-estimate').text()));
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    // 初期化処理
    applms.summaryCol = new SummaryCollection(); // 見積書または請求書
    applms.meisaiCol = new MeisaiCollection(); // 葬送儀礼～その他
    applms.meisaiCol1 = new MeisaiCollection(); // 葬送儀礼
    applms.meisaiCol2 = new MeisaiCollection(); // 返礼品
    applms.meisaiCol3 = new MeisaiCollection(); // 飲食費
    applms.meisaiCol4 = new MeisaiCollection(); // 立替金
    applms.meisaiCol5 = new MeisaiCollection(); // その他
    applms.appModel = new AppModel();
    applms.appView = new AppView({model: applms.appModel});

    // サイドメニュー設定
    applms.setSideMenu();
    // コントローラー名
    applms.controllerName = data.controllerName;

    applms.resetData = function(dataApp, dataCol) {
        // 印刷区分
        $.msiJqlib.setSelect2Com1($("#print_kbn"), {data: $.msiJqlib.objToArray3(dataApp.printKbn)});
        if (!applms.initializeView(dataApp)) {
            return;
        }

        $("#detail").show();
        $(".buttons #btn_print").show();// 印刷ボタン

        applms.meisaiDelCol = new MeisaiCollection();
        // APPデータを設定する
        applms.appModel.set(dataApp);
        // 明細コレクションを設定する
        applms.meisaiCol.set(dataCol);
        // 葬送儀礼～その他を設定する
        _setSosogire();
        _setHenrei();
        _setInshoku();
        _setTatekae();
        _setSonota();
        // 見積書または請求書を設定する
        _setSummary();
        // データを退避する
        applms.orgDataApp = applms.appModel.toJSON();
        applms.orgDataCol1 = applms.meisaiCol1.toJSON();
        applms.orgDataCol2 = applms.meisaiCol2.toJSON();
        applms.orgDataCol3 = applms.meisaiCol3.toJSON();
        applms.orgDataCol4 = applms.meisaiCol4.toJSON();
        applms.orgDataCol5 = applms.meisaiCol5.toJSON();
    };

    applms.resetData(data.dataApp, data.dataCol);

    // リサイズ発火
    $(window).trigger("resize");
});