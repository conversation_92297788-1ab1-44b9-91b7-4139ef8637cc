@charset "UTF-8";

/* 
 * dlg_signature01
 */
#dlg_signature01 {
    position: absolute;
    top: 0;
}

#dlg_signature01 .dlg_shade {
	position: relative;
	width: 100%;
	height: 100%;
	background-color: rgba(0,0,0,0.5);
}

#dlg_signature01 .dlg_close {
	position: absolute;
	width: 22px;
	height: 22px;
	padding: 5px;
	top: 0;
	right: 2px;
	text-align: center;
	font-size: 22px;
	font-weight: normal;
	color: #000;
	line-height: 1;
	cursor: pointer;
	z-index: 1;
    text-shadow: 0 1px 0 white;
    opacity: 0.2;
}
#dlg_signature01 .dlg_close:hover {
    opacity: 0.6;
}

#dlg_signature01 .dlg_body {
	position: fixed;
	height: auto;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	top: 130px;
	left: 50%;
	background-color: #FFF;
	box-shadow: 1px 2px 2px 2px rgba(0.5,0.5,0.5,0.7);
	padding: 15px 15px 15px 15px;

    width: 80%; /* 600px; */
    margin-left: -40%;/* -300px; */
    border-radius: 2px 2px 2px 2px;
}

#dlg_signature01 .top_cap {
    margin-top: 0px;
    margin-bottom: 10px;
}
#dlg_signature01 .dlg_body .top_cap span {
    padding:2px;
    color:#000;
    font-size: 18px;
}

#dlg_signature01 .top_info {
    margin-top: 0px;
    margin-bottom: 10px;
    font-size: 12px;
    max-height: 50vh;
    overflow: auto;
}

#dlg_signature01 .dlg_body .dlg_buttons {
    clear: both;
    text-align:right;
}

#dlg_signature01 .dlg_body .dlg_buttons .dlg_exe_button {
	display: inline-block;
	box-sizing: border-box;
	width: 120px;
	height: 30px;
	margin: 20px auto 0 auto;
	background-color: #EEE;
	border: 1px solid #AAA;
	border-radius: 4px;
	font-size: 14px;
	font-weight: normal;
	color: #111;
	cursor: pointer;
}
#dlg_signature01 .dlg_body .dlg_buttons .dlg_exe_button:focus {
	background-color: #3e7db0;
    color: white;
}
#dlg_signature01 .dlg_body .dlg_buttons .dlg_exe_button:disabled {
    opacity: 0.5;
}

#dlg_signature01 .dlg_body .dlg_opt_line {
	line-height: 1.5;
	font-size: 16px;
	font-weight: normal;
    padding-left: 5px;
}

#dlg_signature01 .dlg_body .dlg_opt_line .dlg_opt_radio_cls {
	cursor: pointer;
}
#dlg_signature01 .dlg_body .dlg_opt_line .dlg_opt_radio_cls:checked {
}

#dlg_signature01 .dlg_body .dlg_opt_line .dlg_opt_label_cls {
	cursor: pointer;
    padding-left: 7px;
    padding-right: 7px;
}
#dlg_signature01 .dlg_body .dlg_opt_line .dlg_opt_radio_cls:checked + .dlg_opt_label_cls {
    background-color: #DDD;
}

#dlg_signature01 .error {
    color: red;
    font-weight: bold;
}

#dlg_signature01 .dlg_signature01_div_cap {
    font-weight: bold;
	font-size: 15px;
}

#dlg_signature01 #dlg_signature01_options {
    max-height: calc(20vh);
    overflow-y: auto;
	border: 2px solid #CCC;
    padding: 3px 10px;
}

#dlg_signature01 #dlg_signature01_adj {
    max-height: calc(30vh);
    overflow-y: auto;
	border: 2px solid #CCC;
    padding: 3px 10px;
}

#dlg_signature01 #dlg_signature01_adj input {
    box-sizing: border-box;
}

.digiSignCanvasCls {
    background-color: #ffffee;
	border: 1px solid #CCC;
}
