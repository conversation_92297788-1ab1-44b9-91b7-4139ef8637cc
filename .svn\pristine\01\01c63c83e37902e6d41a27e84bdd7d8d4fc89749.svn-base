<div id="item-picker-footer-id">
<div id="item-picker-footer-toggle-id" class="st-open" 
     style="height:20px;margin-bottom:2px;cursor:pointer"><span>飲物（概算）、アレルギー確認</span></div>
<div id="item-picker-footer-cont-id" style="height:{$footer_height}px">
{if $is_footer_drink}
<div id="drink" class="data-in" 
    data-item="{$drink_shohin_cd}" 
    data-chargekbn="{$chargekbn1}"
    data-article="{$drink_shohin_nm}" 
    data-org-shohin_nm="{$drink_shohin_nm}" 
    data-summary="{$drink_shohin_tkiyo_nm}" 
    data-org-summary="{$drink_shohin_tkiyo_nm}" 
    data-nm_input_kbn="{$drink_nm_input_kbn}"
    data-unit_price="{$drink_hanbai_tnk}"
    data-dai_bunrui_cd="{$drink_dai_bunrui_cd}" 
    data-chu_bunrui_cd="{$drink_chu_bunrui_cd}" 
    data-mokuteki_kbn="{$itemex_mokuteki_kbn}"
    data-shohin_kbn="{$drink_shohin_kbn}"  
    data-category_kbn="{$drink_category_kbn}" 
    data-org-price="{$drink_org_hanbai_tnk}" 
    data-org-quantity="{$drink_org_quantity}" 
    data-org-chargekbn="{$drink_org_chargekbn}"
    data-kbn2="1" >
  <label for="drink" class="lbl_drink option" id="lbl_drink">飲物（概算）</label>
  <input name="drink_quantity" id="drink_quantity" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="4" value="{$drink_count}">
  <div class="label charge_x">人×</div>
  <input name="drink_tanka" id="drink_tanka" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="9" value="{$drink_quantity}">
  <div class="label charge_x">円＝ </div>
  <input name="drink_prc" id="drink_prc" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="9" readonly="readonly" value="{$drink_prc}">
  <div class="label charge_x">円</div>
</div><!-- /#drink -->
{/if}{* is_footer_drink *}

{if $is_footer_charge}
<div id="charge" class="data-in" 
    data-item="{$charge_shohin_cd}" 
    data-chargekbn="{$chargekbn2}"
    data-article="{$charge_shohin_nm}" 
    data-org-shohin_nm="{$charge_shohin_nm}" 
    data-summary="{$charge_shohin_tkiyo_nm}" 
    data-org-summary="{$charge_shohin_tkiyo_nm}" 
    data-nm_input_kbn="{$charge_nm_input_kbn}"
    data-unit_price="{$charge_hanbai_tnk}"
    data-dai_bunrui_cd="{$charge_dai_bunrui_cd}" 
    data-chu_bunrui_cd="{$charge_chu_bunrui_cd}" 
    data-mokuteki_kbn="{$itemex_mokuteki_kbn}"
    data-shohin_kbn="{$charge_shohin_kbn}"  
    data-category_kbn="{$charge_category_kbn}" 
    data-org-price="{$charge_org_hanbai_tnk}" 
    data-org-quantity="{$charge_org_quantity}"
    data-org-chargekbn="{$charge_org_chargekbn}" 
    data-kbn2="2" >
  <label for="charge" class="lbl_charge option" id="lbl_charge">座席料</label>
  <input name="charge_quantity" id="charge_quantity" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="4" value="{$charge_count}">
  <div class="label charge_x">人×</div>
  <input name="charge_tanka" id="charge_tanka" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="9" value="{$charge_quantity}">
  <div class="label charge_x">円＝</div>
  <input name="charge_prc" id="charge_prc" type="text" class="txt i_count input_field text-center" onblur="$.msiJqlib.commaFilterTemp($(this));" maxlength="9" readonly="readonly" value="{$charge_prc}">
  <div class="label charge_x">円</div>
</div><!-- /#charge -->
{/if}{* is_footer_charge *}

<div id="allergy" class="data-in" data-org_allergy_memo="{$allergy_memo}" data-category_kbn="{$allergy_category_kbn}" >
  <label for="allergy_memo" class="lbl_allergy_memo option" id="lbl_allergy_memo" style="height:70px">アレルギー確認</label>
  <!-- input name="allergy_memo" id="allergy_memo" type="text" class="txt input_field" maxlength="60" value="{$allergy_memo}" -->
  <textarea name="allergy_memo" id="allergy_memo" class="txt" cols="60" rows="3" maxlength="60"
            style="height:70px">{$allergy_memo}</textarea>
</div><!-- /#allergy_memo -->
</div><!-- /#item-picker-footer-cont-id -->

<!--
<div style="clear:both"></div>
<div style="margin-top:10px">
  <fieldset class="allergy">
    <label for="memo" class="lbl_memo option">アレルギー確認</label>
    <textarea name="memo" id="memo" class="txt" cols="50" rows="10" maxlength="60">{$memo}</textarea>
  </fieldset>
</div>
-->

</div><!-- /#item-picker-footer-id -->
