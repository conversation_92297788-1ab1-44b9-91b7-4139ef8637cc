<?php

/**
 * Juchu_HoujiCustomerinfo
 *
 * 法事お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\HoujiCustomerinfo
 * <AUTHOR> Sai
 * @since      2014/06/26
 * @version    2019/05/07 sugiyama 軽減税率対応
 * @filesource 
 */

/**
 * 法事お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\HoujiCustomerinfo
 * <AUTHOR>
 * @since      2014/06/26
 */
class Juchu_HoujiCustomerinfo extends Juchu_JuchuCustomerinfoCom {

    /** 申込区分=>2:法事 */
    const MOUSHI_KBN_HOUJI = '2';

    /** コード区分: 0950=>施行法要 */
    const CODE_KBN_HOUYO = '0950';

    /** コード区分: 0960=>法要場所 */
//    const CODE_HOUYO_BASHO_KBN = "0960";
    const CODE_HOUYO_BASHO_KBN = "0670";

    /** コード区分: 2530=>会食会場 */
//    const CODE_KAISHOKU_KAIJO_KBN = "2530";
    const CODE_KAISHOKU_KAIJO_KBN = "0670";

    /** コード区分: 1790=>元号 */
    const CODE_KBN_GENGO2 = '1790';

    /**
     *
     * currentのcss名を取得する
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @return customer
     */
    public function getCssClassName() {
        return 'customer';
    }

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @return array jsonData
     */
    public function getInitData() {

        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();
        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        if (!isset($dataSekoKihon['seko_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['seko_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['seko_tanto_nm'] = App_Utils::getTantoNm();
        }
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        if (Msi_Sys_Utils::myCount($dataKihonFree) == 0 && !isset($dataKihonFree['d_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['d_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d');
        }
        if (!isset($dataSekoKihon['kaishu_ymd'])) {
            $uri = $this->getUriageKaisu();
            if (Msi_Sys_Utils::myCount($uri) > 0 && isset($uri['kaishu_ymd'])) {
                $dataSekoKihon['kaishu_ymd'] = $uri['kaishu_ymd'];
            }
        }
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember();
        // 施行日程を取得する
        $dataNiteiCol = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfo = $this->getSekyuInfo();
//2018/04/11	ごんきや	請求情報の整形（喪主に同じの場合の穴埋め）を行ったため、画面表示されてしまうので、ここでクリア	START

		if(isset($dataSekoKihon['sekyu_kbn'])){
			if($dataSekoKihon['sekyu_kbn']	==	'0'){
				//「喪主に同じ」チェックオフは請求情報そのまま
			}else{
				//「喪主に同じ」チェックオンの場合
				//請求先名
				$dataSekyuInfo['sekyu_nm']			=	null;
				$dataSekyuInfo['sekyu_knm']			=	null;
				$dataSekyuInfo['sekyu_yubin_no']	=	null;
				$dataSekyuInfo['sekyu_addr1']		=	null;
				$dataSekyuInfo['sekyu_addr2']		=	null;
				$dataSekyuInfo['sekyu_tel']			=	null;
				$dataSekyuInfo['mobile_tel']		=	null;
			}
		}

//2018/04/11	ごんきや	請求情報の整形（喪主に同じの場合の穴埋め）を行ったため、画面表示されてしまうので、ここでクリア	END

        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // 互助会金額マスタを取得する
        $gojokaiPrcMst = $this->getGojokaiPrcMst2();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();

        //ごんきや 2017/08/14 法事向けに特定の会員区分を削除する
        foreach ($dataCodeNameMst as $key => $val) {
            if ((($val['code_kbn'] === '0030') && ($val['kbn_value_cd'] === '3'))       //3：イオン
              || (($val['code_kbn'] === '0030') && ($val['kbn_value_cd'] === '4'))      //4：プリエ
              || (($val['code_kbn'] === '0030') && ($val['kbn_value_cd'] === '5'))){    //5：プリエメンバー

                //削除実行
                unset($dataCodeNameMst[$key]);
            }
        }
        //Indexを詰める
        array_values($dataCodeNameMst);


        $controllerName = App_Utils::getParamsControllerName();
        $actionName = App_Utils::getParamsActionName();
        $db = Msi_Sys_DbManager::getMyDb();
        $gojokai_cose = $this->GetCodeNameMst2($db, self::CODE_GOJOKAI_COSE); // ソート順のため、個別取得する
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $dataKbns = array(
            // 基本タブ
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN), // 申込区分
            'moushi_kbn2' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN_H), // 申込区分2
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'gengo' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENGO), // 元号
            'gengo2' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENGO2), // 元号
            'keishiki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEISHIKI_KBN), // 葬儀形式
            'houyo_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HOUYO), // 施行法要
            'houyo_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_HOUYO_BASHO_KBN), // 法要場所
            'kaishoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KAISHOKU_KAIJO_KBN), // 会食会場
            'p_info' => $this->filter($dataCodeNameMst, self::CODE_P_INFO), // 個人情報保護
            'haigu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAIGU_KBN), // 配偶者区分
            'setai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETAI_KBN), // 世帯主
            'hito_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HITO_KBN), // 筆頭者
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            // 喪主タブ
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            'dm_print_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_DM_PRINT), // DM発行区分
            'dm_sofu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_DM_SOFU), // DM送付先区分
            // 互助会タブ
            'kanyu_dantai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KANYU_DANTAI), // 加入団体
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_HOUJI), // 用途
            'gojokai_cose' => $gojokai_cose, // 互助会コース
            'kanyu_toku' => $this->filter($dataCodeNameMst, self::CODE_KANYU_TOKU), // 加入団体（特約）
        );
        // 画面データを設定する
        $data = array(
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataNiteiCol' => $dataNiteiCol,
            'dataSekyuInfo' => $dataSekyuInfo,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'gojokaiPrcMst' => $gojokaiPrcMst,
            'dataKbns' => $dataKbns,
            'controllerName' => $controllerName,
            'actionName' => $actionName,
            'taxInfo' => $taxInfo,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/12
     * @version 2014/05/30 お知らせ状で使用するため、Juchu_JuchuCustomerinfoから移動
     * @return array 施行基本情報
     */
    protected function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.bumon_cd         -- 部門コード
            ,k.moushi_cd        -- 申込コード
            ,k.free7_kbn AS moushi_cd2 -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,k.daicho_no_eria   -- 台帳番号（エリア）
            ,k.daicho_no_mm     -- 台帳番号（月）
            ,k.daicho_no_seq    -- 台帳番号（連番）
            ,k.p_info_cd        -- 個人情報保護コード
            ,k.p_info_kbn       -- 個人情報保護区分
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,TRIM(k.k_seinengappi_ymd) AS k_seinengappi_ymd  -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kg_setai_cd      -- 世帯主コード
            ,k.kg_setai_kbn     -- 世帯主区分
            ,k.kj_kbn           -- 住民登録住所の現住所に同じチェックボックス
            ,k.kj_yubin_no      -- 住民登録住所郵便番号
            ,k.kj_addr1         -- 住民登録住所1
            ,k.kj_tel           -- 住民登録住所TEL
            ,k.kj_addr2         -- 住民登録住所2
            ,k.kj_setai_cd      -- 住民登録住所世帯主コード
            ,k.kj_setai_kbn     -- 住民登録住所世帯主区分
            ,k.kh_kbn           -- 本籍の現住所に同じチェックボックス
            ,k.kh_yubin_no      -- 本籍郵便番号
            ,k.kh_addr1         -- 本籍住所1
            ,k.kh_hito_cd       -- 筆頭者コード
            ,k.kh_hito_kbn      -- 筆頭者区分
            ,k.kh_addr2         -- 本籍住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.m_nm             -- 喪主名
            ,k.m_knm            -- 喪主名カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
            ,k.m_gengo          -- 喪主生年月日元号
            ,TRIM(k.m_seinengappi_ymd) AS m_seinengappi_ymd-- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_kbn           -- 喪主 故人に同じ
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mj_kbn           -- 喪主 住民登録住所の故人に同じ
            ,k.mj_yubin_no      -- 喪主住民登録住所郵便番号
            ,k.mj_addr1         -- 喪主住民登録住所1
            ,k.mj_tel           -- 喪主住民登録住所TEL
            ,k.mj_addr2         -- 喪主住民登録住所2
            ,k.mh_kbn           -- 喪主 本籍の故人に同じ
            ,k.mh_yubin_no      -- 喪主本籍住所郵便番号
            ,k.mh_addr1         -- 喪主本籍住所1
            ,k.mh_addr2         -- 喪主本籍住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.sekyu_kbn        -- 請求先の喪主に同じ
            ,k.sekyu_cd         -- 請求先コード
            ,k.jichu_kakute_ymd -- 受注確定日
            ,k.sg_seko_kbn -- 葬儀施行区分
            ,k.hj_seko_kbn -- 法事施行区分
            ,k.sk_houyo_cd -- 施行法要区分コード
            ,k.sk_houyo_kbn -- 施行法要区分
            ,k.k_kaimyo -- 故人戒名
            ,k.v_free10 -- 催事の件名
            ,TO_CHAR(k.k_death_ymd ,'YYYY/MM/DD') AS k_death_ymd-- 故人亡日
            ,k.k_nenrei_man     -- 年齢満
            ,k.free2_cd         -- 過去葬儀・法事施行番号
            ,k.souke_nm         -- 葬家名
            ,k.souke_knm        -- 葬家名カナ
            ,k.mg_m_tel         -- 喪主携帯
            ,k.biko1            -- 備考
            ,k.biko2            -- 備考
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.syushi_cd        -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_cd         -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.syuha_knm        -- 宗派カナ名
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.jyusho_knm       -- 寺院カナ名
            ,k.dm_print_kbn     -- DM発行区分
            ,k.dm_soufu_kbn     -- DM送付先区分
            ,k.dm_print_mi_ryu  -- DM未発行理由
            ,TO_CHAR(k.d_free1 ,'YYYY/MM/DD') AS kaishu_ymd-- 回収予定日
            ,k.v_free13                             -- 会員コード
            ,sks.sekyu_nm          AS  kaiin_nm                           -- 会員名
            ,ski.kaiin_kbn                          -- 会員区分
            ,cd1.kbn_value_lnm     AS  kaiin_kbnnm    -- 会員区分名

        FROM
            seko_kihon_info k
            LEFT OUTER JOIN tanto_mst t1
            ON  (
                    k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t2
            ON  (
                    k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
                )
            LEFT JOIN sekyu_saki_info  sks          -- 請求先情報   2017/03/17 ADD Kayo
               ON '0000000000'      = sks.seko_no           
               AND k.v_free13       = sks.sekyu_cd  
               AND 0				= sks.delete_flg
            LEFT JOIN sekyu_kaiin_info  ski         -- 請求先会員情報   2017/03/17 ADD Kayo
               ON '0000000000'      = ski.seko_no           
               AND k.v_free13       = ski.sekyu_cd  
               AND 0				= ski.delete_flg
            LEFT JOIN code_nm_mst cd1               -- 会員区分 2017/03/17 ADD Kayo
               ON	'2570'			=	cd1.code_kbn
               AND ski.kaiin_kbn	=	cd1.kbn_value_cd_num
               AND 0				=	cd1.delete_flg
        WHERE
            k.seko_no = :seko_no
        AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * コード名称マスタ取得処理
     * 
     * <AUTHOR> Sai
     * @since      2014/06/30
     * @return  array コード名称マスタ
     */
    protected function getCodeNameMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタを取得する

        $sql = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
        FROM
            code_nm_mst
        WHERE
            delete_flg = 0
        AND code_kbn IN (
             ?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?,?,?
            ,?,?
                        )
        ORDER BY
            code_kbn
			,disp_nox				--2017/05/22 Okuyama 追加
            ,kbn_value_cd_num
                ";

        $select = $db->easySelect($sql, array(
            self::CODE_KBN_MOUSHI_KBN,
            self::CODE_KBN_SOUGI_KBN,
            self::CODE_KBN_KAIIN_KBN,
            self::CODE_KBN_GENGO,
            self::CODE_KBN_KEISHIKI_KBN,
            self::CODE_KBN_KANYU_DANTAI,
            self::CODE_KBN_YOTO_KBN,
            self::CODE_KBN_ZOKU_KBN,
            self::CODE_KBN_HOUYO,
            self::CODE_HOUYO_BASHO_KBN,
            self::CODE_KAISHOKU_KAIJO_KBN,
            self::CODE_P_INFO,
            self::CODE_KBN_YOTO_HOUJI,
            self::CODE_KBN_GENGO2,
            self::CODE_KBN_MOUSHI_KBN_H,
            self::CODE_KBN_SYUSHI_KBN,
            self::CODE_KBN_HAIGU_KBN,self::CODE_KBN_SETAI_KBN,self::CODE_KBN_HITO_KBN,
            self::CODE_KBN_DM_PRINT, self::CODE_KBN_DM_SOFU
        ));
        
        // kbn_value_cdを数字に変換して整列したいのでクエリ分けてます
        $sql2 = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
        FROM
            code_nm_mst
        WHERE
            delete_flg = 0
        AND code_kbn IN (
             ?
                        )
        ORDER BY
             code_kbn
			,disp_nox				--2017/05/22 Okuyama 追加
            ,kbn_value_cd_num
            ,CAST(kbn_value_cd AS INTEGER)
                ";

        $select2 = $db->easySelect($sql2, array(
            self::CODE_KBN_SYUHA_KBN,
        ));
        
        return array_merge($select, $select2);
    }

    /**
     *
     * 施行日程情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/06/30
     * @return array 施行日程情報
     */
    private function getNitei() {

        $dataNitei = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ymd-- 日程タイムスタンプ
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,CASE 
                    WHEN sn.free3_kbn = 1 
                    THEN NULL
                    ELSE TO_CHAR(sn.nitei_ymd ,'HH24:MI')
                END nitei_time -- 日程時刻のみ
                ,sn.spot_cd       -- 場所区分コード
                ,sn.basho_kbn       -- 場所区分
                ,sn.basho_cd        -- 場所コード
                ,sn.basho_nm        -- 場所名
            FROM
                seko_nitei_houji sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.delete_flg = 0
            ORDER BY
                sn.nitei_kbn
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 施行日程に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $niteiOneRowData = array();
                $niteiOneRowData['seko_no'] = $select[$i]['seko_no'];
                $niteiOneRowData['nitei_kbn'] = (int) $select[$i]['nitei_kbn'];
                $niteiOneRowData['nitei_ymd'] = $select[$i]['nitei_ymd'];
                $niteiOneRowData['nitei_date'] = $select[$i]['nitei_date'];
                $niteiOneRowData['nitei_time'] = $select[$i]['nitei_time'];
                $niteiOneRowData['spot_cd'] = $select[$i]['spot_cd'];
                $niteiOneRowData['basho_kbn'] = $select[$i]['basho_kbn'];
                $niteiOneRowData['basho_cd'] = $select[$i]['basho_cd'];
                $niteiOneRowData['basho_nm'] = $select[$i]['basho_nm'];
                $dataNitei[$i] = $niteiOneRowData;
            }
        } else {
            $dataNitei = array(
                array('nitei_kbn' => 0,),
                array('nitei_kbn' => 1,),
                array('nitei_kbn' => 2,),
                array('nitei_kbn' => 3,),
            );
        }
        return $dataNitei;
    }

    /**
     * 保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @param array $req リクエスト
     */
    public function save($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $controllerName = $req->getPost('controllerName');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataNiteiCol = Msi_Sys_Utils::json_decode($req->getPost('dataNiteiColJson'));
        $dataSekyuInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekyuInfoJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));

        // 加入員番号重複チェック
        if (!$this->checkGojokaiMember($dataGojokaiMemberCol)) {
            return;
        }
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        if (empty($this->_sekoNo)) {
            $this->_sekoNo = $this->getAutoSekoNo($db);
        }
        else { // 2019/05/07 sugiyama keigen
            App_KeigenUtils::sougiYmdChgPre( $this->_sekoNo ); // sougi_ymd 変更の準備
        }

        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }
        // 互助会完納を求める
        $flg = $this->checkHenreiDanbariKannou($dataGojokaiMemberCol);
        if ($flg !== '1') {
            $this->gojokaiKannoNgOutJson($flg);
            return;
        }

        // 互助会コース、基本パターンを求める。
        $sekoInfo = $this->calcSekoPlanPtn($dataGojokaiMemberCol);
        if (count($sekoInfo) > 0) {
            $dataSekoKihon['gojokai_kbn'] = $sekoInfo['gojokai_kbn']; // 互助会区分
            $dataSekoKihon['main_pt_cd'] = $sekoInfo['main_pt_cd']; // 基本パターン区分コード
            $dataSekoKihon['main_pt_kbn'] = $sekoInfo['main_pt_kbn']; // 基本パターン区分
            $dataSekoKihon['seko_plan_cd'] = null; // 施行プランコード
            if (!empty($sekoInfo['main_pt_cd'])) {
                $sekoPlan = $this->getSekoPlanMst($sekoInfo['main_pt_cd']);
                if (count($sekoPlan) > 0) {
                    $dataSekoKihon['seko_plan_cd'] = $sekoPlan['seko_plan_cd']; // 施行プランコード
                } else {
                    $data['status'] = 'NG';
                    $data['msg'] = "互助会に該当する施行プランマスタが存在しません。";
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
            }
            $changeFlg['kihonChangeFlg'] = true;
        }
        // 見積確定済　かつ基本パターンコードが変わっていたら警告メッセージを出す
        if (($this->_juchuKakuteiYMD) && $this->_mainPtCd !== $dataSekoKihon['main_pt_cd']) {
            $data['status'] = 'NG';
            $data['msg'] = "見積確定済みのため、互助会の法事利用を変更することができません。";
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $denpyoNo = $this->getJuchudenpyoNo();
        // 施行日程保存処理 
        if ($changeFlg['niteiChangeFlg']) {
            $cnt += $this->saveSekoNitei($db, $dataNiteiCol);
        }
        // 請求先情報保存処理 
//        if ($changeFlg['sekyuInfoChangeFlg'] || $dataSekoKihon['sekyu_kbn'] === "1") {

//2018/04/11	ごんきや	請求情報の整形（喪主に同じの場合の穴埋め）	START
		if($dataSekoKihon['sekyu_kbn']	==	'0'){
			//「喪主に同じ」チェックオフは請求情報そのまま
		}else{
			//「喪主に同じ」チェックオンの場合
			//請求先名
			$dataSekyuInfo['sekyu_nm']	=	$dataSekoKihon['m_nm'];
			$dataSekyuInfo['sekyu_knm']	=	$dataSekoKihon['m_knm'];
			// 現住所の「故人に同じ」チェック判断
			if($dataSekoKihon['mg_kbn']	==	'0'){
				//「故人に同じ」チェックオフは喪主の入力情報から
				$dataSekyuInfo['sekyu_yubin_no']	=	$dataSekoKihon['mg_yubin_no'];
				$dataSekyuInfo['sekyu_addr1']		=	$dataSekoKihon['mg_addr1'];
				$dataSekyuInfo['sekyu_addr2']		=	$dataSekoKihon['mg_addr2'];
				$dataSekyuInfo['sekyu_tel']			=	$dataSekoKihon['mg_tel'];
				$dataSekyuInfo['mobile_tel']		=	$dataSekoKihon['mg_m_tel'];
			}else{
				//「故人に同じ」チェックオンは故人の入力情報から
				$dataSekyuInfo['sekyu_yubin_no']	=	$dataSekoKihon['kg_yubin_no'];
				$dataSekyuInfo['sekyu_addr1']		=	$dataSekoKihon['kg_addr1'];
				$dataSekyuInfo['sekyu_addr2']		=	$dataSekoKihon['kg_addr2'];
				$dataSekyuInfo['sekyu_tel']			=	$dataSekoKihon['kg_tel'];
				$dataSekyuInfo['mobile_tel']		=	null;
			}
		}
//            ,k.m_nm             -- 喪主名
//            ,k.m_knm            -- 喪主名カナ
//            ,k.m_file_nm        -- 喪主添付ファイル
//            ,k.m_zoku_cd        -- 喪主続柄コード
//            ,k.m_zoku_kbn       -- 喪主続柄区分
//            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
//            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
//            ,k.m_gengo          -- 喪主生年月日元号
//            ,TRIM(k.m_seinengappi_ymd) AS m_seinengappi_ymd-- 喪主生年月日
//            ,k.m_nenrei_man     -- 喪主年齢
//            ,k.mg_kbn           -- 喪主 故人に同じ
//            ,k.mg_yubin_no      -- 喪主現住所郵便番号
//            ,k.mg_addr1         -- 喪主現住所1
//            ,k.mg_tel           -- 喪主現住所TEL
//            ,k.mg_addr2         -- 喪主現住所2
//            ,k.mj_kbn           -- 喪主 住民登録住所の故人に同じ
//            ,k.mj_yubin_no      -- 喪主住民登録住所郵便番号
//            ,k.mj_addr1         -- 喪主住民登録住所1
//            ,k.mj_tel           -- 喪主住民登録住所TEL
//            ,k.mj_addr2         -- 喪主住民登録住所2
//            ,k.mh_kbn           -- 喪主 本籍の故人に同じ
//            ,k.mh_yubin_no      -- 喪主本籍住所郵便番号
//            ,k.mh_addr1         -- 喪主本籍住所1
//            ,k.mh_addr2         -- 喪主本籍住所2
//            ,k.mk_kinmusaki_kbn -- 喪主勤務先
//            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
//            ,k.mk_tel           -- 喪主勤務先TEL
//            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
//            ,k.mk_fax           -- 喪主勤務先FAX
//            ,k.sekyu_kbn        -- 請求先の喪主に同じ
//            ,k.sekyu_cd         -- 請求先コード

//2018/04/11	ごんきや	請求情報の整形（喪主に同じの場合の穴埋め）	END

        $cnt += $this->saveSekyuInfo($db, $dataSekyuInfo);
        // 基本情報の請求コード更新のため
        $changeFlg['kihonChangeFlg'] = true;
        $dataSekoKihon['sekyu_cd'] = $this->_sekyuCd;
//        }
        // 施行互助会情報保存処理 
        if ($changeFlg['gojokaiInfoChangeFlg']) {
            $cnt += $this->saveGojokaiInfo($db, $dataGojokaiInfo, null);
        }
        // 施行互助会加入者保存処理 
        if ($changeFlg['gojokaiMemberChangeFlg']) {
            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 会員区分を求めるを求める
//        $kaiin = $this->calcKaiinKbn($dataGojokaiInfo, $dataGojokaiMemberCol);
//        $dataSekoKihon['kaiin_cd'] = $kaiin['kaiin_cd']; // 会員区分コード
//        $dataSekoKihon['kaiin_kbn'] = $kaiin['kaiin_kbn']; // 会員区分
        // 施行基本保存処理 
        if ($changeFlg['kihonChangeFlg']) {
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $cnt += $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg);
            if ($bumonCdChangeFlg) {
                // 伝票の部門コードを更新する。
                $cnt += $this->updateDenpyoBumoncd($db, $denpyoNo, $dataSekoKihon['bumon_cd']);
            }
            // 伝票の担当コードを更新する
            $cnt += $this->updateDenpyoDantocd($db, $dataSekoKihon['seko_tanto_cd']);
        }

        // 施行基本フリー保存処理 
//        if ($changeFlg['kihonFreeChangeFlg']) {
            $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
            $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon);
//        }
        // 基本パターンコードが変更されたら、基本プランの更新または削除処理をする
        $this->upOrdelKihonPlan($db, $dataSekoKihon, $denpyoNo);

        $sbt = 4;
        if (!App_Utils::isHoujiInJuchu($controllerName)) {
            $sbt = 5;
        } else {
            // 担当者別施行情報を更新する
            App_Utils::saveTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $sbt, 1);

        // 2019/05/07 sugiyama keigen
        try {
            App_KeigenUtils::sougiYmdChgEnd( $db, $this->_sekoNo, $denpyoNo ); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array( 'status'=>'NG', 'msg' => $e->getMessage() );
            Msi_Sys_Utils::outJson($data);
            return;
        }

        // 各種集計テーブル作成、更新処理
        $cnt +=Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '2', null, $this->_sekoNo);
        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 施行日程情報を取得する
        $dataNiteiColNew = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfoNew = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember();


        // サイドメニューデータを取得する
        if (App_Utils::isHoujiInJuchu($controllerName)) {
            $sideMenuData = Juchu_Utils::getSideMenuData($this->getCssClassName(), $this->_sekoNo, null, '2');
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataNiteiCol' => $dataNiteiColNew,
            'dataSekyuInfo' => $dataSekyuInfoNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 施行日程保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/30
     * @param Msi_Sys_Db $db db
     * @param array $dataNiteiCol 画面日程タブデータ
     * @return int 更新件数
     */
    private function saveSekoNitei($db, $dataNiteiCol) {
        $cnt = 0;
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'nitei_date');
        array_push($except, 'nitei_time');

        // 施行日程データがない場合、登録する
        // 施行日程がある場合、更新する
        foreach ($dataNiteiCol as $niteiRow) {
            if (empty($niteiRow['nitei_time'])) {
                $niteiRow['free3_kbn'] = 1;
            } else {
                $niteiRow['free3_kbn'] = null;
            }
            // 1:法要 2:墓参 3:会食
            $nitei_kbn = $niteiRow["nitei_kbn"];
            // 受注伝票明細存在チェック
            $sqlSelectNitei = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分 
            FROM
                seko_nitei_houji sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY
                sn.nitei_kbn
                ";
            $selectNitei = $db->easySelect($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
            if (count($selectNitei) === 0) {
                if ($nitei_kbn === 1) {
                    $niteiRow['spot_code_kbn'] = self::CODE_HOUYO_BASHO_KBN; // 法要 
                } else if ($nitei_kbn === 3) {
                    $niteiRow['spot_code_kbn'] = self::CODE_KAISHOKU_KAIJO_KBN; // 会食
                }
                $niteiRow['seko_no'] = $this->_sekoNo;
                // 施行日程登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_nitei_houji", $niteiRow, $except);
            } else {
                array_push($except, 'seko_no');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['nitei_kbn'] = $niteiRow['nitei_kbn'];  // 日程区分
                $where['delete_flg'] = 0;  // 削除フラグ
                // 施行基本更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_nitei_houji", $niteiRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 施行基本保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihonOrg 画面施行基本データ
     * @param boolean &$bumonCdChangeFlg 部門コード変更フラグ
     * @return int 更新件数
     */
    private function saveSekoKihon($db, $dataSekoKihonOrg, &$bumonCdChangeFlg) {
        // 施行基本存在チェック
        $selectSekoKihon = $this->selectSekoKihon();
        $tableSekokihon = "seko_kihon_info";


        // 更新対象項目設定
        $dataSekoKihon = Msi_Sys_Utils::remapArrayFlat($dataSekoKihonOrg, 'seko_no bumon_cd moushi_cd moushi_kbn sougi_cd sougi_kbn kaiin_cd kaiin_kbn kaiin_sonota
                                                            daicho_no_eria daicho_no_mm daicho_no_seq uketuke_tanto_cd seko_tanto_cd k_nm k_knm k_sex_cd k_sex_kbn k_nenrei_kyounen
                                                             m_nm m_knm m_gengo m_seinengappi_ymd m_nenrei_man mg_yubin_no mg_addr1 mg_tel mg_addr2 mh_yubin_no mh_addr1 mh_addr2 
                                                             mk_kinmusaki_kbn mk_kinmusaki_nm mk_tel mk_yakusyoku_nm mk_fax sekyu_kbn sekyu_cd sg_seko_kbn hj_seko_kbn sk_houyo_cd 
                                                              k_kaimyo v_free10 k_death_ymd sougi_ymd p_info_cd p_info_kbn gojokai_kbn main_pt_cd main_pt_kbn seko_plan_cd
                                                             k_nenrei_man free2_cd souke_nm souke_knm mg_m_tel biko1 biko2 m_zoku_cd m_zoku_kbn
                                                             syushi_cd syushi_kbn syuha_cd syuha_kbn syuha_nm syuha_knm jyusho_cd jyusho_nm jyusho_knm
                                                             k_haigu_cd k_haigu_kbn k_gengo k_seinengappi_ymd kg_yubin_no kg_addr1 kg_tel kg_addr2 kg_setai_cd kg_setai_kbn kj_kbn
                                                             kj_yubin_no kj_addr1 kj_tel kj_addr2 kj_setai_cd kj_setai_kbn kh_kbn kh_yubin_no kh_addr1 kh_hito_cd kh_hito_kbn kh_addr2
                                                             mg_kbn mj_kbn mj_yubin_no mj_addr1 mj_tel mj_addr2 m_zoku_cd2 m_zoku_kbn2 dm_print_kbn dm_soufu_kbn dm_print_mi_ryu v_free13');
        // emptyToNull
        $dataSekoKihon['k_nenrei_man'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['k_nenrei_man']);
        $dataSekoKihon['k_nenrei_kyounen'] = $dataSekoKihon['k_nenrei_man'];
        $dataSekoKihon['sg_seko_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sg_seko_kbn']);
        $dataSekoKihon['hj_seko_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['hj_seko_kbn']);
//        $dataSekoKihon['sk_houyo_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sk_houyo_kbn']);
        $dataSekoKihon['k_death_ymd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['k_death_ymd']);
        $dataSekoKihon['mk_kinmusaki_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['mk_kinmusaki_kbn']);
        if (isset($dataSekoKihon['sougi_ymd'])) {
            $dataSekoKihon['sougi_ymd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['sougi_ymd']);
        }
        $dataSekoKihon['free7_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihonOrg['moushi_cd2']);

        $dataSekoKihon['k_haigu_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['k_haigu_kbn']);
        $dataSekoKihon['kg_setai_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kg_setai_kbn']);
        $dataSekoKihon['kj_setai_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kj_setai_kbn']);
        $dataSekoKihon['kh_hito_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kh_hito_kbn']);
        $dataSekoKihon['m_zoku_kbn2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['m_zoku_kbn2']);
        $dataSekoKihon['dm_print_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['dm_print_kbn']);
        $dataSekoKihon['dm_soufu_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['dm_soufu_kbn']);
        $dataSekoKihon['d_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihonOrg['kaishu_ymd']);    // 回収予定
        // 故人名添付ファイル存在するときに登録する
        if ($dataSekoKihonOrg['k_file']) {
            $dataSekoKihon['k_file_nm'] = $dataSekoKihonOrg['k_file'];
        }
        // 喪主名添付ファイル存在するときに登録する
        if ($dataSekoKihonOrg['m_file']) {
            $dataSekoKihon['m_file_nm'] = $dataSekoKihonOrg['m_file'];
        }
        // 法要日が入力された場合
//        if (isset($dataSekoKihonOrg['sougi_ymd'])) {
//            if (empty($dataSekoKihon['daicho_no_eria']) && empty($dataSekoKihon['daicho_no_eria']) && empty($dataSekoKihon['daicho_no_seq'])) {
//                // 台帳番号を自動採番する
//                $daichoNo = $this->getAutoDaichoNo($dataSekoKihonOrg['sougi_ymd']);
//                // 台帳番号を設定する
//                $dataSekoKihon['daicho_no_eria'] = $daichoNo['daicho_no_eria'];
//                $dataSekoKihon['daicho_no_mm'] = $daichoNo['daicho_no_mm'];
//                $dataSekoKihon['daicho_no_seq'] = $daichoNo['daicho_no_seq'];
//            }
//        }
        if (Msi_Sys_Utils::myCount($selectSekoKihon) === 0) {
            $dataSekoKihon['seko_no'] = $this->_sekoNo;
            // 施行基本登録SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeInsertSQL($tableSekokihon, $dataSekoKihon);
        } else {
            if ($selectSekoKihon['bumon_cd'] !== $dataSekoKihon['bumon_cd']) {
                $bumonCdChangeFlg = true;
            }
            // 条件部
            $where['seko_no'] = $dataSekoKihon['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行基本更新SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeUpdateSQL($tableSekokihon, $dataSekoKihon, $where, array('seko_no'));
        }
        $cnt = $db->easyExecute($sqlSekoKihon, $param);
        return $cnt;
    }

    /**
     *
     * 互助会コース、基本パターン求める。
     *
     * <AUTHOR> Sai
     * @since 2014/09/22
     * @param array $dataGojokaiMemberCol 施行互助会加入情報
     * @return array[gojokai_kbn]     => 互助会区分
     *          array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     */
    private function calcSekoPlanPtn($dataGojokaiMemberCol) {
        $sekoPP = array();
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        $gojokai_kbn = null;
        $main_pt_cd = null;
        $main_pt_kbn = null;
        // 基本プランの互助会区分を求める
        foreach ($dataGojokaiMemberCol as $oneRow) {
            if ($oneRow['yoto_kbn'] === '1') { // 1:法事
                $kaiinNo = $oneRow['kain_no'];
                foreach ($gojokaiCouseMst as $coseOneRow) {
                    if (strpos($kaiinNo, $coseOneRow['gojokai_cose_iw']) === 0) {
                        $gojokai_kbn = $coseOneRow['gojokai_kbn'];
                        $main_pt_cd = 'L1'; // MYTODO 法事の互助会利用時基本パターンコードは固定
                        $main_pt_kbn = '11';
                        break;
                    }
                }
            }
        }
        $sekoPP['gojokai_kbn'] = $gojokai_kbn;
        $sekoPP['main_pt_cd'] = $main_pt_cd;
        $sekoPP['main_pt_kbn'] = $main_pt_kbn;
        return $sekoPP;
    }

    /**
     *
     * 基本パターン区分コードによる施行プランマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/09/22
     * @param string $main_pt_cd 基本パターン区分コード
     * @return array    施行プランマスタ
     */
    private function getSekoPlanMst($main_pt_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                seko_plan_cd          -- 施行番号
            FROM
                seko_plan_mst
            WHERE
                main_pt_cd = :main_pt_cd
                AND delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('main_pt_cd' => $main_pt_cd));
        return $select;
    }

    /**
     *
     * 基本パターンコードが変更されたら、基本プランの更新または削除処理をする
     *
     * <AUTHOR> Sai
     * @since 2014/09/22
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 施行基本
     * @param string $denpyoNo 伝票番号
     * @return 
     */
    private function upOrdelKihonPlan($db, $dataSekoKihon, $denpyoNo) {
        $cnt = 0;
        $oldPtnCd = $this->_mainPtCd; // 変更前基本パターンコード
        $newPtnCd = $dataSekoKihon['main_pt_cd']; // 変更後基本パターンコード
        $sekoPlanCd = null; // 施行プランコード
        if (isset($dataSekoKihon['seko_plan_cd'])) {
            $sekoPlanCd = $dataSekoKihon['seko_plan_cd'];
        }

        // 法事なし→法事利用：受注明細登録処理
        if (empty($oldPtnCd) && !empty($newPtnCd) && !empty($sekoPlanCd)) {
            $hoshi = $this->getHosiritu();
            $dataApp['hoshi_ritu_cd'] = $hoshi['hoshi_ritu_cd'];
            $dataApp['hoshi_ritu'] = $hoshi['zei_rtu'];
            $dataApp['hasu_kbn'] = $hoshi['hasu_kbn'];
            $dataApp['bumon_cd'] = $dataSekoKihon['bumon_cd'];
            $dataApp['seko_plan_cd'] = $sekoPlanCd;
            $this->_gojokaiKbn = $dataSekoKihon['gojokai_kbn'];
            $this->_sekoPlanCd = $dataApp['seko_plan_cd'];
            // マスタより取得する
            $dataDtl = $this->getJuchuDetailDataFromMst($dataApp);
            // 受注データの登録
            $cnt += $this->saveJuchu($db, $dataApp, $dataDtl);
            // 施行発注管理情報を保存する
            $cnt += $this->saveHachuInfo($db, $dataApp, $dataDtl);
        }
        // 法事利用→法事なし：受注明細削除処理
        if (!empty($oldPtnCd) && empty($newPtnCd)) {
            // 大分類が葬送儀礼のデータを削除する
            $cnt += $this->deleteSosogireiData($db, $denpyoNo);
        }
    }

    /**
     * データ種別を取得する
     * 1 => '法事'
     *
     * <AUTHOR> Sai
     * @since      2014/09/22
     * @return string 1:法事
     */
    public function getDataSbt($record) {
        $data_sbt = "1";
        if (isset($record) && isset($record['service_kbn'])) {
            if ($record['service_kbn'] == '4') {
                $data_sbt = '2';
            } else if ($record['service_kbn'] == '5') {
                $data_sbt = '4';
            }
        }
        return $data_sbt;
    }

    /**
     * 施行情報取得処理 
     *
     * <AUTHOR> Sai
     * @since 2016/07/22
     * @param array $req リクエスト
     */
    public function getSekoInfoData($req) {
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        list($sekodata, $seikyudata) = $this->getSekoCopyInfoData($dataSekoKihon['free2_cd']);
        $data = array(
            'sekodata' => $sekodata,
            'seikyudata' => $seikyudata,
            'status' => 'OK',
            'msg' => ('情報をコピーしました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     *
     * 施行情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2016/7/22
     * @return array 施行基本情報
     */
    private function getSekoCopyInfoData($sekoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.*
            ,TO_CHAR(sn.nitei_ymd, 'YYYY/MM/DD') AS k_death_ymd2
            ,TO_CHAR(k.k_death_ymd, 'YYYY/MM/DD') AS k_death_ymd3
        FROM
            seko_kihon_info k
            LEFT OUTER JOIN
                seko_nitei sn
                ON 
                (
                    k.seko_no = sn.seko_no
                AND sn.nitei_kbn = 1
                AND sn.delete_flg = 0
                )
                
        WHERE
            k.seko_no = :seko_no
        AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo));
        $sekokihon = Msi_Sys_Utils::remapArrayFlat($select, 'souke_nm souke_knm m_nm m_knm m_gengo m_seinengappi_ymd m_nenrei_man mg_yubin_no mg_addr1 mg_addr2 mg_tel mg_m_tel  
                                                              mk_kinmusaki_kbn mk_kinmusaki_nm mk_yakusyoku_nm mk_tel mk_fax m_zoku_cd m_zoku_kbn
                                                              syushi_cd syushi_kbn syuha_cd syuha_kbn syuha_nm syuha_knm jyusho_cd jyusho_nm jyusho_knm
                                                              sekyu_kbn k_nm k_knm k_sex_cd k_sex_kbn k_nenrei_man biko1 k_kaimyo k_death_ymd2 m_zoku_cd2 m_zoku_kbn2
                                                              k_haigu_cd k_haigu_kbn k_gengo k_seinengappi_ymd k_nenrei_man kg_yubin_no kg_addr1 kg_tel kg_addr2
                                                              kg_setai_cd kg_setai_kbn kj_kbn kj_yubin_no kj_addr1 kj_tel kj_addr2 kj_setai_cd kj_setai_kbn kh_kbn
                                                              kh_yubin_no kh_addr1 kh_hito_cd kh_hito_kbn kh_addr2 mg_kbn mj_kbn mj_yubin_no mj_addr1 mj_tel mj_addr2
                                                              dm_print_kbn dm_soufu_kbn dm_print_mi_ryu'
                                                    ,array('k_death_ymd2' => 'k_death_ymd'));
        if (isset($sekokihon['m_seinengappi_ymd'])) {
            $sekokihon['m_seinengappi_ymd'] = trim($sekokihon['m_seinengappi_ymd']);
        }

//■ごんきや	故人の生年月日のスペース対応	2019/03/25
        if(isset($sekokihon['k_seinengappi_ymd'])){
			$sekokihon['k_seinengappi_ymd']	=	trim($sekokihon['k_seinengappi_ymd']);
        }

//        if ($select['mg_kbn'] == 1) { // 現住所に同じ
//            $sekokihon['mg_yubin_no'] = $select['kg_yubin_no'];
//            $sekokihon['mg_addr1'] = $select['kg_addr1'];
//            $sekokihon['mg_addr2'] = $select['kg_addr2'];
//            $sekokihon['mg_tel'] = $select['kg_tel'];
//        }
        
        if ($select['moushi_kbn'] == 2) { // 法事の場合
             $sekokihon['k_death_ymd'] = $select['k_death_ymd3'];
        }
            
        $sql2 = "
        SELECT
            s.*
        FROM
            sekyu_saki_info s
        WHERE
            s.seko_no = :seko_no
        AND s.sekyu_cd = :sekyu_cd
        AND s.delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql2, array('seko_no' => $sekoNo, 'sekyu_cd' => $select['sekyu_cd']));
        $seikyu = Msi_Sys_Utils::remapArrayFlat($select2, 'sekyu_nm sekyu_knm moshu_kankei_kbn moshu_kankei yubin_no addr1 addr2 tel mobile_tel biko1', 
                                array('moshu_kankei_kbn' => 'sekyu_moshu_kankei_kbn','moshu_kankei' => 'sekyu_moshu_kankei', 'yubin_no' => 'sekyu_yubin_no'
                                    ,'addr1' => 'sekyu_addr1','addr2' => 'sekyu_addr2','tel' => 'sekyu_tel','biko1' => 'sekyu_biko1'));
        return array($sekokihon, $seikyu);
    }


    /**
     *
     * 施行基本フリーを取得する
     *
     * <AUTHOR> Sai
     * @since 2015/08/03
     * @return array 施行基本フリーデータ
     */
    private function getKihonFree() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            skf.seko_no                     -- 施行番号
            ,TO_CHAR(skf.d_free1 ,'YYYY/MM/DD') AS d_free1
            ,skf.zip_no1
            ,skf.addr1_1
            ,skf.addr1_2
            ,skf.tel_no1
            ,skf.i_free1
            ,skf.i_free2
            ,skf.i_free3
            ,skf.i_free4
            ,skf.free1_kbn
            ,skf.free_kbn1
            ,skf.free_kbn2
            ,skf.free_kbn3
            ,skf.free_kbn4
            ,skf.free_kbn5
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD') AS ts_free1
            ,TO_CHAR(skf.ts_free2 ,'YYYY/MM/DD') AS ts_free2
            ,TO_CHAR(skf.ts_free3 ,'YYYY/MM/DD') AS ts_free3
            ,TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD') AS ts_free4
            ,TO_CHAR(skf.ts_free5 ,'YYYY/MM/DD') AS ts_free5
            ,skf.v_free1
            ,skf.v_free2
            ,skf.v_free3
            ,skf.v_free4
            ,skf.v_free5
            ,skf.v_free6
        FROM
            seko_kihon_all_free skf
        WHERE
            skf.seko_no = :seko_no
        AND skf.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }
    
    /**
     * 施行基本フリー保存処理
     *
     * <AUTHOR> Sai
     * @since 2016/07/22
     * @param Msi_Sys_Db $db db
     * @param array $dataKihonFree 施行基本フリーデータ
     * @param array $dataSekoKihon 施行基本
     * @return int 更新件数
     */
    private function saveKihonFree($db, $dataKihonFree, $dataSekoKihon) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataKihonFree, 'd_free1 zip_no1 addr1_1 addr1_2 tel_no1 i_free1 i_free2 i_free3 i_free4 free1_kbn 
                                                                    free_kbn1 free_kbn2 free_kbn3 free_kbn4  free_kbn5 ts_free1 ts_free2
                                                                    ts_free3 ts_free4 ts_free5 v_free1 v_free2 v_free3 v_free4 v_free5 v_free6');
        $oneRow['d_free1'] = Msi_Sys_Utils::emptyToNull($oneRow['d_free1']);
        $oneRow['i_free1'] = Msi_Sys_Utils::emptyToNull($oneRow['i_free1']);
        $oneRow['i_free2'] = Msi_Sys_Utils::emptyToNull($oneRow['i_free2']);
        $oneRow['i_free3'] = Msi_Sys_Utils::emptyToNull($oneRow['i_free3']);
        $oneRow['i_free4'] = Msi_Sys_Utils::emptyToNull($oneRow['i_free4']);
        $oneRow['ts_free1'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free1']);
        $oneRow['ts_free2'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free2']);
        $oneRow['ts_free3'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free3']);
        $oneRow['ts_free4'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free4']);
        $oneRow['ts_free5'] = Msi_Sys_Utils::emptyToNull($oneRow['ts_free5']);
        $except = array();
        // 存在チェック
        $select = $this->getKihonFree();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }
}
